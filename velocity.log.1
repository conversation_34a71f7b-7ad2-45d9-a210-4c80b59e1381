2015-06-02 15:05:14,943 - Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2015-06-02 15:05:14,943 - Initializing Velocity, Calling init()...
2015-06-02 15:05:14,943 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:05:14,943 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:05:14,943 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:05:14,943 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:05:14,943 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:05:14,943 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:05:14,943 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:05:14,959 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:05:14,959 - Do unicode file recognition:  false
2015-06-02 15:05:14,959 - FileResourceLoader : adding path '.'
2015-06-02 15:05:14,959 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:05:14,959 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:05:14,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:05:14,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:05:14,990 - Created '20' parsers.
2015-06-02 15:05:14,990 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:05:14,990 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:05:14,990 - Velocimacro : Default library not found.
2015-06-02 15:05:14,990 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:05:14,990 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:05:14,990 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:05:14,990 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:05:15,006 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:09:22,959 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 15:09:22,959 - Initializing Velocity, Calling init()...
2015-06-02 15:09:22,959 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:09:22,959 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:09:22,959 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:09:22,959 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:09:22,959 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:09:22,959 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:09:22,959 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:09:22,959 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:09:22,959 - Do unicode file recognition:  false
2015-06-02 15:09:22,959 - FileResourceLoader : adding path '.'
2015-06-02 15:09:22,959 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:09:22,959 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:09:22,974 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:09:22,990 - Created '20' parsers.
2015-06-02 15:09:23,005 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:09:23,005 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:09:23,005 - Velocimacro : Default library not found.
2015-06-02 15:09:23,005 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:09:23,005 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:09:23,005 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:09:23,005 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:09:23,021 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:10:13,567 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 15:10:13,567 - Initializing Velocity, Calling init()...
2015-06-02 15:10:13,567 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:10:13,567 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:10:13,567 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:10:13,567 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:10:13,567 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:10:13,567 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:10:13,567 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:10:13,583 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:10:13,583 - Do unicode file recognition:  false
2015-06-02 15:10:13,583 - FileResourceLoader : adding path '.'
2015-06-02 15:10:13,583 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:10:13,583 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:10:13,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:10:13,598 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:10:13,614 - Created '20' parsers.
2015-06-02 15:10:13,614 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:10:13,614 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:10:13,614 - Velocimacro : Default library not found.
2015-06-02 15:10:13,614 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:10:13,614 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:10:13,614 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:10:13,614 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:10:13,629 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:11:27,950 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 15:11:27,950 - Initializing Velocity, Calling init()...
2015-06-02 15:11:27,950 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:11:27,950 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:11:27,950 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:11:27,950 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:11:27,950 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:11:27,950 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:11:27,950 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:11:27,950 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:11:27,950 - Do unicode file recognition:  false
2015-06-02 15:11:27,950 - FileResourceLoader : adding path '.'
2015-06-02 15:11:27,950 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:11:27,966 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:11:27,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:11:27,981 - Created '20' parsers.
2015-06-02 15:11:27,997 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:11:27,997 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:11:27,997 - Velocimacro : Default library not found.
2015-06-02 15:11:27,997 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:11:27,997 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:11:27,997 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:11:27,997 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:11:28,013 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:12:12,553 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 15:12:12,553 - Initializing Velocity, Calling init()...
2015-06-02 15:12:12,553 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:12:12,553 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:12:12,553 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:12:12,553 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:12:12,553 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:12:12,553 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:12:12,553 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:12:12,553 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:12:12,553 - Do unicode file recognition:  false
2015-06-02 15:12:12,553 - FileResourceLoader : adding path '.'
2015-06-02 15:12:12,553 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:12:12,568 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:12:12,568 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:12:12,599 - Created '20' parsers.
2015-06-02 15:12:12,599 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:12:12,599 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:12:12,599 - Velocimacro : Default library not found.
2015-06-02 15:12:12,599 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:12:12,599 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:12:12,599 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:12:12,599 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:12:12,615 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:49:28,045 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 15:49:28,045 - Initializing Velocity, Calling init()...
2015-06-02 15:49:28,045 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:49:28,045 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:49:28,045 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:49:28,045 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:49:28,045 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:49:28,045 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:49:28,045 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:49:28,045 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:49:28,045 - Do unicode file recognition:  false
2015-06-02 15:49:28,045 - FileResourceLoader : adding path '.'
2015-06-02 15:49:28,045 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:49:28,045 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:49:28,045 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:49:28,045 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:49:28,045 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:49:28,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:49:28,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:49:28,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:49:28,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:49:28,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:49:28,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:49:28,077 - Created '20' parsers.
2015-06-02 15:49:28,077 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:49:28,077 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:49:28,077 - Velocimacro : Default library not found.
2015-06-02 15:49:28,077 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:49:28,077 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:49:28,077 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:49:28,077 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:49:28,108 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:50:29,979 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 15:50:29,979 - Initializing Velocity, Calling init()...
2015-06-02 15:50:29,979 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 15:50:29,979 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 15:50:29,979 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 15:50:29,979 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 15:50:29,979 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:50:29,979 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 15:50:29,979 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 15:50:29,979 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 15:50:29,994 - Do unicode file recognition:  false
2015-06-02 15:50:29,994 - FileResourceLoader : adding path '.'
2015-06-02 15:50:29,994 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 15:50:29,994 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 15:50:29,994 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 15:50:30,025 - Created '20' parsers.
2015-06-02 15:50:30,025 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 15:50:30,025 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 15:50:30,025 - Velocimacro : Default library not found.
2015-06-02 15:50:30,025 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 15:50:30,025 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 15:50:30,025 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 15:50:30,025 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 15:50:30,041 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:05:38,494 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:05:38,494 - Initializing Velocity, Calling init()...
2015-06-02 16:05:38,494 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:05:38,494 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:05:38,494 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:05:38,494 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:05:38,494 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:05:38,494 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:05:38,494 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:05:38,494 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:05:38,494 - Do unicode file recognition:  false
2015-06-02 16:05:38,494 - FileResourceLoader : adding path '.'
2015-06-02 16:05:38,494 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:05:38,510 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:05:38,510 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:05:38,525 - Created '20' parsers.
2015-06-02 16:05:38,541 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:05:38,541 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:05:38,541 - Velocimacro : Default library not found.
2015-06-02 16:05:38,541 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:05:38,541 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:05:38,541 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:05:38,541 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:05:38,557 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:16:11,197 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:16:11,197 - Initializing Velocity, Calling init()...
2015-06-02 16:16:11,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:16:11,197 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:16:11,197 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:16:11,197 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:16:11,197 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:16:11,197 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:16:11,197 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:16:11,197 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:16:11,197 - Do unicode file recognition:  false
2015-06-02 16:16:11,197 - FileResourceLoader : adding path '.'
2015-06-02 16:16:11,197 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:16:11,197 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:16:11,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:16:11,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:16:11,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:16:11,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:16:11,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:16:11,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:16:11,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:16:11,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:16:11,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:16:11,228 - Created '20' parsers.
2015-06-02 16:16:11,228 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:16:11,228 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:16:11,228 - Velocimacro : Default library not found.
2015-06-02 16:16:11,228 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:16:11,228 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:16:11,228 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:16:11,228 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:16:11,243 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:17:08,638 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:17:08,638 - Initializing Velocity, Calling init()...
2015-06-02 16:17:08,638 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:17:08,638 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:17:08,638 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:17:08,638 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:17:08,638 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:17:08,638 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:17:08,638 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:17:08,653 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:17:08,653 - Do unicode file recognition:  false
2015-06-02 16:17:08,653 - FileResourceLoader : adding path '.'
2015-06-02 16:17:08,653 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:17:08,653 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:17:08,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:17:08,685 - Created '20' parsers.
2015-06-02 16:17:08,685 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:17:08,685 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:17:08,685 - Velocimacro : Default library not found.
2015-06-02 16:17:08,685 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:17:08,685 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:17:08,685 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:17:08,685 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:17:08,700 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:17:57,189 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:17:57,189 - Initializing Velocity, Calling init()...
2015-06-02 16:17:57,189 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:17:57,189 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:17:57,189 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:17:57,189 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:17:57,189 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:17:57,189 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:17:57,189 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:17:57,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:17:57,189 - Do unicode file recognition:  false
2015-06-02 16:17:57,189 - FileResourceLoader : adding path '.'
2015-06-02 16:17:57,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:17:57,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:17:57,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:17:57,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:17:57,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:17:57,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:17:57,189 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:17:57,205 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:17:57,205 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:17:57,205 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:17:57,205 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:17:57,220 - Created '20' parsers.
2015-06-02 16:17:57,220 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:17:57,220 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:17:57,220 - Velocimacro : Default library not found.
2015-06-02 16:17:57,220 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:17:57,220 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:17:57,220 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:17:57,220 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:17:57,251 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:43:20,637 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:43:20,637 - Initializing Velocity, Calling init()...
2015-06-02 16:43:20,637 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:43:20,637 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:43:20,637 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:43:20,637 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:43:20,637 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:43:20,637 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:43:20,637 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:43:20,637 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:43:20,637 - Do unicode file recognition:  false
2015-06-02 16:43:20,637 - FileResourceLoader : adding path '.'
2015-06-02 16:43:20,637 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:43:20,637 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:43:20,637 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:43:20,637 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:43:20,637 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:43:20,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:43:20,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:43:20,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:43:20,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:43:20,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:43:20,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:43:20,668 - Created '20' parsers.
2015-06-02 16:43:20,668 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:43:20,668 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:43:20,668 - Velocimacro : Default library not found.
2015-06-02 16:43:20,668 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:43:20,668 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:43:20,668 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:43:20,668 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:43:20,700 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:44:23,101 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:44:23,101 - Initializing Velocity, Calling init()...
2015-06-02 16:44:23,101 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:44:23,101 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:44:23,101 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:44:23,101 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:44:23,101 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:44:23,101 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:44:23,101 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:44:23,101 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:44:23,101 - Do unicode file recognition:  false
2015-06-02 16:44:23,101 - FileResourceLoader : adding path '.'
2015-06-02 16:44:23,101 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:44:23,116 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:44:23,116 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:44:23,148 - Created '20' parsers.
2015-06-02 16:44:23,148 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:44:23,148 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:44:23,148 - Velocimacro : Default library not found.
2015-06-02 16:44:23,148 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:44:23,148 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:44:23,148 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:44:23,148 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:44:23,163 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:47:07,809 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:47:07,809 - Initializing Velocity, Calling init()...
2015-06-02 16:47:07,809 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:47:07,809 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:47:07,809 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:47:07,809 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:47:07,809 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:47:07,809 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:47:07,809 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:47:07,809 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:47:07,809 - Do unicode file recognition:  false
2015-06-02 16:47:07,809 - FileResourceLoader : adding path '.'
2015-06-02 16:47:07,809 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:47:07,809 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:47:07,809 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:47:07,809 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:47:07,809 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:47:07,809 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:47:07,824 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:47:07,824 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:47:07,824 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:47:07,824 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:47:07,824 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:47:07,840 - Created '20' parsers.
2015-06-02 16:47:07,840 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:47:07,840 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:47:07,840 - Velocimacro : Default library not found.
2015-06-02 16:47:07,840 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:47:07,840 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:47:07,840 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:47:07,840 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:47:07,871 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:48:36,014 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:48:36,014 - Initializing Velocity, Calling init()...
2015-06-02 16:48:36,014 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:48:36,014 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:48:36,014 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:48:36,014 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:48:36,014 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:48:36,014 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:48:36,014 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:48:36,014 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:48:36,014 - Do unicode file recognition:  false
2015-06-02 16:48:36,014 - FileResourceLoader : adding path '.'
2015-06-02 16:48:36,014 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:48:36,030 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:48:36,030 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:48:36,061 - Created '20' parsers.
2015-06-02 16:48:36,061 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:48:36,061 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:48:36,061 - Velocimacro : Default library not found.
2015-06-02 16:48:36,061 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:48:36,061 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:48:36,061 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:48:36,061 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:48:36,076 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:49:14,860 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:49:14,860 - Initializing Velocity, Calling init()...
2015-06-02 16:49:14,860 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:49:14,860 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:49:14,860 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:49:14,860 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:49:14,860 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:49:14,860 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:49:14,860 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:49:14,860 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:49:14,860 - Do unicode file recognition:  false
2015-06-02 16:49:14,860 - FileResourceLoader : adding path '.'
2015-06-02 16:49:14,860 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:49:14,875 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:49:14,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:49:14,891 - Created '20' parsers.
2015-06-02 16:49:14,907 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:49:14,907 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:49:14,907 - Velocimacro : Default library not found.
2015-06-02 16:49:14,907 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:49:14,907 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:49:14,907 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:49:14,907 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:49:14,922 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:49:48,525 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:49:48,525 - Initializing Velocity, Calling init()...
2015-06-02 16:49:48,525 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:49:48,525 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:49:48,525 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:49:48,525 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:49:48,525 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:49:48,525 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:49:48,525 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:49:48,525 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:49:48,525 - Do unicode file recognition:  false
2015-06-02 16:49:48,525 - FileResourceLoader : adding path '.'
2015-06-02 16:49:48,525 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:49:48,525 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:49:48,541 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:49:48,556 - Created '20' parsers.
2015-06-02 16:49:48,556 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:49:48,572 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:49:48,572 - Velocimacro : Default library not found.
2015-06-02 16:49:48,572 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:49:48,572 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:49:48,572 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:49:48,572 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:49:48,588 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:50:19,820 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 16:50:19,820 - Initializing Velocity, Calling init()...
2015-06-02 16:50:19,820 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 16:50:19,820 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 16:50:19,820 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 16:50:19,820 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 16:50:19,820 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:50:19,820 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 16:50:19,820 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 16:50:19,820 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 16:50:19,820 - Do unicode file recognition:  false
2015-06-02 16:50:19,820 - FileResourceLoader : adding path '.'
2015-06-02 16:50:19,820 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 16:50:19,836 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 16:50:19,836 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 16:50:19,852 - Created '20' parsers.
2015-06-02 16:50:19,867 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 16:50:19,867 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 16:50:19,867 - Velocimacro : Default library not found.
2015-06-02 16:50:19,867 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 16:50:19,867 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 16:50:19,867 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 16:50:19,867 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 16:50:19,883 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:00:17,484 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:00:17,484 - Initializing Velocity, Calling init()...
2015-06-02 17:00:17,484 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:00:17,484 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:00:17,484 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:00:17,484 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:00:17,484 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:00:17,484 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:00:17,484 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:00:17,484 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:00:17,484 - Do unicode file recognition:  false
2015-06-02 17:00:17,484 - FileResourceLoader : adding path '.'
2015-06-02 17:00:17,484 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:00:17,500 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:00:17,500 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:00:17,516 - Created '20' parsers.
2015-06-02 17:00:17,531 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:00:17,531 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:00:17,531 - Velocimacro : Default library not found.
2015-06-02 17:00:17,531 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:00:17,531 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:00:17,531 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:00:17,531 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:00:17,547 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:01:08,155 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:01:08,155 - Initializing Velocity, Calling init()...
2015-06-02 17:01:08,155 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:01:08,155 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:01:08,155 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:01:08,155 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:01:08,155 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:01:08,155 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:01:08,155 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:01:08,155 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:01:08,155 - Do unicode file recognition:  false
2015-06-02 17:01:08,155 - FileResourceLoader : adding path '.'
2015-06-02 17:01:08,155 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:01:08,155 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:01:08,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:01:08,186 - Created '20' parsers.
2015-06-02 17:01:08,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:01:08,202 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:01:08,202 - Velocimacro : Default library not found.
2015-06-02 17:01:08,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:01:08,202 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:01:08,202 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:01:08,202 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:01:08,218 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:01:41,338 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:01:41,338 - Initializing Velocity, Calling init()...
2015-06-02 17:01:41,338 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:01:41,338 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:01:41,338 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:01:41,338 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:01:41,338 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:01:41,338 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:01:41,338 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:01:41,338 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:01:41,338 - Do unicode file recognition:  false
2015-06-02 17:01:41,338 - FileResourceLoader : adding path '.'
2015-06-02 17:01:41,338 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:01:41,354 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:01:41,354 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:01:41,369 - Created '20' parsers.
2015-06-02 17:01:41,385 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:01:41,385 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:01:41,385 - Velocimacro : Default library not found.
2015-06-02 17:01:41,385 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:01:41,385 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:01:41,385 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:01:41,385 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:01:41,400 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:02:14,832 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:02:14,832 - Initializing Velocity, Calling init()...
2015-06-02 17:02:14,832 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:02:14,832 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:02:14,832 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:02:14,832 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:02:14,832 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:02:14,832 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:02:14,832 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:02:14,832 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:02:14,832 - Do unicode file recognition:  false
2015-06-02 17:02:14,832 - FileResourceLoader : adding path '.'
2015-06-02 17:02:14,832 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:02:14,848 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:02:14,848 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:02:14,879 - Created '20' parsers.
2015-06-02 17:02:14,894 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:02:14,894 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:02:14,894 - Velocimacro : Default library not found.
2015-06-02 17:02:14,894 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:02:14,894 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:02:14,894 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:02:14,894 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:02:14,910 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:02:45,004 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:02:45,004 - Initializing Velocity, Calling init()...
2015-06-02 17:02:45,004 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:02:45,004 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:02:45,004 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:02:45,004 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:02:45,004 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:02:45,004 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:02:45,019 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:02:45,019 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:02:45,019 - Do unicode file recognition:  false
2015-06-02 17:02:45,019 - FileResourceLoader : adding path '.'
2015-06-02 17:02:45,019 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:02:45,019 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:02:45,019 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:02:45,019 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:02:45,019 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:02:45,019 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:02:45,019 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:02:45,019 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:02:45,035 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:02:45,035 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:02:45,035 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:02:45,051 - Created '20' parsers.
2015-06-02 17:02:45,051 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:02:45,051 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:02:45,051 - Velocimacro : Default library not found.
2015-06-02 17:02:45,051 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:02:45,051 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:02:45,051 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:02:45,051 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:02:45,066 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:03:19,639 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:03:19,639 - Initializing Velocity, Calling init()...
2015-06-02 17:03:19,639 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:03:19,639 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:03:19,639 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:03:19,639 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:03:19,639 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:03:19,639 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:03:19,639 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:03:19,639 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:03:19,639 - Do unicode file recognition:  false
2015-06-02 17:03:19,639 - FileResourceLoader : adding path '.'
2015-06-02 17:03:19,639 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:03:19,654 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:03:19,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:03:19,685 - Created '20' parsers.
2015-06-02 17:03:19,685 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:03:19,685 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:03:19,685 - Velocimacro : Default library not found.
2015-06-02 17:03:19,685 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:03:19,685 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:03:19,685 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:03:19,685 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:03:19,701 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:04:59,341 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:04:59,341 - Initializing Velocity, Calling init()...
2015-06-02 17:04:59,357 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:04:59,357 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:04:59,357 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:04:59,357 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:04:59,357 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:04:59,357 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:04:59,357 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:04:59,357 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:04:59,357 - Do unicode file recognition:  false
2015-06-02 17:04:59,357 - FileResourceLoader : adding path '.'
2015-06-02 17:04:59,357 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:04:59,357 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:04:59,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:04:59,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:04:59,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:04:59,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:04:59,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:04:59,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:04:59,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:04:59,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:04:59,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:04:59,388 - Created '20' parsers.
2015-06-02 17:04:59,388 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:04:59,388 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:04:59,388 - Velocimacro : Default library not found.
2015-06-02 17:04:59,388 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:04:59,388 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:04:59,388 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:04:59,388 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:04:59,403 - ResourceManager : found META-INF/velocity/template/JavaVO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:31:44,924 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:31:44,924 - Initializing Velocity, Calling init()...
2015-06-02 17:31:44,924 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:31:44,924 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:31:44,924 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:31:44,924 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:31:44,924 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:31:44,924 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:31:44,924 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:31:44,924 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:31:44,924 - Do unicode file recognition:  false
2015-06-02 17:31:44,924 - FileResourceLoader : adding path '.'
2015-06-02 17:31:44,924 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:31:44,940 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:31:44,940 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:31:44,971 - Created '20' parsers.
2015-06-02 17:31:44,971 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:31:44,971 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:31:44,971 - Velocimacro : Default library not found.
2015-06-02 17:31:44,971 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:31:44,971 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:31:44,971 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:31:44,971 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:31:44,986 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:32:26,860 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:32:26,860 - Initializing Velocity, Calling init()...
2015-06-02 17:32:26,860 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:32:26,860 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:32:26,860 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:32:26,860 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:32:26,860 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:32:26,860 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:32:26,860 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:32:26,860 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:32:26,860 - Do unicode file recognition:  false
2015-06-02 17:32:26,860 - FileResourceLoader : adding path '.'
2015-06-02 17:32:26,860 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:32:26,875 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:32:26,875 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:32:26,891 - Created '20' parsers.
2015-06-02 17:32:26,906 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:32:26,906 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:32:26,906 - Velocimacro : Default library not found.
2015-06-02 17:32:26,906 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:32:26,906 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:32:26,906 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:32:26,906 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:32:26,922 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:37:47,370 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:37:47,370 - Initializing Velocity, Calling init()...
2015-06-02 17:37:47,370 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:37:47,370 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:37:47,370 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:37:47,370 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:37:47,370 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:37:47,370 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2015-06-02 17:37:47,370 - ResourceManager : No configuration information found for resource loader named 'jar' (id is jar.resource.loader). Skipping it...
2015-06-02 17:37:47,370 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2015-06-02 17:37:47,370 - Do unicode file recognition:  false
2015-06-02 17:37:47,370 - FileResourceLoader : adding path '.'
2015-06-02 17:37:47,385 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:37:47,385 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2015-06-02 17:37:47,385 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2015-06-02 17:37:47,417 - Created '20' parsers.
2015-06-02 17:37:47,417 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2015-06-02 17:37:47,417 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2015-06-02 17:37:47,417 - Velocimacro : Default library not found.
2015-06-02 17:37:47,417 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2015-06-02 17:37:47,417 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2015-06-02 17:37:47,417 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2015-06-02 17:37:47,417 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2015-06-02 17:37:47,432 - ResourceManager : found META-INF/velocity/template/JavaPO.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2015-06-02 17:38:16,269 - Log4JLogChute initialized using file 'velocity.log'
2015-06-02 17:38:16,269 - Initializing Velocity, Calling init()...
2015-06-02 17:38:16,269 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2015-06-02 17:38:16,269 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2015-06-02 17:38:16,269 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2015-06-02 17:38:16,269 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2015-06-02 17:38:16,269 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
aaaa

