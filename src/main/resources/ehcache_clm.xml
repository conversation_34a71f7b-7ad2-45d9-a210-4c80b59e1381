<ehcache updateCheck="false">
	<diskStore path="java.io.tmpdir" />
	<defaultCache maxEntriesLocalHeap="10000" eternal="false" timeToIdleSeconds="1200" timeToLiveSeconds="1200"
		overflowToDisk="false" maxEntriesLocalDisk="10000000" diskPersistent="false" diskExpiryThreadIntervalSeconds="120"
		memoryStoreEvictionPolicy="LRU">		
	</defaultCache>
  <cache name="sampleCache" maxElementsInMemory="10000"
        maxElementsOnDisk="10000" eternal="true" overflowToDisk="false"
        diskSpoolBufferSizeMB="50" />
</ehcache>