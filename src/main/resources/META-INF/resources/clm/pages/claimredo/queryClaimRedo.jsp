<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--引入jQuery -->
<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>
<script type="text/javascript">
	function queryClaimCase(){
		var caseNo=$("#caseNo",navTab.getCurrentPanel()).attr("value");
		if(caseNo.length != 0 && caseNo.length != 11){
			alertMsg.warn("录入的赔案号不正确，请重新输入！");
			return false;
		}
		var insuredName=$("#insuredName",navTab.getCurrentPanel()).attr("value");
		var certiCode=$("#certiCode",navTab.getCurrentPanel()).attr("value");
		if(insuredName != ""){
			if(!checkName($("#insuredName", navTab.getCurrentPanel()))){
				return;
			}
		}
		if(caseNo=="" && insuredName=="" && certiCode==""){
			alertMsg.error ("请至少输入一项查询条件");
			return false;
		}else{
			$("#claimRedoForm", navTab.getCurrentPanel()).submit();
			return true;
		} 
	}
	function changeCaseNO(obj){
		obj.value=obj.value.replace(/\D/g,'');
		if(obj.value[0]!=9){
			obj.value='';
		}
	}
	
	function regiestClaimRedo(){
		//得到选中的长度
	    var radioLength = $("#redoCheckBody tr", navTab.getCurrentPanel()).find("td:eq(0)").find("input[name='r']:checked").length;
		if(radioLength < 1){
			alertMsg.warn('请选择一条记录！');
			return false;
		}
		var caseId = $("#redoCheckBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("td").find("input:eq(1)").val();
		$.ajax({
			'type':'post',
			'url':'clm/claimredo/redoCheckSH_CLM_claimRedoAction.action',
			'data':{'caseId':caseId},
			'datatype':'json',
			'success':function(obj){
				var dataObj = eval("(" + obj + ")");
				if(dataObj.statusCode==300){//300
					alertMsg.warn("上海医保的理赔退费赔案暂不支持回退。");
					return false;//是上海医保
				}else{
					$.ajax({
						'type':'post',
						'url':'clm/claimredo/redoCaseVerify_CLM_claimRedoAction.action',
						'data':{'caseId':caseId},
						'datatype':'json',
						'success':function(data){
							var data = eval("(" + data + ")");
							if(data.statusCode==300){
								alertMsg.error(data.message);
							}
							if(data.statusCode==200){
								var claimRedoRegisterClick = $("#claimRedoRegisterClick", navTab.getCurrentPanel());
								claimRedoRegisterClick.attr("href","clm/claimredo/registerClaimRedo_CLM_claimRedoAction.action?caseId="+caseId);
								claimRedoRegisterClick.click();
							}
						},
						'error':function(){
							alertMsg.error("系统出险异常，请联系管理员");
						}
					});  
				}
			}
		});  
	}
	
	function testClick3() {
		navTab.openTab(20264,
				"clm/claimredo/toClaimRedoAudit_CLM_claimRedoAction.action?caseNo=92001784215", {
					title : '测试3'
				});
	}
		
</script>

<!-- 页面内容 -->
<div layoutH="0" id="queryClaimCasePage">
	<!-- 查询事件访问路径 -->
	<form id="claimRedoForm"
		action="clm/claimredo/queryClaimCase_CLM_claimRedoAction.action" method="post" 
		onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate">
		
	<!-- 查询区域 -->
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
	</div>
	<div class="pageFormInfoContent"  id="queryClaimRedoSearch">
				<dl>	
					<dt>赔案号</dt>
					<dd>
						<input type="text" id="caseNo" name="claimRedoVO.caseNo" onkeyup="changeCaseNO(this)" value="${claimRedoVO.caseNo}" size="25" />
					</dd> 
				</dl> 
				<dl>	
					<dt>出险人姓名</dt>
					<dd>
						<input type="text" id="insuredName" name="claimRedoVO.insuredName" value="${claimRedoVO.insuredName}" size="25" onblur="resetStyle(this)" />
					</dd> 
				</dl>
				<dl>	
					<dt>出险人证件号码</dt>
					<dd>
						<input type="text" id="certiCode" name="claimRedoVO.certiCode" value="${claimRedoVO.certiCode}" size="25" />
					</dd> 
				</dl>
				<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="queryClaimCase();">查询</button> 
				</div>
		</div>
		
		<div class="tabdivclassbr" style="margin-top: 15px;">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>选择</th>
						<th nowrap>序号</th>
						<th nowrap>赔案号</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>理赔类型</th>
						<th nowrap>理赔结论</th>  
						<th nowrap>给付总金额</th>
						<th nowrap>给付状态</th>
						<th nowrap>结案时间</th>
					</tr>
				</thead>
				<tbody id="redoCheckBody">
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="responseVOList == null || responseVOList.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
					<s:iterator value="responseVOList"  status="st" id="claimRedoVO">
						<tr align="center">
							<td><input type="radio" name="r"><input type="hidden" value="${caseId}"></td>
							<td> ${st.index+1}</td>
							<td><s:property value="#claimRedoVO.caseNo"/></td>
							<td><s:property value="#claimRedoVO.insuredName" /></td>
							<td><s:property value="#claimRedoVO.claimTypeName" /></td>
							<td><Field:codeValue value="${auditDecision}" tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION"/></td>
							<td><s:property value="#claimRedoVO.actualPay" /></td>
							<td><s:property value="#claimRedoVO.payStatus"/></td>
							<td><s:date name="#claimRedoVO.endCaseTime" format="yyyy-MM-dd"/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
			<div class="formBarButton">
					<ul>
					   <li><button type="button" class="but_blue" onclick="regiestClaimRedo();">理赔回退</button></li>
		               <li><a href="clm/claimredo/regiesterClaimRedo_CLM_claimRedoAction.action" id="claimRedoRegisterClick" target="navTab" style="display: none">回退申请录入</a></li>
					</ul>
			</div>
	</form>
</div>
 