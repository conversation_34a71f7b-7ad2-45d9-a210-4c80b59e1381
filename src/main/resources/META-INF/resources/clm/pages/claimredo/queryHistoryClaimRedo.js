//回退历史查询
function queryHistoryClaimRedo(){
	
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	var insuredName = $("#insuredName", navTab.getCurrentPanel()).val();
	var certiCode = $("#certiCode", navTab.getCurrentPanel()).val();
	//关于赔案号的校验
	if(caseNo.length!=0 && caseNo.length!=11){
		alertMsg.error("请输入正确赔案号");
		return false;
	}else if(caseNo.length!=0 && caseNo.substr(0,1)!=9){
		alertMsg.error("请输入正确赔案号");
		return false;
	}
	//查询条件的校验
	if(caseNo.trim() == "" && insuredName.trim() == "" && certiCode.trim() == ""){
		alertMsg.error ("请至少输入一项查询条件");
		return false;
	}
	/*if(insuredName !=""){
		if(!checkName($("#insuredName", navTab.getCurrentPanel()))){
			return;
		}
	}*/
	$("#queryHistoryClaimRedoId", navTab.getCurrentPanel()).submit();
}
//radio触发按钮为下面赋值
function claimRedoHistoryDown(k){
	var applyId = $(k).val();
	var caseNo = $(k).parent().parent().find("td:eq(0)").find("input:eq(1)").val()
	$.ajax({
		'type':'post',
		'url':'clm/claimredo/claimRedoHistoryDown_CLM_queryHistoryClaimRedo.action?claimBackApplyVO.applyId='+applyId+'&claimBackApplyVO.caseNo='+caseNo,
		'datatype':'json',
		'success':function(data){
			var claimBackApply = eval("(" + data + ")");
			
			if(claimBackApply.statusCode=='300'){
				alertMsg.error(claimBackApply.message);
				return;
			}
			$("#caseNoDown", navTab.getCurrentPanel()).val(caseNo);
			$("#applyDateIdDown", navTab.getCurrentPanel()).val(claimBackApply.applyDateFmt);
			$("#cusApplyDateDown", navTab.getCurrentPanel()).val(claimBackApply.cusApplyDateStr);
			$("#casePayIdDown", navTab.getCurrentPanel()).val(claimBackApply.casePay);
			$("#treatyConclusionIdDown", navTab.getCurrentPanel()).text(claimBackApply.backDesc);
			$("#backRemarkIdDown", navTab.getCurrentPanel()).text(claimBackApply.backRemark);
			$("#backAuditConIdDown", navTab.getCurrentPanel()).val(claimBackApply.backAudiTconName);
			$("#auditDescIdDown", navTab.getCurrentPanel()).text(claimBackApply.auditDesc);
			if(claimBackApply.backReason == 1){
				$("#backReasonIdDown", navTab.getCurrentPanel()).val("系统操作错误");
			} else if(claimBackApply.backReason == 2){
				$("#backReasonIdDown", navTab.getCurrentPanel()).val("专业技术错误");
			} else if(claimBackApply.backReason == 3){
				$("#backReasonIdDown", navTab.getCurrentPanel()).val("法律诉讼结论");
			} else if(claimBackApply.backReason == 4){
				$("#backReasonIdDown", navTab.getCurrentPanel()).val("新关键证据");
			} else if(claimBackApply.backReason == 5){
				$("#backReasonIdDown", navTab.getCurrentPanel()).val("民事申诉调解");
			}
			if(claimBackApply.terminateFlag == 1){
				$("#terminateFlagIdDown", navTab.getCurrentPanel()).attr("checked","checked");
			}
			if(claimBackApply.caseConclusion == 1){
				$("#caseConclusionIdDown", navTab.getCurrentPanel()).val("全部给付");
			} else if(claimBackApply.caseConclusion == 2){
				$("#caseConclusionIdDown", navTab.getCurrentPanel()).val("部分给付");
			} else if(claimBackApply.caseConclusion == 3){
				$("#caseConclusionIdDown", navTab.getCurrentPanel()).val("全部拒付");
			} else if(claimBackApply.caseConclusion == 4){
				$("#caseConclusionIdDown", navTab.getCurrentPanel()).val("公司撤案");
			} else if(claimBackApply.caseConclusion == 5){
				$("#caseConclusionIdDown", navTab.getCurrentPanel()).val("客户撤案");
			} else if(claimBackApply.caseConclusion == 6){
				$("#caseConclusionIdDown", navTab.getCurrentPanel()).val("审核不通过");
			}
			if(claimBackApply.conclusionAf == 1){
				$("#conclusionAfIdDown", navTab.getCurrentPanel()).val("全部给付");
			} else if(claimBackApply.conclusionAf == 2){
				$("#conclusionAfIdDown", navTab.getCurrentPanel()).val("部分给付");
			} else if(claimBackApply.conclusionAf == 3){
				$("#conclusionAfIdDown", navTab.getCurrentPanel()).val("全部拒付");
			} else if(claimBackApply.conclusionAf == 4){
				$("#conclusionAfIdDown", navTab.getCurrentPanel()).val("公司撤案");
			} else if(claimBackApply.conclusionAf == 5){
				$("#conclusionAfIdDown", navTab.getCurrentPanel()).val("客户撤案");
			} else if(claimBackApply.conclusionAf == 6){
				$("#conclusionAfIdDown", navTab.getCurrentPanel()).val("审核不通过");
			}
			//为下面列表赋值
			var insertHtml = "";
			$("#claimRedoHistoryBodyId", navTab.getCurrentPanel()).empty();
			for(var i = 0; i < claimBackApply.claimBackTraceSincereVOList.length; i++){
				insertHtml = insertHtml + "<tr align='center'>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].payeeName+"</td>"
					+ "<td></td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].feeAmount+"</td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].payMode+"</td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].feeStatus+"</td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].dueTime+"</td>" + "</tr>";
			}
			$("#claimRedoHistoryBodyId", navTab.getCurrentPanel()).append(insertHtml);
			$("#claimRedoHistoryBodyId", navTab.getCurrentPanel()).initUI();
		},
	});
}