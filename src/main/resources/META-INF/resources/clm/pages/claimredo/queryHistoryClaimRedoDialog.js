//回退历史查询
function queryHistoryClaimRedo(){
	var caseNo = $("#caseNo", $.pdialog.getCurrent()).val();
	var insuredName = $("#insuredName", $.pdialog.getCurrent()).val();
	var certiCode = $("#certiCode", $.pdialog.getCurrent()).val();
	//关于赔案号的校验
	if(caseNo.length!=0 && caseNo.length!=11){
		alertMsg.error("请输入正确赔案号");
		return false;
	}else if(caseNo.length!=0 && caseNo.substr(0,1)!=9){
		alertMsg.error("请输入正确赔案号");
		return false;
	}
	//查询条件的校验
	if(caseNo.trim() == "" && insuredName.trim() == "" && certiCode.trim() == ""){
		alertMsg.error ("请至少输入一项查询条件");
		return false;
	}
	$("#queryHistoryClaimRedoId").submit();
}
//radio触发按钮为下面赋值
function claimRedoHistoryDown(k){
	var applyId = $(k).val();
	var caseNo = $(k).parent().parent().find("td:eq(0)").find("input:eq(1)").val()
	$.ajax({
		'type':'post',
		'url':'clm/claimredo/claimRedoHistoryDown_CLM_queryHistoryClaimRedo.action?claimBackApplyVO.applyId='+applyId+'&claimBackApplyVO.caseNo='+caseNo,
		'datatype':'json',
		'success':function(data){
			var claimBackApply = eval("(" + data + ")");
			
			if(claimBackApply.statusCode=='300'){
				alertMsg.error(claimBackApply.message);
				return;
			}
			$("#caseNoDown", $.pdialog.getCurrent()).val(caseNo);
			$("#applyDateIdDown", $.pdialog.getCurrent()).val(claimBackApply.applyDateFmt);
			$("#cusApplyDateDown", $.pdialog.getCurrent()).val(claimBackApply.cusApplyDateStr);
			$("#casePayIdDown", $.pdialog.getCurrent()).val(claimBackApply.casePay);
			$("#treatyConclusionIdDown", $.pdialog.getCurrent()).text(claimBackApply.backDesc);
			$("#backRemarkIdDown", $.pdialog.getCurrent()).text(claimBackApply.backRemark);
			$("#backAuditConIdDown", $.pdialog.getCurrent()).val(claimBackApply.backAudiTconName);
			$("#auditDescIdDown", $.pdialog.getCurrent()).text(claimBackApply.auditDesc);
			if(claimBackApply.backReason == 1){
				$("#backReasonIdDown", $.pdialog.getCurrent()).val("系统操作错误");
			} else if(claimBackApply.backReason == 2){
				$("#backReasonIdDown", $.pdialog.getCurrent()).val("专业技术错误");
			} else if(claimBackApply.backReason == 3){
				$("#backReasonIdDown", $.pdialog.getCurrent()).val("法律诉讼结论");
			} else if(claimBackApply.backReason == 4){
				$("#backReasonIdDown", $.pdialog.getCurrent()).val("新关键证据");
			} else if(claimBackApply.backReason == 5){
				$("#backReasonIdDown", $.pdialog.getCurrent()).val("民事申诉调解");
			}
			if(claimBackApply.terminateFlag == 1){
				$("#terminateFlagIdDown", $.pdialog.getCurrent()).attr("checked","checked");
			}
			if(claimBackApply.caseConclusion == 1){
				$("#caseConclusionIdDown", $.pdialog.getCurrent()).val("全部给付");
			} else if(claimBackApply.caseConclusion == 2){
				$("#caseConclusionIdDown", $.pdialog.getCurrent()).val("部分给付");
			} else if(claimBackApply.caseConclusion == 3){
				$("#caseConclusionIdDown", $.pdialog.getCurrent()).val("全部拒付");
			} else if(claimBackApply.caseConclusion == 4){
				$("#caseConclusionIdDown", $.pdialog.getCurrent()).val("公司撤案");
			} else if(claimBackApply.caseConclusion == 5){
				$("#caseConclusionIdDown", $.pdialog.getCurrent()).val("客户撤案");
			} else if(claimBackApply.caseConclusion == 6){
				$("#caseConclusionIdDown", $.pdialog.getCurrent()).val("审核不通过");
			}
			if(claimBackApply.conclusionAf == 1){
				$("#conclusionAfIdDown", $.pdialog.getCurrent()).val("全部给付");
			} else if(claimBackApply.conclusionAf == 2){
				$("#conclusionAfIdDown", $.pdialog.getCurrent()).val("部分给付");
			} else if(claimBackApply.conclusionAf == 3){
				$("#conclusionAfIdDown", $.pdialog.getCurrent()).val("全部拒付");
			} else if(claimBackApply.conclusionAf == 4){
				$("#conclusionAfIdDown", $.pdialog.getCurrent()).val("公司撤案");
			} else if(claimBackApply.conclusionAf == 5){
				$("#conclusionAfIdDown", $.pdialog.getCurrent()).val("客户撤案");
			} else if(claimBackApply.conclusionAf == 6){
				$("#conclusionAfIdDown", $.pdialog.getCurrent()).val("审核不通过");
			}
			//为下面列表赋值
			var insertHtml = "";
			for(var i = 0; i < claimBackApply.claimBackTraceSincereVOList.length; i++){
				insertHtml = "<tr align='center'>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].payeeName+"</td>"
					+ "<td></td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].feeAmount+"</td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].payMode+"</td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].feeStatus+"</td>"
					+ "<td>"+claimBackApply.claimBackTraceSincereVOList[i].dueTime+"</td>" + "</tr>";
			}
		$("#claimRedoHistoryBodyId", $.pdialog.getCurrent()).append(insertHtml);
		$("#claimRedoHistoryBodyId", $.pdialog.getCurrent()).initUI();
		},
	});
}