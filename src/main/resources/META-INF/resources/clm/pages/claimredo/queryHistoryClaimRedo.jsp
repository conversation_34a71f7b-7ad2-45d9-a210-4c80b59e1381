<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%>
	<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>



<s:if test="caseId == null">
		<script type="text/javascript" language="javascript"
	src="clm/pages/claimredo/queryHistoryClaimRedo.js">
</script>
 </s:if>

<s:if test="caseId != null">
				<script type="text/javascript" language="javascript"
	                src="clm/pages/claimredo/queryHistoryClaimRedoDialog.js">
    </script>
 </s:if>

<!-- 页面内容 -->

<div layouth="0" id="queryClaimCasePage" >
	<div>
	<!-- 查询区域 -->
		<form id="queryHistoryClaimRedoId"
		action="clm/claimredo/queryHistoryClaimRedoQuery_CLM_queryHistoryClaimRedo.action" method="post" 
		onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate">
		
		<!-- 查询区域 -->
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
	</div>
    <div class="pageFormInfoContent" id="queryClaimRedoSearch">  
		<dl>	
			<dt>赔案号</dt>
			<dd>
				<input type="text" maxlength="11" id="caseNo" name="claimBackApplyVO.caseNo" value="${claimBackApplyVO.caseNo}" size="25" />
			</dd> 
		</dl> 
		<dl>	
			<dt>出险人姓名</dt>
			<dd>
				<input type="text" id="insuredName" name="claimBackApplyVO.insuredName" value="${claimBackApplyVO.insuredName}" size="25" onblur="resetStyle(this)"/>
			</dd> 
		</dl>
		<dl>	
			<dt>出险人证件号码</dt>
			<dd>
				<input type="text" id="certiCode" name="claimBackApplyVO.certiCode" value="${claimBackApplyVO.certiCode}" size="25" />
			</dd> 
		</dl> 
				<s:if test="caseId == null">
					<div class="pageFormdiv">
						<button type="button" class="but_blue"
							onclick="queryHistoryClaimRedo();">查询</button>

					</div>
				</s:if>

			</div>
    </form>
    </div>
		<div class="tabdivclassbr main_tabdiv">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap>选择</th>
						<th nowrap>序号</th>
						<th nowrap>赔案号</th>
						<th nowrap>保单号</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>出险人证件号码</th>
						<th nowrap>回退申请日期（客户）</th>
						<th nowrap>回退申请日期（审核）</th>    
						<th nowrap>回退申请审核日期</th>
						<th nowrap>任务状态</th>
					</tr>
				</thead>
				<tbody id="checkBody">
					<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="claimBackApplyVOs.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
					<s:iterator value="claimBackApplyVOs"  status="st" id="claimBackAppl">
						<tr align="center">
							<td><input name ="rd" type="radio" onclick="claimRedoHistoryDown(this)" value="${claimBackAppl.applyId}"><input type="hidden" value="${claimBackAppl.caseNo}"/></td>
							<td> ${st.index+1}</td>
							<td><s:property value="#claimBackAppl.caseNo"/></td>
							<td><s:property value="#claimBackAppl.policyCode" /></td>
							<td><s:property value="#claimBackAppl.insuredName" /></td>
							<td><s:property value="#claimBackAppl.certiCode" /></td>
							<td><s:date name="#claimBackAppl.cusApplyDate" format="yyyy-MM-dd"/></td>
							<td><s:date name="#claimBackAppl.applyDate" format="yyyy-MM-dd"/></td>
							<td><s:date name="#claimBackAppl.auditDate" format="yyyy-MM-dd"/></td>
							<td><s:property value="#claimBackAppl.redoStatus" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
		
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">回退历史信息
			</h1>
		</div>
		
		<div class="panelPageFormContent" style="overflow: hidden;">
<!-- 				<div> -->
					<dl>
						<dt>赔 案 号</dt> <dd><input id="caseNoDown" size="20" readonly="true"/></dd>
					</dl>
					<dl>
						<dt>回退申请日期（客户）</dt> <dd><input id="cusApplyDateDown"  size="20" readonly="true"/></dd>
					</dl>
					<dl>
						<dt>回退申请日期（审核）</dt> <dd><input id="applyDateIdDown" size="20" readonly="true" /></dd>
					</dl>
					<dl>
						<dt>原理赔结论</dt> <dd><input id="caseConclusionIdDown" readonly="true" /></dd>
					</dl>
					<dl>
						<dt>原给付总金额</dt><dd> <input id="casePayIdDown" readonly="true" /></dd>
					</dl>
					<dl>
						<dt>拟申请回退后理赔结论</dt><dd><input id="conclusionAfIdDown" readonly="true"/></dd>
					</dl>
					<dl>
						<dt>回退原因</dt>
						<dd>
							<input readonly="readonly" id="backReasonIdDown"/>
						</dd>
					</dl>
					<!-- <dl>
						<dt>回退后需要解除合同</dt>
						<dd>
							<input  id="terminateFlagIdDown" type="checkbox" disabled="disabled"/>	
						<dd>
					</dl> -->
					
<!-- 				</div> -->
<!-- 			</div> -->
<!-- 			<div > -->
				<dl style="width: 100%;height: auto;">
					<dt>回退原因详细说明</dt> 
					<dd>
					<textarea id="treatyConclusionIdDown" rows="3" cols="100" readonly="readonly"></textarea>
					</dd>
				</dl>
<!-- 			</div> -->
<!-- 			<div> -->
				<dl style="width: 100%;height: auto;">
					<dt>备注</dt> 
					<dd>
					<textarea id="backRemarkIdDown" readonly="readonly" rows="3" cols="100"></textarea>
					</dd>
				</dl>
<!-- 			</div> -->
<!-- 			<div> -->
				<dl>
					<dt>回退申请审核结论</dt> 
					<dd>
					<input readonly="readonly" id="backAuditConIdDown"/>
					</dd>
				</dl>
				<dl style="width: 100%;height: auto;">
					<dt>结论描述</dt> 
                    <dd>
					<textarea id="auditDescIdDown" rows="3" cols="100" readonly="readonly"></textarea>
					</dd>
				</dl>
			</div>
			<div class="panelPageFormContent">
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">追偿款信息
			</h1>
		</div>
			<div class="tabdivclassbr">
				<table class="list" style="width: 100%;">
					<thead>
						<tr>
							<th nowrap>领款人</th>
							<th nowrap>通知书号</th>
							<th nowrap>应收金额</th>
							<th nowrap>收费方式</th>
							<th nowrap>费用状态</th>
							<th nowrap>收费日期</th>
						</tr>
					</thead>
					<tbody id="claimRedoHistoryBodyId">
						<s:iterator value="claimPayVOs" var="claimPayVO">
							<tr align="center">
								<td><s:property value="#claimPayVO.claimPayeeVO.payeeName"/></td>
								<td></td>
								<td><s:property value="#claimPayVO.payAmount"/></td>
								<td>现金</td>
								<td>未收取</td>
								<td></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div>	
</div>