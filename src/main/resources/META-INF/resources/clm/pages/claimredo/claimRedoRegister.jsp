<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript"
	src="clm/pages/claimredo/claimRedoRegister.js">
</script>
<script type="text/javascript" language="javascript">
	var caseId = ${claimBackApplyVO.caseId};
	$('#claimRedoChangeProp', navTab.getCurrentPanel()).toggle(
			  function () {
				  $("#claimRedoCmdiv", navTab.getCurrentPanel()).show();
			  },
			  function () {
				  $("#claimRedoCmdiv", navTab.getCurrentPanel()).hide();
			  }
	);
	//影像查询gaojun_wb 
	function redoRegisterClaimImages() {
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		$.post("clm/common/queryClaimImages_CLM_commonQueryAction.action",{caseId:caseId},function(data){
			if(data.message!=""){
				//不等于空点击返回的url
				navTab.openTab("redoRegisterClaimImages", data.message, {title:'影像查询'});
				//window.open(data.message);
			}else{
				alertMsg.error("调用影像接口失败");
			}
		},'json');
	}
	//赔案查询
	function redoRegisterQueryCase() {
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		navTab.openTab("20464","clm/common/otherIframeCase_CLM_commonQueryAction.action",{title:"赔案查询",data:{caseId:caseId}});
	}
</script>

<div  id="reportConfirmJsp">
	<form id="claimRedoRegisterForm"  layoutH="30"
		action="clm/claimredo/registerClaimRedoConfirm_CLM_claimRedoAction.action"
		method="post" class="pageForm required-validate"
		name="form5" onsubmit="return validateCallback(this,redoConfirmAjaxDone)">
	    <input type="hidden" name="surveyApplyVO.bizType" value="5"/>
			<div class="tabsHeader" style="margin-top: -30px; position: absolute; margin-left: 100%; z-index: 5">
				<div class="tabsHeaderContent">
					<div id="claimRedoCmdiv" style="display: none;">
						<div>
							<dl class="colummenu">
								<dt><a href="javaScript:void(0)" onclick="redoRegisterClaimImages();">影像查询</a></dt>
								<dt><a href="javaScript:void(0)" onclick="redoRegisterQueryCase();">赔案查询</a></dt>
							</dl>
						</div>
					</div>
				</div>
			</div>
			
			<a class="button" style="float: right;" id="claimRedoChangeProp" href="#" ><span>!</span></a>
			<div class="divfclass">
		    	<h1> </h1>
	        </div>
			<div class="panelPageFormContent" style="overflow: hidden;" >
				     <dl>
						<dt>赔 案 号</dt>
						<dd>
						   <input name="claimCaseVO.caseNo" value='<s:property value="claimBackApplyVO.caseNo"/>' id="caseNo" size="20" readonly="true"/>
							<input id="caseId" name="caseId" type="hidden" value="${claimBackApplyVO.caseId}">
							<input id="isFirstBack" name="isFirstBack" type="hidden" value="${isFirstBack}">
						</dd>
					</dl>
					<dl>
						<dt>回退申请日期（审核）</dt>
						<dd> <input name="claimBackApplyVO.applyDate"
							value='<s:date name="claimBackApplyVO.applyDate" format="yyyy-MM-dd"/>' id="" size="20"
							readonly="true" />
						</dd>
					</dl>
					<dl>
						<dt>原理赔结论</dt> 
						<dd><input name=""
							value='<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" value="${claimBackApplyVO.caseConclusion}"/>' id="" size="20"
							readonly="true" />
							<input name="claimBackApplyVO.caseConclusion" type="hidden" value="${claimBackApplyVO.caseConclusion}">
					     </dd>
					</dl>
					
					<dl>
						<dt>原给付总金额</dt> 
						<dd><input name="claimBackApplyVO.casePay"
							value='<s:property value="claimBackApplyVO.casePay"/>' id="" size="20"
							readonly="true" />
					   </dd>
					</dl>
					<dl>
						<dt>拟申请回退后的理赔结论</dt> 
						<dd><Field:codeTable cssClass="combox title" id="conclusionAf"  tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" name="claimBackApplyVO.conclusionAf" value="${claimBackApplyVO.conclusionAf}" whereClause="code not in('4')" orderBy="decode(code,'5','3.5',code)"/>
					    </dd>
					</dl>
					<dl>
						<dt><font>* </font>回退申请日期（客户）</dt>
						<dd>
						   <input type="expandDateYMD"  name="claimBackApplyVO.cusApplyDate" id="cusApplyDate" 
						   value="<s:date name="claimBackApplyVO.cusApplyDate" format="yyyy-MM-dd"/>"  />
						</dd>
					</dl>
					
					<%-- <dl>
					  <dt>
						<input name="claimBackApplyVO.terminateFlag"  style="border: 0px none; background: transparent none repeat scroll 0px center; width: auto; float: right; padding: 0px; margin: 0px;" type="checkbox" value="1" <s:if test="claimBackApplyVO.terminateFlag==1">checked="checked"</s:if>/>
						</dt>
					  <dd><span>回退后需要解除合同</span></dd>
					</dl> --%>
					
					<dl>
						<dt>回退原因</dt>
						<dd><Field:codeTable cssClass="combox title"  name="claimBackApplyVO.backReason" tableName="APP___CLM__DBUSER.T_CLAIM_BACK_REASON" value="${claimBackApplyVO.backReason}" whereClause="1=1" orderBy="CODE"/>
					</dd>
					</dl>
					
					<input id="isPayeeSame" name="claimBackApplyVO.isPayeeSame" type="hidden" value="1" />
					
					<dl style="height: auto;">
						<dt >是否为医院撤案(仅限直赔案件)</dt>
						<s:if test ="claimBackApplyVO.isHospitalBackFlag==1">
							<dd>
								<input id="isHospitalBackFlag" name="claimBackApplyVO.isHospitalBackFlag" type="hidden" value="${claimBackApplyVO.isHospitalBackFlag}">
								<Field:codeTable cssClass="combox title" id="isHospitalBack" name="claimBackApplyVO.isHospitalBack" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimBackApplyVO.isHospitalBack }" whereClause="1=1" orderBy="YES_NO"/>
							</dd>
						</s:if>
						<s:else>
							<dd>
								<input id="isHospitalBackFlag" name="claimBackApplyVO.isHospitalBackFlag" type="hidden" value="${claimBackApplyVO.isHospitalBackFlag}">
								<Field:codeTable cssClass="combox title" id="isHospitalBack" name="claimBackApplyVO.isHospitalBack" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimBackApplyVO.isHospitalBack }" whereClause="YES_NO='0'" orderBy="YES_NO" disabled="true"/>
							</dd>
						</s:else>
					</dl>
			
				<dl style="width: 100%;height: auto;">
					<dt>回退原因详细说明</dt> 
					 <dd><textarea id="treatyConclusion"  name="claimBackApplyVO.backDesc" rows="2" cols="70">${claimBackApplyVO.backDesc}</textarea></dd>
				</dl>
				<dl style="width: 100%;height: auto;">
					<dt>备注</dt> 
					 <dd><textarea id="treatyConclusion" name="claimBackApplyVO.backRemark"  rows="2" cols="70">${claimBackApplyVO.backRemark}</textarea>
				    </dd>
				</dl>

		</div>
		
		
		
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">回退审核意见</h1>
	   </div>
		
		<div class="panelPageFormContent">
			<dl>
			 <dt  >回退申请审核结论</dt>
			 <dd><Field:codeValue tableName="APP___CLM__DBUSER.T_BACK_AUDIT_DECISION" value="${claimBackApplyVO.claimBackAuditVO.backAuditCon}"/></dd>
		   </dl> 
			<dl style="width: 100%;height: auto;">
			 <dt>结论描述</dt>
		    	<dd  > <textarea id="treatyConclusion" name=""  rows="2" cols="70" readonly>${claimBackApplyVO.claimBackAuditVO.auditDesc}</textarea> </dd>
			</dl>
		</div>
					

		<div class="formBarButton">
			<ul>
				<li><button type="button" class="but_blue" onclick="redoScanSubmit();">扫描</button></li>
				<li><a class="but_blue main_buta" href="javaScript:void(0)" onclick="redoApplyVerify();"><span>回退申请确认 </span></a></li>
				<li><button type="button" class="but_blue"onclick="exit();">退出</button></li>
				<li><button type="button" class="but_blue"onclick="revokeRedoRegister();">取消回退申请</button></li>
			</ul>
		</div>
	</form>
</div>