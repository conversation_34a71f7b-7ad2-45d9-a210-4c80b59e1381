var isFirstBack = $("#isFirstBack", navTab.getCurrentPanel()).val();
var backAuditCon = $("#backAuditCon", navTab.getCurrentPanel()).val();
var conclusionAf = $("[name='claimBackApplyVO.conclusionAf']", navTab.getCurrentPanel()).val();
var isPayeeSame = $("#isPayeeSame", navTab.getCurrentPanel()).val();
$(function(){
	if(isFirstBack == '0' && backAuditCon == 1 && isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6)){
		$("#backAuditConId", navTab.getCurrentPanel()).setMyComboxDisabled(true,"navTab");
	}
});
//理赔回退申请页面初始化警告提示
function redoApplyVerifyAudit(){
	//需求100470  当“是否为医院撤案”标识为：是 时，该字段只能选择：公司撤案、客户撤案。
	var isHospitalBackFlag = $("#isHospitalBackFlag", navTab.getCurrentPanel()).val();
	var isHospitalBack = $("#isHospitalBack", navTab.getCurrentPanel()).val();
	var conclusionAf = $("[name='claimBackApplyVO.conclusionAf']", navTab.getCurrentPanel()).val();
	var isPayeeSame = $("#isPayeeSame", navTab.getCurrentPanel()).val();
	var backAuditCon = $("#backAuditConId", navTab.getCurrentPanel()).val();
	
	var flag = false;
	//判断本赔案是否为二次回退案件
	if(isFirstBack=='1'){
		alertMsg.confirm("此次回退为本案二次及以上回退，请尽快提交问题单，并在问题单提醒研发实时监测结案后应付金额的调整，在确认应付金额正确后再关闭问题单。");
	}else if((isFirstBack=='0')){//判断赔款收回是否完成
		if(isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6) && backAuditCon != 2){
			//判断有赔款是否未收回
			var caseId = $("#caseId", navTab.getCurrentPanel()).val();
			$.ajax({
				'type':'post',
				'url':'clm/claimredo/findPremArapFinish_CLM_claimRedoAction.action',
				'data':{'caseId':caseId},
				'datatype':'json',
				async: false,
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode==300){
						flag = true;
					}
				},
				'error':function(){
					alertMsg.error("系统出现异常，请联系管理员");
				}
			});
		}
	}
	if(flag){
		alertMsg.error("请收回理赔款并由财务完成收费回销再进行回退！");
		return false;
	}else{
		if(isHospitalBack ==1 && (conclusionAf == 1 || conclusionAf == 2 || conclusionAf == 3 || conclusionAf == 6)){
			alertMsg.error("该案件为医院撤案，拟申请回退后的理赔结论仅支持选择：公司撤案、客户撤案。");
		}else{
			$.ajax({
				'type':'post',
				'url':'clm/claimredo/redoApplyVerify_CLM_claimRedoAction.action',
				'data':$("#redofrom",navTab.getCurrentPanel()).serialize(),
				'datatype':'json',
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode==400){
						alertMsg.confirm(data.message,{
							okCall:function(){
								claimRedoAuditConfirm();
							}
						});
					}else if(data.statusCode==300){
						alertMsg.error(data.message);
					}else{
						claimRedoAuditConfirm();
					}
				},
				'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
				}
			});  
		}		
	}
}

function claimRedoAuditConfirm(){
	 var backAuditCon = $("#backAuditConId", navTab.getCurrentPanel()).val();
	 var conclusionAf = $("[name='claimBackApplyVO.conclusionAf']", navTab.getCurrentPanel()).val();
	 var isPayeeSame = $("#isPayeeSame", navTab.getCurrentPanel()).val();
	 if(isFirstBack == '0' && backAuditCon == 1 && isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6)){
		 $("#backAuditConId", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
		 claimFireEvent($("#backAuditConId",navTab.getCurrentPanel()) );
		}
	 $.ajax({
			'type':'post',
			'url':'clm/claimredo/redoAuditConfirm_CLM_claimRedoAction.action',
			'data':$("#redofrom",navTab.getCurrentPanel()).serialize(),
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode==400){
					alertMsg.warn(data.message);
					navTab.closeCurrentTab();
					//先关闭访问理赔工作台页面
					navTab.closeTab("20345");
					//重新打开理赔工作台页面
					var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
					navTab.openTab("20345", url, {title:'访问理赔工作台'});
		        }
				if(data.statusCode==300){
					alertMsg.error(data.message);
					if(isFirstBack == '0' && backAuditCon == 1 && isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6)){
						$("#backAuditConId", navTab.getCurrentPanel()).setMyComboxDisabled(true,"navTab");
						}
				}
				if(data.statusCode==200){
					alertMsg.correct(data.message);
					//关闭回退审核确认页面
					navTab.closeCurrentTab();
					//先关闭访问理赔工作台页面
					navTab.closeTab("20345");
					//重新打开理赔工作台页面
					var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
					navTab.openTab("20345", url, {title:'访问理赔工作台'});
				}
			//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//				$("#checklistScan", navTab.getCurrentPanel()).click();
			},
			'error':function(){
				alertMsg.error("系统出险异常，请联系管理员");
			}
	});  
}
function redoAuditSave(a){
	$(a).attr("disabled","true");
	$("#backAuditConId", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
	claimFireEvent($("#backAuditConId",navTab.getCurrentPanel()) );
	var backAuditCon = $("#backAuditConId", navTab.getCurrentPanel()).val();
	$("#backAuditConId", navTab.getCurrentPanel()).prev().val(backAuditCon);
	// add by zhuyanrui  #151601
	if("" == $("#cusApplyDate", navTab.getCurrentPanel()).val()){
		alertMsg.error("回退申请日期（客户）不能为空，请填写！");
		return false;
	}
	//需求100470  当“是否为医院撤案”标识为：是 时，该字段只能选择：公司撤案、客户撤案。
	var isHospitalBackFlag = $("#isHospitalBackFlag", navTab.getCurrentPanel()).val();
	var isHospitalBack = $("#isHospitalBack", navTab.getCurrentPanel()).val();
	var conclusionAf = $("[name='claimBackApplyVO.conclusionAf']", navTab.getCurrentPanel()).val();
	var isPayeeSame = $("#isPayeeSame", navTab.getCurrentPanel()).val();
	if(isFirstBack == '0' && backAuditCon == 1 && isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6)){
		 $("#backAuditConId", navTab.getCurrentPanel()).remove("disabled","disabled");
		}
	if(isHospitalBack ==1 && (conclusionAf == 1 || conclusionAf == 2 || conclusionAf == 3 || conclusionAf == 6)){
		alertMsg.error("该案件为医院撤案，拟申请回退后的理赔结论仅支持选择：公司撤案、客户撤案。");
	}else{
		$.ajax({
			'type':'post',
			'url':'clm/claimredo/claimRedoAuditSave_CLM_claimRedoAction.action',
			'data':$("#redofrom",navTab.getCurrentPanel()).serialize(),
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode==300){
					alertMsg.error(data.message);
				}
				if(data.statusCode==200){
					alertMsg.correct(data.message);
					$("#redoAuditConfirm", navTab.getCurrentPanel()).attr("disabled",false);
					$("#redoAuditConfirm", navTab.getCurrentPanel()).attr("class","but_blue");
				}
				//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//				$("#checklistScan", navTab.getCurrentPanel()).click();
				if(isFirstBack == '0' && backAuditCon == 1 && isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6)){
					$("#backAuditConId", navTab.getCurrentPanel()).setMyComboxDisabled(true,"navTab");
				}
				$(a).attr("disabled",null);
			},
			'error':function(){
				alertMsg.error("系统出险异常，请联系管理员");
				if(isFirstBack == '0' && backAuditCon == 1 && isPayeeSame == 1 && (conclusionAf == 4 || conclusionAf == 5 || conclusionAf == 6)){
					$("#backAuditConId", navTab.getCurrentPanel()).setMyComboxDisabled(true,"navTab");
				}
				$(a).attr("disabled",null);
			}
		});  
	}
}
/*function redoAuditClose(){
	alertMsg.confirm("是否确定退出该页面？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
}*/
/*function addTraceSincere(){
	$.ajax({
		'type':'post',
		'url':'clm/claimredo/addTraceSincere_CLM_claimRedoAction.action',
		'data':$("#redofrom",navTab.getCurrentPanel()).serialize(),
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if(data.statusCode==300){
				alertMsg.error(data.message);
			}
			if(data.statusCode==200){
				alertMsg.correct(data.message);
				$("#redoAuditConfirm", navTab.getCurrentPanel()).attr("disabled",false);
			}
			//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//				$("#checklistScan", navTab.getCurrentPanel()).click();
		},
		'error':function(){
			alertMsg.error("系统出险异常，请联系管理员");
		}
	});  
}*/
/*function printRedoTollMessage(){
	$.ajax({
		'type':'post',
		'url':'clm/claimredo/printRedoTollMessage_CLM_claimRedoAction.action',
		'data':$("#redofrom",navTab.getCurrentPanel()).serialize(),
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if(data.statusCode==300){
				alertMsg.error(data.message);
			}
			if(data.statusCode==200){
				alertMsg.correct(data.message);
				$("#redoAuditConfirm", navTab.getCurrentPanel()).attr("disabled",false);
			}
			//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//				$("#checklistScan", navTab.getCurrentPanel()).click();
		},
		'error':function(){
			alertMsg.error("系统出险异常，请联系管理员");
		}
	});  
}*/