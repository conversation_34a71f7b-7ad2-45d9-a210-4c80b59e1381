<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>


<script type="text/javascript" language="javascript"
	src="clm/pages/claimredo/claimRedoAudit.js">
</script>
<script type="text/javascript" language="javascript">
	$('#claimRedoAuditChangeProp', navTab.getCurrentPanel()).toggle(
		  function () {
		  if($('#claimRedoAuditCmdiv', navTab.getCurrentPanel()).css("display") == "none"){
			  $("#claimRedoAuditCmdiv", navTab.getCurrentPanel()).show();
			  } else {
				  $("#claimRedoAuditCmdiv", navTab.getCurrentPanel()).hide();
			  }
		  },
		  function () {
			  if($('#claimRedoAuditCmdiv', navTab.getCurrentPanel()).css("display") == "none"){
			  $("#claimRedoAuditCmdiv", navTab.getCurrentPanel()).show();
			  } else {
				  $("#claimRedoAuditCmdiv", navTab.getCurrentPanel()).hide();
			  }
		  }
	);
	//影像查询
	function redoAuditClaimImages() {
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		$.post("clm/common/queryClaimImages_CLM_commonQueryAction.action",{caseId:caseId},function(data){
			if(data.message!=""){
				//不等于空点击返回的url
				navTab.openTab("redoAuditClaimImages", data.message, {title:'影像查询'});
				//window.open(data.message);
			}else{
				alertMsg.error("调用影像接口失败");
			}
		},'json');
	}
	//赔案查询
	function redoAuditQueryCase() {
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		navTab.openTab("20464","clm/common/otherIframeCase_CLM_commonQueryAction.action",{title:"赔案查询",data:{caseId:caseId}});
	}
	function hideAlertInfo(obj) { //v9.0
		if($("div",navTab.getCurrentPanel()).attr("id")!=obj){
			$(obj).hide();
		}
	}
</script>
<div layoutH="40" id="reportConfirmJsp" class="panelPageFormContent">
	<form action="clm/claimredo/claimRedoAuditSave_CLM_claimRedoAction.action"
		method="post" class="pageForm required-validate" id="redofrom"
		name="form5" onsubmit="return validateCallback(this,myNavTabAjaxDone)">
	<input type="hidden" name="surveyApplyVO.bizType" value="5"/>
			<div class="tabsHeader" >
				<div class="tabsHeaderContent">
					<div id="claimRedoAuditCmdiv" style="display:none;" onmouseleave="hideAlertInfo(claimRedoAuditCmdiv);">
						<div>
							<dl class="colummenu">
								<dt><a href="javaScript:void(0)" onclick="redoAuditClaimImages();">影像查询</a></dt>
								<dt><a href="javaScript:void(0)" onclick="redoAuditQueryCase();">赔案查询</a></dt>
							</dl>
						</div>
					</div>
				</div>
			</div>
			
			<a class="button" style="float: right;" id="claimRedoAuditChangeProp" href="#" ><span>!</span></a>
			
			<div class="panelPageFormContent main_tabdiv">
					<dl>
						<dt>赔 案 号</dt>
						<dd> 
						  <input name="claimCaseVO.caseNo" value='${claimBackApplyVO.caseNo}' id="caseNo1" readonly="true"/>
					   	  <input name="claimBackAuditVO.applyId" type="hidden" value="${claimBackApplyVO.applyId}"/>
					   	  <input id="caseId" name="caseId" type="hidden" value="${claimBackApplyVO.caseId}">
					   	  <input name="claimBackApplyVO.applyId" type="hidden" value="${claimBackApplyVO.applyId}"/>
					   	  <input id="isFirstBack" name="isFirstBack" type="hidden" value="${isFirstBack}">
					   </dd>
					</dl>
					
					<dl>
						<dt>回退申请日期（审核）</dt> 
						<dd> 
						  <input name="" value='<s:date name="claimBackApplyVO.applyDate" format="yyyy-MM-dd"/>' id="" readonly="true" />
						</dd>
					</dl>
					
					<dl>
						<dt>原理赔结论</dt>
						<dd>
						  <input name="" value='<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" value="${claimBackApplyVO.caseConclusion}"/>' id="" readonly="true" />
						</dd>
					</dl>
					
					<dl>
						<dt>原给付总金额</dt>
						<dd> <input name="" value='${claimBackApplyVO.casePay}' id="" readonly="true" /></dd>
					</dl>
					<dl>
						<dt><font>* </font>拟申请回退后的理赔结论</dt>
						<dd>
						<input id="conclusionAf" name="claimBackApplyVO.conclusionAf" type="hidden" value="${claimBackApplyVO.conclusionAf}">
						   <Field:codeTable disabled="true" cssClass="combox title" id="conclusionAfCP"  name="" tableName="APP___CLM__DBUSER.T_CLAIM_AUDIT_DECISION" value="${claimBackApplyVO.conclusionAf}" />
						</dd>
					</dl>
					<dl>
						<dt><font>* </font>回退申请日期（客户）</dt>
						<dd>
						   <input type="expandDateYMD"  name="claimBackApplyVO.cusApplyDate" id="cusApplyDate" 
						   value="<s:date name="claimBackApplyVO.cusApplyDate" format="yyyy-MM-dd"/>"  />
						</dd>
					</dl>
					<%-- <dl>
					 	<dt>回退后需要解除合同</dt>
						<dd>
							<input disabled="disabled" name="" type="checkbox" <s:if test="claimBackApplyVO.terminateFlag==1">checked="checked"</s:if>/>
							<input name="claimBackAuditVO.terminateFlag" type="hidden" value="${claimBackApplyVO.terminateFlag}"/>
						</dd>
					</dl> --%>
					<dl>
						<dt><font>* </font>回退原因</dt> 
						<dd>
						 <Field:codeTable cssClass="combox title"   name="claimBackApplyVO.backReason" tableName="APP___CLM__DBUSER.T_CLAIM_BACK_REASON" value="${claimBackApplyVO.backReason}"/>
						</dd>
					</dl>
						<input id="isPayeeSame" name="claimBackApplyVO.isPayeeSame" type="hidden" value="1" />
					<dl  style="height: auto;">
						<dt>是否为医院撤案(仅限直赔案件)</dt>
						<dd>
							<input id="isHospitalBackFlag" name="claimBackApplyVO.isHospitalBackFlag" type="hidden" value="${claimBackApplyVO.isHospitalBackFlag}">
							<s:if test ="claimBackApplyVO.isHospitalBackFlag==1">
								<Field:codeTable cssClass="combox title" id="isHospitalBack"  name="claimBackApplyVO.isHospitalBack" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimBackApplyVO.isHospitalBack }" whereClause="1=1" orderBy="YES_NO"/>
							</s:if>
							<s:else>
								<Field:codeTable cssClass="combox title" id="isHospitalBack"  name="claimBackApplyVO.isHospitalBack" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimBackApplyVO.isHospitalBack }" whereClause="YES_NO='0'" orderBy="YES_NO" disabled="true"/>
							</s:else>
						</dd>
					</dl>
			
				<dl style="width:100%;height:auto">
					<dt>回退原因详细说明</dt> 
					<dd>
						<textarea id="backDesc" name="claimBackApplyVO.backDesc" rows="3" cols="70" >${claimBackApplyVO.backDesc}</textarea>
						<%-- <input name="claimBackApplyVO.backDesc" value="${claimBackApplyVO.backDesc}" id=""/> --%>
				  	</dd>
				</dl>
				<dl style="width:100%;height:auto">
					<dt>备注</dt> 
					<dd>
						<textarea id="backRemark" name="claimBackApplyVO.backRemark" rows="3" cols="70"  >${claimBackApplyVO.backRemark}</textarea>
				    </dd>
				</dl>
				<dl>
					<dt>回退申请审核结论</dt> 
					<input id="backAuditCon" type="hidden" value="${claimBackApplyVO.claimBackAuditVO.backAuditCon}">
					<dd><Field:codeTable id="backAuditConId" cssClass="combox title"  name="claimBackAuditVO.backAuditCon" tableName="APP___CLM__DBUSER.T_BACK_AUDIT_DECISION" value="${claimBackApplyVO.claimBackAuditVO.backAuditCon}"/>
					<!-- <button type="button" onclick="addTraceSincere();">产生追偿款</button>
					<button type="button" onclick="printRedoTollMessage();">打印回退收费通知书</button> -->
				</dd>
				</dl>
				<dl style="width:100%;height:auto">
					<dt>结论描述</dt> 
					<dd><textarea id="treatyConclusion" name="claimBackAuditVO.auditDesc"  rows="5" cols="70">${claimBackApplyVO.claimBackAuditVO.auditDesc}</textarea> </dd>
				</dl>
				</div>
				<div class="panelPageFormContent">	
					<div class="divfclass">
						<h1><img src="clm/images/tubiao.png">原回退审核意见</h1>
				   </div>
				   
					<dl style="width:100%;height:auto"><dt>回退申请审核结论</dt>
					   <dd><Field:codeValue tableName="APP___CLM__DBUSER.T_BACK_AUDIT_DECISION" value="${claimBackApplyVO.claimBackAuditVO.backAuditCon}"/></dd>
					</dl>
					
					<dl style="width:100%;height:auto" ><dt>结论描述</dt>
						<dd><textarea disabled="disabled" id="treatyConclusion" name="claimDiscussVO.discussContent" 
									rows="5" cols="70">${claimBackApplyVO.claimBackAuditVO.auditDesc}</textarea>
						</dd>
					</dl>
			</div>
		
         <div class="formBarButton">
			<ul>
				<li><button type="button" class="but_blue" onclick="redoAuditSave(this);">保存</button></li>
				<li><button id="redoAuditConfirm" type="button" class="but_gray"  onclick="redoApplyVerifyAudit();" disabled="disabled">回退审核确认</button></li>
				<li><button type="button"  class="but_gray" onclick="exit();">退出</button></li>
			</ul>
		</div>
	</form>
</div>
