var isFirstBack = $("#isFirstBack", navTab.getCurrentPanel()).val();
//redoApplyVerify();
//理赔回退扫描
function redoScanSubmit(){
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).attr("value");
	 $.ajax({
			'type':'post',
			'url':'clm/claimredo/claimRedoScan_CLM_claimRedoAction.action',
			'data':{"claimCaseVO.caseNo":caseNo},
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode==300){
					alertMsg.error(data.message);
				}
				if(data.statusCode==200){
					window.open(data.message);
				}
			//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//				$("#checklistScan", navTab.getCurrentPanel()).click();
			},
			'error':function(){
				alertMsg.error("系统出险异常，请联系管理员");
			}
	});  
}
//理赔回退申请页面初始化警告提示
function redoApplyVerify(){
	//判断本赔案是否为二次回退案件
	if(isFirstBack=='1'){
		alertMsg.error("此次回退为本案二次及以上回退，请尽快提交问题单，并在问题单提醒研发实时监测结案后应付金额的调整，在确认应付金额正确后再关闭问题单。");
		return false;
	}
	// add by zhuyanrui  #151601
	if("" == $("#cusApplyDate", navTab.getCurrentPanel()).val()){
		alertMsg.error("回退申请日期（客户）不能为空，请填写！");
		return false;
	}
	//需求100470  当“是否为医院撤案”标识为：是 时，该字段只能选择：公司撤案、客户撤案。
	var isHospitalBackFlag = $("#isHospitalBackFlag", navTab.getCurrentPanel()).val();
	var isHospitalBack = $("#isHospitalBack", navTab.getCurrentPanel()).val();
	var conclusionAf = $("#conclusionAf", navTab.getCurrentPanel()).val();
	if(isHospitalBack ==1 && (conclusionAf == 1 || conclusionAf == 2 || conclusionAf == 3 || conclusionAf == 6)){
		alertMsg.error("该案件为医院撤案，拟申请回退后的理赔结论仅支持选择：公司撤案、客户撤案。");
	}else{
		$.ajax({
			'type':'post',
			'url':'clm/claimredo/redoApplyVerify_CLM_claimRedoAction.action',
			'data':{'caseId':caseId},
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode==400){
					alertMsg.confirm(data.message,{
						okCall:function(){
							redoRegisterSubmit();
						}
					});
				}else if(data.statusCode==300){
					alertMsg.error(data.message);
				}else{
					redoRegisterSubmit();
				}
			},
			'error':function(){
				alertMsg.error("系统出险异常，请联系管理员");
			}
		});  
	}
	
}
/*function redoRegisterClose(){
	alertMsg.confirm("是否确定退出该页面？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
}*/
//取消回退申请
function revokeRedoRegister(){
	alertMsg.confirm("是否取消回退？",{
		okCall:function(){
			$.ajax({
				'type':'post',
				'url':'clm/claimredo/revokeRedoRegister_CLM_claimRedoAction.action',
				'data':{'caseId':caseId},
				'datatype':'json',
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode==300){
						alertMsg.info(data.message);
					}
					if(data.statusCode==200){
						alertMsg.correct("操作成功，关闭页面");
						navTab.closeCurrentTab();
					}
				},
				'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
				}
			});
		}
	});
}
//回退申请确认
function redoRegisterSubmit(){
	$.ajax({
		'type':'post',
		'url':'clm/claimredo/registerClaimRedoConfirm_CLM_claimRedoAction.action',
		'data':$("#claimRedoRegisterForm", navTab.getCurrentPanel()).serialize() ,
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if(data.statusCode==400){
						alertMsg.warn(data.message);
						navTab.closeCurrentTab();
						//先关闭访问理赔工作台页面
						navTab.closeTab("20345");
						//重新打开理赔工作台页面
						var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
						navTab.openTab("20345", url, {title:'访问理赔工作台'});
			}
			if(data.statusCode==300){
				alertMsg.error(data.message);
			}
			if(data.statusCode==200){
				alertMsg.correct(data.message);
				navTab.closeCurrentTab();
				//先关闭访问理赔工作台页面
				navTab.closeTab("20345");
				//重新打开理赔工作台页面
				var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
				navTab.openTab("20345", url, {title:'访问理赔工作台'});
			}
		},
		'error':function(){
			alertMsg.error("系统出险异常，请联系管理员");
		}
	});
}

//回退申请确认回调函数
function redoConfirmAjaxDone(json) {
	alert(json.statusCode);
	if (json.statusCode == DWZ.statusCode.ok) {
		alertMsg.correct(json.message);
		//若审核确认成功，则刷新访问理赔工作台
		//关闭回退申请页面
		navTab.closeCurrentTab();
		//先关闭访问理赔工作台页面
		navTab.closeTab("20345");
		//重新打开理赔工作台页面
		var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
		navTab.openTab("20345", url, {title:'访问理赔工作台'});
	}else if (json.statusCode == "999"){
		alertMsg.confirm(json.message,{
		 	okCall:function(){
				//关闭回退申请页面
				navTab.closeCurrentTab();
				//先关闭访问理赔工作台页面
				navTab.closeTab("20345");
				//重新打开理赔工作台页面
				var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
				navTab.openTab("20345", url, {title:'访问理赔工作台'});
		 	}
		 });
	} else {
		alertMsg.error(json.message);
	}
}