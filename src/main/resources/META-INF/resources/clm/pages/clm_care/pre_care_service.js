// 选项卡切换 
$(function(){
		$("#preCare", navTab.getCurrentPanel()).click(function(){
			$(this).addClass("selected");
			$("#addCare", navTab.getCurrentPanel()).removeClass("selected"); 
			$("#addCareMissionPage", navTab.getCurrentPanel()).hide();
			$("#preCareServicePage", navTab.getCurrentPanel()).show();
		});
	
		$("#addCare", navTab.getCurrentPanel()).click(function(){
			$(this).addClass("selected");
			$("#preCare", navTab.getCurrentPanel()).removeClass("selected"); 
			$("#preCareServicePage", navTab.getCurrentPanel()).hide();
			$("#addCareMissionPage", navTab.getCurrentPanel()).show();
		});
	});		
	
	//维护关怀服务查询
	function preCareQuery(){
		var caseno = $("#caseNo", navTab.getCurrentPanel()).val();
		if(caseno.length != 0 && caseno.length != 11){
			alertMsg.warn("录入的赔案号不正确，请重新输入！");
			return false;
		}
		if($("#customerName", navTab.getCurrentPanel()).val() != ""){
			if(!checkName($("#customerName", navTab.getCurrentPanel()))){
				return;
			}
		}
		var flag1=$("#institutionNo", navTab.getCurrentPanel()).val()==null||$("#institutionNo", navTab.getCurrentPanel()).val()=="";
		var flag2=$("#caseNo", navTab.getCurrentPanel()).val()==null||$("#caseNo", navTab.getCurrentPanel()).val()=="";
		var flag3=$("#policyNo", navTab.getCurrentPanel()).val()==null||$("#policyNo", navTab.getCurrentPanel()).val()=="";
		var flag4=$("#customerName", navTab.getCurrentPanel()).val()==null||$("#customerName", navTab.getCurrentPanel()).val()=="";
		var flag5=$("#customerId", navTab.getCurrentPanel()).val()==null||$("#customerId", navTab.getCurrentPanel()).val()=="";
		var flag6=$("#visitorName", navTab.getCurrentPanel()).val()==null||$("#visitorName", navTab.getCurrentPanel()).val()=="";
		var flag7=$("#visitMarker", navTab.getCurrentPanel()).val()==null||$("#visitMarker", navTab.getCurrentPanel()).val()=="";
		var flag8=$("#serviceStatusId", navTab.getCurrentPanel()).val()==null||$("#serviceStatusId", navTab.getCurrentPanel()).val()=="";
		if(flag1&&flag2&&flag3&&flag4&&flag5&&flag6&&flag7&&flag8){
		    alertMsg.warn("查询条件当中请至少选择或输入一项");
		}else{
			$("#preCareServicePageForm", navTab.getCurrentPanel()).submit();
		}
	}
	
	//"维护关怀信息页面"点击回访数据列表中的某一选项后，将此行的部分数据信息填入回访结果信息模块相应的选项中
	function selectedRadioInput(thisObj){
		var careServerId = $(thisObj).parent().parent().find("#careServerId").val();
		var visitQueryCareTimes = $(thisObj).parent().parent().find("#visitQueryCareTimes").val();
		//查询回访人信息
		if(careServerId != "" && visitQueryCareTimes == 0){
			$.ajax({
				'type':'post',
				'url':'clm/clmcare/queryCareServer_CLM_preserveCareServiceMsgAction.action',
				'data':{'claimCareServerVO.serverId':$("#careServerId", navTab.getCurrentPanel()).val()},
				'datatype':'json',
				'success':function(data){
					var json = eval("("+data+")");
					var obj = json[0];
					if(obj != undefined){
						$("#visitorType", navTab.getCurrentPanel()).selectMyComBox(obj.serverType);
						$("#visitor", navTab.getCurrentPanel()).val(obj.name);
						$("#visitorPhone", navTab.getCurrentPanel()).val(obj.mobile);
						("#visitResult", navTab.getCurrentPanel()).val(obj.mobile);
					}
					
				},
				'error':function(){
					alertMsg.error("系统异常!");
				}
			});
		}
		if(visitQueryCareTimes == 0){
			
			//赔案NO
			var visitQueryCaseNo = $(thisObj).parent().parent().find("#visitQueryCaseNo").val();
			$("#visitResultCaseNo", navTab.getCurrentPanel()).val(visitQueryCaseNo);
			//出险人姓名
			var visitQueryInsuredName = $(thisObj).parent().parent().find("#visitQueryInsuredName").val();
			$("#visitResultCustomerName", navTab.getCurrentPanel()).val(visitQueryInsuredName);
			//赔案ID
			var visitQueryCaseId = $(thisObj).parent().parent().find("#visitQueryCaseId").val();
			$("#visitResultCaseId", navTab.getCurrentPanel()).val(visitQueryCaseId);
			//出险人ID
			var visitQueryCustomerId = $(thisObj).parent().parent().find("#visitQueryCustomerId").val();
			$("#visitResultCustomerId", navTab.getCurrentPanel()).val(visitQueryCustomerId);
			//备忘录
			var visitQueryRemark = $(thisObj).parent().parent().find("#visitQueryRemark").val();
			$("#remark", navTab.getCurrentPanel()).val(visitQueryRemark);
			//回访内容
			$("#visitContent", navTab.getCurrentPanel()).val("");
			//回访日期
			$("#visitDate", navTab.getCurrentPanel()).val("");
			//联系地址
			$("#contactAddress", navTab.getCurrentPanel()).val("");
			//联系电话
			$("#contactPhone", navTab.getCurrentPanel()).val("");
			//回访结果  0不成功  1成功
			$("#visitResult", navTab.getCurrentPanel()).selectMyComBox("");
			//生存状态  1生存  2身故
			$("select#survivalState", navTab.getCurrentPanel()).selectMyComBox("");
			//关怀对象
			$("#careObject", navTab.getCurrentPanel()).val("");
			//关怀对象类型
			$("select#careTargetType", navTab.getCurrentPanel()).selectMyComBox("");
			//健康状态
			$("select#healthStatus", navTab.getCurrentPanel()).selectMyComBox("");
			//家庭状态
			$("select#familyStatus", navTab.getCurrentPanel()).selectMyComBox("");
			//服务状态
			$("select#serviceStatus", navTab.getCurrentPanel()).selectMyComBox("");
			//关怀隶属人群
			$("select#careTargetCrowd", navTab.getCurrentPanel()).selectMyComBox("");
			//关怀对象
			var return_careObjectName = $(thisObj).parent().parent().find("#return_careObjectName").val();
			$("#careObjectName", navTab.getCurrentPanel()).val(return_careObjectName);
			//关怀对象类型*
			var return_careObjectType = $(thisObj).parent().parent().find("#return_careObjectType").val();
			$("#careTargetType", navTab.getCurrentPanel()).selectMyComBox(return_careObjectType);
			$("#careObjectId", navTab.getCurrentPanel()).val(return_careObjectType);
			//联系地址
			var return_contactAddress = $(thisObj).parent().parent().find("#return_contactAddress").val();
			$("#contactAddress", navTab.getCurrentPanel()).val(return_contactAddress);
			//联系电话
			var return_contactPhone = $(thisObj).parent().parent().find("#return_contactPhone").val();
			$("#contactPhone", navTab.getCurrentPanel()).val(return_contactPhone);
			//关怀ID
			var visitQueryCareId = $(thisObj).parent().parent().find("#visitQueryCareId").val();
			$("#visitQueryCareIdSave", navTab.getCurrentPanel()).val(visitQueryCareId);
		}else{
			//赔案NO
			var visitQueryCaseNo = $(thisObj).parent().parent().find("#visitQueryCaseNo").val();
			$("#visitResultCaseNo", navTab.getCurrentPanel()).val(visitQueryCaseNo);
			//出险人姓名
			var visitQueryInsuredName = $(thisObj).parent().parent().find("#visitQueryInsuredName").val();
			$("#visitResultCustomerName", navTab.getCurrentPanel()).val(visitQueryInsuredName);
			//赔案ID
			var visitQueryCaseId = $(thisObj).parent().parent().find("#visitQueryCaseId").val();
			$("#visitResultCaseId", navTab.getCurrentPanel()).val(visitQueryCaseId);
			//出险人ID
			var visitQueryCustomerId = $(thisObj).parent().parent().find("#visitQueryCustomerId").val();
			$("#visitResultCustomerId", navTab.getCurrentPanel()).val(visitQueryCustomerId);
			//关怀ID
			var visitQueryCareId = $(thisObj).parent().parent().find("#visitQueryCareId").val();
			$("#visitQueryCareIdSave", navTab.getCurrentPanel()).val(visitQueryCareId);
			/*//回访内容
			var visitQueryVisitContent = $(thisObj).parent().parent().find("#visitQueryVisitContent").val();
			$("#visitContent", navTab.getCurrentPanel()).val(visitQueryVisitContent);
			//回访日期
			var visitQueryVisitDate = $(thisObj).parent().parent().find("#visitQueryVisitDate").val();

			$("#visitDate", navTab.getCurrentPanel()).val(visitQueryVisitDate);
			//回访人类型
			var visitQueryServerType = $(thisObj).parent().parent().find("#visitQueryServerType").val();
			$("#visitorType", navTab.getCurrentPanel()).selectMyComBox(visitQueryServerType);
			//回访人
			var visitQueryVisiteBy = $(thisObj).parent().parent().find("#visitQueryVisiteBy").val();
			$("#visitor", navTab.getCurrentPanel()).val(visitQueryVisiteBy);
			//回访人电话
			var visitQueryVisitorPhone = $(thisObj).parent().parent().find("#visitQueryVisitorPhone").val();
			$("#visitorPhone", navTab.getCurrentPanel()).val(visitQueryVisitorPhone);
			//备忘录
			var visitQueryRemark = $(thisObj).parent().parent().find("#visitQueryRemark").val();
			$("#remark", navTab.getCurrentPanel()).val(visitQueryRemark);
			//联系地址
			var visitQueryContactAddress = $(thisObj).parent().parent().find("#visitQueryContactAddress").val(); 
			$("#contactAddress", navTab.getCurrentPanel()).val(visitQueryContactAddress);
			//联系电话
			var visitQueryContactPhone = $(thisObj).parent().parent().find("#visitQueryContactPhone").val(); 
			$("#contactPhone", navTab.getCurrentPanel()).val(visitQueryContactPhone);
			//回访结果  0不成功  1成功
			var visitQueryIsSucceed = $(thisObj).parent().parent().find("#visitQueryIsSucceed").val(); 
			$("#visitResult", navTab.getCurrentPanel()).selectMyComBox(visitQueryIsSucceed);
			//生存状态  1生存  2身故
			var visitQueryLiveStatus = $(thisObj).parent().parent().find("#visitQueryLiveStatus").val(); 
			$("select#survivalState", navTab.getCurrentPanel()).selectMyComBox(visitQueryLiveStatus);
			//关怀对象
			var visitQueryCareObjectId = $(thisObj).parent().parent().find("#visitQueryCareObjectId").val(); 
			$("#careObjectId", navTab.getCurrentPanel()).val(visitQueryCareObjectId);
			//关怀对象类型
			var visitQueryCareObjectType = $(thisObj).parent().parent().find("#visitQueryCareObjectType").val();
			$("select#careTargetType", navTab.getCurrentPanel()).selectMyComBox(visitQueryCareObjectType);
			$("select#careObjectId", navTab.getCurrentPanel()).val(visitQueryCareObjectType);
			//健康状态
			var visitQueryHealthStatus = $(thisObj).parent().parent().find("#visitQueryHealthStatus").val(); 
			$("select#healthStatus", navTab.getCurrentPanel()).selectMyComBox(visitQueryHealthStatus);
			//家庭状态
			var visitQueryFamilyStatus = $(thisObj).parent().parent().find("#visitQueryFamilyStatus").val(); 
			$("select#familyStatus", navTab.getCurrentPanel()).selectMyComBox(visitQueryFamilyStatus);
			//服务状态
			var visitQueryServiceStatus = $(thisObj).parent().parent().find("#visitQueryServiceStatus").val(); 
			$("select#serviceStatus", navTab.getCurrentPanel()).selectMyComBox(visitQueryServiceStatus);
			//关怀隶属人群
			var visitQueryCareCrowd = $(thisObj).parent().parent().find("#visitQueryCareCrowd").val(); 
			$("select#careTargetCrowd", navTab.getCurrentPanel()).selectMyComBox(visitQueryCareCrowd);
			//关怀ID
			var visitQueryCareId = $(thisObj).parent().parent().find("#visitQueryCareId").val();
			$("#visitQueryCareIdSave", navTab.getCurrentPanel()).val(visitQueryCareId);
			//下次回访日期
			var nextVisitDateId = $(thisObj).parent().parent().find("td:eq(8)").text();
			$("#nextVisitDate", navTab.getCurrentPanel()).val(nextVisitDateId);
			//提醒日期
			var remindDateId = $(thisObj).parent().parent().find("td:eq(9)").find("#return_remindDate").val();
			$("#remindDate", navTab.getCurrentPanel()).val(remindDateId);*/
		}
		
	}
	
	//回访内容自动生成(健康状态+选择的结果，家庭状态+选择结果拼在一起带入),用户可在其后面增加内容
	function createVisitContent(){
		var healthStatus = $("#healthStatus", navTab.getCurrentPanel()).val();
		if (healthStatus == '2') {
			healthStatus = "一般";
		} else if (healthStatus == '3') {
			healthStatus = "差";
		} else if(healthStatus == '1') {
			healthStatus = "良好";
		}
		var familyStatus = $("#familyStatus", navTab.getCurrentPanel()).val();
		if (familyStatus == '2') {
			familyStatus = "一般";
		} else if (familyStatus == '3') {
			familyStatus = "差";
		} else if(familyStatus == '1') {
			familyStatus = "良好";
		}
		if(healthStatus != null && healthStatus != "" && (familyStatus == null || familyStatus == "")) {
			$("#visitContent", navTab.getCurrentPanel()).val("健康状态："+healthStatus );
		} else if (familyStatus != null && familyStatus != "" && (healthStatus == null || healthStatus == "")) {
			$("#visitContent", navTab.getCurrentPanel()).val("家庭状态：" + familyStatus );
		} else if (healthStatus != null && healthStatus != "" && familyStatus != null || familyStatus != ""){
			$("#visitContent", navTab.getCurrentPanel()).val("健康状态："+healthStatus + " " + "家庭状态：" + familyStatus);
		} else {
			$("#visitContent", navTab.getCurrentPanel()).val("");
		}
	}
	//选择健康/家庭状态更新回访内容
	function changeVisitContent(){
		createVisitContent();
	}
	
	function isAcceptMegCheck(){
		$("input[type='checkbox']", navTab.getCurrentPanel()).each(function(){
			if($(this).prop('checked') == false){
				$("#careIsAcceptMeg", navTab.getCurrentPanel()).val(0);
			}
		});
	}
	
	function saveVisitObject(){//关怀对象类型     回访人赔案号   备注
		if($("#careObjectName", navTab.getCurrentPanel()).val()!=""){
			if(!checkName($("#careObjectName", navTab.getCurrentPanel()))){
				return;
			}
		}
		if($("#visitor", navTab.getCurrentPanel()).val()!=""){
			if(!checkName($("#visitor", navTab.getCurrentPanel()))){
				return;
			}
		}
		if($("#visitResult", navTab.getCurrentPanel()).val()==""||$("#visitResult", navTab.getCurrentPanel()).val()==null) {
			alertMsg.error("回访结果不能为空");
		}else if($("#survivalState", navTab.getCurrentPanel()).val()==""||$("#survivalState", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("生存状态不能为空,请选择一项");
		}else if($("#visitDate", navTab.getCurrentPanel()).val()==""||$("#visitDate", navTab.getCurrentPanel()).val()==null){
	 		alertMsg.error("回访日期不能为空");
		}else if($("#careObjectName", navTab.getCurrentPanel()).val()==""||$("#careObjectName", navTab.getCurrentPanel()).val()==null){
	 		alertMsg.error("关怀对象不能为空");
		}else if($("#healthStatus", navTab.getCurrentPanel()).val()==""||$("#healthStatus", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("健康状态不能为空,请选择一项");
		}else if($("#familyStatus", navTab.getCurrentPanel()).val()==""||$("#familyStatus", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("家庭状态不能为空,请选择一项");
		}else if($("#visitContent", navTab.getCurrentPanel()).val()==null||$.trim($("#visitContent", navTab.getCurrentPanel()).val())==""){
	 		alertMsg.error("回访内容不能为空");
		}else if($("#careTargetType", navTab.getCurrentPanel()).val()==""||$("#careTargetType", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("关怀对象类型不能为空,请选择一项");
		}else if($("#careTargetCrowd", navTab.getCurrentPanel()).val()==""||$("#careTargetCrowd", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("关怀对象隶属人群不能为空,请选择一项");	
		}else if($("#visitResultCaseNo", navTab.getCurrentPanel()).val()==""||$("#visitResultCaseNo", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("赔案号不能为空");
		}else if ($("#serviceStatus", navTab.getCurrentPanel()).val()==""||$("#serviceStatus", navTab.getCurrentPanel()).val()==null){
			alertMsg.error("服务状态不能为空");
		}else{
			//$("#saveVisitResultForm", navTab.getCurrentPanel()).submit();	
			$.ajax({
				'type':'post',
				'url':'clm/clmcare/saveVisitResult_CLM_preserveCareServiceMsgAction.action',
				'data':$("#saveVisitResultForm", navTab.getCurrentPanel()).serialize(),
				'datatype':'json',
				'success':function(data){
					if(data==1){
						alertMsg.correct("保存成功"); 
						//刷新关怀任务信息
						preCareQuery();
					}else if(data==0){
						alertMsg.warn("保存失败");
					}else if(data==2){
						alertMsg.warn("此任务已关闭,不可再回访");
					}else if(data==3){
						alertMsg.warn("联系电话验证错误，请检查！");
					}else if(data==4){
						alertMsg.warn("回访人电话验证错误，请检查！");
					}
				},
				'error':function(){
					alertMsg.error("保存失败!");
				}
		   });  
	 	}
	}
	
	//添加关怀任务查询
	function addCareQuery(){
		var caseno = $("#caseNo2", navTab.getCurrentPanel()).val();
		if(caseno.length != 0 && caseno.length != 11){
			alertMsg.warn("录入的赔案号不正确，请重新输入！");
			return false;
		}
		var flag1=$("#caseNo2", navTab.getCurrentPanel()).val()==null||$("#caseNo2", navTab.getCurrentPanel()).val()=="";
		var flag2=$("#policyNo2", navTab.getCurrentPanel()).val()==null||$("#policyNo2", navTab.getCurrentPanel()).val()=="";
		var flag3=$("#customerName2", navTab.getCurrentPanel()).val()==null||$("#customerName2", navTab.getCurrentPanel()).val()=="";
		var flag4=$("#customerId2", navTab.getCurrentPanel()).val()==null||$("#customerId2", navTab.getCurrentPanel()).val()=="";
		if(flag1&&flag2&&flag3&&flag4){
		    alertMsg.error("查询条件当中请至少输入一项");
		}else{
			//$('#queryClaimCare', navTab.getCurrentPanel()).submit();
			
			//执行ajax查询返回数据
			$.ajax({
				url:"clm/clmcare/queryAddCareMission_CLM_preserveCareServiceMsgAction.action",
				type:"POST",
				data:{"caseNo":$("#caseNo2", navTab.getCurrentPanel()).val(),
					"policyNo":$("#policyNo2", navTab.getCurrentPanel()).val(),
					"customerName":$("#customerName2", navTab.getCurrentPanel()).val(),
					"customerId":$("#customerId2", navTab.getCurrentPanel()).val()},
				success:function(data){
					if(data==null){
						$("#addCareResult", navTab.getCurrentPanel()).empty();
						alertMsg.error("您要查询的理赔关怀任务不存在，请重新输入查询条件");
					}else{
						var json = eval("("+data+")");
						callbackacc(json);
					}
				} 
			});	
		}
	}
	
	function callbackacc(jsonlist){
		$("#addCareResult", navTab.getCurrentPanel()).empty();
		for(var i=0;i<jsonlist.length;i++){
			var interHtml = "<tr id='addCareResultInfo'>"
				+ "<td style='width:50px'>"+(i+1)+"&nbsp&nbsp"
				+ "<input type='checkbox' flag='flag' name='addCareCheckBox' />"
				+ "</td>"
				+ "<td style='width:50px' name='claimCareVOList.careId'>"
				+ jsonlist[i].caseId
				+ "</td>"
				+ "<td style='width:100px' name='claimCareVOList.caseNo'>"
				+ jsonlist[i].caseNo
				+ "</td>"
				+ "<td style='width:100px'>"
				+ jsonlist[i].customerName
				+ "</td>"
				+ "<td style='width:100px'>"
				+ jsonlist[i].organName
				+ "</td>"
				+ "<td style='width:115px'>"
				+ jsonlist[i].claimType
				+ "</td>"
				+ "<td style='width:115px'>"
				+jsonlist[i].caseResult
				+ "</td>"
				+ "<td style='width:115px'>"
				+ jsonlist[i].claimDateAll
				+ "</td>"
				+ "<td style='width:100px'>"
				+ jsonlist[i].endCaseTime
				+ "</td>" + "</tr>";
			$("#addCareResult", navTab.getCurrentPanel()).append(interHtml);
		}
	}
	
	//添加关怀任务选项卡中的赔案信息列表确认
	function addCareConfirm(){
		if($("input[name='addCareCheckBox']", navTab.getCurrentPanel()).is(':checked')==false){
			alertMsg.error("请至少选择一条数据!");
			return false;
		} else {
			//获取所有被选中的数据
			var selectedObj = $("input[name='addCareCheckBox']", navTab.getCurrentPanel());
			var jsonFinalStr="";
			var count=0;
		    for(var i=0;i<selectedObj.length;i++){
		    	
			    if(selectedObj[i].checked){
			    	//获取被勾选的行的所有子元素对象
					var siblingObject=$(selectedObj[i]).parent().nextAll(); 
					 jsonStr="{   'caseId':"+$(siblingObject[0]).html()+",'caseNo':"+$(siblingObject[1]).html()
									+",'customerName':"+$(siblingObject[2]).html()
									+",'organName':"+$(siblingObject[3]).html()
									+",'claimType':"+$(siblingObject[4]).html()
									+",'caseResult':"+$(siblingObject[5]).html()
									+",'caseDate':"+$(siblingObject[6]).html()
									+",'endDate':"+$(siblingObject[7]).html()+"}";
					count++;
					if(count>1){
						jsonFinalStr =jsonFinalStr+","+jsonStr;
					}else{
						jsonFinalStr =jsonFinalStr+jsonStr;
					}
			   }
		    }
		    jsonFinalStr="["+jsonFinalStr+"]";
		    //通过ajax将数据保存，并返回保存结果
			$.ajax({
				url:"clm/clmcare/addCareMission_CLM_preserveCareServiceMsgAction.action",
				type:"POST",
				data:{"saveAddCareMissionInfo":jsonFinalStr},
				success:function(data){	
					//此处的判断条件需要根据返回的值的类型进行修改，此处暂作如下处理
					if(data==1){
						alertMsg.correct("添加成功");   
					}else if (data==2) {
						alertMsg.warn("案件不符合产生关怀任务规则");   
					}else if (data==3){
						alertMsg.warn("案件不符合生成关怀对象规则");   
					}else{
						alertMsg.warn("您添加的理赔关怀任务记录重复，请检查");//后台返回0
					}
				} 
			});	
		}
		
	}
	
	//获取当前日期
	function getCurrentDate(){ 
		var date=new Date();
		var dateStr=date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate();
		$("#writeDate", navTab.getCurrentPanel()).val(dateStr) ;
	}
	
	/*function exitPreCareVisit(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		 });
	}*/
	
	function careTargetTypeChange(obj){
		$("#careObjectId", navTab.getCurrentPanel()).val($(obj).val());
	}