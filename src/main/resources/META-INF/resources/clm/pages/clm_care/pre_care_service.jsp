<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--   引入jQuery -->
<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="clm/pages/clm_care/pre_care_service.js"></script>
<script>
function changeCaseNO(obj){
	obj.value=obj.value.replace(/\D/g,'');
	if(obj.value[0]!=9){
		obj.value='';
	}
}

function queryCareObject(){
	 var visitResultCaseNo = $("#visitResultCaseNo", navTab.getCurrentPanel()).val();
	 var visitResultCaseId = $("#visitResultCaseId", navTab.getCurrentPanel()).val();
	 var visitResultCustomerId = $("#visitResultCustomerId", navTab.getCurrentPanel()).val();
	 $('#queryObject', navTab.getCurrentPanel()).attr("href","clm/clmcare/queryCareObjectInit_CLM_preserveCareServiceMsgAction.action?caseNo="+visitResultCaseNo+"&caseId="+visitResultCaseId+"&customerNameId="+visitResultCustomerId);			
}
if('${msg}'!=''){
	alertMsg.warn("您要查询的理赔关怀任务不存在，请重新输入查询条件!");
}
</script>
<!-- 退出层样式 -->
<style>
	#outer_exit { clear:both;   height:710px;  }
</style>

<!-- 退出层 -->
<div class="pageContent" layoutH="0" id="outer_exit">
	<!-- 选项卡 -->
	<div class="pageContent">
		<div class="tabs" currentIndex="0" eventType="click">
			<div class="tabsHeader">
				<div class="tabsHeaderContent">
					<ul>
						<li> <span class="j-ajax" id="preCare">维护关怀服务信息</span> </li>
						<li> <span class="j-ajax" id="addCare">添加关怀任务</span> </li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	<!-- 选项卡内容 -->
	<div class="tab_box">
		<div layoutH="0" id="preCareServicePage">
				<!-- 分页查询访问路径 -->
				<form id="pagerForm" method="post"
		              action="clm/clmcare/queryPerserveCareServiceMsg_CLM_preserveCareServiceMsgAction.action">
		              <input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		              <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
                </form>
				<!-- 查询事件访问路径 -->
				<form id="preCareServicePageForm"
					action="clm/clmcare/queryPerserveCareServiceMsg_CLM_preserveCareServiceMsgAction.action" method="post" 
					onsubmit="return navTabSearch(this)"
					class="pagerForm required-validate" rel="pagerForm" name=fm>		
					
					<!-- 查询区域 -->
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">查询条件
						</h1>
					</div>
							
					    <div class="pageFormInfoContent" id="div1">  
					    	<dl>
						    	<dt>机构号码</dt>
						    	<dd>
						    		<!-- 
									<Field:codeTable id="institutionNo" name="institutionNo" value="${institutionNo}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD"/>
									 -->
									<input id="institutionNo" name="institutionNo"  style="width: 30px;border-right:0px"
										type="text" class="organ" clickId="menuBtn" showOrgName="institutionName" 
										needAll="true" size="8"
										value="<s:property value='institutionNo'/>"/>
									<input id="institutionName" type="text" readOnly size="15"  style="width:110px;"
										name="institutionName"
										value="<s:property value='institutionName'/>"/> 
									<a id="menuBtn" class="btnLook" href="#"></a>
							    </dd>
							</dl>    
							<dl  >	
								<dt>赔案号</dt>
								<dd>
									<input id="caseNo" type="text" name="caseNo" value="${caseNo}" maxlength="11"  size="25" onkeyup="changeCaseNO(this)"/>
								</dd> 
							</dl> 
							<dl  >	
								<dt>保单号</dt>
								<dd>
									<input id="policyNo" type="text" name="policyNo" value="${policyNo}"  size="25" onkeyup="this.value=this.value.replace(/[^\w\.\/]/ig,'')"/>
								</dd> 
							</dl> 
							<dl>	
								<dt>出险人</dt>
								<dd>
									<input id="customerName" type="text" name="customerName" value="${customerName}"  size="25" onkeyup="this.value=this.value.replace(/\s/g,'')" onblur="resetStyle(this)" />
								</dd> 
							</dl>
			       			<dl>	
								<dt>证件号码</dt>
								<dd>
									<input id="customerId" type="text" name="customerId" value="${customerId}"  size="25"  />
								</dd>
							</dl>
			       			<dl>	
								<dt>回访人</dt>
								<dd>
									<input id="visitorName" type="text" name="visitorName" value="${visitorName }"  size="25" onkeyup="this.value=this.value.replace(/\s/g,'')" />
								</dd> 
							</dl>
							<dl>
						    	<dt>回访标志</dt>
						    	<dd>
						    	    <select class="combox"  id="visitMarker" name="visitMarker">
						    	       <option value="3">全部</option>
						    	       <s:if test="visitMarker == 1"><option value="1" selected="selected">待回访</option></s:if>
						    	       <s:else><option value="1">待回访</option></s:else>
						    	       <s:if test="visitMarker == 2"><option value="2" selected="selected">已回访</option></s:if>
						    	       <s:else><option value="2">已回访</option></s:else>
						    	    </select>
							    </dd>
							</dl>
							<%-- <dl>
						    <dt>服务状态</dt>
						    	<dd>
							        <select class="selectToInput"  name="serviceStatusId" id="serviceStatusId" >
							           <s:if test="serviceStatusId == 2"><option value="2" selected="selected">继续访问</option></s:if>
						    	       <s:else><option value="2">继续访问</option></s:else>
						    	       <s:if test="serviceStatusId == 1"><option value="1" selected="selected">关闭</option></s:if>
						    	       <s:else><option value="1">关闭</option></s:else>
                                    </select>
	                            </dd>
						    </dl> --%>						 
							<div class="pageFormdiv">
			                      <button type="button"class="but_blue"   onclick="preCareQuery();">查询</button>
				   			</div>
					   </div>
				</form>	
					
				<!-- 显示回访数据列表区域 -->	
				   <div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">回访数据列表
						</h1>
					</div>
					<div class="tabdivclassbr">
						<table class="list" width="100%">
							<thead>
								<tr>
			                   		<th nowrap>选择</th>
									<th nowrap>赔案号</th>
									<th nowrap>出险人姓名</th>
									<th nowrap>管理机构</th>
									<th nowrap>理赔类型</th>
									<th nowrap>出险结果</th>
									<th nowrap>出险日期</th>
									<th nowrap>结案日期</th>
									<th nowrap>下次回访日期</th>
									<th nowrap>关怀次数</th>
								</tr>
							</thead>
							<tbody>
								<!-- 循环显示数据 -->
								<s:if test="imageFlag != null">
									<tr>
										<td colspan="10">
											<div class="noRueryResult">请选择条件查询数据！</div>
										</td>
									</tr>
								</s:if>
								<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
									<tr>
										<td colspan="10">
											<div class="noRueryResult">没有符合条件的查询结果！</div>
										</td>
									</tr>
								</s:elseif>
								<s:iterator value="currentPage.PageItems" var="claimCareVisitVO" status="st">
									<tr style="text-align:center;">
										<!-- 点击单选钮后，此单选按钮所在行的信息自动填入"回访结果信息"模块的相应位置 -->
										<td><input type="radio" name="visitDataListRadio" id=id<s:property value="#st.index"/> 
													 onclick="selectedRadioInput(this);" /></td>
										<td><input type="hidden" id="visitQueryCaseNo" value="${caseNo}">
										    <input type="hidden" id="visitQueryCaseId" value="${caseId}">
										    <s:property value="caseNo" />
										</td>
										<td><input type="hidden" id="visitQueryInsuredName" value="${customerName}"><s:property value="customerName" /></td>
										<td><input type="hidden" id="visitQueryOrganCode" value="${organCode}"><s:property value="organName" /></td>
										<td><s:property value="claimType" /></td>
										<td><s:property value="accResult2" /></td>
										<td>${claimDate}</td>
										<td><s:date name='endCaseTime' format='yyyy-MM-dd' /></td>
										<td><s:date name='nextVisitDate' format='yyyy-MM-dd' /></td>
										<td>
										<input type="hidden" id="visitQueryVisitDate" value="<s:date name='visitDate' format='yyyy-MM-dd' />"> 
										<input type="hidden" id="visitQueryCareTimes" value="${careTimes}"><s:property value="careTimes" />
										<input type="hidden" id="visitQueryIsSucceed" value="${isSucceed}">
										<input type="hidden" id="visitQueryLiveStatus" value="${liveStatus}">
										<input type="hidden" id="visitQueryCareObjectId" value="${careObjectId}">
										<input type="hidden" id="visitQueryCareObjectType" value="${careObjectType}">
										<input type="hidden" id="visitQueryContactAddress" value="${contactAddress}">
										<input type="hidden" id="visitQueryContactPhone" value="${contactPhone}">
										<input type="hidden" id="visitQueryHealthStatus" value="${healthStatus}">
										<input type="hidden" id="visitQueryFamilyStatus" value="${familyStatus}">
										<input type="hidden" id="visitQueryVisitContent" value="${visitContent}">
										<input type="hidden" id="visitQueryServiceStatus" value="${serviceStatus}">
										<input type="hidden" id="visitQueryIsAcceptMeg" value="${isAcceptMeg}">
										<input type="hidden" id="visitQueryCareCrowd" value="${careCrowd}">
										<input type="hidden" id="visitQueryCustomerId" value="${customerNameId}">
										<input type="hidden" id="visitQueryCareId" value="${careId}">
										<input type="hidden" id="careServerId" value="${serverId}">
										<input type="hidden" id="visitQueryRemark" value="${remark}">
										<input type="hidden" id="visitQueryServerType" value="${serverType}">
										<input type="hidden" id="visitQueryVisiteBy" value="${visiteBy}">
										<input type="hidden" id="visitQueryVisitorPhone" value="${visitorPhone}">
										
										<input type="hidden" id="return_careObjectName" value="${careObjectName}">
										<input type="hidden" id="return_careObjectType" value="${careObjectType}">
										<input type="hidden" id="return_contactAddress" value="${contactAddress}">
										<input type="hidden" id="return_contactPhone" value="${contactPhone}">
										<input type="hidden" id="return_remindDate" value="<s:date name='remindDate' format='yyyy-MM-dd' />">
										</td>
									</tr>
								</s:iterator>
			                </tbody>
			   		   </table>
			   		   <!-- 分页查询区域 -->
						<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}">
				</div>
		    </div>
     </div>
			   	<!-- 保存回访结果信息  action="clm/clmcare/saveVisitResult_CLM_preserveCareServiceMsgAction.action" method="post"  -->
				<form id="saveVisitResultForm"
					class="pagerForm required-validate" novalidate="novalidate" onsubmit="return validateCallback(this, navTabAjaxDone)" >	
						   <div class="divfclass">
							  <h1><img src="clm/images/tubiao.png">回访结果信息</h1>
						   </div>
							<div class="panelPageFormContent" >  
						    	<dl >	
									<dt>赔案号</dt> 
									<dd>
									    <input id="visitResultCaseNo" type="text" name="claimCareVisitVO.caseNo" value=""  size="25" readonly="readonly"/>
									    <input type="hidden" id="visitResultCaseId"  name="claimCareVisitVO.caseId" value="${claimCareVisitVO.caseId}"/>
									    <input type="hidden" id="visitQueryCareIdSave" name="claimCareVisitVO.careId" />
									</dd> 
								</dl> 
						    	<dl>	
									<dt>出险人姓名</dt>
									<dd>
									    <input id="visitResultCustomerId" type="hidden" name="claimCareVisitVO.customerNameId"  size="25" readonly="readonly"/>
										<input id="visitResultCustomerName" type="text" name=""  size="25" readonly="readonly"/>
									</dd> 
								</dl>
						    	<dl>
							    	<dt><font class="point" color="red">* </font>回访结果</dt>
							    	<dd>
										<select class="combox"  id="visitResult" name="claimCareVisitVO.isSucceed"  >
											<option value="">请选择</option>
											<option value="1" >成功</option>
											<option value="0" >不成功</option>
										</select>
								    </dd>
								</dl>  
							  	<dl>
							    	<dt><font class="point" color="red">* </font>生存状态</dt>
							    	<dd> 
										<Field:codeTable cssClass="combox title"  id="survivalState" name="claimCareVisitVO.liveStatus" tableName="APP___CLM__DBUSER.T_LIFE_STATE" nullOption="true"/> 
								    </dd>
								</dl> 
						    	<dl>
							    	<dt><font class="point" color="red">* </font>回访日期</dt>
							    		<dd>
											<input type="expandDateYMD" name="claimCareVisitVO.visitDate" size="22" class="date"
													id="visitDate" value="<s:date name='claimCareVisitVO.visitDate' format='yyyy-MM-dd' />" />
											<a class="inputDateButton" href="javascript:;">选择</a>
										</dd>
						    	</dl>
						    	<dl>	
									<dt><font class="point" color="red">* </font>关怀对象</dt>
									<dd>
									   <input id="careObjectName" name="claimCareVisitVO.careObjectName"
							                   type="text" onblur="resetStyle(this)"
							                   postField="keyword" 
							                   suggestFields="careObjectId,careObjectName,careObjectType,contactAddress,contactPhone"
							                   suggestUrl="clm/pages/clm_care/queryCareVisitObject.jsp"/> 
						               <a class="btnLook" id="queryObject" onclick="queryCareObject()"
						                    lookupGroup="claimCareVisitVO">理赔关怀》查找关怀对象</a>
									</dd>
								</dl>
									<input id="careObjectId" type="hidden" name="claimCareVisitVO.careObjectId">
								<dl>	
									<dt><font class="point" color="red">* </font>关怀对象类型</dt>
									<dd>
										<Field:codeTable  id="careTargetType" name="claimCareVisitVO.careObjectType" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"  cssClass="notuseflagDelete combox title selectChange" 
											whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />  
									</dd> 
								</dl>
								<dl>	
									<dt>联系地址</dt>
									<dd>
										<input type="text" id="contactAddress" name="claimCareVisitVO.contactAddress"  size="25"/>
									</dd> 
								</dl>
								<dl>	
									<dt>联系电话</dt>
									<dd>
										<input type="text" id="contactPhone" name="claimCareVisitVO.contactPhone"  size="25"/>
									</dd> 
								</dl>
								<dl>	
									<dt><font class="point" color="red">* </font>健康状态</dt>
									<dd>
									<Field:codeTable cssClass="combox title"  id="healthStatus" name="claimCareVisitVO.healthStatus" tableName="APP___CLM__DBUSER.T_EVALUATE_STATE" nullOption="true" onChange="changeVisitContent()"/> 
									</dd> 
								</dl>
								<dl>	
									<dt><font class="point" color="red">* </font>家庭状态</dt>
									<dd>
									<Field:codeTable cssClass="combox title"  id="familyStatus" name="claimCareVisitVO.familyStatus" tableName="APP___CLM__DBUSER.T_EVALUATE_STATE" nullOption="true" onChange="changeVisitContent()"/> 
									</dd> 
								</dl> <br>
								<dl style="width: 100%;height: auto;" >	
									<dt><font class="point" color="red">* </font>回访内容</dt>
									<dd>
										<textarea id="visitContent" rows="2" cols="123"  name="claimCareVisitVO.visitContent"></textarea>
									</dd> 
								</dl>
							</div>
						    <div class="panelPageFormContent" > 
						        <dl>	
									<dt><font class="point" color="red">* </font>回访人类型</dt>
									<dd>
										<Field:codeTable cssClass="combox title"  id="visitorType" name="claimCareVisitVO.serverType" tableName="APP___CLM__DBUSER.T_SERVER_TYPE" nullOption="true"/> 
									</dd> 
								</dl>
								<dl>	
									<dt><font class="point" color="red">* </font>回访人</dt>
									<dd>
										<input id="visitor" type="text" name="claimCareVisitVO.visiteBy" size="25" onblur="resetStyle(this)" />
									</dd> 
								</dl>
								<dl>	
									<dt>回访人电话</dt>
									<dd>
										<input type="text" name="claimCareVisitVO.visitorPhone" id="visitorPhone" size="25"/>
									</dd> 
								</dl>
								<dl>	
									<dt><font class="point" color="red">* </font>服务状态</dt>
									<dd>
									<Field:codeTable cssClass="combox title"  id="serviceStatus" name="claimCareVisitVO.serviceStatus" tableName="APP___CLM__DBUSER.T_SERVICE_STATE" nullOption="true"/> 
									</dd> 
								</dl> 
								<dl >	
									<dt>是否接收生日慰问短信</dt>
									<dd> 
										<input type="checkbox" name="claimCareVisitVO.isAcceptMeg" id="careIsAcceptMeg" checked="checked" value="1" onclick="isAcceptMegCheck()"/>是
				         			</dd>
				          		</dl>
				          		<dl>	
									<dt><font class="point" color="red">* </font>关怀对象隶属人群</dt>
									<dd>
									<Field:codeTable cssClass="combox title"  id="careTargetCrowd" name="claimCareVisitVO.careCrowd" tableName="APP___CLM__DBUSER.T_CARE_CROWD" nullOption="true"/> 
									</dd> 
								</dl>
				          	</div>
				          	<div class="panelPageFormContent" >
				          		<dl>
							    	<dt>下次回访日期</dt>
						    		<dd>
										<input type="expandDateYMD" name="claimCareVisitVO.nextVisitDate" size="22" class="date"
												id="nextVisitDate" value="<s:date name='claimCareVisitVO.nextVisitDate' format='yyyy-MM-dd' />" />
										<a class="inputDateButton" href="javascript:;">选择</a>
									</dd>
						    	</dl>
				          		<dl>
							    	<dt>提醒日期</dt>
						    		<dd>
										<input type="expandDateYMD" name="claimCareVisitVO.remindDate" size="22" class="date"
												id="remindDate" value="<s:date name='claimCareVisitVO.remindDate' format='yyyy-MM-dd' />" />
										<a class="inputDateButton" href="javascript:;">选择</a>
									</dd>
						    	</dl>
				          	</div>
				          	<div class="panelPageFormContent" >				          		
				           		<dl  style="width: 100%; height: auto;">	
									<dt>备忘录</dt>
									<dd>
										<textarea id="remark" rows="2" cols="123" name="claimCareVisitVO.remark"></textarea>
									</dd> 
								</dl>
							</div>
							<div class="panelPageFormContent" >
				          		<dl>
							    	<dt>填写日期</dt>  
						    		<dd>
										<input type="text" name="claimCareVisitVO.inputDate" id="todayDate"
							                   value="<s:date name='todayDate' format='yyyy-MM-dd'/>" readonly="readonly"/>
									</dd>
						    	</dl>
				          		<dl>	
									<dt>填写人</dt>
									<dd>
										<input type="text" name="userName" readonly="readonly" value="${userName}" />
										<input type="hidden" name="claimCareVisitVO.inputBy" value="${userId}" />
									</dd> 
								</dl>
						   </div>
						   
						   <div class="formBarButton">
					          <ul>
					            <li><button type="button" class="but_blue" onclick="saveVisitObject();">保存</button></li>
					            <li><button type="button" class="but_gray" onclick="exit()">退出</button></li>
		                     </ul>
	                      </div>
				</form>
			</div>			
		
			<!-- 隐藏的添加关怀任务页面 -->
			<div  layoutH="0" id="addCareMissionPage" style="display:none;">
					<div  id="addCareMissionPageForm">
						<div class="divfclass">
								<h1><img src="clm/images/tubiao.png">查询条件</h1>
						</div>
					    <div class="pageFormInfoContent">  
					    	<dl>	
								<dt>赔案号</dt>
								<dd>
									<input id="caseNo2" type="text" name="caseNo" maxlength="11" onkeyup="changeCaseNO(this)" value="${caseNo}"  size="25" />
								</dd> 
							</dl> 
							<dl>	
								<dt>保单号</dt>
								<dd>
									<input id="policyNo2" type="text" name="policyNo" value="${policyNo}"  size="25" />
								</dd> 
							</dl> 
							<dl>	
								<dt>出险人</dt>
								<dd>
									<input id="customerName2" type="text" name="customerName" value="${customerName}"  size="25" />
								</dd> 
							</dl>
			       			<dl>	
								<dt>证件号码</dt>
								<dd>
									<input id="customerId2" type="text" name="customerId" value="${customerId}"  size="25" />
								</dd>
							</dl>
							<div class="pageFormdiv">
			                   <button type="button"  class="but_blue"  onclick="addCareQuery();">查询</button>
			                </div>
						</div>
					</div>
				
				<!-- 显示数据列表区域 -->	
				<div class="pageFormContent" id="confirmCareMissionPageForm">
					<div class="divfclass">
						 <h1><img src="clm/images/tubiao.png">赔案信息列表</h1>
					</div>
					<div class="tabdivclassbr">
						<table class="list" width="100%">
							<thead>
								<tr>
			                   		<th nowrap>序号</th>
			                   		<th nowrap>赔案ID</th>
									<th nowrap>赔案号</th>
									<th nowrap>出险人姓名</th>
									<th nowrap>管理机构</th>
									<th nowrap>理赔类型</th>
									<th nowrap>出险结果</th>
									<th nowrap>出险日期</th>
									<th nowrap>结案日期</th>
								</tr>
							</thead>
							<!-- tbody的内容靠调用callbackacc(jsonlist)函数进行填充 -->
							<tbody id="addCareResult" style="text-align:center;" ></tbody>
			   		   </table>
			   		</div>
<!-- 					<div>
						<ul style="float:right;">
			        		<li>   
								<div class="button"><div class="buttonContent"></div></div>
						    </li>
			   			</ul> 
					</div> -->
					
				 <div class="formBarButton">
					<ul>
					   <li><button type="button" class="but_blue" onclick="addCareConfirm();">确认</button></li>
					   <li><button type="button" class="but_gray" onclick="exit()">退出</button></li>
		             </ul>
	               </div>
	               
			   	</div>
		   	
		 <!-- hide结束标记 -->  	
		</div>
	<!-- tab_box结束标记 -->	
	</div>
<!-- outer-exit结束标记 -->
</div>

