<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--引入jQuery -->
<script type="text/javascript" language="javascript"  src="clm/pages/clm_care/queryCareHistoryInfo.js"></script>
<script>
if('${msg}'!=''){
	alertMsg.warn("您要查询的理赔关怀历史任务不存在，请重新输入查询条件!");
}
</script>
<!-- 选项卡 -->
<div class="pageContent">
	<div class="tabs main_tabdiv">
		<div class="tabsHeader ">
			<div class="tabsHeaderContent ">
				<ul>
					<li><span class="j-ajax" id="preCare">关怀服务历史信息</span></li>
				</ul>
			</div>
		</div>
	</div>
</div>

<!-- 页面内容 -->
	<div layoutH="100" id="preCareServicePage" class="main_tabsContent">
		<!-- 查询条件模块 -->
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
		   </div>
			<form id="queryCareHistoryInfoId"
			action="clm/claimcare/queryCareHistoryInfo_CLM_queryCareHistoryInfoAction.action"
			method="post" onsubmit="return navTabSearch(this)"
			class="pagerForm required-validate">
			    <div class="pageFormInfoContent" >  
			    	<dl>	
						<dt><font class="point" color="red">* </font>赔案号</dt>
						<dd>
							<input id="caseNoQuery" type="text" value="${claimCareVisitVO.caseNo}" name="claimCareVisitVO.caseNo" maxlength="11" size="25" onkeyup="changeCaseNO(this)"/>
						</dd> 
					</dl> 
					<div class="pageFormdiv">
					   <button type="button"class="but_blue" onclick="careHistoryQuery();">查询</button>
					 </div>
			   </div>
		   </form>
		
		<!-- 显示关怀历史列表信息	 -->
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">关怀历史列表信息</h1>
		   </div>
			<div class="tabdivclassbr" >
				<table class="list" width="100%">
					<thead>
						<tr>
	                   		<th nowrap>序号</th>
							<th nowrap>赔案号</th>
							<th nowrap>出险人</th>
							<th nowrap>出险结果</th>
							<th nowrap>结案日期</th>
							<th nowrap>回访日期</th>
							<th nowrap>回访人</th>
							<th nowrap>服务状态</th>
							<th nowrap>回访次序</th>
						</tr>
					</thead>
					<tbody id="careHistoryQueryResult">
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="claimCareVisitVOList == null || claimCareVisitVOList.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="claimCareVisitVOList" var="claimCareVisi" status="st">
						<tr>
							<td><div align="center">
									${st.index+1}
									<input type="radio" name="caseId" onclick="queryCareHistoryInfo(this)">
									<input type="hidden" value="${visitId}">
								</div>
							</td>
							<td><div align="center">${caseNo}</div></td>
							<td><div align="center">${customerName}</div></td>
							<td><div align="center">${claimType}</div></td>
							<td><div align="center"><fmt:formatDate value="${endCaseTime}" pattern="yyyy-MM-dd" /></div></td>
							<td><div align="center"><fmt:formatDate value="${visitDate}" pattern="yyyy-MM-dd" /></div></td>
							<td><div align="center">${visiteBy}</div></td>
							<td><div align="center">
								<s:if test="serviceStatus == 1">
									<span>关闭</span>
								</s:if>
								<s:else>
									<span>继续访问</span>
								</s:else>
							</div></td>
							<td><div align="center">${careTimes}</div></td>
						</tr>
					</s:iterator>
					</tbody>
	   		   </table>
	   		</div>
	   	
		<!-- 显示回访结果详细信息 -->	
	   	<div  >
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">回访结果详细信息</h1>
		   </div>
			 
				<div class="panelPageFormContent" >  
			    	<dl >	
						<dt>赔案号</dt>
						<dd>
							<input id="careHistoryCaseNo" type="text" name="" value=""  size="25" readonly="readonly"/>
						</dd> 
					</dl> 
			    	<dl>	
						<dt>出险人姓名</dt>
						<dd>
							<input id="careHistoryCustomerName" type="text" name="" value=""  size="25" readonly="readonly"/>
						</dd> 
					</dl>
			    	<dl>
				    	<dt>回访结果</dt>
				    	<dd>
							<select class="combox title"  id="careHistoryVisitResult" disabled="disabled" >
								<option value=""></option>
								<option value="1">成功</option>
								<option value="0">不成功</option>
							</select>
					    </dd>
					</dl>  
				  	<dl>
				    	<dt>生存状态</dt>
				    	<dd>
							<select class="combox title"  id="careHistorySurvivalState" name="" value="" disabled="disabled" >
								<option value=""></option>
								<option value="1">生存</option>
								<option value="2">身故</option>
							</select>
					    </dd>
					</dl> 
			    	<dl>
				    	<dt>回访日期</dt>
			    		<dd>
							<input  id="careHistoryVisitDate" type="text" name=""  size="25" readonly="readonly" />
						</dd>
			    	</dl>
			    	<dl>	
						<dt>关怀对象</dt>
						<dd>
							<input id="careHistoryCareObject" name=""  value="" type="text"  size="25" readonly="readonly"/> 
						</dd> 
					</dl>
					<dl>	
						<dt>关怀对象类型</dt>
						<dd>
							<select class="combox title"  id="careHistoryCareTargetType" name="" value="" disabled="disabled">
								<option value=""></option>
								<option value="00">本人</option>
								<option value="01">父子</option>
								<option value="02">父女</option>
								<option value="03">母子</option>
								<option value="04">母女</option>
								<option value="05">祖孙</option>
								<option value="07">夫妻</option>
								<option value="08">兄弟</option>
								<option value="09">兄妹</option>
								<option value="10">姐弟</option>
								<option value="11">姐妹</option>
								<option value="12">叔侄</option>
								<option value="13">姑侄</option>
								<option value="14">外甥</option>
								<option value="15">媳</option>
								<option value="16">婿</option>
								<option value="17">姐夫</option>
								<option value="18">朋友</option>
								<option value="19">同事</option>
								<option value="20">师生</option>
								<option value="21">劳动关系</option>
								<option value="22">法定</option>
								<option value="23">子女</option>
								<option value="24">父母</option>
								<option value="99">其他</option>
							</select>
						</dd> 
					</dl>
					<dl>	
						<dt>联系地址</dt>
						<dd>
							<input id="careHistoryAddress" type="text" name="" value=""  size="25" readonly="readonly"/>
						</dd> 
					</dl>
					<dl>	
						<dt>联系电话</dt>
						<dd>
							<input id="careHistoryTelephone" type="text" size="25" readonly="readonly"/>
						</dd> 
					</dl>
					<dl>	
						<dt>健康状态</dt>
						<dd>
							<select class="combox title"  id="careHistoryHealthStatus" name="" value="" disabled="disabled"  >
								<option value=""></option>
								<option value="1">良好</option>
								<option value="2">一般</option>
								<option value="3">差</option>
							</select>
						</dd> 
					</dl>
					<dl>	
						<dt>家庭状态</dt>
						<dd>
							<select class="combox title"  id="careHistoryFamilyStatus" name="" value=""   disabled="disabled">
								<option value=""></option>
								<option value="1">良好</option>
								<option value="2">一般</option>
								<option value="3">差</option>
							</select>
						</dd> 
					</dl> <br>
					<dl style="width: 100%;height: auto">	
						<dt>回访内容</dt>
						<dd>
							<textarea id="careHistoryVisitContent" rows="2" cols="128"  name="" size="25" readonly="readonly" >
							</textarea>
						</dd> 
					</dl>
				</div>
				<div class="panelPageFormContent" > 
					<dl>	
						<dt>回访人</dt>
						<dd>
							<input id="careHistoryVisitor" type="text" name="" value="" 
							size="25" readonly="readonly"/>
						</dd> 
					</dl>
					<dl>	
						<dt>回访人电话</dt>
						<dd>
							<input id="careHistoryVisitorTelephone" type="text" name="" 
							value="" size="25" readonly="readonly"/>
						</dd> 
					</dl>
					<dl>	
						<dt>服务状态</dt>
						<dd>
							<select class="combox title"  id="careHistoryServiceStatus" name="" value=""  disabled="disabled" >
								<option value=""></option>
								<option value="1">关闭</option>
								<option value="2">继续访问</option>
							</select>
						</dd> 
					</dl> 
					<dl >	
						<dt>是否接收生日慰问短信</dt>
						<dd> 
							<input id="careHistorybirthGreetingMsg" type="checkbox" disabled="disabled" />是
	         			</dd>
	          		</dl>
	          		<dl>	
						<dt>关怀对象隶属人群</dt>
						<dd>
							<select class="combox title"  id="careHistoryCareTargetCrowd" name="" value=""  disabled="disabled">
								<option value=""></option>
								<option value="1">未成年人身故受益人</option>
								<option value="2">失去子女的老年人</option>
								<option value="3">重大疾病</option>
								<option value="4">其他</option>
							</select>
						</dd> 
					</dl>
	          	</div>
	          	<div class="panelPageFormContent" >
	          		<dl>
				    	<dt>下次回访日期</dt>
			    		<dd>
							<input id="careHistoryNextVisitDate" type="text" name=""   size="25" readonly="readonly" />
						</dd>
			    	</dl>
	          		<dl>
				    	<dt>提醒日期</dt>
			    		<dd>
							<input id="careHistoryRemindDate" type="text" name=""  size="25" readonly="readonly"/>
						</dd>
			    	</dl>
	           		<dl style="width: 100%;height: auto;">	
						<dt>备忘录</dt>
						<dd>
							<textarea  id="careHistoryMemo" rows="2" cols="128"  name="" size="25" readonly="readonly">
							</textarea>
						</dd> 
					</dl>
				</div>
				<div class="panelPageFormContent" >
	          		<dl>
				    	<dt>填写日期</dt>  
			    		<dd>
							<input id="careHistoryWriteDate" type="text" name="claimCareVisitVO.WOrkDate" value="" size="25" readonly="readonly"/>
						</dd>
			    	</dl>
	          		<dl>	
						<dt>填写人</dt>
						<dd>
							<input id="careHistoryWriter" type="text" name="claimCareVisitVO.UserName" value="" size="25" readonly="readonly"/>
						</dd> 
					</dl>
				</div>
			<div class="formBarButton">
				 <button type="button" class="but_gray close">退出</button> 
			</div>
        </div>
	</div>