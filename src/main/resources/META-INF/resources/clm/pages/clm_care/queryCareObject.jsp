<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
	<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<script type="text/javascript">

//根据赔案号进行查询
function careObjectQuery(){
	if($("#caseNoQuery", navTab.getCurrentPanel()).val()==null||$("#caseNoQuery", navTab.getCurrentPanel()).val()==""){
 		alertMsg.warn("赔案号不能为空");
	}else{
		$.ajax({ 
			type: "post",
			url : "clm/clmcare/queryCareObject_CLM_preserveCareServiceMsgAction.action",
			data:{"caseNo":$("#caseNoQuery", navTab.getCurrentPanel()).val()},
			dataType:'json',
			success: function(data)
			{
				if(data==null){
					$("#queryCareObjectResult", navTab.getCurrentPanel()).empty();
					alertMsg.error("您要查询的关怀服务历史信息不存在，请重新输入赔案号");
					
				}else{
					var json = eval("("+data+")");
					callbackacc(json);
				}
			},
			error: function()
			{
			
			}
		});
	}
}

function callbackacc(jsonlist){
	$("#queryCareObjectResult", navTab.getCurrentPanel()).empty();
	for(var i=0;i<jsonlist.length;i++){
		var interHtml = "<tr id='careObjectListInfo' style='text-align:center'>"
			+ "<td style='width:50px'>"+(i+1)+"&nbsp&nbsp"
			+ "<input type='radio' name='careObjectListRadio' onclick='selectedRelationCoustomer();'/>"
			+ "</td>"
			+ "<td style='width:100px'>"
			+ jsonlist[i].clientType
			+ "</td>"
			+ "<td style='width:100px'>"
			+ jsonlist[i].clientName 
			+ "</td>"
			+ "<td style='width:100px'>"
			+ jsonlist[i].relation
			+ "</td>"
			+ "<td style='width:100px'>"
			+ jsonlist[i].address
			+ "</td>"
			+ "<td style='width:100px'>"
			+jsonlist[i].telephone
			+ "</td>" + "</tr>";
		$("#queryCareObjectResult", navTab.getCurrentPanel()).append(interHtml);
	}
}

//获取到选中行的信息
function selectedRelationCoustomer(){
	//获取被选中的数据行
	var selectedObj = $("input[name='careObjectListRadio']", navTab.getCurrentPanel());
	for(var i=0;i<selectedObj.length;i++){
	    if(selectedObj[i].checked){
	    	//将被选中的行的信息存入隐藏的div模块的相应元素中，便于点击确认按钮进行查找回带时获取数据
	    	var siblingObject=$(selectedObj[i]).parent().nextAll();
	    	$("#hiddenClientType", navTab.getCurrentPanel()).val($(siblingObject[0]).html());
	    	$("#hiddenClientName", navTab.getCurrentPanel()).val($(siblingObject[1]).html());
	    	$("#hiddenRelation", navTab.getCurrentPanel()).val($(siblingObject[2]).html());
	    	$("#hiddenAddress", navTab.getCurrentPanel()).val($(siblingObject[3]).html());
	    	$("#hiddenTelephone", navTab.getCurrentPanel()).val($(siblingObject[4]).html());
		}
    }
}

// 将选中行信息带回
function bringBackFunction(){
	$.bringBack({"clientType":$("#hiddenClientType", navTab.getCurrentPanel()).val(),
				"clientName":$("#hiddenClientName", navTab.getCurrentPanel()).val(),
				"relation":$("#hiddenRelation", navTab.getCurrentPanel()).val(),
				"address":$("#hiddenAddress", navTab.getCurrentPanel()).val(),
				"telephone":$("#hiddenTelephone", navTab.getCurrentPanel()).val()});
}

</script>

<!-- 页面 -->
<div class="pageContent" layoutH="0" id="queryCareObjectJsp">
	<div class="pageContent">
		<div class="tabs" >
			<div class="tabsHeader">
				<div class="tabsHeaderContent">
					<ul>
						<li><a href=""
							class="j-ajax"><span>查询关怀对象</span></a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	
		   <div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
		   </div>
	    <div class="pageFormContent" >  
	    	<dl>	
				<dt>赔案号：</dt>
				<dd>
					<input id="caseNoQuery" type="text" name="caseNo" value="${caseNo}"  size="25" />
				</dd> 
			</dl> 
			<ul >
        		<li class="nowrap">   
					<div class="button"><div class="buttonContent"><button type="button" onclick="careObjectQuery();">查询</button></div></div>
			    </li>
   			</ul> 
		</div>
	
	<!-- 显示数据列表区域 -->	
	<div class="panel" >
		<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">赔案号关联的客户</h1>
		   </div>
		<div class="tabdivclassbr">
			<table class="table" width="100%">
				<thead>
					<tr>
                   		<th nowrap>序号</th>
						<th nowrap>客户类型</th>
						<th nowrap>姓名</th>
						<th nowrap>与出险人关系</th>
						<th nowrap>联系地址</th>
						<th nowrap>联系电话</th>
						
					</tr>
				</thead >
				<tbody id="queryCareObjectResult" style="text-align:center;" >
					<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
				</tbody>
   		   	</table>
		</div>
   	</div>
   	
   	<!-- 隐藏区域，存放被选中行的相关数据信息，方便查询回带时相关信息的获取 -->	
	<div class="panel" style="display:none">
	           被选中行的客户ID：<input id="hiddenID" type="text" value=""/><br>
		被选中行的客户类型：<input id="hiddenClientType" type="text" value=""/><br>
		姓名：<input id="hiddenClientName" type="text" value=""/><br>
		与出险人关系：<input id="hiddenRelation" type="text" value=""/><br>
		联系地址：<input id="hiddenAddress" type="text" value=""/><br>
		联系电话：<input id="hiddenTelephone" type="text" value=""/>
   	</div>

   	<!-- 确认、退出 -->
   	<div class="pageFormContent" >
	   	<ul style="margin-left:43%;margin-ritht:10%;">
	      	<li class="nowrap" >   
				<div class="button"><div class="buttonContent">		
					<button type="button" onclick="bringBackFunction();">确认</button>
				</div></div>
		    </li>
		</ul> 
		<ul style="margin-left:53%;">
	      	<li class="nowrap"  >   
				<div class="button"><div class="buttonContent"><button type="button" class="close">退出</button></div></div>
		    </li>
		</ul> 
   	</div>
	 
</div>