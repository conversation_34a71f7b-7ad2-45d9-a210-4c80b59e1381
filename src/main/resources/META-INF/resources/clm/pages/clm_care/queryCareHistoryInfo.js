//查询
function careHistoryQuery(){
	var caseno = $("#caseNoQuery", navTab.getCurrentPanel()).val();
	if(caseno.length != 0 && caseno.length != 11){
		alertMsg.warn("录入的赔案号不正确，请重新输入！");
		return false;
	}
	if($("#caseNoQuery", navTab.getCurrentPanel()).val()==null||$("#caseNoQuery", navTab.getCurrentPanel()).val()==""){
		   alertMsg.error("请输入赔案号");
	} else {
		$("#queryCareHistoryInfoId", navTab.getCurrentPanel()).submit();
	}
}
function changeCaseNO(obj){
	obj.value=obj.value.replace(/\D/g,'');
	if(obj.value[0]!=9){
		obj.value='';
	}
}
//radio查询
function queryCareHistoryInfo(k){
	var visitId = $(k).parent().find("input:eq(1)").val(); 
	$.ajax({
		url : "clm/claimcare/queryCareHistoryDetailInfo_CLM_queryCareHistoryInfoAction.action?claimCareVisitVO.visitId="+visitId,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(beneVO) {
			if(beneVO.isAcceptMeg == "1"){
				$("#careHistorybirthGreetingMsg", navTab.getCurrentPanel()).attr("checked",true);
			}else{
				$("#careHistorybirthGreetingMsg", navTab.getCurrentPanel()).attr("checked",false);
			}
			$("#careHistoryCaseNo", navTab.getCurrentPanel()).val(beneVO.caseNo);
			$("#careHistoryCustomerName", navTab.getCurrentPanel()).val(beneVO.customerName);
			$("#careHistoryVisitResult", navTab.getCurrentPanel()).selectMyComBox(beneVO.isSucceed);
			$("#careHistorySurvivalState", navTab.getCurrentPanel()).selectMyComBox(beneVO.liveStatus);
			$("#careHistoryVisitDate", navTab.getCurrentPanel()).val(beneVO.visitDateStr);
			$("#careHistoryCareObject", navTab.getCurrentPanel()).val(beneVO.customerName1);
			$("#careHistoryCareTargetType", navTab.getCurrentPanel()).selectMyComBox(beneVO.careObjectType);
			$("#careHistoryAddress", navTab.getCurrentPanel()).val(beneVO.contactAddress);
			$("#careHistoryTelephone", navTab.getCurrentPanel()).val(beneVO.contactPhone);
			$("#careHistoryHealthStatus", navTab.getCurrentPanel()).selectMyComBox(beneVO.healthStatus);
			$("#careHistoryFamilyStatus", navTab.getCurrentPanel()).selectMyComBox(beneVO.familyStatus);
			$("#careHistoryVisitContent", navTab.getCurrentPanel()).val(beneVO.visitContent);
			$("#careHistoryVisitor", navTab.getCurrentPanel()).val(beneVO.visiteBy);
			$("#careHistoryVisitorTelephone", navTab.getCurrentPanel()).val(beneVO.visitorPhone);
			$("#careHistoryServiceStatus", navTab.getCurrentPanel()).selectMyComBox(beneVO.serviceStatus);
			$("#careHistoryCareTargetCrowd", navTab.getCurrentPanel()).selectMyComBox(beneVO.careCrowd);
			$("#careHistoryNextVisitDate", navTab.getCurrentPanel()).val(beneVO.nextVisitDateStr);
			$("#careHistoryRemindDate", navTab.getCurrentPanel()).val(beneVO.remindDateStr);
			$("#careHistoryMemo", navTab.getCurrentPanel()).val(beneVO.remark);
			$("#careHistoryWriteDate", navTab.getCurrentPanel()).val(beneVO.inputDateStr);
			$("#careHistoryWriter", navTab.getCurrentPanel()).val(beneVO.inputByName);
		}
	});
}