<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ page import="java.util.*"%>

<script type="text/javascript">
function changeCaseNO(obj){
	obj.value=obj.value.replace(/\D/g,'');
	if(obj.value[0]!=9){
		obj.value='';
	}
}
//根据赔案号进行查询
function careObjectQuery(){
	var caseno = $("#caseNoQuery", $.pdialog.getCurrent()).val();
	if(caseno.length != 0 && caseno.length != 11){
		alertMsg.warn("录入的赔案号不正确，请重新输入！");
		return false;
	}
	if($("#caseNoQuery", $.pdialog.getCurrent()).val()==null||$("#caseNoQuery", $.pdialog.getCurrent).val()==""){
 		alertMsg.warn("赔案号不能为空");
 		return false;
	} else {
		$('#confirmFormsCare', $.pdialog.getCurrent()).submit();
	}
	
}
//获取到选中行的信息
function selectedRelationCoustomer(thisObj){
	//获取被选中的数据行
	var clientName = $(thisObj).parents('tr').children('td').eq(2).text();
	var clientId = $(thisObj).parents("tr").find("#clientId").val();
	var clientAddress = $(thisObj).parents('tr').children('td').eq(4).text();
	var clientPhone = $(thisObj).parents('tr').children('td').eq(5).text();
	var clientRelationId = $(thisObj).parents("tr").find("#clientRelationId").val();
	$("#clientNameNone",$.pdialog.getCurrent()).val(trim(clientName));
	$("#clientIdNone",$.pdialog.getCurrent()).val(clientId);
	$("#clientAddressNone",$.pdialog.getCurrent()).val(clientAddress);
	$("#clientPhoneNone",$.pdialog.getCurrent()).val(clientPhone);
	$("#clientRelationIdNone",$.pdialog.getCurrent()).val(clientRelationId);
}
//将选中行信息带回
function bringBackFunction(){
	$.bringBack({"careObjectId":$("#clientIdNone",$.pdialog.getCurrent()).val(),
				"careObjectName":$("#clientNameNone",$.pdialog.getCurrent()).val(),
				"careObjectType":$("#clientRelationIdNone",$.pdialog.getCurrent()).val(),
				"contactAddress":$("#clientAddressNone",$.pdialog.getCurrent()).val(),
				"contactPhone":$("#clientPhoneNone",$.pdialog.getCurrent()).val()});
}
/* function exit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			$.pdialog.closeCurrent();
	 	}
	 });
} */
if('${msgObject}'!=''){
	alertMsg.warn("您要查询的理赔关怀对象不存在，请重新输入查询条件!");
}
</script>
<div class="pageContent" layoutH="20" id="queryCareObjectJsp">
	<div class="pageContent">
		<div class="tabs" >
			<div class="tabsHeader">
				<div class="tabsHeaderContent">
					<ul>
						<li><a href=""
							class="j-ajax"><span>查询关怀对象</span></a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	<form id="confirmFormsCare"  method="post" onsubmit="return dialogSearch(this);"
			class="pagerForm required-validate"
	    action="clm/clmcare/queryCareObjectList_CLM_preserveCareServiceMsgAction.action">
		<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
		   </div>
	    <div class="pageFormInfoContent" >  
	    	<dl>	
				<dt>赔案号</dt>
				<dd>
				    <input id="caseIdQuery" type="hidden" name="caseId" value="${caseId}"  size="25" />
					<input id="caseNoQuery" type="text" name="caseNo" maxlength="11" onkeyup="changeCaseNO(this)" value="${caseNo}"  size="25" />
					<input id="customerNameIdQuery" type="hidden" name="" value="${customerNameId}"  size="25" />
				</dd> 
			</dl> 
			<dl>	
				<dt><button type="button"class="but_blue" onclick="careObjectQuery()">查询</button></dt>				
			</dl>
		</div>
	
	<!-- 显示数据列表区域 -->	
		 <div class="divfclass">
				<h1><img src="clm/images/tubiao.png">赔案号关联的客户</h1>
		   </div>
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
					    <th nowrap>序号</th>
						<th nowrap>客户类型</th>
						<th nowrap>姓名</th>
						<th nowrap>与出险人关系</th>
						<th nowrap>联系地址</th>
						<th nowrap>联系电话</th>
					</tr>
				</thead >
				<tbody>
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="insuredAndBeneAndCustomVOList == null || insuredAndBeneAndCustomVOList.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
				   <s:iterator value="insuredAndBeneAndCustomVOList" status="st">
						<tr>
						    <td>${st.index+1}<input type='radio' name='careObjectListRadio' onclick='selectedRelationCoustomer(this);'/></td>
							<td style="align:center"><s:property value="clientType" /></td>
							<td style="align:center">
							    <s:property value="clientName" />
							    <input type="hidden" id="clientId" value="${clientId}">
							</td>
							<td style="align:center">
							    <s:property value="clientRelationName" />
							    <input type="hidden" id="clientRelationId" value="${clientRelationId}">
							</td>
							<td style="align:center"><s:property value="clientAddress" /></td>
							<td style="align:center"><s:property value="clientPhone" /></td>
						</tr>
				  </s:iterator>
				</tbody>
   		   	</table>
		</div>
   	<!-- 隐藏区域，存放被选中行的相关数据信息，方便查询回带时相关信息的获取 -->	
	<div class="panel" style="display:none">
		姓名：<input id="clientNameNone" type="text" value=""/><br>
		ID：<input id="clientIdNone" type="text" value=""/><br>
		与出险人关系：<input id="clientRelationIdNone" type="text" value=""/><br>
		联系地址：<input id="clientAddressNone" type="text" value=""/><br>
		联系电话：<input id="clientPhoneNone" type="text" value=""/>
   	</div>
   	<!-- 确认、退出 -->
   	<div class="formBarButton" >
	   	<ul>
	      	<li>   
				<div class="button"><div class="buttonContent">		
					<button type="button" onclick="bringBackFunction();">确认</button>
				</div></div>
		    </li>		
	      	<li>   
				<div class="button"><div class="buttonContent"><button type="button" onclick="exit();">退出</button></div></div>
		    </li>
		</ul> 
   	</div>
   	</form>
</div>	


