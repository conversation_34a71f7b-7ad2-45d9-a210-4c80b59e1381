<%@ page language="java" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--   引入jQuery -->
<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="clm/pages/claimcare/claimcarePage.js"></script> 

<script type="text/javascript">
//重新分配中支机构
function reAssignOrgan(){
	var careIds = "";
	$("input#check","table#revistDataAssign").each(function(){
		if ($(this).is(':checked')) {
			careIds=careIds+$(this).attr("careId")+",";
		}
	});
	if(careIds == ""){
		alertMsg.warn("请至少选中列表中的一条记录！");
		return;
	}
	var orgCode = $("#udmporg", navTab.getCurrentPanel()).val();
	if(orgCode == null || orgCode == ""){
		alertMsg.warn("重新指定操作机构必选！");
		return;
	}
	var serverId = $("#serverId", navTab.getCurrentPanel()).val();
	$.ajax({
		'type':'post',
		'url':'clm/claimcare/reAssignOrgan_CLM_claimcareAssignAction.action?caseNos='+careIds+'&claimCareServerVO.serverId='+serverId+'&orgCode='+orgCode,
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if(data.flag == true){
				alertMsg.correct("重新指定操作机构成功");
				$("#initPage", navTab.getCurrentPanel()).submit();
				claimCareQuery();
			}else{
				alertMsg.error("重新指定操作机构失败");
			}
		}
	}); 
} 
//查询服务人员信息
function queryServerCode(obj){
	var serverName = $(obj).val();
	if(serverName==''||serverName==null){
		return false;
	}
	$.ajax({
		'type':'post',
		'url':'clm/claimcare/queryServerByCondition_CLM_queryServerAction.action?claimCareServerVO.name='+serverName,
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if (data.flag != false) {
				$("#serverId", navTab.getCurrentPanel()).val(data.serverId);
				$("#serverCodeId", navTab.getCurrentPanel()).val(data.serverCode);
				$("#serverNameId", navTab.getCurrentPanel()).val(data.name);
			} else {
				alertMsg.error('您要查询的服务人员信息不存在，请重新输入查询条件！');
			}
		}
	}); 
}

function queryServerName(obj){
	var serverCode = $(obj).val();
	$.ajax({
		'type':'post',
		'url':'clm/claimcare/queryServerByCondition_CLM_queryServerAction.action?claimCareServerVO.serverCode='+serverCode,
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if (data.flag != false) {
				$("#serverId", navTab.getCurrentPanel()).val(data.serverId);
				$("#serverCodeId", navTab.getCurrentPanel()).val(data.serverCode);
				$("#serverNameId", navTab.getCurrentPanel()).val(data.name);
			} else {
				alertMsg.error('您要查询的服务人员信息不存在，请重新输入查询条件！');
			}
		}
	}); 
}
//点击分配按钮
function claimAssign(){
	var caseNos = ""; 
	$("input#check","table#revistDataAssign").each(function(){
		if ($(this).is(':checked')) {
			caseNos = caseNos+$(this).val()+"-"+$(this).attr("organCode")+"-"+$(this).attr("careId")+",";
		}
	});
	if(caseNos == ""){
		alertMsg.warn("请至少选中列表中的一条记录！");
		return;
	}
	var serverNameId = $("#serverNameId", navTab.getCurrentPanel()).val();
	if(serverNameId == "" || serverNameId == null){
		alertMsg.warn("用户名/业务员姓名必填！");
		return;
	}else{
		if(!checkName($("#serverNameId", navTab.getCurrentPanel()))){
			return;
		}
	}
	var serverId = $("#serverId", navTab.getCurrentPanel()).val();
	$.ajax({
			'type':'post',
			'url':'clm/claimcare/assignToServer_CLM_claimcareAssignAction.action?caseNos='+caseNos+'&claimCareServerVO.serverId='+serverId,
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.flag == true){
					alertMsg.correct("分配服务人员成功");
				}else{
					alertMsg.error("分配服务人员失败");
				}
			}
	}); 
}

//弹出是否
/* function exit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
} */
$(function() {
	var flag = '${flag}';
	if (flag == 'true') {
		alertMsg.error('您要查询的关怀任务不存在，请重新输入查询条件！');
	}
});	
$("#tomessage", navTab.getCurrentPanel()).change(function(){
	  var check=$(this).attr("checked");
	  if(check=="checked"){
		    $("input#check","table#revistDataAssign", navTab.getCurrentPanel()).each(function(){
				if (!$(this).is(':checked')) {
					$(this).attr("checked",true);
				}
			});
	  }else{
		  $("input#check","table#revistDataAssign", navTab.getCurrentPanel()).each(function(){
				if ($(this).is(':checked')) {
					$(this).attr("checked",false);
				}
			});
	  }
	});
$("input#check", navTab.getCurrentPanel()).change(function(){
		var num=0,numone=0;
		$("input#check","table#revistDataAssign", navTab.getCurrentPanel()).each(function(){
			num++;
			if ($(this).is(':checked')) {
				numone++;
			}
		});
		if(numone==num){
			$("#tomessage", navTab.getCurrentPanel()).attr("checked",true);
		}else{
			$("#tomessage", navTab.getCurrentPanel()).attr("checked",false);
		}
});
</script>
<form id="initPage" method="post"
	action="clm/claimcare/toClaimcareAssignPage_CLM_claimcareAssignAction.action" class="pageForm required-validate"  onsubmit ="return navTabSearch(this);" novalidate="novalidate">
</form>
<div id="careAssignDataId">
	<div layoutH="36" id="mainContent">
		<form id="pagerForm" method="post" action="clm/claimcare/showClaimAssinInfo_CLM_claimcareAssignAction.action">
			<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		    <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
		    <input type="hidden" name="claimCareVisitVO.organCode" value="${claimCareVisitVO.organCode}"/>
		    <input type="hidden" name="claimCareVisitVO.caseNo" value="${claimCareVisitVO.caseNo}"/>
		    <input type="hidden" name="claimCareVisitVO.policyCode" value="${claimCareVisitVO.policyCode}"/>
		    <input type="hidden" name="claimCareVisitVO.customerName" value="${claimCareVisitVO.customerName}"/>
		    <input type="hidden" name="claimCareVisitVO.customerCertiCode" value="${claimCareVisitVO.customerCertiCode}"/>
		    <input type="hidden" name="claimCareVisitVO.revisitStatus" value="${claimCareVisitVO.revisitStatus}"/>
		</form>
		<!-- 查询事件访问路径 -->
		<form id="queryHistoryId" method="post" action="" class="pageForm required-validate"  onsubmit ="return navTabSearch(this);" novalidate="novalidate" rel="pagerForm">
				<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询条件
					</h1>
				</div>
					<div class="pageFormInfoContent">
							<dl>
								<dt>机构</dt>
								<dd>
									<input id="comId" style="width: 30px;border-right:0px" name="claimCareVisitVO.organCode" size="8" value="${claimCareVisitVO.organCode}" type="text" class="organ" clickId="menuBtn" showOrgName="careOrgName" needAll="true"/>
									<input id="careOrgName" style="width:110px;" type="text" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${claimCareVisitVO.organCode}" />" readOnly/>
									<a id="menuBtn" class="btnLook" href="#" style="position: relative;"></a> 
								</dd></dl>
								<dl>
								<dt>赔案号</dt>
								<dd>
									<input type="text" name="claimCareVisitVO.caseNo"
										id="caseNoId" maxlength="11" size="25" value="${claimCareVisitVO.caseNo}" onkeyup="changeCaseNO(this)"/>
								</dd>
								</dl>
								<dl>
								<dt>保单号</dt>
								<dd>
									<input type="text" name="claimCareVisitVO.policyCode"
										id="polNoId" size="25" value="${claimCareVisitVO.policyCode}" onkeyup="this.value=this.value.replace(/[^\w\.\/]/ig,'')">
								</dd>
								</dl>
								<dl>
								<dt>出险人</dt>
								<dd>
									<input type="text" name="claimCareVisitVO.customerName" onblur="resetStyle(this)"
										id="accNameId" size="25" value="${claimCareVisitVO.customerName}" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
								</dd>
								</dl>
								<dl>
								<dt>出险人证件号码</dt>
								<dd>
									<input type="text" name="claimCareVisitVO.customerCertiCode"
										id="certiCodeId" size="25" value="${claimCareVisitVO.customerCertiCode}"/>
								</dd>
								</dl>
								<dl>
								<dt>回访状态</dt>								
								<dd>
									 <select class="combox title"  name="claimCareVisitVO.revisitStatus" id="revisitStatusId" >
										<option value=""  ${claimCareVisitVO.revisitStatus == ''?'selected':''}>全部</option>
										<option value="1" ${claimCareVisitVO.revisitStatus == '1'?'selected':''} >待回访</option>
										<option value="2" ${claimCareVisitVO.revisitStatus == '2'?'selected':''} >已回访</option>
									</select> 
									<%--<Field:codeTable  name="claimCareVisitVO.revisitStatus" id="revisitStatusId" tableName="APP___CLM__DBUSER.T_RETURN_VISIT_STATE" nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" value="${claimCareVisitVO.revisitStatus}"/>--%> 
								</dd>
								</dl>
						<div class="pageFormdiv">
							<button type="button" class="but_blue" onclick="claimCareQuery();" >查询</button>
						</div>
					</div>
				</div>
				<!-- 显示数据列表区域 -->
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">回访数据列表
						</h1>
					</div>
					<div class="tabdivclassbr">
						<table class="list" width="100%" id="revistDataAssign">
							<thead>
								<tr>
									<th nowrap><input type="checkbox" style="border:0px;background:0px;width:auto;padding: 0;margin: 0" size="5px" id="tomessage"  value="0"/>全选</th>
									<th nowrap>赔案号</th>
									<th nowrap>出险人姓名</th>
									<th nowrap>管理机构</th>
									<th nowrap>理赔类型/出险日期</th>
									<th nowrap>出险结果</th>
									<th nowrap>结案日期</th>
									<th nowrap>下次回访日期</th>
									<th nowrap>关怀次数</th>
									<th nowrap>操作人</th>
									<th nowrap>操作日期</th>
								</tr>
							</thead>
							<tbody>
								<s:if test="imageFlag != null">
									<tr>
										<td colspan="11">
											<div class="noRueryResult">请选择条件查询数据！</div>
										</td>
									</tr>
								</s:if>
								<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
									<tr>
										<td colspan="11">
											<div class="noRueryResult">没有符合条件的查询结果！</div>
										</td>
									</tr>
								</s:elseif>
								<!-- 循环显示数据 -->
								<s:iterator value="currentPage.pageItems" status="st" var="cp">
									<tr> 
										<td>${cp.revisitStatus}<input type="checkbox" style="border:0px;background:0px;width:auto; float: right;" type="checkbox"  name="" value="${cp.caseNo}" id="check" careId='${cp.careId}' organCode='${cp.organCode }'/></td>
										<td >${cp.caseNo}</td>
										<td>${cp.customerName}</td>
										<td>
										    <input type="hidden" value="${cp.organCode}"/>
											<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${cp.organCode}"/>
										</td>
										<td><input type="text" value="${cp.claimType}" readonly="readonly" style="width:100%;height:100%;border:0px;"/></td>
										<td><input type="text" value="${cp.accResult1}" readonly="readonly" style="width:100%;height:100%;border:0px;"/></td>
										<td><s:date name='#cp.endCaseTime' format='yyyy-MM-dd'/></td>
										<td><s:date name='#cp.nextVisitDate' format='yyyy-MM-dd'/></td>
										<td>${cp.careTimes}</td>
										<td>${cp.userName}</td>
										<td>${cp.workDate}</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
						<!-- 分页查询区域 -->
						<div class="panelBar">
							<div class="pages">
								<span>显示</span>
								<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
									name="select" onchange="navTabPageBreak({numPerPage:this.value},'careAssignDataId')"
									value="currentPage.pageSize">
								</s:select>
								<span>条，共${currentPage.total}条</span>
							</div>
							<div class="pagination" targetType="navTab" rel="careAssignDataId"
								totalCount="${currentPage.total}"
								numPerPage="${currentPage.pageSize}" pageNumShown="10"
								currentPage="${currentPage.pageNo}">
							</div>
						</div>
					</div>
		</form>
			<!-- 显示数据列表区域 -->
			<%-- <div id="careAssignDataId">
				<%@ include file="/clm/pages/claimcare/careAssignData.jsp" %>
			</div> --%>
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">指定服务人员
					</h1>
				</div>
				<div class="panelPageFormContent" >
				    <input type="hidden" name="claimCareServerVO.serverId" size="25" lookupGroup="claimCareServerVO" id="serverId"/>
					<dl>
						<dt>用户名/业务员代码</dt>
						<dd>
						    <input type="text" class="serverMsg" name="claimCareServerVO.serverCode" size="25" lookupGroup="claimCareServerVO" id="serverCodeId" onblur="queryServerName(this);"/>
						</dd>
					</dl>
					<dl>
						<dt><font class="point" color="red">* </font>姓名</dt>
						<dd>
							<input id="serverNameId" class="serverMsg" name="claimCareServerVO.name" onblur="resetStyle(this)"
								 type="text" suggestFields="serverCode,name"
								suggestUrl="clm/claimcare/showServerInfo_CLM_queryServerAction.action"
								lookupGroup="claimCareServerVO" size="25" onblur="queryServerCode(this);"/> <a class="btnLook"
								href="clm/claimcare/showServerInfo_CLM_queryServerAction.action"
								lookupGroup="claimCareServerVO">查询服务人员</a>
						</dd>
					</dl>
					</div>
					<div>
					<div class="pageFormdiv">
						<button type="button" class="but_blue" onclick="claimAssign();">分配</button>
					</div>
				</div>
			</div>
			
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">重新指定操作机构
					</h1>
				</div>
				<div class="panelPageFormContent" >
					<dl>
						<dt><font class="point" color="red">* </font>机构</dt>
						<dd>
							<input id="udmporg" name="claimCaseVO.organCode" value="" type="hidden" />
							<input id="organName" name="claimCaseVO.organName" 
								value="" type="text"
								postField="keyword" 
								suggestUrl="clm/pages/invoice/findOrg.jsp"
								readonly /> 
							<a class="btnLook" href="clm/invoice/queryPageInit_CLM_claimInvoicesPrintAction.action?leftFlag=0&menuId=${menuId}" lookupGroup="claimCaseVO">查找带回</a>
						</dd>
					</dl>
<!-- 					<div class="pageFormdiv"> -->
<!-- 						<button type="button" class="but_blue" onclick="reAssignOrgan();">确认</button> -->
<!-- 					</div> -->
				</div>
			</div>
	
		<div class="formBarButton">
			<ul>
				<li><button type="button" class="but_blue" onclick="reAssignOrgan();">确认</button></li>
				<li><button type="button" class="but_gray" onclick="exit()">退出</button></li>
			</ul>
		</div>
	</div>
</div>
