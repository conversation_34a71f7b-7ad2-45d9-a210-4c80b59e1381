//维护关怀服务查询
	function claimCareQuery(){
		var caseno = $("#caseNoId", navTab.getCurrentPanel()).val();
		if(caseno.length != 0 && caseno.length != 11){
			alertMsg.error("录入的赔案号不正确，请重新输入！");
			return false;
		}
		if($("#accNameId", navTab.getCurrentPanel()).val() != ""){
			if(!checkName($("#accNameId", navTab.getCurrentPanel()))){
				return;
			}
		}
		$("#queryHistoryId", navTab.getCurrentPanel()).attr("action","clm/claimcare/showClaimAssinInfo_CLM_claimcareAssignAction.action");
		var flag1 = $("#comId", navTab.getCurrentPanel()).val()==null||$("#comId", navTab.getCurrentPanel()).val()=="";
		if(flag1){
		    alertMsg.error("机构不能为空");
		    return false;
		  }
		$("#queryHistoryId", navTab.getCurrentPanel()).submit();
	}
	
	function changeCaseNO(obj){
		obj.value=obj.value.replace(/\D/g,'');
		if(obj.value[0]!=9){
			obj.value='';
		}
	}
	function selectedRadioInput(){
		//获取被选中的数据行
		var selectedObj = $("input[name='claimcareListRadio']", navTab.getCurrentPanel());
		for(var i=0;i<selectedObj.length;i++){
		    if(selectedObj[i].checked){
		    	//获取被选中行 除序号列以外的其他列元素对象
				var siblingObject=$(selectedObj[i]).parent().nextAll(); 
				//执行ajax查询返回关怀历史详细信息数据
		    	var param={
		    		"agentType":$(siblingObject[1]).html(),
					"agentCode":$(siblingObject[2]).html(),
					"agentName":$(siblingObject[3]).html(),
					"com":$(siblingObject[4]).html(),
					"tel":$(siblingObject[5]).html(),
					"validFlag":$(siblingObject[6]).html()};
		    	var strDetailCareQueryParam = JSON.stringify(param);
				$.ajax({
					url:"clm/claimcare/queryClaimcareDetailInfo_claimcareDetailAction.action",
					type:"post",
					data:{"detailCareQueryParam":strDetailCareQueryParam},
					dataType:'json',
					success:function(data){	
						if(data.length==0){
							alertMsg.error("您要查询的详细信息不存在");
						}else{
							alert(data.agentType);
							$("#agentTypeId", navTab.getCurrentPanel()).val(data.agentType);//将获取到的值回填给相应元素
							$("#agentCodeId", navTab.getCurrentPanel()).val(data.agentCode);
							$("#agentNameId", navTab.getCurrentPanel()).val(data.agentName);
							$("#comId", navTab.getCurrentPanel()).val(data.com);
							$("#telId", navTab.getCurrentPanel()).val(data.tel);
							$("#validFlagId", navTab.getCurrentPanel()).val(data.validFlag);
							
							if(data.validFlag=="YES"){
								$("#validFlagId", navTab.getCurrentPanel()).attr("checked","checked");
							}else{
								$("#validFlagId", navTab.getCurrentPanel()).removeAttr("checked");
							};
						}
					},
				  error: function()
					{
					}
				});	
			}
		}
	}
		