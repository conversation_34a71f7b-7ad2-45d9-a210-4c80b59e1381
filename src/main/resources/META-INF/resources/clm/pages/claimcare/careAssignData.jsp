<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>

<script type="text/javascript">
$(function() {
	var flag = '${flag}';
	if (flag == 'true') {
		alertMsg.error('您要查询的关怀任务不存在，请重新输入查询条件！');
	}
});	
//获取查询条件
$(function(){
	var organCode = $("[name='claimCareVisitVO.organCode']", navTab.getCurrentPanel()).val();
	$("[name='claimCareVisitVO.organCode']", navTab.getCurrentPanel()).val(organCode);
	var caseNo = $("[name='claimCareVisitVO.caseNo']", navTab.getCurrentPanel()).val();
	$("[name='claimCareVisitVO.caseNo']", navTab.getCurrentPanel()).val(caseNo);
	var policyCode = $("[name='claimCareVisitVO.policyCode']", navTab.getCurrentPanel()).val();
	$("[name='claimCareVisitVO.policyCode']", navTab.getCurrentPanel()).val(policyCode);
	var customerName = $("[name='claimCareVisitVO.customerName']", navTab.getCurrentPanel()).val();
	$("[name='claimCareVisitVO.customerName']", navTab.getCurrentPanel()).val(customerName);
	var customerCertiCode = $("[name='claimCareVisitVO.customerCertiCode']", navTab.getCurrentPanel()).val();
	$("[name='claimCareVisitVO.customerCertiCode']", navTab.getCurrentPanel()).val(customerCertiCode);
	var revisitStatus = $("[name='claimCareVisitVO.revisitStatus']", navTab.getCurrentPanel()).val();
	$("[name='claimCareVisitVO.revisitStatus']", navTab.getCurrentPanel()).val(revisitStatus);
}); 
$("#tomessage", navTab.getCurrentPanel()).change(function(){
	  var check=$(this).attr("checked");
	  if(check=="checked"){
		    $("input#check","table#revistData").each(function(){
				if (!$(this).is(':checked')) {
					$(this).attr("checked",true);
				}
			});
	  }else{
		  $("input#check","table#revistData").each(function(){
				if ($(this).is(':checked')) {
					$(this).attr("checked",false);
				}
			});
	  }
	});
$("input#check", navTab.getCurrentPanel()).change(function(){
		var num=0,numone=0;
		$("input#check","table#revistData").each(function(){
			num++;
			if ($(this).is(':checked')) {
				numone++;
			}
		});
		if(numone==num){
			$("#tomessage", navTab.getCurrentPanel()).attr("checked",true);
		}else{
			$("#tomessage", navTab.getCurrentPanel()).attr("checked",false);
		}
});
</script>

<form id="pagerForm" method="post" action="clm/claimcare/showClaimAssinInfo_CLM_claimcareAssignAction.action">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
    <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
    <input type="hidden" name="claimCareVisitVO.organCode" />
    <input type="hidden" name="claimCareVisitVO.caseNo" />
    <input type="hidden" name="claimCareVisitVO.policyCode" />
    <input type="hidden" name="claimCareVisitVO.customerName" />
    <input type="hidden" name="claimCareVisitVO.customerCertiCode" />
    <input type="hidden" name="claimCareVisitVO.revisitStatus" />
</form>
<div class="panel">
	<h1>回访数据列表</h1>
	<div><input type="checkbox" size="5px" id="tomessage" value="0"/>全选</div>
	<div>
		<table class="list" width="100%" id="revistData">
			<thead>
				<tr>
					<th width="50">选择</th>
					<th width="80">赔案号</th>
					<th width="80">出险人姓名</th>
					<th width="80">管理机构</th>
					<th width="80">理赔类型/出险日期</th>
					<th width="80">出险结果</th>
					<th width="80">结案日期</th>
					<th width="80">下次回访日期</th>
					<th width="80">关怀次数</th>
					<th width="80">操作人</th>
					<th width="80">操作日期</th>
				</tr>
			</thead>
			<tbody>
				<!-- 循环显示数据 -->
				<s:iterator value="currentPage.pageItems" status="st" var="cp">
					<tr>
						<td align="center"><input type="checkbox" name="" value="${cp.caseNo}" id="check"/></td>
						<td align="center">${cp.caseNo}</td>
						<td align="center">${cp.customerName}</td>
						<td align="center">
						    <input type="hidden" value="${cp.organCode}"/>
							<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${cp.organCode}"/>
						</td>
						<td align="center"><input type="text" value="${cp.claimType}" readonly="readonly" style="width:100%;height:100%;border:0px;"/></td>
						<td align="center"><input type="text" value="${cp.accResult1}" readonly="readonly" style="width:100%;height:100%;border:0px;"/></td>
						<td align="center"><s:date name='#cp.endCaseTime' format='yyyy-MM-dd'/></td>
						<td align="center"><s:date name='#cp.nextVisitDate' format='yyyy-MM-dd'/></td>
						<td align="center">${cp.careTimes}</td>
						<td align="center">${cp.userName}</td>
						<td align="center"><s:date name='#cp.workDate' format='yyyy-MM-dd'/></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<!-- 分页查询区域 -->
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value},'careAssignDataId')"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab" rel="careAssignDataId"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}">
			</div>
		</div>
	</div>
</div>