<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@taglib uri="/struts-tags" prefix="s"%>
<!DOCTYPE html PUBLIC  "_//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%@ page import="java.util.*"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<!-- 分页切换页码提交使用 -->
<form id="pagerForm" action="clm/claimcare/queryAgentInfo_agentInfoQueryAction.action">
	<input type="hidden" name="pageNum" value="1" />
	<input type="hidden" name="numPerPage" value="${model.numPerPage}" />
	<input type="hidden" name="orderField" value="${param.orderField}" />
	<input type="hidden" name="orderDirection" value="${param.orderDirection}" />
</form>

<div class="pageHeader">
	<form rel="pagerForm" method="post" action="clm/claimcare/queryAgentInfo_agentInfoQueryAction.action" onsubmit="return dwzSearch(this, 'dialog');">
	<div class="searchBar">
		<ul class="searchContent">
			<li>
				<label>用户名/业务员代码</label>
				<input class="textInput" name="agentInfoVO.agentCode" value="" type="text">
			</li>	  
			<li>
				<label>姓名</label>
				<input class="textInput" name="agentInfoVO.agentName" value="" type="text">
			</li>
		</ul>
		<div class="subBar">
			<ul>
				<li>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="submit">查询</button>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	</form>
</div>
<div class="pageContent">

	<table class="list" layoutH="118" targetType="dialog" width="100%">
		<thead>
			<tr>
						<th width="100">选择</th>
						<th width="100">序号</th>
						<th width="100" orderfield="agentType">人员类型</th>
						<th width="150" orderfield="agentCode">用户名/业务员代码</th>
						<th width="100" orderfield="agentName">姓名</th>
						<th width="100" orderfield="certiCode">证件号码</th>
						<th width="100" orderfield="com">机构</th>
						<th width="100" orderfield="tel">手机号码</th>
						<th width="100" orderfield="employDate">入司日期</th>
						<th width="100" orderfield="validFlag">有效标识</th>
						<th width="80">查找带回</th>
			</tr>
		</thead>
		<tbody>
			<s:iterator value="agentInfoList" status="st">
				<tr align="center">
				
					<td><input type="radio" name="" value="" /></td>
					<td>${st.index+1}</td>
					<td>${agentType}</td>
					<td>${agentCode}</td>
					<td>${agentName}</td>
					<td>${certiCode}</td>
					<td>${com}</td>
					<td>${tel}</td>
					<td>${employDate}</td>
					<td>${validFlag}</td>
					<td>
						<a class="btnSelect" href="javascript:$.bringBack({agentCode:'${agentCode}', agentName:'${agentName}'})" title="查找带回"></a>
					</td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
		<div class="pages">
			<span>每页</span>
			<select class="combox title"  name="numPerPage" onchange="dwzPageBreak({targetType:dialog, numPerPage:'10'})">
				<option value="10" selected="selected">10</option>
				<option value="20">20</option>
				<option value="50">50</option>
				<option value="100">100</option>
			</select>
			<span>条，共2条</span>
		</div>
		<div class="pagination" targetType="dialog" totalCount="2" numPerPage="10" pageNumShown="1" currentPage="1"></div>
	</div>
</div>

<!-- 关闭按钮 -->
	<div class="formBar">
		<div class="button" style="margin-left: 350px">
			<div class="buttonContent">
				<button type="button">确认</button>
			</div>
		</div>
		<div class="button">
			<div class="buttonContent">
				<button type="button" class="close">退出</button>
			</div>
		</div>
	</div>
