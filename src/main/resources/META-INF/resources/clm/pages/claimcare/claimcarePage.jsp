<%@ page language="java" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--   引入jQuery -->
<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>

<script src="clm/pages/claimcare/claimcarePage.js" type="text/javascript"></script>

<form id="pagerForm" method="post"
	action="clm/claimcare/showClaimcareInfo_claimcareAction.action" onsubmit="return navTabSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	<!-- 查询事件访问路径 -->
<div class="searchBar">
	<table align="center"  class="searchContent" style="width: 100%;" >
			    <tr>
					<td>服务人员类型：</td>
					<td>
						<select class="combox title"  class="comboxDD" id="agentTypeId"
							name="claimcareServiceVO.agentType"  size="25">
							<option name="claimcareServiceVO.agentType" value="1" 
								>核心业务系统用户</option>
							<option value="2" >业务员</option>
							<option value="3"  >其他</option>
						</select>
					</td>
					<td ></td>
					<td>用户名/业务员代码：</td>
					<td><input type="text" name="claimcareServiceVO.agentCode" id="agentCodeId"
							 size="25"></td>
					<td></td>
					<td>姓名：</td>
					<td><input type="text" name="claimcareServiceVO.agentName" id="agentNameId"
							size="25"></td>
				</tr>
				<tr>
					<td>机构：</td>
					<td><input type="text" name="claimcareServiceVO.com" id="comId"
							 size="25" /></td>
				</tr>
			</table>
			 
					<div class="subBar">
						<div class="buttonActive" >
						<div class="button" style="margin-left: 500px">
							<div class="buttonContent">
								<button type="submit" onclick="preCareQuery();" >查询</button>
							</div>
						</div>
						</div>
						<div class="buttonActive">
							<div class="buttonContent">
								<button type="submit" >新增</button>
							</div>
						</div>
					</div>

</div>

	
		<!-- 显示数据列表区域 -->
		<div class="panel">
			<h1>服务人员列表</h1>
			<div>
				<table class="list" width="100%">
					<thead>
						<tr>
							<th width="50">选择</th>
							<th width="50">序号</th>
							<th width="100" orderfield="agentType">服务人员类型</th>
							<th width="100" orderfield="agentCode">用户名/业务员代码</th>
							<th width="100" orderfield="agentName">姓名</th>
							<th width="100" orderfield="com">机构</th>
							<th width="100" orderfield="tel">手机号码</th>
							<th width="100" orderfield="validFlag">有效标志</th>
						</tr>
					</thead>
					<tbody>
						<!-- 循环显示数据 -->
						<s:iterator value="claimcareListVO" status="status">
							<tr>
								<td><input type="radio" name="claimcareListRadio" id=id<s:property value="#status.index"/>
										onclick="selectedRadioInput();" /></td>
								<td><s:property value="status.index+1" /></td>
								<td><s:property value="agentType" /></td>
								<td><s:property value="agentCode" /></td>
								<td><s:property value="agentName" /></td>
								<td><s:property value="com" /></td>
								<td><s:property value="tel" /></td>
								<td><s:property value="validFlag" /></td>								
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<!-- 分页查询区域 -->
				<div class="panelBar">
					<ul style="margin-left: 500px">
						<li class="nowrap">
							<div class="button">
								<div class="buttonContent">
									<a href="uw/nbmanualuw/ExportAfterQualityCheckRule_uwAfterQualityCheckRuleAction.action"><button type="button">数据下载</button></a>
								</div>
							</div>
						</li>
					</ul>
					<div class="pages">
						<span>显示</span>
						<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
							name="select" onchange="navTabPageBreak({numPerPage:this.value})"
							value="currentPage.pageSize">
						</s:select>
						<span>条，共${currentPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>
				</div>
			</div>
		</div>

		<h1></h1>
		<h1></h1>
		<div class="panel">
			<h1>服务人员信息</h1>
			<div class="pageFormContent" >
				<dl style="width: 32%">
					<dt>
						服务人员类型 <font class="point" color="red">*</font>
					</dt>
					<dd>
						<select class="combox title"  class="comboxDD" id="agentTypeId"
							name="claimcareServiceVO.agentType" ref="" size="25">
							<option name="claimcareServiceVO.agentType" value="1"
								selected="selected">核心业务系统用户</option>
							<option value="2">业务员</option>
							<option value="3">其他</option>
						</select>
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>用户名/业务员代码</dt>
					<dd>
						<input name="claimcareServiceVO.agentCode" id="agentCodeId"
							 type="text" readonly="true"
							suggestFields="agentCode,agentName"
							suggestUrl="clm/claimcare/showAgentInfo_agentInfoQueryAction.action"
							lookupGroup="claimcareServiceVO" size="25" /> 
							<a class="btnLook"
							href="clm/claimcare/showAgentInfo_agentInfoQueryAction.action"
							lookupGroup="claimcareServiceVO">查找带回</a>
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>
						姓名<font class="point" readonly="true" color="red" >*</font>
					</dt>
					<dd>
						<input class="readonly" type="text" name="claimcareServiceVO.agentName"  id="agentNameId" readonly="readonly" 
							size="25" />
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>证件号码</dt>
					<dd>
						<input type="text" name="claimcareServiceVO.certiCode"  id="certiCodeId" 
							id="certiCode" size="25" />
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>机构</dt>
					<dd>
						<input type="select" name="claimcareServiceVO.com" value="8621" id="comId"  size="20" />
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>入司日期</dt>
					<dd>
						<input type="expandDateYMD" id="employDateId" flag="flag" class="date" 
							name="claimcareServiceVO.employDate" size="25" /> <a
							class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>业务员标识：</dt>
					<dd>
						<select class="combox title"  class="comboxDD" id="agentTypeFlagId"
							name="claimCaseVO.agentTypeFlag" ref="" size="25">
							<option name="claimCaseVO.applyType1" value="1"
								selected="selected">明星</option>
							<option value=" ">普通</option>
						</select>
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>电话</dt>
					<dd>
						<input type="text" name="claimcareServiceVO.phone"
							 id="phoneId" size="25">
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>
						手机号码<font class="point" color="red">*</font>
					</dt>
					<dd>
						<input type="expandMobile" name="claimcareServiceVO.tel"
							id="telId"  size="25">
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt style="width: 32%">
						电子邮箱<font class="point" color="red">*</font>
					</dt>
					<dd style="width: 60%">
						<input name="claimCaseVO.Email"
							 id="EmailId" class="email" size="25" />
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt style="width: 32%">服务起期</dt>
					<dd>
						<input type="expandDateYMD" id="serviceStartDateId" flag="flag" class="date" 
							name="claimcareServiceVO.serviceStartDate" size="25" /> <a
							class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>志愿者职务</dt>
					<dd>
						<input type="text" name="claimcareServiceVO.duty"
							id="dutyId"  size="25">
					</dd>
				</dl>
				<dl style="width: 32%">
					<dt>志愿者组别</dt>
					<dd>
						<input type="text" name="claimcareServiceVO.group"
							id="groupId" size="25">
					</dd>
				</dl>
				<dl style="width: 64%">
					<dt>有效标志</dt>
					<dd>
						<input type="checkbox"
							name="claimcareServiceVO.validFlag" value="YES" id="validFlagId"
							checked="checked" />
					</dd>
				</dl>
				<dl class="nowrap">
					<h1 class="contentTitle">工作履历</h1>
					<dd>
						<textarea class="required" rows="5" cols="100" maxlength="500"   id="workRecordId" name="claimcareServiceVO.workRecord"></textarea>
					</dd>
				</dl>
				<dl class="nowrap">
					<h1 class="contentTitle">志愿履历</h1>
					<dd>
						<textarea class="required" readonly="true" id="" rows="5"  id="volunteerRecordId" name="claimcareServiceVO.volunteerRecord"
							cols="100"></textarea>
					</dd>
				</dl>

			</div>
		</div>
	</form>
	<s:if test="flag==update">
		<div class="formBar">
			<div class="button" style="margin-left: 500px">
				<div class="buttonContent">
					<button type="button">修改</button>
				</div>
			</div>
			<div class="button">
				<div class="buttonContent">
					<button type="button">退出</button>
				</div>
			</div>
		</div>
	</s:if>
	<s:else>
		<div class="formBar">
				<div class="button" style="margin-left: 500px">
					<div class="buttonContent">
						<button type="button">保存</button>
					</div>
				</div>
				<div class="button">
					<div class="buttonContent">
						<button type="button">退出</button>
					</div>
				</div>
		 </div>
	</s:else>
