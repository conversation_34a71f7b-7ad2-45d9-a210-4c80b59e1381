<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<!DOCTYPE html PUBLIC  "_//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%@ page import="java.util.*"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">

<script type="text/javascript">
	$(function() {
		var flag = '${flag}';
		if (flag == 'true') {
			alertMsg.error('您要查询的服务人员信息不存在，请重新输入查询条件！');
		}
	});
	function queryUserOrServer() {
		var serverCode = $("#serverCode", $.pdialog.getCurrent()).val().trim();
		var serverName = $("#serverName1", $.pdialog.getCurrent()).val().trim();
		if (serverName == "" && serverCode == "") {
			alertMsg.warn("查询条件当中请至少输入一项!");
			return false;
		}
		$("#pagerForms", $.pdialog.getCurrent()).submit();
	}
</script>
<div layoutH="36">
<!-- 分页切换页码提交使用 -->
<form id="pagerForm" method="post"
	action="clm/claimcare/queryUserOrServerInfoList_CLM_queryServerAction.action">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo }" />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<form id="pagerForms" method="post"
	action="clm/claimcare/queryUserOrServerInfoList_CLM_queryServerAction.action"
	onsubmit="return dialogSearch(this);"
	class="pagerForm required-validate" novalidate="novalidate"
	rel="pagerForm">
	<div class="pageFormInfoContent">
		<dl>
			<dt>用户名/业务员代码</dt>
			<dd><input class="textInput" name="serverCode" type="text"
				id="serverCode" value="${serverCode}"></dd>
		</dl>
		<dl>
			<dt>姓名</dt>
			<dd><input class="textInput" type="text" name="serverName"
				id="serverName1" value="" /></dd>
			<td>
		</dl>
		<div class="pageFormdiv">
			<button type="button" class="but_blue"
				onclick="queryUserOrServer();">查询</button>
		</div>
	</div>
</form>
<div>
<div class="tabdivclassbr main_tabdiv">
	<table class="list" targetType="dialog" width="100%">
		<thead>
			<tr>
				<th nowrap>序号</th>
				<th nowrap>人员类型</th>
				<th nowrap>用户名/业务员代码</th>
				<th nowrap>姓名</th>
				<th nowrap>证件号码</th>
				<th nowrap>机构</th>
				<th nowrap>手机号码</th>
				<th nowrap>入司日期</th>
				<th nowrap>有效标识</th>
				<th nowrap>查找带回</th>
			</tr>
		</thead>
		<tbody>
				<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
			<s:iterator value="currentPage.pageItems" status="st" var="cps">
				<tr align="center">
					<td>${st.index+1}</td>
					<td><s:if test="agentType == 1">
							业务员
						</s:if> <s:if test="agentType == 2">
							用户
						</s:if></td>
					<td>${cps.serverCode}</td>
					<td>${cps.name}</td>
					<td>${cps.certiCode}</td>
					<td>${cps.organCode}</td>
					<td>${cps.phone}</td>
					<td><s:date name='#cps.entryDate' format='yyyy-MM-dd' /></td>
					<td><s:if test="#cps.validFlag == 1">
								是
						</s:if> <s:if test="#cps.validFlag == 2">
						                   否
						</s:if></td>
					<td><a class="btnSelect"
						href="javascript:$.bringBack({serverCode:'${cps.serverCode}', name:'${cps.name}',certiCode:'${cps.certiCode}'})"
						title="查找带回"></a></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
		<div class="pages">
			<span>每页</span>
			<s:select list="#{5:'5',10:'10',15:'15',20:'20'}" name="select"
				onchange="dialogPageBreak({numPerPage:this.value})"
				value="currentPage.pageSize">
			</s:select>
			<span>条，共${currentPage.total }条</span>
		</div>
		<div class="pagination" targetType="dialog"
			totalCount="${currentPage.total }"
			numPerPage="${currentPage.pageSize }" pageNumShown="10"
			currentPage="${currentPage.pageNo }"></div>
	</div>
</div>
</div>

<!-- 关闭按钮 -->
<div class="formBarButton">
	<ul>
		<li><button type="button" class="but_gray close">退出</button></li>
	</ul>
</div>
</div>
