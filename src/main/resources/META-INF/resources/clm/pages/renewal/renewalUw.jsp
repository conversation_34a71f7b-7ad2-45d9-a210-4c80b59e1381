<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--引入jQuery -->
<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script> 
<script type="text/javascript">
	function queryPolicy(){
		var caseNo=$("#caseNo",navTab.getCurrentPanel()).attr("value");
		if(caseNo.length != 0 && caseNo.length != 11){
			alertMsg.warn("录入的赔案号不正确，请重新输入！");
			return false;
		}else if(caseNo==""){
			alertMsg.warn("请输入赔案号！");
			return false;
		} else{
			$("#sendPolicyUw", navTab.getCurrentPanel()).submit();
			return true;
		}
	}
	function sendPolicyUw(){
		 $.ajax({
				'type':'post',
				'url':'clm/renewal/sendPolicyUw_CLM_renewalUwAction.action',
				'data':$("#sendPolicyUw").serialize(),
				'datatype':'json',
				'success':function(data){
					alertMsg.correct(data);
				},
				'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
				}
		});  
	}
</script>

<!-- 页面内容 -->
<div layoutH="0">
	<!-- 查询事件访问路径 -->
	<form action="clm/renewal/queryPolicyByCaseNo_CLM_renewalUwAction.action" method="post" id="sendPolicyUw" onsubmit="return navTabSearch(this)" class="pagerForm required-validate">
		
	<!-- 查询区域 -->
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">发起续保人工核保
		</h1>
	</div>
	<div class="pageFormInfoContent">
		<dl>	
			<dt>赔案号</dt>
			<dd>
				<input type="text" id="caseNo" name="caseNo" size="11" value="${caseNo}"/>
			</dd> 
		</dl> 
		<div class="pageFormdiv">
			<button type="button" class="but_blue" onclick="queryPolicy();">查询</button> 
		</div>
	</div>
		
	<div class="tabdivclassbr" style="margin-top: 15px;">
		<table class="list" width="100%">
			<thead>
				<tr>
					<th nowrap>选择</th>
					<th nowrap>序号</th>
					<th nowrap>保单号码</th>
					<th nowrap>管理机构</th>
					<th nowrap>投保人姓名</th>
					<th nowrap>被保人姓名</th>  
				</tr>
			</thead>
			<tbody>
				<s:if test="imageFlag != null">
					<tr>
						<td colspan="9">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif test="renewalUwVOs == null || renewalUwVOs.size()==0">
					<tr>
						<td colspan="9">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>
					<s:iterator value="renewalUwVOs"  status="st" id="renewalUwVO">
						<tr align="center">
							<td><input type="checkbox" name="renewalUwVOs.policyCode" value="<s:property value='#renewalUwVO.policyCode'/>"></td>
							<td> ${st.index+1}</td>
							<td><s:property value="#renewalUwVO.policyCode"/></td>
							<td><s:property value="#renewalUwVO.organCode" /></td>
							<td><s:property value="#renewalUwVO.holderName" /></td>
							<td><s:property value="#renewalUwVO.insuredName" /></td>
						</tr>
					</s:iterator>
			</tbody>
		</table>
	</div>
		<div class="formBarButton">
				<ul>
				   <li><button type="button" class="but_blue" onclick="sendPolicyUw();">确认</button></li>
				</ul>
		</div>
	</form>
</div>
 