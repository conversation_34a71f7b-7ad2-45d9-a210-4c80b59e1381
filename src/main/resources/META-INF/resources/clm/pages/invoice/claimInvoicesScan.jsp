<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>

<script type="text/javascript" language="javascript"
	src="clm/pages/invoice/claimInvoicesScan.js">
	
</script>
<body>
	<div id="claimInvoicesScan" class="pageHeader" layoutH="36">
		<div class="panel">
			<h1>单证管理》单证扫描</h1>
			<div class="panelPageFormContent">
				<div>
					<form id="invoicesScan" class="pageForm required-validate"
						action="clm/invoice/invoicesScanStart_CLM_claimInvoicesScanAction.action"
						method="post"
						onsubmit="return validateCallback(this, dialogAjaxDone)"
						novalidate="novalidate">
						<fieldset style="border-style: solid;">
							<legend style="border-style: solid;">单证扫描</legend>
							<div class="divfclass">
								<h1>
									<img src="clm/images/tubiao.png">赔案信息
								</h1>
							</div>
							<div class="panelPageFormContent">
								<dl style="width: 100%;">
				                	<dt style="width: 30%;">
										赔案号<font style="color: red">* </font>
									</dt>
									<dd>
										<input id="caseNo" name="caseNo" value="${caseNo }"
											type="text" onkeyup="this.value=this.value.replace(/\D/g,'')"
											maxlength="11" onblur = "caseNoIsRealPass(this)"/>
									</dd>
								</dl>
							</div>
							<div class="panelPageFormContent">
								  <dl style="width: 100%;">
								  <dt style="width: 30%;">是否理赔决定通知书（或不予立案通知书）类型单证<font>*</font></dt>
								   <dd>
						             <s:select id="isRejectDocChecklist" list="#{1:'是',0:'否'}" cssClass="notuseflagDelete combox title comboxDD" 
						                onchange="controlSendTime(this)" value="isRejectDocChecklist" name="isRejectDocChecklist">
					                 </s:select>
					                 <input type="hidden" id ="isDocReject" name="isDocReject" value="0" />
					                 <input type="hidden" id ="docRejectJudge" name="docRejectJudge" value="1" />
					                 <input type="hidden" id ="exitJudge" name="exitJudge" value="0" />
					               </dd> 
								  </dl>
							</div>  
							<div class="panelPageFormContent">
								  <dl style="width: 100%;">
				                	<dt style="width: 30%;">是否实名制查验证明类单证</dt>
								   <dd>
                             		<s:select id="isDocProve" list="#{1:'是',0:'否'}" cssClass="combox title" 
						                 value="isDocProve" name="isDocProve" disabled="true" >
					                 </s:select>
					               </dd> 
								  </dl>
							</div>  
							<div class="panelPageFormContent" id="rejectDocSendTime">
								  <dl style="width: 100%;">
				                	<dt style="width: 30%;">理赔决定通知书（或不予立案通知书）寄送日期<font>*</font></dt>
				                   	<dd>
                                      <input type="expandDateYMD" name="claimSendNoticeVO.inspectionRegisterTime"
								             class="date" id="inspectionRegisterTime"
								             value="<s:date name='claimSendNoticeVO.inspectionRegisterTime' format='yyyy-MM-dd'/>" /><a
								             class="inputDateButton" href="javascript:;">选择</a>
					               </dd>
			                  	</dl>
							</div>
							<div class="pageFormdiv">
							
							            <button type="button" id="saveNews" class="but_blue" onclick="saveMess();">保存</button>
										<button class="but_blue" type="button" value="补扫" disabled id="scanButton"
										onclick="invoicesScanSubmit()">扫描</button>
										
								    	<button type="button" class="but_gray" onclick="exit1();">退出</button>
							</div>
							<div></div>
						</fieldset>
					</form>
				</div>

			</div>
		</div>
	</div>
</body>



