/*$(document).ready(function() {
	  //navTab重新初始化 
	 navTab.init();  这是导致tab乱跳的原因
  });*/

function seleClaimCase(){
	var caseNo = $('#caseNo', navTab.getCurrentPanel()).val();
	var caseStatus = $('#caseStatus', navTab.getCurrentPanel()).val();
	var policyno = $('#policyno', navTab.getCurrentPanel()).val();
	var organName = $('#branchname', navTab.getCurrentPanel()).val();
	var insuredName = $('#insuredName', navTab.getCurrentPanel()).val();
	var insuredCode = $('#insuredCode', navTab.getCurrentPanel()).val();
	var startDate = $('#startDate', navTab.getCurrentPanel()).val();
	var endDate = $('#endDate', navTab.getCurrentPanel()).val();
	if(!((caseNo!="" || policyno!="" || insuredCode!="") || 
			(startDate!="" && endDate!="" && (caseStatus !="" || organName!="" || insuredName!=""|| 
					$("input[type='checkbox']", navTab.getCurrentPanel()).is(':checked') == true)))){
		alertMsg.error("请至少录入如下查询条件之一：“赔案号”、“保单号”、“出险人证件号码”、“单证生成日期区间”合并任一其他条件！");
		return false;
	}
	if(insuredName != ""){
		if(!checkName($("#insuredName", navTab.getCurrentPanel()))){
			return;
		}
	}
    var checkAll = "";
    $('input[flag="flag"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') != false){
			checkAll = checkAll + $(this).val() +',';
		}
	});
    
    $('#checkAll', navTab.getCurrentPanel()).val(checkAll);
    $('#checkAllhid', navTab.getCurrentPanel()).val(checkAll);
    var seNo = $('#select', navTab.getCurrentPanel()).val();
	$('#seleNo', navTab.getCurrentPanel()).val(seNo);
	$('#seleNohid', navTab.getCurrentPanel()).val(seNo);
    $('#confirmForms', navTab.getCurrentPanel()).submit();
}

//改变每页显示条数时重新调用此方法，不提交表单
function seleClaimCaseChange(){
	/*var caseNo = $('#caseNo', navTab.getCurrentPanel()).val();
	var caseStatus = $('#caseStatus', navTab.getCurrentPanel()).val();
	var policyno = $('#policyno', navTab.getCurrentPanel()).val();
	var organName = $('#organName', navTab.getCurrentPanel()).val();
	var insuredName = $('#insuredName', navTab.getCurrentPanel()).val();
	var insuredCode = $('#insuredCode', navTab.getCurrentPanel()).val();
	var startDate = $('#startDate', navTab.getCurrentPanel()).val();
	var endDate = $('#endDate', navTab.getCurrentPanel()).val();
	if(caseNo=="" && caseStatus=="" && policyno=="" && organName=="" && insuredName=="" && insuredCode=="" && startDate=="" && endDate==""){
		if($("input[type='checkbox']", navTab.getCurrentPanel()).is(':checked')==false){
			alertMsg.error("查询条件至少有一项不能为空！!");
			return false;
		}
	}*/
    var checkAll = "";
    $('input[flag="flag"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') != false){
			checkAll = checkAll + $(this).val() +',';
		}
	});
    $('#checkAll', navTab.getCurrentPanel()).val(checkAll);
    $('#checkAllhid', navTab.getCurrentPanel()).val(checkAll);
    var seNo = $('#select', navTab.getCurrentPanel()).val();
	$('#seleNo', navTab.getCurrentPanel()).val(seNo);
	$('#seleNohid', navTab.getCurrentPanel()).val(seNo);
}

/*function exit(){
	$("#printShow", navTab.getCurrentPanel()).hide();
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
}*/

function printDocuments(p){
	var check = 0;
	$('input[flag="flagprint"]').each(function(){		
		$(this).val(0);
   });	
   $('input[flag="flagprint"]').each(function(){
	if($(this).prop('checked') != false){
		$(this).val(1);
		check = check + 1;
	}
   });	
   if (check == 0) {
	   alertMsg.error("请选择一条记录!");
		return false;
   }
   var printType = '';
   if(p==1){//打印
	   printType='Print'; 
   }else if(p==2){//预览和打印
	   printType='PreviewAndPrint'; 
   }
	$.ajax({
		'type':'post',
		'url':'clm/invoice/claimCompersatePrint_CLM_claimInvoicesPrintAction.action?printType='+printType,
		'data':$("#confirmForms").serialize(),
		'datatype':'json',
		'success':function(data){
			var data = eval("("+data+")");
			//批量打印循环拼接
			var printHtml;
			for(var i=0;i<data.length;i++){
				dataNew = data[i];
				if(dataNew.sessionStatus=="NO"||dataNew.statusCode==DWZ.statusCode.timeout||dataNew.sessionStatus==undefined){
					alertMsg.error("会话超时,请重新登录!");
				}else{
					if(dataNew.jobId!="" && dataNew.error!=""){
						alertMsg.error("单证打印失败!");
					}else{
						//批量打印拼接
						printHtml += "<object  type=\"application/x-java-applet\" >"+
						"<param name=\"code\" value=\"SipRpcltApplet.class\" />"+
						"<param name=\"archive\" value=\"SipRpclt.jar\" />" +
						"<param name=\"reqId\" value='"+dataNew.jobId+"' />"+
						"<param name=\"server\" value='"+dataNew.serviceIp+"'/>"+
						"<param name=\"operation\" value='"+dataNew.printType+"' />"+
						"</object>";
					}
				}
			}
			//批量打印需要循环调用打印插件
			$("#printShow", navTab.getCurrentPanel()).show().html(printHtml);
			
			if(p==1){
				alertMsg.correct("单证打印成功");
			}
			$("#printShow", navTab.getCurrentPanel()).hide();
		},
		'error':function(){
			alertMsg.error("打印失败!");
		}
});  

}

function showPreviewButton() {
	var check = 0;
	$('input[flag="flagprint"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') != false){
			$(this).val(1);
			check = check + 1;
		}
	});
	
	var JsonList  = "[";				
		 //循环 拼接选中的提交的值
		 $("table#tablecheck", $.pdialog.getCurrent()).find("tbody").find("tr").each(function (){
			if($(this).find("td").eq(0).find("input").is(":checked")){
				var caseNo = $(this).find("td").eq(1).find("input").val();
				var documentType = $(this).find("td").eq(6).find("input").val();
				JsonList = JsonList + "{'caseNo':'"+ caseNo + "','documentType':'"+ documentType + "'},";
			}
		 });
	
	 JsonList = JsonList.substr(0,JsonList.length-1) + "]";
	 
	 if(JsonList == "]"){
		 seleClaimCase();
	 }else{	 
		 $.ajax({
				'url':"clm/invoice/queryClaimDocSendLog_CLM_claimInvoicesPrintAction.action" ,
				'type':'post',
				'data':{jsonA:JsonList},
				'datatype':'json',
				 'success':function(html){
					$("#claimDocSendLog",navTab.getCurrentPanel()).empty();
					$("#claimDocSendLog",navTab.getCurrentPanel()).append(html);
					initUI($("#claimDocSendLog",navTab.getCurrentPanel()));
				 }
			});
		 $.ajax({
				'url':"clm/invoice/findClaimDocumentEmail_CLM_claimInvoicesPrintAction.action" ,
				'type':'post',
				'data':{jsonA:JsonList},
				'datatype':'json',
				 'success':function(html){
					$("#claimDocSendMail",navTab.getCurrentPanel()).empty();
					$("#claimDocSendMail",navTab.getCurrentPanel()).append(html);
					initUI($("#claimDocSendMail",navTab.getCurrentPanel()));
				 }
			});
	 } 
	if (check == 1) {
		$("#prewButton", navTab.getCurrentPanel()).css("display", "");				
	} else {
		$("#prewButton", navTab.getCurrentPanel()).css("display", "none");	
	}
	
	
	
}



