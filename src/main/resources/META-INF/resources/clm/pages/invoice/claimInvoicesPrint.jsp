<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/pages/invoice/claimInvoicesPrint.js">
	
</script>
<style>
.spanBox span {width:22%}	
</style>
<script>
	var documentListSize = '${documentListSize}';
	if (documentListSize != "" && documentListSize == "0") {
		alertMsg.info("您要查询的赔案不存在，请重新输入查询条件!");
	}
</script>
<div layoutH="0">
	<form id="pagerForm" method="post"
		action="clm/invoice/claimInvoicesPrintIni_CLM_claimInvoicesPrintAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
		<input type="hidden" id="checkAllhid" name="claimCaseVO.checkAll"
			value="${claimCaseVO.checkAll}" /> <input type="hidden"
			id="seleNohid" name="claimCaseVO.seleNohid"
			value="${claimCaseVO.seleNohid}">
	</form>
	<form id="confirmForms" class="pagerForm required-validate"
		novalidate="novalidate" method="post"
		onsubmit="return navTabSearch(this)"
		action="clm/invoice/claimInvoicesPrintIni_CLM_claimInvoicesPrintAction.action"
		rel="pagerForm">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询条件</h1>
		</div>
		 
			<input type="hidden" id="seleNo" value="${claimCaseVO.seleNo}"
				name="claimCaseVO.seleNo">
		
				<div class="pageFormInfoContent">
					<dl>
						<dt>赔 案 号</dt>
						<dd>
							<input name="claimCaseVO.caseNo" type="text"
								value="${claimCaseVO.caseNo}" id="caseNo" maxlength="20"
								onkeyup="this.value=this.value.replace(/[^A-Za-z0-9]+$/gi,'')" />
						</dd>
					</dl>
					<dl>
						<dt>赔案状态</dt>
						<dd>
							<Field:codeTable cssClass="combox title"
								name="claimCaseVO.caseStatus"
								tableName="APP___CLM__DBUSER.T_CASE_STATUS"
								value="${claimCaseVO.caseStatus}" id="caseStatus"
								nullOption="true" />
						</dd>
					</dl>
					<dl>
						<dt>保单号</dt>
						<dd>
							<input name="claimCaseVO.policyCode" type="text"
								value="${claimCaseVO.policyCode}" id="policyno" maxlength="20"
								onkeyup="this.value=this.value.replace(/[^A-Za-z0-9]+$/gi,'')" />
						</dd>
					</dl>
					<dl>
						<dt>管理机构</dt>
						<dd>
							<input id="menuBtn" name="claimCaseVO.organCode" style="width: 30px; border-right: 0;" size="8"
								value="${claimCaseVO.organCode}" type="text" class="organ" clickId="printMenuBtn" showOrgName="branchname" needAll="true" />
							<input id="branchname" type="text" style="width: 110px" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${claimCaseVO.organCode}" />" readOnly /> 
							<a id="printMenuBtn" class="btnLook" href="#" style="position: relative;"></a>
						</dd>
					</dl>
					<dl>
						<dt  >出险人姓名</dt>
						<dd  >
							<input name="claimCaseVO.insuredName" type="text"
								value="${claimCaseVO.insuredName}" id="insuredName"
								maxlength="30" onkeyup="this.value=this.value.replace(/\s/g,'')" onblur="resetStyle(this)"/>
						</dd>
					</dl>
					<dl>
						<dt>出险人证件号码</dt>
						<dd>
							<input name="claimCaseVO.insuredCode" type="text"
								value="${claimCaseVO.insuredCode}" id="insuredCode"
								maxlength="30"  />
						</dd>
					</dl>
					<dl>
						<dt>单证生成日期区间</dt>
						<dd>
							<input type="expandDateYMD" name="claimCaseVO.documentSDate"
								class="date" id="startDate"
								value="<s:date name='claimCaseVO.documentSDate' format='yyyy-MM-dd'/>" /><a
								class="inputDateButton" href="javascript:;">选择</a>
							<%-- <input name="claimCaseVO.documentSDate" type="expandDateYMDRO" id="startDate" value="<s:date name='claimCaseVO.documentSDate' format='yyyy-MM-dd'/>" readonly="readonly"/> --%>
							<span style="padding-left: 15px;">至</span>
						</dd>
						<dd style="width:">
							<input type="expandDateYMD" name="claimCaseVO.documentEDate"
								class="date" id="endDate" gt="startDate"
								value="<s:date name='claimCaseVO.documentEDate' format='yyyy-MM-dd' />" /><a
								class="inputDateButton" href="javascript:;">选择</a>
							<%-- <input name="claimCaseVO.documentEDate" type="expandDateYMDRO" id="endDate" gt="startDate" value="<s:date name='claimCaseVO.documentEDate' format='yyyy-MM-dd'/>" readonly="readonly"/> --%>
						</dd>
					</dl>
					<dl style="width:100%;height:auto">
						<dt>单证类型</dt>
						<dd style="width:80%">
							<input type="hidden" id="checkAll"
								value="${claimCaseVO.checkAll}" name="claimCaseVO.checkAll">
							<span class="spanBox" style="width: 100%;">
							<s:if test="document10 == 'CLM_00010'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00010"
									name="claimCaseVO.documentType" checked="checked" />赔付依据与说明
								</span>
							</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00010"
									name="claimCaseVO.documentType" />赔付依据与说明
								</span>
							</s:else>
							<s:if test="document01 == 'CLM_00001'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00001"
									name="claimCaseVO.documentType" checked="checked" />分割单
								</span>
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00001"
									name="claimCaseVO.documentType" />分割单
								</span>
					    	</s:else>
					    	<s:if test="document06 == 'CLM_00006'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00006"
									name="claimCaseVO.documentType" checked="checked" />理赔案件审核报告
								</span>
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00006"
									name="claimCaseVO.documentType" />理赔案件审核报告
								</span>
					    	</s:else>
					    	</span>
					    	
					    	<span class="spanBox" style="width: 100%;">
					    	<s:if test="document18 == 'CLM_00018'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00018"
									name="claimCaseVO.documentType" checked="checked" />批单-理赔给付批注
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00018"
									name="claimCaseVO.documentType" />批单-理赔给付批注
								</span>
					    	</s:else>
					    	<s:if test="document16 == 'CLM_00016'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00016"
									name="claimCaseVO.documentType" checked="checked" />批单-合同处理批注
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00016"
									name="claimCaseVO.documentType" />批单-合同处理批注
								</span>
					    	</s:else>
					    	<s:if test="document17 == 'CLM_00017'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00017"
									name="claimCaseVO.documentType" checked="checked" />批单-豁免保费批注
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00017"
									name="claimCaseVO.documentType" />批单-豁免保费批注
								</span>
					    	</s:else>
					    	<s:if test="document15 == 'CLM_00015'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00015"
									name="claimCaseVO.documentType" checked="checked" />批单-理赔预付保险金给付批注
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00015"
									name="claimCaseVO.documentType" />批单-理赔预付保险金给付批注
								</span>
					    	</s:else>
					    	
					    	
							<s:if test="document03 == 'CLM_00003'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00003"
									name="claimCaseVO.documentType" checked="checked" />不予立案通知书
								</span>
							</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00003"
									name="claimCaseVO.documentType" />不予立案通知书
								</span>
					    	</s:else>
					    	<%-- <s:if test="document12 == 'CLM_00012'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00012"
									name="claimCaseVO.documentType" checked="checked" />理赔决定通知书(审核)
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00012"
									name="claimCaseVO.documentType" />理赔决定通知书(审核)
								</span>
					    	</s:else> --%>
					    	<s:if test="document02 == 'CLM_00002'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00002"
									name="claimCaseVO.documentType" checked="checked" />理赔决定通知书（拒付）
								</span>
							</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00002"
									name="claimCaseVO.documentType" />理赔决定通知书（拒付）
								</span>
					    	</s:else>
					    	<s:if test="document21 == 'CLM_00021'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00021"
									name="claimCaseVO.documentType" checked="checked" />合同解除通知书
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00021"
									name="claimCaseVO.documentType" />合同解除通知书
								</span>
					    	</s:else>
					    	
					    	
					    	<s:if test="document11 == 'CLM_00011'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00011"
									name="claimCaseVO.documentType" checked="checked" />理赔照会结论通知书
								</span>
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00011"
									name="claimCaseVO.documentType" />理赔照会结论通知书
								</span>
					    	</s:else>
					    	<s:if test="document20 == 'CLM_00020'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00020"
									name="claimCaseVO.documentType" checked="checked" />续保核保结论通知书
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00020"
									name="claimCaseVO.documentType" />续保核保结论通知书
								</span>
					    	</s:else>
					    	<s:if test="document19 == 'CLM_00019'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00019"
									name="claimCaseVO.documentType" checked="checked" />不自动续保通知书
								</span>	
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00019"
									name="claimCaseVO.documentType" />不自动续保通知书
								</span>
					    	</s:else>
							<s:if test="document05 == 'CLM_00005'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00005"
									name="claimCaseVO.documentType" checked="checked" />理赔回退收费通知书
								</span>
							</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00005"
									name="claimCaseVO.documentType" />理赔回退收费通知书
								</span>
					    	</s:else>
					    	
<%-- 					    		<s:if test="document08 == 'CLM_00008'"> --%>
<%-- 									<span> --%>
<!-- 										<input type="checkbox" flag="flag" value="CLM_00008" -->
<!-- 										name="claimCaseVO.documentType" checked="checked" />索赔申请书 -->
<%-- 									</span> --%>
<%-- 								</s:if> --%>
<%-- 							<s:else> --%>
<%-- 								<span> --%>
<!-- 									<input type="checkbox" flag="flag" value="CLM_00008" -->
<!-- 									name="claimCaseVO.documentType" />索赔申请书 -->
<%-- 								</span> --%>
<%-- 					    	</s:else> --%>
							<s:if test="document07 == 'CLM_00007'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00007"
									name="claimCaseVO.documentType" checked="checked" />索赔文件签收清单
								</span>
							</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00007"
									name="claimCaseVO.documentType" />索赔文件签收清单								
								</span>
					    	</s:else>
							<s:if test="document04 == 'CLM_00004'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00004"
									name="claimCaseVO.documentType" checked="checked" />理赔单证通知书
								</span>
							</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00004"
									name="claimCaseVO.documentType" />理赔单证通知书
								</span>
					    	</s:else>
							<s:if test="document09 == 'CLM_00009'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00009"
									name="claimCaseVO.documentType" checked="checked" />补充提交理赔单证通知书&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								</span>
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00009"
									name="claimCaseVO.documentType" />补充提交理赔单证通知书
								</span>
					    	</s:else>
					    	<s:if test="document09 == 'CLM_00023'">
								<span>
									<input type="checkbox" flag="flag" value="CLM_00023"
									name="claimCaseVO.documentType" checked="checked" />缴费申请书&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								</span>
					    	</s:if>
							<s:else>
								<span>
									<input type="checkbox" flag="flag" value="CLM_00023"
									name="claimCaseVO.documentType" />缴费申请书
								</span>
					    	</s:else>
					    	
					    	</span>	
						</dd>
					</dl>
					<div class="pageFormdiv">
						<button type="button"class="but_blue" onclick="seleClaimCase()">查询</button>
					</div>	 
			</div>
       
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询赔案信息列表</h1>
		</div>
			<div class="tabdivclassbr tableScope">
				<table class="list main_dbottom" width="100%" id="tablecheck">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>赔案号</th>
							<th nowrap>赔案状态</th>
							<th nowrap>出险人姓名</th>
							<th nowrap>出险人证件号码</th>
							<th nowrap>出险日期</th>
							<th nowrap>单证类型</th>
							<th nowrap>打印次数</th>
							<th nowrap>打印人</th>
							<th nowrap>打印日期</th>
							<th nowrap>是否已短信通知</th>
							<th nowrap>是否邮件通知</th>
						</tr>
					</thead>
					<tbody>
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="11">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="11">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.PageItems" var="claimCaseVO"
							status="st">
							<tr>
								<td>${st.index+1}<input type="checkbox" flag="flagprint"
									id="checklistItem" name="claimCaseVOs[${st.index }].checkPrint"  onclick="showPreviewButton()">
									<input type="hidden" name="claimCaseVOs[${st.index }].caseId"
									value="${caseId}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].organCode"
									value="${organCode}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].policyCodeStrs"
									value="${policyCodeStrs}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].accReason"
									value="${accReason}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].documentNo"
									value="${documentNo}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].documentTName"
									value="${documentTName}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].docListId"
									value="${docListId}"> <input type="hidden"
									name="claimCaseVOs[${st.index }].insuredId"
									value="${insuredId}"> <input type="hidden"
									name="isPrint">
								</td>
								<td><input type="hidden" id="caseeNo"
									name="claimCaseVOs[${st.index }].caseNo" value="${caseNo}">${caseNo}</td>
								<td><input type="hidden"
									name="claimCaseVOs[${st.index }].caseStatus"
									value="${caseStatus}">${caseStatusName}</td>
								<td><input type="hidden"
									name="claimCaseVOs[${st.index }].insuredName"
									value="${insuredName}">${insuredName}</td>
								<td><input type="hidden"
									name="claimCaseVOs[${st.index }].insuredCode"
									value="${insuredCode}">${insuredCode}</td>
								<td><input type="hidden"
									name="claimCaseVOs[${st.index }].claimDateAll"
									value="${claimDateAll}">${claimDateAll}</td>
								<s:if test="#claimCaseVO.documentType == 'CLM_00003'">
									<td><input type="hidden" id="documentType"
									name="claimCaseVOs[${st.index }].documentType"
									value="${documentType}">${documentType}_不予立案通知书</td>
								</s:if>
								<s:elseif test="#claimCaseVO.documentType == 'CLM_00011'">
									<td><input type="hidden" id="documentType"
									name="claimCaseVOs[${st.index }].documentType"
									value="${documentType}">${documentType}_理赔照会结论通知书</td>
								</s:elseif>
								<s:elseif test="#claimCaseVO.documentType == 'CLM_00020'">
									<td><input type="hidden" id="documentType"
									name="claimCaseVOs[${st.index }].documentType"
									value="${documentType}">${documentType}_续保核保结论通知书</td>
								</s:elseif>
								<s:else>
									<td><input type="hidden" id="documentType"
										name="claimCaseVOs[${st.index }].documentType"
										value="${documentType}">${documentType}_${documentName}</td>
								</s:else>
								<td><input type="hidden"
									name="claimCaseVOs[${st.index }].rePrintTimes"
									value="${rePrintTimes}">${rePrintTimes}</td>
								<td>							
                                    <a style="font-size: 10px; text-decoration: underline" id="queryClaimDocPrintLog"
										href="clm/invoice/queryClaimDocPrintLog_CLM_claimInvoicesPrintAction.action?claimCaseVO.documentType=${documentType}" 
										target="dialog" rel="page2" name="claimCaseVOs[${st.index }].printBy" value="${printBy}">${printByName}</a>
								</td>
								<td><s:date name='printTime' format='yyyy-MM-dd' /></td>
								<td>
									<input type="hidden"
										name="claimCaseVOs[${st.index }].isSelected" value="${isSelected}">${isSelected}
								</td>
								<td>
									<input type="hidden"
										name="claimCaseVOs[${st.index }].isInform" value="${isInform}">${isInform}
								</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
				<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select panelBarValue="${currentPage.total}"  panelBarFlag='panelBarFlag' list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
							id="select" name="select"
							onchange="seleClaimCase();navTabPageBreak({numPerPage:this.value});"
							value="currentPage.pageSize">
						</s:select>
						<span>条，共${currentPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>
				</div>
			</div>
	
	<div class="panelPageFormContent main_tabdiv">
		<dl>
			<dt>
				<font>* </font>操作人员
			</dt>
			<dd>
				<input type="text" name="userName" readonly="readonly"
					value="${userName}" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>打印日期
			</dt>
			<dd>
				<input type="text" name="todayDate" id="todayDate"
					value="<s:date name='todayDate' format='yyyy-MM-dd'/>"
					readonly="readonly" />
			</dd>
		</dl>
	</div>

	<div id="claimDocSendLog">
	<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">短信发送信息：</h1>
	</div>
		<div class="tabdivclassbr tableScope">
				<table class="list main_dbottom" width="100%" id="table_sendNote">
					<thead>
						<tr>
							<th nowrap>短信接收人</th>
							<th nowrap>手机号码</th>
							<th nowrap>发送日期</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="claimDocSendLogVOList" var="status"
							status="var">
							<tr>		
									<td><s:property value="sendName"></s:property></td>
									<td><s:property value="sendPhone"></s:property></td>
									<td><s:date name="sendTime" format="yyyy-MM-dd HH:mm" /></td>								
							</tr>
						</s:iterator>
					</tbody>
				</table>
		</div>
	</div>
	
	<div id="claimDocSendMail">
	<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">邮件发送信息：</h1>
	</div>
		<div class="tabdivclassbr tableScope">
				<table class="list main_dbottom" width="100%" id="table_sendMail">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>接收人姓名</th>
							<th nowrap>收件邮箱</th>
							<th nowrap>发送日期</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="claimDocSendMailVOList" var="status"
							status="var">
							<tr>	
									<td>${var.index+1}</td>	
									<td><s:property value="accepterName"></s:property></td>
									<td><s:property value="receiveMail"></s:property></td>
									<td><s:date name="sendDate" format="yyyy-MM-dd HH:mm" /></td>								
							</tr>
						</s:iterator>
					</tbody>
				</table>
		</div>
	</div>

	<div class="formBarButton">
		<ul>
		    <li>
				<button type="button" class="but_blue" onclick="printDocuments(2)" style="display:none" id="prewButton">预览</button>   
			</li>
			<li>
				<button type="button" class="but_blue" onclick="printDocuments(1)">打印</button>   
			</li>
			<li>
				<button type="button" class="but_gray" onclick="exit()">退出</button>   
			</li>
		</ul>
	</div>

	<div id="printShow" style="display: none; width: 0px; height: 0px"></div>
</div>

</form>

