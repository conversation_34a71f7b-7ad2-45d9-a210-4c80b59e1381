function invoicesScanSubmit(){
	$("#isDocProve",navTab.getCurrentPanel()).setMyComboxDisabled(false);
	 $.ajax({
			'type':'post',
			'url':'clm/invoice/invoicesScanStart_CLM_claimInvoicesScanAction.action',
			'data':$("#invoicesScan",navTab.getCurrentPanel()).serialize(),
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode==300){
					alertMsg.error(data.message);
				}
				if(data.statusCode==200){
					window.open(data.message);
				}
			//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//				$("#checklistScan", navTab.getCurrentPanel()).click();
			},
			'error':function(){
				alertMsg.error("系统出险异常，请联系管理员");
			}
	});  
		$("#isDocProve",navTab.getCurrentPanel()).setMyComboxDisabled(true);

}
/*function closeScan(){
	alertMsg.confirm("当前页面录入的信息将不被保存,是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
 }*/
function controlSendTime(obj){
	var thisVal = $(obj).val();
	if(thisVal!=1){
		$("#rejectDocSendTime", navTab.getCurrentPanel()).css("display","none"); 
		$("#saveNews", navTab.getCurrentPanel()).attr('disabled',true);
		//是否理赔决定通知书类单证选择否时，扫描按钮可用
		$("#scanButton", navTab.getCurrentPanel()).attr('disabled',false);
		$("#isDocReject", navTab.getCurrentPanel()).val("0");
		$("#docRejectJudge", navTab.getCurrentPanel()).val("0");
	}else{
		$("#rejectDocSendTime", navTab.getCurrentPanel()).css("display",""); 
		$("#saveNews", navTab.getCurrentPanel()).attr('disabled',false);
		$("#scanButton", navTab.getCurrentPanel()).attr('disabled',true);
		$("#docRejectJudge", navTab.getCurrentPanel()).val("1");
	}
}

//判断赔案号最新一次的实名查验是否通过
function caseNoIsRealPass(obj){
	var caseNo = obj.value;
	if(caseNo !=null && caseNo != ""){
		$.ajax({
			'url':'clm/common/queryRealName_CLM_commonQueryAction.action?claimCaseVO.caseNo='+caseNo,
			'type':'post',
			'datatype':'json',
			'success':function(data){
				var data = eval("("+data+")");
				if(data.result != '0'){
					//如果未通过 是否实名制查验证明类单证 为可编辑状态
					$("#isDocProve",navTab.getCurrentPanel()).setMyComboxDisabled(false);
				}else{
					$("#isDocProve",navTab.getCurrentPanel()).setMyComboxDisabled(true);
				}
				if($("#isDocProve").prop("disabled") == true){
					$("a[name='isDocProve']",navTab.getCurrentPanel()).text("否");
					$("[name='isDocProve']",navTab.getCurrentPanel()).val("0");
				}
			},
			 'error':function(){
					alert("出错了！");
				} 
		});
	}
}

function saveMess(){
    var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
    var sendTime = $("#inspectionRegisterTime", navTab.getCurrentPanel()).val();
    if(caseNo == null || caseNo==""){
    	alertMsg.error("请填写赔案号");
    	return false;
    }
    //校验案件状态
    $.ajax({                      
			type:'post',   
			url:'clm/invoice/checkCaseStatus_CLM_claimInvoicesScanAction.action?claimChecklistVO.caseNo='+caseNo+'&claimChecklistVO.rejectDocSendTime='+sendTime,
			datatype:'json',
			success:function(data){
				var json = eval("("+data+")");
				if(json.statusCode == '300') {//寄送日期超过3天
					if(json.message =="寄送日期已超过结案后3日，请确认选择的寄送日期是否正确。"){
						alertMsg.confirm("寄送日期已超过结案后3日，请确认选择的寄送日期是否正确。",{
							okCall:function(){
								$.ajax({
									url : 'clm/invoice/checkSaveRecord_CLM_claimInvoicesScanAction.action?claimSendNoticeVO.caseNo='+caseNo,
									type : 'post',  
									datatype : 'json',
									success : function (data){
										var json = eval("("+data+")");
										if (json.statusCode == '300') {//之前有过扫描记录
											alertMsg.confirm("既往已扫描过理赔决定通知书（或不予立案通知书），请确认是否继续扫描？",{
												okCall:function(){
													alertMsg.confirm("请再次确认【理赔决定通知书（或不予立案通知书）寄送日期】是否录入准确？",{  
													okCall:function(){
														$.ajax({
															url : 'clm/invoice/saveNewRecord_CLM_claimInvoicesScanAction.action?claimSendNoticeVO.caseNo='+caseNo+'&claimSendNoticeVO.sendingDate='+sendTime,
															type : 'post',
															datatype : 'json',
															success : function (data){
																var json = eval("("+data+")");
																if (json.statusCode == '200') {
																	alertMsg.correct("保存成功");
																	$("#isDocReject", navTab.getCurrentPanel()).val("1");
																	$("#scanButton", navTab.getCurrentPanel()).attr('disabled',false);
																	$("#exitJudge", navTab.getCurrentPanel()).val("1");
																}
															},
															error:function(){
																alert("出错了！");
															}
														});
													  } 
												  });
												}
											});
											
										}else{//之前没有扫描记录
											alertMsg.confirm("请再次确认【理赔决定通知书（或不予立案通知书）寄送日期】是否录入准确？",{  
												okCall:function(){
													$.ajax({
														url : 'clm/invoice/saveNewRecord_CLM_claimInvoicesScanAction.action?claimSendNoticeVO.caseNo='+caseNo+'&claimSendNoticeVO.sendingDate='+sendTime,
														type : 'post',
														datatype : 'json',
														success : function (data){
															var json = eval("("+data+")");
															if (json.statusCode == '200') {
																alertMsg.correct("保存成功");
																$("#isDocReject", navTab.getCurrentPanel()).val("1");
																$("#scanButton", navTab.getCurrentPanel()).attr('disabled',false);
																$("#exitJudge", navTab.getCurrentPanel()).val("1");
															}
														},
														error:function(){
															alert("出错了！");
														}
													});
												  } 
											  });
										}
									},
									error:function(){
										alert("出错了！");
									}
								});
								
								
							}
						});
					}else{
						alertMsg.error(json.message);
					}
			    }else {//寄送日期校验没有超过3天，继续进行下面的校验
			    	$.ajax({
						url : 'clm/invoice/checkSaveRecord_CLM_claimInvoicesScanAction.action?claimSendNoticeVO.caseNo='+caseNo,
						type : 'post',  
						datatype : 'json',
						success : function (data){
							var json = eval("("+data+")");
							if (json.statusCode == '300') {//之前有过扫描记录
								alertMsg.confirm("既往已扫描过理赔决定通知书（或不予立案通知书），请确认是否继续扫描？",{
									okCall:function(){
										alertMsg.confirm("请再次确认【理赔决定通知书（或不予立案通知书）寄送日期】是否录入准确？",{  
										okCall:function(){
											$.ajax({
												url : 'clm/invoice/saveNewRecord_CLM_claimInvoicesScanAction.action?claimSendNoticeVO.caseNo='+caseNo+'&claimSendNoticeVO.sendingDate='+sendTime,
												type : 'post',
												datatype : 'json',
												success : function (data){
													var json = eval("("+data+")");
													if (json.statusCode == '200') {
														alertMsg.correct("保存成功");
														$("#isDocReject", navTab.getCurrentPanel()).val("1");
														$("#scanButton", navTab.getCurrentPanel()).attr('disabled',false);
														$("#exitJudge", navTab.getCurrentPanel()).val("1");
													}
												},
												error:function(){
													alert("出错了！");
												}
											});
										  } 
									  });
									}
								});
								
							}else{//之前没有扫描记录
								alertMsg.confirm("请再次确认【理赔决定通知书（或不予立案通知书）寄送日期】是否录入准确？",{  
									okCall:function(){
										$.ajax({
											url : 'clm/invoice/saveNewRecord_CLM_claimInvoicesScanAction.action?claimSendNoticeVO.caseNo='+caseNo+'&claimSendNoticeVO.sendingDate='+sendTime,
											type : 'post',
											datatype : 'json',
											success : function (data){
												var json = eval("("+data+")");
												if (json.statusCode == '200') {
													alertMsg.correct("保存成功");
													$("#isDocReject", navTab.getCurrentPanel()).val("1");
													$("#scanButton", navTab.getCurrentPanel()).attr('disabled',false);
													$("#exitJudge", navTab.getCurrentPanel()).val("1");
												}
											},
											error:function(){
												alert("出错了！");
											}
										});
									  } 
								  });
							}
						},
						error:function(){
							alert("出错了！");
						}
					});
			    }
			},
			error:function(){
				alert("出错了！");
			}
	});
}
//退出
function exit1(){ 
	var docRejectJudge = $("#docRejectJudge", navTab.getCurrentPanel()).val();
	var exitJudge = $("#exitJudge", navTab.getCurrentPanel()).val();
	if(docRejectJudge !="" &&  docRejectJudge=="1"){
		if(exitJudge !="" && exitJudge=="1"){
			navTab.closeCurrentTab();
		}else{
			alertMsg.confirm("寄送日期未保存，请确认是否退出！",{
			 	okCall:function(){
			 		navTab.closeCurrentTab();
			 	}
			});
		}
	}else{
		navTab.closeCurrentTab();
	}
}