<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>

<script type="text/javascript">
	function resetAll() {
		$('#branchCodePhone', navTab.getCurrentPanel()).attr('value', '');
		$('#branchnamePhone', navTab.getCurrentPanel()).attr('value', '');
		
	}
	function query(obj){
		$(obj).find("td:eq(0)").find("input").attr("checked","checked");
	}
</script>
<form id="pagerForm" method="post"
	action="clm/parameter/findClaimPhone_CLM_maintainPhoneAction.action?leftFlag=0&menuId=${menuId }">
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
</form>
<div layouth="36">
	<form action="clm/parameter/findClaimPhone_CLM_maintainPhoneAction.action?leftFlag=0&menuId=${menuId }" method="post" name="form1" id="form1"
		class="pagerForm required-validate"
		onsubmit="return navTabSearch(this);" rel="pagerForm">
		 <div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
		</div>
<div class="pageFormInfoContent">
		<dl>
			<dt>机构代码</dt>
			<dd>
				<dd><input size="8" style="width: 30px;border-right:0px" id="branchCodePhone" name="claimPhoneServiceVO.organCode" value="${claimPhoneServiceVO.organCode}" type="text" class="organ" clickId="branchCodePhoneBtn" showOrgName="branchnamePhone" data-grade-in="01,02,03" needAll="true"/>
					<input  id="branchnamePhone" size="15" name="claimPhoneServiceVO.orgName" value="${claimPhoneServiceVO.orgName}" style="width:110px;" type="text" readOnly/>
 					<a id="branchCodePhoneBtn" class="btnLook" href="#" style="position: relative;"></a>
			</dd>
		</dl>
		<div class="pageFormdiv">
			 <button  class="but_blue" type="submit" id="userQuery">查询</button>
		</div>
	</div>
	</form>

	<div class="divfclass">
		 <h1><img src="clm/images/tubiao.png">查询结果</h1>
	 </div>
	 <div class="tabdivclassbr">
	<div class="panelBar">
		<ul class="toolBar">
			<li><a class="add" href="clm/parameter/addClaimPhone_CLM_maintainPhoneAction.action?addUserFlag=add&menuId=${menuId }" maxable="false" minable="false" 
			resizable="false" title="服务电话新增" width="600" height="280" target="dialog" rel="addClaimPhone"><span>新增</span></a></li>
			<li class="line">line</li>
			<li><a class="edit" href="clm/parameter/updateClaimPhone_CLM_maintainPhoneAction.action?claimPhoneServiceVO.phoneServiceId={listIdInfo}&updateFlag=update&menuId=${menuId }" 
				maxable="false" resizable="false" minable="false" title="修改服务电话" width="600" height="280" rel="updateClaimPhone" 
				target="dialog"><span>修改</span></a></li>
			<li class="line">line</li>
			<li><a class="delete" href="clm/parameter/deleteClaimPhone_CLM_maintainPhoneAction.action?claimPhoneServiceVO.phoneServiceId={listIdInfo}&menuId=${menuId }" target="ajaxTodo"  title="是否确认要删除" id="phoneDelete"><span>删除</span></a></li>
			<%--<li class="line">line</li>
			 <li><a class="edit" href="clm/audit/distributenegotiateinit_CLM_claimTreatyTalkAction.action" 
				maxable="false" resizable="false" minable="false" title="分配协谈任务" width="800" height="350" rel="updateClaimPhone" 
				target="dialog"><span>协谈</span></a></li>
			<li class="line">line</li>
			<li><a class="edit" href="clm/audit/updatenegotiateresultinit_CLM_claimTreatyTalkAction.action" 
				maxable="false" resizable="false" minable="false" title="分配协谈任务" width="800" height="350" rel="updateClaimPhone" 
				target="dialog"><span>更新协谈结论</span></a></li> --%>
		</ul>
	</div>

<div class="main_border">
	<table class="list main_all" width="100%">
		<thead>
			<tr >
				<th nowrap>选择</th>
				<th nowrap>序号</th>
				<th nowrap>服务机构代码</th>
				<th nowrap>机构名称</th>
				<th nowrap>服务电话</th>
				<th nowrap>EMAIL</th>
			</tr>
		</thead>
		<tbody>
		<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
			<s:iterator value="currentPage.pageItems"  status="st"  >
			<tr onclick="query(this);" align="center"  target="listIdInfo" rel="${phoneServiceId}">
				<td><input type="radio" name="r"></td>
				<td> ${st.index+1}</td>
				<td> ${organCode}</td>
				<td>${orgName}</td>
				<td>${servicePhone}</td>
				<s:if test="email==null">	
				<td>${email}</td>
				</s:if>
				<s:else>
				<td>${email}</td>
				</s:else>
				
			</tr>
			</s:iterator>
		</tbody>
	</table>
	</div>
	<div class="panelBar" >
		<div class="pages">
			<span>显示</span>
			<s:select   list="#{20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
	    		</s:select>
			<span>条，共${currentPage.total}条</span>		
		</div>
		<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="20"
						currentPage="${currentPage.pageNo}"></div>

	</div>
	</div>
<div class="formBarButton">
	<ul>
		<li>
			 <button class="but_blue"  type="button" onclick="resetAll();" id="userQueryReset">重置</button>
		</li>
		<li>
			 <button class="but_gray" type="button" onclick="exit();" id="userQueryReset">退出</button>
		</li>
	</ul>
</div>	
</div>
<script type="text/javascript">
/* function exit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 okCall:function(){
			navTab.closeCurrentTab();
			$(".navTab-tab .main").click();
		 }
	 });
} */
</script>