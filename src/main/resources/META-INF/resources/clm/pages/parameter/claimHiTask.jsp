<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script>
/* function queryProcessHITask() {
	$("#formquery", navTab.getCurrentPanel()).submit();
} */

function queryHITask(obj){
	if($(obj).is(":checked")){
		$('input[name = "taskmanage"]').each(function(){
			$(this).prop("checked",true);
		});
	}else{
		$('input[name = "taskmanage"]').each(function(){
			$(this).prop("checked",false);
		});
	}
}


function findSurvey(flag){
	if(flag==0){
		$("#hiTask" , navTab.getCurrentPanel()).attr("href","clm/parameter/inform_bfSurveyHITaskManageAction.action?flag=2");
		$("#hiTask" , navTab.getCurrentPanel()).click();
	}
}

//
function queryProcessHITask(){
	//获取当前页面的用户列表
	var trs = $("#taskListBody", $.pdialog.getCurrent()).find("tr");
	var listIdList = "";
	for(var i = 0; i < trs.length; i++){
		if($("#taskListBody", $.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("input").prop('checked')){
			//获取当前勾选的用户id，并与抽取检查任务页面检查人列表中的用户id比较，若已存在则本次不添加
			var listId = $("#taskListBody", $.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("input").val();
					listIdList = listIdList + listId +",";
		}
	}
	if(listIdList == null || listIdList == ""){
		alertMsg.info("请至少选择一条记录。");
		return;
	}
	
	var r = confirm('请确认是否要关闭任务？');
	if(r==true){$("#formquery", navTab.getCurrentPanel()).attr("action","clm/parameter/closeHITask_CLM_bfSurveyHITaskManageAction.action?listIdList="+listIdList);
	$("#formquery", navTab.getCurrentPanel()).submit();}
		
		/* $.ajax({
			'url':"clm/parameter/closeHITask_CLM_bfSurveyHITaskManageAction.action?listIdList="+listIdList,
			'type':'post',
			'datatype':'json',
			'success':function(data){
				
			 }
		}); */
}

//待处理页面 跳转页面
function backInform(id,code,obj){
/* 	$("#formquery", navTab.getCurrentPanel()).attr("action",'clm/parameter/claimHiTaskinform_CLM_bfSurveyHITaskManageAction.action?listId='+id+'&surveyApplyVO.policyCode='+code);
	$("#formquery", navTab.getCurrentPanel()).submit(); */
	navTab.openTab("inform", "clm/parameter/claimHiTaskinform_CLM_bfSurveyHITaskManageAction.action?listId="+id+"&surveyApplyVO.policyCode="+code, {title:'增补告知'});
}

</script>
<div>

<form id="pagerForm" method="post"
		action="clm/parameter/queryProcessHITask_CLM_bfSurveyHITaskManageAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	
 		<form id="formquery"
		action="clm/parameter/queryProcessHITask_CLM_bfSurveyHITaskManageAction.action"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm" name=fm>
		</form>	 
<div layoutH="30" style="background: #fafafa">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询结果
		</h1>
	</div>
	<div class="tabdivclass">
		<div style="overflow: auto;">
			<table class="list" width="100%">
				<thead align="center">
					<tr>
						<th nowrap>选择</th>
						<th nowrap>序号</th>
						<th nowrap>保全申请号</th>
						<th nowrap>保全受理号</th>
						<th nowrap>保单号</th>
						<th nowrap>保全项目</th>
						<th nowrap>姓名</th>
						<th nowrap>申请提交日期</th>
						<th nowrap>失败退回原因</th>
						<th nowrap>任务状态</th>
					</tr>
				</thead>
				<tbody id="taskListBody" align="center">
					<s:elseif
						test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
						<tr>
							<td colspan="100">
								<div class="noRueryResult">没有符合条件的查询结果！</div>
							</td>
						</tr>
					</s:elseif>
					<s:iterator value="currentPage.pageItems" status="st">
						<tr  ondblclick="backInform('${listId}','${policyCode}',this)" align="center">
						<%-- <tr align="center" target="listIdInfo" rel="${listId}"> --%>
							<td>
								<input id="hiTask" type="checkbox" class="radioIndex" name="taskVO" 
								value="${listId}"/>
							</td> 
							<td><div align="center" class="index">${st.index+1}</div></td>
							<td align="center" style="word-break: break-all;">${applyCode}</td>
							<td align="center" style="word-break: break-all;">${acceptCode}</td>
							<td align="center" style="word-break: break-all;">${policyCode}</td>
							<td align="center" style="word-break: break-all;">增补告知</td>
							<td align="center" style="word-break: break-all;">${acceptorName}</td>
							<td align="center" style="word-break: break-all;">${applyConfirmTimeStr}</td>
							<td align="center" style="word-break: break-all;">${backReason}</td>
							<td align="center" style="word-break: break-all;">${taskStatusStr}</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			<!-- 分页查询区域 -->
			<!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{1:'1',5:'5',10:'10',20:'20',50:'50',100:'100'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab" totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10" 
					currentPage="${currentPage.pageNo}">
				</div>
			</div>
		</div>
	</div>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button id="closeHITask" target=”selectedTodo” postType=”String” class="but_blue" type="submit" onclick="queryProcessHITask()">关闭</button>
			</li>
			<li>
			<button class="but_gray" type="button" id="exitbtn" onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
</div>
<!-- </form> -->
</div>



