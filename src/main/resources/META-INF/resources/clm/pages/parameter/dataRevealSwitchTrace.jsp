<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/positiveResultRedSetInit.js"></script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">
</script>
<!-- 分页表单 -->

<div layoutH="20" style="background: #fafafa" id="allForce">
    <form id="pagerForm" method="post"
		action="clm/parameter/dataRevealSwitchTrace_CLM_ciitcLinkSwitchSetAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}"/>
	</form>
	<form id="messageAlertJspForm"
		action="clm/parameter/dataRevealSwitchTrace_CLM_ciitcLinkSwitchSetAction.action"
		method="post" onsubmit="return dialogSearch(this)"
		class="pageForm required-validate" rel="pagerForm">
	
     <div class="divfclass">
	    <h1>
		   <img src="clm/images/tubiao.png">数据展示开关配置轨迹
		</h1>
     </div>
	 <div class="tabdivclassbr">
		   <table id="news" class="list" style="width: 100%;">
			  <thead>
				<tr>
					<th nowrap>序号</th>
					<th nowrap>自核案件</th>
					<th nowrap>单人审核案件</th>
					<th nowrap>双人审核审批案件</th>
					<th nowrap>提交人</th>
					<th nowrap>提交日期</th>
				</tr>
			</thead>
			<tbody id="jobManagerUserMsg">
			  <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>  
				<s:iterator value="currentPage.pageItems" var="status" status="st">
					<tr>
						<td align="center">${st.index+1}</td>
						<td align="center">
						   <s:if test="selfAuditFlag==1">显示</s:if>
						   <s:else>不显示</s:else>
                        </td>
                        <td align="center">
						   <s:if test="singleAuditFlag==1">显示</s:if>
						   <s:else>不显示</s:else>
                        </td>
                        <td align="center">
						   <s:if test="doubleAuditApproveFlag==1">显示</s:if>
						   <s:else>不显示</s:else>
                        </td>
						<td align="center">${userRealName}</td>
		   			    <td align="center"><s:date name="submitDate" format="yyyy-MM-dd"/></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<!-- 分页查询区域 -->
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="dialogPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="dialog"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		 </div>
	   </div>
	</form>   
</div>