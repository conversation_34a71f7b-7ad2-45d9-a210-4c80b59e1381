<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*" %>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx"><%=request.getContextPath()%></s:set>
<link href="${ctx}/clm/css/main.css" rel="stylesheet" type="text/css" media="screen" />

<script type="text/javascript">
function exitPage(){
	navTab.closeCurrentTab();
}
//取消按钮提示
function exitAddPage() {
		navTab.closeCurrentTab();
	}

function gainOrganCodeArea(){
	 var trs = $("#accOrganCodeAreaId", $.pdialog.getCurrent()).find("input:checked").val();
	 if(trs!=null&&trs!=""){
		 var ss = trs.split("|");
		 javascript:$.bringBack({
			 		organCodeArea:ss[0],
			 		organCodeStry:ss[1],
			 		organCodeGroup:ss[2]
				});
	 }else{
		 alertMsg.info("请选择一条记录");		 
	 }
}

function checkTaskDisposal() {
	var organCode = $("#taskDisposalForm",navTab.getCurrentPanel()).find("#organCode").val();
	var surveyStatus = $("#taskDisposalForm",navTab.getCurrentPanel()).find("#surveyStatus").val(); 
	var startDate = $("#taskDisposalForm",navTab.getCurrentPanel()).find("#startDate").val();
	var endDate = $("#taskDisposalForm",navTab.getCurrentPanel()).find("#endDate").val();
	var positiveFlag = $("#taskDisposalForm",navTab.getCurrentPanel()).find("#positiveFlag").val();
	if(organCode.trim()=='' 
			|| surveyStatus.trim()==''
			|| startDate.trim()==''
			|| endDate.trim()==''
			){ 
		alertMsg.warn("机构,调查状态,调查发起起止期为必录条件。");
	}else if(endDate<startDate) {
			alertMsg.warn("发起调查的止期不可晚于起期。");
	}else{
		$("#taskDisposalForm", navTab.getCurrentPanel()).submit();
	}
	var A = positiveFlag;
	var B = surveyStatus;
	debugger;
	$("#positiveFlag", navTab.getCurrentPanel()).selectMyComBox(A);
    $("#surveyStatus", navTab.getCurrentPanel()).selectMyComBox(B);
}

</script>
<!-- 分页查询访问路径 -->
<div layoutH="0" >	
	<form id="pagerForm" method="post"
		action="clm/parameter/findTaskDisposal_CLM_bfSurveyHITaskManageAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>

	<!-- 查询事件访问路径 -->
	<form id="taskDisposalForm"
		action="clm/parameter/findTaskDisposal_CLM_bfSurveyHITaskManageAction.action"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm" name=fm>
		
		
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">保后调查任务处置
				</h1>
			</div>
			<div class="pageFormInfoContent">
				<dl>
					<dt><font>* </font>机构</dt>
					<dd>
						<input readonly = "true" style="width: 30px;border-right: 0;" id="organCode" name="surveyApplyVO.organCode" 
							type="text" class="organ" clickId="menuBtn" showOrgName="branchname" 
							needAll="true" size="8"
							value="<s:property value='surveyApplyVO.organCode'/>"/>
						<input style="width: 110px" id="branchname" type="text" readOnly size="15" 
							name="surveyApplyVO.branchname"
							value="<s:property value='surveyApplyVO.branchname'/>"/>
						<a id="menuBtn" class="btnLook" href="#"></a>
					</dd>
				</dl>
			<dl>
				<dt>阳性标识</dt>
				<dd>
					<select class="combox title" name="surveyApplyVO.positiveFlag" id="positiveFlag">
						<option <s:if test="surveyApplyVO.positiveFlag==''">selected</s:if> value=""  >请选择</option>
						<option <s:if test="surveyApplyVO.positiveFlag==0">selected</s:if> value="0"  >否</option>
						<option <s:if test="surveyApplyVO.positiveFlag==1">selected</s:if> value="1"  >是</option>
					</select>  
						<%-- <select id="positiveFlag" name="surveyApplyVO.positiveFlag"
						class="combox">
						<option value="">请选择</option>
					 	<option value="0">否</option>
						<option value="1">是</option>
					</select> --%>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>调查状态</dt>
				<dd>
						<select class="combox title" name="surveyApplyVO.surveyStatus" id="surveyStatus">
						<option <s:if test="surveyApplyVO.surveyStatus==''">selected</s:if> value=""  >请选择</option>
						<option <s:if test="surveyApplyVO.surveyStatus==0">selected</s:if> value="0"  >全部</option>
						<option <s:if test="surveyApplyVO.surveyStatus==1">selected</s:if> value="1"  >已申请</option>
						<option <s:if test="surveyApplyVO.surveyStatus==2">selected</s:if> value="2"  >已完成</option>
						<option <s:if test="surveyApplyVO.surveyStatus==3">selected</s:if> value="3"  >已撤销</option>
						<option <s:if test="surveyApplyVO.surveyStatus==4">selected</s:if> value="4"  >进行中</option>
						</select>  
					<%-- <select id="surveyStatus" name="surveyApplyVO.surveyStatus"
						class="combox ">
						<option value="">请选择</option>
					 	<option value="0">全部</option>
						<option value="1">已申请</option>
						<option value="2">已完成</option>
						<option value="3">已撤销</option>
						<option value="4">进行中</option>
					</select> --%>
				</dd>
			</dl>
			<dl>
					<dt><font>* </font>调查发起起期</dt>
						<dd>
						<input id="startDate" type="expandDateYMD" name="surveyApplyVO.startDate"
						class="date"
						value="<s:date name='surveyApplyVO.startDate' format='yyyy-MM-dd'/>" />
						<a class="inputDateButton" href="javascript:;">选择</a>
						</dd>
				</dl>
				<dl>
					<dt><font>* </font>调查发起止期</dt>
						<dd>
						<input id="endDate" type="expandDateYMD" name="surveyApplyVO.endDate" class="date"
						value="<s:date name='surveyApplyVO.endDate' format='yyyy-MM-dd'/>" />
						<a class="inputDateButton" href="javascript:;">选择</a>
						</dd>
				</dl>
				<div class="formBarButton">
					<ul>
						<li>
							<button onclick="checkTaskDisposal()" class="but_blue"  type="button">查询</button>
						</li>
					</ul>
				</div>
			</div>
	</form>
	
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询结果
		</h1>
	</div>
	<!-- 显示数据列表区域 -->
		<div id="findTaskDispo" class="tabdivclass" >
	<div>
			<table class="list" width="100%" >
				<thead  align="center">
					<tr>
						<th>序号</th>
						<th>机构</th>
						<th>保单号</th>
						<th>调查机构</th>
						<th>调查状态</th>
						<th>阳性标识</th>
						<th>发起调查日期</th>
						<th>调查详情</th>
						<th>增补告知</th>
					</tr>
				</thead>
				<tbody id="accOrganCodeAreaId"  align="center">
					<!-- 循环显示数据 -->
					<s:if test="imageFlag != null">
							<tr>
								<td colspan="8">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.pageItems == null || currentPage.pageItems.size()==0">
							<tr>
								<td colspan="8">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:else>
					<s:iterator value="currentPage.pageItems" status="status" var="plan">
						<tr height="25" target="rid" rel="${applyId}">
							<td><s:property value="#status.index + 1" /></td>
							<td>${organCode}</td>
							<td>${policyCode}</td>
							<td>${surveyOrg}</td>
							<td>${surveyStatusStr}</td>
							<s:if test="positiveFlagStr==0">
							<td>否</td>
							</s:if >
							<s:if test="positiveFlagStr==1">
							<td>是</td>
							</s:if >
							<s:if test="positiveFlagStr==null">
							<td> </td>
							</s:if >
 							<%-- <td>${positiveFlagStr}</td> --%>
							<td>${applyDateStr}</td>
							<td>
								<a href="clm/survey/querySurveyClm_CLM_surveyConclusionQueryAction.action?applyId=${applyId}" max='true' target="navTab" title="调查详情">调查详情</a>
							</td> 
							<s:if test="positiveFlagStr==0">
							<td>
							<a class="add" hidden="true" href="clm/parameter/inform_CLM_bfSurveyHITaskManageAction.action?applyId=${applyId}&surveyApplyVO.policyCode=${policyCode}" max="true" title="增补告知" target="navTab"><span>增补告知</span></a>
							</td>
							</s:if >
							<s:if test="positiveFlagStr==1">
							<td>
							<a class="add"  href="clm/parameter/inform_CLM_bfSurveyHITaskManageAction.action?applyId=${applyId}&surveyApplyVO.policyCode=${policyCode}" max="true" title="增补告知" target="navTab"><span>增补告知</span></a>
							</td>
							</s:if >
							
						</tr>
					</s:iterator>
					</s:else>
				</tbody>
			</table>
			<!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{1:'1',5:'5',10:'10',20:'20',50:'50',100:'100'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab" totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10" 
					currentPage="${currentPage.pageNo}">
				</div>
			</div>
		</div>
		<div class="formBarButton main_bottom">
			<ul>
				<li>
					<button class="but_gray" id="addSurveyPlan" type="button"
						onclick="exitAddPage()">退出</button>
				</li>
			</ul>
		</div>
</div>
</div>

