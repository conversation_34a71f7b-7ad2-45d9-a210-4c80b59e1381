<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript" src="clm/pages/parameter/deformityGradeInit.js" >
</script>

<script type="text/javascript"  >


/**
 * 普通ajax表单提交
 * 
 * @param {Object}
 *            form
 * @param {Object}
 *            callback
 * @param {String}
 *            confirmMsg 提示确认信息
 */
function myValidateCallback(form,confirmMsg) {
	  
	// ******* 超时监控 Add HYP
	if (JudgeTimeOut()) {
		DWZ.loadLogin();
		return false;
	}
	 
	// form 校验 若校验失败不允许提交
	var $form = $(form);
	
	
	
	 
	if (!$form.valid()) {
		$form.find(".error:eq(0)").focus();
		
		alertMsg.error("提交数据不完整，"+$form.find(".error").length+"个字段有错误，请改正后再提交!");
		
		 $(".error", navTab.getCurrentPanel()).find(".button").click(function(){
			 $form.find(".error:eq(0)").focus();   
		 }); 
		return false;
	}
	
	
	// ********添加自定义判断程序 add LiAnDong 
	var myOption = $form.attr("myOption");
	if (null != myOption && myOption != '') {
		var myFlag = eval(myOption);
		if (!myFlag)
			return myFlag;
	}
	
	var result = {data:$form.serializeArray()};
	var parent = $.pdialog.getCurrent() || navTab.getCurrentPanel();
	// *******交易管理监控 Add HYP
	if(dealSwitch){
		result = addHiddenAttrs(result, parent);
	}
	// 按钮添加审计功能
	if (btnSwitch) {
		result = addBtnHiddenAttrs(result, parent);
	}
	
	 
	
	/*start  在请求参数中获取页面token ADD BY tanzl*/
	var $page = $(form).parents(".dialog");
	if($page.length == 0){
		$page = $(form).parents(".unitBox");
	}
	var tokenval=$page.find("input[name='token']").val();
	var tokenKey=$page.find("input[name=jspname]").val();
	//表单提交获取token和校验token标志
	result.data.push({'name':'token','value':tokenval});
	result.data.push({'name':'tokenKey','value':tokenKey});
	result.data.push({'name':'checkTokenFlag','value':'1'});
	/*token end*/
	  
	var _submitFn = function() {
		$form.find(':focus').blur();
	 
		$.ajax({
			type : form.method || 'POST',
			url : $form.attr("action"),
			data : result.data,
			dataType : "json",
			cache : false,
			success : function(data){
				 //成功添加上 ID   
				 if(data!=null&&data.claimDeformityGradeVO!=null&&
						 data.claimDeformityGradeVO.deformityGradeId!=null&&data.claimDeformityGradeVO.deformityGradeId!=""){
					 $("#deformityGradeId", navTab.getCurrentPanel()).attr("value",data.claimDeformityGradeVO.deformityGradeId);
					 queryLev();
					 alertMsg.correct("保存成功");
				 }else{ 
					 alertMsg.info("保存失败");
				 };
				
			} ,
			error : DWZ.ajaxError
		});
	}
	if (confirmMsg) {
		alertMsg.confirm(confirmMsg, {
			okCall : _submitFn
		});
	} else {
		_submitFn();
	}
	return false;
}
 
  
	 $("#deformityGrade option", navTab.getCurrentPanel()).each(function(){
	 
		 if($(this).attr("value")!=""){
		 $(this).html($(this).attr("value")+"-"+$(this).attr("title"));
		 }
	 }) 
	 
	 
	 
	 function deformityRateValidate(obj){ 
		 obj.value=obj.value.replace(/[^0-9.]/g,'');
		 if(obj.value>=10){ 
			 obj.value="";
		 }
	 }
	     
	  
	 function queryLev(){
		 var deformityType = $("#deformityType",navTab.getCurrentPanel()).val();
		 var deformityGrade = $("#deformityGrade",navTab.getCurrentPanel()).val();
		 var deformityCode = $("#deformityCode",navTab.getCurrentPanel()).val().trim();
		 var deformityCode5Name = $("#deformityCode5Name",navTab.getCurrentPanel()).val();
		 if(deformityType==""&&deformityGrade==""&&deformityCode==""&&deformityCode5Name==""){
			 alertMsg.error("请至少输入一项查询条件!");
			 return false;
		 }
		 $("#claimDeformityGradeJspForm",navTab.getCurrentPanel()).submit();
	 }
	 
/* $("#deformityType1", navTab.getCurrentPanel())[0].fireEvent("onchange");	        
 */
 
 </script>

<div layoutH="0" id="claimDeformityGradeJsp">
	<form id="pagerForm" method="post"
		action="clm/parameter/findDeformityGrade_CLM_claimDeformityGradeAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<!-- 查询事件访问路径 -->
	<form id="claimDeformityGradeJspForm"
		action="clm/parameter/findDeformityGrade_CLM_claimDeformityGradeAction.action"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm">
		<!-- 查询区域 -->
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">伤残等级参数查询条件</h1>
		   </div>
			
			<div class="pageFormInfoContent">
				<dl>
					<dt>伤残类型</dt>
					<dd>
						<Field:codeTable cssClass="combox title"   id="deformityType2" value="${claimDeformityGradeVO.deformityType}"
								name="claimDeformityGradeVO.deformityType" 
								  tableName="APP___CLM__DBUSER.T_DEFORMITY_TYPE" onChange="deformityType2Change()"
								    whereClause="deformity_type in (0,1,2,3,4,5,6,7,8)"  
								nullOption="true"></Field:codeTable>
					</dd>
				</dl>
				<dl>
					<dt>伤残级别</dt>
					<dd>
						<Field:codeTable cssClass="combox title"  id="deformityGrade2" value="${claimDeformityGradeVO.deformityGrade}"
						       
								name="claimDeformityGradeVO.deformityGrade"
								  tableName="APP___CLM__DBUSER.T_DEFORMITY_GRADE"
								nullOption="true"></Field:codeTable>
					</dd>
				</dl>
				<dl>
					<dt>伤残代码</dt>
					<dd>
					<input type="text" id="deformityCode" value="${claimDeformityGradeVO.deformityCode}" name="claimDeformityGradeVO.deformityCode"
							size="25" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
					</dd>
				</dl>
				<dl>
					<dt>伤残代码名称</dt>
					<dd>
					<input type="text"   name="claimDeformityGradeVO.deformityCodeName"  value="${claimDeformityGradeVO.deformityCodeName}"
							size="25"  id="deformityCode5Name"/>
					</dd>
				</dl>
			<div class="pageFormdiv">
				<button type="button" class="but_blue"  type="button" onclick="queryLev()">查询</button>
				<button type="button" class="but_blue"  type="reset" onclick="addInfo()">新增</button>
		   </div>  
			</div>
			
		   
		<!-- 显示数据列表区域 -->
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">伤残等级参数查询列表</h1>
		   </div>
			<div class="tabdivclassbr">
				<table class="list main_dbottom" width="100%">
					<thead>
						<tr>
							<th nowrap>选择</th>
							<th nowrap>伤残类型</th>
							<th nowrap>伤残级别</th>
							<th nowrap>伤残级别名称</th>
							<th nowrap>伤残代码</th>
							<th nowrap>伤残代码名称</th>
							<th nowrap>ICF编码</th>
							<th nowrap>残疾给付比例</th>
							<th nowrap>有效标识</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody align="center">
						<!-- 循环显示数据 -->
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
							<s:iterator value="currentPage.pageItems" status="st">
								<tr>
									<td><input type="radio" name="r1"
										id='id<s:property value="#st.index" />'
										onclick="getSelectInfo(this)"
										value="${deformityType}|${deformityGrade}|${deformityGradeName}|${deformityCode}|${deformityCodeName}|${icfCode }|${deformityRate}|${isEffective}|${deformityGradeId}|<s:date name='startDate' format='yyyy-MM-dd' />|<s:date name='endDate' format='yyyy-MM-dd' />|${updateTime}" />
									</td>
									<td><div align="center">${deformityTypeName}</div></td>
									<td><div align="center">${deformityGrade}</div></td>
									<td><div align="center">${deformityGradeName}</div></td>
									<td><div align="center">${deformityCode}</div></td>
									<td><div align="center">${deformityCodeName}</div></td>
									<td><div align="center">${icfCode }</div></td>
									<td><div align="center">${deformityRate}</div></td>
									<td><s:if test="isEffective == 1 ">
									 有效
									</s:if>
									<s:else >
									 失效
									</s:else>
									 
									</td>
									<td><div align="center"><a class="btnDel" id='delButton' href='javascript:void(0);' onclick="del(this)">删除</a></div></td>
								</tr>
							</s:iterator>
					</tbody>
				</table>
				<!-- 分页查询区域 -->
				<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
							name="select" onchange="navTabPageBreak({numPerPage:this.value})"
							value="currentPage.pageSize">
						</s:select>
						<span>条，共${currentPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>
				</div>
			</div>
	</form>
	<form id="deformityGradeJspForm" 
		action="clm/pay/saveDeformityInfo_CLM_claimDeformityGradeAction.action"
		method="post" onsubmit="return myValidateCallback(this)"
		class="pageForm">
		<!-- 修改信息区域 -->
		<input id="deformityGradeId" name="claimDeformityGradeVO.deformityGradeId" type="hidden" value=""/>
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">伤残等级参数信息</h1>
		   </div>
			<div class="panelPageFormContent">
				<dl >
					<dt><font style="color: red;">* </font>伤残类型</dt>
					<dd>
						<Field:codeTable id="deformityType1" onChange="deformityType1Change()"
								name="claimDeformityGradeVO.deformityType"
								value="" tableName="APP___CLM__DBUSER.T_DEFORMITY_TYPE"  whereClause="deformity_type in (0,1,2,3,4,5,6,7,8)" 
								nullOption="true"  cssClass="notuseflagDelete combox title required" ></Field:codeTable>
					</dd>
				</dl>
				<dl >
					<dt>伤残级别</dt>
					<%-- <dd>
						<Field:codeTable cssClass="selectToInput"  id="deformityGrade1"
								name="claimDeformityGradeVO.deformityGrade"
								value="" tableName="APP___CLM__DBUSER.T_DEFORMITY_GRADE"
								nullOption="true"></Field:codeTable>
					</dd> --%>
					<dd>
						<select class="combox title"  onchange="defomityGradeChange(this)"  id="deformityGrade1" style="width:155px;" name="claimDeformityGradeVO.deformityGrade" value=""></select>
					</dd>
				</dl>
				<dl >
					<dt>伤残级别名称</dt>
					<dd>
					<input   type="text" id="deformityGradeName" name="claimDeformityGradeVO.deformityGradeName" readonly="true"
							value=""  />
					</dd>
				</dl>
				<dl >
					<dt><font style="color: red;">* </font>伤残代码</dt>
					<dd>
						<input type="text" id="deformityCode1" name="claimDeformityGradeVO.deformityCode"
							value=""  maxlength="30" />
					</dd>
				</dl>
				<dl >
					<dt>伤残代码名称</dt>
					<dd>
						<input  id="deformityCode1Name"
							name="claimDeformityGradeVO.deformityCodeName"
							value="" size="25" />
					</dd>
				</dl>
				<dl >
					<dt>ICF编码：</dt>
					<dd>
						<input type="text" id="icfCode"  onkeyup="this.value=this.value.replace(/[^0-9]/g,'');"
							name="claimDeformityGradeVO.icfCode" 
							value="" size="25" maxlength="10" />
					</dd>
				</dl>
				<dl >
					<dt><font style="color: red;">* </font>残疾给付比例</dt>
					<dd>
						<input type="text" id="deformityRate" onkeyup="deformityRateValidate(this)"
							name="claimDeformityGradeVO.deformityRate"
							value="" size="25" />
					</dd>
				</dl>
				<dl >
					<dt ><font style="color: red;">* </font>有效日期区间</dt>
					<dd style="width: 500px;">
						<input   type="expandDateYMD" id="startDate"
							name="claimDeformityGradeVO.startDate"
							value="" size="15" />
						<a class="inputDateButton" href="javascript:;">选择</a>
<!-- 					</dd> -->
					<span style="padding:0px 15px;">至</span>
<!-- 					<dd > -->
						<input   type="expandDateYMD" id="endDate"
							name="claimDeformityGradeVO.endDate"
							value="" size="15" />
						<a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl >
					<dt>有效标识</dt>
					<dd>
						<select class="combox title"    id="isEffective" name="claimDeformityGradeVO.isEffective" value="" >
							<option value="">------------请选择 -----------</option>
							<option value="1" selected="selected">有效</option>
							<option value="2">失效</option>
						</select>
					</dd>
				</dl>
				<dl >
					<dt>操作日期</dt>
					<dd>
						<input disabled="disabled"   type="expandDateYMD"   
							  value="<fmt:formatDate value='${claimDeformityGradeVO.updateTime}' pattern='yyyy-MM-dd'/> "
							value="" size="25" />
					    <input type="hidden" id="updateTime" name="claimDeformityGradeVO.updateTime" value="<fmt:formatDate value='${claimDeformityGradeVO.updateTime}'/>" />		
					</dd>
				</dl>
				
			</div>
			<div class="formBarButton">
			  	<ul>
					<li><button class="but_blue" type="button" onclick="saveDeformityInfo();">保存</button></li>
					<li><button class="but_gray" type="reset" onclick="reset3()" >重置</button></li>
					<li><button class="but_gray" type="button" onclick="exit()">退出</button></li>
				</ul> 
		   </div>
	  </form>
</div>


