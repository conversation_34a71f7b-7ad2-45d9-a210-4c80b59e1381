﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/claimRole.js"></script>
<style type="text/css">
    /* 删除按钮样式 */
	a.btnDelPrivate{
		display:block; width:22px; height:20px; 
		text-indent:-1000px; overflow:hidden; 
		float:left; margin-right: 3px}
		a.btnDelPrivate{
		background-position: -23px 0;
		background-repeat: no-repeat;
		background-color: transparent;
		background-image: url("clm/images/imgX.gif");
		}
		/* 删除按钮灰色样式 */
		a.grayBtnDelPrivate{ 
		display:block; width:22px; height:20px; 
		text-indent:-1000px; overflow:hidden; 
		float:left; margin-right: 3px}
		a.grayBtnDelPrivate{
		background-position: -23px 0;
		background-repeat: no-repeat;
		background-color: transparent;
		background-image: url("clm/images/imgGrayX.gif");
		}
</style>
	<form id="pagerForm" method="post"
		action="clm/parameter/queryClaimRoleServicing_CLM_claimRoleServicingAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
<div id="claimRoleResult">
<div layoutH="10"  >
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
	</div>
	<form id="pagerForm" method="post"
		action="clm/parameter/queryClaimRole_CLM_claimBlackNameAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<form id="claimRoleFormId" action="clm/parameter/queryClaimRoleServicing_CLM_claimRoleServicingAction.action" 
					method="post" class="required-validate" onsubmit="return navTabSearch(this,'claimRoleResult');" rel="claimRoleResult">
			<div class="pageFormInfoContent">
				<dl>
					<dt>角色</dt>
					<dd>
						<select class="combox" name="claimRoleServicingVO.claimRoleId" value="${claimRoleServicingVO.claimRoleId}">
							<option value="">请选择</option>
							<s:iterator  value="claimRoleServicingVOList">
								<option <s:if test="claimRoleServicingVO.claimRoleId  == claimRoleId">selected</s:if> value="${claimRoleId}">${claimRoleCode }-${claimRoleName }</option>
							</s:iterator>
						</select>
					</dd>
				</dl>
			<div class="pageFormdiv"><button class="but_blue" id="queryClaimRoleId" type="button" onclick="queryClaimRoleServicing();">查询</button></div>
		</div>
	</form>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">已配置角色
		</h1>
	</div>
	<div class="tabdivclassbr main_tabdiv">
		<table class="list sortable main_dbottom" width="100%">
			<thead>
				<tr>
					<th nowrap>选择</th>
					<th nowrap>序号</th>
					<th nowrap>角色代码</th>
					<th nowrap>角色名称</th>
					<th nowrap>接收任务开关</th>
					<th nowrap>角色权限（审核简易）</th>
					<th nowrap>角色权限（审核）</th>
					<th nowrap>角色权限（审批普通）</th>
					<th nowrap>角色权限（审批疑难）</th>
					<th nowrap>操作</th>
				</tr>
			</thead>
			<tbody id="claimRoleTbodyId">
				<s:if test="imageFlag != null">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif test="currentPage.pageItems == null || currentPage.pageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>
					<s:iterator value="currentPage.pageItems" status="st">
						<tr>
							<td><input type="radio" name="n1" onclick="queryClaimRoleDetails(this)"/><input type="hidden" value="${claimRoleId}"></td>
							<td>${st.index+1}</td>
							<td>${claimRoleCode}</td>
							<td>${claimRoleName}</td>
							<td><s:if test="isWorking == 1">接收</s:if><s:else>不接受</s:else></td>
							<td><s:if test="easyAuditPermission == 1">有</s:if><s:else>无</s:else></td>
							<td>
								<s:if test="auditFrom == 'CLM-MAX'">人员最高权限-</s:if><s:else>${auditFrom }-</s:else>
								<s:if test="auditTo == 'CLM-MAX'">人员最高权限</s:if><s:else>${auditTo }</s:else>
							</td>
							<td>
								<s:if test="approveFrom == 'CLM-MAX'">人员最高权限-</s:if><s:else>${approveFrom }-</s:else>
								<s:if test="approveTo == 'CLM-MAX'">人员最高权限</s:if><s:else>${approveTo }</s:else>
							</td>
							<td>
								<s:if test="approveDifficultFrom == 'CLM-MAX'">人员最高权限-</s:if><s:else>${approveDifficultFrom }-</s:else>
								<s:if test="approveDifficultTo == 'CLM-MAX'">人员最高权限</s:if><s:else>${approveDifficultTo }</s:else>
							</td>
							<td><a class="btnDelPrivate" href="javascript:void(0);" onclick="deleteClaimRole(${claimRoleId})">删除</a></td>
						</tr>
					</s:iterator>
			</tbody>
		</table>
		<!-- 分页查询区域 -->
		<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value},'claimRoleResult')"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab"  id="queryTotalPage" rel="claimRoleResult"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
	</div>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button type="button" class="but_blue" id="addClaimRoleInfoId"  onclick="addClaimRoleInfo()">新增</button>
			</li>
			<li>
				<button type="button" class="but_blue" disabled="disabled" id="saveClaimRoleInfoId"  onclick="saveClaimRoleInfo()">保存</button>
			</li>
		</ul>
	</div>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">角色设置
		</h1>
	</div>
	<form id="saveForm" action="clm/parameter/saveClaimRole_CLM_claimRoleServicingAction.action" class="required-validate" 
		method="post" onsubmit="return validateCallback(this,claimRoleCallBack)">
		<div class="panelPageFormContent main_tabdiv">
			<dl>
				<dt>角色代码<font color="red">*</font></dt>
				<dd>
					<input id="claimRoleId" type="hidden" name="claimRoleServicingVO.claimRoleId"/>
					<input id="claimRoleCode" type="text" name="claimRoleServicingVO.claimRoleCode"/>
				</dd>
			</dl>
			<dl>
				<dt>角色名称<font color="red">*</font></dt>
				<dd>
					<input id="claimRoleName"  type="text" name="claimRoleServicingVO.claimRoleName" />
				</dd>
			</dl>
			<dl>
				<dt>接收任务状态<font color="red">*</font></dt>
				<dd>
					<select class="combox" name="claimRoleServicingVO.isWorking" id="isWorking">
						<option value="1">接收</option>
						<option value="0">不接受</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>简易案件审核权限<font color="red">*</font></dt>
				<dd>
					<select class="combox" name="claimRoleServicingVO.easyAuditPermission" id="easyAuditPermission">
						<option value="1">有</option>
						<option value="0">无</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>分配案件权限（审核）</dt>
				<dd style="width: 150px">
					<select class="combox title" name="claimRoleServicingVO.auditFrom" onchange="maxPermission(this)" id="auditIdStart">
						<option value="">请选择</option>
					</select> 
				</dd>
				<dd style="width: 30px;">
						<span style="padding-left: 10px">至</span> 
				</dd>
				<dd>
					
					<select class="combox title" name="claimRoleServicingVO.auditTo" id="auditIdEnd">
						<option value="">请选择</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>分配案件权限（审批普通）</dt>
				<dd style="width: 150px">
					<select name="claimRoleServicingVO.approveFrom" onchange="maxPermission(this)" class="combox title" id="approveIdStart">
						<option value="">请选择</option>
					</select>
				</dd> 
				<dd style="width: 30px;">	
						<span style="padding-left: 10px">至</span> 
				</dd>
				<dd>
					<select class="combox title" name="claimRoleServicingVO.approveTo" id="approveIdEnd">
						<option value="">请选择</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>分配案件权限（审批疑难）</dt>
				<dd style="width: 150px">
					<select name="claimRoleServicingVO.approveDifficultFrom" onchange="maxPermission(this)" class="combox title" id="approveDifficultStart">
						<option value="">请选择</option>
					</select>
				</dd> 
				<dd style="width: 30px;">	
						<span style="padding-left: 10px">至</span> 
				</dd>
				<dd>
					<select class="combox title" name="claimRoleServicingVO.approveDifficultTo" id="approveDifficultEnd">
						<option value="">请选择</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>可处理的理赔类型</dt>
				<dd id="claimTypeId" style="width: 900px;height: auto;">
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList" value="01">身故
					</span>
					<span>
						 <input type="checkbox" name="claimRoleServicingVO.claimTypeList" value="04">高残
					</span>
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList"  value="03">重大疾病
					</span>
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList" value="02">伤残
					</span>
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList"  value="08">医疗
					</span>
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList"  value="10">特种疾病
					</span>
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList"  value="06">一般失能
					</span>
					<span>
						<input type="checkbox" name="claimRoleServicingVO.claimTypeList"  value="07">重度失能
					</span>
				</dd>
			</dl>
		</div>	
	</form>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button type="button" class="but_gray" onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
</div>
</div>