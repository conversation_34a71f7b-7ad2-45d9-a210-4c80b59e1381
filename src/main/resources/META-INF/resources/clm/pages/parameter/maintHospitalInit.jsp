<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>


		<script type="text/javascript">
	function resetAll() {
		$('#branchCodeHospital', navTab.getCurrentPanel()).attr('value', '');
		$('#branchnameHospital', navTab.getCurrentPanel()).attr('value', '');
		$('#hospitalCode', navTab.getCurrentPanel()).attr('value', '');
		$('#hospitalName', navTab.getCurrentPanel()).attr('value', '');
		selectMycomboxDD("hospitalLevel","0");
		selectMycomboxDD("isDesignated","1");
		selectMycomboxDD("hospitalStatus","1");
	}
	
	function queryHos(){
		var hospitalCode = $("#hospitalCode", navTab.getCurrentPanel()).val();
		var hospitalName = $("#hospitalName", navTab.getCurrentPanel()).val();
		var branchCode = $("#branchCodeHospital", navTab.getCurrentPanel()).val();
		if(hospitalCode=='' && hospitalName=='' && branchCode == ''){
			alertMsg.error("请至少录入医院代码、医院名称、管理机构中的一个查询条件!");
			return false;
		}
		$("#form1", navTab.getCurrentPanel()).submit();
	}
</script>
<form id="pagerForm" method="post"
	action="clm/parameter/findHospital_CLM_maintHospitalAction.action?leftFlag=0&menuId=${menuId }">
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
</form>

<div layoutH="0">
	<form action="clm/parameter/findHospital_CLM_maintHospitalAction.action?leftFlag=0&menuId=${menuId }" method="post" name="form1" id="form1"
		class="pageForm required-validate"
		onsubmit="return navTabSearch(this);" rel="pagerForm">
		
		  <div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
		   </div>
			<div class="pageFormInfoContent">
			<dl>
				<dt>医院代码</dt>
				<dd><input type="text" class="data" id="hospitalCode" name="claimHospitalServiceVO.hospitalCode" value="${claimHospitalServiceVO.hospitalCode}" onkeyup="this.value=this.value.replace(/\s/g,'')"/></dd>
			</dl>
			<dl>
				<dt>医院名称</dt>
				<dd><input type="text" class="data" id="hospitalName" name="claimHospitalServiceVO.hospitalName" value="${claimHospitalServiceVO.hospitalName}" onkeyup="this.value=this.value.replace(/\s/g,'')"/></dd>
			</dl>
			<dl>
				<dt>医院等级</dt>
				<dd>
				<%-- <s:select cssClass="notuseflagDelete selectToInput comboxDD"  id="hospitalLevel" name="claimHospitalServiceVO.hospitalLevel" list="hospitalLevelMap"  listKey="key" listValue="value"
					  headerKey="0" headerValue="全部"  >
				</s:select> --%>
				<Field:codeTable name="claimHospitalServiceVO.hospitalLevel"
							tableName="APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL" cssClass="notuseflagDelete combox title comboxDD"
							nullOption="true" value="${claimHospitalServiceVO.hospitalLevel}" id="hospitalLevel" 
							/>
				</dd>
			</dl>
			<dl>
				<dt>定点标志</dt>
				<dd>
				<s:select
				cssClass="notuseflagDelete combox title comboxDD" id="isDesignated" name="claimHospitalServiceVO.isDesignated"
				list="#{'':'请选择',1:'是',0:'否'}"></s:select>
				</dd>
			</dl>
			<dl>	
				<dt>医院状态</dt>
				<dd>
				<s:select
				cssClass="notuseflagDelete combox title comboxDD" id="hospitalStatus" name="claimHospitalServiceVO.hospitalStatus" 
				list="#{'':'请选择',1:'有效',2:'无效'}"></s:select>
				</dd>
			</dl>
			<dl>
				<dt>管理机构</dt>
				<dd>
					<input id="branchCodeHospital" style="width: 30px;border-right:0px" name="claimHospitalServiceVO.organCode" showOrgName="branchnameHospital" value="${claimHospitalServiceVO.organCode }" type="text" class="organ" clickId="branchCodeHospitalBtn"  needAll="true"/>
					<input style="width:110px;" type="text" size="11" name="claimHospitalServiceVO.organName" id="branchnameHospital" value="${claimHospitalServiceVO.organName}" readOnly class="" />
					<a id="branchCodeHospitalBtn" class="btnLook" href="#" style="position: relative;"></a>
				</dd>
			</dl>
			<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="queryHos();" id="userQuery">查询</button>
		    </div>
		</div>
		
	</form>
<div class="tabdivclassbr main_tabdiv">
	<!-- <h1>查询结果</h1> -->
	<div class="panelBar">
		<ul class="toolBar">
			<li><a class="add" href="clm/parameter/addHospital_CLM_maintHospitalAction.action?addUserFlag=add&menuId=${menuId }" maxable="false" minable="false" 
			resizable="false" title="医院信息新增" width="600" height="280" target="dialog" rel="addHospital"><span>新增</span></a></li>
			<li class="line">line</li>
			<li><a class="edit" href="clm/parameter/updateHospital_CLM_maintHospitalAction.action?claimHospitalServiceVO.hospitalId={listIdInfo}&updateFlag=update&menuId=${menuId }" 
				maxable="false" resizable="false" minable="false" title="医院信息修改" width="600" height="280" rel="updateHospital" 
				target="dialog"><span>修改</span></a></li>
			<li class="line">line</li>
			<li><a class="delete" href="clm/parameter/deleteHospital_CLM_maintHospitalAction.action?claimHospitalServiceVO.hospitalId={listIdInfo}&menuId=${menuId }" target="ajaxTodo"  title="是否确认要删除" id="phoneDelete"><span>删除</span></a></li>
		</ul>
	</div>
	<div class="main_border">
	<table class="list main_all" width="100%">
		<thead>
			<tr >
				<th>选择</th>
				<th>序号</th>
				<th>医院代码</th>
				<th>医院名称</th>
				<th>医院等级</th>
				<th>医院状态</th>
				<th>定点标志</th>
				<th style="border-right: 0;">管理机构</th>
				
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
			<s:iterator value="currentPage.pageItems"  status="st"  >
			<tr align="center"  target="listIdInfo" rel="${hospitalId}">
				<td><input type="radio" name="r"></td>
				<td> ${st.index+1}</td>
				<td>${hospitalCode}</td>
				<td>${hospitalName}</td>
				<td>${hospitalLevelName}</td>
				<s:if test="hospitalStatus==1">	
				<td>有效</td>
				</s:if>
				<s:elseif test="hospitalStatus==2">
				<td>无效</td>
				</s:elseif>
				<s:if test="isDesignated==0">	
				<td>否</td>
				</s:if>
				<s:elseif test="isDesignated==1">
				<td>是</td>
				</s:elseif>
				<s:else>
				<td>不确定</td>
				</s:else>
				<%-- <Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO"
											value="${isDesignated}" /> --%>
				<td style="border-right: 0;">${organName}</td>
				
			</tr>
			</s:iterator>
		</tbody>
	</table>
	</div>
	<div class="panelBar" >
		<div class="pages">
			<span>显示</span>
			<s:select   list="#{20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
	    		</s:select>
			<span>条，共${currentPage.total}条</span>		
		</div>
		<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>

	</div>
	</div>
	<div  class="formBarButton">
		<ul>
			<li>
				<button type="button" class="but_blue" onclick="resetAll();" id="userQueryReset">重置</button>
			</li>
			<li>
					<button type="button" class="but_gray" onclick="exit();" id="userQueryReset">退出</button>
			</li>
		</ul>
	</div>
</div>
	
<script type="text/javascript">
/*  function exit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 okCall:function(){
			navTab.closeCurrentTab();
			$(".home_icon", navTab.getCurrentPanel()).click();
		 }
	 });
} */
</script>