<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<%-- <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%> --%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<script type="text/javascript">
/* function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
} */

 
function delRule(delect,ruleId) {
 
	 alertMsg.confirm("确认删除吗?",{
		okCall :function(){   
			$.ajax({ 
	  			type:'post',
	  			url:'clm/register/delRuleData_CLM_claimComfortRuleAction.action?ruleId='+ruleId,
	  			datatype:'json',
	  			global : false,
	  			success:function(data){
	  				var json = eval("(" + data + ")");
	  				if(undefined != json.message){
	  					if(json.statusCode==200){
	  						alertMsg.correct(json.message);
	  						$(delect).parent().parent().remove();
	  					}else{
	  						alertMsg.error(json.message);
	  					}
						
					}
	  				
	  			},
	  			error:function(){
	  				alert("出错了！");
	  			}
	  	   });  
		}
	 })
	 
}
function queryComfortRule(delect){
	var delectEdit = $(delect).val();
	$(delect).parent().parent().find("td:eq(0)").find("input:radio").attr("checked",true);
	$("#comfortInfo", navTab.getCurrentPanel()).find("input[name='claimType']").removeAttr("checked");
	$("#comfortInfo", navTab.getCurrentPanel()).find("input[name='nature']").removeAttr("checked");
	
	var ruleId = $(delect).parent().find("input:eq(0)").val();
	var cusType = $(delect).parent().parent().find("td:eq(2) input").val();
	var agentType = $(delect).parent().parent().find("td:eq(3) input").val();
	var accReason = $(delect).parent().parent().find("td:eq(4) input").val();
	var claimType = $(delect).parent().parent().find("td:eq(5) input").val();
	if (delectEdit != null && delectEdit != "" && delectEdit != 'on') {
		$("#checkComfortRuleCode", navTab.getCurrentPanel()).val(delectEdit);
	} else {
		$("#checkComfortRuleCode", navTab.getCurrentPanel()).val(ruleId);
	}
	

	$("#cusType", navTab.getCurrentPanel()).selectMyComBox(cusType);
	
	   
	$("#agentType", navTab.getCurrentPanel()).selectMyComBox(agentType);
	 
	 var claimTypes = claimType.split(",");
	 for(var i = 0; i < claimTypes.length; i++){
			var ch = "input[name='claimType'][value='" + claimTypes[i].trim() + "']";
			$(ch).attr("checked","checked");
	  }
	  var accReasons = accReason.split(",");
	  for(var i = 0; i < claimTypes.length; i++){
		var ch = "input[name='nature'][value='" + accReasons[i].trim() + "']";
		$(ch).attr("checked","checked");
	  }
	
}
</script>

<div layoutH="0">

	       <div class="divfclass">
				<h1><img src="clm/images/tubiao.png">发起慰问规则维护</h1>
		   </div>

			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">发起慰问规则查询列表</h1>
		   </div>
			<div id="claimBene" class="tabdivclassbr">
			
				<table id="claimBeneTable" class="list" style="width: 100%;" >
					<thead>
						<tr align="center">
						    <th nowrap>选择</th>
						    <th nowrap>序号</th>
							<th nowrap>客户类型</th>
							<th nowrap>业务员类型</th>
							<th nowrap>出险原因</th>
							<th nowrap>理赔类型</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="claimBeneTbody">
						<s:iterator value="claimComfortRuleVOList" status="st" >
							<tr align="center">
								 <td>
								    <input type="hidden" name="comfortRuleCode" id="comfortRuleCode" value="${comfortRuleCode }">
								 	<input type="radio" name="caseId" onclick="queryComfortRule(this);" >
								 </td> 
								 <td><s:property value="#st.index + 1" /></td>
								 <td>
								 	<input type="hidden" value="${cusType }">
								 	<Field:codeValue tableName="APP___CLM__DBUSER.T_CUSTOMER_TYPE" value="${cusType}" ></Field:codeValue>
								 </td>
								 <td>
								 	<input type="hidden" value="${agentNormalTypeCode }">
								 	<Field:codeValue  tableName=" APP___CLM__DBUSER.T_AGENT_NORMAL_TYPE" value="${agentNormalTypeCode }"/>
								 </td>
								 <td>
								 	<input type="hidden" value="${accReasonList}">
								 	<c:forEach items="${accReasonList}" var="j">
									 	<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_NATURE" value="${j}"></Field:codeValue>
								 	</c:forEach>
								 </td>
								 <td>
								 	<input type="hidden" value="${accTypeList}">
									 <c:forEach items="${accTypeList}" var="i">
									 	<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${i}"/>
									 </c:forEach>
								 </td>
								 <td>
								 <a title="修改" class="btnEdit" id="editButton" href="javascript:void(0);" value="${comfortRuleCode}" onclick="queryComfortRule(this);">修改</a>
								 <a class="btnDel" id='delButton' href='javascript:void(0);' onclick="delRule(this,'${comfortRuleCode}');">删除</a>
								 </td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
<!-- 				</fieldset>			 -->
			</div>
		
			<div class="panelPageFormContent">
			<div id="comfortInfo">
			<form id="comfortForm" action="clm/parameter/saveComfort_CLM_claimComfortRuleAction.action" method="post" 
			class="pageForm required-validate" onsubmit="return validateCallback(this, navTabAjaxDone)">
			 <input type="hidden" name="checkComfortRuleCode" id="checkComfortRuleCode" >

			     <div class="divfclass">
				   <h1><img src="clm/images/tubiao.png">发起慰问规则信息</h1>
		         </div>
			     
			    <dl>
					<dt>客户类型</dt>
					<dd>
					   <Field:codeTable  name="claimComfortRuleVO.cusType" id="cusType" value="" tableName="APP___CLM__DBUSER.T_CUSTOMER_TYPE" nullOption="true" cssClass="notuseflagDelete combox title required"/>
					</dd>
				</dl>
				
				<dl>
					<dt>业务员类型</dt>
					<dd>
					   <Field:codeTable  name="claimComfortRuleVO.agentNormalTypeCode" id="agentType" tableName="APP___CLM__DBUSER.T_AGENT_NORMAL_TYPE" nullOption="true" cssClass="notuseflagDelete combox title required"/>
					</dd>
				</dl>
				<dl>
					<dt>出险原因</dt>
					<dd>
						<span>
					   <input type="checkbox" name="nature" id="nature1" value="1"/>疾病
					   </span>
					   <span>
					   <input type="checkbox" name="nature" id="nature2" value="2"/>意外
					   </span>
					   <span>
					   <input type="checkbox" name="nature" id="nature3" value="3"/>工伤意外
					   </span>
					</dd>
				</dl>
				
				<dl style="width: 100%;">
					<dt >理赔类型</dt>
					<dd  style="width: 80%;">
					   <span>
					   <input type="checkbox" name="claimType" id="claimType1" value="01"/>身故
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType2" value="04"/>高残
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType3" value="03"/>重大疾病
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType4" value="02"/>伤残
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType5" value="08"/>医疗
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType7" value="10"/>特种疾病
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType8" value="06"/>一般失能
					   </span>
					   <span>
					   <input type="checkbox" name="claimType" id="claimType9" value="07"/>重度失能
					   </span>
					</dd>
				</dl>
				
			      <div class="formBarButton">
					<ul>
						<li><button class="but_blue" id="save1" >保存</button></li>
						<li><button class="but_gray" type="button" id="cancel"  onclick="exit()">退出</button></li>
					</ul>
				</div>
			</form>
			</div>

</div>
</div>