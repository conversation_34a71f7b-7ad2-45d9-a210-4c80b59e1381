<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<form id="pagerForm" method="post" 
	action="clm/parameter/selectAccidentResultLog_CLM_claimIntelPortraitConfigAction.action"
	<input type="hidden" name="pageNum" vaule="${currentPage4.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage4.pageSize}" />
	<!-- 查询条件回调 -->
	<input type="hidden" name="claimAccResultBusiVO.accident2Code"
		value="${claimAccResultBusiVO.accResult2}" /> <input type="hidden"
		name="claimAccResultBusiVO.accident2Name"
		value="${claimAccResultBusiVO.accResult2Str}" /> <input type="hidden"
		name="claimAccResultBusiLogVO.startDate"
		value="<s:date name='claimAccResultBusiLogVO.startDate' format='yyyy-MM-dd'/>" />
	<input type="hidden" name="claimAccResultBusiLogVO.endDate"
		value="<s:date name='claimAccResultBusiLogVO.endDate' format='yyyy-MM-dd'/>" />
</form>
<div class="main_bqtabdivbr">
	<table class="list" id="surveyApplyOperTable" width="100%">
		<span><b>出险结果责免与险种匹配表配置轨迹</b></span>
		<thead>
			<tr>
				<th nowrap>出险结果2代码</th>
				<th nowrap>出险结果2名称</th>
				<th nowrap>责免险种</th>
				<th nowrap>操作</th>
				<th nowrap>提交人</th>
				<th nowrap>提交日期</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag != null">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">请选择条件查询数据！</div>
					</td>
				</tr>
			</s:if>
			<s:elseif
				test="currentPage4.PageItems == null || currentPage4.PageItems.size()==0">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:elseif>
			<s:iterator value="currentPage4.pageItems" status="st">
				<tr align="center" target="listIdInfo">
					<td align="center" style="word-break: break-all;">${accResult2}</td>
					<td align="center" style="word-break: break-all;">${accResult2Str}</td>
					<td align="center" style="word-break: break-all;">${busiFrom}</td>
					<td align="center" style="word-break: break-all;">${operationType}</td>
					<td align="center" style="word-break: break-all;">${submitUserStr}</td>
					<td align="center" style="word-break: break-all;"><s:date
							name='submitDate' format='yyyy-MM-dd' /></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
				name="select"
				onchange="navTabPageBreak({numPerPage:this.value},'findIntelPortraitConfigActionLog')"
				value="currentPage4.pageSize">
			</s:select>
			<span>条，共${currentPage4.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${currentPage4.total}"
			numPerPage="${currentPage4.pageSize}" pageNumShown="10"
			currentPage="${currentPage4.pageNo}"></div>
	</div>
</div>

