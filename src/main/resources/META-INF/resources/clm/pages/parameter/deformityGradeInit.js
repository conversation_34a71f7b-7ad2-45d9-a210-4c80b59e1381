
//格式化时间
Date.prototype.format = function(format) {
	var o = {
		"M+" : this.getMonth() + 1, // month
		"d+" : this.getDate(), // day
		"h+" : this.getHours(), // hour
		"m+" : this.getMinutes(), // minute
		"s+" : this.getSeconds(), // second
		"q+" : Math.floor((this.getMonth() + 3) / 3), // quarter
		"S" : this.getMilliseconds()
	// millisecond
	};
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (this.getFullYear() + "")
				.substr(4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp("(" + k + ")").test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
					: ("00" + o[k]).substr(("" + o[k]).length));
	return format;
};
function getTime(timeValue){
	return new Date().format('yyyy-MM-dd');
	 
}
function getTime2(timeValue){
	return new Date().format('yyyy-MM-dd hh:mm');
}

function exitDeformity(){
/*	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	  okCall:function(){*/
			//navTab.closeCurrentTab();
			navTab.closeCurrentTab();
			$(".home_icon", navTab.getCurrentPanel()).click();
			
	 	/*}
	 });*/
}

$(function() {
	$("#updateTime", navTab.getCurrentPanel()).val(new Date().format("yyyy-MM-dd"));
});

var map = new Map(); 
function deformityType1Change(selectedValue){ 
	$("#updateTime", navTab.getCurrentPanel()).val(new Date().format("yyyy-MM-dd"));
	var deformityId = $("#deformityType1", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/parameter/findDeformityGradeByTypeId_CLM_claimDeformityGradeAction.action?typeId='+deformityId,
		'type':'post',
		'datatype':'json',
		'success':function(data){
			//清空
			$("#deformityGrade1", navTab.getCurrentPanel()).html("");
			var json = eval("("+data+")");
			var html="";
			for(var i=0;i<json.length;i++){
				html +="<option value='" + json[i].deformityGrade +"'>" + json[i].deformityGrade + "</option>";
				map.put(json[i].deformityGrade, json[i].deformityGradeName);
				//初始化伤残级别名称
				if(i==0){
					$("#deformityGradeName", navTab.getCurrentPanel()).val(json[i].deformityGradeName);
				}
			}
			$("#deformityGrade1", navTab.getCurrentPanel()).loadMyComboxOptions(html);
		}
	});
};
//查询条件的随动
function deformityType2Change(selectedValue){ 
	var deformityType2 = $("#deformityType2", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/parameter/findDeformityGradeByTypeId_CLM_claimDeformityGradeAction.action?typeId='+deformityType2,
		'type':'post',
		'datatype':'json',
		'success':function(data){
			//清空
			$("#deformityGrade2", navTab.getCurrentPanel()).html("");
			var json = eval("("+data+")");
			var html="";
			for(var i=0;i<json.length;i++){
				html +="<option value='" + json[i].deformityGrade +"'>" + json[i].deformityGrade + "</option>";
			}
			$("#deformityGrade2", navTab.getCurrentPanel()).loadMyComboxOptions(html);
		}
	});
};


function defomityGradeChange(){
	
	var deformityGradeName_key = $("#deformityGrade1", navTab.getCurrentPanel()).val();
	$("#deformityGradeName", navTab.getCurrentPanel()).val(map.get(deformityGradeName_key)); 

	
}



//创建Map集合
function Map(){
	this.elements = new Array();
	
	this.size = function(){
		return this.elements.length;
	};
	
	this.put = function(_key,_value){
		this.elements.push({
			key:_key,
			value:_value
		});
	};
	
	this.get = function(_key){
		try{
			for(var i=0;i<this.elements.length;i++){
				if(this.elements[i].key == _key){
					return this.elements[i].value;
				}
			}
		}catch(e){
			return null;
		}
	};
};


function getSelectInfo(delect){
	reset1();
	var selectedValue = $(delect).val().split("|");
	$("#deformityType1", navTab.getCurrentPanel()).selectMyComBox(selectedValue[0]);
//	$("#deformityGrade1", navTab.getCurrentPanel()).val(selectedValue[1]);
	//$("#deformityGrade1", navTab.getCurrentPanel()).html(""); 
	//$("#deformityGrade1", navTab.getCurrentPanel()).loadMyComboxOptions("<option value='" + selectedValue[1] +"'>" + selectedValue[1] + "</option>");
	
	deformityType1Change(selectedValue[1]);
	
	$("#deformityGradeName", navTab.getCurrentPanel()).val(selectedValue[2]);
	$("#deformityCode1", navTab.getCurrentPanel()).val(selectedValue[3]);	
	$("#deformityCode1Name", navTab.getCurrentPanel()).val(selectedValue[4]);
	$("#icfCode", navTab.getCurrentPanel()).val(selectedValue[5]);
	$("#deformityRate", navTab.getCurrentPanel()).val(selectedValue[6]);
	$("#isEffective", navTab.getCurrentPanel()).val(selectedValue[7]);
	$("#deformityGradeId", navTab.getCurrentPanel()).val(selectedValue[8]);
	$("#startDate", navTab.getCurrentPanel()).val(selectedValue[9] );
	$("#endDate", navTab.getCurrentPanel()).val(selectedValue[10]);
	//$("#updateTime", navTab.getCurrentPanel()).val(new Date(selectedValue[11]).format('yyyy-MM-dd'));
}

function reset1(){
	$("#deformityGradeJspForm", navTab.getCurrentPanel())[0].reset();
}

function reset2(){
	$("input[type='radio']", navTab.getCurrentPanel()).removeAttr('checked');
	$("#deformityGradeId", navTab.getCurrentPanel()).val("");
	$("#deformityGrade1", navTab.getCurrentPanel()).html("");
	$("#deformityGradeJspForm", navTab.getCurrentPanel())[0].reset();
}
function reset3(){ 
	$("#deformityType1",navTab.getCurrentPanel()).selectMyComBox("");
	$("#deformityGrade1", navTab.getCurrentPanel()).loadMyComboxOptions("<option value=''>" + "请选择" + "</option>");
 
}
function addInfo(){ 
	$("#deformityType1",navTab.getCurrentPanel()).selectMyComBox("");
	$("#deformityGrade1", navTab.getCurrentPanel()).loadMyComboxOptions("<option value=''>" + "请选择" + "</option>");
	$("#deformityGradeId", navTab.getCurrentPanel()).val(null);
	//新增按钮进行置空操作
	$("#deformityGradeName", navTab.getCurrentPanel()).val("");
	$("#deformityCode1", navTab.getCurrentPanel()).val("");
	$("#deformityCode1Name", navTab.getCurrentPanel()).val("");
	$("#icfCode", navTab.getCurrentPanel()).val("");
	$("#deformityRate", navTab.getCurrentPanel()).val("");
	$("#startDate", navTab.getCurrentPanel()).val("");
	$("#endDate", navTab.getCurrentPanel()).val("");
}
function saveDeformityInfo(){
	var deformityType1 = $("#deformityType1",navTab.getCurrentPanel()).val().trim();
	var deformityCode1 = $("#deformityCode1",navTab.getCurrentPanel()).val().trim();
	var deformityRate = $("#deformityRate",navTab.getCurrentPanel()).val().trim();
	var startDate = $("#startDate",navTab.getCurrentPanel()).val().trim();
	var endDate = $("#endDate",navTab.getCurrentPanel()).val().trim();
	if(deformityType1 == ""){
		alertMsg.error("请录入必录项伤残类型");
		$("#alertMsgBox .toolBar .button", navTab.getCurrentPanel()).on("click",function(){
		    $("#deformityType1", navTab.getCurrentPanel()).focus();
		})
		return false;
	}
	if(deformityCode1 == ""){
		alertMsg.error("请录入必录项伤残代码");
		$("#alertMsgBox .toolBar .button", navTab.getCurrentPanel()).on("click",function(){
		    $("#deformityCode1", navTab.getCurrentPanel()).focus();
		})
		return false;
	}
	if(deformityRate == ""){
		alertMsg.error("请录入必录项残疾给付比例");
		$("#alertMsgBox .toolBar .button", navTab.getCurrentPanel()).on("click",function(){
		    $("#deformityRate", navTab.getCurrentPanel()).focus();
		})
		return false;
	}
	if(startDate == ""){
		alertMsg.error("请录入必录项有效日期开始日期");
		$("#alertMsgBox .toolBar .button", navTab.getCurrentPanel()).on("click",function(){
		    $("#startDate", navTab.getCurrentPanel()).focus();
		})
		return false;
	}
	if(endDate == ""){
		alertMsg.error("请录入必录项有效日期结束日期");
		$("#alertMsgBox .toolBar .button", navTab.getCurrentPanel()).on("click",function(){
		    $("#endDate", navTab.getCurrentPanel()).focus();
		})
		return false;
	}
	if(endDate < startDate){
		alertMsg.error("有效日期结束日期不能小于有效日期开始日期,请检查!");
		return false;
	}
	$("#deformityGradeJspForm",navTab.getCurrentPanel()).submit();
}

function add1(){
	reset2();
}

function del(del){
	var selectedValue = $(del).parent().parent().parent().find("td:eq(0)").find("input").val().split("|");
	var deformityGradeId = selectedValue[8];
	if(window.confirm("确认删除吗?")){
		$.ajax({
	  			type:'post',   
	  			url:'clm/parameter/delDeformityInfo_CLM_claimDeformityGradeAction.action?deformityGradeId='+deformityGradeId,
	  			datatype:'json',
	  			global : false,
	  			success:function(data){
	  				var json = eval("(" + data + ")");
	  				if(undefined != json.message){
						alertMsg.info(json.message);
					}
	  				$(del).parent().parent().parent().remove();
	  			},
	  			error:function(){
	  				alert("出错了！");
	  			}
	  	});  
	}
}
















