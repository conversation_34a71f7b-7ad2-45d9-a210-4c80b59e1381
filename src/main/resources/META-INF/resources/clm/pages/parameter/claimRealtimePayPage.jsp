<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/claimRealtimePayPage.js"></script>

<div layoutH="36" id="claimRealtimePayDiv">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
	</div>
	<form id="pagerForm" method="post"
		action="clm/parameter/queryClaimRealtimePayByFlowType_CLM_claimRealtimePayAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>

	<form id="claimRealtimePayForm"
		action="clm/parameter/queryClaimRealtimePayByFlowType_CLM_claimRealtimePayAction.action"
		class="pagerForm required-validate"
		onsubmit="return navTabSearch(this);" rel="pagerForm">

		<div class="pageFormInfoContent">
			<dl>
				<dt>流程节点</dt>
				<dd>
					<Field:codeTable id="flowType1" name="claimRealtimePayVO.flowType"
						value="${claimRealtimePayVO.flowType}"
						cssClass="notuseflagDelete combox title comboxDD"
						tableName="APP___CLM__DBUSER.T_FLOW_TYPE" nullOption="true"></Field:codeTable>
				</dd>
			</dl>
			<dl>
				<dt></dt>
				<dd>
					<button class="but_blue" type="button" onclick="queryClaimRealtimePayByFlowType();">查询</button>
				</dd>
			</dl>
		</div>
	</form>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询列表
		</h1>
	</div>
	<div class="tabdivclassbr main_tabdiv">
		<table class="list sortable main_dbottom" width="100%">
			<thead>
				<tr>
					<th nowrap>序号</th>
					<th nowrap>流程节点</th>
					<th nowrap>实时支付开关</th>
					<th nowrap>配置人姓名</th>
					<th nowrap>配置时间</th>
				</tr>
			</thead>
			<tbody id="claimRealtimePaybody">
				<s:if test="imageFlag != null">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif
					test="currentPage.pageItems == null || currentPage.pageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>
				<s:iterator value="currentPage.pageItems" status="st">
					<tr>
						<td nowrap>${st.index+1 }</td>
						<td nowrap>
							<Field:codeValue value="${flowType }" tableName="APP___CLM__DBUSER.T_FLOW_TYPE"/>
						</td>
						<td nowrap>
							<s:if test="paymentSwitch == 1">开</s:if><s:else>关</s:else>
						</td>
						<td nowrap>
							<Field:codeValue value="${deployPerson }" tableName="APP___CLM__DBUSER.T_UDMP_USER"/>
						</td>
						<td nowrap><s:date name="deployTime" format='yyyy-MM-dd HH:mm:ss'/></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<!-- 分页查询区域 -->
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="20"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
	
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">实时支付开关配置
		</h1>
	</div>
	<form id="claimRealtimePayForm2"
		action="clm/parameter/saveClaimRealtimePay_CLM_claimRealtimePayAction.action"
		class="pagerForm required-validate" onsubmit="return navTabSearch(this);">

		<div class="pageFormInfoContent">
			<dl>
				<dt>流程节点</dt>
				<dd>
					<Field:codeTable id="flowType2" name="claimRealtimePay.flowType" 
						tableName="APP___CLM__DBUSER.T_FLOW_TYPE" nullOption="false"
						cssClass="notuseflagDelete combox title comboxDD"
						value="${claimRealtimePay.flowType}" onChange="checkPaymentSwitch(this)"/>
				</dd>
			</dl>
			<dl>
				<dt>实时支付开关</dt>
				<dd>
					<span><input type="radio" id="on" name="claimRealtimePay.paymentSwitch" value="1" />开 </span>
					<span><input type="radio" id="off" name="claimRealtimePay.paymentSwitch" value="0" checked />关 </span>
				</dd>
			</dl>
			<dl>
				<dt></dt>
				<dd>
					<button class="but_blue" type="button" onclick="saveClaimRealtimePay();">确认</button>
				</dd>
			</dl>
		</div>
	</form>
	
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button type="button" class="but_gray" id="exitbtn" onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
</div>