﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/claimBlackNamePage.js"></script>

<div layoutH="36" id="claimBlackNameFormIdDivId">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">理赔黑名单维护
				</h1>
			</div>
			<form id="pagerForm" method="post"
				action="clm/parameter/queryClaimBlackNameInfo_CLM_claimBlackNameAction.action">
				<input type="hidden" name="pageNum" value="${claimBlackNameVOPage.pageNo}" />
				<input type="hidden" name="numPerPage" value="${claimBlackNameVOPage.pageSize}" />
			</form>
					
					<form id="claimBlackNameFormId" action="clm/parameter/queryClaimBlackNameInfo_CLM_claimBlackNameAction.action" 
							class="pagerForm required-validate" onsubmit="return navTabSearch(this);" rel="pagerForm">
					
					<div class="pageFormInfoContent">
						<%-- <dl>
							<dt>客户号码</dt>
							<dd>
								<input type="text"  value="${claimBlackNameVO.customerId}" name="claimBlackNameVO.customerId" value="" onkeyup="this.value=this.value.replace(/\D/g,'')" />
							</dd>
						</dl> --%>
						<dl>
							<dt>姓名</dt>
							<dd>
								<input type="text" name="claimBlackNameVO.blackName" value="${claimBlackNameVO.blackName}" />
							</dd>
						</dl>
						<dl>
							<dt>证件号码</dt>
							<dd>
								<input type="text" name="claimBlackNameVO.blackCertiCode" value="${claimBlackNameVO.blackCertiCode}"/>
							</dd>
						</dl>
						<dl>
							<dt>黑名单状态标识</dt>
							<dd>
									
								<Field:codeTable  name="claimBlackNameVO.blackNameStatus"
									value="${claimBlackNameVO.blackNameStatus}" cssClass="notuseflagDelete combox title required comboxDD"
									tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" ></Field:codeTable>
							</dd>
						</dl>
						<dl>
							<dt>业务员编号</dt>
							<dd>
								<input type="text"  name="claimBlackNameVO.agentCode" value="${claimBlackNameVO.agentCode}" onkeyup="this.value=this.value.replace(/\s/g,'')" />
							</dd>
						</dl>
					<div class="pageFormdiv"><button class="but_blue" type="button" onclick="queryClaimBlackNameQuery();">查询</button></div>
				</div>
			</form>
			<div class="tabdivclassbr main_tabdiv">
				<table class="list sortable main_dbottom" width="100%">
					<thead>
						<tr>
							<th nowrap>选择</th>
							<th nowrap>序号</th>
							<th nowrap>业务员编号</th>
							<th nowrap>姓名</th>
							<th nowrap>性别</th>
							<th nowrap>出生日期</th>
							<th nowrap>证件类型</th>
							<th nowrap>证件号码</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="claimBlackNameTbodyId">
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="claimBlackNameVOPage.pageItems == null || claimBlackNameVOPage.pageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
							<s:iterator value="claimBlackNameVOPage.pageItems" status="st">
								<tr onclick="claimBlackNameCheck(this)">
									<td><input type="radio" name="n1" onclick="queryClaimBlackNameInfoSingle(this)"/><input type="hidden" value="${blackId}"></td>
									<td>${st.index+1}
										<input type="hidden" value="${blackNameType}"/>
										<input type="hidden" value="${blackNameStatus}"/>
										<input type="hidden" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${establishPerson}"/>"/>
										<input type="hidden" value="<s:date name="establishTime" format="yyyy-MM-dd"/>"/>
										<input type="hidden" value="<s:date name="finalUpdateTime" format="yyyy-MM-dd"/>"/>
										<input type="hidden" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${finalUpdatePerson}"/>"/>
										<input type="hidden" value="${blackNameReason}"/>
										<input type="hidden" value="${blackGender}"/>
										<input type="hidden" value="${blackCertiType}"/>
										<input type="hidden" value="${establishPerson}"/>
									</td>
									<td>${agentCode}</td>
									<td>${blackName}</td>
									<td><Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${blackGender}"/> </td>
									<td><s:date name="blackBirthday" format="yyyy-MM-dd"/></td>
									<td><Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${blackCertiType}"/></td>
									<td>${blackCertiCode}</td>
									<td><a class="btnDelPrivate" href="javascript:;" onclick="deleteCaimBlackName(${blackId},this)" ref="current" >删除</a></td>
								</tr>
							</s:iterator>
					</tbody>
				</table>
				<!-- 分页查询区域 -->
				<div class="panelBar">
<!-- 					<div class="pages"> -->
<%-- 						<span>显示</span> --%>
<%-- 						<s:select list="#{5:'5',10:'10',20:'20'}" --%>
<%-- 							name="select" onchange="navTabPageBreak({numPerPage:this.value},'claimBlackNameFormIdId')" --%>
<%-- 							value="claimBlackNameVOPage.pageSize"> --%>
<%-- 						</s:select> --%>
<%-- 						<span>条，共${claimBlackNameVOPage.total}条</span> --%>
<!-- 					</div> -->
					<div class="pagination" targetType="navTab" id="queryTotalPage"
						totalCount="${claimBlackNameVOPage.total}" rel="claimBlackNameFormIdDivId"
						numPerPage="${claimBlackNameVOPage.pageSize}" pageNumShown="10"
						currentPage="${claimBlackNameVOPage.pageNo}"></div>
				</div>
			</div>
			
			<div class="formBarButton main_bottom">
			<ul>
				<li>
					<button type="button" class="but_blue" id="addClaimBlackNameInfoId"  onclick="addClaimBlackNameInfo()">添加</button>
				</li>
				<li>
					<button type="button" class="but_blue" id="saveClaimBlackNameInfoId"  onclick="saveClaimBlackNameInfo()">保存</button>
				</li>
			</ul>
		</div>
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">理赔黑名单详细信息
				</h1>
			</div>
		<div class="panelPageFormContent main_tabdiv" id="claimBlackNameInfoId">
			<input type="hidden" id="saveAndUpdateFlag">
			<dl>
				<dt><font>* </font>姓名</dt>
				<dd>
					<input id="blackNameBlackId" onkeyup="resetStyle(this)" type="text" name="claimBlackNameVO.blackName" value="${claimBlackNameVO.blackName}" />
				</dd>
			</dl>
			<%-- <dl>
				<dt>客户号码</dt>
				<dd>
					<input id="customerIdBlackId"  type="text" name="claimBlackNameVO.customerId" value="${claimBlackNameVO.customerId}" onkeyup="this.value=this.value.replace(/\D/g,'')"/>
				</dd>
			</dl> --%>
			<dl>
				<dt>性别</dt>
				<dd>
					<Field:codeTable id="blackGenderId"  name="claimBlackNameVO.blackGender"
								value="${claimBlackNameVO.blackGender}" cssClass="notuseflagDelete combox title required comboxDD"
								tableName="APP___CLM__DBUSER.T_GENDER" nullOption="true"  whereClause="gender_code in (1,2)"></Field:codeTable>
				</dd>
			</dl>
			<dl>
				<dt>出生日期</dt>
				<dd>
					<input type="expandDateYMD" id="blackBirthdayBlackId" flag="flag" class="date"
								name="claimBlackNameVO.blackBirthday" size="17" value="${claimBlackNameVO.blackBirthday}"/>
								<a class="inputDateButton" href="javascript:;">选择</a>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>证件类型</dt>
				<dd>
					<Field:codeTable id="blackCertiTypeId" name="claimBlackNameVO.blackCertiType"
								value="${claimBlackNameVO.blackCertiType}" cssClass="notuseflagDelete combox title required comboxDD"
								tableName="APP___CLM__DBUSER.T_CERTI_TYPE" nullOption="true" 
								whereClause="code in ('0','5','2','b','4','1','8')"  orderBy="decode(code,'0','001','5','002','2','003','b','004','4','005','1','006','8','007', code)" ></Field:codeTable>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>证件号码</dt>
				<dd>
					<input id="blackCertiCodeId" type="text" name="claimBlackNameVO.blackCertiCode" size="17" value="${claimBlackNameVO.blackCertiCode}"/>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>黑名单类型</dt>
				<dd>
					<Field:codeTable id="blackNameTypeId" name="claimBlackNameVO.blackNameType"
								value="${claimBlackNameVO.blackNameType}" cssClass="notuseflagDelete combox title required comboxDD"
								tableName="APP___CLM__DBUSER.T_ISSUE_OBJ_TYPE" nullOption="true"  ></Field:codeTable>
				</dd>
			</dl>
			<dl>
				<dt>业务员编号</dt>
				<dd>
					<input id="agentCodeBlackId"  type="text" name="claimBlackNameVO.agentCode" value="${claimBlackNameVO.agentCode}" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>黑名单状态标识</dt>
				<dd>
					<Field:codeTable id="blackNameStatusId" name="claimBlackNameVO.blackNameStatus"
								value="${claimBlackNameVO.blackNameStatus}" cssClass="notuseflagDelete combox title required comboxDD"
								tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" ></Field:codeTable>
				</dd>
			</dl>
			<dl style="width: 100%;height: auto;">
				<dt>黑名单事由</dt>
				<dd>
					<textarea id="blackNameReasonId" name="claimBlackNameVO.blackNameReason"  rows="3" cols="100">${claimBlackNameVO.blackNameReason}</textarea>
				</dd>
			</dl>
			<dl>
				<dt>创建人</dt>
				<dd>
					<input id="establishPersonIdCopy" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${claimBlackNameVO.establishPerson}"/>" readonly/>
					<input id="establishPersonId" type="hidden" name="claimBlackNameVO.establishPerson" value="${claimBlackNameVO.establishPerson}" readonly/>
				</dd>
			</dl>
			<dl>
				<dt>创建日期</dt>
				<dd>
					<input id="establishTimeIdCopy" type="text" name="claimBlackNameVO.establishTime" value="<s:date name="claimBlackNameVO.establishTime" format="yyyy-MM-dd"/>" readonly/>
					<input id="establishTimeId" type="hidden" value="${claimBlackNameVO.establishTime}" readonly/>
				</dd>
			</dl>
			<dl>
				<dt>最后一次创建人</dt>
				<dd>
					<input id="finalUpdatePersonId" type="hidden" name="claimBlackNameVO.finalUpdatePerson" value="${claimBlackNameVO.finalUpdatePerson}" readonly/>
					<input id="finalUpdatePersonIdCopy" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${claimBlackNameVO.finalUpdatePerson}"/>" readonly/>
				</dd>
			</dl>
			<dl>
				<dt>最后一次修改日期</dt>
				<dd>
					<input id="finalUpdateTimeIdCopy" type="text"  value="<s:date name="claimBlackNameVO.finalUpdateTime" format="yyyy-MM-dd"/>" readonly/>
					<input id="finalUpdateTimeId" type="hidden" name="claimBlackNameVO.finalUpdateTime" value="${claimBlackNameVO.finalUpdateTime}" readonly/>
				</dd>
			</dl>
		</div>	
		<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">上载理赔黑名单
				</h1>
		</div>
		<div class="pageFormInfoContent" style="MARGIN-TOP: 15px;MARGIN-BOTTOM: 15px;">
		  <div>
		     <form id="buttonFormSubmit"
				action="clm/parameter/importToBlackName_CLM_claimBlackNameAction.action"
				method="post" enctype="multipart/form-data" style="margin-top: 10px"
				onsubmit="return iframeCallback(this,myAjaxDone);">
				
				<dl style="margin-top: 10px; margin-top: 3px">
					<dt style="width: 60px;">
						<span>选择文件</span>
					</dt>
					<dd>
						<input style="WIDTH: 270px" type="file" name="uploadfile" id="fileName">
					</dd>
				</dl>
				<dl style="width: 80px; margin-left: 70px;">
				<dt>
					<button type="button" class="but_blue" onclick="downLoadExcel('downLoadExcelForm','clm/parameter/downLoadExcelForm_CLM_claimBlackNameAction.action')">下载模板</button>
				</dt>
			    </dl>
			    <dl>
					<dt>
						<button type="button"  class="but_blue" onclick="sumit()">导入</button>
					</dt>
				</dl>
			</form>
		  </div><br/>
		    <div class="divfclass" style="padding-top:25px">
							<h1>
							 <img src="cs/img/icon/tubiao.png">导入清单
							</h1>			
			</div><br/>	
		<div id="downLoadResultId">
		    <form id="downLoadExcelForm" class="pageForm required-validate" novalidate="novalidate"
			  action="clm/parameter/downLoadExcelForm_CLM_claimBlackNameAction.action"
			  onsubmit="return navTabSearch(this)" method="post"></form>
		</div><br/>	
		<div class="tabdivclassbr">
	    <div style="overflow: auto;">
		<table class="list sortable main_dbottom" style="width: 100%;">
			<thead align="center">
				<tr>
					<th nowrap>序号</th>
					<th nowrap>业务员代码</th>
					<th nowrap>姓名</th>
					<th nowrap>性别</th>
					<th nowrap>出生日期</th>
					<th nowrap>证件类型</th>
					<th nowrap>证件号码</th>
					<th nowrap>黑名单类型</th>
					<th nowrap>黑名单事由</th>
					<th nowrap>详细信息</th>
					<th nowrap>维护日期</th>
					<th nowrap>维护人</th>					
				</tr>
			</thead>

			<tbody align="center" id="claimBlackNameFailTable">				
				<s:iterator value="claimBlackNameFailVOPage.pageItems" status="st" var="claimBlackNameFailVOPage">
					<tr align="center" target="listIdInfo">
						<td>${st.index+1}</td>
						<td>${agentCode}</td>
						<td>${blackName}</td>
						<td><Field:codeValue tableName="APP___CLM__DBUSER.T_GENDER" value="${blackGender}"/> </td>
						<td><s:date name="blackBirthday" format="yyyy-MM-dd"/></td>
						<td><Field:codeValue tableName="APP___CLM__DBUSER.T_CERTI_TYPE" value="${blackCertiType}"/></td>
						<td>${blackCertiCode}</td>
						<td><Field:codeValue tableName="APP___CLM__DBUSER.T_ISSUE_OBJ_TYPE" value="${blackNameType}"/></td>
						<td>${blackNameReason}</td>
						<td>${failureReason}</td>
						<td><s:date name="maintainTime" format="yyyy-MM-dd"/></td>
						<td><Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${maintainPerson}"/></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		</div>
		<div class="panelBar">
			<div class="pages">
				<span>每页</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50'}" name="select"
					onchange="navTabPageBreak({numPerPage:this.value})"
					value="claimBlackNameFailVOPage.pageSize">
				</s:select>
				<span>条，共${claimBlackNameFailVOPage.total }条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${claimBlackNameFailVOPage.total }"
				numPerPage="${claimBlackNameFailVOPage.pageSize }" pageNumShown="10"
				currentPage="${claimBlackNameFailVOPage.pageNo }"></div>
		</div>
	</div>		  		  
		</div>
		<div id="importResultId">
				<form id="frushImportResult" action="clm/parameter/frushImportResult_CLM_claimBlackNameAction.action" 
				class="pagerForm required-validate" onsubmit="return navTabSearch(this);" rel="pagerForm"></form>
		</div>		
		<div class="formBarButton main_bottom">
			<ul>
				<li>
					<button type="button" class="but_gray" id="exitbtn"  onclick="exit()">退出</button>
				</li>
			</ul>
		</div>		
</div>