<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
 
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">

var arr='${dataSwitchSettingsVO.caseType}';
var arrvalue;//用于存放取出的数组的值
for(var i=0;i<arr.length;i++){
	arrvalue=arr[i];
	if(arrvalue=="1"){
		$("#caseType1",navTab.getCurrentPanel()).attr("checked","checked"); 
	}
	if(arrvalue=="2"){
		$("#caseType2",navTab.getCurrentPanel()).attr("checked","checked"); 
	}
	if(arrvalue=="3"){
		$("#caseType3",navTab.getCurrentPanel()).attr("checked","checked"); 
	}
}

//保存后回调函数
function saveNewsMethod(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		alertMsg.correct(json.message);
	} else {
		alertMsg.error(json.message);
	}
}

function saveSet(){
	$("#saveDataSet", navTab.getCurrentPanel()).submit();
}
</script>
<!-- 分页表单 -->

<div layoutH="50" style="background: #fafafa" id="allForce">

  <form id="saveDataSet" method="post"
		action="clm/parameter/saveDataRevealSwitch_CLM_ciitcLinkSwitchSetAction.action"
		onsubmit="return validateCallback(this, saveNewsMethod)"
		class="pagerForm required-validate">	
    <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">数据展示开关设置</h1>
    </div>
	<div class="panelPageFormContent"  >
        <dl style="width: 100%;">
           <dt >案件类型</dt>
           <dd  style="width: 30%;">
               <span>
					<input type="checkbox" name="dataSwitchSettingsVO.caseType" id="caseType1" value="1"/>自核案件
			   </span>
			   <span>
					<input type="checkbox" name="dataSwitchSettingsVO.caseType" id="caseType2" value="2"/>单人审核案件
			   </span>
			   <span>
					<input type="checkbox" name="dataSwitchSettingsVO.caseType" id="caseType3" value="3"/>双人审核审批案件
			   </span>
		   </dd>
        </dl>   
	</div>
    <div class="formBarButton main_bottom" style='margin-top: 6%'>
		 <ul>
			<li>
			    <a id="preserve" class="but_blue main_buta"  onclick="saveSet();">保存</a>
			</li>
			<li>
			   <a class="but_blue main_buta"
				  href="clm/parameter/dataRevealSwitchTrace_CLM_ciitcLinkSwitchSetAction.action?"
				  target="dialog" title="中保信开关设置" rel="page2" type="button" width="1000" height="550">配置轨迹查询</a>
			</li>
			<li>
			    <button class="but_gray" type="button" id="exitbtn"  onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
  </form>
</div>