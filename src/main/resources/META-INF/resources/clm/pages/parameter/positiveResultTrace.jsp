<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/positiveResultRedSetInit.js"></script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">
</script>
<!-- 分页表单 -->

<div layoutH="20" style="background: #fafafa" id="allForce">
      <form id="pagerForm" method="post"
		action="clm/parameter/dataRevealSwitchTrace_CLM_ciitcLinkSwitchSetAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}"/>
	</form>
	<form id="messageAlertJspForm"
		action="clm/parameter/dataRevealSwitchTrace_CLM_ciitcLinkSwitchSetAction.action"
		method="post" onsubmit="return dialogSearch(this)"
		class="pageForm required-validate" rel="pagerForm">
      <div class="main_box">
      <div class="main_heading">
         <h1><img src="clm/images/tubiao.png">个人意外险核保风险提示
			<b class="maim_lpask"></b>
		 </h1>
		</div>
      <div>
      <table class="list" width="100%">
       <thead>
          <tr>
             <th>多家公司承保提示</th>
             <th>重疾理赔史</th>
             <th>伤残理赔史</th>
             <th>是否密集投保</th>
             <th>累计保额提示</th>
             <th>自驾车意外险累计保额提示</th>
             <th>自驾车责任多家投保家数</th>
             <th>特定疾病理赔史</th>
             <th>提交人</th>
             <th>提交日期</th>
          </tr>
       </thead>
       <tbody id="jobManagerUserMsg1">
        <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
		 </s:if>
		 <s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
		 </s:elseif>  
		 <s:iterator value="currentPage.pageItems" var="status" status="st">
          <tr>
            <td align="center">
                <s:if test="codeOn==1">标红</s:if>
            </td>
            <td align="center">
                <s:if test="codeTw==1">标红</s:if>
            </td>
            <td align="center">
                <s:if test="codeTh==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeFo==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeFi==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeSi==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeSe==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeEi==1">标红</s:if>
            </td>
            <td align="center">
               ${userRealName}
            </td>
            <td>
               <s:date name="submitDate" format="yyyy-MM-dd"/>
            </td>
          </tr>
          </s:iterator>
       </tbody>
     </table>
     </div>
     </div>
  </form>   
     
     
     <div class="main_box">
      <div class="main_heading">
         <h1><img src="clm/images/tubiao.png">个人健康险理赔风险提示(补偿医疗)
			<b class="maim_lpask"></b>
		 </h1>
		</div>
      <div>
      <table class="list" width="100%">
       <thead>
          <tr>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>重疾理赔史</th>
             <th>慢性病理赔史</th>
             <th>一年内总赔付次数</th>
             <th>多家公司承保提示</th>
             <th>特定疾病理赔史</th>
             <th>提交人</th>
             <th>提交日期</th>
          </tr>
       </thead>
       <tbody id="jobManagerUserMsg2">
        <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
		 </s:if>
		 <s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
		 </s:elseif>  
		 <s:iterator value="currentPage.pageItems" var="status" status="st">
          <tr>
            <td> 
               <s:if test="codeNiS==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeS==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTwS==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeOnS==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeTwS==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeOnS==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeEiS==1">标红</s:if>
            </td>
            <td>
               ${userRealName}
            </td>
            <td> 
               <s:date name="submitDate" format="yyyy-MM-dd"/>
            </td>
          </tr>
         </s:iterator>
       </tbody>
     </table>
     </div>
   </div>  
     
     <div class="main_box">
      <div class="main_heading">
         <h1><img src="clm/images/tubiao.png">个人健康险理赔风险提示(重大疾病)
			<b class="maim_lpask"></b>
		 </h1>
		</div>
      <div class="tabdivclassbr" >
      <table class="list" width="100%">
       <thead>
          <tr>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>重疾理赔史</th>
             <th>慢性病理赔史</th>
             <th>重疾保额</th>
             <th>多家公司承保提示</th>
             <th>密集投保</th>
             <th>投保前理赔</th>
             <th>特定疾病理赔史</th>
             <th>提交人</th>
             <th>提交日期</th>
          </tr>
       </thead>
       <tbody id="jobManagerUserMsg3">
        <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
		 </s:if>
		 <s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
		 </s:elseif>  
		 <s:iterator value="currentPage.pageItems" var="status" status="st">
          <tr>
            <td> 
                <s:if test="codeNiT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeTeT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeTwT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeTeOnT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeTeThT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeOnT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeTeFoT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeTeFiT==1">标红</s:if>
            </td>
            <td> 
                <s:if test="codeEiT==1">标红</s:if>
            </td>
            <td> 
                ${userRealName}
            </td>
            <td> 
                <s:date name="submitDate" format="yyyy-MM-dd"/>
            </td>
          </tr>
         </s:iterator>
       </tbody>
     </table>
     </div>
    </div>
     
     <div class="main_box">
      <div class="main_heading">
         <h1><img src="clm/images/tubiao.png">个人健康险理赔风险提示(个人津贴)
			<b class="maim_lpask"></b>
		 </h1>
		</div>
      <div class="tabdivclassbr">
      <table class="list" width="100%">
        <thead>
          <tr>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>重疾理赔史</th>
             <th>慢性病理赔史</th>
             <th>理赔次数风险</th>
             <th>有效保额累计日额</th>
             <th>累计津贴险赔付天数</th>
             <th>特定疾病理赔史</th>
             <th>提交人</th>
             <th>提交日期</th>
          </tr>
       </thead>
       <tbody id="jobManagerUserMsg4">
         <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
		 </s:if>
		 <s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
		 </s:elseif> 
		 <s:iterator value="currentPage.pageItems" var="status" status="st">
          <tr>
            <td> 
               <s:if test="codeNiF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTwF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeOnF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeSiF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeSeF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTeEiF==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeEiF==1">标红</s:if>
            </td>
            <td> 
               ${userRealName}
            </td>
            <td> 
               <s:date name="submitDate" format="yyyy-MM-dd"/>
            </td>
          </tr>
        </s:iterator>
       </tbody>
     </table>
     </div>
    </div> 
     
      <div class="main_box">
      <div class="main_heading">
         <h1><img src="clm/images/tubiao.png">理赔重复收据提示
			<b class="maim_lpask"></b>
		 </h1>
		</div>
      
      <div>
      <table class="list" width="100%">
       <thead>
          <tr>
             <th>客户既往是否存在重复发票</th>
             <th>收据号其他应用赔案</th>
             <th>是否有分割单</th>
             <th>提交人</th>
             <th>提交日期</th>
          </tr>
       </thead>
       <tbody id="jobManagerUserMsg5">
        <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
		 </s:if>
		 <s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
		 </s:elseif>  
		 <s:iterator value="currentPage.pageItems" var="status" status="st">
          <tr>
            <td> 
               <s:if test="codeTeNi==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTwZe==1">标红</s:if>
            </td>
            <td> 
               <s:if test="codeTwOn==1">标红</s:if>
            </td>
            <td> 
               ${userRealName}
            </td>
            <td> 
               <s:date name="submitDate" format="yyyy-MM-dd"/>
            </td>
          </tr>
         </s:iterator>
       </tbody>
     </table>
     </div>
    </div> 
    
    <div class="main_box">
      <div class="main_heading">
         <h1><img src="clm/images/tubiao.png">个人意外险理赔风险提示
			<b class="maim_lpask"></b>
		 </h1>
		</div>
      <div>
      <table class="list" width="100%">
       <thead>
          <tr>
             <th>多家公司承保提示</th>
             <th>伤残理赔史</th>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>短期出险史</th>
             <th>是否死亡</th>
             <th>查询公司家数</th>
             <th>重疾理赔史</th>
             <th>是否密集投保</th>
             <th>意外医疗赔付总次数</th>
             <th>提交人</th>
             <th>提交日期</th>
          </tr>
       </thead>
       <tbody id="jobManagerUserMsg1">
        <s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
		 </s:if>
		 <s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
		 </s:elseif>  
		 <s:iterator value="currentPage.pageItems" var="status" status="st">
          <tr>
            <td align="center">
                <s:if test="codeOnC==1">标红</s:if>
            </td>
            <td align="center">
                <s:if test="codeThC==1">标红</s:if>
            </td>
            <td align="center">
                <s:if test="codeNiC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeTeC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeTwTwC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeTwThC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeTwFoC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeTwC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeFoC==1">标红</s:if>
            </td>
            <td align="center">
               <s:if test="codeTwFiC==1">标红</s:if>
            </td>
            <td align="center">
               ${userRealName}
            </td>
            <td>
               <s:date name="submitDate" format="yyyy-MM-dd"/>
            </td>
          </tr>
          </s:iterator>
       </tbody>
     </table>
     </div>
     </div>
     
    <div class="pageFormdiv main_bottom" >
					<button type="button" class="but_gray" onclick="exitDialog()">返回</button>
     </div>
</div>