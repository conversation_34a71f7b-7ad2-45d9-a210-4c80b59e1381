<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>

<script type="text/javascript">
	$("#validateBtn",$.pdialog.getCurrent()).click(
		function validateBtn(){
			var branchname =$("#branchnameAddPhoneUpdate",$.pdialog.getCurrent()).val();
			var branchPhone = $("#branchPhone",$.pdialog.getCurrent()).val();
			var flag = true;
			if($.trim(branchname)==""){
				flag = false;
				alertMsg.error("请录入必录项!");
				$("#alertMsgBox .toolBar .button").off().on("click",function(){
					$("#branchname",$.pdialog.getCurrent()).focus();
				});
				return false;
			}
			if($.trim(branchPhone)==""){
				flag = false;
				alertMsg.error("请录入必录项!");
				$("#alertMsgBox .toolBar .button").off().on("click",function(){
					$("#branchPhone",$.pdialog.getCurrent()).focus();
				});
				return false;
			}
			var isok= $("#userForm").valid();  
			$("#alertMsgBox .toolBar .button").on("click",function(){
			    $(".error:eq(0)",$.pdialog.getCurrent()).focus();
			});
			if(!isok){ 
		      return false;
			}
// 			$("#userForm", navTab.getCurrentPanel()).submit() ;
		}
	);
</script>

<div class="pageFormInfoContent">
	<form method="post" id="userForm"
		action="clm/parameter/updateClaimPhone_CLM_maintainPhoneAction.action?menuId=${menuId}"
		class="pageForm required-validate"
		onsubmit="return validateCallback(this, dialogAjaxDone)">
		<input type="hidden" name="claimPhoneServiceVO.phoneServiceId" value="${claimPhoneServiceVO.phoneServiceId}" />
		
			<dl>
				<dt>机构代码</dt>
				<dd>
					<input name="claimPhoneServiceVO.organCode" id="branchCode" value="${claimPhoneServiceVO.organCode }" type="text" class="organ" clickId="menuBtnAddPhoneUpdate" showOrgName="branchnameAddPhoneUpdate" needAll="true" data-grade-in="01,02,03" />
					<a id="menuBtnAddPhoneUpdate" class="btnLook" href="#" style="position: relative;"></a>
				</dd>
			</dl>
			<dl>
				<dt>机构名称</dt>
				<dd>
					<input id="branchnameAddPhoneUpdate" name="claimPhoneServiceVO.orgName" value="${claimPhoneServiceVO.orgName }" type="text" readOnly />
				</dd>
			</dl>
			<dl>
				<dt>理赔服务电话</dt>
				<dd>
					<input  name="claimPhoneServiceVO.servicePhone"
						value="${claimPhoneServiceVO.servicePhone}" class="required"  maxlength="20" id="branchPhone" onBlur="checkPhoneAndMobileNew(this,'理赔服务电话')" />
				</dd>
			</dl>
			<dl>
				<dt>email地址</dt>
				<dd>
					<input type="text" name="claimPhoneServiceVO.email"
						value="${claimPhoneServiceVO.email}" />
						<DIV >@newchinalife.com</DIV>
				</dd>
			</dl>
			
			
		<div class="formBarButton">
			<ul>
				<li><button class="but_blue"  type="submit" id="validateBtn">保存</button></li>
				<li><button class="but_blue" type="button" onclick="$.pdialog.closeCurrent();" id="userUpdateCancel">取消</button></li>
			</ul>
		</div>
		
	</form>
</div>
