//初始化查询审核、审批普通、审批疑难权限级别
$(function() {
var taskType = "3";
$.ajax({ 
	url : "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType="+taskType+"&permissionTypeVO.flag=3",
	global : false,
	type : "POST",
	dataType : "json",
	success : function (json){
		var permissionInfoVOList = eval(json);
		var interHTML = "<option value=''>请选择</option>";
		$.each(permissionInfoVOList, function(index, val) {
			var permissionName = val.permissionName;
			interHTML = interHTML +"<option value='"+permissionName+"'>" + permissionName + "</option>";
		});
		interHTML = interHTML + "<option value='CLM-MAX'>人员最高权限</option>";
		$("select#auditIdStart", navTab.getCurrentPanel()).loadMyComboxOptions(interHTML);
		$("select#auditIdEnd", navTab.getCurrentPanel()).loadMyComboxOptions(interHTML);
		$("select#auditIdEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	} 
});
//查询审批权限
var taskType = "4";
$.ajax({ 
	url : "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType="+taskType+"&permissionTypeVO.flag=4",
	type : "POST",
	dataType : "json",
	success : function (json){
		var permissionInfoVOList = eval(json);
		var interHTML = "<option value=''>请选择</option>";
		$.each(permissionInfoVOList, function(index, val) {
			var permissionName = val.permissionName;
			interHTML = interHTML + "<option value='"+permissionName+"'>" + permissionName + "</option>";
		});
		interHTML = interHTML + "<option value='CLM-MAX'>人员最高权限</option>";
		$("select#approveIdStart", navTab.getCurrentPanel()).loadMyComboxOptions(interHTML);
		$("select#approveIdEnd", navTab.getCurrentPanel()).loadMyComboxOptions(interHTML);
		$("select#approveIdEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	} 
});

//查询审批权限（疑难）
$.ajax({ 
	url : "clm/handworkAssignTask/queryPermissionRegion_CLM_handworkAssignTaskAction.action?permissionTypeVO.taskType="+taskType+"&permissionTypeVO.difficultFlag=1"+"&permissionTypeVO.flag=4",
	type : "POST",
	dataType : "json",
	success : function (json){
		var permissionInfoVOList = eval(json);
		var interHTML = "<option value=''>请选择</option>";
		$.each(permissionInfoVOList, function(index, val) {
			var permissionName = val.permissionName;
			interHTML = interHTML + "<option value='"+permissionName+"'>" + permissionName + "</option>";
		});
		interHTML = interHTML + "<option value='CLM-MAX'>人员最高权限</option>";
		$("select#approveDifficultStart", navTab.getCurrentPanel()).loadMyComboxOptions(interHTML);
		$("select#approveDifficultEnd", navTab.getCurrentPanel()).loadMyComboxOptions(interHTML);
		$("select#approveDifficultEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	} 
});
});
//查询按钮
function queryClaimRoleServicing(){
	$("#claimRoleFormId", navTab.getCurrentPanel()).submit();
}
//radio查询详细信息
function queryClaimRoleDetails(k){
	var claimRoleId = $(k).next().val()
	$("#claimRoleId", navTab.getCurrentPanel()).val(claimRoleId);
	$("#saveClaimRoleInfoId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#claimRoleCode", navTab.getCurrentPanel()).attr("readOnly","readOnly");
	$("#claimRoleName", navTab.getCurrentPanel()).attr("readOnly","readOnly");
	$.ajax({ 
		url : "clm/parameter/queryAllClaimRoleServicing_CLM_claimRoleServicingAction.action?claimRoleServicingVO.claimRoleId="+claimRoleId,
		type : "POST",
		dataType : "json",
		success : function (json){
			var claimRoleList = eval(json);
			var claimRole = claimRoleList[0];
			$("#claimRoleCode", navTab.getCurrentPanel()).val(claimRole.claimRoleCode);
			$("#claimRoleCode", navTab.getCurrentPanel()).attr(claimRole.claimRoleCode);
			$("#claimRoleName", navTab.getCurrentPanel()).val(claimRole.claimRoleName);
			$("#claimRoleName", navTab.getCurrentPanel()).attr(claimRole.claimRoleName);
			$("#isWorking", navTab.getCurrentPanel()).selectMyComBox(claimRole.isWorking);
			$("#easyAuditPermission", navTab.getCurrentPanel()).selectMyComBox(claimRole.easyAuditPermission);
			$("#auditIdStart", navTab.getCurrentPanel()).selectMyComBox(claimRole.auditFrom);
			$("#auditIdEnd", navTab.getCurrentPanel()).selectMyComBox(claimRole.auditTo);
			if(claimRole.auditFrom == ""){
				$("#auditIdEnd", navTab.getCurrentPanel()).selectMyComBox("");
			}
			if(claimRole.auditFrom == "CLM-MAX" || claimRole.auditFrom == ""){
				$("#auditIdEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
			}else{
				$("#auditIdEnd", navTab.getCurrentPanel()).prev().removeAttr("disabled");
			}
			$("#approveIdStart", navTab.getCurrentPanel()).selectMyComBox(claimRole.approveFrom);
			$("#approveIdEnd", navTab.getCurrentPanel()).selectMyComBox(claimRole.approveTo);
			if(claimRole.approveFrom == ""){
				$("#approveIdEnd", navTab.getCurrentPanel()).selectMyComBox("");
			}
			if(claimRole.approveFrom == "CLM-MAX" || claimRole.approveFrom == ""){
				$("#approveIdEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
			}else{
				$("#approveIdEnd", navTab.getCurrentPanel()).prev().removeAttr("disabled");
			}
			$("#approveDifficultStart", navTab.getCurrentPanel()).selectMyComBox(claimRole.approveDifficultFrom);
			$("#approveDifficultEnd", navTab.getCurrentPanel()).selectMyComBox(claimRole.approveDifficultTo);
			if(claimRole.approveDifficultFrom == ""){
				$("#approveDifficultEnd", navTab.getCurrentPanel()).selectMyComBox("");
			}
			if(claimRole.approveDifficultFrom == "CLM-MAX" || claimRole.approveDifficultFrom == ""){
				$("#approveDifficultEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
			}else{
				$("#approveDifficultEnd", navTab.getCurrentPanel()).prev().removeAttr("disabled");
			}
			var claimTypeList = claimRole.claimTypeList.split(",");
			//先清空理赔类型
			$("#claimTypeId", navTab.getCurrentPanel()).find("input").each(function(){
				$(this).removeAttr("checked");
			});
			if(claimTypeList.length > 0){
				for(var i = 0; i < claimTypeList.length; i++){
					if(claimTypeList[i].trim() == "01"){
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(0)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "04") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(1)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "03") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(2)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "02") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(3)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "08") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(4)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "10") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(5)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "06") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(6)").attr("checked","checked");
					} else if (claimTypeList[i].trim() == "07") {
						$("#claimTypeId", navTab.getCurrentPanel()).find("input:eq(7)").attr("checked","checked");
					}
				}
			}
		} 
	});
}
function deleteClaimRole(k){
	$.ajax({
		'url':'clm/parameter/deletedClaimRole_CLM_claimRoleServicingAction.action?claimRoleServicingVO.claimRoleId='+k,
		'type':'post',
		'async':false,
		'success':function(){
			 alertMsg.info("删除成功");
			 navTab.openTab("400040","clm/parameter/claimRoleServicingInit_CLM_claimRoleServicingAction.action",{title:"角色权限管理"});
		 },
		 'error':function(){
			 alertMsg.error("删除失败,请联系管理员");
			 
		 }
	});
}
//添加按钮清空展示数据
function addClaimRoleInfo(){
	$("#claimRoleId", navTab.getCurrentPanel()).val("");
	$("#claimRoleCode", navTab.getCurrentPanel()).removeAttr("readOnly");
	$("#claimRoleName", navTab.getCurrentPanel()).removeAttr("readOnly");
	$("#saveClaimRoleInfoId", navTab.getCurrentPanel()).removeAttr("disabled");
	$("#claimTypeId", navTab.getCurrentPanel()).val(null);
	$("#claimRoleCode", navTab.getCurrentPanel()).val(null);
	$("#claimRoleName", navTab.getCurrentPanel()).val(null);
	$("#auditIdStart", navTab.getCurrentPanel()).selectMyComBox("");
	$("#auditIdEnd", navTab.getCurrentPanel()).selectMyComBox("");
	$("#auditIdEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	$("#approveIdStart", navTab.getCurrentPanel()).selectMyComBox("");
	$("#approveIdEnd", navTab.getCurrentPanel()).selectMyComBox("");
	$("#approveIdEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	$("#approveDifficultStart", navTab.getCurrentPanel()).selectMyComBox("");
	$("#approveDifficultEnd", navTab.getCurrentPanel()).selectMyComBox("");
	$("#approveDifficultEnd", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	$("#claimTypeId", navTab.getCurrentPanel()).find("input").each(function(){
		$(this).removeAttr("checked");			
	});
}
//人员权限为最大或为空时
function maxPermission(k){
	var permissionLevel = $(k).val();
	if(permissionLevel == "CLM-MAX"){
		$(k).parent().parent().parent().parent().find("select:eq(1)").selectMyComBox("CLM-MAX");
		$(k).parent().parent().parent().parent().find("select:eq(1)").prev().attr("disabled","disabled");
	}else{
		$(k).parent().parent().parent().parent().find("select:eq(1)").prev().removeAttr("disabled");
	}
	if(permissionLevel == ""){
		$(k).parent().parent().parent().parent().find("select:eq(1)").selectMyComBox("");
		$(k).parent().parent().parent().parent().find("select:eq(1)").prev().attr("disabled","disabled");
	}
}
//保存按钮
function saveClaimRoleInfo(){
	var claimRoleCode = $("#claimRoleCode", navTab.getCurrentPanel()).val();
	var claimRoleName = $("#claimRoleName", navTab.getCurrentPanel()).val();
	var isWorking = $("#isWorking", navTab.getCurrentPanel()).val();
	var surveyItemNum=0;
	$("#claimTypeId", navTab.getCurrentPanel()).find("input[type='checkbox']").each(function(){
		if ($(this).is(':checked')) {
			surveyItemNum++;
		}
	});
	if(claimRoleCode == ""){
		alertMsg.error("请录入必录项");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#claimRoleCode", navTab.getCurrentPanel()).focus();
	    });
		return false;
	}else if(claimRoleName == ""){
		alertMsg.error("请录入必录项");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#claimRoleName", navTab.getCurrentPanel()).focus();
	    });
		return false;
	}
	var auditIdEnd = $("#auditIdEnd", navTab.getCurrentPanel()).val();
	var auditIdStart = $("#auditIdStart", navTab.getCurrentPanel()).val();
	var approveIdEnd = $("#approveIdEnd", navTab.getCurrentPanel()).val();
	var approveIdStart = $("#approveIdStart", navTab.getCurrentPanel()).val();
	var approveDifficultEnd = $("#approveDifficultEnd", navTab.getCurrentPanel()).val();
	var approveDifficultStart = $("#approveDifficultStart", navTab.getCurrentPanel()).val();
	if(auditIdStart != "" && (auditIdEnd == ""
			|| (auditIdEnd != "CLM-MAX" && auditIdEnd < auditIdStart))){
		alertMsg.error("权限止应大于或等于权限起");
		return false;
	}
	if(approveIdStart != "" && (approveIdEnd == ""
		|| (approveIdEnd != "CLM-MAX" && approveIdEnd < approveIdStart))){
		alertMsg.error("权限止应大于或等于权限起");
		return false;
	}
	if(approveDifficultStart != "" && (approveDifficultEnd == ""
		|| (approveDifficultEnd != "CLM-MAX" && approveDifficultEnd < approveDifficultStart))){
		alertMsg.error("权限止应大于或等于权限起");
		return false;
	}
	if(isWorking == 1 && surveyItemNum == 0){
		alertMsg.error("接收任务状态为开，理赔类型至少选择一个!");
		return false;
	}
	$("#saveForm", navTab.getCurrentPanel()).submit();
}
function claimRoleCallBack(json){
	if (json.statusCode == DWZ.statusCode.ok) {
		$("#queryClaimRoleId", navTab.getCurrentPanel()).click();
		alertMsg.correct(json.message);
	} else {
		alertMsg.error(json.message);
	}
}