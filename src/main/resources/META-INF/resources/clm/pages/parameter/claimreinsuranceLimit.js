//查询理赔再保限额数据
function queryClaimReinsuranceLimit(){
	//为全部时将值清空。
	var claimtypeCode = $("#claimTypeId" ,navTab.getCurrentPanel()).val();
	if(claimtypeCode == "all"){
		$("#claimTypeHiddenId", navTab.getCurrentPanel()).val("");
		claimtypeCode = "";
	} else {
		$("#claimTypeHiddenId", navTab.getCurrentPanel()).val(claimtypeCode);
	}
	$("#claimReinsuranceLimitFormId",navTab.getCurrentPanel()).attr("action","clm/parameter/queryClaimReinsuranceLimit_CLM_claimReinsuranceLimitAction.action");
	$("#claimReinsuranceLimitFormId",navTab.getCurrentPanel()).submit();
}


//保存理赔再保限额数据
function saveClaimReinsuranceLimitForm(){
	//为全部时将值清空。
	var claimtypeCode = $("#claimTypeId" ,navTab.getCurrentPanel()).val();
	if(claimtypeCode == "all"){
		$("#claimTypeHiddenId", navTab.getCurrentPanel()).val("");
		claimtypeCode = "";
	} else {
		$("#claimTypeHiddenId", navTab.getCurrentPanel()).val(claimtypeCode);
	}
	//校验理赔类型和额度（万）是否都有值。
	var trs = $("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr");
	for(var i = 0; i < trs.length; i++){
		//理赔类型校验
		if(trim($("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("select").val()) == "" || trim($("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("select").val()) == null){
			alertMsg.error("理赔类型不可为空！");
			return false;
		}
		//额度（万）校验
		if(trim($("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(2)").find("input").val()) == "" || trim($("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(2)").find("input").val()) == null){
			alertMsg.error("额度（万）不可为空！");
			return false;
		}
	}
	$("#claimReinsuranceLimitFormId",navTab.getCurrentPanel()).attr("action","clm/parameter/saveClaimReinsuranceLimit_CLM_claimReinsuranceLimitAction.action");
	$("#claimReinsuranceLimitFormId",navTab.getCurrentPanel()).submit();
}

//删除理赔再保限额数据
function deleteClaimReinsuranceLimitForm(obj){
	var reinsuranceLimitId = $(obj ,navTab.getCurrentPanel()).parent().parent().find("td").find("input").val();
	if( reinsuranceLimitId != ""){
		//为全部时将值清空。
		var claimtypeCode = $("#claimTypeId" ,navTab.getCurrentPanel()).val();
		if(claimtypeCode == "all"){
			$("#claimTypeHiddenId", navTab.getCurrentPanel()).val("");
			claimtypeCode = "";
		} else {
			$("#claimTypeHiddenId", navTab.getCurrentPanel()).val(claimtypeCode);
		}
		$("#claimReinsuranceLimitFormId",navTab.getCurrentPanel()).attr("action","clm/parameter/deleteClaimReinsuranceLimitForm_CLM_claimReinsuranceLimitAction.action?claimReinsuranceLimitVO.reinsuranceLimitId="+reinsuranceLimitId);
		$("#claimReinsuranceLimitFormId",navTab.getCurrentPanel()).submit();
	} else {
		$(obj ,navTab.getCurrentPanel()).parent().parent().remove();
		//删除数据后将列表的数据从新编号。
		var trslength = $("#claimReinsuranceLimitTbodyId" ,navTab.getCurrentPanel()).find("tr");
		for(var i = 0; i < trslength.length; i++){
			if($("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input").val() == "" || $("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input").val() == null){
				$("#claimReinsuranceLimitTbodyId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").text(i+1);
			}
		}
	}
}

//控制额度输入框输入整数和保留两位小数。
function claimReinsuranceLimitChange(obj){
	obj.value=obj.value.replace(/[^\d\.]/g,'');
	//判断小数点后有几位，只保留俩位
	var values = obj.value.split(".");
	if(values[1].length > 0){
		if(values[1].length > 2 || values.length > 2){
			$(obj).val(values[0]+"."+values[1].substring(0,2));
		}
	}
}

