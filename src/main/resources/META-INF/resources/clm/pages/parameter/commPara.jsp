<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript">
//格式化时间
Date.prototype.format = function(format) {
	var o = {
		"M+" : this.getMonth() + 1, // month
		"d+" : this.getDate(), // day
		"h+" : this.getHours(), // hour
		"m+" : this.getMinutes(), // minute
		"s+" : this.getSeconds(), // second
		"q+" : Math.floor((this.getMonth() + 3) / 3), // quarter
		"S" : this.getMilliseconds()
	// millisecond
	};
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (this.getFullYear() + "")
				.substr(4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp("(" + k + ")").test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
					: ("00" + o[k]).substr(("" + o[k]).length));
	return format;
};

//初始化置灰按钮
/* $("#saveBtn", navTab.getCurrentPanel()).attr("disabled","disabled");

$(".digits", navTab.getCurrentPanel()).on("change",function(){ 
	$("#saveBtn", navTab.getCurrentPanel()).removeAttr("disabled");  
}); */


function getTime(timeValue){
	return new Date().format('yyyy-MM-dd');
}
/* function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
} */

function findParaTrace1(){
	var paraType = $("#paraType", navTab.getCurrentPanel()).val();

	$.ajax({
			type:'post',
			url:'clm/parameter/findParaTrace_CLM_commParaAction.action?paraType='+paraType,
			datatype:'json',
			global : false,
			success:function(data){ 
				
				var data = eval("(" + data + ")");
				var traceL = data[0].paraTraceList.length;
				$("#paraTraceTbody", navTab.getCurrentPanel()).find("tr").remove();
				for(var n=0;n<traceL;n++){
					var k = n+1;
					var type = data[0].paraTraceList[n].paraTypeCode;
					var paraTypeName = "";
					if(type=="1"){
						paraTypeName="预付比例";
					}else if(type=="2"){
						paraTypeName="分次给付提前抽档天数";
					}else if(type=="3"){
						paraTypeName="续期挂起需要发短信提醒客户的触发条件";
					}else if(type=="4"){
						paraTypeName="发起关怀提示服务人员天数";
					}
				 
					var startDate =  data[0].paraTraceList[n].startDate;
					var endDate ="";
					if(data[0].paraTraceList[n].finishDate!=null){
						endDate = data[0].paraTraceList[n].finishDate;
					}
					var insertHtml ="<tr align='center' style='width:100%'>"
									+"<td style='width:16.67%'>"+k+"</td>"
									+"<td style='width:16.67%'>"+paraTypeName+"</td>"
									+"<td style='width:16.67%'>"+data[0].paraTraceList[n].paraTypeValue+"</td>"
									+"<td style='width:16.67%'>"+startDate+"</td>"
									+"<td style='width:16.67%'>"+endDate+"</td>"
									+"<td style='width:16.67%'>"+data[0].paraTraceList[n].operator+"</td>"
									+"</tr>";
					$("#paraTraceTbody", navTab.getCurrentPanel()).append(
											insertHtml);						
				
				}
			},
			error:function(){
				alert("出错了！");
			}
	});  
}
$('#touchType').change(function() {
		var touchType = $(this).val();
		//1为下次缴费日期 2为报案日期。
		var touchDays1 = $("#touchDays1").val();//下次缴费日天数
		var touchDays2 = $("#touchDays2").val();//报案日天数
		var touchDays = $("#touchDays").val();
		if(touchType == '1' ){
			$("#touchDays").val(touchDays1);
		}else{
			$("#touchDays").val(touchDays2);
		}
	});
function changeValue(){
	var touchTypeR = $('#touchType').val();
	var touchDaysR = $("#touchDays").val();
	if(touchTypeR == '1' ){
		$("#touchDays1").val(touchDaysR);
	}else{
		$("#touchDays2").val(touchDaysR);
	}
}
/* 
      jQuery(function() {
          jQuery.fn.CloneTableHeader("paraTraceTable", "paraTraceTableDIV");
      });       //传入表和表所在的DIV的ID就OK了
 */

</script>


<div  layoutH="0">
	<div class="panelPageFormContent">
		<form action="clm/parameter/saveCommPara_CLM_commParaAction.action"
			method="post" name="form1" id="form1" class="form1 required-validate"
			onsubmit="return validateCallback(this, navTabAjaxDone)">
			<div>
						<div class="panelPageFormContent">
				<div class="divfclass">
				  <h1><img src="clm/images/tubiao.png">理赔公共参数</h1>
		        </div>
				
						<div class="divfclass">
						  <h1><img src="clm/images/tubiao.png">预付比例的配置</h1>
				        </div>
						
							<dl>
								<dt>预付比例</dt>
								<dd>
									<input type="text" name="commParaVO.advanceRatio"
										value="${commParaVO.advanceRatio }" class="digits required" />%
								</dd>
							</dl>
<!-- 							<table> -->
<!-- 								<tr> -->
<!-- 									<td>预付比例</td> -->
<!-- 									<td><input type="text" name="commParaVO.advanceRatio" -->
<%-- 										value="${commParaVO.advanceRatio }" class="digits required" />%</td> --%>
<!-- 								</tr> -->
<!-- 							</table> -->
						</div>
				<div  class="panelPageFormContent">
						<div class="divfclass">
							<h1><img src="clm/images/tubiao.png">分次给付提前抽档天数配置</h1>
					   </div>
						
							<dl>
								<dt>分次给付提前抽档天数</dt>
								<dd>
									<input type="text" name="commParaVO.instalmentDays"
										value="${commParaVO.instalmentDays }" class="digits required" />天
								</dd>
							</dl>
<!-- 							<table> -->
<!-- 								<tr> -->
<!-- 									<td>分次给付提前抽档天数</td> -->
<!-- 									<td><input type="text" name="commParaVO.instalmentDays" -->
<%-- 										value="${commParaVO.instalmentDays }" class="digits required" />天</td> --%>
<!-- 								</tr> -->
<!-- 							</table> -->
						</div>
				<div class="panelPageFormContent">
						<div class="divfclass">
							<h1><img src="clm/images/tubiao.png">续期挂起需要发短信提醒客户的触发条件</h1>
					   </div>
						
						<dl>
								<dt>触发方式</dt>
								<dd>
									<select class="combox title digits"   id="touchType" name="commParaVO.touchType">
										<option value="1">按下次缴费日期</option>
										<option value="2">按报案日期</option>
									</select>
								</dd>
								<input type="hidden" id="touchDays1" value="${commParaVO.touchDays }" />
								<input type="hidden" id="touchDays2" value="${commParaVO1.touchDays }"  />
								<dd>
									<input type="text" name="commParaVO.touchDays" value="${commParaVO.touchDays }" id="touchDays" class="digits required"/>天
								</dd>
							</dl>
<!-- 					<table> -->
<!-- 						<tr> -->
<!-- 							<td>触发方式</td> -->
<!-- 							<td> -->
<%-- 							<select class="selectToInput digits"   id="touchType" name="commParaVO.touchType"> --%>
<!-- 								<option value="1">按下次缴费日期</option> -->
<!-- 								<option value="2">按报案日期</option> -->
<%-- 							</select> --%>
<!-- 							</td> -->
<%-- 							<td><input type="text" name="commParaVO.touchDays" value="${commParaVO.touchDays }" class="digits required"/>天</td> --%>
<!-- 						</tr> -->
<!-- 					</table> -->
				</div>
				<div class="panelPageFormContent">
						<div class="divfclass">
							<h1><img src="clm/images/tubiao.png">发起关怀提示服务人员天数</h1>
					   </div>
						<div>  
							<dl>
								<dt>发起关怀提示服务人员天数</dt>
								<dd>
									<input type="text" name="commParaVO.careDays"
										value="${commParaVO.careDays }" class="digits required" />天
								</dd>
							</dl>
<!-- 							<table> -->
<!-- 								<tr> -->
<!-- 									<td>发起关怀提示服务人员天数</td> -->
<!-- 									<td><input type="text" name="commParaVO.careDays" -->
<%-- 										value="${commParaVO.careDays }" class="digits required" />天</td> --%>
<!-- 								</tr> -->

<!-- 							</table> -->
						</div>
				</div>
				<div class="panelPageFormContent">
					<dl>
						<dt>操作员</dt>
						<dd>
							<input type="text" name="commParaVO.operator"
								id="operator" value="${commParaVO.operator }" readonly>
						</dd>
					</dl>
					<dl>
						<dt>操作日期</dt>
						<dd>
							<input type="text" name="commParaVO.operateDate"
								id="operateDate" value="${commParaVO.operateDate}" readonly />
						</dd>
					</dl>
<!-- 					<table> -->
<!-- 						<tr> -->
<!-- 							<td>操作员</td> -->
<!-- 							<td><input type="text" name="commParaVO.operator" -->
<%-- 								id="operator" value="${commParaVO.operator }" readonly></td> --%>
<!-- 							<td>操作日期</td> -->
<!-- 							<td><input type="text" name="commParaVO.operateDate" -->
<%-- 								id="operateDate" value="${commParaVO.operateDate}" readonly /></td> --%>
<!-- 						</tr> -->
<!-- 					</table> -->
				</div>
			</div>
			
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1><img src="clm/images/tubiao.png">超期补偿天数</h1>
				</div>
					<dl>
						<dt>超期补偿天数</dt>
						<dd>
							<input type="text" name="commParaVO.overDueCompensationDays" value="${commParaVO.overDueCompensationDays }" class="digits required"/>天
						</dd>
					</dl>
<!-- 					<table> -->
<!-- 						<tr> -->
<!-- 							<td>超期补偿天数</td> -->
<%-- 							<td><input type="text" name="commParaVO.overDueCompensationDays" value="${commParaVO.overDueCompensationDays }" class="digits required"/>天</td> --%>
<!-- 						</tr> -->
						
<!-- 					</table> -->
				</div>
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1><img src="clm/images/tubiao.png">报案后自动撤销保单挂起天数</h1>
				</div>
					<dl>
						<dt>报案后自动撤销保单挂起天数</dt>
						<dd>
							<input type="text" name="commParaVO.autoPaHandUpDays" value="${commParaVO.autoPaHandUpDays }" class="digits required"/>天
						</dd>
					</dl>
<!-- 					<table> -->
<!-- 						<tr> -->
<!-- 							<td>报案后自动撤销保单挂起天数</td> -->
<%-- 							<td><input type="text" name="commParaVO.autoPaHandUpDays" value="${commParaVO.autoPaHandUpDays }" class="digits required"/>天</td> --%>
<!-- 						</tr> -->
						
<!-- 					</table> -->
				</div>
			<div class="panelPageFormContent">
				<dl>
						<dt>操作员</dt>
						<dd>
							<input type="text" name="commParaVO.operator" id="operator" value="${commParaVO.operator }" readonly>
						</dd>
					</dl>
				<dl>
						<dt>操作日期</dt>
						<dd>
							<input type="text" name="commParaVO.operateDate" id="operateDate" value="${commParaVO.operateDate}" readonly/>
						</dd>
					</dl>
<!-- 			<table> -->
<!-- 				<tr> -->
<!-- 					<td>操作员</td> -->
<%-- 					<td><input type="text" name="commParaVO.operator" id="operator" value="${commParaVO.operator }" readonly></td> --%>
<!-- 					<td>操作日期</td> -->
<%-- 					<td><input type="text" name="commParaVO.operateDate" id="operateDate" value="${commParaVO.operateDate}" readonly/> --%>
<!-- 					</td> -->
<!-- 				</tr> -->
<!-- 			</table> -->
			</div>
			
			
			<div class="pageFormdiv">
					<button class="but_blue" onclick="changeValue()" type="submit" id="saveBtn">保存</button>
					<button class="but_blue" type="button" id="clearBtn" onclick="exit();">退出</button>
			 </div>
				
				</form>
				
			</div> 
			
		 
<div>
			
<!-- 			<fieldset> -->
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询修改轨迹</h1>
		   </div>
			
			<div class="panelPageFormContent" > 
			<dl>
				<dt>参数类型</dt>
				<dd>
					<select class="combox title"  id="paraType" name="paraType" value="" >
					<!-- 	<option value="">全部</option> -->
						<option value="1">预付比例</option>
						<option value="2">分次给付提前抽档天数</option>
						<option value="3">续期挂起需发短信提醒客户的条件</option>
						<option value="4">发起关怀提示服务人员天数</option>
					</select>
				</dd>
			</dl>
			
		</div>
			<div class="pageFormdiv">
					<button type="button" class="but_blue"    id="findParaTrace"  onclick="findParaTrace1();">轨迹查询</button>
			</div>
		<div  id="paraTraceTableDIV" class="tabdivclassbr main_tabdiv ">
			<table id="paraTraceTable" class="list" style="width: 100%;">
				<thead>
					<tr align="center">
						<th nowrap>序号</th>
						<th nowrap>参数类型</th>
						<th nowrap>参数值</th>
						<th nowrap>创建日期</th>
						<th nowrap>结束日期</th>
						<th nowrap>操作员</th>
					</tr>
				</thead>
				<tbody id="paraTraceTbody">
				
				</tbody>
			</table>
		</div>
<!-- 	</fieldset> -->
</div>
</div>