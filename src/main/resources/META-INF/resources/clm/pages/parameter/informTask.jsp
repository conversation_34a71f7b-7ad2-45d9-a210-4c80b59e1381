<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">

	
</script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript">

//
/* function applyCode() {
		if ($("#applyCode", navTab.getCurrentPanel()).val() != ""
	&& $("#applyCode", navTab.getCurrentPanel()).val() != null 
	&& $("#acceptCode", navTab.getCurrentPanel()).val() == ""
		&& $("#acceptCode", navTab.getCurrentPanel()).val() == null) {
			$("#exitbtn", navTab.getCurrentPanel()).removeAttr("disabled", false);
			$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled", false);
		}
} */

//
/* function acceptCode() {
	if ($("#applyCode", navTab.getCurrentPanel()).val() != ""
		&& $("#applyCode", navTab.getCurrentPanel()).val() != null 
		&& $("#acceptCode", navTab.getCurrentPanel()).val() == ""
			&& $("#acceptCode", navTab.getCurrentPanel()).val() == null) {
				$("#exitbtn", navTab.getCurrentPanel()).removeAttr("disabled", false);
				$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled", false);
			}
} */

//被保人姓名
function addCustomerNames() {
	var customerId = $("#customerNames", navTab.getCurrentPanel()).val();

	$.ajax({
		'type':'post',
		'url':'clm/parameter/findInsureListsTask_CLM_bfSurveyHITaskManageAction.action?claimHiTaskVO.customerId='+customerId,
		'type':'post',
		'datatype':'json',
		'success':function(data){
		    var datas=eval("("+data+")");
		    $("#customerCertType", navTab.getCurrentPanel()).val(datas[0].customerCertType);
		    $("#customerCertiCode", navTab.getCurrentPanel()).val(datas[0].customerCertiCode);
		    $("#customerGender", navTab.getCurrentPanel()).val(datas[0].customerGenderStr);
		    $("#customerBirthday", navTab.getCurrentPanel()).val(datas[0].customerBirthdayStr); 
			},
		'error':function(){
			alert("出错了！");
		}});
	/* $("#saveClaimHiTask", navTab.getCurrentPanel()).attr("action","clm/parameter/findInsureListsTask_CLM_bfSurveyHITaskManageAction.action");
			$("#saveClaimHiTask", navTab.getCurrentPanel()).submit(); */
}

//发起增补告知
function SaveAndCheckOutputData() {
 	if ($("#customerNames", navTab.getCurrentPanel()).val() == ""
		||$("#customerNames", navTab.getCurrentPanel()).val() == "0"
		|| $("#customerNames", navTab.getCurrentPanel()).val() == null) {
	alertMsg.info("请选择被保险人。");
	return;
}
	   if ($("#userVOs", navTab.getCurrentPanel()).val() == "0"
		|| $("#userVOs", navTab.getCurrentPanel()).val() == null
		|| $("#userVOs", navTab.getCurrentPanel()).val() == "") {
	alertMsg.info("请选择受理人。");
	return;
}   
$("#saveClaimHiTask", navTab.getCurrentPanel()).attr("action","clm/parameter/saveAndCheckOutputDataTask_CLM_bfSurveyHITaskManageAction.action");
$("#saveClaimHiTask", navTab.getCurrentPanel()).submit();
}
//提交
function updateAppInfoCLM() {
	var applyCode = $("#applyCode", navTab.getCurrentPanel()).val();
    var acceptCode = $("#acceptCode", navTab.getCurrentPanel()).val();
	$.ajax({
		'type':'post',
		'url':'clm/parameter/updateAppInfoCLM_CLM_bfSurveyHITaskManageAction.action?claimHiTaskVO.applyCode='+applyCode+"&claimHiTaskVO.acceptCode="+acceptCode,
		'datatype':'json',
		'data':$("#saveClaimHiTask").serialize(),
		'success':function(data){
				var data = eval("(" + data + ")");
				if(data.statusCode==200){
					alertMsg.info(data.message)
				}
			},
		'error':function(){
			alert("出错了！");
		}});
	$("#exitbtn", navTab.getCurrentPanel()).attr("disabled",true);
	$("#saveRule", navTab.getCurrentPanel()).attr("disabled",true);
}

/**
 * 提交表单
 */
function dateUpload() {
		$.ajax({
  			'type':'post',
  			'url':'clm/parameter/dateUpload_CLM_bfSurveyHITaskManageAction.action',
  			'datatype':'json',
  			'data':$("#saveClaimHiTask").serialize(),
  			'success':function(data){
  				var data = eval("(" + data + ")");
  				if(data.statusCode==200){
  					window.open(data.message);
  					/* alertMsg.confirm("是否扫描成功?",{
  						okCall:function(){
  							setTimeout("queryImageScan()",3000);
  						}
  					}); */
  				}
  			},
  			'error':function(){
  				alert("出错了！");
  			}});
  	}

	//新增按钮
	function insert() {
		$("#saveApplySurvey").show();
		$("#saveApplySurvey").find(".main_foldContent").slideDown();
		if ($("#main_3", navTab.getCurrentPanel()).find("#two",
				navTab.getCurrentPanel()).hasClass("main_plus")) {
			$("#main_3", navTab.getCurrentPanel()).find("h5",
					navTab.getCurrentPanel()).click();
		}
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled", false);
		cleanSave();
	};

	//保存按钮 
	function saveSet() {
		if ($("#intervalSymbol", navTab.getCurrentPanel()).val() == ""
				|| $("#intervalSymbol", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险时间距离生效时间计算符号不能为空");
			return;
		}
		if ($("#intervalNum", navTab.getCurrentPanel()).val() == ""
				|| $("#intervalNum", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险时间距离生效时间不能为空");
			return;
		}
		if ($("#intervalType", navTab.getCurrentPanel()).val() == ""
				|| $("#intervalType", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险时间距离生效时间类型不能为空");
			return;
		}
		if ($("#amountSymbol", navTab.getCurrentPanel()).val() == ""
				|| $("#amountSymbol", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("保额计算符号不能为空");
			return;
		}
		if ($("#amount", navTab.getCurrentPanel()).val() == ""
				|| $("#amount", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("保额不能为空");
			return;
		}
		if ($("#accReason", navTab.getCurrentPanel()).val() == ""
				|| $("#accReason", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险原因不能为空");
			return;
		}
		if ($("#claimType", navTab.getCurrentPanel()).val() == ""
				|| $("#claimType", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("理赔类型不能为空");
			return;
		}
		$("#saveSurvey", navTab.getCurrentPanel()).submit();
		$("#saveApplySurvey", navTab.getCurrentPanel()).hide();
	};



	
</script>

<div layouth="36" id="saveSurveyId">

	<form id="pagerForm" method="post"
		action="pa/claimBfSurveyPlan/search_PA_claimBfSurveyPlanAction.action">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	
	<div id="claimHiTask">
	<div class="formBarButton main_bottom">
			<ul>
				<li>
					<button class="but_blue" type="button" onclick="SaveAndCheckOutputData()"
						id="saveRule"
						<s:if test="claimHiTaskVO.applyCode != null">disabled="disabled"</s:if>>发起增补告知</button>
				</li>
				<li>
					<button class="but_blue" type="button" id="exitbtn"
						onclick="exit()">退出</button>
				</li>
			</ul>
		</div>
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">增补告知信息
			</h1>
		</div>
		<div class="pageFormInfoContent">
			<div>
				<form id="saveClaimHiTask"
					action="clm/parameter/findInsureListsTask_CLM_bfSurveyHITaskManageAction.action"
					onsubmit="return navTabSearch(this)" rel="saveClaimHiTask"
					method="post">
					<input name="claimHiTaskVO.listId" type="hidden" id="claimHiTaskVOlistId" value="${claimHiTaskVO.listId }"/>
					<input type="hidden" name="claimHiTaskVO.policyCode" id="policyCode" value="${claimHiTaskVO.policyCode }">
					<dl>
						<dt>保全申请号</dt>
						<dd>
							<input name="claimHiTaskVO.applyCode" type="text"
								id="applyCode" disabled="disabled"
								value="<s:property value="claimHiTaskVO.applyCode"/>" />
						</dd>
					</dl>
					<dl>
						<dt>保全受理号</dt>
						<dd>
							<input name="claimHiTaskVO.acceptCode" type="text" disabled="disabled"
								id="acceptCode" value="<s:property value="claimHiTaskVO.acceptCode"/>" />
						</dd>
					</dl>
					<dl>
						<dt>保全项目</dt>
						<dd>
							<input name="claimHiTaskVO.casItem" type="text" disabled="disabled"
								id="casItem" value="增补告知" />
						</dd>
					</dl>
					<dl>
						<dt>被保人姓名</dt>
							<dd>
							<select name="claimHiTaskVO.customerId" 
								class="combox" id="customerNames" onChange="addCustomerNames()">
								<s:if test="claimHiTaskVO.customerName == null ">
									<option value="0">请选择</option>
								</s:if>
								<s:if test="claimHiTaskVO.customerName != null ">
									<option value="${claimHiTaskVO.customerId }" >${claimHiTaskVO.customerName }</option>
								</s:if>
								<s:if test="claimHiTaskVO.customerName == null ">
									<s:iterator value="claimHiTaskVO.customerVOs" status="var">
										<option value="${customerIdStr }" >${customerName }</option>
									</s:iterator>
								</s:if>
							</select>
						</dd>
					</dl>
					<dl>
						<dt>证件类型</dt>
						<dd>
							<input readonly = "true" name="claimHiTaskVO.customerCertType" type="text"
								id="customerCertType" disabled="disabled"
								value="<s:property value="claimHiTaskVO.customerCertType"/>" />
						</dd>
					</dl>
					<dl>
						<dt>证件号码</dt>
						<dd>
							<input readonly = "true" name="claimHiTaskVO.customerCertiCode" type="text"
								id="customerCertiCode" disabled="disabled"
								value="<s:property value="claimHiTaskVO.customerCertiCode"/>"/>
						</dd>
					</dl>
					<dl>
						<dt>出生日期</dt>
						<dd>
							<input readonly = "true" class="combox" name="claimHiTaskVO.customerBirthdayStr" type="text"
								id="customerBirthday" disabled="disabled"
								value="<s:property value="claimHiTaskVO.customerBirthdayStr"/>" format='yyyy-MM-dd'/>
						</dd>
					</dl>
					<dl>
						<dt>性别</dt>
						<dd>
							<input readonly = "true" name="claimHiTaskVO.customerGenderStr" type="text"
								id="customerGender" disabled="disabled"
								value="<s:property value="claimHiTaskVO.customerGenderStr"/>"/>
						</dd>
					</dl>
					<dl>
						<dt>申请提交日期</dt>
						<dd>
							<input readonly = "true" name="claimHiTaskVO.applyConfirmTimeStr" disabled="disabled"
							value="<s:property value="claimHiTaskVO.applyConfirmTimeStr"/>"
							 type="text" id="applyConfirmTimeStr" /> 
						</dd>
					</dl>
					<dl>
						<dt>申请方式</dt>
						<dd>
							<input name="claimHiTaskVO.intervalNum" type="text" disabled="disabled"
								id="intervalNum" value="其他内容转办"  />
						</dd>
					</dl>
					<dl>
						<dt>受理人姓名</dt>
							<dd>
							<select name="claimHiTaskVO.acceptorName" 
								class="combox" id="userVOs" >
								<s:if test="claimHiTaskVO.acceptorName == null ">
									<option value="0">请选择</option>
								</s:if>
								<s:if test="claimHiTaskVO.acceptorName != null ">
									<option value="${claimHiTaskVO.acceptorName }" >${claimHiTaskVO.acceptorName }</option>
								</s:if>
								<s:if test="claimHiTaskVO.acceptorName == null ">
									<s:iterator value="claimHiTaskVO.userVOs" status="var">
										<option value="${userName }" >${userName }</option>
									</s:iterator>
								</s:if>
							</select>
						</dd>
					</dl>
				</form>
			</div>
		</div>
	</div>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button class="but_blue" type="button" onclick="dateUpload()"
					id="saveRule" 
					<s:if test="claimHiTaskVO.applyCode == null">disabled="disabled"</s:if>>资料上载</button>
			</li>
			<li>
				<button  class="but_blue" type="button" id="exitbtn" onclick="updateAppInfoCLM()"
				<s:if test="claimHiTaskVO.applyCode == null">disabled="disabled"</s:if>
					>提交</button>
			</li>
		</ul>
	</div>




