<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript">
	
</script>

<div class="pageContent" id="insureListJsp">
	<div style="background: #fafafa" id="allForce">
		<!-- 查询访问路径 -->
		<%-- <form id="pagerForm" method="post"
			action="clm/parameter/queryApplyMbSurTrace_cLM_claimIntelPortraitConfigAction.action">
			<input type="hidden" name="pageNum" vaule="${currentPage1.pageNo} " />
			<input type="hidden" name="numPerPage"
				value="${currentPage1.pageSize}" />
		</form> --%>


		<s:if test="imageFlag != null">
				<div id="main_1">
					<ul class="main_ul">
						<li class="clearfix fold">
							<h5 hasborder="true">
								<b id="two" class="main_plus"></b><span>投保必调配置轨迹查询</span>
							</h5>
							<div class="main_foldContent" style="display: none;">
								<div class="main_bqtabdivbr">
									<div class="panelPageFormContent">
									<form id="selectSurceyLog"
										action="clm/parameter/queryApplyMbSurTrace_CLM_claimIntelPortraitConfigAction.action"
										onsubmit="return navTabSearch(this,'insureListSurceyLog')"
										class="pagerForm required-validate" rel="pagerForm" method="post">
											<div class="pageFormInfoContent">
												<dl>
													<dt>
														<font></font>查询起期
													</dt>
													<dd>
														<input id="startTime" type="expandDateYMD"
															name="claimApplySurveyLogVO.startTime"
															value="<s:date name='claimApplySurveyLogVO.startTime' format='yyyy-MM-dd'/>" />
														<a class="inputDateButton" href="javascript:;">选择</a>
													</dd>
												</dl>
												<dl>
													<dt>
														<font></font>查询止期
													</dt>
													<dd>
														<input id="endTime" type="expandDateYMD"
															name="claimApplySurveyLogVO.endTime"
															value="<s:date name='claimApplySurveyLogVO.endTime' format='yyyy-MM-dd'/>" />
														<a class="inputDateButton" href="javascript:;">选择</a>
													</dd>
												</dl>
												<dl style="width: 32%">
													<dt class="searchWhere">出险原因</dt>
													<dd style="width: 60%">
														<Field:codeTable cssClass="notuseflagDelete combox title" id="accReasonId"
															name="claimApplySurveyLogVO.accReason"
															value="${claimApplySurveyLogVO.accReason}"
															tableName="APP___CLM__DBUSER.T_CLAIM_NATURE"
															whereClause="code in (1,2)" nullOption='true' 
															orderBy="code" />
													</dd>
												</dl>
												<dl style="width: 32%">
													<dt style="width: 32%">理赔类型</dt>
													<dd style="width: 60%">
														<Field:codeTable cssClass="combox title" id="claimTypeId"
															name="claimApplySurveyLogVO.claimType"
															value="${claimApplySurveyLogVO.claimType}"
															tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"
															whereClause="code not in (11)" nullOption='true'
															orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)" />
													</dd>
												</dl>
												<div class="pageFormdiv">
													<button type="button" class="but_blue"
														onclick="selectSurceyLog();">查询</button>
												</div>
											</div>
										</form>
										<h5 hasborder="true">
										<b id="two" class="main_plus"></b><span>投保必调配置轨迹</span>
										</h5>
										<div id="insureListSurceyLog">
											<div class="main_bqtabdivbr">
												<table class="list" id="surveyApplyOperTable" width="100%">
													<thead>
														<tr>
															<th nowrap>出险时间距离生效时间计算符号</th>
															<th nowrap>出险时间距离生效时间</th>
															<th nowrap>出险时间距离生效时间类型</th>
															<th nowrap>保额计算符号</th>
															<th nowrap>保额（万元）</th>
															<th nowrap>出险原因</th>
															<th nowrap>理赔类型</th>
															<th nowrap>操作</th>
															<th nowrap>提交人</th>
															<th nowrap>提交日期</th>
														</tr>
													</thead>
													<tbody>
														<s:if test="imageFlag != null">
															<tr>
																<td colspan="100">
																	<div class="noRueryResult">请选择条件查询数据！</div>
																</td>
															</tr>
														</s:if>
														<s:elseif
															test="currentPage1.PageItems == null || currentPage1.PageItems.size()==0">
															<tr>
																<td colspan="100">
																	<div class="noRueryResult">没有符合条件的查询结果！</div>
																</td>
															</tr>
														</s:elseif>
														<s:iterator value="currentPage1.pageItems" status="st">
															<tr align="center" target="listIdInfo">
																<td align="center" style="word-break: break-all;">${intervalSymbol}</td>
																<td align="center" style="word-break: break-all;">${intervalNum}</td>
																<td align="center" style="word-break: break-all;">${intervalType}</td>
																<td align="center" style="word-break: break-all;">${amountSymbol}</td>
																<td align="center" style="word-break: break-all;">${amount}</td>
																<td align="center" style="word-break: break-all;">${accReasonStr}</td>
																<td align="center" style="word-break: break-all;">${claimTypeStr}</td>
																<td align="center" style="word-break: break-all;">${operationTypeStr}</td>
																<td align="center" style="word-break: break-all;">${submitUserStr}</td>
																<td align="center" style="word-break: break-all;"><s:date
																		name='submitDate' format='yyyy-MM-dd' /></td>
															</tr>
														</s:iterator>
													</tbody>
												</table>
												<div class="panelBar">
													<div class="pages">
														<span>显示</span>
														<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
															name="select"
															onchange="navTabPageBreak({numPerPage:this.value},'insureSurceyLog')"
															value="currentPage1.pageSize">
														</s:select>
														<span>条，共${currentPage1.total}条</span>
													</div>
													<div class="pagination" targetType="navTab"
														rel="insureSurceyLog" totalCount="${currentPage1.total}"
														numPerPage="${currentPage1.pageSize}" pageNumShown="20"
														currentPage="${currentPage1.pageNo}"></div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</li>
					</ul>
				</div>
		</s:if>
		<s:else>
			<form id="selectSurceyLog"
				action="clm/parameter/queryApplyMbSurTrace_CLM_claimIntelPortraitConfigAction.action"
				onsubmit="return navTabSearch(this,'insureListSurceyLog')"
				rel="pagerForm" method="post" class="pagerForm required-validate">
				<div id="main_1" class="main_borderbg">
					<ul class="main_ul">
						<li class="clearfix fold">
							<h5 hasborder="true">
								<b id="two" class="main_minus"></b><span>投保必调配置轨迹查询</span>
							</h5>
							<div class="main_foldContent">
								<div class="main_bqtabdivbr">
									<div class="panelPageFormContent">
										<div class="pageFormInfoContent">
											<dl>
												<dt>
													<font>* </font>查询起期
												</dt>
												<dd>
													<input id="startTime" type="expandDateYMD"
														name="claimApplySurveyLogVO.startTime"
														value="<s:date name='claimApplySurveyLogVO.startTime' format='yyyy-MM-dd'/>" />
													<a class="inputDateButton" href="javascript:;">选择</a>
												</dd>
											</dl>
											<dl>
												<dt>
													<font>* </font>查询止期
												</dt>
												<dd>
													<input id="endTime" type="expandDateYMD"
														name="claimApplySurveyLogVO.endTime"
														value="<s:date name='claimApplySurveyLogVO.endTime' format='yyyy-MM-dd'/>" />
													<a class="inputDateButton" href="javascript:;">选择</a>
												</dd>
											</dl>
											<dl style="width: 32%">
												<dt class="searchWhere">出险原因</dt>
												<dd style="width: 60%">
													<Field:codeTable cssClass="notuseflagDelete combox title" id="accReasonId"
														name="claimApplySurveyLogVO.accReason"
														value="${claimApplySurveyLogVO.accReason}"
														tableName="APP___CLM__DBUSER.T_CLAIM_NATURE"
														whereClause="code in (1,2)" nullOption='true'
														orderBy="code" />
												</dd>
											</dl>
											<dl style="width: 32%">
												<dt style="width: 32%">理赔类型</dt>
												<dd style="width: 60%">
													<Field:codeTable cssClass="combox title" id="claimTypeId"
														name="claimApplySurveyLogVO.claimType"
														value="${claimApplySurveyLogVO.claimType}"
														tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"
														whereClause="code not in (11)" nullOption='true'
														orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)" />
												</dd>
											</dl>
											<div class="pageFormdiv">
												<button type="button" class="but_blue"
													onclick="selectSurceyLog();">查询</button>
											</div>
										</div>
										<div id="insureListSurceyLog">
											<div class="main_bqtabdivbr">
												<table class="list" id="surveyApplyOperTable" width="100%">
													<thead>
														<tr>
															<th nowrap>出险时间距离生效时间计算符号</th>
															<th nowrap>出险时间距离生效时间</th>
															<th nowrap>出险时间距离生效时间类型</th>
															<th nowrap>保额计算符号</th>
															<th nowrap>保额（万元）</th>
															<th nowrap>出险原因</th>
															<th nowrap>理赔类型</th>
															<th nowrap>操作</th>
															<th nowrap>提交人</th>
															<th nowrap>提交日期</th>
														</tr>
													</thead>
													<tbody>
														<s:if test="imageFlag != null">
															<tr>
																<td colspan="100">
																	<div class="noRueryResult">请选择条件查询数据！</div>
																</td>
															</tr>
														</s:if>
														<s:elseif
															test="currentPage1.PageItems == null || currentPage1.PageItems.size()==0">
															<tr>
																<td colspan="100">
																	<div class="noRueryResult">没有符合条件的查询结果！</div>
																</td>
															</tr>
														</s:elseif>
														<s:iterator value="currentPage1.pageItems" status="st">
															<tr align="center" target="listIdInfo">
																<td align="center" style="word-break: break-all;">${intervalSymbol}</td>
																<td align="center" style="word-break: break-all;">${intervalNum}</td>
																<td align="center" style="word-break: break-all;">${intervalType}</td>
																<td align="center" style="word-break: break-all;">${amountSymbol}</td>
																<td align="center" style="word-break: break-all;">${amount}</td>
																<td align="center" style="word-break: break-all;">${accReasonStr}</td>
																<td align="center" style="word-break: break-all;">${claimTypeStr}</td>
																<td align="center" style="word-break: break-all;">${operationTypeStr}</td>
																<td align="center" style="word-break: break-all;">${submitUserStr}</td>
																<td align="center" style="word-break: break-all;"><s:date
																		name='submitDate' format='yyyy-MM-dd' /></td>
															</tr>
														</s:iterator>
													</tbody>
												</table>
												<div class="panelBar">
													<div class="pages">
														<span>显示</span>
														<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
															name="select"
															onchange="navTabPageBreak({numPerPage:this.value},'insureSurceyLog')"
															value="currentPage1.pageSize">
														</s:select>
														<span>条，共${currentPage1.total}条</span>
													</div>
													<div class="pagination" targetType="navTab"
														rel="insureSurceyLog" totalCount="${currentPage1.total}"
														numPerPage="${currentPage1.pageSize}" pageNumShown="20"
														currentPage="${currentPage1.pageNo}"></div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</li>
					</ul>
				</div>
			</form>
		</s:else>


	</div>