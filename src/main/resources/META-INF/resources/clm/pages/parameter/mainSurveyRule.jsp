<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<script type="text/javascript">
var organCode = '${claimSurveyRuleVO.organCode}';
 
if(organCode!=""){
 
	$("#surveyRuleOrganCode", navTab.getCurrentPanel()).val(organCode);
	//禁用操作释放
	var objone = $("#surveyRuleDIVOne", navTab.getCurrentPanel());
	//输入框 复选框 单选按钮  控制
	/* objone.find("input").each(function(){
		$(this).attr("",false);
	}); */
	var objtwo = $("#surveyRuleDIVTwo", navTab.getCurrentPanel());
	//按钮
	/* objtwo.find("button").each(function(){
		$(this).attr("",false);
	}); */
	//下拉框
	/* objtwo.find("select").each(function(){
		$(this).attr("",false);
	}); */
	var objthree = $("#surveyRuleDIVThree", navTab.getCurrentPanel());
	//下拉框
	/* objthree.find("select").each(function(){
		$(this).attr("",false);
	}); */
	//按钮
	/* objthree.find("button").each(function(){
		$(this).attr("",false);
	}); */
   
	$("#update", navTab.getCurrentPanel()).css("display","block");
	 
}
$(function() {
	var flag = $("#flag", navTab.getCurrentPanel()).val();
	var num = 0;
	$("#checkBody", navTab.getCurrentPanel()).find("tr").each(function(){
		num++;
	});
	if(flag == 1 && num == 0){
		$("#update", navTab.getCurrentPanel()).css("display","block");
	}
});

//=========查询按钮操作========
function searchSurveyRule(){
	var organCode=$("#branchCodeSurevy", navTab.getCurrentPanel()).val();
	if($.trim(organCode)==""){
	  	alertMsg.info("请输入机构代码！");
	  	$("#branchCodeSurevy", navTab.getCurrentPanel()).focus();
	  	return;
	}
	$("#form1", navTab.getCurrentPanel()).submit();
}
//========重置查询条件========
function resetAll() {
	$('#branchCodeSurevy', navTab.getCurrentPanel()).attr('value', '');
	$('#branchnameSurevy', navTab.getCurrentPanel()).attr('value', '');
	
}
//==========多行文本域选择多条数据操作============
function AppendItem(allMenu, menu, isAll) {
	for (j=0; j<document.getElementById(allMenu).length; j++){
		if (isAll == true || document.getElementById(allMenu).options[j].selected){
			//GET VALUE
			document.getElementById(allMenu).options[j].selected = false;
			//GET LENGTH
			DesLen = document.getElementById(menu).length;
			// NEW OPTION
			document.getElementById(menu).options[DesLen] = new Option(LTrim(document.getElementById(allMenu).options[j].text), document.getElementById(allMenu).options[j].value);
			document.getElementById(allMenu).remove(j);
			j--;
		}
	}
}
function LTrim(str) {
	var whitespace = new String("　 \t\n\r");
	var s = new String(str);
	if (whitespace.indexOf(s.charAt(0)) != -1) {
		var j = 0, i = s.length;
		while (j < i && whitespace.indexOf(s.charAt(j)) != -1) {
			j++;
		}
		s = s.substring(j, i);
	}
	return s;
}
//==============修改操作===============
function checkEdit(surveyRuleCode){
	$("#checkBody", navTab.getCurrentPanel()).find("tr#surveyRule"+surveyRuleCode).find("td:eq(0)").find("input:radio").attr("checked",true);
	var surveyRuleId = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("td").find("input:eq(1)").val();
	var surveyRuleCode = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("td").find("input:eq(2)").val();
	var polYear = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(3)").html();
	var beforePay = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(4)").html();
	var rate = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(5)").html();
	var isBlack = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(7)").find("input").val();
	var isDirect = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(8)").find("input").val();
	var isQuickClaim = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(9)").find("input").val();
	$("#surveyRuleId", navTab.getCurrentPanel()).val(surveyRuleId);
	$("#surveyRuleCode", navTab.getCurrentPanel()).val(surveyRuleCode);
	$("#polYear", navTab.getCurrentPanel()).val(polYear);
	$("#beforePay", navTab.getCurrentPanel()).val(beforePay);
	$("#rate", navTab.getCurrentPanel()).val(rate);
	$("#isBlack", navTab.getCurrentPanel()).val(isBlack);
	  if(isBlack == "1"){
		  $("#isBlack", navTab.getCurrentPanel()).attr("checked","checked");
	  }else{
		  $("#isBlack", navTab.getCurrentPanel()).removeAttr("checked");
	  }
	  $("#isDirect", navTab.getCurrentPanel()).val(isDirect);
	  if(isDirect == "1"){
		  $("#isDirect", navTab.getCurrentPanel()).attr("checked","checked");
	  }else{
		  $("#isDirect", navTab.getCurrentPanel()).removeAttr("checked");
	  }
	  $("#isQuickClaim", navTab.getCurrentPanel()).val(isQuickClaim);
	  if(isQuickClaim == "1"){
		  $("#isQuickClaim", navTab.getCurrentPanel()).attr("checked","checked");
	  }else{
		  $("#isQuickClaim", navTab.getCurrentPanel()).removeAttr("checked");
	  }
	//查询出险原因 回显数据
	$("#checkBody tr", navTab.getCurrentPanel()).find("td:eq(6)").each(function(){
		var accreason = $(this).find("input").val();
		if(accreason=="1"){
			$("input[name=accReason1]", navTab.getCurrentPanel()).attr("checked","checked");
		}
        if(accreason=="2"){
        	$("input[name=accReason2]", navTab.getCurrentPanel()).attr("checked","checked");
		}
	});
	var accReason1=$("#accReason1", navTab.getCurrentPanel()).val();
	var accReason2=$("#accReason2", navTab.getCurrentPanel()).val();
	if(accReason1=="1"){
		$("input[name=accReason1]", navTab.getCurrentPanel()).attr("checked","checked");
	}
	if(accReason2=="2"){
		$("input[name=accReason2]", navTab.getCurrentPanel()).attr("checked","checked");
	}
	getProductList();
	$("#update", navTab.getCurrentPanel()).css("display","block");
}

function getProductList(){
	var a=$("#surveyRuleCode", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/parameter/getProductAndOrganList_CLM_claimSurveyRuleAction.action',
		'type':'POST',
		'global':false,
		'async':true,
		'datatype':'json',
		'data':{"claimSurveyRuleVO.surveyRuleCode":$("#surveyRuleCode", navTab.getCurrentPanel()).val()},
		'success':function(data){	
			var data=eval("("+data+")");
			var orgList = data[0].org.length;
			var prodList = data[0].prod.length;
			//查询出来的值赋值
			$("#productList", navTab.getCurrentPanel()).find("option").remove();
			for(var i=0;i<prodList;i++){
				$("#productList", navTab.getCurrentPanel()).append("<option class='item' value='"+data[0].prod[i].busiProdCode+"'>"+data[0].prod[i].productAbbrName+"</option>");
			}
			$("#orgList", navTab.getCurrentPanel()).find("option").remove();
			for(var j=0;j<orgList;j++){
				$("#orgList", navTab.getCurrentPanel()).append("<option class='item' value='"+data[0].org[j].organCode+"'>"+data[0].org[j].organCode+"-"+data[0].org[j].organName+"</option>");
			}
			//同步左侧数据
		    $("select#allProduct", navTab.getCurrentPanel()).find("option").each(function(){
				var num = 0;
				for(var h=0;h<data[0].prod.length;h++){
					var busiProdCode = data[0].prod[h].busiProdCode;
					if($(this).val() == busiProdCode){
						num++;
					}
				}
				if(num > 0){
					$(this).remove();
				}
			});
			$("select#allOrgan", navTab.getCurrentPanel()).find("option").each(function(){
				var numorg = 0;
				for(var k=0;k<data[0].org.length;k++){
					if($(this).val() == data[0].org[k].organCode){
						numorg++;
					}
				}
				if(numorg > 0){
					$(this).remove();
				}
			});
		}
	});
}
//删除操作
function checkDelete(surveyRuleCode,obj) {
	alertMsg.confirm("确认删除吗?", {
		okCall : function() {
			$.ajax({
	  			'type':'post',
	  			'url':'clm/parameter/delSurveyRule_CLM_claimSurveyRuleAction.action?surveyRuleCode='+surveyRuleCode,
	  			'datatype':'json',
	  			'success':function(data){
	  				var json = eval("(" + data + ")");
	  				if(json.message=="删除成功"){
	  					$(obj).parent().parent().remove();
	  				  alertMsg.correct(json.message);
					}else{
					  alertMsg.error(json.message);
					}
	  				
	  			},
	  			'error':function(){
	  				alertMsg.error("操作失败！");
	  			}
	  	});
		}
	});
}
//==========重置操作==========
function clearForm(){
	//清空from表单的数据
	$("[name='ClaimSurveyRuleVO.polYear']", navTab.getCurrentPanel()).val("");
	$("[name='ClaimSurveyRuleVO.rate']", navTab.getCurrentPanel()).val("");
	$("[name='ClaimSurveyRuleVO.isBlack']", navTab.getCurrentPanel()).attr("checked",false);
	$("[name='ClaimSurveyRuleVO.isDirect']", navTab.getCurrentPanel()).attr("checked",false);
	$("[name='ClaimSurveyRuleVO.isQuickClaim']", navTab.getCurrentPanel()).attr("checked",false);
	$("[name='accReason1']", navTab.getCurrentPanel()).attr("checked",false);
	$("[name='accReason2']", navTab.getCurrentPanel()).attr("checked",false);
	$("[name='ClaimSurveyRuleVO.beforePay']", navTab.getCurrentPanel()).val("");
	//清空险种与机构的选中数据  
	moveselect("productList", "allProduct");
	moveselect("orgList", "allOrgan");
}
function moveselect(objId, targetId) {
	//获取要移动的下拉框对象
	var obj=document.getElementById(objId);
	//获取要移动到的下拉框对象
	var target = document.getElementById(targetId);
	//遍历需要移动的下拉框对象，将所有的option选中
	for(var i=0;i<obj.options.length;i++){
		obj.options[i].selected=true;
	}
	//判断要移动的下拉框的选中索引是否大于-1直到移动完为止
	while (obj.selectedIndex > -1) {
		//获取要移动数据的文本
		mot = obj.options[obj.selectedIndex].text;
		//获取要移动数据的value
		mov = obj.options[obj.selectedIndex].value;
		//在需要移动的下拉框对象中移除选中对象的索引
		obj.remove(obj.selectedIndex);
		//创建新的option标签
		var newoption = document.createElement("OPTION");
		//将移除的option的text,与value复制到该对象
		newoption.text = mot;
		newoption.value = mov;
		//在需要移动到的下拉框中将其加入
		target.add(newoption);
	}
}
//===========保存操作=========
function saveForm(){
	var countradionum = 0; //总行数
	var notchecknum = 0; //选中的行数
	$("#checkBody", navTab.getCurrentPanel()).find("tr").each(function(){
		countradionum++;
		if($(this).find("td:eq(0)").find("input:radio").prop('checked') != false){
			notchecknum = notchecknum + 1;
		}
	    });
	if(countradionum > 0){
		if (notchecknum == 0) {
	    	alertMsg.info("请先选择一条数据!");
	    	return;
	    }
	}
	var product = document.getElementById("productList");
	var productLength = document.getElementById("productList").length;
	for(var i = 0;i<productLength;i++){
		product.options[i].selected=true;
	}
	
	var org = document.getElementById("orgList");
	var orgLength = document.getElementById("orgList").length;
	for(var j = 0;j<orgLength;j++){
		org.options[j].selected=true;
	}
	var isok= $("#Form2", navTab.getCurrentPanel()).valid();  
	$("#alertMsgBox .toolBar .button").on("click",function(){
	    $(".error", navTab.getCurrentPanel()).each(function(i,obj){
	    	  
	    	if(i==0){
	    		$(this).focus();
	    	} 
	    }); 
	    
	    if( $("#polYear", navTab.getCurrentPanel()).val()==""){
	    	$("#polYear", navTab.getCurrentPanel()).focus();
	    }
	});
	/* if(!isok){ 
      return false;
	} */
	$("#Form2", navTab.getCurrentPanel()).submit();
}
//是否是黑名单控制
$("#isBlack", navTab.getCurrentPanel()).click(function(){
  if($(this).attr("checked") == "checked"){
	  $(this).val("1");
  }else{
	  $(this).val("0");
  }
});

//是否是直赔案件控制
$("#isDirect", navTab.getCurrentPanel()).click(function(){
  if($(this).attr("checked") == "checked"){
	  $(this).val("1");
  }else{
	  $(this).val("0");
  }
});

//是否是快赔案件控制
$("#isQuickClaim", navTab.getCurrentPanel()).click(function(){
  if($(this).attr("checked") == "checked"){
	  $(this).val("1");
  }else{
	  $(this).val("0");
  }
});
//======退出操作=======
/* function exitPage(){
	  alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
	 	okCall : function() {
			navTab.closeCurrentTab();
	 	}
	 });
} */


//险种快速查询
var val_flag = "";  //定义一个标识，防止多次重复验证
$("#businessSearch", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#businessSearch", navTab.getCurrentPanel()).val();
	if(value != val_flag){
		val_flag = value;
		var optionStr = "";
		for(var i=0;i<_businessArray.length;i++){
			var obj = _businessArray[i];
			var text = obj.productNameSys;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='"+ obj.productCodeSys +"'>"+ obj.productNameSys + "</option>";
			}
		}
		$("#allProduct", navTab.getCurrentPanel()).html("");
		$("#allProduct", navTab.getCurrentPanel()).append(optionStr);
	}
});

//机构快速查询
var organ_flag = "";  //定义一个标识，防止多次重复验证
$("#organSearch", navTab.getCurrentPanel()).bind("input propertychange", function(){
	var value = $("#organSearch", navTab.getCurrentPanel()).val();
	if(value != organ_flag){
		organ_flag = value;
		var optionStr = "";
		for(var i=0;i<_organArray.length;i++){
			var obj = _organArray[i];
			var text = obj.organCode + "-" +  obj.organName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.organCode + "'>" + obj.organCode + "-" + obj.organName + "</option>";
			};
		};
		$("#allOrgan", navTab.getCurrentPanel()).html("");
		$("#allOrgan", navTab.getCurrentPanel()).append(optionStr);
	};
});


	//定义险种集合
	var _businessArray = new Array();
	
	//定义管理机构集合
	var _organArray = new Array();
	
</script>


<s:iterator value="businessProductListVO" var="bus" >
	<script>
		var obj = new Object();
		obj.productCodeSys = '<s:property value="#bus.productCodeSys"/>';
		obj.productNameSys = '<s:property value="#bus.productNameSys"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>

<s:iterator value="orgVOList" var="org">
	<script>
		var obj = new Object();
		obj.organCode = '<s:property value="#org.organCode"/>';
		obj.organName = '<s:property value="#org.organName"/>';
		_organArray.push(obj);
	</script>
</s:iterator>


<form id="pagerForm" method="post"
	action="clm/parameter/findSurveyRuleList_CLM_claimSurveyRuleAction.action?leftFlag=0&menuId=${menuId}">
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
	<input type="hidden" id="flag" value="${flag}" />
</form>
<!-- 调查申请规则查询条件begin -->
<div  layoutH="36" >
	<div  >
		<form
			action="clm/parameter/findSurveyRuleList_CLM_claimSurveyRuleAction.action?leftFlag=0&menuId=${menuId}"
			method="post" name="form1" id="form1"
			class="pageForm required-validate"
			onsubmit="return navTabSearch(this);" rel="pagerForm">
				<div class="divfclass">
				   <h1><img src="clm/images/tubiao.png">调查申请规则查询条件</h1>
		         </div>
				<div class="pageFormInfoContent">
					<div  >
						<dl>
							<dt>管理机构</dt>
							<dd>
								<input id="branchCodeSurevy" name="claimSurveyRuleVO.organCode" style="width: 30px; border-right: 0;" size="8"
									value="${claimSurveyRuleVO.organCode}"  type="text" class="organ" clickId="branchCodeSurevy" showOrgName="branchnameSurevy" needAll="true" />
								<input id="branchnameSurevy" type="text" name="claimSurveyRuleVO.organName" style="width: 110px" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${claimSurveyRuleVO.organCode}" />" readOnly /> 
								<a id="branchCodeSurevy" class="btnLook" href="#" style="position: relative;"></a>
							</dd>
						</dl>
						<div class="pageFormdiv">
					        <button type="button" class="but_blue" type="button" id="userQuery" onclick="searchSurveyRule()">查询</button>
							 <button type="button" class="but_gray" onclick="resetAll();" id="userQueryReset">重置</button>
						</div>
					</div>
				</div>
		</form>
<!-- 调查申请规则查询条件end -->
<!-- 调查申请规则查询列表begin -->
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">调查申请规则查询列表</h1>
		   </div>
			<div class="tabdivclassbr">
<!-- 				<div style="overflow: auto;"> -->
				<table class="list main_dbottom" width="100%" >
					<thead>
						<tr>
							<th nowrap>选择</th>
							<th nowrap>序号</th>
							<th nowrap>管理机构</th>
							<th nowrap>保单年限</th>
							<th nowrap>案件责任预算金额</th>
							<th nowrap>赔付额与保费的比例</th>
							<th nowrap>出险原因</th>
							<th nowrap>是否黑名单</th>
							<th nowrap>是否直赔案件</th>
							<th nowrap>是否快赔案件</th>
							<th nowrap>险种</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="checkBody">
					<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="currentPage.pageItems"   status="st" id = "claimSurveyRule">
                        <tr align="center"  target="listIdInfo" rel="${surveyRuleId}" id="surveyRule${surveyRuleCode}">
							<td><input type="radio" name="r" onclick="checkEdit('${surveyRuleCode}');">
							<input type="hidden" value='${surveyRuleId}'>
							<input type="hidden" value='${surveyRuleCode}'>
							<input type="hidden" id="accReason1" value='${accReason1}'>
							<input type="hidden" id="accReason2" value='${accReason2}'>
							<td>${st.index+1}</td>
							<td><s:property value="#claimSurveyRule.organCode"/></td>
							<td><s:property value="#claimSurveyRule.polYear"/></td>
							<td><s:property value="#claimSurveyRule.beforePay"/></td>
							<td><s:property value="#claimSurveyRule.rate"/></td>
							<td>${claimNature}</td>
							<td><input type="hidden" value="${isBlack}"/><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${isBlack}" /></td>
							<td><input type="hidden" value="${isDirect}"/><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${isDirect}" /></td>
							<td><input type="hidden" value="${isQuickClaim}"/><Field:codeValue tableName="APP___CLM__DBUSER.T_YES_NO" value="${isQuickClaim}" /></td>
							<td> 
							<s:iterator value="#claimSurveyRule.busiProdCodeList"  var="prodCode" status="bpcl"  >
							${prodCode}
							</s:iterator>
							 
							<td>
								<a title="修改" class="btnEdit" id="editButton" href="javascript:void(0);" onclick="checkEdit('${surveyRuleCode}');">修改</a>
								<a title="删除" class="btnDel" id='delButton' href='javascript:void(0);' onclick="checkDelete('${surveyRuleCode}',this);">删除</a>
							</td>
						</tr>
						</s:iterator>
					</tbody>
				</table>
<!-- 				</div> -->
				<div class="panelBar" >
					<div class="pages">
						<span>显示</span>
						<s:select   list="#{20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
				    		</s:select>
						<span>条，共${currentPage.total}条</span>		
					</div>
					<div class="pagination" targetType="navTab"
									totalCount="${currentPage.total}"
									numPerPage="${currentPage.pageSize}" pageNumShown="20"
									currentPage="${currentPage.pageNo}"></div>
			
				</div>
			</div>
<!-- 调查申请规则查询列表end -->
<!-- 调查申请规则信息begin -->
	<div id="update"  style="display: none"   >
		<form id="Form2"  onsubmit="return validateCallback(this,navTabAjaxDone);" class="required-validate" 
			action="clm/parameter/claimSurveyRuleSaveOrUpdate_CLM_claimSurveyRuleAction.action" 
			method="post" target="navTab" rel="pagerForm2" readOnly>
			<div id="surveyRuleDIVOne">
<!-- 				<fieldset> -->
					<div class="divfclass">
						<h1><img src="clm/images/tubiao.png">调查申请规则信息</h1>
				   </div>
						<div class="pageFormContent nowrap" >
								<table width="100%">
									<tr height="30">
										<td>保单年限<= </td>
										<td>
											<input type="hidden" name="claimSurveyRuleVO.surveyRuleId" id="surveyRuleId"/>	
											<input type="hidden" name="claimSurveyRuleVO.surveyRuleCode" id="surveyRuleCode"/>
											<input type="hidden" name="claimSurveyRuleVO.organCode" id="surveyRuleOrganCode"/>	
											<input type="text" name="claimSurveyRuleVO.polYear" id="polYear" onchange="addReasonReq()" class="digits" >
										</td>
										<td >案件责任预算金额>=</td>
										<td>
											<input type="text" name="claimSurveyRuleVO.beforePay" id="beforePay" onchange="addReasonReq()" class="digits" >
										</td>
										<td>出险原因</td>
										<td><input type = "checkbox" value="2" name="accReason2"  >意外</td>
										<td><input type = "checkbox" value="1" name="accReason1"  >疾病</td>
									</tr>
									<tr height="30">
										<td>是否黑名单</td>
										<td><input type = "checkbox" name="claimSurveyRuleVO.isBlack" value="0" id="isBlack" >是</td>
										<td>赔付额与保费的比例>=</td>
										<td><input type="text" name="claimSurveyRuleVO.rate"  min="0.000001" max="9.999999" id="rate" onchange="addReasonReq()" ></td>
										<td>是否直赔案件</td>
										<td><input type = "checkbox" name="claimSurveyRuleVO.isDirect" value="0" id="isDirect" >是</td>
									</tr>
									<tr height="30">
										<td>是否快赔案件</td>
										<td><input type = "checkbox" name="claimSurveyRuleVO.isQuickClaim" value="0" id="isQuickClaim" >是</td>
									</tr>
								</table>
						</div>
<!-- 				</fieldset> -->
			</div>
	<!-- 调查申请规则信息end -->
	<!-- 险种begin -->
			<div class="pageFormInfoContent" id="surveyRuleDIVTwo">
				<div class="divfclass">
				  <h1><img src="clm/images/tubiao.png">险种</h1>
		        </div>
		   
				<div id="productForm" class="pageFormContent">
					<table>
						<tr>
							<td>
								<dl class='nowrap'>
									<dt>险种</dt>
								</dl>
							</td>
							<td>
							</td>
							<td>
								<dl class='nowrap'>
									<dt>参与调查的险种</dt>
								</dl>
							</td>
						</tr>
						<tr>
							<td>
								<span style="float:left;line-height:1.6;margin-right:10px;">险种快速查询</span><input id="businessSearch" type="text" value="" style="float:left;">
							</td>
							<td></td>
							<td></td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>
						<tr>
							<td>
								<div style="width: 300px">
									<select    id="allProduct" name="allProduct" multiple="multiple"
									style="width: 300px;height:120px" size=5
									ondblclick="return AppendItem('allProduct', 'productList', false);" >
										<s:iterator value="businessProductListVO"  status="st" id="businessProduct">
											<option value="${businessProduct.productCodeSys}" >${businessProduct.productNameSys}</option> 
										</s:iterator>		
									
									</select>
								</div>
							</td>
							<td>
								<div style="margin: 0 30px 0 30px; float: left;">
									<div class="buttonActive">
										<div class="buttonContent">
											<button id="toRightP"
												onclick="return AppendItem('allProduct', 'productList', false);"
												type="button" >&nbsp&nbsp&nbsp>&nbsp&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive" style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id = "allToRightP"
												onclick="return AppendItem('allProduct','productList', true);"
												type="button" >&nbsp&nbsp>>&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive" style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id="toleftP"
												onclick="return AppendItem('productList', 'allProduct', false);"
												type="button" >&nbsp&nbsp&nbsp<&nbsp&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive" style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id = "allToLeftP"
												onclick="return AppendItem('productList','allProduct', true);"
												type="button" >&nbsp&nbsp<<&nbsp&nbsp</button>
										</div>
									</div>
								</div>
							</td>
							<td>
								<div style="width: 300px">
									<select   id="productList" name="productList"
											multiple="multiple" style="width: 300px;height:120px" size=5
											ondblclick="return AppendItem('productList','allProduct',  false);" >
									</select>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</div>		
	<!-- 险种end -->
	<!-- 保单机构begin -->
			<div class="pageFormInfoContent" id="surveyRuleDIVThree">
				<div class="divfclass">
				   <h1><img src="clm/images/tubiao.png">保单机构</h1>
		         </div>
				<div id="productForm" class="pageFormContent">
					<table>
						<tr>
							<td>
								<dl class='nowrap'>
									<dt>管理机构</dt>
								</dl>
							</td>
							<td>
							</td>
							<td>
								<dl class='nowrap'>
									<dt>参与调查的管理机构</dt>
								</dl>
							</td>
						</tr>
						<tr>
							<td>
								<span style="float:left;line-height:1.6;margin-right:10px;">管理机构快速查询</span><input id="organSearch" type="text" value="" style="float:left;">
							</td>
							<td></td>
							<td></td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>
						<tr>
							<td>
								<div style="width: 300px">
									<select    id="allOrgan" name="allOrgan" multiple="multiple"
									style="width: 300px;height:120px" size=5
									ondblclick="return AppendItem('allOrgan', 'orgList', false);" >
											<s:iterator value="orgVOList"  status="st" id="org">
												 <option value="${org.organCode}" >${org.organCode}-${org.organName}</option> 
											</s:iterator>		
									</select>
								</div>
							</td>
							<td>
								<div style="margin: 0 30px 0 30px; float: left;">
									<div class="buttonActive">
										<div class="buttonContent">
											<button id="toRightO"
												onclick="return AppendItem('allOrgan', 'orgList', false);"
												type="button" >&nbsp&nbsp&nbsp>&nbsp&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive" style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id = "allToRightO"
												onclick="return AppendItem('allOrgan','orgList', true);"
												type="button" >&nbsp&nbsp>>&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive" style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id="toleftO"
												onclick="return AppendItem('orgList', 'allOrgan', false);"
												type="button" >&nbsp&nbsp&nbsp<&nbsp&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive" style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id = "allToLeftO"
												onclick="return AppendItem('orgList','allOrgan', true);"
												type="button" >&nbsp&nbsp<<&nbsp&nbsp</button>
										</div>
									</div>
								</div>
							</td>
							<td>
								<div style="width: 300px">
									<select   id="orgList" name="orgList"
											multiple="multiple" style="width: 300px;height:120px" size=5
											ondblclick="return AppendItem('orgList','allOrgan',  false);" >
									</select>
								</div>
							</td>
						</tr>
					</table>
				</div>
				
				
			 <div class="formBarButton">
			     <ul>
					<li><button class="but_blue"  type="button" onclick="saveForm()" id="saveBtn" >保存</button></li>
					<li><button class="but_blue" type="button" id="clearBtn" onclick="clearForm()" >重置</button></li>
					<li><button class="but_blue" type="button" onclick="exit()" id="saveBtn">退出</button></li>
				</ul>
			</div>
				
			</div>		
	<!-- 保单机构end -->
		</form>
	</div>
	</div>
</div>
