<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="clm/pages/clm_care/pre_care_service.js"></script>
<script type="text/javascript">
 	var editFlag = 0;
 	var addFlag = 0;
    function checkQueryCareServer() {
    	 var serverType = $('#serverType', navTab.getCurrentPanel()).val();
    	var serverIdsel = $('#serverIdsel', navTab.getCurrentPanel()).val();
    	var serverName = $('#serverName', navTab.getCurrentPanel()).val();
    	var branchCode = $('#branchCode', navTab.getCurrentPanel()).val();
    	if (serverType == "" && serverIdsel == "" && serverName == "" && branchCode == "") {
    		alertMsg.error("查询条件至少有一项不能为空！!");
			return false;
    	}
    	//因为用户可能存在纯数字的情况，暂时不加校验
    	/* if(serverName != ""){
    		if(!checkName($("#serverName", navTab.getCurrentPanel()))){
    			return;
    		}
    	} */
    	$('#careServerForm', navTab.getCurrentPanel()).submit();
    }
	function resetAll() {
		$('#serverType', navTab.getCurrentPanel()).attr('value', '');
		$('#serverIdsel', navTab.getCurrentPanel()).attr('value', '');
		$('#serverName', navTab.getCurrentPanel()).attr('value', '');
		$('#branchCode', navTab.getCurrentPanel()).attr('value', '');
	}
	/* function exit(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				navTab.closeCurrentTab();
		 	}
		 });
	} */
	function exportSite(){	
		 var name = $("#serverName", navTab.getCurrentPanel()).val();
		 var serverType = $("#serverType", navTab.getCurrentPanel()).val();
		 var serverIdsel = $("#serverIdsel", navTab.getCurrentPanel()).val();
		 var branchCode = $("#branchCode", navTab.getCurrentPanel()).val();
		 var url = "clm/parameter/outputStaff_CLM_maintStaffAction.action?nameD="+encodeURI(encodeURI(name))+"&serverTypeD="+serverType+"&serverIdselD="+serverIdsel+"&branchCodeD="+branchCode;

		    var formAction=$("#careServerForm", navTab.getCurrentPanel()).attr("action");
			
			var formSubmit=$("#careServerForm", navTab.getCurrentPanel()).attr("onsubmit");
			 
			$("#careServerForm", navTab.getCurrentPanel()).attr("action",url);
			
			$("#careServerForm", navTab.getCurrentPanel()).attr("onsubmit",""); 
			
			$("#careServerForm", navTab.getCurrentPanel()).submit();
			
			$("#careServerForm", navTab.getCurrentPanel()).attr("onsubmit",formSubmit);
			
			$("#careServerForm", navTab.getCurrentPanel()).attr("action",formAction);
	}
	
	//编辑数据
	function bringInfo(obj){
		editFlag = 1;
		addFlag = 0;
		$("#addInfoFlag",navTab.getCurrentPanel()).removeAttr("style","display:none");
		var serverTypeInfo = $(obj).parent().parent().find("td:eq(2) input").val();
		var serverCodeInfo = $(obj).parent().parent().find("td:eq(3)").html();
		var serverNameInfo = $(obj).parent().parent().find("td:eq(4)").html();
		var serverCertiCodeInfo = $(obj).parent().parent().find("input#hiddenCertiCode").val();
		var serverEntryDateInfo = $(obj).parent().parent().find("input#hiddenEntryDate").val();
		var serverPhoneInfo = $(obj).parent().parent().find("input#hiddenPhone").val();
		var serverMobileInfo = $(obj).parent().parent().find("td:eq(6)").html();
		var serverOrganCodeInfo = $(obj).parent().parent().find("td:eq(5) input").val();
		var serverOrganName = $(obj).parent().parent().find("input#hiddenOrganName").val();
		var serverValidFlagInfo = $(obj).parent().parent().find("td:eq(7) input").val();
		var serverEmailInfo = $(obj).parent().parent().find("input#hiddenEmail").val();
		var serverStartDateInfo = $(obj).parent().parent().find("input#hiddenServerStartDate").val();
		var serverVolunteerJobInfo = $(obj).parent().parent().find("input#hiddenVolunteerJob").val();
		var serverVolunteerLevelInfo = $(obj).parent().parent().find("input#hiddenVolunteerLevel").val();
		var serverWorkRecordInfo = $(obj).parent().parent().find("input#hiddenWorkRecord").val();
		var serverVolunteerRecordInfo = $(obj).parent().parent().find("input#hiddenVolunteerRecord").val();
		var hiddenServerId = $(obj).parent().parent().find("input#hiddenServerId").val();
		var hiddenAgentType = $(obj).parent().parent().find("input#hiddenAgentType").val();
		$("#serverCodeInfo",navTab.getCurrentPanel()).val(serverCodeInfo);
		$("#serverNameInfo",navTab.getCurrentPanel()).val(serverNameInfo);
		$("#serverCertiCodeInfo",navTab.getCurrentPanel()).val(serverCertiCodeInfo);
		$("#serverEntryDateInfo",navTab.getCurrentPanel()).val(serverEntryDateInfo);
		$("#serverPhoneInfo",navTab.getCurrentPanel()).val(serverPhoneInfo);
		$("#serverMobileInfo",navTab.getCurrentPanel()).val(serverMobileInfo);
		$("#serverOrganCodeInfo",navTab.getCurrentPanel()).val(serverOrganCodeInfo);
		$("#serverOrganName",navTab.getCurrentPanel()).val(serverOrganName);
		$("#serverEmailInfo",navTab.getCurrentPanel()).val(serverEmailInfo);
		$("#serverStartDateInfo",navTab.getCurrentPanel()).val(serverStartDateInfo);
		$("#serverVolunteerJobInfo",navTab.getCurrentPanel()).val(serverVolunteerJobInfo);
		$("#serverVolunteerLevelInfo",navTab.getCurrentPanel()).val(serverVolunteerLevelInfo);
		$("#serverWorkRecordInfo",navTab.getCurrentPanel()).val(serverWorkRecordInfo);
		$("#serverVolunteerRecordInfo",navTab.getCurrentPanel()).val(serverVolunteerRecordInfo);
		 
		if(serverValidFlagInfo == 1){
			$("#serverValidFlagInfo",navTab.getCurrentPanel()).attr("checked","checked");
		}else{
			$("#serverValidFlagInfo",navTab.getCurrentPanel()).removeAttr("checked");
		}
		$("select#serverAgentTypeInfo", navTab.getCurrentPanel()).selectMyComBox(hiddenAgentType);
		$("#serverTypeInfo", navTab.getCurrentPanel()).selectMyComBox(serverTypeInfo);
		claimFireEvent($("#serverTypeInfo", navTab.getCurrentPanel()));
		$("input#hiddenServerIdInfo",navTab.getCurrentPanel()).val(hiddenServerId);
	}
	
	//弹出是否
	/* function close(){
		alertMsg.confirm("是否确定退出",{
			okCall:function(){
				navTab.closeCurrentTab();
			}
		});
	} */
	//保存按钮
	function saveInfo(){
		
		var serverType = $("#serverTypeInfo",navTab.getCurrentPanel()).val();
		var serverCode = $("#serverCodeInfo",navTab.getCurrentPanel()).val();
		var serverName = $("#serverNameInfo",navTab.getCurrentPanel()).val();
		var certiCode = $("#serverCertiCodeInfo",navTab.getCurrentPanel()).val();
		var organCode = $("#serverOrganCodeInfo",navTab.getCurrentPanel()).val();
		var mobile = $("#serverMobileInfo",navTab.getCurrentPanel()).val();
		var email = $("#serverEmailInfo",navTab.getCurrentPanel()).val();
		if(serverType == ""){
			alertMsg.info("服务人员类型不能为空");
			return false;
		}else if(serverName == ""){
			alertMsg.info("姓名不能为空");
			return false;
		}else if(certiCode == ""){
			alertMsg.info("证件号码不能为空");
			return false;
		}else if(organCode == ""){
			alertMsg.info("机构不能为空");
			return false;
		}else if(mobile == ""){
			alertMsg.info("手机号码不能为空");
			return false;
		}else if(email == ""){
			alertMsg.info("电子邮箱不能为空");
			return false;
		}
		if(serverType != 4){
			if(serverCode == ""){
				alertMsg.info("业务员代码不能为空");
				return false;
			}
		} 
		$("#saveClaimCareServer", navTab.getCurrentPanel()).attr("action","clm/parameter/updateStaffInfo_CLM_maintStaffAction.action?addFlag="+addFlag+"&editFlag="+editFlag);
		$("#saveClaimCareServer", navTab.getCurrentPanel()).submit();		
	}
	//点击保存按钮的回调函数
	function updateMethod(json) {
		if (json.statusCode == DWZ.statusCode.ok) {
			if(addFlag==1){
				 $("#serverType",navTab.getCurrentPanel()).val($("#serverTypeInfo",navTab.getCurrentPanel()).val());
			}
			
			$("#userQuery", navTab.getCurrentPanel()).click();
			alertMsg.correct(json.message);
		} else {
			alertMsg.error(json.message);
		}
	}
	
	function serverTypeChange(){
		var obj = $("select#serverType", navTab.getCurrentPanel()).val();
		if(obj == 4){
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(3)").attr("style","display:none");
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(2)").attr("style","display:none");
		}else{
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(3)").removeAttr("style");
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(2)").removeAttr("style");
		}
	}
	$(document).ready(function(){
		var obj = $("select#serverType", navTab.getCurrentPanel()).val();
		if(obj == 4){
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(3)").attr("style","display:none");
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(2)").attr("style","display:none");
			$("#serverCodeInfo", navTab.getCurrentPanel()).parent().parent().attr("style","display:none");
		}else{
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(3)").removeAttr("style");
			$("select#serverType", navTab.getCurrentPanel()).parent().parent().find("td:eq(2)").removeAttr("style");
			$("#serverCodeInfo", navTab.getCurrentPanel()).parent().parent().removeAttr("style");
		}
	});
	function serverTypeInfoChange(){
		var serverTypeInfo = $("#serverTypeInfo", navTab.getCurrentPanel()).val()
		if(serverTypeInfo == 4){
			$("#serverCodeInfoDL", navTab.getCurrentPanel()).attr("style","display:none");
		}else{
			$("#serverCodeInfoDL", navTab.getCurrentPanel()).removeAttr("style","display:none");
		}
		
	}
	//新增服务人员
	function viewAddInfo(){
		//添加数据清空数据
		//if(addFlag == 0){
			var obj = $("#addInfoFlag", navTab.getCurrentPanel());
			obj.find("input,textarea").each(function(){
				$(this).val('');
			});
		 
			obj.find("select").each(function(){
				$(this).val('');
				$(this).selectMyComBox("");
			});
		//} 
		editFlag = 0;
		addFlag = 1;
		$("#addInfoFlag",navTab.getCurrentPanel()).removeAttr("style","display:none");
	}
</script>
<div layoutH="0" >
<form id="pagerForm" method="post"
	action="clm/parameter/findStaff_CLM_maintStaffAction.action" >
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
</form>
	<form action="clm/parameter/findStaff_CLM_maintStaffAction.action" method="post" name="form1" id="careServerForm"
		class="pageForm required-validate"
		onsubmit="return navTabSearch(this);" rel="pagerForm">
		  <div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
		   </div>
<div class="pageFormInfoContent">
		   <dl>
		   		<dt>服务人员类型</dt>
		   		<dd>
		   			<Field:codeTable cssClass="combox title"  name="claimCareServerVO.serverType" id="serverType" tableName="APP___CLM__DBUSER.T_SERVER_TYPE" value="${claimCareServerVO.serverType}" nullOption="true" onChange="serverTypeChange()"/>
		   		</dd>
		   </dl>
		   <dl>
		   		<dt>用户名/业务员代码</dt>
		   		<dd>
		   			<input type="text" class="data" size=16 id="serverIdsel" name="claimCareServerVO.serverCode" value="${claimCareServerVO.serverCode}" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
		   		</dd>
		   </dl>
		   <dl>
		   		<dt>姓名</dt>
		   		<dd>
		   			<input type="text" class="data" id="serverName" name="claimCareServerVO.name" value="${claimCareServerVO.name}" onkeyup="this.value=this.value.replace(/\s/g,'')" onblur="resetStyle(this)" />
		   		</dd>
		   </dl>
		   <dl>
		   		<dt>机构代码</dt>
		   		<dd>
		   		<%-- 	<input id="branchCode" name="claimCareServerVO.organCode" style="width: 30px;border-right:0px" value="${claimCareServerVO.organCode}" type="text" class="organ" clickId="menuBtn" showOrgName="branchname" needAll="true"/>
						<input id="branchname" name="claimCareServerVO.organName" style="width:90px;" value="${claimCareServerVO.organName}" type="text" readOnly/>
						<a id="menuBtn" class="btnLook" href="#" ></a> --%>
						
					<input id="branchCode" name="claimCareServerVO.organCode" style="width: 30px; border-right: 0;" size="8"
						value="${claimCareServerVO.organCode}" type="text" class="organ" clickId="staffBtn" showOrgName="branchname" needAll="true" />
					<input id="branchname" type="text" style="width: 110px" name="claimCareServerVO.organName" value="${claimCareServerVO.organName}" readOnly /> 
					<a id="staffBtn" class="btnLook" href="#" style="position: relative;"></a>
		   		</dd>
		   </dl>
			<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="checkQueryCareServer();" id="userQuery">查询</button>
					<button id="exportSite" type="button" class="but_blue"  onclick="exportSite();" >数据下载</button>
			</div>
</div>
	</form>

<div >
	<div class="divfclass">
		 <h1><img src="clm/images/tubiao.png">服务人员列表</h1>
	</div>
	<div class="tabdivclassbr">
	<div class="panelBar">
		<ul class="toolBar">
			<li><a class="add"><span onclick="viewAddInfo()">新增</span></a></li>
			<%-- <li class="line">line</li>
			<li><a class="edit" href="clm/parameter/updateStaff_CLM_maintStaffAction.action?claimCareServerVO.serverId={listIdInfo}&updateFlag=update&menuId=${menuId }" 
				maxable="false" resizable="false" minable="false" title="服务人员信息修改" width="600" height="280" rel="updateStaff" 
				target="dialog"><span>修改</span></a></li> --%>
		</ul>

	</div>
	<div class="main_border">
	<table class="list main_all" width="100%">
		<thead>
			<tr >
				<th nowrap>选择</th>
				<th nowrap>序号</th>
				<th nowrap>服务人员类型</th>
				<th nowrap>用户名/业务员代码</th>
				<th nowrap>姓名</th>
				<th nowrap>机构</th>
				<th nowrap>手机号码</th>
				<th nowrap>有效标志</th>
			</tr>
		</thead>
		<tbody>
		<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
			<s:iterator value="currentPage.pageItems"  status="st"  >
			<tr align="center"  target="listIdInfo" rel="${serverId}">
				<td><input type="radio" name="r" onclick="bringInfo(this)"/><input type="hidden" value='${listId}'/></td>
				<td> ${st.index+1}</td>
				<td><input type="hidden" value="${serverType}">${serverTypeName}</td>
				<td>${serverCode}</td>
				<td>${name}</td>
				<td><input type="hidden" value="${organCode}">${organCodeName}</td>
				<td>${mobile}</td>
				<td><input type="hidden" value="${validFlag}">${validFlagName}</td>
				<input type="hidden" id="hiddenCertiCode" value="${certiCode}">
				<input type="hidden" id="hiddenEntryDate" value="<s:date name='entryDate' format='yyyy-MM-dd' />">
				<input type="hidden" id="hiddenAgentType" value="${agentType}">
				<input type="hidden" id="hiddenPhone" value="${phone}">
				<input type="hidden" id="hiddenEmail" value="${email}">
				<input type="hidden" id="hiddenServerStartDate" value="<s:date name='serverStartDate' format='yyyy-MM-dd' />">
				<input type="hidden" id="hiddenVolunteerJob" value="${volunteerJob}">
				<input type="hidden" id="hiddenVolunteerLevel" value="${volunteerLevel}">
				<input type="hidden" id="hiddenWorkRecord" value="${workRecord}">
				<input type="hidden" id="hiddenVolunteerRecord" value="${volunteerRecord}">
				<input type="hidden" id="hiddenServerId" value="${serverId}">
				<input type="hidden" id="hiddenOrganName" value="${organCodeName}">
			</tr>
			</s:iterator>
		</tbody>
	</table>
	</div>
	<div class="panelBar" >
		<div class="pages">
			<span>显示</span>
			<s:select   list="#{5:'5',10:'10',20:'20',50:'50'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
	    		</s:select>
			<span>条，共${currentPage.total}条</span>		
		</div>
		<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}">
		</div>
	</div>
	</div>
</div>
<form id="saveClaimCareServer" action="clm/parameter/updateStaffInfo_CLM_maintStaffAction.action" method="post" class="pageForm required-validate"  onsubmit="return validateCallback(this, updateMethod)">
	<div class="pageFormInfoContent" style="display: none" id="addInfoFlag">
		<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">服务人员信息</h1>				
		   </div>
		<div class="pageFormContent">
		
		<dl>
			<dt><font color="red">* </font>服务人员类型</dt>
			<dd>
			    <Field:codeTable name="careServerAVO.serverType" value="${careServerAVO.serverType }" id="serverTypeInfo" tableName="APP___CLM__DBUSER.T_SERVER_TYPE" nullOption="true" cssClass="notuseflagDelete combox title comboxDD required" onChange="serverTypeInfoChange()"/>
			<input name="careServerAVO.serverId" type="hidden" id="hiddenServerIdInfo" />
			</dd>
		</dl>
		<s:if test="serverCode != 4">
		<dl id="serverCodeInfoDL">	
			<dt><font color="red">* </font>用户名/业务员代码</dt>
			<dd>
				<input id="serverCodeInfo" class="data" name="careServerAVO.serverCode"
								 type="text"  size="25"/> 
								<a class="btnLook" href="clm/claimcare/queryUserOrServerInfo_CLM_queryServerAction.action"
								lookupGroup="careServerAVO">查询系统用户/业务员 </a>
			</dd>
		</dl>
		</s:if>
		<dl>	
			<dt><font color="red">* </font>姓名</dt>
			<dd><input type="text" class="data" id="serverNameInfo" name="careServerAVO.name" value="${claimCareServerVO.name}" onkeyup="this.value=this.value.replace(/\s/g,'')"/></dd>
		</dl>
		</div>
		<div class="pageFormContent">
	 	<dl>
			<dt><font color="red">* </font>证件号码</dt>
			<dd>
				<input type="text" name="careServerAVO.certiCode" id="serverCertiCodeInfo" value="${claimCareServerVO.certiCode}"/>
			</dd>
		</dl>
		<dl>
			<dt><font color="red">* </font>机构</dt>
			<dd>
				<input id="serverOrganCodeInfo" name="careServerAVO.organCode" style="width: 30px; border-right: 0;" size="8"
						value="${claimCareServerVO.organCode}" type="text" class="organ" clickId="staffOrgBtn" showOrgName="serverOrganName" needAll="true" />
					<input id="serverOrganName" type="text" style="width: 110px" readOnly /> 
					<a id="staffOrgBtn" class="btnLook" href="#" name="careServerAVO.organName" value="${claimCareServerVO.organCodeName }" style="position: relative;"></a>
			</dd>
		</dl>
		<dl>
			<dt>入司日期</dt>
			<dd>
				<input type="text" name="careServerAVO.entryDate" size="22" class="date" id="serverEntryDateInfo"
				value="<s:date name='claimCareServerVO.entryDate' format='yyyy-MM-dd'  />" />
				<a class="inputDateButton" href="javascript:;">选择</a>
			</dd>
		</dl>
		</div>
		<div class="pageFormContent">
		<dl>
			<dt>业务员标识</dt>
			<dd>
				<select class="combox"  id="serverAgentTypeInfo" name ="careServerAVO.agentType">
					<option value="">请选择</option>
					<option value="2">普通</option>
					<option value="1">明星</option>
				</select>
			</dd>
		</dl>
		<dl>
			<dt>电话</dt>
			<dd>
				<input type="text" id="serverPhoneInfo" name="careServerAVO.phone" value="${phone}">
			</dd>
		</dl>
		<dl>
			<dt><font color="red">* </font>手机号码</dt>
			<dd>
				<input type="textInput exmobile" id="serverMobileInfo" name="careServerAVO.mobile" value="${mobile}">
			</dd>
		</dl>
		</div>
		<div class="pageFormContent">
		<dl>
			<dt><font color="red">* </font>电子邮箱</dt>
			<dd>
				<input type="text" id="serverEmailInfo" name="careServerAVO.email" class="email"  value="${email}">
			</dd>
		</dl>
		<dl>
			<dt>服务起期</dt>
			<dd>
				<input type="text" name="careServerAVO.serverStartDate" size="22" class="date" id="serverStartDateInfo"
				value="<s:date name='careServerAVO.serverStartDate' format='yyyy-MM-dd' />" />
				<a class="inputDateButton" href="javascript:;">选择</a>
			</dd>
		</dl>
		<dl>
			<dt>志愿者职务</dt>
			<dd>
				<input type="text" name="careServerAVO.volunteerJob" value="${volunteerJob}" id="serverVolunteerJobInfo">
			</dd>
		</dl>
		</div>
		<div class="pageFormContent">
		<dl>
			<dt>志愿者级别</dt>
			<dd>
				<input type="text" name="careServerAVO.volunteerLevel" value="${volunteerLevel}" id="serverVolunteerLevelInfo">
			</dd>
		</dl>
		<dl>
			<dt>有效标识</dt>
			<dd>
				<input type="checkbox" id="serverValidFlagInfo" name="careServerAVO.validFlag" value="1"/>
			</dd>
		</dl>	
		</div>
		<div class="pageFormContent" style="height: 100px">
		<dl>
			<dt>工作履历</dt>
			<dd>
				<textarea rows="5" cols="128" name="careServerAVO.workRecord" id="serverWorkRecordInfo"></textarea>
			</dd>
		</dl>
		</div>
		<div class="pageFormContent" style="height: 100px">
		<dl>
			<dt>志愿履历</dt>
			<dd>
				<textarea rows="5" cols="128" name="careServerAVO.volunteerRecord" id="serverVolunteerRecordInfo"></textarea>
			</dd>
		</dl>
		</div>
		<div style="height: 20px"></div>
		
		<div class="formBarButton">
			<ul>
				<li><a class="but_blue main_buta" href="javascript:void(0)" type="button" onclick="saveInfo()">保存</a><li>
			    <li><button class="but_gray" type="button" onclick="exit()">退出</button></li>
			</ul> 
		</div>
		
	</div>
</form>
</div>