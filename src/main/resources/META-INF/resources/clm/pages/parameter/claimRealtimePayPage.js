var paymentSwitchOld;
//初始化开关状态
$(function(){
	var flowType = $("#flowType2", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/parameter/queryOnOrOffByFlowType_CLM_claimRealtimePayAction.action',
		'data':{"claimRealtimePay.flowType":flowType},
		'type':'post',
		'async':true,
		'success':function(data){
			 var data=eval("("+data+")");
			 paymentSwitchOld = data.paymentSwitch;
			 if(paymentSwitchOld == "1"){
			 	$("#on", navTab.getCurrentPanel()).attr("checked","true");
			 }else{
				paymentSwitchOld = "0";
			 	$("#off", navTab.getCurrentPanel()).attr("checked","true");
			 }
		 },
		 'error':function(){
			 alertMsg.error("获取开关状态失败,请联系管理员");
		 }
	});
});

//实时支付开关列表查询
function queryClaimRealtimePayByFlowType() {
	$("#claimRealtimePayForm", navTab.getCurrentPanel()).submit();
}

//根据流程节点，单选框动态选中
function checkPaymentSwitch(obj) {
	var flowType = $(obj).val();
	$.ajax({
		'url':'clm/parameter/queryOnOrOffByFlowType_CLM_claimRealtimePayAction.action',
		'data':{"claimRealtimePay.flowType":flowType},
		'type':'post',
		'async':false,
		'success':function(data){
			 var data=eval("("+data+")");
			 paymentSwitchOld = data.paymentSwitch;
			 if(paymentSwitchOld == "1"){
			 	$("#on", navTab.getCurrentPanel()).attr("checked","true");
			 }else{
				 paymentSwitchOld = "0";
			 	$("#off", navTab.getCurrentPanel()).attr("checked","true");
			 }
		 },
		 'error':function(){
			 alertMsg.error("获取开关状态失败,请联系管理员");
		 }
	});
}

function saveClaimRealtimePay() {
	var flowType2 = $("#flowType2", navTab.getCurrentPanel()).val();
	var paymentSwitch = $("#claimRealtimePayForm2").find("input:radio:checked").val();
	//连续配置校验规则
	$.ajax({
		'url':'clm/parameter/queryOnOrOffByFlowType_CLM_claimRealtimePayAction.action',
		'data':{"claimRealtimePay.flowType":flowType2},
		'type':'post',
		'async':false,
		'success':function(data){
			 var data=eval("("+data+")");
			 paymentSwitchOld = data.paymentSwitch;
			 if(paymentSwitchOld != "1"){
				 paymentSwitchOld = "0";
			 }
		 },
		 'error':function(){
			 alertMsg.error("获取开关状态失败,请联系管理员");
		 }
	});
	if(paymentSwitch == paymentSwitchOld){
		alertMsg.error("当前流程节点的实时支付开关未做更改，请确认。");
		return;
	}else{
		$.ajax({
			'url':'clm/parameter/saveClaimRealtimePay_CLM_claimRealtimePayAction.action',
			'data':{"claimRealtimePay.flowType":flowType2,
					"claimRealtimePay.paymentSwitch":paymentSwitch},
			'type':'post',
			'async':false,
			'success':function(data){
				 var data=eval("("+data+")");
				 if(null != data.listId){
					 alertMsg.info("实时支付开关配置成功");
				 }else{
					 alertMsg.error("实时支付开关保存失败,请联系管理员");
				 }
			 },
			 'error':function(){
				 alertMsg.error("实时支付开关保存失败,请联系管理员");
			 }
		});
	}
}