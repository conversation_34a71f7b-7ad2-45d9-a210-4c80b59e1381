<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<form id="pagerForm" method="post"
	action="clm/parameter/selectRuleConfigurationLog_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${currentPage6.pageNo} " />
	<inputtype ="hidden" name="numPerPage" value="${currentPage6.pageSize}" />
	<!-- 查询条件回调 -->
	<input type="hidden" name="claimRuleExplainVO.promptType"
		value="${claimRuleExplainVO.promptType}" /> <input type="hidden"
		name="claimRuleExplainVO.serviceScenario"
		value="${claimRuleExplainVO.serviceScenario}" /> <input type="hidden"
		name="claimRuleExplainVO.ruleCode"
		value="${claimRuleExplainVO.ruleCode}" /> <input type="hidden"
		name="claimRuleExplainVO.ruleName"
		value="${claimRuleExplainVO.ruleName}" />
</form>
<div class="main_bqtabdivbr">
	<table class="list" id="surveyApplyOperTable" width="100%">
	<span><b>规则解释配置轨迹</b></span>
		<thead>
			<tr>
				<th nowrap>提示类型</th>
				<th nowrap>数据服务场景</th>
				<th nowrap>现有规则编码</th>
				<th nowrap>现有规则名称</th>
				<th nowrap>是否提示</th>
				<th nowrap>提示内容</th>
				<th nowrap>必调项</th>
				<th nowrap>操作</th>
				<th nowrap>提交人</th>
				<th nowrap>提交日期</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag != null">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">请选择条件查询数据！</div>
					</td>
				</tr>
			</s:if>
			<s:elseif
				test="currentPage6.PageItems == null || currentPage6.PageItems.size()==0">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:elseif>
			<s:iterator value="currentPage6.pageItems" status="st">
				<tr align="center" target="listIdInfo">
					<td align="center" style="word-break: break-all;">${promptType}</td>
					<td align="center" style="word-break: break-all;">${serviceScenario}</td>
					<td align="center" style="word-break: break-all;">${ruleCode}</td>
					<td align="center" style="word-break: break-all;">${ruleName}</td>
					<td align="center" style="word-break: break-all;">${isPromptStr}</td>
					<td align="center" style="word-break: break-all;">${promptContent}</td>
					<td align="center" style="word-break: break-all;">${mbSurStr}</td>
					<td align="center" style="word-break: break-all;">${operationType}</td>
					<td align="center" style="word-break: break-all;">${submitUserStr}</td>
					<td align="center" style="word-break: break-all;"><s:date
							name='submitDate' format='yyyy-MM-dd' /></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}" name="select"
				onchange="navTabPageBreak({numPerPage:this.value},'claimIRuleConfigurationLog')"
				value="currentPage6.pageSize">
			</s:select>
			<span>条，共${currentPage6.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${currentPage6.total}"
			numPerPage="${currentPage6.pageSize}" pageNumShown="20"
			currentPage="${currentPage6.pageNo}" rel="claimIRuleConfigurationLog"></div>
	</div>
</div>