<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript">
 function save(){//如果有必录项未录，光标指向第一个未录项
		var branchname =$("#branchname",$.pdialog.getCurrent()).val();
		var hospitalCode = $("#hospitalCode1",$.pdialog.getCurrent()).val();
		var hospitalName = $("#hospitalName1",$.pdialog.getCurrent()).val();
		var hospitalState = $("#hospitalState",$.pdialog.getCurrent()).val();
		var hospitalCity = $("#hospitalCity",$.pdialog.getCurrent()).val();
		var hospitalDistrict = $("#hospitalDistrict",$.pdialog.getCurrent()).val();
		var flag = true;
		if($.trim(hospitalCode)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalCode1",$.pdialog.getCurrent()).focus();
			});
			return false;
		} 
		if($.trim(hospitalName)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalName1",$.pdialog.getCurrent()).focus();
			});
			return false;
		} 
		if($.trim(branchname)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#branchname",$.pdialog.getCurrent()).focus();
			});
			return false;
		}
		if($.trim(hospitalState)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalState",$.pdialog.getCurrent()).focus();
			});
			return false;
		}
		if($.trim(hospitalCity)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalCity",$.pdialog.getCurrent()).focus();
			});
			return false;
		}
	var isok= $("#userForm",$.pdialog.getCurrent()).valid();  
	$("#alertMsgBox .toolBar .button").on("click",function(){
	    $(".error:eq(0)",$.pdialog.getCurrent()).focus();
	});
	if(!isok){ 
      return false;
	}
	$("#userForm",$.pdialog.getCurrent()).submit(); 
 }
 
 function resetAll(){
	 $("#hospitalName1",$.pdialog.getCurrent()).val("");
	 $("#hospitalLevel",$.pdialog.getCurrent()).prev().attr("value","-全部");
	 $("#designated",$.pdialog.getCurrent()).prev().attr("value","0-否");
	 $("#deformity",$.pdialog.getCurrent()).prev().attr("value","0-否");
	 $("#hospitalStatus",$.pdialog.getCurrent()).prev().attr("value","1-有效");
	 $("#isCancer",$.pdialog.getCurrent()).prev().attr("value","0-否");
	 $("#isCostly",$.pdialog.getCurrent()).prev().attr("value","0-否"); 
 }

 //初始化赋值市
var hospitalCityReportId = $("#hospitalCity", $.pdialog.getCurrent());
var hospitalAreaReportId = $("#hospitalDistrict", $.pdialog.getCurrent());
var province = $("#hospitalStateReportId", $.pdialog.getCurrent()).val();
var city = $("#hospitalCityReportId", $.pdialog.getCurrent()).val();
var area = $("#hospitalDistrictReportId", $.pdialog.getCurrent()).val();
cityAssign(province,city,area);
function cityAssign(province,city,area){
 	
 	$.ajax({
 		'type' : 'post',
 		'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
 				+ province,
 		'datatype' : 'json',
 		'async' : true,
 		'success' : function(data) {
 			var data = eval("(" + data + ")");
 			$("#hospitalCity", $.pdialog.getCurrent())
 					.empty();
 			$(hospitalCityReportId).append(
 					"<option value=''>市</option>");
 			for (var i = 0; i < data.length; i++) {
 				if (data[i].code == city) {
 					$("#hospitalCity", $.pdialog.getCurrent())
 							.prev().val(
 									data[i].code + "-"
 											+ data[i].name);
 					$(hospitalCityReportId).append(
 							"<option value='" + city
 									+ "' class = '"
 									+ data[i].name + "'>"
 									+ data[i].name
 									+ "</option>");
 				}
 			}
 			for (var i = 0; i < data.length; i++) {
 				if (data[i].code != city) {
 					var option1 = "<option value='"
 							+ data[i].code + "'   class='"
 							+ data[i].name + "' >"
 							+ data[i].name + "</option>";
 					$(hospitalCityReportId).append(option1);
 				}
 			}
 			$("#hospitalCity", $.pdialog.getCurrent()).val(
 					city);
 		},
 	});
 	setTimeout("areaAssign(" + city + " , "+ area +")", 0.0003);
 	
 }
 //初始化赋值市
 function areaAssign (city,area){
 	// 获取市为县赋值
 	$.ajax({
 				'type' : 'post',
 				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
 						+ city,
 				'datatype' : 'json',
 				'async' : true,
 				'success' : function(data) {
 					var data = eval("(" + data + ")");
 					$("#hospitalDistrict", $.pdialog.getCurrent()).empty();
 					$(hospitalAreaReportId).append("<option value=''>区/县</option>");
 					for (var i = 0; i < data.length; i++) {
 						if (data[i].code == area) {
 							$("#hospitalDistrict", $.pdialog.getCurrent()).prev()
 									.val(data[i].code + "-" + data[i].name);
 							$(hospitalAreaReportId).append(
 									"<option value='" + area + "' class = '"
 											+ data[i].name + "'>"
 											+ data[i].name + "</option>");
 						}
 					}
 					for (var i = 0; i < data.length; i++) {
 						if (data[i].code != area) {
 							var option1 = "<option value='" + data[i].code
 									+ "'   class='" + data[i].name + "' >"
 									+ data[i].name + "</option>";
 							$(hospitalAreaReportId).append(option1);
 						}
 					}
 					$("#hospitalDistrict", $.pdialog.getCurrent()).val(area);
 				},
 			});
 }
 
//根据省查询市
 function ProvinceChangeReportData(k) {
	var cityReportId = $("#hospitalCity", $.pdialog.getCurrent());
	var province = $(k).val();
	$("#hospitalCity", $.pdialog.getCurrent()).prev().val("");
	$("#hospitalDistrict", $.pdialog.getCurrent()).prev().val("");
 	$.ajax({
 				'type' : 'post',
 				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
 						+ province,
 				'datatype' : 'json',
 				'async' : true,
 				'success' : function(data) {
 					var data = eval("(" + data + ")");
 					$("#hospitalCity", $.pdialog.getCurrent()).empty();
 					$("#hospitalDistrict", $.pdialog.getCurrent()).empty();
 					$("<option value=''>市</option>").appendTo(cityReportId);
 					for (var i = 0; i < data.length; i++) {
 						var option1 = "<option value='" + data[i].code
 								+ "'   class='" + data[i].name + "' >"
 								+ data[i].name + "</option>";
 						$(option1).appendTo(cityReportId);
 					}
 				},
 			});
 	
 }
 //根据市查询县
 function cityChageReportData(k) {
 	var city = $(k).val();
 	var hospitalDistrict = $("#hospitalDistrict", $.pdialog.getCurrent());
 	$("#hospitalDistrict", $.pdialog.getCurrent()).prev().val("");
 	$.ajax({
 				'type' : 'post',
 				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
 						+ city,
 				'datatype' : 'json',
 				'async' : true,
 				'success' : function(data) {
 					var data = eval("(" + data + ")");
 					$("#hospitalDistrict", $.pdialog.getCurrent()).empty();
 					$("<option value=''>区/县</option>").appendTo(hospitalDistrict);
 					for (var i = 0; i < data.length; i++) {
 						var option1 = "<option value='" + data[i].code
 								+ "'   class='" + data[i].name + "' >"
 								+ data[i].name + "</option>";
 						$(option1).appendTo(hospitalDistrict);
 					}
 				},
 			});
 }
</script>
<style type="text/css">
	.pageFormInfoContent dl {
		display: inline-block;
		height: 24px;
		padding-top: 8px;
		position: relative;
		margin-left: 20px /*    text-align: center;
width: 343px; */
	}

	.pageFormInfoContent dt {
		float: left;
		line-height: 21px;
		padding-right: 10px;
		text-align: right;
		width: 180px; /* 仅增加宽度 */
	}

</style>
<div layoutH="50"  class="pageFormInfoContent">
	<form method="post" id="userForm"
		action="clm/parameter/updateHospital_CLM_maintHospitalAction.action?menuId=${menuId}"
		class="pageForm required-validate"
		onsubmit="return validateCallback(this, dialogAjaxDone)">
		<input type="hidden" name="claimHospitalServiceVO.hospitalId" value="${claimHospitalServiceVO.hospitalId}" />
		
			<dl>
				<dt>医院代码：<font color="red">* </font></dt>
				<dd>
					<input id="hospitalCode1" name="hospitalVO.hospitalCode" value="${claimHospitalServiceVO.hospitalCode }"  type="text"/>
				</dd>
			</dl>
			<dl>
				<dt>医院名称：<font color="red">* </font></dt>
				<dd>
					<input id="hospitalName1" class="required" name="hospitalVO.hospitalName" value="${claimHospitalServiceVO.hospitalName }" type="text"  />
				</dd>
			</dl>
			<dl>
				<dt>医院等级：</dt>
				<dd>
					<%-- <s:select cssClass="notuseflagDelete selectToInput comboxDD"   name="hospitalVO.hospitalLevel" list="hospitalLevelMap"  
							listKey="key" listValue="value" 
						  headerKey="0" headerValue="全部"  initval="${claimHospitalServiceVO.hospitalLevel }">
					</s:select> --%>
					<Field:codeTable name="hospitalVO.hospitalLevel"
							tableName="APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL" cssClass="notuseflagDelete combox title comboxDD"
							nullOption="true" value="${claimHospitalServiceVO.hospitalLevel }" id="hospitalLevel" />
				</dd>
			</dl>
			<dl>
				<dt>定点标志：</dt>
				<dd>
					<Field:codeTable id="designated" name="claimHospitalServiceVO.isDesignated" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.isDesignated }"
					 cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>残疾鉴定资质标志：</dt>
				<dd>
					<Field:codeTable id="deformity" name="claimHospitalServiceVO.isDeformity" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.isDeformity }"
						 cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>医院状态：<font color="red">* </font></dt>
				<dd><s:select id="hospitalStatus" cssClass="notuseflagDelete combox title comboxDD"  name="claimHospitalServiceVO.hospitalStatus" 	initval="${claimHospitalServiceVO.hospitalStatus }" list="#{1:'有效',2:'无效'}"></s:select>
				</dd>
			</dl>
			<dl>
				<dt>管理机构：<font color="red">* </font></dt>
				<dd>
					<input name="hospitalVO.organCode" style="width: 30px;border-right:0px" id="branchCode" value="${claimHospitalServiceVO.organCode }" type="text" class="organ" clickId="menuBtn" showOrgName="branchname" needAll="true"/>
					<input name="hospitalVO.organName" style="width:110px;" id="branchname" value="${claimHospitalServiceVO.organName }" type="text" readOnly class="required"/>
					<a id="menuBtn" class="btnLook" href="#"></a>
				</dd>
			</dl>
			<dl>
				<dt>防癌定点医院：<font color="red">* </font></dt>
				<dd>
					<Field:codeTable id="isCancer" name="claimHospitalServiceVO.isCancer" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.isCancer }"
						 cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>是否昂贵医院：<font color="red">* </font></dt>
				<dd>
					<Field:codeTable id="isCostly" name="claimHospitalServiceVO.isCostly" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.isCostly }"
						 cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>私立标识：</dt>
				<dd>
					<Field:codeTable id="privateFlag" name="claimHospitalServiceVO.privateFlag" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.privateFlag }"
									 nullOption="true" cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>以疗养、护理、戒酒或戒毒、精神心理治疗或类似功能为主标识：</dt>
				<dd>
					<Field:codeTable id="auxiliaryFlag" name="claimHospitalServiceVO.auxiliaryFlag" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.auxiliaryFlag }"
									 nullOption="true" cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>康复标识：</dt>
				<dd>
					<Field:codeTable id="recoveryFlag" name="claimHospitalServiceVO.recoveryFlag" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimHospitalServiceVO.recoveryFlag }"
									 nullOption="true" cssClass="notuseflagDelete combox title comboxDD" />
				</dd>
			</dl>
			<dl>
				<dt>省/直辖市<font style="color:#FF0000">* </font></dt>
				<dd>
					<input type="hidden"  value="${claimHospitalServiceVO.state}" id="hospitalStateReportId">
	               	<input type="hidden" value="${claimHospitalServiceVO.city}" id="hospitalCityReportId">
	               	<input type="hidden" value="${claimHospitalServiceVO.district}" id="hospitalDistrictReportId">
                   	<Field:codeTable cssClass="selectToInput"  name="claimHospitalServiceVO.state" onChange="ProvinceChangeReportData(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="hospitalState" 
                   	value="${claimHospitalServiceVO.state }"
                   	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
					
				</dd>
			</dl>
			<dl>
				<dt>市<font style="color:#FF0000">* </font></dt>
				<dd>
					<select class="selectToInput" name="claimHospitalServiceVO.city" onchange="cityChageReportData(this);" id='hospitalCity' >
						</select>
					
				</dd>
			</dl>
			<dl>
				<dt>区/县</dt>
				<dd>
					<select class="selectToInput"  name="claimHospitalServiceVO.district" onchange="DistreactChageReportData(this);" id="hospitalDistrict" >
						</select>
					
				</dd>
			</dl>
			
			
		<div class="formBarButton">
			<ul>
			   <li><button type="button" class="but_blue" class="button"  onclick="save()">提交</button></li>
			  <li><button  type="reset" class="but_blue" onclick="resetAll()">重置</button></li>
		      <li><button type="button" class="but_blue" onclick="$.pdialog.closeCurrent();" id="userUpdateCancel">取消</button>
		    </ul>
		</div>
	</form>
</div>
