<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<div class="pageContent pageHeader" layoutH="20">
	<script type="text/javascript">
		/* function exitPage() {
			 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
			 	okCall : function() {
					navTab.closeCurrentTab();
			 	}
			 });
		} */
		
		$(
		$(".itemFlag" ).die().live("click",function(){
			
			if($(this).is(":checked")){
				
				$(this).next().attr("value", 0);
			}else{
				$(this).next().attr("value", 1);
			}
		})
		);
	</script>
	<!-- 分页查询访问路径 -->
	<form id="pagerForm" method="post"
		action="clm/parameter/queryHangEdorItem_CLM_claimHangEdorItemAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<form id="form1"
		action="clm/parameter/addHangEdorItem_CLM_claimHangEdorItemAction.action"
		method="post" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">可挂起的保全项目维护</h1>
		   </div>
				<div class="tabdivclassbr">
					<table class="list main_dbottom" width="100%">
						<thead>
							<tr>
								<th nowrap>选择</th>
								<th nowrap>序号</th>
								<th nowrap>保全项目</th>

							</tr>
						</thead>
						<tbody id="ContractSubLis">
							<s:iterator value="currentPage.pageItems" status="st">
								<tr align="center" target="listIdInfo" rel="${serviceName}">
									<td>
										<input class="itemFlag" type="checkbox" size="5px" name="" value="" <s:if test="itemFlag==true">checked="checked"</s:if>/>
										<s:if test="itemFlag==true">
											<input type="hidden" name="serviceVOs[${st.index}].itemFlagg" value="0"/>
										</s:if>
										<s:else>
											<input type="hidden" name="serviceVOs[${st.index}].itemFlagg" value="1"/>
										</s:else>
									</td>
									<td><div align="center" class="index">${st.index+1}</div></td>
									<td>${ serviceName}
									</td> 
									<input type="hidden" name="serviceVOs[${st.index}].lockServiceId" value="${lockServiceId}">
									<input type="hidden" name="serviceVOs[${st.index}].serviceGroup" value="${serviceGroup}">
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<!-- 分页查询区域 -->
					<div class="panelBar">
						<div class="pages">
							<span>显示</span>
							<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
								name="select"
								onchange="navTabPageBreak({numPerPage:this.value})"
								value="currentPage.pageSize">
							</s:select>
							<span>条，共${currentPage.total}条</span>
						</div>
						<div class="pagination" targetType="navTab"
							totalCount="${currentPage.total}"
							numPerPage="${currentPage.pageSize}" pageNumShown="10"
							currentPage="${currentPage.pageNo}"></div>
					</div>
		</div>
		<div class="formBarButton">
						<ul>
							<li><button class="but_blue" type="submit">保存</button> </li>
							<li><button class="but_gray" type="button" onclick="exit();">退出</button></li>
						</ul>
			   </div>
	</form>
</div>