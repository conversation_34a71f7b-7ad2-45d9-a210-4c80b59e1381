<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
 <script type="text/javascript" src="clm/pages/parameter/ciitcLinkSwitchSetInit.js"></script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">
var currentOrganCode = '${currentOrganCode}';
//产品快速查询
//定义产品险种集合
var _businessArray = new Array();
var val_flag = "";  //定义一个标识，防止多次重复验证
$("#businessSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#businessSearchId", navTab.getCurrentPanel()).val();
	if(value != val_flag){
		val_flag = value;
		var optionStr = "";
		for(var i=0;i<_businessArray.length;i++){
			var obj = _businessArray[i];
			var text = obj.productNameSys;
			if(text.indexOf(value) != "-1"){//${productCodeSys}_
				optionStr = optionStr + "<option value='"+ obj.productCodeSys +"'>"+  obj.productCodeSys + "_" + obj.productNameSys + "</option>";
			}
		}
		$("#ruleAllProduct", navTab.getCurrentPanel()).html("");
		$("#ruleAllProduct", navTab.getCurrentPanel()).append(optionStr);
	}
});

function AppendItem(allMenu, menu, isAll) {
	for (j=0; j<document.getElementById(allMenu).length; j++){
		if (isAll == true || document.getElementById(allMenu).options[j].selected){
			//GET VALUE
			document.getElementById(allMenu).options[j].selected = false;
			//GET LENGTH
			DesLen = document.getElementById(menu).length;
			// NEW OPTION
			
			var flag = true;
			for(var i=0;i<document.getElementById(menu).options.length;i++) {
				//alert("document.getElementById(allMenu).options[j].value:" + document.getElementById(allMenu).options[j].value);
				//alert("document.getElementById(menu).options[i].value:" + document.getElementById(menu).options[i].value);
				//alert("i:" + i);
				if(document.getElementById(allMenu).options[j].value == document.getElementById(menu).options[i].value) {
					flag = false;
					//alert('jj');
					break;
				}
			}
			if(flag) {
				document.getElementById(menu).options[DesLen] = new Option(LTrim(document.getElementById(allMenu).options[j].text), document.getElementById(allMenu).options[j].value);
			}
			
			
			document.getElementById(allMenu).remove(j);
			j--;
		}
	}
}

function LTrim(str) {
	var whitespace = new String("　 \t\n\r");
	var s = new String(str);
	if (whitespace.indexOf(s.charAt(0)) != -1) {
		var j = 0, i = s.length;
		while (j < i && whitespace.indexOf(s.charAt(j)) != -1) {
			j++;
		}
		s = s.substring(j, i);
	}
	return s;
}
</script>
<s:iterator value="businessProductListVO" var="bus" >
	<script>
		var obj = new Object();
		obj.productCodeSys = '<s:property value="#bus.productCodeSys"/>';
		obj.productNameSys = '<s:property value="#bus.productNameSys"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>
<!-- 分页表单 -->

<div layoutH="20" style="background: #fafafa" id="allForce">

    <form id="pagerForm" method="post"
		action="clm/parameter/ciitcLinkSwitchSetInit_CLM_ciitcLinkSwitchSetAction.action"
		onsubmit="return navTabSearch(this,'caseQueryTabs1')">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<!-- 查询中保信设置 -->
    <form id="setJspForm"
		action="clm/parameter/ciitcLinkSwitchSetInit_CLM_ciitcLinkSwitchSetAction.action"
		method="post" onsubmit="return navTabSearch(this,'caseQueryTabs1')"
		class="pagerForm required-validate" rel="pagerForm">
          <div class="divfclass">
		      <h1>
			      <img src="clm/images/tubiao.png">中保信对接开关查询
		      </h1>
	      </div>
	
	     <div class="pageFormInfoContent">
	     	<dl>
			   <dt>受理机构</dt>
			   <dd>
				  <div id="1">
						<s:if test="linkSwitchSettingsVO.organCode != null">
							<input style="width: 30px; border-right: 0px" type="text"
								size="2" name="linkSwitchSettingsVO.organCode"
								id="operatorOrganMana1"
								value="<s:property value='linkSwitchSettingsVO.organCode'/>"
								onclick='manaOperatorMySelectOrgan2()' clickId="findUserBtn"
								showOrgName="findUserOrgName" needAll="true" />
							<input style="width: 110px;" type="text" size="11" readOnly
								id="findUserOrgName"
								value="<Field:codeValue value="${linkSwitchSettingsVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
						</s:if>
						<s:else>
							<input style="width: 30px; border-right: 0px" type="text"
								size="2" name="linkSwitchSettingsVO.organCode"
								id="operatorOrganMana1"
								value="<s:property value='linkSwitchSettingsVO.organCode'/>"
								class="organ" onclick='manaOperatorMySelectOrgan2()' clickId="findUserBtn"
								showOrgName="findUserOrgName" needAll="true" />
							<input style="width: 110px;" type="text" size="11" readOnly
								id="findUserOrgName"
								value="<Field:codeValue value="${linkSwitchSettingsVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
						</s:else>
					</div>
					<div id="2">
						<a id="findUserBtn" onclick='manaOperatorMySelectOrgan2()'
							class="btnLook" href="#" style="position: relative;"></a>
					</div>
			   </dd>
		    </dl>
		    <dl>
			   <dt>
				  <button type="button" class="but_blue" id="queryLink" onclick="queryLinkSwitchSettings();">查询</button>
	    	   </dt>
	        </dl>
 	     </div>
    </form>
	
	
	<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">中保信对接开关查询列表</h1>
	</div>
	<div class="tabdivclassbr">
<!-- 				<div style="overflow: auto;"> -->
		<table class="list main_dbottom" width="100%" >
			<thead>
				<tr>
					<th nowrap>选择</th>
					<th nowrap>序号</th>
					<th nowrap>受理机构</th>
					<th nowrap>调用产品</th>
					<th nowrap>理赔类型</th>
					<th nowrap>业务渠道</th>
					<th nowrap>账单金额</th>
					<th nowrap>理算金额</th>
					<th nowrap>产品代码</th>
					<th nowrap>是否已获得客户授权</th>
					<th nowrap>提交人</th>
					<th nowrap>提交日期</th>
					<th nowrap>操作</th>
				</tr>
			</thead>
			<tbody id="checkBody">
			   <s:if test="imageFlag != null">
				  <tr>
					  <td colspan="13">
						  <div class="noRueryResult">请选择条件查询数据！</div>
					  </td>
				  </tr>
			   </s:if>
			   <s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
				  <tr>
					  <td colspan="13">
						   <div class="noRueryResult">没有符合条件的查询结果！</div>
					  </td>
				  </tr>
			   </s:elseif>
				  <s:iterator value="currentPage.pageItems"   status="st" id = "claimSurveyRule">
                      <tr align="center"  target="listIdInfo" rel="${listId}" id="surveyRule${listId}">
						 <td><input type="radio" name="r" onclick="getSelectInfo(this);"
						 value="${listId}|${organCode}|${productType}|${claimTypes}|${businessChannel}|${minBillAmount}|${maxBillAmount}|${minCalcAmount}|${maxCalcAmount}|${productCodes}|${userRealName}|${submitDate}"></td>
						 <td align="center">${st.index+1}</td>
						 <td align="center">${organCode}</td>
						 <td align="center">
						 <Field:codeValue value="${productType}"
										tableName="APP___CLM__DBUSER.T_CALL_PRODUCT_TYPE" />
						<%--  ${productTypeName}<input type="hidden" value="${productType}"> --%>
						 </td>
						 <td align="center">
						 <input type="hidden" value="${claimTypes}">
						 <c:forEach items="${claimTypes}" var="i">
									 	<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${i}"/>
									 </c:forEach>
						 </td>
						 <td align="center"><s:if test="businessChannel !=null && businessChannel == 1">个人</s:if>	</td>
						 <td align="center">
						 <s:if test="minBillAmount !=null && maxBillAmount == null">>=${minBillAmount }</s:if>
						 <s:if test="minBillAmount ==null && maxBillAmount != null"> <=${maxBillAmount }</s:if>
						 <s:if test="minBillAmount !=null && maxBillAmount != null">[${minBillAmount }，${maxBillAmount }]</s:if>
						 </td>
						 <td align="center">
						 <s:if test="minCalcAmount !=null && maxCalcAmount == null">>=${minCalcAmount }</s:if>
						 <s:if test="minCalcAmount ==null && maxCalcAmount != null"><=${maxCalcAmount }</s:if>
						 <s:if test="minCalcAmount !=null && maxCalcAmount != null">[${minCalcAmount }，${maxCalcAmount }]</s:if>
						 </td>
						 <td align="center">${productCodes}</td>
						 <td align="center"><s:if test="isGetAuthorize !=null && isGetAuthorize == 1">是</s:if></td>
						 <td align="center">${userRealName}</td>
						 <td align="center">
						    <s:date name="submitDate" format="yyyy-MM-dd"/>
						 </td>
						 <td>
							 <a title="删除" class="btnDel" id='delButton' href='javascript:void(0);' onclick="checkDelete('${listId}',this);">删除</a>
						 </td>
					  </tr>
				  </s:iterator>
			</tbody>
		</table>
<!-- 				</div> -->
		<div class="panelBar" >
			<div class="pages">
				<span>显示</span>
					<s:select   list="#{20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
				    </s:select>
				<span>条，共${currentPage.total}条</span>		
			</div>
			<div class="pagination" targetType="navTab"
				 totalCount="${currentPage.total}"
				 numPerPage="${currentPage.pageSize}" pageNumShown="20"
				 currentPage="${currentPage.pageNo}">
		    </div>
		</div>
	</div>
	
   <form id="saveLinkSet" method="post"
		action="clm/parameter/saveCiitcLinkSwitchSet_CLM_ciitcLinkSwitchSetAction.action"
		onsubmit="return validateCallback(this, saveNewsMethod)"
		class="pagerForm required-validate">	
	<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">中保信对接开关信息</h1>
	</div>
	<div class="panelPageFormContent" id="SpeFestival">
	<input type="hidden" id="listId" name="addLinkSwitchSettingsVO.listId" value="${addLinkSwitchSettingsVO.listId }"/>
	    <dl>
	       <dt><font style="color:red">* </font>受理机构</dt>
	       <dd>
	           <input style="width: 30px; border-right: 0px" type="text"
					  size="2" name="addLinkSwitchSettingsVO.organCode"
					  id="noticePremOrganCode"
					  value="<s:property value='addLinkSwitchSettingsVO.organCode'/>"
					  onclick='manaOperatorMySelectOrgan1()' clickId="peratorSetUpBtn"
					  showOrgName="branchname" needAll="true" />
			   <input style="width: 110px;" type="text" size="11" readOnly
					  id="branchname"
					  value="<Field:codeValue value="${addLinkSwitchSettingsVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
               <a id="peratorSetUpBtn" onclick='manaOperatorMySelectOrgan1()'
							class="btnLook" href="#" style="position: relative;"></a>
	       </dd>
	    </dl>
	    
	    <dl>
	       <dt><font style="color:red">* </font>调用产品</dt>
	       <dd>
	           <Field:codeTable cssClass="combox title"  id="productType"  name="addLinkSwitchSettingsVO.productType" value="${addLinkSwitchSettingsVO.productType}" 
					  tableName="APP___CLM__DBUSER.T_CALL_PRODUCT_TYPE" orderBy="product_type_code"  ></Field:codeTable>
	       </dd>
	    </dl>
	    
	    <dl>
	       <dt><font style="color:red">* </font>业务渠道</dt>
	       <dd>
	          <!--  <input type="text" name="addLinkSwitchSettingsVO.businessChannel" id="businessChannel" value="个人"
					  size="25"  readonly="readonly"/> -->
					  <select name="addLinkSwitchSettingsVO.businessChannel" id="businessChannel">
					  <option value="1" >个人</option>
					  </select>
	       </dd>
	    </dl>
	</div>
	
    <div>
        <dl>
           <dd style="float:left;padding-left:108px;"><span  style="float:left;line-height:3;">理赔类型</span></dd>
		   <dd style="float:left;padding-left:10px;line-height:3;" >
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType1" value="01" <s:if test="01 in strTypeList">checked="checked"</s:if>/>身故
			   </span>
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType4" value="04" <s:if test="04 in strTypeList">checked="checked"</s:if>/>高残
			   </span>
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType3" value="03" <s:if test="03 in strTypeList">checked="checked"</s:if>/>重大疾病
			   </span>
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType2" value="02" <s:if test="02 in strTypeList">checked="checked"</s:if>/>伤残
			   </span>
			   <%-- <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType11" value="11" <s:if test="11 in strTypeList">checked="checked"</s:if>/>豁免
			   </span> --%>
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType8" value="08" <s:if test="08 in strTypeList">checked="checked"</s:if>/>医疗
			   </span>
		       <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType10" value="10" <s:if test="10 in strTypeList">checked="checked"</s:if>/>特种疾病
			   </span>
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType6" value="06" <s:if test="06 in strTypeList">checked="checked"</s:if>/>一般失能
			   </span>
			   <span>
					<input type="checkbox" name="addLinkSwitchSettingsVO.claimTypes" id="claimType7" value="07" <s:if test="07 in strTypeList">checked="checked"</s:if>/>重度失能
			   </span>
		    </dd>
        </dl>
    </div>
    <div class="panelPageFormContent"  style="width: 100%;border-right:0px;line-height:3;">
       <dl>
          <dt>是否已获得客户授权</dt>
          <dd>
             <span>
                   <input type="checkbox" checked="checked" onclick="return false;" name="addLinkSwitchSettingsVO.isGetAuthorize" id="isGetAuthorize" value="1"/>
             </span>
          </dd>
       </dl>
       <dl style="line-height:3;float:left;">
          <table class="main_text">
			     <tr>
			    	 <td><span>账单金额>=</span></td>
			    	 <td><input type="text" name="addLinkSwitchSettingsVO.minBillAmount" id="minBillAmount" style="width: 75%;" size="10" onkeyup="this.value=this.value.replace(/\D/g,'')"/></td>
			    	 <td style="border-right:0px;line-height:3;"><span>账单金额<=</span></td>
			    	 <td><input type="text" name="addLinkSwitchSettingsVO.maxBillAmount" id="maxBillAmount" style="width: 75%;" size="10" onkeyup="this.value=this.value.replace(/\D/g,'')"/></td>
			     </tr>
		  </table>
       </dl>
       <dl style="line-height:3;float:left;">
          <table class="main_text">
			     <tr>
			    	 <td><span>理算金额>=</span></td>
			    	 <td><input type="text" name="addLinkSwitchSettingsVO.minCalcAmount" id="minCalcAmount" style="width: 75%;" size="10" onkeyup="this.value=this.value.replace(/\D/g,'')"/></td>
			    	 <td style="border-right:0px;line-height:3;"><span>理算金额<=</span></td>
			    	 <td><input type="text" name="addLinkSwitchSettingsVO.maxCalcAmount" id="maxCalcAmount" style="width: 75%;" size="10"  onkeyup="this.value=this.value.replace(/\D/g,'')"/></td>
			     </tr>
		  </table>
       </dl>
    </div>
	   <!-- 产品（险种）begin -->
		<div>
				<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">产品代码
						</h1>
					</div>
			<div id="productForm" class="main_text">
				<table width="98%">
					<tr>
						<td><span  style="float:left;line-height:1.6;">产品快速查询：</span><input type="text" id="businessSearchId"  style="float:left;" /></td>
						<td></td>
						<td></td>
					</tr>
					<tr height="30px;">
						<td>
							<dl class='nowrap'>
								<dt>产品</dt>
							</dl>
						</td>
						<td>
						</td>
						<td>
							<dl class='nowrap'>
								<dt>参与产品</dt>
							</dl>
						</td>
					</tr>
					<tr>
						<td width="40%">
							<div >
								<select id="ruleAllProduct" name="allProduct" multiple="multiple"
								style="height:120px; width:100%;" size=5
								ondblclick="return AppendItem('ruleAllProduct', 'ruleProductList', false);">
									<s:iterator value="businessProductListVO"  status="st" id="businessProduct">
										<option value="${businessProduct.productCodeSys}" >${businessProduct.productCodeSys}_${businessProduct.productNameSys}</option> 
									</s:iterator>		
								
								</select>
							</div>
						</td>
						<td align="center" width="8%">
<!-- 							<div style="margin: 0 30px 0 30px; float: left;"> -->
<!-- 								<div class="buttonActive"> -->
									<div class="buttonContent">
										<button class="but_gray" id="toRightP"
											onclick="return AppendItem('ruleAllProduct', 'ruleProductList', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;">></button>
									</div>
<!-- 								</div> -->
								<div style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToRightP"
											onclick="return AppendItem('ruleAllProduct','ruleProductList', true);"
											type="button">>></button>
									</div>
								</div>
								<div style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id="toleftP"
											onclick="return AppendItem('ruleProductList', 'ruleAllProduct', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToLeftP"
											onclick="return AppendItem('ruleProductList','ruleAllProduct', true);"
											type="button"><<</button>
									</div>
								</div>
<!-- 							</div> -->
						</td>
						<td width="40%">
							<div >
								<select id="ruleProductList" name="addLinkSwitchSettingsVO.productCodes"
										multiple="multiple" style="height:120px; width:100%;" size=5
										ondblclick="return AppendItem('ruleProductList','ruleAllProduct',  false);">
										<s:iterator value="businessStrList" var="businessStr">
														<option value="${productCodeSys}">
															${productCodeSys}_${productNameSys}</option>
													</s:iterator>
								</select>
							</div>
						</td>
					</tr>
				</table>
			</div>
		</div>		
<!-- 产品（险种）end -->
	<table class="main_text main_tabdiv">  
		<tr>
			<td><span>提交人</span></td>
			<td><span id="submitor"></span></td>
			<td><span>提交日期</span></td>
			<td><span id="submitTime"></span></td>
		</tr>
	</table>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button class="but_blue" type="button" id="saveRule"  onclick="saveSet()">保存</button>
			</li>
			<li>
				<a class="but_blue main_buta"
				  href="clm/parameter/linkSwitchSettingsTraceInit_CLM_ciitcLinkSwitchSetAction.action"
				  target="dialog" title="中保信开关设置" rel="page2" type="button" width="1000" height="550" resizable="false" maxable="false" minable="false" >配置轨迹查询</a>
			</li>
			<li>
				<button class="but_blue" type="button" id="saveRule"  onclick="cleanSave()">重置</button>
			</li>
			<li>
				<button class="but_gray" type="button" id="exitbtn"  onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
  </form>
    
</div>