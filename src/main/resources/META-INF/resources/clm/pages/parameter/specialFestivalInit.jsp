<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/specialFestivalInit.js"></script>

<form id="pagerForm" method="post"
	action="clm/parameter/findClaimHolidayByPages_CLM_maintainSpecialFestivalAction.action">
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
</form>
<div layoutH="0">
	<form action="" method="post" name="queryHolidayForm" id="queryHolidayFormId"
		class="pageForm required-validate"
		onsubmit="return navTabSearch(this);" rel="pagerForm">
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">查询条件</h1>
		</div>
		<div class="pageFormInfoContent">
			<dl>
				<dt>年份</dt>
				<dd>
					<Field:codeTable name="claimHolidayVO.holidayYearCode"
						tableName="APP___CLM__DBUSER.T_HOLIDAY_YEAR" cssClass="notuseflagDelete combox title comboxDD"
						nullOption="true" value="${claimHolidayVO.holidayYearCode}" id="holidayYear" />
				</dd>
			</dl>
			<dl>
				<dt>节假日名称</dt>
				<dd>
					<Field:codeTable name="claimHolidayVO.holidayNameCode"
						tableName="APP___CLM__DBUSER.T_HOLIDAY_NAME" cssClass="notuseflagDelete combox title comboxDD"
						nullOption="true" value="${claimHolidayVO.holidayNameCode}" id="holidayName" />
				</dd>
			</dl>
			<dl>
				<dt>
					<button type="button" class="but_blue" id="queryHoliday" >查询</button>
	    		</dt>
	    	</dl>
		</div>
	</form>
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">已配置信息</h1>
	</div>
	<div id="claimHoliday" class="tabdivclassbr">
		<table id="claimHolidayTable" class="list" style="width: 100%;" >
			<thead>
				<tr align="center">
				    <th nowrap>选择</th>
				    <th nowrap>序号</th>
					<th nowrap>年份</th>
					<th nowrap>节假日名称</th>
					<th nowrap>节假日起期</th>
					<th nowrap>节假日止期</th>
					<th nowrap>操作</th>
				</tr>
			</thead>
			<tbody id="claimHolidayTbody">
				<s:if test="imageFlag != null">
					<tr>
						<td colspan="7"><div class="noRueryResult">请选择条件查询数据！</div></td>
					</tr>
				</s:if>
				<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="7"><div class="noRueryResult">没有符合条件的查询结果！</div></td>
					</tr>
				</s:elseif>
				<s:iterator value="currentPage.pageItems" status="st" >
					<tr align="center">
						<td>
							<input type="radio" name="holidayId" onclick="queryClaimHoliday(this);" >
						</td> 
						<td>
							<input type="hidden" value="${holidayId }">
							<s:property value="#st.index + 1" />
						</td>
						<td>
							<input type="hidden" value="${holidayYearCode }">
							<Field:codeValue tableName="APP___CLM__DBUSER.T_HOLIDAY_YEAR" value="${holidayYearCode}" ></Field:codeValue>
						</td>
						<td>
							<input type="hidden" value="${holidayNameCode }">
							<Field:codeValue  tableName=" APP___CLM__DBUSER.T_HOLIDAY_NAME" value="${holidayNameCode }"/>
						</td>
						<td>
							<input type="hidden" value="${starTimeStr }">
							<s:date name='holidayStartTime' format='yyyy-MM-dd' />
						</td>
						<td>
							<input type="hidden" value="${endTimeStr }">
							<s:date name='holidayEndTime' format='yyyy-MM-dd'/>
						</td>
						<td><a class="btnDel" id='delButton' href='javascript:void(0);' onclick="delHoliday('${holidayId}')">删除</a></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar" >
			<div class="pages">
				<span>显示</span>
				<s:select   list="#{20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize"></s:select>
				<span>条，共${currentPage.total}条</span>		
			</div>
		<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}">
		</div>
	</div>
</div>
	<div class="divfclass">
		<h1><img src="clm/images/tubiao.png">配置信息</h1>
	</div>
	<form action="" method="post" id="editHolidayFormId" novalidate="novalidate"
				class="required-validate" onsubmit="return validateCallback(this, navTabAjaxDone)">
		<div class="panelPageFormContent" id="SpeFestival">
			<dl> 
				<dt><font style="color:red">* </font>年份</dt>
				<dd>
					<input id="saveFlag" type="hidden" />
					<input id="holidayId" type="hidden" name="claimHolidayVO.holidayId" />
					<Field:codeTable cssClass="combox title"  id="holidayYearId"  name="claimHolidayVO.holidayYearCode" value="${claimHolidayVO.holidayYearCode}" 
						tableName="APP___CLM__DBUSER.T_HOLIDAY_YEAR" nullOption="true" ></Field:codeTable>                                   
                </dd>
 			</dl>
			<dl> 
 				<dt><font style="color:red">* </font>节假日名称</dt> 
				<dd>
					<Field:codeTable cssClass="combox title"  id="holidayNameId"  name="claimHolidayVO.holidayNameCode" value="${claimHolidayVO.holidayNameCode}" 
						tableName="APP___CLM__DBUSER.T_HOLIDAY_NAME" nullOption="true" ></Field:codeTable>                                   
                </dd>
 			</dl>
 			<dl> 
				<dt><font style="color:red">* </font>节假日起期</dt> 
 				<dd>
					<input type="expandDateYMD" name="claimHolidayVO.holidayStartTime" id="startDate"
						     value="<s:date name='claimHolidayVO.holidayStartTime' format='yyyy-MM-dd'/>"/>
					<a class="inputDateButton" href="javascript:;">选择</a>
				</dd> 
 			</dl>
 			<dl> 
				<dt><font style="color:red">* </font>节假日止期</dt> 
 				<dd>
					<input type="expandDateYMD" name="claimHolidayVO.holidayEndTime" id="endDate"
						     value="<s:date name='claimHolidayVO.holidayEndTime' format='yyyy-MM-dd'/>"/>
					<a class="inputDateButton" href="javascript:;">选择</a>
				</dd> 
 			</dl>
 		</div>
 	</form>
	<div class="formBarButton">
		<ul>
			<li><button class="but_blue"  type="button" id="clearSource">新增</button></li>
			<li><button class="but_blue"  type="button" id="saveHoliday">保存</button></li>
			<li><button class="but_gray" type="button" onclick="exit();">退出</button></li>
		</ul>
	</div>
</div>