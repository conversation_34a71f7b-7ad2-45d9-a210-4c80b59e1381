<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8" %>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<%@ taglib uri="/struts-tags" prefix="s" %>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" language="javascript" src="clm/js/commonMianBox.js"></script>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">
    //定义险种编码集合
    var val_flag = "";

    function findItem(menu, obj) {
        debugger
        var table = $(obj).parents("table");
        var allOptions = $(table).find("#" + menu).next().find("option");
        var value = $(obj).attr("value");
        if (value != val_flag) {
            val_flag = value;
            var optionStr = "";
            for (var i = 0; i < $(allOptions).length; i++) {
                var name = $(allOptions).eq(i).text();
                var code = $(allOptions).eq(i).val();
                if (name.indexOf(value) != "-1") {
                    optionStr = optionStr + "<option value='" + code + "'>" + name + "</option>";
                }
            }
            $(table).find("#" + menu).empty();
            $(table).find("#" + menu).append(optionStr);
        }
    }

    function AppendItem(allMenu, menu, isAll) {
        debugger
        var table = $("#" + allMenu).parents("table");
        var allMenuOptions = $(table).find("#" + allMenu).find("option");
        var menuOptions = $(table).find("#" + menu).find("option");
        var allBusiOptions = $(table).find("#" + allMenu).next().find("option");
        //获取select
        var menuSelect = $(table).find("#" + menu);
        var menuNextSelect = $(table).find("#" + menu).next();
        var allMenuSelect = $(table).find("#" + allMenu);
        var allMenuNextSelect = $(table).find("#" + allMenu).next();
        if (isAll == true) {
            var allMenuHtml = allMenuSelect.html();
            var allMenuNextHtml = allMenuNextSelect.html();
            /* var menuHtml = menuSelect.html();
            var menuNextHtml = menuNextSelect.html(); */
            allMenuSelect.empty();
            allMenuNextSelect.empty();
            menuSelect.append(allMenuHtml);
            menuNextSelect.append(allMenuNextHtml);
        }
        for (var j = 0; j < $(allMenuOptions).length; j++) {
            if ($(allMenuOptions).eq(j).is(":selected")) {
                $(allMenuOptions).eq(j).attr("selected", false);
                var flag = true;
                for (var i = 0; i < $(menuOptions).length; i++) {
                    if ($(allMenuOptions).eq(j).attr("value") == $(menuOptions).eq(i).attr("value")) {
                        flag = false;
                        break;
                    }
                }
                for (var i = 0; i < $(allBusiOptions).length; i++) {
                    if ($(allMenuOptions).eq(j).attr("value") == $(allBusiOptions).eq(i).attr("value")) {
                        $(allBusiOptions).eq(i).remove();
                    }
                }
                if (flag) {
                    menuSelect.append($(allMenuOptions).eq(j)[0].outerHTML);
                    menuNextSelect.append($(allMenuOptions).eq(j)[0].outerHTML);
                }
                $(allMenuOptions).eq(j).remove();
            }
        }
    }

    function saveSet() {
        var $options = $("#cxBusiCodeList1 option", navTab.getCurrentPanel());
        if ($options.length === 0) {
            alertMsg.error("直赔快赔不适用险种不能为空！")
            return;
        }
        $options.each(function () {
            this.selected = true;
        });
        $("#Form2", navTab.getCurrentPanel()).submit();
    }

    function clearForm() {
        $("#queryAllBusiCode1", navTab.getCurrentPanel()).attr("value", "");
        findItem('allBusiCodeList1', $("#queryAllBusiCode1", navTab.getCurrentPanel()));
        AppendItem('cxBusiCodeList1', 'allBusiCodeList1', true);
    }

    //轨迹查询
    function query() {
        $("#importServiceTrackForms", navTab.getCurrentPanel()).submit();
    }
</script>
<div layoutH="50">
    <div id="main_1" class="main_borderbg">
        <ul class="main_ul">
            <li class="clearfix">
                <h5 hasborder="true">
                    <b id="one" class="main_minus"></b><span>直赔快赔险种管理</span>
                </h5>
                <div class="main_foldContent">
                    <div class="main_bqtabdivbr">
                        <div class="panelPageFormContent">
                            <form id="Form2" onsubmit="return navTabSearch(this);" class="required-validate"
                                  action="clm/parameter/saveDirectBusi_CLM_claimDirectBusiAction.action"
                                  method="post" target="navTab">
                                <div id="productForm" class="pageFormContent">
                                    <table style="width: 98%">
                                        <tr height="30px">
                                            <td>险种</td>
                                            <td></td>
                                            <td>直赔快赔不适用险种</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <span>险种快速查询</span>
                                                <input type="text" id="queryAllBusiCode1" style="float: left;"
                                                       onpropertychange="return findItem('allBusiCodeList1',this);"/>
                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td width="40%">
                                                <div>
                                                    <select id="allBusiCodeList1"
                                                            multiple="multiple" style="height: 120px; width: 100%;"
                                                            size=5
                                                            ondblclick="return AppendItem('allBusiCodeList1', 'cxBusiCodeList1',false);">
                                                        <s:iterator value="claimDirectBusiAllVOs" status="st"
                                                                    id="claimDirectBusiAllVO">
                                                            <option value="${claimDirectBusiAllVO.busiProdCode}">${claimDirectBusiAllVO.busiProdName}</option>
                                                        </s:iterator>
                                                    </select>
                                                    <select style="display: none;">
                                                        <s:iterator value="claimDirectBusiAllVOs" status="st"
                                                                    id="claimDirectBusiAllVO">
                                                            <option value="${claimDirectBusiAllVO.busiProdCode}">${claimDirectBusiAllVO.busiProdName}</option>
                                                        </s:iterator>
                                                    </select>
                                                </div>
                                            </td>
                                            <td align="center" width="8%">
                                                <div class="buttonContent">
                                                    <button class="but_gray" id="toRightP"
                                                            onclick="return AppendItem('allBusiCodeList1', 'cxBusiCodeList1',false);"
                                                            type="button"
                                                            style="padding-left: 14px; padding-right: 14px;">>
                                                    </button>
                                                </div>
                                                <div style="clear: left; margin: 5px 0px 0px;">
                                                    <div class="buttonContent">
                                                        <button class="but_gray" id="allToRightP"
                                                                onclick="return AppendItem('allBusiCodeList1','cxBusiCodeList1', true);"
                                                                type="button">>>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div style="clear: left; margin: 5px 0px 0px;">
                                                    <div class="buttonContent">
                                                        <button class="but_gray" id="toleftP"
                                                                onclick="return AppendItem('cxBusiCodeList1', 'allBusiCodeList1', false);"
                                                                type="button"
                                                                style="padding-left: 14px; padding-right: 14px;"><
                                                        </button>
                                                    </div>
                                                </div>
                                                <div style="clear: left; margin: 5px 0px 0px;">
                                                    <div class="buttonContent">
                                                        <button class="but_gray" id="allToLeftP"
                                                                onclick="return AppendItem('cxBusiCodeList1','allBusiCodeList1', true);"
                                                                type="button"><<
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>
                                            <td width="40%">
                                                <div>
                                                    <select id="cxBusiCodeList1"
                                                            name="productList" multiple="multiple"
                                                            style="height: 120px; width: 100%;" size=5
                                                            ondblclick="return AppendItem('cxBusiCodeList1','allBusiCodeList1',  false);">
                                                        <s:iterator value="claimDirectBusiCheckVOs" status="st"
                                                                    id="claimDirectBusiCheckVO">
                                                            <option value="${claimDirectBusiCheckVO.busiProdCode}">${claimDirectBusiCheckVO.busiProdName}</option>
                                                        </s:iterator>
                                                    </select>
                                                    <select style="display: none;"></select>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="formBarButton main_bottom">
                                    <ul>
                                        <li>
                                            <button class="but_blue" type="button" id="saveRule" onclick="saveSet()">
                                                保存
                                            </button>
                                        </li>
                                        <li>
                                            <button class="but_blue" type="button" id="clearBtn" onclick="clearForm()">
                                                重置
                                            </button>
                                        </li>
                                    </ul>
                                </div>

                            </form>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <div id="main_2" class="main_borderbg">
        <ul class="main_ul">
            <li class="clearfix">
                <h5 hasborder="true">
                    <b id="two" class="main_minus"></b><span>修改轨迹查询</span>
                </h5>
                <div class="main_foldContent">
                    <div class="main_bqtabdivbr">
                        <div class="panelPageFormContent">
                            <!-- 显示列表区域 -->
                            <div id="importantServiceConfig">
                                <form id="pagerForm" method="post"
                                      action="clm/parameter/findDirectBusiLog_CLM_claimDirectBusiAction.action">
                                    <input type="hidden" name="pageNum" vaule="${currentPage.pageNo} "/>
                                    <input type="hidden" name="numPerPage" value="${currentPage.pageSize}"/>
                                </form>
                                <table class="list sortable main_dbottom" id="importantServiceConfigOperTable"
                                       width="100%">
                                    <thead>
                                    <tr align="center">
                                        <th nowrap>序号</th>
                                        <th nowrap>操作类型</th>
                                        <th nowrap>修改项</th>
                                        <th nowrap>操作人</th>
                                        <th nowrap>操作时间</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <s:if test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
                                        <tr>
                                            <td colspan="13">
                                                <div class="noRueryResult">没有符合条件的查询结果！</div>
                                            </td>
                                        </tr>
                                    </s:if>
                                    <s:iterator value="currentPage.PageItems" var="status"
                                                status="st">
                                        <tr align="center">
                                            <td>${st.index+1 }</td>
                                            <td>${operateType }</td>
                                            <td>${busiProdName }</td>
                                            <td>${operateName }</td>
                                            <th><s:date name='operateDate' format='yyyy-MM-dd HH:mm:ss'/></th>
                                        </tr>
                                    </s:iterator>
                                    </tbody>
                                </table>
                                <!-- 分页查询区域 -->
                                <div class="panelBar">
                                    <div class="pages">
                                        <span>显示</span>
                                        <s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
                                                  value="currentPage.pageSize" name="select"
                                                  onchange="navTabPageBreak({numPerPage:this.value},'importantServiceConfig')">
                                        </s:select>
                                        <span>条，共${currentPage.total}条</span>
                                    </div>
                                    <div class="pagination" targetType="navTab"
                                         rel="importantServiceConfig" totalCount="${currentPage.total}"
                                         numPerPage="${currentPage.pageSize}" pageNumShown="10"
                                         currentPage="${currentPage.pageNo}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>