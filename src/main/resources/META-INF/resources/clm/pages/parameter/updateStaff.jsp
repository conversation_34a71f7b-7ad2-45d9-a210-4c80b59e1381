<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>
<div class="pageContent">
<script type="text/javascript">
function closeUpdateStaff(){
		/*  alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){ */
				$.pdialog.closeCurrent();
		 /* 	}
		  }); */
	}
</script>
<form id="userForm" method="post" action="clm/parameter/updateStaff_CLM_maintStaffAction.action?menuId=${menuId}" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
		<%-- <input type="hidden" name="careServerVO.serverId" value="${careServerVO.serverId}" /> --%>
		
		<div class="pageFormContent nowrap" layoutH="48">
			<dl>
				<dt>服务人员类型：</dt>
				<dd>
					<input name="claimCareServerVO.serverId" value="${claimCareServerVO.serverId}" type="hidden" class="required" />
				    <Field:codeTable cssClass="combox title"  name="claimCareServerVO.serverType" tableName="APP___CLM__DBUSER.T_SERVER_TYPE" value="${claimCareServerVO.serverType}" nullOption="true"/> 
				</dd>
			</dl>
			<dl>
				<dt>用户名/业务员代码：</dt>
				<dd>
					<input name="claimCareServerVO.serverCode" value="${claimCareServerVO.serverCode}" type="text" class="required" />
				</dd>
			</dl>
			<dl>
				<dt>姓名：</dt>
				<dd>
					<input name="claimCareServerVO.name" value="${claimCareServerVO.name }" type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>证件号码：</dt>
				<dd>
					<input name="claimCareServerVO.certiCode" value="${claimCareServerVO.certiCode }" type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>机构：</dt>
					<dd><input id="branchCode" name="claimCareServerVO.organCode" value="${claimCareServerVO.organCode}" type="text" class="organ" clickId="menuBtn" showOrgName="branchname" needAll="true"/>
						<a id="menuBtn" class="btnLook" href="#" style="position: relative; left: 158px;"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<input id="branchname" type="text" readOnly/>
					</dd>
			</dl>
			<dl>
				<dt>入司日期：</dt>
				<dd>
					<input name="claimCareServerVO.entryDate" class="date" type="expandDateYMD"
													value="<s:date name='claimCareServerVO.entryDate' format='yyyy-MM-dd'/>" />
				</dd>
			</dl>
			<dl>
				<dt>电话：</dt>
				<dd>
					<input name="claimCareServerVO.phone" value="${claimCareServerVO.phone }" type="expandPhone"/>
				</dd>
			</dl>
			<dl>
				<dt>手机号码：</dt>
				<dd>
					<input name="claimCareServerVO.mobile" value="${claimCareServerVO.mobile }" type="expandMobile" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>电子邮箱：</dt>
				<dd>
					<input name="claimCareServerVO.email" value="${claimCareServerVO.email }" type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>服务起期：</dt>
				<dd>
					<input name="claimCareServerVO.serverStartDate" class="date" type="expandDateYMD"
													value="<s:date name='claimCareServerVO.serverStartDate' format='yyyy-MM-dd'/>" />
				</dd>
			</dl>
			<dl>
				<dt>志愿者职务：</dt>
				<dd>
					<input name="claimCareServerVO.volunteerJob" value="${claimCareServerVO.volunteerJob }" type="text" />
				</dd>
			</dl>
			<dl>
				<dt>志愿者级别：</dt>
				<dd>
					<input name="claimCareServerVO.volunteerLevel" value="${claimCareServerVO.volunteerLevel }" type="text" />
				</dd>
			</dl>
			<dl>
				<dt>有效标志：</dt>
				<dd>
					<%-- <input name="claimCareServerVO.validFlag" value="${claimCareServerVO.validFlag }" type="text" /> --%>
					<Field:codeTable cssClass="combox title"  name="claimCareServerVO.validFlag" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCareServerVO.validFlag}" nullOption="true"/> 
				</dd>
			</dl>
			<dl>
				<dt>工作履历：</dt>
				<dd>
					<input name="claimCareServerVO.workRecord" value="${claimCareServerVO.workRecord }" type="text" />
				</dd>
			</dl>
			<dl>
				<dt>志愿履历：</dt>
				<dd>
					<input name="claimCareServerVO.volunteerRecord" value="${claimCareServerVO.volunteerRecord }" type="text" />
				</dd>
			</dl>
		</div>
		
		<div class="formBarButton">
			<ul>
				<li><button class="but_blue"   type="submit" >保存</button></li>
				<li><button type="button" onclick="closeUpdateStaff();" id="userUpdateCancel">取消</button></li>
			</ul>
		</div>
		
		
	</form>
</div>
