<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>

<script type="text/javascript">
	$("#validateBtn",$.pdialog.getCurrent()).click(
		function validateBtn(){
			var branchname =$("#branchnameAddPhone",$.pdialog.getCurrent()).val();
			var branchCode =$("#branchCode",$.pdialog.getCurrent()).val();
			var branchPhone = $("#branchPhone").val();
			var flag = true;
			if($.trim(branchCode)==""){
				flag = false;
				alertMsg.error("请录入必录项!");
				$("#alertMsgBox .toolBar .button").off().on("click",function(){
					$("#branchCode",$.pdialog.getCurrent()).focus();
				});
				return false;
			}
			if($.trim(branchname)==""){
				flag = false;
				alertMsg.error("请录入必录项!");
				$("#alertMsgBox .toolBar .button").off().on("click",function(){
					$("#branchnameAddPhone",$.pdialog.getCurrent()).focus();
				});
				return false;
			}
			if($.trim(branchPhone)==""){
				flag = false;
				alertMsg.error("请录入必录项!");
				$("#alertMsgBox .toolBar .button").off().on("click",function(){
					$("#branchPhone",$.pdialog.getCurrent()).focus();
				});
				return false;
			}
			var isok= $("#claimPhoneForm").valid();  
			$("#alertMsgBox .toolBar .button").on("click",function(){
			    $(".error:eq(0)",$.pdialog.getCurrent()).focus();
			});
			if(!isok){ 
		      return false;
			}
// 			$("#claimPhoneForm", navTab.getCurrentPanel()).submit() ;
		}
	);
</script>
<div layoutH="0" >
	<form id="claimPhoneForm" method="post" action="clm/parameter/saveClaimPhone_CLM_maintainPhoneAction.action" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
		<div class="pageFormInfoContent">
			
			<dl>
				<dt>所属机构代码</dt>
				<dd>
					<input name="claimPhoneServiceVO.organCode" id="branchCode" type="text" class="organ" clickId="menuBtnAddPhone" showOrgName="branchnameAddPhone" needAll="true" data-grade-in="01,02,03"    readOnly/>
					<a id="menuBtnAddPhone" class="btnLook" href="#"></a>
				</dd>
			</dl>
			<dl>
				<dt>所属机构</dt>
				<dd>
					<input name="claimPhoneServiceVO.orgName" id="branchnameAddPhone" type="text" readOnly class="required"/>
				</dd>
			</dl>
			
			<dl>
				<dt>理赔服务电话：</dt>
				<dd>
					<input name="claimPhoneServiceVO.servicePhone" class="required" type="expandPhone" id="branchPhone"/>
				</dd>
			</dl>
			<dl>
				<dt>email地址：</dt>
				<dd><input name="claimPhoneServiceVO.email" type="text" id="branchEmail"/>@newchinalife.com
				</dd>
			</dl>
		</div>
		
        <div class="formBarButton">
			<ul>
				<li><button type="submit" class="but_blue" id="validateBtn"  >提交</button></li>
				<li><button type="button" class="but_blue" onclick="document.getElementById('claimPhoneForm').reset();">重置</button></li>
				<li><button type="button" class="but_blue close">取消</button></li>
			</ul>
		</div>
	</form>
</div>
