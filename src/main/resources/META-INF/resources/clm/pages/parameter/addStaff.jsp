<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script src="/uploads/common/js/jquery-1.4.2.min.js" type="text/javascript"></script>
<script type="text/javascript" language="javascript" src="clm/pages/clm_care/pre_care_service.js"></script>
<script type="text/javascript">
/* function exit(){
		 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
				$.pdialog.closeCurrent();
		 }
		 });
	} */
 function serverCodeNone() {
	var addStaffServerType = $("#addStaffServerType", navTab.getCurrentPanel()).val();
	if (addStaffServerType == "3") {
		$("#addStaffServerCode", navTab.getCurrentPanel()).css("display","none");
	} else {
		$("#addStaffServerCode", navTab.getCurrentPanel()).css("display","");
	}
}
</script>
<div class="pageContent">
	<form name="form2" id="form2" method="post" action="clm/parameter/saveStaff_CLM_maintStaffAction.action?menuId=${menuId}" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
		<div class="pageFormInfoContent">
			<dl>
				<dt>服务人员类型：</dt>
				<dd>
				    <Field:codeTable cssClass="combox title"  name="careServerAVO.serverType" tableName="APP___CLM__DBUSER.T_SERVER_TYPE" nullOption="true" id="addStaffServerType" onChange="serverCodeNone()"/> 
				</dd>
			</dl>
			<dl id="addStaffServerCode">
				<dt>用户名/业务员代码：</dt>
				<dd>
					<input name="careServerAVO.serverCode"  type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>服务人员姓名：</dt>
				<dd>
					<input name="careServerAVO.name" id="addStaffName" type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>证件号码：</dt>
				<dd>
					<input name="careServerAVO.certiCode"  type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>机构：</dt>
					<dd>
						<%-- <input id="branchCode" name="careServerAVO.organCode" value="${careServerVO.organCode}" type="text" class="organ" clickId="menuBtn" showOrgName="branchname" needAll="true"/>
						<a id="menuBtn" class="btnLook" href="#" style="position: relative; left: 158px;"></a> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<input id="branchname" type="text" readOnly/> --%>
						<input id="branchCode" name="careServerAVO.organCode" style="width: 30px; border-right: 0;" size="8"
								value="${careServerAVO.organCode}" type="text" class="organ" clickId="staffMenuBtn" showOrgName="branchname" needAll="true" />
						<input id="branchname" type="text" style="width: 110px" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${careServerAVO.organCode}" />" readOnly /> 
						<a id="staffMenuBtn" class="btnLook" href="#" style="position: relative;"></a>
					</dd>
			</dl>
			<dl>
				<dt>入司日期：</dt>
				<dd>
					<!-- <input name="careServerAVO.entryDate"  type="text" class="date" /> -->
					<input type="expandDateYMD" name="careServerAVO.entryDate" size="22" class="date"
						value="<s:date name='careServerAVO.entryDate' format='yyyy-MM-dd' />" />
					<a class="inputDateButton" href="javascript:;">选择</a>
				</dd>
			</dl>
			<dl>
				<dt>电话：</dt>
				<dd>
					<input name="careServerAVO.phone"  type="expandPhone"/>
				</dd>
			</dl>
			<dl>
				<dt>手机号码：</dt>
				<dd>
					<input name="careServerAVO.mobile"  type="expandMobile" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>电子邮箱：</dt>
				<dd>
					<input name="careServerAVO.email"  type="text" class="required"/>
				</dd>
			</dl>
			<dl>
				<dt>服务起期：</dt>
				<dd>
					<!-- <input name="careServerAVO.serverStartDate"  type="text"  class="date"/> -->
					<input type="expandDateYMD" name="careServerAVO.serverStartDate" size="22" class="date"
						value="<s:date name='careServerAVO.serverStartDate' format='yyyy-MM-dd' />" />
					<a class="inputDateButton" href="javascript:;">选择</a>
				</dd>
			</dl>
			<dl>
				<dt>志愿者职务：</dt>
				<dd>
					<input name="careServerAVO.volunteerJob"  type="text" />
				</dd>
			</dl>
			<dl>
				<dt>志愿者级别：</dt>
				<dd>
					<input name="careServerAVO.volunteerLevel"  type="text" />
				</dd>
			</dl>
			<dl>
				<dt>有效标志：</dt>
				<dd>
					<Field:codeTable cssClass="combox title"  name="careServerAVO.validFlag" tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true"/> 
				</dd>
			</dl>
			<dl>
				<dt>工作履历：</dt>
				<dd>
					<input name="careServerAVO.workRecord"  type="text" />
				</dd>
			</dl>
			<dl>
				<dt>志愿履历：</dt>
				<dd>
					<input name="careServerAVO.volunteerRecord"  type="text" />
				</dd>
			</dl>
		</div>
		<div class="formBarButton">
			<ul>
				<li><button class="but_blue"  type="submit">提交</button></li>
				<li><button class="but_blue"  type="button" onclick="exit();">取消</button></li>
			</ul>
		</div>
	</form>
</div>
