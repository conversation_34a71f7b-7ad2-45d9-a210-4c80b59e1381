<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>

<script type="text/javascript" src="clm/pages/parameter/maintChildrenAmntInit.js"></script>
<form id="pagerForm" method="post"
	action="clm/parameter/findChildrenAmnt_CLM_maintainChildrenAmntAction.action?leftFlag=0&menuId=${menuId }">
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
</form>

<div layoutH="11">
		<div>
				<div class="divfclass">
				  <h1><img src="clm/images/tubiao.png">未成年人保额标准查询条件</h1>
		       </div>
		       
		        
				<form action="" method="post" id="childrenAmntForm"
				class="pageForm required-validate" onsubmit="return navTabSearch(this);" rel="pagerForm">
				<div class="pageFormInfoContent">
					<dl>
						<dt>管理机构</dt>
						<dd>
							<input id="branchCodeChildren" style="width: 30px;border-right:0px" name="claimChildrenAmntVO.organCode" value="${claimChildrenAmntVO.organCode}" 
								type="text" class="organ" clickId="branchCodeChildrenBtn" showOrgName="branchnameChildren" needAll="true"/>
							<input id="branchnameChildren" style="width:110px;" type="text" readOnly value="${claimChildrenAmntVO.organName}" name="claimChildrenAmntVO.organName"/>
 							<a id="branchCodeChildrenBtn" class="btnLook" href="#" style="position: relative;"></a>
						</dd>
					</dl>
					<div class="pageFormdiv"><button class="but_blue" type="button" id="queryChildrenAmnt">查询</button></div>	
				</div>
				</form>
				
				<div class="divfclass">
				   <h1><img src="clm/images/tubiao.png">未成年人保额标准列表</h1>
		        </div>
				
				<div class="tabdivclassbr">
					<h1></h1>
					<table class="list main_dbottom" width="100%">
						<thead>
							<tr>
								<th nowrap>选择</th>
								<th nowrap>序号</th>
								<th nowrap>管理机构</th>
								<th nowrap>管理机构名称</th>
								<th nowrap>年龄区间</th>
								<th nowrap>标准保额（元）</th>
								<th nowrap>启用日期</th>
								<th nowrap>结束日期</th>
								<th nowrap>有效标识</th>
								<th nowrap>操作</th>
							</tr>
						</thead>
						<tbody>
							<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
							<s:iterator value="currentPage.pageItems"  status="st">
							<tr align="center"  target="listIdInfo" id="children${childrenAmntId}">
								<td><input type="radio" name="r" value="${childrenAmntId}" onclick="editChildrenAmnt(this.value);"></td>
								<td>${st.index+1}</td>
								<td>${organCode}</td>
								<td><Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${organCode}" /></td>
								<td>
									<s:if test="organCode eq '86'">
										${lowestAge}-${highestAge}
									</s:if>
								</td>
								<td>${standardAmnt}</td>
								<td><s:date name='startDate' format='yyyy-MM-dd'/></td>
								<td><s:date name='endDate' format='yyyy-MM-dd'/></td>
								<td>
								<input value="${validFalg}" type="hidden"/>
								<s:if test="validFalg == 0">否</s:if>
								<s:if test="validFalg == 1">是</s:if>
								</td>
								<td>
								<a title="修改" class="btnEdit" id="editButton" href="javascript:void(0);" value="${childrenAmntId}" onclick="editChildrenAmnt(this.value);">修改</a>
								<a title="删除" class="btnDel" id="delButton" href="javascript:void(0);" value="${childrenAmntId}" onclick="deleteChildrenAmnt(this.value);">删除</a>
								</td>
							</tr>
							</s:iterator>
						</tbody>
					</table>
					<div class="panelBar" >
						<div class="pages">
							<span>显示</span>
							<s:select   list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
					    		</s:select>
							<span>条，共${currentPage.total}条</span>		
						</div>
						<div class="pagination" targetType="navTab"
										totalCount="${currentPage.total}"
										numPerPage="${currentPage.pageSize}" pageNumShown="20"
										currentPage="${currentPage.pageNo}"></div>
				
					</div>
				</div>
				<div class="divfclass">
				  <h1><img src="clm/images/tubiao.png">未成年人保额标准信息</h1>
		        </div>
				<form action="" method="post" id="saveChildrenAmntForm" novalidate="novalidate"
				class="required-validate" onsubmit="return validateCallback(this, navTabAjaxDone)">
				<div class="panelPageFormContent" id="childrenAmnt">
					<dl> 
 						<dt><font style="color:red">* </font>管理机构</dt> 
						<dd>
						<input id="childrenAmntId" name="claimChildrenAmntVO.childrenAmntId" type="hidden" />
						<input id="organCode" name="claimCaseVO.organCode" value="" type="hidden" />
						<input id="organName" name="claimCaseVO.organName" 
							value="" type="text"
							postField="keyword" 
							suggestUrl="clm/pages/invoice/findOrg.jsp"
							readonly /> 
						<a class="btnLook" href="clm/invoice/queryPage_CLM_claimInvoicesPrintAction.action?leftFlag=0&menuId=${menuId}" lookupGroup="claimCaseVO">查找带回</a>
						</dd> 
 					</dl>
					<dl> 
 						<dt>年龄区间</dt> 
						    <dd style="width: 500px;">
							<select style="width: 10px;border-right:0px"
							 class="combox title"  name="claimChildrenAmntVO.lowestAge" id="lowestAgeId">
								<option value="">请选择</option>
								<option value="0">0岁</option>
								<option value="1">1岁</option>
								<option value="2">2岁</option>
								<option value="3">3岁</option>
								<option value="4">4岁</option>
								<option value="5">5岁</option>
								<option value="6">6岁</option>
								<option value="7">7岁</option>
								<option value="8">8岁</option>
								<option value="9">9岁</option>
								<option value="10">10岁</option>
								<option value="11">11岁</option>
								<option value="12">12岁</option>
								<option value="13">13岁</option>
								<option value="14">14岁</option>
								<option value="15">15岁</option>
								<option value="16">16岁</option>
								<option value="17">17岁</option>
								<option value="18">18岁</option>
							</select>
<!-- 							</dd> -->
<!-- 							<dd>至</dd> -->
<!-- 							<dd> -->
							<span style="padding:0px 15px;">至</span>
							<select style="width:80px;"
							 class="combox title"  name="claimChildrenAmntVO.highestAge" id="highestAgeId">
								<option value="">请选择</option>
								<option value="0">0岁</option>
								<option value="1">1岁</option>
								<option value="2">2岁</option>
								<option value="3">3岁</option>
								<option value="4">4岁</option>
								<option value="5">5岁</option>
								<option value="6">6岁</option>
								<option value="7">7岁</option>
								<option value="8">8岁</option>
								<option value="9">9岁</option>
								<option value="10">10岁</option>
								<option value="11">11岁</option>
								<option value="12">12岁</option>
								<option value="13">13岁</option>
								<option value="14">14岁</option>
								<option value="15">15岁</option>
								<option value="16">16岁</option>
								<option value="17">17岁</option>
								<option value="18">18岁</option>
							</select>
						</dd> 
 					</dl>
 					<dl> 
 						<dt><font style="color:red">* </font>标准保额</dt> 
 						<dd><input id="standardAmnt" class="number" maxlength="15" onkeyup="value=value.replace(/[^\d\.]/g,'')"
 						  name="claimChildrenAmntVO.standardAmnt" type="text"/>元</dd>
 					</dl>
<!--  					<dl>  -->
<!--  						<dt>元</dt>  -->
<!--  					</dl>   -->
 					<dl>
 						<dt><font style="color:red">* </font>启用日期</dt> 
 						<dd>
 						<input type="expandDateYMD" name="claimChildrenAmntVO.startDate" id="startDate"
						     value="<s:date name='claimChildrenAmntVO.startDate' format='yyyy-MM-dd'/>" class="data"/>
						<a class="inputDateButton" href="javascript:;">选择</a>
						</dd> 
 					</dl> 
 					<dl>
 						<dt>结束日期</dt> 
 						<dd>
 						<input type="expandDateYMD" name="claimChildrenAmntVO.endDate" id="endDate"
						     value="<s:date name='claimChildrenAmntVO.endDate' format='yyyy-MM-dd'/>" class="data" />
						<a class="inputDateButton" href="javascript:;">选择</a>
 						</dd> 
 					</dl>
 					<dl>
 						<dt>有效标识</dt> 
 						<dd>
 						   <Field:codeTable cssClass="combox title"  id="validFalgSelect" name="claimChildrenAmntVO.validFalg" value="1"
 						   tableName="APP___CLM__DBUSER.T_YES_NO"></Field:codeTable>
 						</dd> 
 					</dl>
 				</div>
                </form>
<!-- 				</fieldset> -->
				
				
				<div class="formBarButton">
					<ul>
						<li><button class="but_blue"  type="button" id="saveChildrenAmnt">保存</button></li>
						<li><button class="but_blue"  type="button" id="clearSource">重置</button></li>
						<li><button class="but_gray" type="button" onclick="exit();">退出</button></li>
				    </ul>
				</div>
		</div>
		
<!-- 		</div> -->
		
</div>
