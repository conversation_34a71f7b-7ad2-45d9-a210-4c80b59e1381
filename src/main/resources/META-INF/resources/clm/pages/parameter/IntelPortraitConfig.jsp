<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<form id="pagerForm" method="post"
	action="clm/parameter/selectAccidentResult_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${currentPage3.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage3.pageSize}" />
	<!-- 查询条件回调 -->
	<input type="hidden" name="claimAccResultBusiVO.accident2Code"
		value="${claimAccResultBusiVO.accResult2}" /> <input type="hidden"
		name="claimAccResultBusiVO.accident2Name"
		value="${claimAccResultBusiVO.accResult2Str}" />
</form>
<div>
	<table class="list" id="surveyApplyOperTable" width="100%">
		<thead>
			<tr>
				<th nowrap>选择</th>
				<th nowrap>出险结果2代码</th>
				<th nowrap>出险结果2名称</th>
				<th nowrap>责免险种</th>
				<th nowrap>操作</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag == null">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">请选择条件查询数据！</div>
					</td>
				</tr>
			</s:if>
			<s:if
				test="currentPage3.pageItems == null || currentPage3.pageItems.size()==0">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:if>
			<s:iterator value="currentPage3.pageItems" status="st">
				<tr align="center" target="listIdInfo">
					<td><input type="radio" class="radioIndex" name="r1"
						value="${listId}|${accResult2}|${accResult2Str}|${busiFrom}" onclick='choiceAccidentResult(this);' /></td>
					<td align="center" style="word-break: break-all;">${accResult2}</td>
					<td align="center" style="word-break: break-all;">${accResult2Str}</td>
					<td align="center" style="word-break: break-all;">${busiFrom}</td>
					<td><a title='删除' class='btnDel' id='delButton'
						href='javascript:void(0);'
						onclick="deleteCxjgBusiMatch('${listId}',this);">删除</a></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
				name="select"
				onchange="navTabPageBreak({numPerPage:this.value},'findIntelPortraitConfigAction')"
				value="currentPage3.pageSize">
			</s:select>
			<span>条，共${currentPage3.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${currentPage3.total}"
			numPerPage="${currentPage3.pageSize}" pageNumShown="20"
			rel="findIntelPortraitConfigAction"></div>
	</div>
</div>

