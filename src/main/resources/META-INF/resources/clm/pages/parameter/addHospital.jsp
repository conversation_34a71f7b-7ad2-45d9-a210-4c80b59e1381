<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
    <%@ taglib uri="/struts-tags" prefix="s"%>

<script type="text/javascript">
 function save(){//如果有必录项未录，光标指向第一个未录项
		var branchname =$("#branchname",$.pdialog.getCurrent()).val();
		var hospitalCode = $("#hospitalCode1",$.pdialog.getCurrent()).val();
		var hospitalName = $("#hospitalName1",$.pdialog.getCurrent()).val();
		var hospitalState = $("#hospitalState",$.pdialog.getCurrent()).val();
		var hospitalCity = $("#hospitalCity",$.pdialog.getCurrent()).val();
		var hospitalDistrict = $("#hospitalDistrict",$.pdialog.getCurrent()).val();
		var flag = true;
		if($.trim(hospitalCode)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalCode1",$.pdialog.getCurrent()).focus();
			});
			return false;
		} 
		if($.trim(hospitalName)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalName1",$.pdialog.getCurrent()).focus();
			});
			return false;
		} 
		if($.trim(branchname)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#branchname",$.pdialog.getCurrent()).focus();
			});
			return false;
		}
		if($.trim(hospitalState)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalState",$.pdialog.getCurrent()).focus();
			});
			return false;
		}
		if($.trim(hospitalCity)==""){
			flag = false;
			alertMsg.error("请录入必录项!");
			$("#alertMsgBox .toolBar .button").off().on("click",function(){
				$("#hospitalCity",$.pdialog.getCurrent()).focus();
			});
			return false;
		}
	var isok= $("#form2",$.pdialog.getCurrent()).valid();  
	$("#alertMsgBox .toolBar .button").on("click",function(){
	    $(".error:eq(0)",$.pdialog.getCurrent()).focus();
	});
	if(!isok){ 
      return false;
	}
	$("#form2",$.pdialog.getCurrent()).submit(); 
 }
 
 function resetAll(){
	 $("#hospitalCode1",$.pdialog.getCurrent()).val("");
	 $("#hospitalName1",$.pdialog.getCurrent()).val("");
	 $("#hospitalLeavel",$.pdialog.getCurrent()).prev().attr("value","-全部");
	 $("#designated",$.pdialog.getCurrent()).prev().attr("value","0-否");
	 $("#deformity",$.pdialog.getCurrent()).prev().attr("value","0-否");
	 $("#hospitalStatus",$.pdialog.getCurrent()).prev().attr("value","1-有效");
	 $("#branchCode",$.pdialog.getCurrent()).val("");
	 $("#branchname",$.pdialog.getCurrent()).val("");
	 $("#isCancer",$.pdialog.getCurrent()).prev().attr("value","0-否");
	 $("#isCostly",$.pdialog.getCurrent()).prev().attr("value","0-否");
 }
 
//根据省查询市
 function ProvinceChangeReportData(k) {
	var cityReportId = $("#hospitalCity", $.pdialog.getCurrent());
	var province = $(k).val();
	$("#hospitalCity", $.pdialog.getCurrent()).prev().val("");
	$("#hospitalDistrict", $.pdialog.getCurrent()).prev().val("");
 	$.ajax({
 				'type' : 'post',
 				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
 						+ province,
 				'datatype' : 'json',
 				'async' : true,
 				'success' : function(data) {
 					var data = eval("(" + data + ")");
 					$("#hospitalCity", $.pdialog.getCurrent()).empty();
 					$("#hospitalDistrict", $.pdialog.getCurrent()).empty();
 					$("<option value=''>市</option>").appendTo(cityReportId);
 					for (var i = 0; i < data.length; i++) {
 						var option1 = "<option value='" + data[i].code
 								+ "'   class='" + data[i].name + "' >"
 								+ data[i].name + "</option>";
 						$(option1).appendTo(cityReportId);
 					}
 				},
 			});
 	
 }
 //根据市查询县
 function cityChageReportData(k) {
 	var city = $(k).val();
 	var hospitalDistrict = $("#hospitalDistrict", $.pdialog.getCurrent());
 	$("#hospitalDistrict", $.pdialog.getCurrent()).prev().val("");
 	$.ajax({
 				'type' : 'post',
 				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
 						+ city,
 				'datatype' : 'json',
 				'async' : true,
 				'success' : function(data) {
 					var data = eval("(" + data + ")");
 					$("#hospitalDistrict", $.pdialog.getCurrent()).empty();
 					$("<option value=''>区/县</option>").appendTo(hospitalDistrict);
 					for (var i = 0; i < data.length; i++) {
 						var option1 = "<option value='" + data[i].code
 								+ "'   class='" + data[i].name + "' >"
 								+ data[i].name + "</option>";
 						$(option1).appendTo(hospitalDistrict);
 					}
 				},
 			});
 }
</script>
<style type="text/css">
	.pageFormInfoContent dl {
		display: inline-block;
		height: 24px;
		padding-top: 8px;
		position: relative;
		margin-left: 20px /*    text-align: center;
width: 343px; */
	}

	.pageFormInfoContent dt {
		float: left;
		line-height: 21px;
		padding-right: 10px;
		text-align: right;
		width: 180px; /* 仅增加宽度 */
	}
	
</style>
<div layoutH="0">
	<form name="form2" id="form2" method="post" action="clm/parameter/saveHospital_CLM_maintHospitalAction.action?menuId=${menuId}" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
		<div class="pageFormInfoContent">
			
			<dl>
				<dt>医院代码：<font color="red">* </font></dt>
				<dd>
					<input id="hospitalCode1" name="hospitalAVO.hospitalCode"  type="text" class="required" />
				</dd>
			</dl>
			<dl>
				<dt>医院名称：<font color="red">* </font></dt>
				<dd>
					<input id="hospitalName1" name="hospitalAVO.hospitalName"  type="text" class="required"/>
				</dd>
			</dl>
			
			<dl>
				<dt>医院等级：</dt>
				<dd>
					<%-- <s:select id="hospitalLeavel" cssClass="notuseflagDelete combox title comboxDD"   name="hospitalAVO.hospitalLevel" list="hospitalLevelMap"  
							listKey="key" listValue="value"
						  headerKey="" headerValue="全部"  >
					</s:select> --%>
					
					<Field:codeTable   name="hospitalAVO.hospitalLevel" id="hospitalLeavel"  tableName="APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL" nullOption="true" cssClass="combox title comboxDD selectChange"/>
				</dd>
			</dl>
			<dl>
				<dt>定点标志：</dt>
				<dd><s:select id="designated" cssClass="notuseflagDelete combox title comboxDD"  name="claimHospitalServiceAVO.isDesignated" 	list="#{0:'否',1:'是'}"></s:select>
<%-- 				<dd><s:select cssClass="notuseflagDelete selectToInput comboxDD"  name="claimHospitalServiceAVO.isDesignated" 	list="#{9:'全部',0:'否',1:'是'}"></s:select> --%>
				</dd>
			</dl>
			<dl>
				<dt>残疾鉴定资质标志：</dt>
				<dd><s:select id="deformity" cssClass="notuseflagDelete combox title comboxDD"  name="claimHospitalServiceAVO.isDeformity" 	list="#{0:'否',1:'是'}"></s:select>
				</dd>
			</dl>
			<dl>
				<dt>医院状态：<font color="red">* </font></dt>
				<dd><s:select id="hospitalStatus" cssClass="notuseflagDelete combox title comboxDD"  name="claimHospitalServiceAVO.hospitalStatus" 	list="#{1:'有效',2:'无效'}"></s:select>
				</dd>
			</dl>
			<dl>
				<dt>管理机构：<font color="red">* </font></dt>
				<dd>
					<!-- <input name="hospitalAVO.organCode" id="branchCode" type="text" class="organ" clickId="menuBtn" showOrgName="branchname" needAll="true" readOnly/>
					<a id="menuBtn" class="btnLook" href="#"></a> -->
					<input id="branchCode" name="hospitalAVO.organCode" style="width: 30px; border-right: 0;" size="8"
						 type="text" class="organ" clickId="hospitalMenuBtn" showOrgName="branchname" needAll="true" />
					<input id="branchname" type="text" style="width: 110px" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${hospitalAVO.organCode}" />" readOnly /> 
						<a id="hospitalMenuBtn" class="btnLook" href="#" style="position: relative;"></a>
				</dd>
			</dl>
					<input name="hospitalAVO.organName" id="branchname" type="hidden" readOnly class="required"/>
			<dl>
				<dt>防癌定点医院：<font color="red">* </font></dt>
				<dd><s:select id="isCancer"
				cssClass="notuseflagDelete combox title comboxDD"  name="claimHospitalServiceAVO.isCancer" 
				list="#{0:'否',1:'是'}"></s:select>
				</dd>
			</dl>
			<dl>
				<dt>是否昂贵医院：</dt>
				<dd><s:select id="isCostly"
				cssClass="notuseflagDelete combox title comboxDD"  name="claimHospitalServiceAVO.isCostly" 
				list="#{0:'否',1:'是'}"></s:select>
				</dd>
			</dl>
			<dl>
				<dt>私立标识：</dt>
					<Field:codeTable   name="claimHospitalServiceAVO.privateFlag" id="privateFlag"  tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" cssClass="combox title comboxDD selectChange"/>
				</dd>
			</dl>
			<dl>
				<dt>以疗养、护理、戒酒或戒毒、精神心理治疗或类似功能为主标识：</dt>
					<Field:codeTable   name="claimHospitalServiceAVO.auxiliaryFlag" id="auxiliaryFlag"  tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" cssClass="combox title comboxDD selectChange"/>
				</dd>
			</dl>
			<dl>
				<dt>康复标识：</dt>
					<Field:codeTable   name="claimHospitalServiceAVO.recoveryFlag" id="recoveryFlag"  tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true" cssClass="combox title comboxDD selectChange"/>
				</dd>
			</dl>
			<dl>
				<dt>省/直辖市<font style="color:#FF0000">* </font></dt>
				<dd>
                   	<Field:codeTable cssClass="selectToInput"  name="claimHospitalServiceAVO.state" onChange="ProvinceChangeReportData(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="hospitalState" 
                   	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
					
				</dd>
			</dl>
			<dl>
				<dt>市<font style="color:#FF0000">* </font></dt>
				<dd>
					<select class="selectToInput" name="claimHospitalServiceAVO.city" onchange="cityChageReportData(this);" id='hospitalCity' >
						</select>
					
				</dd>
			</dl>
			<dl>
				<dt>区/县</dt>
				<dd>
					<select class="selectToInput"  name="claimHospitalServiceAVO.district" onchange="DistreactChageReportData(this);" id="hospitalDistrict" >
						</select>
					
				</dd>
			</dl>
			
		</div>
		
		<div class="formBarButton">
			<ul>
				<li><button type="button" class="but_blue"  onclick="save()">提交</button></li>
				<li><button type="button" class="but_blue"  type="reset" onclick="resetAll()">重置</button></li>
				<li><button type="button" class="but_blue close">取消</button></li>
			</ul>
		</div>
	</form>
</div>
