//退出按钮
/*function exit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	   }
	 });
}*/
//修改显示数据
function editChildrenAmnt(childrenId){
	$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(0)").find("input:radio").attr("checked",true);
	//获取行数据
	var age=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(4)").text();
	var organCode=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(2)").text();
	var organName=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(3)").text();
	var standardAmnt=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(5)").text();
	var startDate=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(6)").text();
	var endDate=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(7)").text();
	var validFalgSelect=$("tr#children"+childrenId, navTab.getCurrentPanel()).find("td:eq(8)").find("input").val();
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("input#childrenAmntId").val(childrenId);
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("input#organCode").val(organCode);
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("input#organName").val(organName);
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("input#standardAmnt").val(standardAmnt);
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("input#startDate").val(startDate);
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("input#endDate").val(endDate);
	
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("select:eq(2)").val(validFalgSelect);
	claimFireEvent($("div#childrenAmnt", navTab.getCurrentPanel()).find("select:eq(2)") );
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("select:eq(0)").selectMyComBox(age.split("-")[0]);
	claimFireEvent($("div#childrenAmnt", navTab.getCurrentPanel()).find("select:eq(0)") );
	$("div#childrenAmnt", navTab.getCurrentPanel()).find("select:eq(1)").selectMyComBox($.trim(age.split("-")[1]));
	claimFireEvent($("div#childrenAmnt", navTab.getCurrentPanel()).find("select:eq(1)") );
	
}
//删除按钮
function deleteChildrenAmnt(childrenId){
	alertMsg.confirm("是否确定删除？",{
		okCall:function(){
			$.ajax({
				 'url':"clm/parameter/deleteChildrenAmnt_CLM_maintainChildrenAmntAction.action?leftFlag=0&claimChildrenAmntVO.childrenAmntId="+childrenId,
			     'type':'post',
			     'datatype':'json',
			     'async':true,
			     'success':function(data){
			   	  var data=eval("("+data+")");
			   	  alertMsg.info("操作成功");
			    }
			    });
			var url = "clm/parameter/findChildrenAmnt_CLM_maintainChildrenAmntAction.action";
			$("#childrenAmntForm", navTab.getCurrentPanel()).attr("action",url);
			$("#childrenAmntForm", navTab.getCurrentPanel()).submit();
		}
	});
	  
}
//查询按钮
$("#queryChildrenAmnt", navTab.getCurrentPanel()).click(function(){
	//var branchCode=$("#branchCode", navTab.getCurrentPanel()).val();
	var url = "clm/parameter/findChildrenAmnt_CLM_maintainChildrenAmntAction.action?leftFlag=0";
	$("#childrenAmntForm", navTab.getCurrentPanel()).attr("action",url);
	$("#childrenAmntForm", navTab.getCurrentPanel()).submit();
	
});
//重置按钮
$("#clearSource", navTab.getCurrentPanel()).click(function(){
	if($("input:radio", navTab.getCurrentPanel()).is(':checked')==true){
		$("input:radio", navTab.getCurrentPanel()).attr("checked",false);
	}
	$("#childrenAmntId", navTab.getCurrentPanel()).val("");
	$("#organCode", navTab.getCurrentPanel()).val("");
	$("#organName", navTab.getCurrentPanel()).val("");
	$("#standardAmnt", navTab.getCurrentPanel()).val("");
	$("#startDate", navTab.getCurrentPanel()).val("");
	$("#endDate", navTab.getCurrentPanel()).val("");
	$("#validFalgSelect", navTab.getCurrentPanel()).val("1");
	$("#highestAgeId", navTab.getCurrentPanel()).val("");
	$("#lowestAgeId", navTab.getCurrentPanel()).val("");
});
//保存
$("#saveChildrenAmnt", navTab.getCurrentPanel()).click(function(){
	var organCode=$("#organCode", navTab.getCurrentPanel()).val();
	var standardAmnt=$("#standardAmnt", navTab.getCurrentPanel()).val();
	var childrenAmntId=$("#childrenAmntId", navTab.getCurrentPanel()).val();
	if($.trim(organCode)==""){
	  	alertMsg.info("管理机构必填");
	  	$("#organName", navTab.getCurrentPanel()).focus();
	  	return;
	}
	if($.trim(standardAmnt)==""){
	  	alertMsg.info("标准保额必填");
	  	$("#standardAmnt", navTab.getCurrentPanel()).focus();
	  	return;
	}
	var startDate = $("#startDate", navTab.getCurrentPanel()).val().replace(/-/g, '/');
	var endDate = $("#endDate", navTab.getCurrentPanel()).val().replace(/-/g, '/');
	if(startDate ==""){
		alertMsg.error("启用日期不能为空！");
		return;
	}
	var validFalgSelect = $("#validFalgSelect", navTab.getCurrentPanel()).find("option:selected").val();
	if(endDate == "" && validFalgSelect != "1"){
		alertMsg.error("未配置结束时间，有效标识为是！");
		return;
	}
	if(startDate !="" && endDate !=""){
		if(startDate > endDate){
			alertMsg.error("启用日期不能在结束日期之后！");
			return;
		}
	}
	//校验前边的年龄需要大于后边的年龄。
	var highestAge = $("#highestAgeId", navTab.getCurrentPanel()).val();
	var lowestAgeId = $("#lowestAgeId", navTab.getCurrentPanel()).val();
	if($.trim(organCode) != "86"){
		//如果机构不是86的情况下，年龄区间是不可以添加的，给出提示。
		if($.trim(highestAge) != "" && $.trim(lowestAgeId) != ""){
			alertMsg.error("只能在总公司（86）机构进行年龄区间标准保额设置！");
			return;
		}
	} else {
		//如果是86的情况下，年龄区间必填
		if($.trim(highestAge) == "" && $.trim(lowestAgeId) == ""){
			alertMsg.error("总公司（86）机构下时年龄区间必填！");
			return;
		}
	}
	if($.trim(lowestAgeId) != "" && $.trim(highestAge) != ""){
		if(eval(lowestAgeId) > eval(highestAge)){
			alertMsg.error("前边的年龄应该小于后边的年龄！");
			return;
		}
	}
	//同一机构，同一时间段内，出现了不同的有效保额标准
	var flag="";
	$.ajax({
		 'url':"clm/parameter/saveChildrenAmntCheck_CLM_maintainChildrenAmntAction.action",
	     'type':'post',
	     'data':{"claimChildrenAmntVO.organCode":organCode,
	    	 "claimChildrenAmntVO.startDate":$("#startDate", navTab.getCurrentPanel()).val(),
	    	 "claimChildrenAmntVO.endDate":$("#endDate", navTab.getCurrentPanel()).val(),
	    	 "claimChildrenAmntVO.highestAge":highestAge,
	    	 "claimChildrenAmntVO.lowestAge":lowestAgeId,
	    	 "claimChildrenAmntVO.childrenAmntId":childrenAmntId},
	     'datatype':'json',
	     'async':false,
	     'success':function(data){
	    	var data=eval("("+data+")");
	    	flag=data.repetFlag;
	    	ageFlag = data.ageFlag;
	     }
	});
	if(flag=="1" && validFalgSelect == "1"){
		alertMsg.info("同一机构，同一时间段内，已经设置过保额！");
		return;
	}
	if(ageFlag == "1" && validFalgSelect == "1"){
		alertMsg.info("在同一时间段内某一年龄不能被重复设置！");
		return;
	}
	var url = "clm/parameter/saveChildrenAmnt_CLM_maintainChildrenAmntAction.action?claimChildrenAmntVO.organCode="+organCode;
	$("#saveChildrenAmntForm", navTab.getCurrentPanel()).attr("action",url);
	$("#saveChildrenAmntForm", navTab.getCurrentPanel()).submit();
});