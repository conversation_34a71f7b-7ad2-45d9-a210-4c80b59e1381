// 初始化清空已配置信息显示
$("#holidayYearId", navTab.getCurrentPanel()).val("");
$("#holidayNameId", navTab.getCurrentPanel()).val("");

//点击查询按钮
$("#queryHoliday", navTab.getCurrentPanel()).click(function(){
	var url = "clm/parameter/findClaimHolidayByPages_CLM_maintainSpecialFestivalAction.action";
	$("#queryHolidayFormId", navTab.getCurrentPanel()).attr("action",url);
	$("#queryHolidayFormId", navTab.getCurrentPanel()).submit();
});

//通过单选按钮带出值显示
function queryClaimHoliday(obj){
	//获取行数据
	var holidayId = $(obj).parent().parent().find("td:eq(1) input").val();
	var holidayYear = $(obj).parent().parent().find("td:eq(2) input").val();
	var holidayName = $(obj).parent().parent().find("td:eq(3) input").val();
	var holidayStarTime = $(obj).parent().parent().find("td:eq(4) input").val();
	var holidayEndTime = $(obj).parent().parent().find("td:eq(5) input").val();
	//给已配置信息赋值
	$("#holidayId", navTab.getCurrentPanel()).val(holidayId);
	$("#holidayYearId",navTab.getCurrentPanel()).selectMyComBox(holidayYear);
	$("#holidayNameId",navTab.getCurrentPanel()).selectMyComBox(holidayName);
	$("#startDate",navTab.getCurrentPanel()).val(holidayStarTime);
	$("#endDate",navTab.getCurrentPanel()).val(holidayEndTime);
	$("#saveFlag",navTab.getCurrentPanel()).val(1);// 表示已配置信息是由单选按钮带值显示
	//单选按钮实现修改功能，年份和名称不可修改
	$("#holidayYearId", navTab.getCurrentPanel()).setMyComboxDisabled(true);
	$("#holidayNameId", navTab.getCurrentPanel()).setMyComboxDisabled(true);
}

//点击新增按钮清空已配置信息内容
$("#clearSource", navTab.getCurrentPanel()).click(function(){
	$("#holidayId",navTab.getCurrentPanel()).val("");
	$("#holidayYearId", navTab.getCurrentPanel()).selectMyComBox("");
	$("#holidayNameId", navTab.getCurrentPanel()).selectMyComBox("");
	$("#startDate",navTab.getCurrentPanel()).val("");
	$("#endDate",navTab.getCurrentPanel()).val("");
	$("#saveFlag",navTab.getCurrentPanel()).val("");
	$("#holidayYearId",navTab.getCurrentPanel()).setMyComboxDisabled(false);
	$("#holidayNameId",navTab.getCurrentPanel()).setMyComboxDisabled(false);
});

//点击保存按钮
$("#saveHoliday", navTab.getCurrentPanel()).click(function(){
	var holidayYearId=$("#holidayYearId", navTab.getCurrentPanel()).val();
	var holidayNameId=$("#holidayNameId", navTab.getCurrentPanel()).val();
	var startDate=$("#startDate", navTab.getCurrentPanel()).val();
	var endDate=$("#endDate", navTab.getCurrentPanel()).val();
	var saveFlag=$("#saveFlag", navTab.getCurrentPanel()).val();
	
	// 当saveFlag为1时，表示是通过单选按钮修改信息保存，其他是新增保存
	if(saveFlag == 1){
		//使用修改功能时只能修改节假日起止日期
		var startDate = $("#startDate", navTab.getCurrentPanel()).val().replace(/-/g, '/');
		var endDate = $("#endDate", navTab.getCurrentPanel()).val().replace(/-/g, '/');
		if(startDate =="" || endDate ==""){
			alertMsg.error("节假日起止日期必填！");
			if(startDate == ""){
				$("#startDate", navTab.getCurrentPanel()).focus();
			}else{
				$("#endDate", navTab.getCurrentPanel()).focus();
			}
			return;
		}
		if(startDate !="" && endDate !=""){
			if(startDate > endDate){
				alertMsg.error("节假日止期不能在节假日起期之后！");
				return;
			}
		}
		//保存修改已配置信息
		var url = "clm/parameter/updateSpeFestival_CLM_maintainSpecialFestivalAction.action";
		$("#editHolidayFormId", navTab.getCurrentPanel()).attr("action",url);
		$("#editHolidayFormId", navTab.getCurrentPanel()).submit();
	}else{
		if($.trim(holidayYearId)==""){
		  	alertMsg.error("节假日年份必填");
		  	$("#holidayYearId", navTab.getCurrentPanel()).focus();
		  	return;
		}
		if($.trim(holidayNameId)==""){
		  	alertMsg.error("节假日名称必填");
		  	$("#holidayNameId", navTab.getCurrentPanel()).focus();
		  	return;
		}
		var startDate = $("#startDate", navTab.getCurrentPanel()).val().replace(/-/g, '/');
		var endDate = $("#endDate", navTab.getCurrentPanel()).val().replace(/-/g, '/');
		if(startDate =="" || endDate ==""){
			alertMsg.error("节假日起止日期必填！");
			if(startDate == ""){
				$("#startDate", navTab.getCurrentPanel()).focus();
			}else{
				$("#endDate", navTab.getCurrentPanel()).focus();
			}
			return;
		}
		if(startDate !="" && endDate !=""){
			if(startDate > endDate){
				alertMsg.error("节假日止期不能在节假日起期之后！");
				return;
			}
		}
		//保存校验：年份+节假日不能存在重复
		var flag="";
		$.ajax({
			 'url':"clm/parameter/saveSpeFestivalCheck_CLM_maintainSpecialFestivalAction.action",
		     'type':'post',
		     'data':{"claimHolidayVO.holidayYearCode":holidayYearId,
		    	 		"claimHolidayVO.holidayNameCode":holidayNameId},
		     'datatype':'json',
		     'async':false,
		     'success':function(data){
		    	var data=eval("("+data+")");
		    	flag=data.repeatFlag;
		     }
		});
		if(flag=="1"){
			alertMsg.error("所要新增的年份节日已存在，若要改动，请用修改功能。");
			return;
		}
		//保存新增节假日信息
		var url = "clm/parameter/saveSpeFestival_CLM_maintainSpecialFestivalAction.action";
		$("#editHolidayFormId", navTab.getCurrentPanel()).attr("action",url);
		$("#editHolidayFormId", navTab.getCurrentPanel()).submit();
	}
});

//删除按钮
function delHoliday(holidayId){
	alertMsg.confirm("是否确定删除？",{
		okCall:function(){
			$.ajax({
				 'url':"clm/parameter/deleteSpeFestival_CLM_maintainSpecialFestivalAction.action?claimHolidayVO.holidayId="+holidayId,
			     'type':'post',
			     'datatype':'json',
			     'async':true,
			     'success':function(data){
			   	  var data=eval("("+data+")");
			   	  alertMsg.info("操作成功");
			   	  var url = "clm/parameter/findClaimHolidayByPages_CLM_maintainSpecialFestivalAction.action";
			   	  $("#queryHolidayFormId", navTab.getCurrentPanel()).attr("action",url);
			   	  $("#queryHolidayFormId", navTab.getCurrentPanel()).submit();
			    }
			    });
		}
	});
}

//点击退出按钮
/*function exit(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	   }
	 });
}*/