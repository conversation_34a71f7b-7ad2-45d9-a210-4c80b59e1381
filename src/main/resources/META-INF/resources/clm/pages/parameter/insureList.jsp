<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">
	
</script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript">
	//点击配置轨迹查询按钮
	function selectSurceyLog() {
		/* if ($("#startTime", navTab.getCurrentPanel()).val() == ""
				|| $("#startTime", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("带*信息不能为空");
			return;
		}
		if ($("#endTime", navTab.getCurrentPanel()).val() == ""
				|| $("#endTime", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("带*信息不能为空");
			return;
		} */
		$("#selectSurceyLog", navTab.getCurrentPanel()).submit();

	};

	//点击配置查询按钮
	function selectSurcey() {
		$("#selectSurcey", navTab.getCurrentPanel()).submit();
	};

	//新增按钮
	function insert() {
		$("#saveApplySurvey").show();
		$("#saveApplySurvey").find(".main_foldContent").slideDown();
		if ($("#main_3", navTab.getCurrentPanel()).find("#two",
				navTab.getCurrentPanel()).hasClass("main_plus")) {
			$("#main_3", navTab.getCurrentPanel()).find("h5",
					navTab.getCurrentPanel()).click();
		}
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled", false);
		cleanSave();
	};

	//保存按钮 
	function saveSet() {
		if ($("#intervalSymbol", navTab.getCurrentPanel()).val() == ""
				|| $("#intervalSymbol", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险时间距离生效时间计算符号不能为空");
			return;
		}
		if ($("#intervalNum", navTab.getCurrentPanel()).val() == ""
				|| $("#intervalNum", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险时间距离生效时间不能为空");
			return;
		}
		if ($("#intervalType", navTab.getCurrentPanel()).val() == ""
				|| $("#intervalType", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险时间距离生效时间类型不能为空");
			return;
		}
		if ($("#amountSymbol", navTab.getCurrentPanel()).val() == ""
				|| $("#amountSymbol", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("保额计算符号不能为空");
			return;
		}
		if ($("#amount", navTab.getCurrentPanel()).val() == ""
				|| $("#amount", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("保额不能为空");
			return;
		}
		if ($("#accReason", navTab.getCurrentPanel()).val() == ""
				|| $("#accReason", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("出险原因不能为空");
			return;
		}
		if ($("#claimType", navTab.getCurrentPanel()).val() == ""
				|| $("#claimType", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("理赔类型不能为空");
			return;
		}
		$("#saveSurvey", navTab.getCurrentPanel()).submit();
		$("#saveApplySurvey", navTab.getCurrentPanel()).hide();
	};

	//重置 
	function clearForm() {

		$("#intervalSymbol").selectMyComBox("");
		$("#intervalNum").selectMyComBox("");
		$("#intervalType").selectMyComBox("");
		$("#amountSymbol").selectMyComBox("");
		$("#amount").selectMyComBox("");
		$("#accReason").selectMyComBox("");
		$("#claimType").selectMyComBox("");

	}

	//删除操作  完成
	function deleteApplySurvey(value) {
		alertMsg
				.confirm(
						"请确认是否要删除",
						{
							okCall : function() {
								$
										.ajax({
											url : "clm/parameter/deleteCaimApplySurvey_CLM_claimIntelPortraitConfigAction.action",
											type : "POST",
											data : {
												'claimApplySurveyVO.listId' : value
											},
											cache : false,
											async : false,
											success : function(data) {
												var json = DWZ.jsonEval(data);
												if (json.statusCode == "200") {
													alertMsg.correct("删除成功！");
													$(
															"#legalPersonJspForm",
															navTab
																	.getCurrentPanel())
															.submit();
												} else {
													alertMsg
															.error(json.message);
												}
												;
											}
										});
							},
							cancelCall : function() {
								return;
							}
						});
	}

	//选择
	function choseSurveyApply(arg) {
		$("#saveApplySurvey", navTab.getCurrentPanel()).show();
		$(arg).parent().parent().find("td:eq(0)").find("input:radio").attr(
				"checked", true);
		var selectedValue = $(arg).val().split("|");
		$("#claimApplySurveylistId", navTab.getCurrentPanel()).val(
				selectedValue[0]);
		$("#intervalSymbol", navTab.getCurrentPanel()).attr("value",
				selectedValue[1]);
		$("#intervalSymbol", navTab.getCurrentPanel()).prev().empty();
		$("#intervalSymbol", navTab.getCurrentPanel()).prev().append(
				selectedValue[1]);
		$("#intervalSymbol", navTab.getCurrentPanel()).find("option").each(
				function() {
					if ($(this).val() == selectedValue[1]) {
						$(this).attr("selected", true);
					} else {
						$(this).attr("selected", false);
					}
				});

		$("#intervalNum", navTab.getCurrentPanel()).val("" + selectedValue[2]);
		$("#intervalType", navTab.getCurrentPanel()).selectMyComBox(
				"" + selectedValue[3]);
		claimFireEvent($("#intervalType", navTab.getCurrentPanel()));

		$("#amountSymbol", navTab.getCurrentPanel()).attr("value",
				selectedValue[4]);
		$("#amountSymbol", navTab.getCurrentPanel()).prev().empty();
		$("#amountSymbol", navTab.getCurrentPanel()).prev().append(
				selectedValue[4]);
		$("#amountSymbol", navTab.getCurrentPanel()).find("option").each(
				function() {
					if ($(this).val() == selectedValue[4]) {
						$(this).attr("selected", true);
					} else {
						$(this).attr("selected", false);
					}
				});

		$("#amount", navTab.getCurrentPanel()).val("" + selectedValue[5]);
		$("#accReason", navTab.getCurrentPanel()).selectMyComBox(
				"" + selectedValue[6]);
		claimFireEvent($("#accReason", navTab.getCurrentPanel()));
		$("#claimType", navTab.getCurrentPanel()).selectMyComBox(
				"" + selectedValue[7]);
		claimFireEvent($("#claimType", navTab.getCurrentPanel()));

		if ($("#main_3", navTab.getCurrentPanel()).find("#two",
				navTab.getCurrentPanel()).hasClass("main_plus")) {
			$("#main_3", navTab.getCurrentPanel()).find("h5",
					navTab.getCurrentPanel()).click();
		}
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled");
	}

	function checkNum(obj){
		var numValue = $(obj).val();
		if(numValue!=""){
			var reg= /^[0-9]*$/;
			if (reg.test(numValue) == false ){
				alertMsg.warn("请输入整数");
				return;
			}
	}
	}
	
</script>

<div layouth="36" id="saveSurveyId">
	<!-- 轨迹查询-子页面 -->
	<div id="reportCommPoolSelfId">
		<%@ include file="insureListImpl.jsp"%>
	</div>

	<!-- 查询访问路径 -->
	<form id="pagerFormSurcey" method="post"
		action="clm/parameter/selectSurcey_CLM_claimIntelPortraitConfigAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage2.pageNo} " />
		<input type="hidden" name="numPerPage"
			value="${currentPage2.pageSize}" />
	</form>
	<!-- 	<div layoutH="30" style="background: #fafafa">
 -->
	<div id="main_2" class="main_borderbg">
		<ul class="main_ul">
			<li class="clearfix">
				<h5 hasborder="true">
					<b id="two" class="main_minus"></b><span>投保必调配置</span>
				</h5>
				<div class="main_foldContent">
					<div class="main_bqtabdivbr">
						<div class="panelPageFormContent">
							<div class="pageFormInfoContent">
								<form id="selectSurcey"
									action="clm/parameter/selectSurcey_CLM_claimIntelPortraitConfigAction.action"
									onsubmit="return navTabSearch(this,'insureListSurcey')" rel="pagerFormSurcey"
									method="post" class="pagerForm required-validate">
									<dl style="width: 32%">
										<dt style="width: 32%">出险原因</dt>
										<dd style="width: 60%">
											<Field:codeTable cssClass="combox title"
												name="claimApplySurveyVO.accReason"
												value="${claimApplySurveyVO.accReason}" nullOption="true"
												tableName="APP___CLM__DBUSER.T_CLAIM_NATURE"
												whereClause="code in (1,2)" orderBy="code" />
										</dd>
									</dl>
									<dl style="width: 32%">
										<dt style="width: 32%">理赔类型</dt>
										<dd style="width: 60%">
											<Field:codeTable cssClass="combox title"
												name="claimApplySurveyVO.claimType"
												value="${claimApplySurveyVO.accReason}"
												tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"
												whereClause="code not in (11)" nullOption='true'
												orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)" />
										</dd>
									</dl>
								</form>
								<div class="pageFormdiv">
									<button type="button" class="but_blue"
										onclick="selectSurcey();">查询</button>
								</div>
								<div id="insureListSurcey">
									<form id="pagerForm" method="post"
										action="clm/parameter/selectSurcey_CLM_claimIntelPortraitConfigAction.action">
										<input type="hidden" name="pageNum"
											vaule="${currentPage2.pageNo} " /> <input type="hidden"
											name="numPerPage" value="${currentPage2.pageSize}" />
										<!-- 查询条件回调 -->
										<input type="hidden" name="claimApplySurveyVO.accReason"
											value="${claimApplySurveyVO.accReason}" /> <input
											type="hidden" name="claimApplySurveyVO.claimType"
											value="${claimApplySurveyVO.claimType}" />
									</form>
									<div>
										<table class="list" id="surveyApplyOperTable" width="100%">
											<thead>
												<tr>
													<th nowrap>选择</th>
													<th nowrap>出险时间距离生效时间计算符号</th>
													<th nowrap>出险时间距离生效时间</th>
													<th nowrap>出险时间距离生效时间类型</th>
													<th nowrap>保额计算符号</th>
													<th nowrap>保额（万元）</th>
													<th nowrap>出险原因</th>
													<th nowrap>理赔类型</th>
													<th nowrap>操作</th>
												</tr>
											</thead>
											<tbody>
												<s:if
													test="currentPage2.pageItems == null || currentPage2.pageItems.size()==0">
													<tr>
														<td colspan="100">
															<div class="noRueryResult">没有符合条件的查询结果！</div>
														</td>
													</tr>
												</s:if>
												<s:iterator value="currentPage2.pageItems" status="st">
													<tr align="center" target="listIdInfo">
														<td><input type="radio" class="radioIndex" name="r1"
															onclick='choseSurveyApply(this);'
															value="${listId}|${intervalSymbol}|${intervalNum}|${intervalType}|${amountSymbol}|${amount}|${accReason}|${claimType}" /></td>
														<td align="center" style="word-break: break-all;">${intervalSymbol}</td>
														<td align="center" style="word-break: break-all;">${intervalNum}</td>
														<td align="center" style="word-break: break-all;">${intervalType}</td>
														<td align="center" style="word-break: break-all;">${amountSymbol}</td>
														<td align="center" style="word-break: break-all;">${amount}</td>
														<td align="center" style="word-break: break-all;">${accReasonStr}</td>
														<td align="center" style="word-break: break-all;">${claimTypeStr}</td>
														<td><a title='删除' class="btnDel" id='delButton'
															href='javascript:void(0);'
															onclick='deleteApplySurvey("${listId}");'>删除</a></td>
													</tr>
												</s:iterator>
											</tbody>
										</table>
										<div class="panelBar">
											<div class="pages">
												<span>显示</span>
												<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
													name="select"
													onchange="navTabPageBreak({numPerPage:this.value},'insureListSurcey')"
													value="currentPage2.pageSize">
												</s:select>
												<span>条，共${currentPage2.total}条</span>
											</div>
											<div class="pagination" targetType="navTab"
												totalCount="${currentPage2.total}"
												numPerPage="${currentPage2.pageSize}" pageNumShown="20"
												currentPage="${currentPage2.pageNo}"
												rel="insureListSurcey"></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</li>
		</ul>
	</div>
	<div id="saveApplySurvey" style="display: none;">
		<div id="main_3">
			<ul class="main_ul">
				<li class="clearfix fold">
					<h5 hasborder="true">
						<b id="two" class="main_plus"></b><span>投保必备配置录入</span>
					</h5>
					<div class="main_foldContent" style="display: none;">
						<div id="medicalSurveyDiv" class="panelPageFormContent">
							<div class="main_bqtabdivbr">
								<form id="saveSurvey"
									action="clm/parameter/saveApplyMbSur_CLM_claimIntelPortraitConfigAction.action"
									onsubmit="return validateCallback(this)" rel="pagerFormSurcey"
									method="post">
									<input name="claimApplySurveyVO.listId" type="hidden"
										id="claimApplySurveylistId" />
									<dl>
										<dt>出险时间距离生效时间</dt>
										<dd>
											<select class="combox"
												name="claimApplySurveyVO.intervalSymbol" id="intervalSymbol">
												<option value="">请选择</option>
												<option
													value="<"
																	<s:if test="claimApplySurveyVO.intervalSymbol != null && claimApplySurveyVO.intervalSymbol == '<'">selected="selected"</s:if>>
													<</option>
												<option value="<="
													<s:if test="claimApplySurveyVO.intervalSymbol != null && claimApplySurveyVO.intervalSymbol == '<='">selected="selected"</s:if>><=</option>
												<option value="="
													<s:if test="claimApplySurveyVO.intervalSymbol != null && claimApplySurveyVO.intervalSymbol == '='">selected="selected"</s:if>>=</option>
												<option value=">"
													<s:if test="claimApplySurveyVO.intervalSymbol != null && claimApplySurveyVO.intervalSymbol == '>'">selected="selected"</s:if>>></option>
												<option value=">="
													<s:if test="claimApplySurveyVO.intervalSymbol != null && claimApplySurveyVO.intervalSymbol == '>='">selected="selected"</s:if>>>=</option>

											</select>
										</dd>
										<dd>
											<input name="claimApplySurveyVO.intervalNum" type="text"
												id="intervalNum" onblur="checkNum(this)"/>
										</dd>
										<dd>
											<select class="combox" name="claimApplySurveyVO.intervalType"
												id="intervalType">
												<option value="">请选择</option>
												<option value="天"
													<s:if test="claimApplySurveyVO.intervalType != null && claimApplySurveyVO.intervalType == '天'">selected="selected"</s:if>>天</option>
												<option value="月"
													<s:if test="claimApplySurveyVO.intervalType != null && claimApplySurveyVO.intervalType == '月'">selected="selected"</s:if>>月</option>
												<option value="年"
													<s:if test="claimApplySurveyVO.intervalType != null && claimApplySurveyVO.intervalType == '年'">selected="selected"</s:if>>年</option>
											</select>
										</dd>
									</dl>
									<div class="main_detail">
										<dl>
											<dt>保额</dt>
											<dd>
												<select class="combox"
													name="claimApplySurveyVO.amountSymbol" id="amountSymbol">
													<option value="">请选择</option>
													<option
														value="<"
														<s:if test="claimApplySurveyVO.amountSymbol != null && claimApplySurveyVO.amountSymbol == '<'">selected="selected"</s:if>><
													</option>
													<option value="<="
														<s:if test="claimApplySurveyVO.amountSymbol != null && claimApplySurveyVO.amountSymbol == '<='">selected="selected"</s:if>><=</option>
													<option value="="
														<s:if test="claimApplySurveyVO.amountSymbol != null && claimApplySurveyVO.amountSymbol == '='">selected="selected"</s:if>>=</option>
													<option value=">"
														<s:if test="claimApplySurveyVO.amountSymbol != null && claimApplySurveyVO.amountSymbol == '>'">selected="selected"</s:if>>></option>
													<option value=">="
														<s:if test="claimApplySurveyVO.amountSymbol != null && claimApplySurveyVO.amountSymbol == '>='">selected="selected"</s:if>>>=</option>
												</select>
											</dd>
											<dd>
												<input name="claimApplySurveyVO.amount" type="text"
													id="amount" onblur="checkNum(this)"/>
											</dd>
											<span>万元 </span>
										</dl>
										<div class="main_detail">
											<dl>
												<dt>出险原因</dt>
												<dd>
													<Field:codeTable cssClass="combox title" id="accReason"
														name="claimApplySurveyVO.accReason"
														value="${claimApplySurveyVO.accReason}"
														tableName="APP___CLM__DBUSER.T_CLAIM_NATURE"
														whereClause="code in (1,2)" orderBy="code" />
												</dd>
											</dl>
											<div class="main_detail">
												<dl>
													<dt>理赔类型</dt>
													<dd>
														<Field:codeTable cssClass="combox title" id="claimType"
															name="claimApplySurveyVO.claimType"
															value="${claimApplySurveyVO.claimTypeNum}"
															tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"
															whereClause="code not in (11)" nullOption='true'
															orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)" />
													</dd>
												</dl>
												<div class="main_detail">
													<dl>
														<dt>提交人</dt>
														<dd>
															<span id="submitor">${currentUser.userName }</span>
														</dd>
													</dl>
													<dl>
														<dt>提交日期</dt>
														<dd>
															<span id="submitTime"></span>
														</dd>
													</dl>
												</div>
											</div>
										</div>
									</div>
								</form>
							</div>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button class="but_blue" type="button" onclick="insert()">新增</button>
			</li>
			<li>
				<button class="but_blue" type="button" onclick="saveSet()"
					id="saveRule" disabled="disabled">保存</button>
			</li>
			<li><button class="but_blue" type="button" id="clearBtn"
					onclick="clearForm()">重置</button></li>
			<li>
				<button class="but_gray" type="button" id="exitbtn" onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
</div>




