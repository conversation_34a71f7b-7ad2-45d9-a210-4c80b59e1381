<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<form id="pagerForm" method="post" 
	action="clm/parameter/queryApplyMbSurTrace_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${currentPage1.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage1.pageSize}" />
	<!-- 查询条件回调 -->
	<input type="hidden" name="claimApplySurveyLogVO.accReason" value="${claimApplySurveyLogVO.accReason}" /> 
	<input type="hidden" name="claimApplySurveyLogVO.claimType" value="${claimApplySurveyLogVO.claimType}" />
	<input type="hidden" name="claimApplySurveyLogVO.startTime" value="<s:date name='claimApplySurveyLogVO.startTime' format='yyyy-MM-dd'/>" />
	<input type="hidden" name="claimApplySurveyLogVO.endTime" value="<s:date name='claimApplySurveyLogVO.endTime' format='yyyy-MM-dd'/>" />
</form>
<div class="main_bqtabdivbr">
	<table class="list" id="surveyApplyOperTable" width="100%">
		<thead>
			<tr>
				<th nowrap>出险时间距离生效时间计算符号</th>
				<th nowrap>出险时间距离生效时间</th>
				<th nowrap>出险时间距离生效时间类型</th>
				<th nowrap>保额计算符号</th>
				<th nowrap>保额（万元）</th>
				<th nowrap>出险原因</th>
				<th nowrap>理赔类型</th>
				<th nowrap>操作</th>
				<th nowrap>提交人</th>
				<th nowrap>提交日期</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag != null">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">请选择条件查询数据！</div>
					</td>
				</tr>
			</s:if>
			<s:elseif
				test="currentPage1.PageItems == null || currentPage1.PageItems.size()==0">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:elseif>
			<s:iterator value="currentPage1.pageItems" status="st">
				<tr align="center" target="listIdInfo">
					<td align="center" style="word-break: break-all;">${intervalSymbol}</td>
					<td align="center" style="word-break: break-all;">${intervalNum}</td>
					<td align="center" style="word-break: break-all;">${intervalType}</td>
					<td align="center" style="word-break: break-all;">${amountSymbol}</td>
					<td align="center" style="word-break: break-all;">${amount}</td>
					<td align="center" style="word-break: break-all;">${accReasonStr}</td>
					<td align="center" style="word-break: break-all;">${claimTypeStr}</td>
					<td align="center" style="word-break: break-all;">${operationTypeStr}</td>
					<td align="center" style="word-break: break-all;">${submitUserStr}</td>
					<td align="center" style="word-break: break-all;"><s:date
							name='submitDate' format='yyyy-MM-dd' /></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}" name="select"
				onchange="navTabPageBreak({numPerPage:this.value})"
				value="currentPage1.pageSize">
			</s:select>
			<span>条，共${currentPage1.total}条</span>
		</div>
		<div class="pagination" targetType="navTab" rel="insureListSurceyLog"
			totalCount="${currentPage1.total}"
			numPerPage="${currentPage1.pageSize}"
			pageNumShown="20"
			currentPage="${currentPage1.pageNo}"></div>
	</div>
</div>

