//理赔黑名单查询
function queryClaimBlackNameQuery(){
	$("#saveAndUpdateFlag", navTab.getCurrentPanel()).val("");
	$("#claimBlackNameFormId", navTab.getCurrentPanel()).submit();
}
//删除某条数据
function deleteCaimBlackName(blackId,obj){
	$.ajax({
		'url':'clm/parameter/deletedClaimBlackNameInfo_CLM_claimBlackNameAction.action?claimBlackNameVO.blackId='+blackId,
		'type':'post',
		'async':false,
		'success':function(){
			 $(obj).parent().parent().remove();
			 //清空
			 $("#claimBlackNameInfoId", navTab.getCurrentPanel()).find("textarea").val(null);
				$("#customerIdBlackId", navTab.getCurrentPanel()).val(null); //客户号
				$("#blackNameBlackId", navTab.getCurrentPanel()).val(null); //姓名
				$("#blackBirthdayBlackId", navTab.getCurrentPanel()).val(null); //出生日期
				$("#blackCertiCodeId", navTab.getCurrentPanel()).val(null); //证件号码
				$("#agentCodeBlackId", navTab.getCurrentPanel()).val(null); //证件号码
				$("#blackGenderId", navTab.getCurrentPanel()).selectMyComBox(""); //性别
				$("#blackCertiTypeId", navTab.getCurrentPanel()).selectMyComBox(""); //证件类型
				$("#blackNameTypeId", navTab.getCurrentPanel()).selectMyComBox(""); //黑名单类型
				$("#blackNameStatusId", navTab.getCurrentPanel()).selectMyComBox(""); //黑名单状态
				frushImportResult();
			 alertMsg.info("删除成功");			 
		 },
		 'error':function(){
			 alertMsg.info("删除失败,请联系管理员");
			 
		 }
	});
}
//添加理赔黑名单信息
function addClaimBlackNameInfo(){
	//输入框和下拉框置空
	$("#claimBlackNameInfoId", navTab.getCurrentPanel()).find("textarea").val(null);
	$("#customerIdBlackId", navTab.getCurrentPanel()).val(null); //客户号
	$("#blackNameBlackId", navTab.getCurrentPanel()).val(null); //姓名
	$("#blackBirthdayBlackId", navTab.getCurrentPanel()).val(null); //出生日期
	$("#blackCertiCodeId", navTab.getCurrentPanel()).val(null); //证件号码
	$("#agentCodeBlackId", navTab.getCurrentPanel()).val(null); //证件号码
	$("#blackGenderId", navTab.getCurrentPanel()).selectMyComBox(""); //性别
	$("#blackCertiTypeId", navTab.getCurrentPanel()).selectMyComBox(""); //证件类型
	$("#blackNameTypeId", navTab.getCurrentPanel()).selectMyComBox(""); //黑名单类型
	$("#blackNameStatusId", navTab.getCurrentPanel()).selectMyComBox(""); //黑名单状态
	$("#saveAndUpdateFlag", navTab.getCurrentPanel()).val("");
	//点击添加时去掉radio按钮选中
//	$("#claimBlackNameTbodyId", navTab.getCurrentPanel()).find("tr")
}
//保存黑名单信息或者修改黑名单信息
function saveClaimBlackNameInfo(){
	//必填项校验
	var blackNameBlackId = $("#blackNameBlackId", navTab.getCurrentPanel()).val(); //姓名
	var blackCertiTypeId = $("#blackCertiTypeId", navTab.getCurrentPanel()).val(); //证件类型
	var blackCertiCodeId = $("#blackCertiCodeId", navTab.getCurrentPanel()).val(); //证件号码
	var blackNameTypeId = $("#blackNameTypeId", navTab.getCurrentPanel()).val(); //黑名单类型
	var blackNameStatusId = $("#blackNameStatusId", navTab.getCurrentPanel()).val(); //黑名单状态
	var customerIdBlackId = $("#customerIdBlackId", navTab.getCurrentPanel()).val(); //客户号
	var blackGenderId = $("#blackGenderId", navTab.getCurrentPanel()).val(); //性别
	var blackBirthdayBlackId = $("#blackBirthdayBlackId", navTab.getCurrentPanel()).val(); //出生日期
	var agentCodeBlackId = $("#agentCodeBlackId", navTab.getCurrentPanel()).val(); //业务员编号
	var blackNameReasonId = $("#blackNameReasonId", navTab.getCurrentPanel()).val(); //黑名单事由
	var establishPersonId = $("#establishPersonId", navTab.getCurrentPanel()).val(); //创建人
//	var establishTimeId = $("#establishTimeId", navTab.getCurrentPanel()).val(); //创建日期
//	var finalUpdatePersonId = $("#finalUpdatePersonId", navTab.getCurrentPanel()).val(); //最后一次创建人
//	var finalUpdateTimeId = $("#finalUpdateTimeId", navTab.getCurrentPanel()).val(); //最后一次修改日期
	if(blackNameBlackId.trim() == ""){
		alertMsg.error("姓名必填!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#blackNameBlackId", navTab.getCurrentPanel()).focus();
		});
		return ;
	}
	if(blackCertiTypeId.trim() == ""){
		alertMsg.error("证件类型必填!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#blackCertiTypeId", navTab.getCurrentPanel()).focus();
		});
		return ;
	}
	if(blackCertiCodeId.trim() == ""){
		alertMsg.error("证件号码必填!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#blackCertiCodeId", navTab.getCurrentPanel()).focus();
		});
		return ;
	}
	if(blackNameTypeId.trim() == ""){
		alertMsg.error("黑名单类型必填!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#blackNameTypeId", navTab.getCurrentPanel()).focus();
		});
		return ;
	}
	if(blackNameStatusId.trim() == ""){
		alertMsg.error("黑名单状态标识必填!");
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#blackNameStatusId", navTab.getCurrentPanel()).focus();
		});
		return ;
	}
	if(!checkName($("#blackNameBlackId", navTab.getCurrentPanel()))){
		return;
	}
	if(!checkCertiNo(blackCertiTypeId,blackCertiCodeId)){
		return;
	}
	
	//用来判断是保存还是修改的标示
	var saveAndUpdateFlag = $("#saveAndUpdateFlag", navTab.getCurrentPanel()).val();
	if(saveAndUpdateFlag == ""){
		$.ajax({
			'url':'clm/parameter/saveClaimBlackNameInfo_CLM_claimBlackNameAction.action',
			'type':'post',
			'async':false,
			'data':{'claimBlackNameVO.blackName':blackNameBlackId ,'claimBlackNameVO.blackCertiType':blackCertiTypeId,'claimBlackNameVO.blackCertiCode':blackCertiCodeId,'claimBlackNameVO.blackNameType':blackNameTypeId,'claimBlackNameVO.blackNameStatus':blackNameStatusId,'claimBlackNameVO.customerId':customerIdBlackId,'claimBlackNameVO.blackGender':blackGenderId,'claimBlackNameVO.blackBirthday':blackBirthdayBlackId,'claimBlackNameVO.agentCode':agentCodeBlackId,'claimBlackNameVO.blackNameReason':blackNameReasonId,'claimBlackNameVO.establishPerson':establishPersonId},
			'datatype':'json',
			'success':function(data){
				queryClaimBlackNameQuery();
					alertMsg.info("保存理赔黑名单信息成功");
			 },
			 'error':function(){
				 alertMsg.info("保存失败,请联系管理员");
				 
			 }
		});
	} else {
		$.ajax({
			'url':'clm/parameter/saveClaimBlackNameInfo_CLM_claimBlackNameAction.action',
			'type':'post',
			'async':false,
			'data':{'claimBlackNameVO.blackId':saveAndUpdateFlag,'claimBlackNameVO.blackName':blackNameBlackId,'claimBlackNameVO.blackCertiType':blackCertiTypeId,'claimBlackNameVO.blackCertiCode':blackCertiCodeId,'claimBlackNameVO.blackNameType':blackNameTypeId,'claimBlackNameVO.blackNameStatus':blackNameStatusId,'claimBlackNameVO.customerId':customerIdBlackId,'claimBlackNameVO.blackGender':blackGenderId,'claimBlackNameVO.blackBirthday':blackBirthdayBlackId,'claimBlackNameVO.agentCode':agentCodeBlackId,'claimBlackNameVO.blackNameReason':blackNameReasonId,'claimBlackNameVO.establishPerson':establishPersonId},
			'datatype':'json',
			'success':function(data){
				queryClaimBlackNameQuery();				
				 	//清空
//				 	$("#claimBlackNameInfoId", navTab.getCurrentPanel()).find("textarea").val(null);
//					$("#customerIdBlackId", navTab.getCurrentPanel()).val(null); //客户号
//					$("#blackNameBlackId", navTab.getCurrentPanel()).val(null); //姓名
//					$("#blackBirthdayBlackId", navTab.getCurrentPanel()).val(null); //出生日期
//					$("#blackCertiCodeId", navTab.getCurrentPanel()).val(null); //证件号码
//					$("#agentCodeBlackId", navTab.getCurrentPanel()).val(null); //业务员编码
//					$("#blackGenderId", navTab.getCurrentPanel()).selectMyComBox(""); //性别
//					$("#blackCertiTypeId", navTab.getCurrentPanel()).selectMyComBox(""); //证件类型
//					$("#blackNameTypeId", navTab.getCurrentPanel()).selectMyComBox(""); //黑名单类型
//					$("#blackNameStatusId", navTab.getCurrentPanel()).selectMyComBox(""); //黑名单状态
					alertMsg.info("保存理赔黑名单信息成功");
			 },
			 'error':function(){
				 alertMsg.info("保存失败,请联系管理员");
				 
			 }
		});
	}
	frushImportResult();
}
//列表信息按钮赋值
function queryClaimBlackNameInfoSingle(obj){
	var blackId = $(obj).next().val();
	//用来判断是保存还是修改的标示
	$("#saveAndUpdateFlag", navTab.getCurrentPanel()).val(blackId);
	
	$("#blackNameBlackId", navTab.getCurrentPanel()).val($(obj).parent().next().next().next().text()); //姓名
	$("#blackCertiTypeId", navTab.getCurrentPanel()).selectMyComBox($(obj).parent().next().find("input:eq(8)").val());//证件类型
	$("#blackCertiCodeId", navTab.getCurrentPanel()).val($(obj).parent().next().next().next().next().next().next().next().text()); //证件号码
	$("#blackNameTypeId", navTab.getCurrentPanel()).selectMyComBox($(obj).parent().next().find("input").val()); //黑名单类型
	$("#blackNameStatusId", navTab.getCurrentPanel()).selectMyComBox($(obj).parent().next().find("input:eq(1)").val()); //黑名单状态
//	$("#customerIdBlackId", navTab.getCurrentPanel()).val($(obj).parent().next().next().text()); //客户号
	$("#blackGenderId", navTab.getCurrentPanel()).selectMyComBox($(obj).parent().next().find("input:eq(7)").val()); //性别
	$("#blackBirthdayBlackId", navTab.getCurrentPanel()).val($(obj).parent().next().next().next().next().next().text()); //出生日期
	$("#agentCodeBlackId", navTab.getCurrentPanel()).val($(obj).parent().next().next().text()); //业务员编号
	$("#blackNameReasonId", navTab.getCurrentPanel()).val($(obj).parent().next().find("input:eq(6)").val()); //黑名单事由
	$("#establishPersonIdCopy", navTab.getCurrentPanel()).val($(obj).parent().next().find("input:eq(2)").val()); //创建人
	$("#establishPersonId", navTab.getCurrentPanel()).val($(obj).parent().next().find("input:eq(9)").val()); //创建人
	$("#establishTimeIdCopy", navTab.getCurrentPanel()).val($(obj).parent().next().find("input:eq(3)").val()); //创建日期
	$("#finalUpdatePersonIdCopy", navTab.getCurrentPanel()).val($(obj).parent().next().find("input:eq(5)").val()); //最后一次创建人
	$("#finalUpdateTimeIdCopy", navTab.getCurrentPanel()).val($(obj).parent().next().find("input:eq(4)").val()); //最后一次修改日期
}
//选择某行tr的时候让radio按钮选中
function claimBlackNameCheck(obj){
	$(obj).find("td:eq(0)").find("input").attr("checked","checked");
}
//下载模板
function downLoadExcel(pageForm,pageAction){
	$("#"+pageForm, navTab.getCurrentPanel()).attr("onsubmit",""); 
	$("#"+pageForm, navTab.getCurrentPanel()).submit();
}
//导入
function sumit(){
	var fileName = $("#fileName", navTab.getCurrentPanel()).val().trim();	
	if($.trim(fileName)==""){
	  	alertMsg.info("请选择要导入的文件！");
	  	return;
	}
	if(fileName.indexOf('.xls')==-1){
		alertMsg.info("文件格式不正确，请先下载模板");
	  	return;
	}
    $("#buttonFormSubmit", navTab.getCurrentPanel()).submit(); 
    
}
function myAjaxDone(json){
	if (json.statusCode == DWZ.statusCode.ok) {
		alertMsg.correct("导入完成");	
		frushImportResult();
	} else {
		alertMsg.warn("导入失败");
	}
	
}
//刷新导入结果
function  frushImportResult(){
	$("#frushImportResult", navTab.getCurrentPanel()).submit();
}
//退出
/*function exitClaimBlackName(){
	alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
}*/