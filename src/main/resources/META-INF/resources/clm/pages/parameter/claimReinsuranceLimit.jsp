<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript" src="clm/pages/parameter/claimreinsuranceLimit.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>



<script type="text/javascript">
//新增理赔再保限额
function addClaimReinsuranceLimitForma(){
	//获取到当前列表的值
	var trLength = $("#claimReinsuranceLimitTbodyId" ,navTab.getCurrentPanel()).find("tr").length;
	var  html = "<tr>" +
							"<td>" +(trLength+1)+"<input type='hidden' name='claimReinsuranceLimitVOList["+trLength+"].reinsuranceLimitId'/>" +
							"</td>"+
							"<td>" +
								'<Field:codeTable  cssClass="combox"   tableName="APP___CLM__DBUSER.T_REINSURANCE_CLAIM_TYPE"  name="claimReinsuranceLimitVOList['+trLength+'].claimType" />'+
							"</td>"+
							"<td>"+
								'<input  name="claimReinsuranceLimitVOList['+trLength+'].reinsuranceLimit"  type="text"  class="number" onkeyup="claimReinsuranceLimitChange(this)"/>' +
							"</td>"+
								"<td><a class='btnDel' href='javascript:;' onclick='deleteClaimReinsuranceLimitForm(this)'  ref='current' >删除</a>" +
							"</td>" +
						"</tr>";
	
	$("#claimReinsuranceLimitTbodyId" ,navTab.getCurrentPanel()).append(html);
}
</script>

<div layoutH="36">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
	</div>
	<form id="pagerForm" method="post" action="clm/parameter/queryClaimReinsuranceLimit_CLM_claimReinsuranceLimitAction.action">
				<input type="hidden" name="pageNum"  />
				<input type="hidden" name="numPerPage"/>
	</form>
	<form id="claimReinsuranceLimitFormId" action="clm/parameter/queryClaimReinsuranceLimit_CLM_claimReinsuranceLimitAction.action" 
			class="pagerForm required-validate" onsubmit="return navTabSearch(this);" rel="pagerForm">
		<div class="pageFormInfoContent">
			<dl>
				<dt>理赔类型</dt>
				<dd>
					<Field:codeTable cssClass="combox" id='claimTypeId' allOption="true" name="claimTypeCode" tableName="APP___CLM__DBUSER.T_REINSURANCE_CLAIM_TYPE" value="${claimReinsuranceLimitVO.claimType}"/>
					<input type="hidden" name="claimReinsuranceLimitVO.claimType" id="claimTypeHiddenId"/>
				</dd>
			</dl>
			<div class="pageFormdiv"><button class="but_blue" type="button" onclick="queryClaimReinsuranceLimit()">查询</button></div>
		</div>	
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">再保理赔限额表
		</h1>
	</div>
	<div class="tabdivclassbr main_tabdiv">
			<table class="list sortable main_dbottom" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>理赔类型<font>*</font></th>
						<th nowrap>额度（万）<font>*</font></th>
						<th nowrap>操作</th>
					</tr>
					</thead>
						<tbody id="claimReinsuranceLimitTbodyId">
							<s:iterator value="claimReinsuranceLimitVOList" status="st">
								<tr>
									<td>${st.index+1}<input type="hidden" name="claimReinsuranceLimitVOList[${st.index}].reinsuranceLimitId" value="${reinsuranceLimitId}"/></td>
									<td>
										<Field:codeValue value="${claimType}" tableName="APP___CLM__DBUSER.T_REINSURANCE_CLAIM_TYPE"/>
										<input value="${claimType}" type="hidden" name="claimReinsuranceLimitVOList[${st.index}].claimType"/>
									</td>
									<td>
										<input name="claimReinsuranceLimitVOList[${st.index}].reinsuranceLimit" type="text" class="number" value="${reinsuranceLimit}" onkeyup="value=value.replace(/[^\d\.]/g,'')"/>
									</td>
									<td><a class="btnDel" href="javascript:;" onclick="deleteClaimReinsuranceLimitForm(this)" ref="current" >删除</a></td>
								</tr>
							</s:iterator>
					</tbody>
			  </table>
<!-- 		  </form> -->
	</div>
	</form>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button type="button" class="but_blue" onclick="addClaimReinsuranceLimitForma()">新增</button>
			</li>
			<li>
				<button type="button" class="but_blue" onclick="saveClaimReinsuranceLimitForm()">保存</button>
			</li>
			<li>
				<button type="button" class="but_gray" id="exitbtn" onclick="exit()">退出</button>
			</li>
		</ul>
	</div>
</div>