<%@ page language="java" contentType="text/html; charset=utf-8"
         pageEncoding="UTF-8" import="java.util.*" %>
<%@include file="/udmpCommon.jsp" %>
<%@taglib uri="/struts-tags" prefix="s" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- 直赔快赔险种管理轨迹 -->
<form id="pagerForm" method="post"
      action="clm/parameter/findDirectBusiLog_CLM_claimDirectBusiAction.action">
    <input type="hidden" name="pageNum" vaule="${currentPage.pageNo} "/>
    <input type="hidden" name="numPerPage" value="${currentPage.pageSize}"/>
</form>

<div class="divfclass">
    <div class="tabdivclassbr">
        <table class="list sortable main_dbottom"
               id="importantServiceConfigOperTable" width="100%">
            <thead>
            <tr align="center">
                <th nowrap>序号</th>
                <th nowrap>操作类型</th>
                <th nowrap>修改项</th>
                <th nowrap>操作人</th>
                <th nowrap>操作时间</th>
            </tr>
            </thead>
            <tbody>
            <s:if test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
                <tr>
                    <td colspan="13">
                        <div class="noRueryResult">没有符合条件的查询结果！</div>
                    </td>
                </tr>
            </s:if>
            <s:iterator value="currentPage.PageItems" var="status"
                        status="st">
                <tr align="center">
                    <td>${st.index+1 }</td>
                    <td>${operateType }</td>
                    <td>${busiProdName }</td>
                    <td>${operateName }</td>
                    <th><s:date name='operateDate' format='yyyy-MM-dd HH:mm:ss'/></th>
                </tr>
            </s:iterator>
            </tbody>
        </table>
        <!-- 分页查询区域 -->
        <div class="panelBar">
            <div class="pages">
                <span>显示</span>
                <s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
                          value="currentPage.pageSize" name="select"
                          onchange="navTabPageBreak({numPerPage:this.value},'importantServiceConfig')">
                </s:select>
                <span>条，共${currentPage.total}条</span>
            </div>
            <div class="pagination" targetType="navTab" rel="importantServiceConfig"
                 totalCount="${currentPage.total}"
                 numPerPage="${currentPage.pageSize}" pageNumShown="10"
                 currentPage="${currentPage.pageNo}"></div>
        </div>
    </div>

</div>