<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@taglib uri="/struts-tags" prefix="s"%>

<!DOCTYPE html PUBLIC  "_//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%@ page import="java.util.*"%>
<script type="text/javascript"
	src="clm/pages/report/policyRelieveLock.js">
</script>
<div  layoutH="36">
	<form id="queryForm"
		action="clm/parameter/findHangUpPolicy_CLM_policyRelieveLockAction.action"
		class="pageForm required-validate"
		onsubmit="return navTabSearch(this)"
		method="post">
				<div class="divfclass">
					<h1><img src="clm/images/tubiao.png">查询条件</h1>
		       </div>
				<div class="pageFormInfoContent">
					<dl>
							<dt>
								<font>* </font>赔案号
							</dt>
							<dd>
								<input type="text" id="caseNo" name="claimCaseVO.caseNo" value="${claimCaseVO.caseNo }" onkeyup="this.value=this.value.replace(/\D/g,'')"/>
							</dd>
					</dl>
				<div class="pageFormdiv">
					<button type="button" class="but_blue" id="queryCondition" onclick="query()">查询</button>
				</div>
				</div>
	</form>
	<form id="reportFormRelieveLock"
		action="clm/parameter/save_CLM_policyRelieveLockAction.action"
		class="pageForm required-validate" 
		onsubmit="return validateCallback(this, myCallBack);"
		method="post">
		<div >
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询结果</h1>
		       </div>
			<div class="tabdivclassbr">
				<table class="list" width="100%">
					<thead>
						<tr align="center">
							<th nowrap>选择</th>
							<th nowrap>序号</th>
							<th nowrap>保单号码</th>
							<th nowrap>保单状态</th>
							<th nowrap>被保人姓名</th>
							<th nowrap>保全挂起</th>
							<th nowrap>续期挂起</th>
						</tr>
					</thead>
					<tbody id="CompVOListTbody">
					<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="insuredHolderCompVOList == null || insuredHolderCompVOList.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
						<s:iterator value="insuredHolderCompVOList" status="st">
							<tr align="center">
								<td>
									<input name="policyCode" value="${policyCode}" type="checkbox" onclick="remove(this)" />
								</td>
								<td>${st.index+1}</td>
								<td>${policyCode}</td>
								<td><Field:codeValue value="${liabilityState}"
										tableName="APP___CLM__DBUSER.T_LIABILITY_STATUS" /></td>
								<td>${insuredName}</td>
								<td>
									<input type="checkbox" value="1" class="itemFlag" disabled="true"
									<s:if test="csFlag == 1">checked="checked"</s:if> 
									<s:if test="csFlag == -1"> readonly="readonly" disabled="disabled" </s:if> />挂起 
									<s:if test="csFlag == 1">
										<input type="hidden" name="insuredHolderCompVOList[${st.index}].csFlag1" value="1" />
									</s:if> 
									<s:else>
										<input type="hidden" name="insuredHolderCompVOList[${st.index}].csFlag1" value="0" />
									</s:else>
								</td>
								<td>
									<input type="checkbox" value="" class="itemFlag" disabled
									<s:if test="xqFlag == 1 ">checked="checked"</s:if> 
									<s:if test="xqFlag == -1"> readonly="readonly" disabled="disabled" </s:if>/>挂起 
									<s:if test="xqFlag == 1">
										<input type="hidden" name="insuredHolderCompVOList[${st.index}].xqFlag1" value="1" />
									</s:if> 
									<s:else>
										<input type="hidden" name="insuredHolderCompVOList[${st.index}].xqFlag1" value="0" />
									</s:else>
								</td>
								<input name="hiddenXqFlag" type="hidden" id=hiddenXqFlag value="${xqFlag}"/>
								<input name="hiddenCsFlag" type="hidden" id=hiddenCsFlag value="${csFlag}"/>
								<input type="hidden" name="insuredHolderCompVOList[${st.index}].policyCode" value="${policyCode}" />
								<input type="hidden" name="insuredHolderCompVOList[${st.index}].policyId" value="${policyId}" />
								<input type="hidden" name="insuredHolderCompVOList[${st.index}].caseNo" value="${caseNo}" />
								<input type="hidden" id="hiddenUpdateFlag" name="insuredHolderCompVOList[${st.index}].updateFlag" value="0" />
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div>
		</div>
		
		
		<div class="formBarButton">
			<ul>
				<li><a class="but_blue main_buta" id="reportSave" onclick="saveRelieveLock()">保存</a></li>
				<li><button class="but_gray" type="button" onclick="exit()">退出</button></li>
			</ul>
		</div>
		
		
	</form>
</div>