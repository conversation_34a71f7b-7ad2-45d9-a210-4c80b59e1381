<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- 出险结果必调配置轨迹 -->
<form id="pagerForm" method="post"
	action="clm/parameter/queryDangerResultMbSurLog_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${claimAccResultSurveyLogVOPage.pageNo} " />
	<input type="hidden" name="numPerPage" value="${claimAccResultSurveyLogVOPage.pageSize}" />
	<input type="hidden" name="claimAccResultSurveyLogVO.accReason" value="${claimAccResultSurveyLogVO.accReason}" /> 
	<input type="hidden" name="claimAccResultSurveyLogVO.accident2Code" value="${claimAccResultSurveyLogVO.accident2Code }" />
	<input type="hidden" name="claimAccResultSurveyLogVO.startDate" value="<s:date name='claimAccResultSurveyLogVO.startDate' format='yyyy-MM-dd'/>" />
	<input type="hidden" name="claimAccResultSurveyLogVO.endDate" value="<s:date name='claimAccResultSurveyLogVO.endDate' format='yyyy-MM-dd'/>" />
</form>
<div>
	<table class="list main_dbottom" width="100%">
		<span><b>出险结果必调配置轨迹</b></span>
		<thead>
			<tr align="center">
				<th nowrap>出险原因</th>
				<th nowrap>出险结果1代码</th>
				<th nowrap>出险结果1名称</th>
				<th nowrap>出险结果2代码</th>
				<th nowrap>出险结果2名称</th>
				<th nowrap>必调标识</th>
				<th nowrap>操作</th>
				<th nowrap>提交人</th>
				<th nowrap>提交日期</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag != null">
				<tr>
					<td colspan="13">
						<div class="noRueryResult">请选择条件查询数据！</div>
					</td>
				</tr>
			</s:if>
			<s:elseif
				test="claimAccResultSurveyLogVOPage.PageItems == null || claimAccResultSurveyLogVOPage.PageItems.size()==0">
				<tr>
					<td colspan="13">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:elseif>
			<s:iterator value="claimAccResultSurveyLogVOPage.PageItems" var="status" status="st">
				<tr align="center">
					<td>${accReasonStr }</td>
					<td>${accResult1 }</td>
					<td>${accResult1Name }</td>
					<td>${accResult2 }</td>
					<td>${accResult2Name }</td>
					<s:if test="mbSur==0">
						<td>否</td>
					</s:if>
					<s:else>
						<td>是</td>
					</s:else>
					<td>${operationType }</td>
					<td>${submitUserStr}</td>
					<td>${submitDateStr}</td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}"
				name="select" onchange="navTabPageBreak({numPerPage:this.value},'accidentResultAdjustLog')"
				value="claimAccResultSurveyLogVOPage.pageSize">
			</s:select>
			<span>条，共${claimAccResultSurveyLogVOPage.total}条</span>
		</div>
		<div class="pagination" targetType="navTab" rel=accidentResultAdjustLog
			totalCount="${claimAccResultSurveyLogVOPage.total}"
			numPerPage="${claimAccResultSurveyLogVOPage.pageSize}" pageNumShown="20"
			currentPage="${claimAccResultSurveyLogVOPage.pageNo}"></div>
	</div>
</div>
