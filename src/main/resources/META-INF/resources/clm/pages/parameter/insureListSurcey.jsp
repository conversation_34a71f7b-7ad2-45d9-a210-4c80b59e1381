<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<form id="pagerForm" method="post" 
	action="clm/parameter/selectSurcey_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${currentPage2.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage2.pageSize}" />
	<!-- 查询条件回调 -->
	<input type="hidden" name="claimApplySurveyVO.accReason"
		value="${claimApplySurveyVO.accReason}" /> <input type="hidden"
		name="claimApplySurveyVO.claimType"
		value="${claimApplySurveyVO.claimType}" />
</form>
<div>
	<table class="list" id="surveyApplyOperTable" width="100%">
		<thead>
			<tr>
				<th nowrap>选择</th>
				<th nowrap>出险时间距离生效时间计算符号</th>
				<th nowrap>出险时间距离生效时间</th>
				<th nowrap>出险时间距离生效时间类型</th>
				<th nowrap>保额计算符号</th>
				<th nowrap>保额（万元）</th>
				<th nowrap>出险原因</th>
				<th nowrap>理赔类型</th>
				<th nowrap>操作</th>
			</tr>
		</thead>
		<tbody>
			<s:if
				test="currentPage2.pageItems == null || currentPage2.pageItems.size()==0">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:if>
			<s:iterator value="currentPage2.pageItems" status="st">
				<tr align="center" target="listIdInfo">
					<td><input type="radio" class="radioIndex" name="r1"
						onclick='choseSurveyApply(this);'
						value="${listId}|${intervalSymbol}|${intervalNum}|${intervalType}|${amountSymbol}|${amount}|${accReason}|${claimType}" /></td>
					<td align="center" style="word-break: break-all;">${intervalSymbol}</td>
					<td align="center" style="word-break: break-all;">${intervalNum}</td>
					<td align="center" style="word-break: break-all;">${intervalType}</td>
					<td align="center" style="word-break: break-all;">${amountSymbol}</td>
					<td align="center" style="word-break: break-all;">${amount}</td>
					<td align="center" style="word-break: break-all;">${accReasonStr}</td>
					<td align="center" style="word-break: break-all;">${claimTypeStr}</td>
					<td><a title='删除' class="btnDel" id='delButton'
						href='javascript:void(0);'
						onclick='deleteApplySurvey("${listId}");'>删除</a></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}" name="select"
				onchange="navTabPageBreak({numPerPage:this.value},'insureListSurcey')"
				value="currentPage2.pageSize">
			</s:select>
			<span>条，共${currentPage2.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${currentPage2.total}"
			numPerPage="${currentPage2.pageSize}" pageNumShown="20"
			currentPage="${currentPage2.pageNo}"
			rel="insureListSurcey"></div>
	</div>
</div>

