//点击查询按钮
function queryLinkSwitchSettings(){
	var organCodePro = $("#operatorOrganMana1",navTab.getCurrentPanel()).val();
	if(organCodePro!=""){
		if(organCodePro.indexOf(currentOrganCode) == -1){
			alertMsg.error("只能查询当前用户机构及下属机构数据！");
			return false;
		}
	}
	$("#setJspForm", navTab.getCurrentPanel()).submit();
};

//机构树
function manaOperatorMySelectOrgan2() {
	$("#operatorOrganMana1",navTab.getCurrentPanel()).orgtreegrn(navTab.getCurrentPanel());
}
function manaOperatorMySelectOrgan1() {
	$("#noticePremOrganCode",navTab.getCurrentPanel()).orgtreegrn(navTab.getCurrentPanel());
}

//保存按钮
function saveSet(){
	var organCodePro = $("#noticePremOrganCode",navTab.getCurrentPanel()).val();
	if(organCodePro == null || organCodePro == ""){
		alertMsg.error("请选择统计机构！");
		return false;
	}
	if(organCodePro!=""){
		if(organCodePro.indexOf(currentOrganCode) == -1){
			alertMsg.error("只能选择当前用户机构及下属机构数据！");
			return false;
		}
	}
	
	$("#ruleProductList option",navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	var productLength = $("#ruleProductList", navTab.getCurrentPanel()).children().length;
	if(productLength > 300){
		alertMsg.error("请选择适量的险种信息");
		return false;
	}
	$("#saveLinkSet", navTab.getCurrentPanel()).submit();
}

//保存后回调函数
function saveNewsMethod(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		alertMsg.correct(json.message);
		queryLinkSwitchSettings();
	} else {
		alertMsg.error(json.message);
	}
}

//删除操作
function checkDelete(listId,selectVal){
	alertMsg.confirm("请确认是否删除？",{
		okCall : function() {
			$.ajax({
				url : "clm/parameter/deleteLinkSwitchSet_CLM_ciitcLinkSwitchSetAction.action?listId="+listId,
				global : false,
				type : "POST",
				dataType : "json",
				success : function(s) {
					//$(selectVal).parent().parent().remove();
					if (s.statusCode == DWZ.statusCode.ok) {
						alertMsg.correct(s.message);
						queryLinkSwitchSettings();
					} else {
						alertMsg.error(s.message);
					}
				}
			});
			
		}
	});
}
//配置轨迹查询

//信息回显
function getSelectInfo(delect){
	$(delect).parent().parent().find("td:eq(0)").find("input:radio").attr("checked",true);
	var selectedValue = $(delect).val().split("|");
	$("#listId", navTab.getCurrentPanel()).val(selectedValue[0]);
	$("#noticePremOrganCode", navTab.getCurrentPanel()).val(selectedValue[1]);
	$("#productType", navTab.getCurrentPanel()).selectMyComBox(selectedValue[2]);
	//$("#businessChannel", navTab.getCurrentPanel()).val(selectedValue[3]);
	var claimType = selectedValue[3];
	var claimTypes = claimType.split(",");
	 for(var i = 0; i < claimTypes.length; i++){
			var ch = "input[name='addLinkSwitchSettingsVO.claimTypes'][value='" + claimTypes[i].trim() + "']";
			$(ch).attr("checked","checked");
	  }
	$("#minBillAmount", navTab.getCurrentPanel()).val(selectedValue[5]);
	$("#maxBillAmount", navTab.getCurrentPanel()).val(selectedValue[6]);
	$("#minCalcAmount", navTab.getCurrentPanel()).val(selectedValue[7]);
	$("#maxCalcAmount", navTab.getCurrentPanel()).val(selectedValue[8]);
	var productCodes = selectedValue[9];
	$.ajax({
		'url':'clm/parameter/getProductCodes_CLM_ciitcLinkSwitchSetAction.action?productCodes='+productCodes,
		'type':'POST',
		'global':false,
		'datatype':'json',
		'success':function(data){	
			var data=eval("("+data+")");
			var prodList = data[0].prod.length;
			$("#ruleProductList", navTab.getCurrentPanel()).find("option").remove();
			for(var i=0;i<prodList;i++){
				$("#ruleProductList", navTab.getCurrentPanel()).append("<option class='item' value='"+data[0].prod[i].productCode+"'>"+data[0].prod[i].productCode+"_"+data[0].prod[i].productName+"</option>");
			}			
		}
	});
	$("#submitor", navTab.getCurrentPanel()).html(selectedValue[10]);
	var time =selectedValue[11];
	$("#submitTime", navTab.getCurrentPanel()).html(formatDate(time));
}
function cleanSave(){
	$("#noticePremOrganCode", navTab.getCurrentPanel()).val("");
	$("#productType", navTab.getCurrentPanel()).selectMyComBox("");
	$("#minBillAmount", navTab.getCurrentPanel()).val("");
	$("#maxBillAmount", navTab.getCurrentPanel()).val("");
	$("#minCalcAmount", navTab.getCurrentPanel()).val("");
	$("#maxCalcAmount", navTab.getCurrentPanel()).val("");
	$("#ruleProductList", navTab.getCurrentPanel()).find("option").remove();
	$("#submitor", navTab.getCurrentPanel()).html("");
	$("#submitTime", navTab.getCurrentPanel()).html("");
	var ch = $("input[name='addLinkSwitchSettingsVO.claimTypes']");
	ch.each(function(){
        $(this).removeAttr('checked',false);
    });
	
}

//格式化Date日期时间数据
function formatDate(date) {
	  var d = new Date(date),
	    month = '' + (d.getMonth() + 1),
	    day = '' + d.getDate(),
	    year = d.getFullYear();

	  if (month.length < 2) month = '0' + month;
	  if (day.length < 2) day = '0' + day;

	  return [year, month, day].join('-');
	}