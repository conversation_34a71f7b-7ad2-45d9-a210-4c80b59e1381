<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript">
	function saveData(){
		var medicalPay = $("[name='claimPreCalcpayConfigVO.medicalPay']",navTab.getCurrentPanel()).attr("value");
		if(medicalPay==""){
			alertMsg.error ("医疗险案均赔付金额必填");
			return;
		}
		var preTrigPay = $("[name='claimPreCalcpayConfigVO.preTrigPay']",navTab.getCurrentPanel()).attr("value");
		if(preTrigPay==""){
			alertMsg.error ("高额意外险预估调整触发金额必填");
			return;
		}
		var calcPrePay = $("[name='claimPreCalcpayConfigVO.calcPrePay']",navTab.getCurrentPanel()).attr("value");
		if(calcPrePay==""){
			alertMsg.error ("高额意外险理赔预设金额必填");
			return;
		}
		$("#calcpayConfigForm", navTab.getCurrentPanel()).submit();
	}
	function checkInputNumber(obj){
		obj.value=obj.value.replace(/\D/g,'');
	}
</script>
<body>
	<div class="pageHeader" layoutH="36">
		<div class="panel">
			<h1>参数管理》未决预估参数配置</h1>
			<div class="panelPageFormContent">
				<div>
					<form id="calcpayConfigForm" class="pageForm required-validate"
						action="clm/parameter/saveClaimPreCalcpayConfig_CLM_claimPreCalcpayConfigAction.action"
						method="post"
						onsubmit="return validateCallback(this, dialogAjaxDone)"
						novalidate="novalidate">
						<fieldset style="border-style: solid;">
							<div class="panelPageFormContent">
								<dl style="width: 100%;">
				                	<dt style="width: 30%;">
										医疗险案均赔付金额<font style="color: red">* </font>
									</dt>
									<dd>
										<input name="claimPreCalcpayConfigVO.medicalPay" value="${claimPreCalcpayConfigVO.medicalPay}" type="text" onkeyup="checkInputNumber(this)"/>
									</dd>
								</dl>
							</div>
							<div class="panelPageFormContent">
								<dl style="width: 100%;">
				                	<dt style="width: 30%;">
										高额意外险预估调整触发金额<font style="color: red">* </font>
									</dt>
									<dd>
										<input name="claimPreCalcpayConfigVO.preTrigPay" value="${claimPreCalcpayConfigVO.preTrigPay }" type="text" onkeyup="checkInputNumber(this)"/>
									</dd>
								</dl>
							</div>
							<div class="panelPageFormContent">
								<dl style="width: 100%;">
				                	<dt style="width: 30%;">
										高额意外险理赔预设金额<font style="color: red">* </font>
									</dt>
									<dd>
										<input name="claimPreCalcpayConfigVO.calcPrePay" value="${claimPreCalcpayConfigVO.calcPrePay }" type="text" onkeyup="checkInputNumber(this)"/>
									</dd>
								</dl>
							</div> 
							<div class="pageFormdiv">
						            <button type="button" class="but_blue" onclick="saveData()">保存</button>
							    	<button type="button" class="but_gray" onclick="navTab.closeCurrentTab();">退出</button>
							</div>
							<div></div>
						</fieldset>
					</form>
				</div>

			</div>
		</div>
	</div>
</body>



