<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<form id="pagerForm" method="post"
	action="clm/parameter/selectRuleConfiguration_CLM_claimIntelPortraitConfigAction.action">
	<input type="hidden" name="pageNum" vaule="${currentPage5.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage5.pageSize}" />
	<!-- 查询条件回调 -->
	<input type="hidden" name="claimRuleExplainVO.promptType"
		value="${claimRuleExplainVO.promptType}" /> <input type="hidden"
		name="claimRuleExplainVO.serviceScenario"
		value="${claimRuleExplainVO.serviceScenario}" /> <input type="hidden"
		name="claimRuleExplainVO.ruleCode"
		value="${claimRuleExplainVO.ruleCode}" /> <input type="hidden"
		name="claimRuleExplainVO.ruleName"
		value="${claimRuleExplainVO.ruleName}" />
</form>
<div>
	<table class="list" id="surveyApplyOperTable" width="100%">
		<thead>
			<tr>
				<th nowrap>选择</th>
				<th nowrap>提示类型</th>
				<th nowrap>数据服务场景</th>
				<th nowrap>现有规则编码</th>
				<th nowrap>现有规则名称</th>
				<th nowrap>是否提示</th>
				<th nowrap>提示内容</th>
				<th nowrap>必调项</th>
				<th nowrap>操作</th>
			</tr>
		</thead>
		<tbody>
			<%-- <s:if test="imageFlag == null">
											<tr>
												<td colspan="100">
													<div class="noRueryResult">请选择条件查询数据！</div>
												</td>
											</tr>
										</s:if> --%>
			<s:if
				test="currentPage5.pageItems == null || currentPage5.pageItems.size()==0">
				<tr>
					<td colspan="100">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:if>
			<s:iterator value="currentPage5.pageItems" status="st">
				<tr align="center" target="listIdInfo">
					<td><input type="radio" class="radioIndex" name="r1"
						onclick='queryLegalPersonInfo(this);'
						value="${listId}|${promptType}|${serviceScenario}|${ruleCode}|${ruleName}|${isPromptStr}|${promptContent}|${mbSurStr}" /></td>
					<td align="center" style="word-break: break-all;">${promptType}</td>
					<td align="center" style="word-break: break-all;">${serviceScenario}</td>
					<td align="center" style="word-break: break-all;">${ruleCode}</td>
					<td align="center" style="word-break: break-all;">${ruleName}</td>
					<td align="center" style="word-break: break-all;">${isPromptStr}</td>
					<td align="center" style="word-break: break-all;">${promptContent}</td>
					<td align="center" style="word-break: break-all;">${mbSurStr}</td>
					<td><a title='删除' class='btnDel' id='delButton'
						href='javascript:void(0);'
						onclick='deleteLegalPerson("${listId}");'>删除</a></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{20:'20',50:'50',100:'100',200:'200'}" name="select"
				onchange="navTabPageBreak({numPerPage:this.value},'claimIRuleConfiguration')"
				value="currentPage5.pageSize">
			</s:select>
			<span>条，共${currentPage5.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${currentPage5.total}"
			numPerPage="${currentPage5.pageSize}" pageNumShown="20"
			currentPage="${currentPage5.pageNo}" rel="claimIRuleConfiguration"></div>
	</div>
</div>

