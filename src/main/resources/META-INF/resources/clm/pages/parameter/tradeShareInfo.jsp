<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="clm/pages/parameter/positiveResultRedSetInit.js"></script>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">
//c网址
function goToCiit(){
	$.ajax({
		'type':'post',
		'url':'clm/parameter/goToCiit_CLM_ciitcLinkSwitchSetAction.action',
		'success':function(data){
			var json = eval("("+data+")");
			var url = json.url;
			if(url==null || url == ""){
				alertMsg.info("意健险平台结果查询网址为空！");
	            return false;
			}else{
				window.open(url);								
			}
			//navTab.openTab("3832654", url2, {title:'意健险平台结果查询网址'});
		}
	}); 
}
</script>
<!-- 分页表单 -->
<script type="text/javascript" charset="UTF-8">

function saveShareInfor(){
	/* if ($("#preAuditCode", $.pdialog.getCurrent()).val() == "") {
		alertMsg.warn("请完整录入所有配置信息。");
		$("#preAuditCode", $.pdialog.getCurrent()).focus();
		return false;
	} */
	
	$("#form66",$.pdialog.getCurrent()).submit();
	}

/* if($("#banana", navTab.getCurrentPanel()).attr("checked")=="checked"){
	$("#auditRemarkOne", navTab.getCurrentPanel()).show();	
}else {
	$("#auditRemarkOne", navTab.getCurrentPanel()).hide();
} */
</script>
<div layoutH="20" style="background: #fafafa" id="allForce">
  
   
      <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">个人意外险核保风险提示</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>多家公司承保提示</th>
             <th>重疾理赔史</th>
             <th>伤残理赔史</th>
             <th>是否密集投保</th>
             <th>累计保额提示</th>
             <th>自驾车意外险累计保额提示</th>
             <th>自驾车责任多家投保家数</th>
             <th>特定疾病理赔史</th>
             <th>是否有网页数据</th>
             <th>网页查询码</th>
             <th>数据截止日期</th>
          </tr>
       </thead>
       <tbody class="1111">
          <s:iterator value="ciitcBackResultVOList" var="status" status="st">
          <s:if test="productType eq 5">
          <tr>
            <td>${codeOn}</td>
            <td>${codeTw}</td>
            <td>${codeTh}</td>
            <td>${codeFo}</td>
            <td>${codeFi}</td>
            <td>${codeSi}</td>
            <td>${codeSe}</td>
            <td>${codeEi}</td>
            <td><s:if test="haveWebData == 0" >
            <a href="javascript:void(0);" onclick="goToCiit()">Y</a></s:if>
            <s:if test="haveWebData != 0" >N.A.</s:if></td>
            <td> ${webQueryCode}</td>
            <td><s:date name="dataExpiryDate" format="yyyy-MM-dd"/></td>
          </tr>
		 </s:if>
          </s:iterator>
       </tbody>
     </table>
     </div>
     
     <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">个人健康险理赔风险提示(补偿医疗)</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>重疾理赔史</th>
             <th>慢性病理赔史</th>
             <th>一年内总赔付次数</th>
             <th>多家公司承保提示</th>
             <th>特定疾病理赔史</th>
             <th>是否有网页数据</th>
             <th>网页查询码</th>
             <th>数据截止日期</th>
          </tr>
       </thead>
       <tbody class="1111">
          <s:iterator value="ciitcBackResultVOList" var="status" status="st">
          <s:if test="productType eq 1">
          <tr>
            <td> ${codeNiS}</td>
            <td> ${codeTeS}</td>
            <td> ${codeTwS}</td>
            <td> ${codeTeOnS}</td>
            <td> ${codeTeTwS}</td>
            <td> ${codeOnS}</td>
            <td> ${codeEiS}</td>
            <td> <s:if test="haveWebData == 0" >
            <a href="javascript:void(0);" onclick="goToCiit()">Y</a></s:if>
            <s:if test="haveWebData != 0" >N.A.</s:if></td>
            <td> ${webQueryCode}</td>
            <td><s:date name="dataExpiryDate" format="yyyy-MM-dd"/></td>
          </tr>
		 </s:if>
         </s:iterator>
       </tbody>
     </table>
     </div>
     
     <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">个人健康险理赔风险提示(重大疾病)</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>重疾理赔史</th>
             <th>慢性病理赔史</th>
             <th>重疾保额</th>
             <th>多家公司承保提示</th>
             <th>密集投保</th>
             <th>投保前理赔</th>
             <th>特定疾病理赔史</th>
             <th>是否有网页数据</th>
             <th>网页查询码</th>
             <th>数据截止日期</th>
          </tr>
       </thead>
       <tbody class="1111">
         <s:iterator value="ciitcBackResultVOList" var="status" status="st">
         <s:if test="productType eq 2">
         <tr>
            <td> ${codeNiT}</td>
            <td> ${codeTeT}</td>
            <td> ${codeTwT}</td>
            <td> ${codeTeOnT}</td>
            <td> ${codeTeThT}</td>
            <td> ${codeOnT}</td>
            <td> ${codeTeFoT}</td>
            <td> ${codeTeFiT}</td>
            <td> ${codeEiT}</td>
            <td> <s:if test="haveWebData == 0" >
            <a href="javascript:void(0);" onclick="goToCiit()">Y</a></s:if>
            <s:if test="haveWebData != 0" >N.A.</s:if></td>
            <td> ${webQueryCode}</td>
            <td><s:date name="dataExpiryDate" format="yyyy-MM-dd"/></td>
          </tr>
		 </s:if>
         </s:iterator>
       </tbody>
     </table>
     </div>
     
     <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">个人健康险理赔风险提示(个人津贴)</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>重疾理赔史</th>
             <th>慢性病理赔史</th>
             <th>理赔次数风险</th>
             <th>有效保额累计日额</th>
             <th>累计津贴险赔付天数</th>
             <th>特定疾病理赔史</th>
             <th>是否有网页数据</th>
             <th>网页查询码</th>
             <th>数据截止日期</th>
          </tr>
       </thead>
       <tbody class="1111">
           <s:iterator value="ciitcBackResultVOList" var="status" status="st">
           <s:if test="productType eq 3">
           <tr>
            <td> ${codeNiF}</td>
            <td> ${codeTeF}</td>
            <td> ${codeTwF}</td>
            <td> ${codeTeOnF}</td>
            <td> ${codeTeSiF}</td>
            <td> ${codeTeSeF}</td>
            <td> ${codeTeEiF}</td>
            <td> ${codeEiF}</td>
            <td> 
            <s:if test="haveWebData == 0" >
            <a href="javascript:void(0);" onclick="goToCiit()">Y</a></s:if>
            <s:if test="haveWebData != 0" >N.A.</s:if>
            </td>
            <td> ${webQueryCode}</td>
            <td><s:date name="dataExpiryDate" format="yyyy-MM-dd"/></td>
          </tr>
		 </s:if>
        </s:iterator>
       </tbody>
     </table>
     </div>
     <form id="pagerForm" method="post"
	action="clm/parameter/tradeShareInfo_CLM_ciitcLinkSwitchSetAction.action?leftFlag=0&menuId=${menuId }&caseId=${caseId}">
	<input type="hidden" name="pageNum" value="${pageInfo.currentPage}" />
	<input type="hidden" name="numPerPage" value="${pageInfo.numPerPage}" />
</form>
     <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">理赔重复收据提示</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>客户既往是否存在重复发票</th>
             <th>收据号其他应用赔案</th>
             <th>是否有分割单</th>
             <th>是否有网页数据</th>
             <th>网页查询码</th>
             <th>数据截止日期</th>
          </tr>
       </thead>
       <tbody class="1111">
          <s:iterator value="ciitcBackResultVOList" var="status" status="st">
          <s:if test="productType eq 4">
          <tr>
            <td> ${codeTeNi}</td>
            <td> ${codeTwZe}</td>
            <td> ${codeTwOn}</td>
            <td> <s:if test="haveWebData == 0" >
            <a href="javascript:void(0);" onclick="goToCiit()">Y</a></s:if>
            <s:if test="haveWebData != 0" >N.A.</s:if></td>
            <td> ${webQueryCode}</td>
            <td><s:date name="dataExpiryDate" format="yyyy-MM-dd"/></td>
          </tr>
		 </s:if>
         </s:iterator>
       </tbody>
     </table>
     </div>
     
      <div class="divfclass">
    	<h1><img src="clm/images/tubiao.png">个人意外险理赔风险提示</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>多家公司承保提示</th>
             <th>伤残理赔史</th>
             <th>非正常核保结论</th>
             <th>非正常理赔结论</th>
             <th>短期出险史</th>
             <th>是否死亡</th>
             <th>查询公司家数</th>
             <th>重疾理赔史</th>
             <th>是否密集投保</th>
             <th>意外医疗赔付总次数</th>
             <th>是否有网页数据</th>
             <th>网页查询码</th>
             <th>数据截止日期</th>
          </tr>
       </thead>
       <tbody class="1111">
          <s:iterator value="ciitcBackResultVOList" var="status" status="st">
          <s:if test="productType eq 6">
          <tr>
            <td>${codeOnC}</td>
            <td>${codeThC}</td>
            <td>${codeNiC}</td>
            <td>${codeTeC}</td>
            <td>${codeTwTwC}</td>
            <td>${codeTwThC}</td>
            <td>${codeTwFoC}</td>
            <td>${codeTwC}</td>
            <td>${codeFoC}</td>
            <td>${codeTwFiC}</td>
            <td><s:if test="haveWebData == 0" >
            <a href="javascript:void(0);" onclick="goToCiit()">Y</a></s:if>
            <s:if test="haveWebData != 0" >N.A.</s:if></td>
            <td> ${webQueryCode}</td>
            <td><s:date name="dataExpiryDate" format="yyyy-MM-dd"/></td>
          </tr>
		 </s:if>
          </s:iterator>
       </tbody>
     </table>
     </div>
     
      <div class="divfclass">
    	<h1><img src="clm/images/tubiao.png">风险事件广播</h1>
      </div>
      <div>
      <table class="list main_dbottom">
       <thead>
          <tr>
             <th>风险场景名称</th>
             <th>标签名称</th>
             <th>保险公司机构代码</th>
             <th>保单号</th>
             <th>信息来源</th>
             <th>信息接收时间</th>
          </tr>
       </thead>
       <tbody class="1111">
          <s:iterator value="ihiClmbroCustomerVOList" var="status" status="st">
          <tr>
            <td>${sceneName}</td>
            <td>${labelName}</td>
            <td>${appkey}</td>
            <td>${policyCode}</td>
            <td>意健险平台</td>
            <td><s:date name="receiveTime" format="yyyy-MM-dd HH:mm:ss"/></td>
          </tr>
          </s:iterator>
       </tbody>
     </table>
     </div>
     
     <div class="divfclass">
		    <h1><img src="clm/images/tubiao.png">意健险平台返回结果查看记录</h1>
      </div>
      <div class="tabdivclassbr main_tabdiv">
	<div class="main_border">
	<table class="list main_all" width="100%">
		<thead>
			<tr >
				<th>序号</th>
				<th>查看人</th>
				<th>查看时间</th>
			</tr>
		</thead>
		<tbody>		
			<s:iterator value="currentPage.pageItems"  status="st"  >
			<tr align="center"  target="listIdInfo" rel="${listId}">
				<td> ${st.index+1}</td>
				<td><Field:codeValue value="${opertorId}" tableName="APP___CLM__DBUSER.T_UDMP_USER"/></td>
				<td><s:date name="updateTime" format="yyyy-MM-dd HH:mm:ss"/> </td>
			</tr>
			</s:iterator>
		</tbody>
	</table>
	</div>
	<div class="panelBar" >
		<div class="pages">
			<span>显示</span>
			<s:select   list="#{20:'20',50:'50',100:'100',200:'200'}"  name="select" onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
	    		</s:select>
			<span>条，共${currentPage.total}条</span>		
		</div>
		<div class="pagination" targetType="navTab"
						totalCount="${currentPage.total}"
						numPerPage="${currentPage.pageSize}" pageNumShown="10"
						currentPage="${currentPage.pageNo}"></div>

	</div>
	</div>
    <%-- <form name="form2" id="form2" method="post" action="clm/parameter/saveTradeShareInfo_CLM_ciitcLinkSwitchSetAction.action?caseId=${caseId}" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
		<div class="">
			
			<dl>
				<dt>行业共享信息有效性</dt>
				<dd>
					<input type="radio" id="shareConditionValid" name="claimCaseVO.shareConditionValid" 
									value="0" checked="checked">有效
				    <input type="radio" id="shareConditionValid"
						name="claimCaseVO.shareConditionValid" 
						value="1">无效
				</dd> 			
				</dl>
				<dl>	
			<dl>
			<dt>
				<font class="point" style="color: red;">* </font>无效原因描述
			</dt>
			<dd >
				<textarea rows="3" cols="80" id="shareConditionDecision" 
					onpropertychange="if(value.length>1500) value=value.substring(0,1500)"
					name="claimCaseVO.shareConditionDecision" >${claimCaseVO.shareConditionDecision}</textarea>
			</dd>
		</dl>
		                                                                          
		</div>
		
		<div class="formBarButton">
			<ul>
				<li><button class="but_blue" type="button" onclick="save()">保存</button></li>
				<li><button type="button" class="but_blue close">退出</button></li>
			</ul>
		</div>
	</form> --%>
	
	
	
	 <div class="divfclass">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">行业共享信息有效性
				</h1>
			</div>
			<div>
				<div class="divfclass" >
					<form name="form66" id="form66" method="post" action="clm/parameter/saveTradeShareInfo_CLM_ciitcLinkSwitchSetAction.action?caseId=${caseId}" class="pageForm required-validate" onsubmit="return validateCallback(this, dialogAjaxDone)">
		                <dl>
							<dt></dt>
							<dt>
								<input type="radio" id="shareConditionValid" name="claimCaseVO.shareConditionValid" 
											value="1" checked="checked">有效
							    <input type="radio" id="shareConditionValid"
									name="claimCaseVO.shareConditionValid" 
									value="0" <s:if test="claimCaseVO.shareConditionValid eq 0"> checked="checked"</s:if>>无效
							</dd>
						</dl>
						<dl style="width: 100%;margin-top: 30px">
							<dt>
								<font class="point" style="color: red;">* </font>无效原因描述
							</dt>
							<dt>
								<textarea rows="3" cols="80" id="shareConditionDecision" 
									onpropertychange="if(value.length>100) value=value.substring(0,100)"
									name="claimCaseVO.shareConditionDecision" >${claimCaseVO.shareConditionDecision}</textarea>
							</dd>
						</dl>
					</form>
				</div>
			</div>
		</div> 
        <div class="formBarButton">
			<ul>
				<li><button class="but_blue" type="button" onclick="saveShareInfor()">保存</button></li>
				<li><button type="button" class="but_blue close">返回</button></li>
			</ul>
		</div>
</div>