<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@taglib uri="/struts-tags" prefix="s"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">

<script type="text/javascript">
//隐藏所有表单
	function hidden() {
		$("#monitRegister", navTab.getCurrentPanel()).hide();
 		$("#monitReport", navTab.getCurrentPanel()).hide();
 		$("#monitCheck", navTab.getCurrentPanel()).hide();
 		$("#monitSign", navTab.getCurrentPanel()).hide();
	}
//根据点击表单查询回显的该表单
	$(function() {
		var flag = $("#reportCommPoolJspFlag", navTab.getCurrentPanel()).val();
 		$("#monitRegister", navTab.getCurrentPanel()).hide();
 		$("#monitReport", navTab.getCurrentPanel()).hide();
 		$("#monitCheck", navTab.getCurrentPanel()).hide();
 		$("#monitSign", navTab.getCurrentPanel()).hide();
 		
		if (flag == 1) {
			$("#monitRegister", navTab.getCurrentPanel()).show();
		}
		if (flag == 2) {
			$("#monitReport", navTab.getCurrentPanel()).show();
		}
		if (flag == 3) {
			$("#monitCheck", navTab.getCurrentPanel()).show();
		}
		if (flag == 4) {
			$("#monitSign", navTab.getCurrentPanel()).show();
		}
 		if (flag == null || flag == '') {
 			$("#monitRegister", navTab.getCurrentPanel()).hide();
 	 		$("#monitReport", navTab.getCurrentPanel()).hide();
 	 		$("#monitCheck", navTab.getCurrentPanel()).hide();
 	 		$("#monitSign", navTab.getCurrentPanel()).hide();
 		}
	});
	//  1: 立案登记  2：报案  3： 审核  4： 签收
	function selectmark(markvalue) {
		//如果点击的是 1 
		if (markvalue == 1) {
			//隐藏其他表单，只显示点击的表单
			hidden();
			$("#monitRegister", navTab.getCurrentPanel()).show();
			//异步访问数据库
			var url = "clm/personalPool/personalPool_personalPoolAction.action?menuId="+menuId+"&flag=1";
			$.ajax({
				type:"post",
				url: url,
				cache:false,
				data:$('#personalPoolJspForm', navTab.getCurrentPanel()).serialize(),//表单提交  temp表单的id
				dataType:"json",
				success:function(data){
					$("#register", navTab.getCurrentPanel()).empty();
					var tr_all='';
					for(var i=0;i<data.length;i++){
						var caseNoAndStatus = data[i].caseNo +","+ data[i].caseStatus+","+data[i].caseId;
						var hrefVal = "clm/personalPool/toDiffPage_personalPoolAction.action?leftFlag=1&menuId=8082&caseNoAndStatus="+caseNoAndStatus;
						
						var tr = "<tr ><td> "+i+"</td>"+
						           "<td><a href=\""+ hrefVal + "\"  target=\"ajaxTodo\" rel=\"starttask\">"+data[i].caseNo+"</a></td>" +
						            "<td>"+data[i].accName+"</td>"+
						            "<td>"+data[i].accCertiNo+"</td>"+
						            "<td>"+data[i].caseStatus+"</td>"+
						            "<td>"+data[i].orgId+"</td>"+
						            "<td>"+data[i].greenFlag+"</td>"+
						            "<td>"+data[i].caseNo+"</td>"+
						            "<td>"+data[i].signConfTime+"</td>";
// 						            "<td >"+data[i].bpmTaskId+"</td>";
						tr+="</tr>";
						tr_all=tr_all+tr;	
					}
					$("#register", navTab.getCurrentPanel()).append(tr_all);
					$("#monitRegister", navTab.getCurrentPanel()).cssTable();
					$("a[target=ajaxTodo]", document).ajaxTodo();
				}
			});
		} else if (markvalue == 2) {
			hidden();
			$("#monitReport", navTab.getCurrentPanel()).show();
			var url = "clm/personalPool/personalPool_personalPoolAction.action?menuId="+menuId+"&flag=2";
			$.ajax({
				type:"post",
				url: url,
				cache:false,
				data:$('#personalPoolJspForm', navTab.getCurrentPanel()).serialize(),//表单提交  temp:form表单的id
				dataType:"json",
				success:function(data){
					$("#report", navTab.getCurrentPanel()).empty();
					var tr_all='';
					for(var i=0;i<data.length;i++){
						var caseNoAndStatus = data[i].caseNo +","+ data[i].caseStatus+","+data[i].caseId;
						var hrefVal = "clm/personalPool/toDiffPage_personalPoolAction.action?leftFlag=1&menuId=8082&caseNoAndStatus="+caseNoAndStatus;
						
						var tr = "<tr ><td> "+i+"</td>"+
// 						var tr = "<tr target=\"caseno\" rel="+ data[i].caseNo +","+ data[i].caseStatus+","+data[i].caseId +" ><td> "+i+"</td>"+
						            "<td><a href=\""+ hrefVal + "\"  target=\"ajaxTodo\" rel=\"starttask\">"+data[i].caseNo+"</a></td>" +
						            "<td>"+data[i].insuredId+"</td>"+
						            "<td>"+data[i].accName+"</td>"+
						            "<td>"+data[i].accCertiNo+"</td>"+
						            "<td>"+data[i].claimType+"</td>"+
						            "<td>"+data[i].greenFlag+"</td>"+
						            "<td>"+data[i].insertBy+"</td>"+
						            "<td>"+data[i].orgId+"</td>"+
						            "<td>"+data[i].rptrTime+"</td>";
// 						            "<td>"+data[i].bpmTaskId+"</td>";
						tr+="</tr>";
						tr_all=tr_all+tr;	
					}
					$("#report", navTab.getCurrentPanel()).append(tr_all);
					$("#monitReport", navTab.getCurrentPanel()).cssTable();
			 		$("a[target=ajaxTodo]", document).ajaxTodo();
				}
			});
		} else if (markvalue == 3) {
			hidden();
			$("#monitCheck", navTab.getCurrentPanel()).show();
			var url = "clm/personalPool/personalPool_personalPoolAction.action?menuId="+menuId+"&flag=3";
			$.ajax({
				type:"post",
				url: url,
				cache:false,
				data:$('#personalPoolJspForm', navTab.getCurrentPanel()).serialize(),//表单提交  fm:form表单的id
				dataType:"json",
				success:function(data){
					$("#check", navTab.getCurrentPanel()).empty();
					var tr_all='';
					for(var i=0;i<data.length;i++){
						var caseNoAndStatus = data[i].caseNo +","+ data[i].caseStatus+","+data[i].caseId;
						var hrefVal = "clm/personalPool/toDiffPage_personalPoolAction.action?leftFlag=1&menuId=8082&caseNoAndStatus="+caseNoAndStatus;
						
						var tr = "<tr ><td> "+i+"</td>"+
						           "<td><a href=\""+ hrefVal + "\"  target=\"ajaxTodo\" rel=\"starttask\">"+data[i].caseNo+"</a></td>" +
						            "<td>"+data[i].accName+"</td>"+
						            "<td>"+data[i].accCertiNo+"</td>"+
						            "<td>"+data[i].greenFlag+"</td>"+
						            "<td>"+data[i].caseNo+"</td>"+
						            "<td>"+data[i].caseNo+"</td>"+
						            "<td>"+data[i].advanceFlag+"</td>"+
						            "<td>"+data[i].uwFlag+"</td>"+
						            "<td>"+data[i].caseNo+"</td>"+
						            "<td>"+data[i].treatyFlag+"</td>"+
						            "<td>"+data[i].discussFlag+"</td>"+
						            "<td>"+data[i].orgId+"</td>"+
						            "<td>"+data[i].caseNo+"</td>";
// 						            "<td>"+data[i].bpmTaskId+"</td>";
						tr+="</tr>";
						tr_all=tr_all+tr;		
					}
					$("#check", navTab.getCurrentPanel()).append(tr_all);
					$("#monitCheck", navTab.getCurrentPanel()).cssTable();
					$("a[target=ajaxTodo]", document).ajaxTodo();
				}
			});
		} else if (markvalue == 4) {
			hidden();
			$("#monitSign", navTab.getCurrentPanel()).show();
			var url = "clm/personalPool/personalPool_personalPoolAction.action?menuId="+menuId+"&flag=4";
			$.ajax({
				type:"post",
				url: url,
				cache:false,
				data:$('#personalPoolJspForm', navTab.getCurrentPanel()).serialize(),//表单提交  fm:form表单的id
				dataType:"json",
				success:function(data){
					$("#sign", navTab.getCurrentPanel()).empty();
					var tr_all='';
					for(var i=0;i<data.length;i++){
						var caseNoAndStatus = data[i].caseNo +","+ data[i].caseStatus+","+data[i].caseId;
						var hrefVal = "clm/personalPool/toDiffPage_personalPoolAction.action?leftFlag=1&menuId=8082&caseNoAndStatus="+caseNoAndStatus;
						
						var tr = "<tr ><td> "+i+"</td>"+
						           "<td><a href=\""+ hrefVal + "\"  target=\"ajaxTodo\" rel=\"starttask\">"+data[i].caseNo+"</a></td>" +
			            "<td>"+data[i].insuredId+"</td>"+
			            "<td>"+data[i].accName+"</td>"+
			            "<td>"+data[i].accCertiNo+"</td>"+
			            "<td>"+data[i].claimType+"</td>"+
			            "<td>"+data[i].greenFlag+"</td>"+
			            "<td>"+data[i].insertBy+"</td>"+
			            "<td>"+data[i].orgId+"</td>"+
			            "<td>"+data[i].rptrTime+"</td>";
// 			            "<td>"+data[i].bpmTaskId+"</td>";
					tr+="</tr>";
					tr_all=tr_all+tr;	
					}
					$("#sign", navTab.getCurrentPanel()).append(tr_all);
					$("#monitSign", navTab.getCurrentPanel()).cssTable();
					$("a[target=ajaxTodo]", document).ajaxTodo();
				}
			});
		}
		var menuId= "${menuId }";
	}
	
</script>

<div layoutH="0" id="personalPoolJsp">
	<!-- 分页查询访问路径 -->
	<form id="pagerForm" method="post"
		action="clm/personalPool/personalPool_personalPoolAction.action?leftFlag=0&menuId=${menuId }">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
		
	</form>
	<!-- 查询事件访问路径 -->
	<form id="personalPoolJspForm"
		action="clm/personalPool/personalPool_personalPoolAction.action.action?leftFlag=0&menuId=${menuId }"
		method="post" onsubmit="return  validateCallback(this,navTabAjaxDone); "
		class="pagerForm required-validate">
		<!-- 查询区域 -->
		<div>
			<table class="list" width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td width="260">&nbsp;&nbsp; 接受任务开关&nbsp;&nbsp; 
						<input type="radio" id="ON" name="switch" value="1" checked /><label for="ON">开</label> 
						<input type="radio" id="OFF" name="switch" value="2" /> <label for="OFF">关</label>
					</td>

					<td width="260">&nbsp;&nbsp;任务列表&nbsp;&nbsp; 
						<select class="combox title"  name="" id="">
							<option>--------请选择 ------</option>
							<option value="1">立案登记任务</option>
							<option value="2">报案任务</option>
							<option value="3">审核任务</option>
							<option value="4">签收任务</option>
					</select>
					</td>
					<td>
						<div class="buttonActive">
							<div class="buttonContent">
								<button type="button">申请任务</button>
							</div>
						</div>
					</td>
					<td align="center"><a href="#">消息中心(${flag})</a></td>
				</tr>
			</table>

			<table class="list" width="100%" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td>&nbsp;&nbsp;
					 	<a href="#" id="mark" name="1" onclick="selectmark(1)">立案登记(1)</a>&nbsp;&nbsp; 
						<a href="#" id="mark" name="2" onclick="selectmark(2)">报案(2) </a>&nbsp;&nbsp;
						<a href="#" id="mark" name="3" onclick="selectmark(3)">审核(3)</a>&nbsp;&nbsp;
						<a href="#" id="mark1" name="4" onclick="selectmark(4)">签收(4)</a>&nbsp;&nbsp;
						<input type="hidden" id="1" name="vo.taskCode" value="123"/>
						<input type="hidden" id="2" name="vo.taskCode" value="456"/>
						<input type="hidden" name="vo.taskCode" value="789"/>
						<input type="hidden" name="vo.taskCode" value="321"/>
						<input type="hidden" name="vo.taskCode" value="457"/>
					</td>
				</tr>
			</table>
			<!-- 显示数据列表区域 -->
				<table class="list"id="monitRegister"  width="100%" border="0" cellspacing="0" cellpadding="0'">
					<thead>
						<div class="panelBar">立案登记任务</div>
						<tr>
							<th width="200">序号</th>
							<th width="400">赔案号</th>
							<th width="400">出险人姓名</th>
							<th width="400">证件号码</th>
							<th width="400">赔案状态</th>
							<th width="400">签收机构</th>
							<th width="400">绩优等级</th>
							<th width="400">签收人</th>
							<th width="400">签收时间</th>
<!-- 							<th width="400">任务ID</th> -->
						</tr>
					</thead>
					<tbody align="center" id="register" >
					</tbody>
				</table>
				<table class="list"  id="monitReport" width="100%" border="0" cellspacing="0" cellpadding="0'">
					<thead>
						<tr>
							<th width="200">序号</th>
							<th width="400">赔案号</th>
							<th width="400">出险人客户号</th>
							<th width="400">出险人姓名</th>
							<th width="400">证件号码</th>
							<th width="400">理赔类型</th>
							<th width="400">绩优等级</th>
							<th width="400">报案操作人</th>
							<th width="400">机构</th>
							<th width="400">报案时间</th>
<!-- 							<th width="400">任务ID</th> -->
						</tr>
					</thead>
					<tbody align="center" id="report">
					</tbody>
				</table>
				<table class="list" id="monitCheck" width="100%" border="0" cellspacing="0" cellpadding="0'">
					<thead>
						<tr>
							<th width="200">序号</th>
							<th width="400">赔案号</th>
							<th width="500">出险人姓名</th>
							<th width="400">证件号码</th>
							<th width="700">绩优等级</th>
							<th width="300">案件权限</th>
							<th width="300">总时效</th>
							<th width="400">预付标识</th>
							<th width="400">二核标识</th>
							<th width="500">补充单证标识</th>
							<th width="400">协谈标识</th>
							<th width="400">合议标识</th>
							<th width="400">机构</th>
							<th width="500">事中质检标识</th>
<!-- 							<th width="400">任务ID</th> -->
						</tr>
					</thead>
					<tbody align="center" id="check">
					</tbody>
				</table>
				<table class="list" id="monitSign" width="100%" border="0" cellspacing="0" cellpadding="0'">
					<thead>
						<tr>
							<th width="200">序号</th>
							<th width="400">赔案号</th>
							<th width="400">出险人客户号</th>
							<th width="400">出险人姓名</th>
							<th width="400">证件号码</th>
							<th width="400">理赔类型</th>
							<th width="400">绩优等级</th>
							<th width="400">报案操作人</th>
							<th width="400">机构</th>
							<th width="400">报案时间</th>
<!-- 							<th width="400">任务ID</th> -->
						</tr>
					</thead>
					<tbody align="center" id="sign">
					</tbody>
				</table>
<!-- 				<div align="right"> -->
<!-- 				<a href="clm/personalPool/toDiffPage_personalPoolAction.action?leftFlag=1&menuId=8082&caseNoAndStatus={caseno}"   target="ajaxTodo" rel="starttask"><button>跳转到页面</button></a> -->
<!-- 				</div> -->
			<!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
		</div>	
	</form>
</div>


