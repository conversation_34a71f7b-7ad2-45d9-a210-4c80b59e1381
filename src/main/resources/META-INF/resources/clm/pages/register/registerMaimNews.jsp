<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript">

 
//单选按钮
 
function radiockeacd(obj){ 
	//定位数据 
	var innerPage = $(obj).parent().parent().parent().parent()
			.parent().parent(); 
	 
	var pageFlag = $(innerPage).parent().attr("titles");
	var billType = $(innerPage).parent().attr("billType"); 
	var injuryId = $(obj).parent().find("#injuryId").val();  
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();  
  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
			+ pageFlag 
			+ '&caseId='
			+ caseId 
		 	+ '&claimInjuryVO.injuryId=' + injuryId
		 	+'&claimInjuryVO.caseId=' + caseId;
	$(innerPage).next().next().loadUrl(url); 
	$(innerPage).next().next().show("30000"); 

	return;
	 
}




function  deleteValueInjury(obj){ 
	
	var injuryId = $(obj).parent().find("#injuryId").val();
	 
	if(injuryId!=""&&injuryId!="0"){  
	$.ajax({ 'type':'post',
			'url':'clm/register/deleteClaimDutyInfo_CLM_tClaimDutyRegisterAction.action'+ '?claimInjuryVO.injuryId=' + injuryId+'&claimTypecode[0]=2',
			'datatype':'json',
			'success':function(data){
				 if(data){ 
					 
					 $(obj).parent().parent().remove();
					 alertMsg.info("删除成功");
				 }else{
					 alertMsg.info("删除失败");
				 }
			    
			},
 			'error':function(){
 				alert("出错了！");
 			}
	 });
	}
	 
}


$(".injuryCode111", navTab.getCurrentPanel()).each(function(i,obj){
	 
	if($(this).html()!=null){
	//是否是详细的列表injuryCode1
	 var deformityType=$(this).parent().find("#deformityType11").attr("deformityTypeValue");
	 var deformityGrade=$(this).parent().find("#deformityGrade11").attr("deformityGradeValue"); 
	   var injuryCode1=$(this).attr("injuryValue") ;
	   
	 if(deformityType != null && deformityType != ""){
		 if(deformityGrade != null && deformityGrade != ""){
			 
			 $.ajax({
		  			'type':'post',
		  			'url':'clm/register/queryInjuryCode1Info_CLM_tClaimDutyRegisterAction.action?deformityCode1VO.deformityType='+deformityType+'&deformityCode1VO.deformityGrade='+deformityGrade+'&deformityCode1VO.deformityCode1='+injuryCode1,
		  			'datatype':'json',
		  			'success':function(data){
		  				var data = eval("(" + data + ")");
		  				 
		  				 if(data!=null&&data!=""){
		  				    $(obj).html(data[0].deformityCode1Name);  
		  				} 
		  			},
		  			'error':function(){
		  				alert("出错了！");
		  			}
		  	});  
		 }
	 }
	}
	  
})  ;

//用于伸缩按钮的伸缩功能
//$(document).ready(function(){
	  
$(".main_heading", navTab.getCurrentPanel()).off().toggle(function(){  
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
},function(){
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
 	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
});

//});

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]").bind("click",function(){
//		var id=$(this).attr("id").split("main_")[1];
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
})

</script>
			<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">伤残录入
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
										<div class="main_bqtitle">
					                         <h1><img src="clm/images/three.png">伤残详细信息录入</h1>
					                     </div>
					                     		<div id="maimNewsInt" titles="registerMaim" billType="7">
														<div class="panelPageFormContent">
															<div class="tabdivclassbr">		
															<table class="list nowrap itemDetail" addButton="添  加" id=""
																hiddenAdd="hiddenAdd" style="border: 2px solid #CCC;width: 100%;">
																<thead>
																	<tr>
																		<th nowrap>选择</th>
																		<th nowrap>序号</th>
													
																		<th nowrap backValueFlag="deformityType" size="12"><font
																			class="point" color="red">* </font>伤残类型</th>
													
																		<th nowrap backValueFlag="deformityGrade" ><font
																			class="point" color="red">* </font>伤残级别</th>
													
																		<th nowrap backValueFlag="injuryCode1" >伤残代码1</th>
													
																		<th nowrap backValueFlag="injuryCode2" >伤残代码2</th>
													
																		<th nowrap backValueFlag="partDegree" size="12">参与度</th>
													
																		<th nowrap backValueFlag="payRate" size="12">残疾给付比例</th>
													
																		<th nowrap backValueFlag="assessOrgan" size="12">鉴定机构</th>
													
																		<th nowrap backValueFlag="assessDate" dateFlag="dateFlag" size="12">鉴定日期</th>
													
																		<th nowrap backValueFlag="remark" size="12">备注</th>
													
																		<th nowrap type="del" >操作</th>
																	</tr>
																</thead>
																<tbody class="list" id="injuryTBody">
																	<s:iterator value="claimInjuryVOlist" status="st">
																		<tr>
																			<td>
																			<input type='hidden'   id='injuryId'
																				value='${injuryId}'  />
																			<input type='radio' name="radio" id='radio${st.index}'
																				value='0' onclick="radiockeacd(this)" class="MedRadio" /></td>
																			<td>${st.index+1}
<!-- 																			<input class="digits textInput" type="text" size="5" -->
<%-- 																				value="${st.index+1}" /> --%>
																				</td>
																			<td id="deformityType11" deformityTypeValue="${deformityType}"><Field:codeValue
																					tableName="APP___CLM__DBUSER.T_DEFORMITY_TYPE"
																					value="${deformityType}" /></td>
																			<td  id="deformityGrade11" deformityGradeValue="${deformityGrade}" ><Field:codeValue
																					tableName="APP___CLM__DBUSER.T_DEFORMITY_GRADE"
																					value="${deformityGrade}" /></td>
																			<td class="injuryCode111" injuryValue="${injuryCode1}"><Field:codeValue
																					tableName="APP___CLM__DBUSER.T_DEFORMITY_CODE1"
																					value="${injuryCode1}" /></td>
																			<td><Field:codeValue
																					tableName="APP___CLM__DBUSER.T_DEFORMITY_CODE2"
																					value="${injuryCode2}" /></td>
																			<td>${partDegree}</td>
																			<td>${payRate}</td>
																			<td>${assessOrgan}</td>
																			<td><s:date name='assessDate' format='yyyy-MM-dd' /></td>
																			<td>${remark}</td>
																			<td><a class="btnDel" href="javascript:void(0)" onclick="deleteValueInjury(this)"> </a>
																			<input type='hidden'   id='injuryId'
																				value='${injuryId}'  />
																			</td>
																		</tr>
																	</s:iterator>
																</tbody>
															</table>
															</div>
														</div>
													
													
													
														<s:include value="addBarForDuty.jsp" />
													
													</div>
					                     
					                     
									</div>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
