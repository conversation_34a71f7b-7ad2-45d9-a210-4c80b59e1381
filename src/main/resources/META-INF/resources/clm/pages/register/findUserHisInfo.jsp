<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<table class="list main_dbottom" width="100%" id="hirstyList">
<thead>
	<tr>
        <th nowrap>用户姓名</th>
		<th nowrap>用户编号</th>
		<th nowrap>用户所属机构</th>
		<th nowrap>修改时间</th>
		<th nowrap>保单号</th>
		<th nowrap>客户身份</th>
		<th nowrap>修改内容</th>
	</tr>
</thead>
<tbody align="center">
		    <s:if test="imageFlag != null">
				<tr>
					<td colspan="12">
						<div class="noRueryResult">请选择条件查询数据！</div>
					</td>
				</tr>
			</s:if>
			<s:elseif test="claimUserHistoryVOList == null || claimUserHistoryVOList.size()==0">
				<tr>
					<td colspan="12">
						<div class="noRueryResult">没有符合条件的查询结果！</div>
					</td>
				</tr>
			</s:elseif>
		<!-- 循环显示数据 -->
		<s:iterator value="claimUserHistoryVOList" status="st">
			<tr>
				<td><s:property	value="realName" /></td>
				<td><s:property value="userName" /></td>
				<td><s:property value="unitName" /></td>
				<td><s:date name="modifyTime" format="yyyy-MM-dd HH:mm:ss" /></td>
				<td><s:property value="policyCode" /></td>
				<td><s:property value="clientName" /></td>
				<td><s:property value="modifyContent" /></td>
			</tr> 
		</s:iterator>
</tbody>
</table>