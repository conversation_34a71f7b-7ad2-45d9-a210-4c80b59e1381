<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript">
function exitDialogOCR(){
	$.pdialog.closeCurrent();
}
</script>
<div class="main_tabdiv">
	 <div  id="registerShowId">
         <div class="panelPageFormContent">
             <div class="divfclass">
	               <h1> <img src="clm/images/tubiao.png">受益人信息 </h1>
		     </div>
				<dl>
					<dt>受益人姓名</dt>
					<dd>
					   <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneName" id="beneName" value="${claimBeneMobileVO.mobileBeneName }" onblur="getBeneInfoRegi();"/>
					</dd>
				</dl>
				<dl>
					<dt>受益人性别</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneSex" value=""/> -->
					   <Field:codeTable disabled="true" cssClass="combox title"  name="claimBeneMobileVO.mobileBeneSex" id="" value="${claimBeneMobileVO.mobileBeneSex}" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2)" nullOption="true"/>
					</dd>
				</dl>
				<dl>
					<dt>受益人出生日期</dt>
					<dd>
					   <input type="text" type="expandDateYMD"  disabled="disabled" name="claimBeneMobileVO.mobileBeneBirth" value="<s:date name="claimBeneMobileVO.mobileBeneBirth" format="yyyy-MM-dd"/>"/>
					</dd>
				</dl>
				<dl>
					<dt>受益人证件类型</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneCertiType" value=""/> -->
					    <Field:codeTable disabled="true" cssClass="combox title"  name="claimBeneMobileVO.mobileBeneCertiType" id="" value="${claimBeneMobileVO.mobileBeneCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  nullOption="true"
					    	whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)" />
					</dd>
				</dl>
				<dl>
					<dt>受益人证件号码</dt>
					<dd>
					   <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneCertiNo" id="" value="${claimBeneMobileVO.mobileBeneCertiNo }"/>
					</dd>
				</dl>
				<dl>
					<dt>受益人国籍</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneNation" value=""/> -->
					   <Field:codeTable disabled="true" cssClass="combox title"  name="claimBeneMobileVO.mobileBeneNation" id="" value="${claimBeneMobileVO.mobileBeneNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" whereClause="1=1" defaultValue="CHN" orderBy="decode(country_code,'CHN','A',country_code)"/>
					</dd>
				</dl>
				<dl>
					<dt>证件有效起期</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneCertiStart" value=""/> -->
					    <input disabled="disabled" onPropertyChange="getBeneInfoRegi();"  type="expandDateYMD"  name="claimBeneMobileVO.mobileBeneCertiStart"  id="" value="<s:date name="claimBeneMobileVO.mobileBeneCertiStart" format="yyyy-MM-dd"/>"/>
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimBeneMobileVO.mobileBeneCertiEnd" value=""/> -->
					   <input type="expandDateYMD" disabled="disabled" name="claimBeneMobileVO.mobileBeneCertiEnd" onPropertyChange="getBeneInfoRegi();" id="" value="<s:date name="claimBeneMobileVO.mobileBeneCertiEnd" format="yyyy-MM-dd"/>"/>	
					   <s:if test="claimBeneMobileVO.isLongCertiNo == 1">
					        <input type="checkbox"  disabled="disabled" checked/>长期
					   </s:if>
					   <s:else>
					        <input type="checkbox"  disabled="disabled"/>长期
					   </s:else>
					</dd>
				</dl>			      
			      <div class="divider" style="border-style: none;"></div>
			      <div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">领款人信息
					</h1>
				</div>
			      	<dl>
					<dt>领款人与受益人关系</dt>
					<dd>
					    <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeRelation" value=""/>	 -->
					    <Field:codeTable disabled="true" name="claimPayeeMobileVO.mobilePayeeRelation" id="" value="${claimPayeeMobileVO.mobilePayeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" cssClass="notuseflagDelete combox title selectChange" 
							whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
					</dd>
				</dl>
				<dl>
					<dt>领款人姓名</dt>
					<dd>
					   <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeName" value="${claimPayeeMobileVO.mobilePayeeName }"/>
					</dd>
				</dl>
				<dl>
					<dt>领款人性别</dt>
					<dd>
					    <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeSex" value=""/> -->
					    <Field:codeTable disabled="true" cssClass="combox title"  name="claimPayeeMobileVO.mobilePayeeSex" id="" value="${claimPayeeMobileVO.mobilePayeeSex}" tableName="APP___CLM__DBUSER.T_GENDER" nullOption="true" whereClause="gender_code in(1,2)" />
					</dd>
				</dl>
				<dl>
					<dt>领款人出生日期</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeBirth" value=""/> -->
					   <input disabled="disabled" onPropertyChange="getpayeeInfoRegi();" type="expandDateYMD" name="claimPayeeMobileVO.mobilePayeeBirth" id="" value="<s:date name="claimPayeeMobileVO.mobilePayeeBirth" format="yyyy-MM-dd"/>"/>
					</dd>
				</dl>
				<dl>
					<dt>领款人证件类型</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeCertiType" value=""/> -->
					    <Field:codeTable disabled="true" cssClass="combox title"  name="claimPayeeMobileVO.mobilePayeeCertiType" id="" value="${claimPayeeMobileVO.mobilePayeeCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  nullOption="true"
					    	whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)" />
					</dd>
				</dl>
				<dl>
					<dt>领款人证件号码</dt>
					<dd>
					   <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeCertiNo" value="${claimPayeeMobileVO.mobilePayeeCertiNo }"/>
					</dd>
				</dl>
				<dl>
					<dt>领款人国籍</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeNation" value=""/> -->
					   <Field:codeTable cssClass="combox title" disabled="true" name="claimPayeeMobileVO.mobilePayeeNation" id="" value="${claimPayeeMobileVO.mobilePayeeNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" whereClause="1=1" defaultValue="CHN" orderBy="decode(country_code,'CHN','A',country_code)"/>
					</dd>
				</dl>
				<dl>
					<dt>证件有效起期</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeCertiStart" value=""/> -->
					   <input type="expandDateYMD" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeCertiStart" id="" value="<s:date name="claimPayeeMobileVO.mobilePayeeCertiStart" format="yyyy-MM-dd"/>" />
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
					   <!-- <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobilePayeeCertiEnd" value=""/> -->
					    <input disabled="disabled" type="expandDateYMD" name="claimPayeeMobileVO.mobilePayeeCertiEnd" id="" value="<s:date name="claimPayeeMobileVO.mobilePayeeCertiEnd" format="yyyy-MM-dd"/>"/>
					   <s:if test="claimPayeeMobileVO.isLongCertiNo == 1">
					        <input type="checkbox"  disabled="disabled" id="" checked/>长期
					   </s:if>
					   <s:else>
					        <input type="checkbox"  disabled="disabled" id=""/>长期
					   </s:else>
					</dd>
				</dl>
				
				 <div class="divider" style="border-style: none;"></div>
			      <div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">銀行卡详细信息
					</h1>
				</div>
                <dl>
					<dt>银行账户名</dt>
					<dd>
					   <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobileAccountName" id="" value="${claimPayeeMobileVO.mobileAccountName }"/>
					</dd>
				</dl>
				<dl>
					<dt>银行编号</dt>
					<dd>
					  <input type="text" disabled="disabled" name="claimPayeeMobileVO.mobileBankCode" id="" value="${claimPayeeMobileVO.mobileBankCode }"/>
					</dd>
				</dl>
				<dl>
					<dt>银行账号</dt>
					<dd>
					   <input type="expandDateYMD" disabled="disabled" name="claimPayeeMobileVO.mobileAccountNo" id="" value="${claimPayeeMobileVO.mobileAccountNo }"/>
					</dd>
				</dl>
        <div class="formBarButton main_bottom">
               <button class="but_gray" type="button" onclick="exitDialogOCR()">退出</button> 
        </div>
</div>
</div>
</div>