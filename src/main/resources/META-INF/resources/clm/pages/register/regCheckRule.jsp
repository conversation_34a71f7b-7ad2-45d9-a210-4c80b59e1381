<%@ page language="java" pageEncoding="UTF-8"%>
<%@include file="/udmpCommon.jsp"%>
<%-- <%@ include file="/clm/pages/common/commonJsCss.jsp" %> --%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<script type="text/javascript">

//时间格式校验
function checkTime(k){
	var regTime = /^([0-2][0-9]):([0-5][0-9]):([0-5][0-9])$/;
	var value = $(k).val();
	var results = false;
	if(!isNulOrEmpty(value)){
		if(regTime.test(value)){
			if((parseInt(RegExp.$1) < 24) && (parseInt(RegExp.$2) < 60) && (parseInt(RegExp.$3) < 60)){
				results = true;
			}
		}
		if(results == false){
			$(k).val("");
			alert("时间格式不正确，正确格式为HH:MM:SS");
		}
	}
}

function reset1(flag){
	AppendItem('ruleProductList','ruleAllProduct', true);
	AppendItem('ruleOrgList','ruleAllOrgan', true);
	AppendItem('ruleUserList','ruleAllUser', true);
	if(flag==1){
		$("#dataCheckForm1", navTab.getCurrentPanel())[0].reset();
	}else{
		$("#dataCheckForm", navTab.getCurrentPanel())[0].reset();
	}
	$("#dataCheck", navTab.getCurrentPanel()).find("input[name='claimType']").removeAttr("checked");
	$("#dataCheck", navTab.getCurrentPanel()).find("input[name='time']").removeAttr("checked");
	$("#checkRuleId", navTab.getCurrentPanel()).attr("value","");
	$("#startTime", navTab.getCurrentPanel()).attr("value","");
	$("#endTime", navTab.getCurrentPanel()).attr("value","");
	$("#timeTbody", navTab.getCurrentPanel()).empty();
}
/* function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
} */

$("#allClmType", navTab.getCurrentPanel()).change(function(){
	  var check=$(this).attr("checked");
	  if(check=="checked"){
		    $("input[name=claimType]","dl#clmType", navTab.getCurrentPanel()).each(function(){
				if (!$(this).is(':checked')) {
					$(this).attr("checked",true);
				}
			});
	  }else{
		  $("input[name=claimType]","dl#clmType", navTab.getCurrentPanel()).each(function(){
				if ($(this).is(':checked')) {
					$(this).attr("checked",false);
				}
			});
	  }
	});

function AppendItem(allMenu, menu, isAll) {
	for (j=0; j<document.getElementById(allMenu).length; j++){
		if (isAll == true || document.getElementById(allMenu).options[j].selected){
			//GET VALUE
			document.getElementById(allMenu).options[j].selected = false;
			//GET LENGTH
			DesLen = document.getElementById(menu).length;
			// NEW OPTION
			
			var flag = true;
			for(var i=0;i<document.getElementById(menu).options.length;i++) {
				//alert("document.getElementById(allMenu).options[j].value:" + document.getElementById(allMenu).options[j].value);
				//alert("document.getElementById(menu).options[i].value:" + document.getElementById(menu).options[i].value);
				//alert("i:" + i);
				if(document.getElementById(allMenu).options[j].value == document.getElementById(menu).options[i].value) {
					flag = false;
					//alert('jj');
					break;
				}
			}
			if(flag) {
				document.getElementById(menu).options[DesLen] = new Option(LTrim(document.getElementById(allMenu).options[j].text), document.getElementById(allMenu).options[j].value);
			}
			
			
			document.getElementById(allMenu).remove(j);
			j--;
		}
	}
}

function LTrim(str) {
	var whitespace = new String("　 \t\n\r");
	var s = new String(str);
	if (whitespace.indexOf(s.charAt(0)) != -1) {
		var j = 0, i = s.length;
		while (j < i && whitespace.indexOf(s.charAt(j)) != -1) {
			j++;
		}
		s = s.substring(j, i);
	}
	return s;
}
function checkEdit(delect){
	reset1();
	var ruleId = $(delect).parents("td").find("input:eq(0)").val();
	var claimType = $(delect).parents("tr").find("td:eq(1)").children().next().val();
	//var claimType2 = $("#checkBody tr", navTab.getCurrentPanel()).find("input[name='r']:checked").parents("tr").find("td:eq(1)").html();
	var product = $(delect).parents("tr").find("td:eq(2)").children().next().val();
	var organ = $(delect).parents("tr").find("td:eq(3)").children().next().val();
	var operator = $(delect).parents("tr").find("td:eq(4)").children().next().val();
	var money = $(delect).parents("tr").find("td:eq(5)").children().next().val();
	var time = $(delect).parents("tr").find("td:eq(6)").children().next().val();
	var count = $(delect).parents("tr").find("td:eq(7)").children().next().val();
    var claimTypes = claimType.split(",");
    for(var i = 0; i < claimTypes.length; i++){
		var ch = "input[name='claimType'][value='" + claimTypes[i].trim() + "']";
		$(ch).attr("checked","checked");
  	}
 	var times = time.split(",");
 	for(var i = 0; i < times.length; i++){
		var ch = "input[name='time'][value='" + times[i].trim() + "']";
		$(ch).attr("checked","checked");
  	}
 	$("#money", navTab.getCurrentPanel()).val(money);
 	$("#countId", navTab.getCurrentPanel()).val(count);
	$("#checkRuleId", navTab.getCurrentPanel()).val(ruleId);
	getProductList(product,organ,operator);
}
function getProductList(product,organ,operator){
	var ruleId=$("#checkRuleId", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/register/getList_CLM_registerCheckRuleAction.action?ruleId='+ruleId+'&product='+product+'&organ='+organ+'&user='+operator	,
		'type':'POST',
		'global':false,
		'datatype':'json',
		'success':function(data){	
			var data=eval("("+data+")");
			var orgList = data[0].org.length;
			var prodList = data[0].prod.length;
			var userList = data[0].user.length;
			var timeL = data[0].timeList.length;
			$("#ruleProductList", navTab.getCurrentPanel()).find("option").remove();
			for(var i=0;i<prodList;i++){
				$("#ruleProductList", navTab.getCurrentPanel()).append("<option class='item' value='"+data[0].prod[i].productCode+"'>"+data[0].prod[i].productName+"</option>");
			}
			$("#ruleOrgList", navTab.getCurrentPanel()).find("option").remove();
			for(var j=0;j<orgList;j++){
				$("#ruleOrgList", navTab.getCurrentPanel()).append("<option class='item' value='"+data[0].org[j].orgCode+"'>"+data[0].org[j].orgCode+'-'+data[0].org[j].orgName+"</option>");
			}
			$("#ruleUserList", navTab.getCurrentPanel()).find("option").remove();
			for(var m=0;m<userList;m++){
				$("#ruleUserList", navTab.getCurrentPanel()).append("<option class='item' value='"+data[0].user[m].userCode+"'>"+data[0].user[m].userName+"</option>");
			}
			$("#startTime", navTab.getCurrentPanel()).val(data[0].startTime);
			$("#endTime", navTab.getCurrentPanel()).val(data[0].endTime);
			$("#timeTbody", navTab.getCurrentPanel()).find("tr").remove();
			for(var n=0;n<timeL;n++){
				var times = data[0].timeList[n].paraValue;
				var time1 = times.split(",");
				var time2 = "";
				if(times=="01,02,03,04,05"){
					time2="工作日";
				}else if(times=="06,07"){
					time2="周末";
				}else{
					for(var k=0;k<time1.length;k++){
						if(time1[k]=="01"){
							time2 = time2+"周一";
						}else if(time1[k]=="02"){
							time2 = time2+"周二";
						}else if(time1[k]=="03"){
							time2 = time2+"周三";
						}else if(time1[k]=="04"){
							time2 = time2+"周四";
						}else if(time1[k]=="05"){
							time2 = time2+"周五";
						}else if(time1[k]=="06"){
							time2 = time2+"周六";
						}else if(time1[k]=="07"){
							time2 = time2+"周日";
						}
					}
				}
				var insertHtml ="<tr>"
								+"<td>"+"<input type='hidden' name='listId' id='listId' value='"+data[0].timeList[n].listId+"'>"
								+time2+"<input type='hidden' name='claimTimes.paraValue' value='"+times+"'>"+"</td>"
								+"<td>"+data[0].timeList[n].subParaStart+"<input type='hidden' name='claimTimes.subParaStart' value='"+data[0].timeList[n].subParaStart+"'>"+"</td>"
								+"<td>"+data[0].timeList[n].subParaEnd+"<input type='hidden' name='claimTimes.subParaEnd' value='"+data[0].timeList[n].subParaEnd+"'>"+"</td>"
								+"<td><a title='删除' class='btnDel' id='delButton1' href='javascript:void(0);' onclick='delTime(this);'>删除</a></td>"
								+"</tr>";
				$("#timeTbody", navTab.getCurrentPanel()).append(
										insertHtml);						
			
			}
			claimTimeSort();
		}
	});
}
function save1(){
	if($("#countId", navTab.getCurrentPanel()).val() == "0"){
		alert("复核间隔不能为0！从1开始");
		return false;
	}
	if($("#isBlack", navTab.getCurrentPanel()).attr("checked")=="checked"){
		if($("#countId", navTab.getCurrentPanel()).val()==""){
			alert("复核间隔不能为空！");
			return false;
		}else if($("#countId", navTab.getCurrentPanel()).val()<=0){
			alert("复核间隔不能为负数！");
			return false;
		}
	}
	//险种
	var product = document.getElementById("ruleProductList");
	var productLength = document.getElementById("ruleProductList").length;
	for(var i = 0;i<productLength;i++){
		product.options[i].selected=true;
	}
	//机构
	var org = document.getElementById("ruleOrgList");
	var orgLength = document.getElementById("ruleOrgList").length;
	for(var j = 0;j<orgLength;j++){
		org.options[j].selected=true;
	}
	//操作人员
	var user = document.getElementById("ruleUserList");
	var userLength = document.getElementById("ruleUserList").length;
	for(var m = 0;m<userLength;m++){
		user.options[m].selected = true;
	}
	//理赔类型
	 var ische = 0;
	 $('input[name="claimType"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') == false){
			ische = ische + 1;
		}
	 });
	 //金额
	 var money = $("#money", navTab.getCurrentPanel()).val();
     //时间段
	 var istime = 0;
	 $('input[name="time"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') == false){
			istime = istime + 1;
		}
	 });
	 
	 var timeTrList = $("#timeTbody", navTab.getCurrentPanel()).find("tr");

	 if(timeTrList.length==0){
		 alertMsg.info("请添加时间段！");
		 return false;
	 }
	 //开始时间
	 var startTime = $("#startTime", navTab.getCurrentPanel()).val();
	 //结束时间
	 var endTime = $("#endTime", navTab.getCurrentPanel()).val();
	 //案件数
	 var countId = $("#countId", navTab.getCurrentPanel()).val();
	 //保存时至少填一项
	 if (ische == 9 && productLength == 0 && orgLength == 0 && userLength == 0 && istime == 7 && money == '' && startTime == '' 
			 && endTime == '' && countId == '') {
		 alertMsg.info("录入内容为空，请检查！");
		 return false;
	 }
	
	$("#dataCheckForm",navTab.getCurrentPanel()).submit();
}
function delRule(delect,ruleId) {
	if(window.confirm("确认删除吗?")){
		$.ajax({
	  			type:'post',
	  			url:'clm/register/delRuleData_CLM_registerCheckRuleAction.action?ruleId='+ruleId,
	  			datatype:'json',
	  			global : false,
	  			success:function(data){
	  				var json = eval("(" + data + ")");
	  				if(undefined != json.message){
						alertMsg.info(json.message);
					}
	  				$(delect).parent().parent().remove();
	  			},
	  			error:function(){
	  				alert("出错了！");
	  			}
	  	});  
	}
}

function save2(){
		var times="";
		$("input[name='time']:checked", navTab.getCurrentPanel()).each(function(index){
			times = times+$(this).attr("value")+",";
		})
		if(times==""){
			alertMsg.error("请勾选重复时间段");
		}
		times = times.substring(0,times.length-1);
		var time1 = times.split(",");
		var startTime = $("#startTime", navTab.getCurrentPanel()).attr("value");
		var endTime = $("#endTime", navTab.getCurrentPanel()).attr("value");
		//查看是否重复，只要时间段交叉就算重复
		var timeTrList = $("#timeTbody", navTab.getCurrentPanel()).find("tr");
		
		
		for(var i=0;i<time1.length;i++){
			for(var j=0;j<timeTrList.length;j++){
					var timeObectList = $(timeTrList[j]).find("input[type=hidden]");
					if($(timeObectList).eq(0).attr("name")!="listId"){
						 if($(timeObectList).eq(0).attr("value").indexOf(time1[i])){
								if((startTime<$(timeObectList).eq(2).attr("value")&&startTime>$(timeObectList).eq(1).attr("value"))
										||(endTime>$(timeObectList).eq(2).attr("value")&&endTime<$(timeObectList).eq(1).attr("value"))){
									alertMsg.error("你所填写的重复时间段与已有记录重复，请检查。");
									return false;
								}else if(startTime<=$(timeObectList).eq(1).attr("value")&&endTime>=$(timeObectList).eq(2).attr("value")){
									alertMsg.error("你所填写的重复时间段与已有记录重复，请检查。");
									return false;
								}
						 }
					}else{
						if($(timeObectList).eq(0).attr("value").indexOf(time1[i])){
							if((startTime<$(timeObectList).eq(3).attr("value")&&startTime>$(timeObectList).eq(2).attr("value"))
									||(endTime>$(timeObectList).eq(3).attr("value")&&endTime<$(timeObectList).eq(2).attr("value"))){
								alertMsg.error("你所填写的重复时间段与已有记录重复，请检查。");
								return false;
							}else if(startTime<=$(timeObectList).eq(2).attr("value")&&endTime>=$(timeObectList).eq(3).attr("value")){
								alertMsg.error("你所填写的重复时间段与已有记录重复，请检查。");
								return false;
							}
					 }
				}
			}
		}
		
	
		var ruleId = $("#checkRuleId", navTab.getCurrentPanel()).val();
		if(ruleId==null||ruleId==""){
				var time2 = "";
				if(times=="01,02,03,04,05"){
					time2="工作日";
				}else if(times=="06,07"){
					time2="周末";
				}else{
					for(var k=0;k<time1.length;k++){
						if(time1[k]=="01"){
							time2 = time2+"周一";
						}else if(time1[k]=="02"){
							time2 = time2+"周二";
						}else if(time1[k]=="03"){
							time2 = time2+"周三";
						}else if(time1[k]=="04"){
							time2 = time2+"周四";
						}else if(time1[k]=="05"){
							time2 = time2+"周五";
						}else if(time1[k]=="06"){
							time2 = time2+"周六";
						}else if(time1[k]=="07"){
							time2 = time2+"周日";
						}
					}
				}
				var insertHtml ="<tr>"
						+"<td>"+time2+"<input type='hidden' name='claimTimes.paraValue' value='"+times+"'>"+"</td>"
						+"<td>"+startTime+"<input type='hidden' name='claimTimes.subParaStart'  value='"+startTime+"'>"+"</td>"
						+"<td>"+endTime+"<input type='hidden' name='claimTimes.subParaEnd'  value='"+endTime+"'>"+"</td>"
						+"<td><a title='删除' class='btnDel' href='javascript:void(0);' onclick='delTime(this);'>删除</a></td>"
					+"</tr>";
					$("#timeTbody", navTab.getCurrentPanel()).append(insertHtml);				
		}else{
			$.ajax({
				type:'post',
				url:'clm/register/saveTime_CLM_registerCheckRuleAction.action',
				datatype:'json',
				global : false,
				data:$("#dataCheckForm", navTab.getCurrentPanel()).serialize(),
				success:function(data){
					var data = eval("(" + data + ")");
					if(data[0].message1!=""){
						alertMsg.info(data[0].message1);
					}
					var timeL = data[0].timeList.length;
					
					$("#timeTbody", navTab.getCurrentPanel()).find("tr").remove();
					for(var n=0;n<timeL;n++){
						times = data[0].timeList[n].paraValue;
						time1 = times.split(",");
						var time2 = "";
						if(times=="01,02,03,04,05"){
							time2="工作日";
						}else if(times=="06,07"){
							time2="周末";
						}else{
							for(var k=0;k<time1.length;k++){
								if(time1[k]=="01"){
									time2 = time2+"周一";
								}else if(time1[k]=="02"){
									time2 = time2+"周二";
								}else if(time1[k]=="03"){
									time2 = time2+"周三";
								}else if(time1[k]=="04"){
									time2 = time2+"周四";
								}else if(time1[k]=="05"){
									time2 = time2+"周五";
								}else if(time1[k]=="06"){
									time2 = time2+"周六";
								}else if(time1[k]=="07"){
									time2 = time2+"周日";
								}
							}
						}
						var insertHtml ="<tr>"
							+"<td>"+"<input type='hidden' name='listId' id='listId' value='"+data[0].timeList[n].listId+"'>"
							+time2+"<input type='hidden' name='claimTimes.paraValue' value='"+times+"'>"+"</td>"
							+"<td>"+data[0].timeList[n].subParaStart+"<input type='hidden' name='claimTimes.subParaStart' value='"+data[0].timeList[n].subParaStart+"'>"+"</td>"
							+"<td>"+data[0].timeList[n].subParaEnd+"<input type='hidden' name='claimTimes.subParaEnd' value='"+data[0].timeList[n].subParaEnd+"'>"+"</td>"
							+"<td><a title='删除' class='btnDel' id='delButton1' href='javascript:void(0);' onclick='delTime(this);'>删除</a></td>"
							+"</tr>";
						$("#timeTbody", navTab.getCurrentPanel()).append(
												insertHtml);			
					}
				},
				error:function(){
					alert("出错了！");
				}
		});  
		}
	
		
				/* for(var n=0;n<timeL;n++){
					var times = data[0].timeList[n].paraValue;
					var time1 = times.split(",");
					var time2 = "";
					if(times=="01,02,03,04,05"){
						time2="工作日";
					}else if(times=="06,07"){
						time2="周末";
					}else{
						for(var k=0;k<time1.length;k++){
							if(time1[k]=="01"){
								time2 = time2+"周一";
							}else if(time1[k]=="02"){
								time2 = time2+"周二";
							}else if(time1[k]=="03"){
								time2 = time2+"周三";
							}else if(time1[k]=="04"){
								time2 = time2+"周四";
							}else if(time1[k]=="05"){
								time2 = time2+"周五";
							}else if(time1[k]=="06"){
								time2 = time2+"周六";
							}else if(time1[k]=="07"){
								time2 = time2+"周日";
							}
						}
					} */
				/* 	var insertHtml ="<tr>"
									+"<td>"+"<input type='hidden' name='listId' id='listId' value='"+data[0].timeList[n].listId+"'>"
									+time2+"</td>"
									+"<td>"+data[0].timeList[n].subParaStart+"</td>"
									+"<td>"+data[0].timeList[n].subParaEnd+"</td>"
									+"<td><a title='删除' class='btnDel' id='delButton1' href='javascript:void(0);' onclick='delTime(this);'>删除</a></td>"
									+"</tr>";
					$("#timeTbody", navTab.getCurrentPanel()).append(
											insertHtml);						
				
				} */
				claimTimeSort();
}
//对于时间段列表name排序
function claimTimeSort(){
	var trList = $("#timeTbody", navTab.getCurrentPanel()).find("tr");
	$(trList).each(function(index){
		 var timeObectList = $(this).find("input[type=hidden]");
		if($(timeObectList).eq(0).attr("name")!="listId"){
			 $(timeObectList).eq(0).attr("name","claimTimes["+index+"].paraValue");
			 $(timeObectList).eq(1).attr("name","claimTimes["+index+"].subParaStart");
			 $(timeObectList).eq(2).attr("name","claimTimes["+index+"].subParaEnd");
		}else{
			 $(timeObectList).eq(1).attr("name","claimTimes["+index+"].paraValue");
			 $(timeObectList).eq(2).attr("name","claimTimes["+index+"].subParaStart");
			 $(timeObectList).eq(3).attr("name","claimTimes["+index+"].subParaEnd");
		}
	})
}

function delTime(delect){
	var listId = $(delect).parent().parent().find("td:eq(0)").find("input[name=listId]").val();
	
	var ruleId = $("#checkRuleId", navTab.getCurrentPanel()).val();
	if(window.confirm("确认删除吗?")){
		if(listId!=null && listId!=""){
				$.ajax({
			  			type:'post',
			  			url:'clm/register/delTime_CLM_registerCheckRuleAction.action?ruleId='+ruleId+'&listId='+listId,
			  			datatype:'json',
			  			global : false,
			  			success:function(data){
			  				var json = eval("(" + data + ")");
			  				if(undefined != json.message){
								alertMsg.info(json.message);
							}
			  				$(delect).parent().parent().remove();
			  			},
			  			error:function(){
			  				alert("出错了！");
			  			}
			  	});  
		}else{
			  $(delect).parent().parent().remove();
			  alertMsg.info("操作成功");
		}
	}
}

//定义险种集合
var _businessArray = new Array();

//定义机构集合
var _organArray = new Array();

//定义操作人员集合
var _userArray = new Array();


//险种快速查询
var val_flag = "";  //定义一个标识，防止多次重复验证
$("#businessSearchId", navTab.getCurrentPanel()).bind("input propertychange",function(){
	var value = $("#businessSearchId", navTab.getCurrentPanel()).val();
	if(value != val_flag){
		val_flag = value;
		var optionStr = "";
		for(var i=0;i<_businessArray.length;i++){
			var obj = _businessArray[i];
			var text = obj.productNameSys;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='"+ obj.businessPrdId +"'>"+  obj.productNameSys + "</option>";
			}
		}
		$("#ruleAllProduct", navTab.getCurrentPanel()).html("");
		$("#ruleAllProduct", navTab.getCurrentPanel()).append(optionStr);
	}
});

//机构快速查询
var organ_flag = "";  //定义一个标识，防止多次重复验证
$("#organSearchId", navTab.getCurrentPanel()).bind("input propertychange", function(){
	var value = $("#organSearchId", navTab.getCurrentPanel()).val();
	if(value != organ_flag){
		organ_flag = value;
		var optionStr = "";
		for(var i=0;i<_organArray.length;i++){
			var obj = _organArray[i];
			var text = obj.organCode + "-" +  obj.organName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.organCode + "'>" + obj.organCode + "-" + obj.organName + "</option>";
			};
		};
		$("#ruleAllOrgan", navTab.getCurrentPanel()).html("");
		$("#ruleAllOrgan", navTab.getCurrentPanel()).append(optionStr);
	};
});

//操作人员快速查询
var user_flag = "";  //定义一个标识，防止多次重复验证
$("#userSearchId", navTab.getCurrentPanel()).bind("input propertychange", function(){
	var value = $("#userSearchId", navTab.getCurrentPanel()).val();
	if(value != user_flag){
		user_flag = value;
		var optionStr = "";
		for(var i=0;i<_userArray.length;i++){
			var obj = _userArray[i];
			var text = obj.userName;
			if(text.indexOf(value) != "-1"){
				optionStr = optionStr + "<option value='" + obj.userId + "'>" + obj.userName + "</option>";
			};
		};
		$("#ruleAllUser", navTab.getCurrentPanel()).html("");
		$("#ruleAllUser", navTab.getCurrentPanel()).append(optionStr);
	};
});


</script>

<s:iterator value="businessProductListVO" var="bus" >
	<script>
		var obj = new Object();
		obj.businessPrdId = '<s:property value="#bus.businessPrdId"/>';
		obj.productNameSys = '<s:property value="#bus.productNameSys"/>';
		_businessArray.push(obj);
	</script>
</s:iterator>

<s:iterator value="orgVOList" var="org">
	<script>
		var obj = new Object();
		obj.organCode = '<s:property value="#org.organCode"/>';
		obj.organName = '<s:property value="#org.organName"/>';
		_organArray.push(obj);
	</script>
</s:iterator>

<s:iterator value="userVOList" var="usr">
	<script>
		var obj = new Object();
		obj.userId = '<s:property value="#usr.userId"/>';
		obj.userName = '<s:property value="#usr.userName"/>';
		obj.realName = '<s:property value="#usr.realName"/>';
		_userArray.push(obj);
	</script>
</s:iterator>




<div layoutH="0" >
<div class="main_bottom">
<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">定义数据复核机制
					</h1>
				</div>
<div class="pageContent">
<div>
<form id="dataCheckForm1" name="dataCheckForm1" action="clm/register/saveDataCheck_CLM_registerCheckRuleAction.action" method="post" 
			class="dataCheckForm required-validate" onsubmit="return validateCallback(this, navTabAjaxDone)">
			
<div class="divider" style="border-style: none;"></div>
			<div class="tabdivclassbr" id="claimBene">
				<table id="claimBeneTable" class="list" style="width: 100%;" >
					<thead>
						<tr align="center">
						    <th nowrap>选择</th>
							<th nowrap>理赔类型</th>
							<th nowrap>险种</th>
							<th nowrap>机构</th>
							<th nowrap>操作人员</th>
							<th nowrap>赔付金额</th>
							<th nowrap>时间段</th>
							<th nowrap>抽取方式</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="checkRuleTbody">
						<s:iterator value="ruleDataVOList" status="st">
							<tr align="center">
								 <td>
								    <input type="hidden" name="ruleId" id="ruleId" value="${ruleId }">
								 	<input type="radio" name="r" onclick="checkEdit(this);">
								 </td>
								 <td ><span>${claimType1}</span><input type="hidden" value="${claimType}" id="claimType"></td>
								 <td  style="word-break:break-all;"><span>${product1}</span><input type="hidden" value="${product}" id="product"></td>
								 <td  style="word-break:break-all;"><span>${organ1}</span><input type="hidden" value="${organ}" id="organ"></td>
								 <td  style="word-break:break-all;"><span>${operator1}</span><input type="hidden" value="${operator}" id="operator"></td>
								 <td ><span>${money}</span><input type="hidden" value="${money}" id="product"></td>
								 <td ><span>${time1}</span><input type="hidden" value="${time}" id="time"></td>
								 <td ><span>${count}</span><input type="hidden" value="${count}" id="count"></td>
								 <td ><a class="delete btnDel" id='delButton' href='javascript:void(0);' onclick="delRule(this,'${ruleId}');">删除</a></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>			
			</div>
			</form>
			<div class="pageFormdiv">
				<button class="but_blue" type="button" onclick="reset1(1)">添加</button>
			</div>
			<div class="divider" style="border-style: none;"></div>
			<form id="dataCheckForm" name="dataCheckForm" action="clm/register/saveDataCheck_CLM_registerCheckRuleAction.action" method="post" 
			class="dataCheckForm required-validate" onsubmit="return validateCallback(this, navTabAjaxDone)">
			 <input type="hidden" name="checkRuleId" id="checkRuleId">
			<div id="dataCheck">
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">理赔类型
						</h1>
					</div>
				<dl id="clmType">
					<dt ></dt>
					<dd style="padding-left:35px;" >
						<span>
					   		<input type="checkbox" name="claimType" id="claimType1" value="01"/>身故
					   </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType2" value="04"/>高残
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType3" value="03"/>重疾
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType4" value="02"/>伤残
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType6" value="11"/>豁免
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType5" value="08"/>医疗
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType7" value="10"/>特种疾病
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType8" value="06"/>一般失能
					    </span>
					   <span>
					   		<input type="checkbox" name="claimType" id="claimType9" value="07"/>重度失能
					    </span>
					    <span>
					   		<input type="checkbox" id="allClmType" value="0"/>全选
					    </span>
					    	</dd>
				</dl>
				
			   <!-- 险种begin -->
		<div>
				<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">险种
						</h1>
					</div>
			<div id="productForm" class="main_text">
				<table width="98%">
					<tr>
						<td><span  style="float:left;line-height:1.6;">险种快速查询：</span><input type="text" id="businessSearchId"  style="float:left;" /></td>
						<td></td>
						<td></td>
					</tr>
					<tr height="30px;">
						<td>
							<dl class='nowrap'>
								<dt>险种</dt>
							</dl>
						</td>
						<td>
						</td>
						<td>
							<dl class='nowrap'>
								<dt>参与复核的险种</dt>
							</dl>
						</td>
					</tr>
					<tr>
						<td width="40%">
							<div >
								<select id="ruleAllProduct" name="allProduct" multiple="multiple"
								style="height:120px; width:100%;" size=5
								ondblclick="return AppendItem('ruleAllProduct', 'ruleProductList', false);">
									<s:iterator value="businessProductListVO"  status="st" id="businessProduct">
										<option value="${businessProduct.businessPrdId}" >${businessProduct.productNameSys}</option> 
									</s:iterator>		
								
								</select>
							</div>
						</td>
						<td align="center" width="8%">
<!-- 							<div style="margin: 0 30px 0 30px; float: left;"> -->
<!-- 								<div class="buttonActive"> -->
									<div class="buttonContent">
										<button class="but_gray" id="toRightP"
											onclick="return AppendItem('ruleAllProduct', 'ruleProductList', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;">></button>
									</div>
<!-- 								</div> -->
								<div style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToRightP"
											onclick="return AppendItem('ruleAllProduct','ruleProductList', true);"
											type="button">>></button>
									</div>
								</div>
								<div style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id="toleftP"
											onclick="return AppendItem('ruleProductList', 'ruleAllProduct', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
									</div>
								</div>
								<div  style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToLeftP"
											onclick="return AppendItem('ruleProductList','ruleAllProduct', true);"
											type="button"><<</button>
									</div>
								</div>
<!-- 							</div> -->
						</td>
						<td width="40%">
							<div >
								<select id="ruleProductList" name="productList"
										multiple="multiple" style="height:120px; width:100%;" size=5
										ondblclick="return AppendItem('ruleProductList','ruleAllProduct',  false);">
								</select>
							</div>
						</td>
					</tr>
				</table>
			</div>
		</div>		
<!-- 险种end -->
<!-- 机构begin -->
		<div >
				<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">机构
						</h1>
					</div>
			<div id="orgForm" class="main_text">
				<table style="width: 98%;">
					<tr>
						<td><span  style="float:left;line-height:1.6;">机构快速查询：</span><input type="text" id="organSearchId" style="float:left;" /></td>
						<td></td>
						<td></td>
					</tr>
					<tr height="30px;">
						<td>
							<dl class='nowrap'>
								<dt>机构</dt>
							</dl>
						</td>
						<td>
						</td>
						<td>
							<dl class='nowrap'>
								<dt>参与复核的机构</dt>
							</dl>
						</td>
					</tr>
					<tr>
						<td width="40%">
							<div >
								<select id="ruleAllOrgan" name="allOrgan" multiple="multiple"
								style="width: 100%;height:120px" size=5
								ondblclick="return AppendItem('ruleAllOrgan', 'ruleOrgList', false);">
										<s:iterator value="orgVOList"  status="st" id="org">
											 <option value="${org.organCode}" >${org.organCode}-${org.organName}</option> 
										</s:iterator>		
								</select>
							</div>
						</td>
						<td align="center" width="8%">
<!-- 							<div style="margin: 0 30px 0 30px; float: left;"> -->
<!-- 								<div class="buttonActive"> -->
									<div class="buttonContent">
										<button class="but_gray" id="toRightO"
											onclick="return AppendItem('ruleAllOrgan', 'ruleOrgList', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;">></button>
									</div>
<!-- 								</div> -->
								<div   style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToRightO"
											onclick="return AppendItem('ruleAllOrgan','ruleOrgList', true);"
											type="button">>></button>
									</div>
								</div>
								<div   style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id="toleftO"
											onclick="return AppendItem('ruleOrgList', 'ruleAllOrgan', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
									</div>
								</div>
								<div   style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToLeftO"
											onclick="return AppendItem('ruleOrgList','ruleAllOrgan', true);"
											type="button"><<</button>
									</div>
								</div>
<!-- 							</div> -->
						</td>
						<td width="40%">
							<div >
								<select id="ruleOrgList" name="orgList"
										multiple="multiple" style="width: 100%;height:120px" size=5
										ondblclick="return AppendItem('ruleOrgList','ruleAllOrgan',  false);">
								</select>
							</div>
						</td>
					</tr>
				</table>
			</div>
			
		</div>		
<!-- 保单机构end -->
<!-- 操作人员begin -->
		<div >
				<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">操作人员
						</h1>
					</div>
			<div id="userForm" class="main_text">
				<table style="width: 98%">
					<tr>
						<td><span  style="float:left;line-height:1.6;">操作人员快速查询：</span><input type="text" id="userSearchId" style="float:left;" /></td>
						<td></td>
						<td></td>
					</tr>
					<tr height="30px">
						<td>
							<dl class='nowrap'>
								<dt>操作人员</dt>
							</dl>
						</td>
						<td>
						</td>
						<td>
							<dl class='nowrap'>
								<dt>参与复核的操作人员</dt>
							</dl>
						</td>
					</tr>
					<tr>
						<td width="40%">
							<div >
								<select id="ruleAllUser" name="allUser" multiple="multiple"
								style="width: 100%;height:120px" size=5
								ondblclick="return AppendItem('ruleAllUser', 'ruleUserList', false);">
									<s:iterator value="userVOList"  status="st" id="user">
										<option value="${user.userId}" >${user.userName}</option> 
									</s:iterator>		
								
								</select>
							</div>
						</td>
						<td align="center" width="8%">
<!-- 							<div style="margin: 0 30px 0 30px; float: left;"> -->
<!-- 								<div class="buttonActive"> -->
									<div class="buttonContent">
										<button class="but_gray" id="toRightP"
											onclick="return AppendItem('ruleAllUser', 'ruleUserList', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;">></button>
									</div>
<!-- 								</div> -->
								<div   style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToRightP"
											onclick="return AppendItem('ruleAllUser','ruleUserList', true);"
											type="button">>></button>
									</div>
								</div>
								<div   style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id="toleftP"
											onclick="return AppendItem('ruleUserList', 'ruleAllUser', false);"
											type="button" style="padding-left: 14px; padding-right: 14px;"><</button>
									</div>
								</div>
								<div   style="clear: left; margin: 5px 0px 0px;">
									<div class="buttonContent">
										<button class="but_gray" id = "allToLeftP"
											onclick="return AppendItem('ruleUserList','ruleAllUser', true);"
											type="button"><<</button>
									</div>
								</div>
<!-- 							</div> -->
						</td>
						<td width="40%">
							<div >
								<select id="ruleUserList" name="userList"
										multiple="multiple" style="width: 100%;height:120px" size=5
										ondblclick="return AppendItem('ruleUserList','ruleAllUser',  false);">
								</select>
							</div>
						</td>
					</tr>
				</table>
			</div>
		</div>		
<!-- 操作人员end -->
			    <div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">赔付金额
						</h1>
					</div>
			    	<table class="main_text">
			    		<tr>
			    			<td><span>赔付金额大于</span></td>
			    			<td><input type="text" name="money" id="money" onkeyup="this.value=this.value.replace(/\D/g,'')"/><span>的赔案，需要进行复核。</span></td>
			    		</tr>
			    	</table>
			   <div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">时间段
						</h1>
					</div>
				<div class="panelPageFormContent">
			   <dl >
					<dt  >重复</dt>
					<dd style="width: 800px">
						<span>
					   <input type="checkbox" name="time" id="time1" value="01"/>周一
					   </span>
					   <span>
					   <input type="checkbox" name="time" id="time2" value="02"/>周二
					   </span>
					   <span>
					   <input type="checkbox" name="time" id="time3" value="03"/>周三
					   </span>
					   <span>
					   <input type="checkbox" name="time" id="time4" value="04"/>周四
					   </span>
					   <span>
					   <input type="checkbox" name="time" id="time5" value="05"/>周五
					   </span>
					   <span>
					   <input type="checkbox" name="time" id="time6" value="06"/>周六
					   </span>
					   <span>
					   <input type="checkbox" name="time" id="time7" value="07"/>周日
					   </span>
					</dd>
				</dl>
				 <dl >
					<dt >开始时间</dt>
					<dd >
					   <input type="text" onblur="checkTime(this)" datefmt="HH:mm:ss"   name="startTime" id="startTime" class="textInput date"/>
					</dd>
				</dl>
				<dl >
					<dt >结束时间</dt>
					<dd >
					   <input type="text" onblur="checkTime(this)" datefmt="HH:mm:ss"  name="endTime" id="endTime"   class="textInput date"/>
					</dd>
				</dl>
			</div>			
			<div class="pageFormdiv">
				<button class="but_blue" type="button" id="saveTime"  onclick="save2();">添加</button>
			</div>
				<div class="tabdivclass main_tabdiv">
					<table id="timeTable" class="list" style="width: 100%;" >
						<thead>
							<tr align="center">
							    <th>重复</th>
								<th>开始时间</th>
								<th>结束时间</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody id="timeTbody">
							<s:iterator value="checkRuleVOList" status="st">
								<tr align="center">
									 <td>${paraValue }<input type='hidden' name='claimTimes[#st.index].paraValue' value='${paraValue }'></td>
									 <td>${subParaStart }<input type='hidden' name='claimTimes[#st.index].subParaStart' value='${subParaStart }'></td>
									 <td>${subParaEnd }<input type='hidden' name='claimTimes[#st.index].subParaEnd' value='${subParaEnd }'></td>
									 <td><a class="delete btnDel" id='delButton' href='javascript:void(0);' onclick="delRule(this,'${ruleId}');">删除</a></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>			
				</div>
			   
			 		<table class="main_text main_tabdiv">  
			   			<tr>
							<td><input type = "radio" name="ClaimSurveyRuleVO.isBlack" value="" id="isBlack"><span>每</span></td>
							<td><input type="expandNumber" afterpoint='0' name="count" min="0" id="countId"><span>个案件复核一个</span></td>
						</tr>
			   		</table>
			  
			      <div class="formBarButton">
			      	<ul>
						<li>
							<button class="but_blue" type="button" id="saveRule"  onclick="save1()">保存</button>
						</li>
						<li>
							<button class="but_gray" type="button" id="exitbtn"  onclick="exit()">退出</button>
						</li>
					 </ul>
				 </div>
			</div>
</form>
</div>
</div>
</div>
</div>