﻿<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/pages/register/easyDutyInfInput.js">
</script>

<script src="clm/pages/register/copyDwzDataBase.js"></script>
<script type="text/javascript">
 
 
if(document.readyState=="complete"){  
	var currentPageId= <%=session.getAttribute("currentPageId") %>;  
	 if(currentPageId!="" && currentPageId>3 ){
		 //只置灰保存按钮 
	}
} 

//用于伸缩按钮的伸缩功能
// $(document).ready(function(){
	  
   $(".main_heading", navTab.getCurrentPanel()).off().toggle(function(){  
     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
   },function(){
     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
    	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
   });

// });

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]", navTab.getCurrentPanel()).bind("click",function(){
// 		var id=$(this).attr("id").split("main_")[1];
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
})


function checkData(obj){
// 如果手动输入的纯数字则置空	
	var reg= new RegExp("^[0-9]*$");
	if(reg.test($(obj).attr("value")) ){
		$(obj).attr("value","");
	} ;
}

function checkBirthDay(obj){
	if(this.val() > workDate){
		alert()
	}
}
var readOnly="";
var isApprove = '';
var initFlag = "";
$(function() {
	initFlag = ${initFlag};
	if (initFlag == "1") {
		$("#dutyUpId", navTab.getCurrentPanel()).parent().attr("style", "display:none"); 
		$("#dutyNextId", navTab.getCurrentPanel()).parent().attr("style", "display:none");
	}

});
//======================================初始化加载，查看时，自动显示勾选过的项目和录入过的数据====================================================//
var jsonShowMap = '${jsonShowMap}'; //判断页面展示数据使用
var caseId = $("#caseId", navTab.getCurrentPanel()).attr("value");
var isMigration = '${claimCaseVO.isMigration}';
var isHospitalFlag = false;
//职业给付系数  年龄误告处理  医疗单证录入 特种费用  社保第三方给付  伤残 //特定手术/疾病/给付录入信息  手术分级给付
if(jsonShowMap.length > 2){
	var readOnly =$("#readOnly", navTab.getCurrentPanel()).val();
	jsonShowMap = jsonShowMap.substring(1,jsonShowMap.length - 1);
	for (var j = 0; j < jsonShowMap.split(",").length; j++) {
	    var show =jsonShowMap.split(",")[j];
	    var checkboxid = "",checkboxname = "";
	    checkboxid = show.split("=")[0].trim();
	    checkboxname = show.split("=")[1].trim();
	    //职业给付系数
		if(checkboxid == "claimOccupationRate" && checkboxname == "show"){
			
			$("input[hiddenDivId='registerJobCateNews']", navTab.getCurrentPanel()).attr("checked",true);
			$("#registerJobCateNews", navTab.getCurrentPanel()).show("3000");
			claimFireEvent($("#registerJobIdFire", navTab.getCurrentPanel())); 
		}
		//年龄误告处理
		if(checkboxid == "claimAdjustAge" && checkboxname == "show"){
			$("input[hiddenDivId='registerAgeNews']", navTab.getCurrentPanel()).attr("checked",true);
			$("#registerAgeNews", navTab.getCurrentPanel()).show("3000");
		}
	    //医疗单证录入 
		if(checkboxid == "claimBill" && checkboxname == "show"){
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerHonsp", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerHonspcheckbox()',800);
		}
	    //伤残录入信息
		if(checkboxid=="claimInjury" && checkboxname == "show"){
			var rel = $("#registerMaimNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerMaimNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerMaimNewscheckbox()',800);
		}
	    //特定手术/疾病/给付录入信息
		if(checkboxid=="claimSurgery" && checkboxname == "show"){
			var rel = $("#registerOpsNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerOpsNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerOpsNewscheckbox()',800);
		}
		//手术分级信息录入
		if(checkboxid=="claimOpsLevel" && checkboxname == "show"){
			var rel = $("#registerOpsLevelNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsLevelNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerOpsLevelNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerOpsLevelNewsbox()',800);
		}
	    //特种费用录入信息
		if(checkboxid=="claimSpecial" && checkboxname == "show"){
			var rel = $("#registerSpeCostNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerSpeCostNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerSpeCostNewscheckbox()',800);
		}
	    //社保第三方给付录入
		if(checkboxid=="claimBillPaid" && checkboxname == "show"){
			var rel = $("#registerThirdPayNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerThirdPayNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerThirdPayNewscheckbox()',800);
		}
	    //一般失能信息录入
		if(checkboxid=="claimDisabilityGeneral" && checkboxname == "show"){
			var rel = $("#registerGeneralNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterGeneralNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerGeneralNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerGeneralNewscheckbox()',800);
		}
	    //重度失能
		if(checkboxid=="claimDisabilityGreat" && checkboxname == "show"){
			var rel = $("#registerGreatNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterGreatNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerGreatNews", navTab.getCurrentPanel()).show("3000");
			//延迟复选框勾选
			setTimeout('registerGreatNewscheckbox()',800);
		}
	}
}
//勾选复选框
function registerHonspcheckbox(){
	$("input[hiddenDivId='registerHonsp']", navTab.getCurrentPanel()).attr("checked",true);
}
function registerMaimNewscheckbox(){
	$("input[hiddenDivId='registerMaimNews']", navTab.getCurrentPanel()).attr("checked",true);
}
function registerOpsNewscheckbox(){
	$("input[hiddenDivId='registerOpsNews']", navTab.getCurrentPanel()).attr("checked",true);
}
function registerOpsLevelNewsbox(){
 
	$("input[hiddenDivId='registerOpsLevelNews']", navTab.getCurrentPanel()).attr("checked",true);
}
 
function registerSpeCostNewscheckbox(){
	$("input[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).attr("checked",true);
}
function registerThirdPayNewscheckbox(){
	$("input[hiddenDivId='registerThirdPayNews']", navTab.getCurrentPanel()).attr("checked",true);
}
function registerGeneralNewscheckbox(){
	$("input[hiddenDivId='registerGeneralNews']", navTab.getCurrentPanel()).attr("checked",true);
}
function registerGreatNewscheckbox(){
	$("input[hiddenDivId='registerGreatNews']", navTab.getCurrentPanel()).attr("checked",true);
}
 
  
//=============================================================================================//
</script> 
<!-- 只读传递数据的标签 -->
<input type="hidden"  id="readOnly" value="${readOnly}"/>

<input type="hidden"  id="errorFlag" value="true"/>
<input type="hidden"  id="claimAccidentResultVOaccResult1"  value="<Field:codeValue tableName="APP___CLM__DBUSER.T_ACCIDENT1" value="${claimAccidentResultVO.accResult1}"/>"  />
<input type="hidden"  id="claimAccidentResultVOaccResult2"  value="<Field:codeValue tableName="APP___CLM__DBUSER.T_ACCIDENT2" value="${claimAccidentResultVO.accResult2}"/>"  />
<input type="hidden"  id="claimAccidentResultVOaccResult1Id"  value="${claimAccidentResultVO.accResult1}"  />
<input type="hidden"  id="claimAccidentResultVOaccResult2Id"  value="${claimAccidentResultVO.accResult2}"  />
<input type="hidden"  id="hiddenWorkDate" value="${workDate}"/>
<input type="hidden"  id="claimCaseResultVOHospResultId"  value="${claimCaseVO.cureHospital}"  />
<input type="hidden"  id="hospitalResultVOHospLevResultId"  value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CLM_HOSPITAL_LEVEL" value="${hospitalVO.hospitalLevel}"/>"    />
<input type="hidden"  id="claimCaseResultVOHospResult"  value="<Field:codeValue tableName="APP___CLM__DBUSER.T_HOSPITAL" value="${claimCaseVO.cureHospital}"/>"  />

<form method="post" id="registerFeeForms" class="pageForm"
	onsubmit="return validateCallback(this, navTabAjaxDone);"></form>
<div   id="registerAskForInfo">
	<div  style="margin: 0px 10px 5px; border: 1px solid #d3e4f8; border-radius: 5px;">
		 <form method="post" class="pageForm" id="submitRegisterDuty"
			action = "javascript:void(0)"
			onsubmit="return validateCallbackDuty(this, dutyMyAjaxDone);"
			style="background-color: #f5f6f8">  
			<input id="caseId" name="caseId" type="hidden" value="${caseId}">
			<input id="easyCase" type="hidden" value="${easyCase}">
			<input id="claimAccReasonId" type="hidden" value="${accident.accReason}">
			<input type='hidden' name="" id='claimDate' value='<s:date name='claimSubCaseVO.claimDate' format='yyyy-MM-dd' />' />
		 	<input type='hidden' name="" id='accDate' value='<s:date name='accident.accDate' format='yyyy-MM-dd' />' />
		 	<input type='hidden' name="" id='accResult1' value='<s:property value='claimAccidentResultVO.accResult1'/>' />
		 	<input type='hidden' name="" id='accResult2' value='<s:property value='claimAccidentResultVO.accResult2' />' />
			<div style="width: 100%; height: 100%;"  >
				<div id="registerClaimSubCase" align="left"
					style="float: left; width: 100px; height: 1300px; border-right: 1px solid #CCC;padding: 10px;">
					<input type="checkbox" name="claimTypecode[6]" id="" value="6" 
						hiddenDivId="registerJobCateNews"
						onclick="smallChange(this)">职业给付系数
					<input type="checkbox" name="claimTypecode[9]" id="ageDealFlag" value="9"
						hiddenDivId="registerAgeNews"
						onclick="smallChange(this)"> 年龄误告处理
				</div>
				<div id="2" align="left">
					<div class="pageFormContent">
						<!-- 医疗单证录入   div  开始 -->
						<div id="registerHonsp" style="display: none"></div>
						<!--伤残录入信息  div start  -->
						<div id="registerMaimNews" class="pageFormContent" style="display: none">
						</div>

						<!-- 特定手术/疾病/给付录入信息  div start  -->
						<div id="registerOpsNews" class="pageFormContent" style="display: none">
						</div>
						
						<!-- 手术分级信息录入  div start  -->
						<div id="registerOpsLevelNews" class="pageFormContent" style="display: none">
						</div>

						<!-- 特种费用录入信息  div start  -->
						<div id="registerSpeCostNews" class="pageFormContent"
							style="display: none"></div>

						<!--社保第三方给付录入  div start  -->
						<div id="registerThirdPayNews" class="pageFormContent"
							style="display: none"></div>

						<!--职业给付系数录入信息  div start  -->
						<div id="registerJobCateNews" class="pageFormContent"
							style="display: none">
							
							<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">职业给付系数录入
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
									<div class="main_bqtitle">
				                         <h1><img src="clm/images/three.png">职业给付系数信息录入</h1>
				                     </div>
				                     
				                     <div id="jobCateNewsInt" class="panelPageFormContent">
								<!--职业给付系数   div start  -->
								<div class="panelPageFormContent">
									<dl >
										<dt>
											<font class="point" color="red">*　</font>职业代码 
										</dt>
										<dd >
											<input type="hidden" value="${claimOccupationRateVO.jobCode}">
											<%--  <Field:codeTable  name="claimOccupationRateVO.occupationCode" tableName="APP___CLM__DBUSER.T_JOB_CODE" nullOption="true" cssClass="notuseflagDelete combox comboxDD" defaultValue=" " showInput="true" showInputId="jobId" onChange="occupationChange(this);"/> --%>
											<Field:codeTable  id="registerJobIdFire"
												name="claimOccupationRateVO.jobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
												whereClause="job_code not in ('Y001023')"  orderBy="JOB_CODE"
												nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
												value="${claimOccupationRateVO.jobCode}"
												onChange="queryJobLevelByJobCode(this);"/>
											<%-- <input id="registerJobId" type="text"
												value="${claimOccupationRateVO.jobName}"> --%>
										</dd>
									</dl>

									<dl >
										<dt>
											 <font class="point" color="red">*　</font>职业类型
										</dt>
										<dd>
											<!-- <input type="text" name="claimOccupationRateVO.jobCategory"  readonly="readonly"/> -->
											<input type="hidden" name="claimOccupationRateVO.jobCategory"
												value="${claimOccupationRateVO.jobCategory}" /> <input
												type="text" name="data"
												value="<Field:codeValue tableName="APP___CLM__DBUSER.T_JOB_CATEGORY" value="${claimOccupationRateVO.jobCategory}"/>"
												readonly="readonly" />
										</dd>
									</dl>

									<dl>
										<dt>
											<font class="point" color="red">*　</font>职业级别 
										</dt>
										<dd>
											<!-- name="claimOccupationRateVO.occupationRate" <input type="text" readonly="readonly" name="claimOccupationRateVO.occupationRate" /> -->
											<input type="text" id="claimOccupationRateVOoccupationRate"
												value="${claimOccupationRateVO.occupationRate}"
												readonly="readonly" />
										</dd>
									</dl>

									<dl style="width: 100%;height: auto">
										<dt>备注</dt>
										<dd>
											<textarea name="claimOccupationRateVO.occupationRemark"
												rows="2" cols="100"
												onpropertychange="if(value.length>1000) value=value.substring(0,1000)">${claimOccupationRateVO.occupationRemark}</textarea>
										</dd>
									</dl>
								</div>
							</div>
				                     
							</div>
							</div>
							</li>
							</ul>
							</div>
							</div>
							</div>
							
							<!--职业给付系数录入信息 div  end  -->
						</div>

						<!--一般失能信息录入  div start  -->
						<div id="registerGeneralNews" class="pageFormContent"
							style="display: none"></div>

						<!--重度失能   div start  -->
						<div id="registerGreatNews" class="pageFormContent" style="display: none">
						</div>

						<!--一年龄误告处理  div start  -->
						<div id="registerAgeNews" class="pageFormContent" style="display: none">
							<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">年龄误告处理
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
									<div class="main_bqtitle">
				                         <h1><img src="clm/images/three.png">年龄误告处理</h1>
				                     </div>
				                     
				                     <div id="registerAgeNewsInt" class="panelPageFormContent">
								<div class="panelPageFormContent">

									<dl>
										<dt>出险人当前出生日期：</dt>
										<dd>
											<input name="claimAdjustAgeVO.insuBirth" id="insuBirthIdId"
												value="<s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' />"
												type="text" readonly="readonly" />
										</dd>
									</dl>

									<dl>
										<dt>
											<font class="point" color="red">*　</font>出险人真实出生日期：
										</dt>
										<dd>
											<input type="expandDateYMD"
												name="claimAdjustAgeVO.insuRealBirth" class="date" onchange="checkBirthDay(this)"
												value="<s:date name='claimAdjustAgeVO.insuRealBirth' format='yyyy-MM-dd'/>" id="insuRealBirth"/>
											<a   class="inputDateButton" href="javascript:;">选择</a>
											
										</dd>
									</dl>
								</div>
							</div>
				                     
				               </div>
				               </div>
				               </li>
				               </ul>
				               </div>
				               </div>
				               </div>
						</div>

						<div class="formBarButton">
							<ul>
								
								<li>　
											<a class="but_blue main_buta" type="button" class="button" id="dutyUpId"  ${currentPageId>3?"disabled='disabled'":""}  ${readOnly==1?"disabled='disabled'":""}
												onclick="submitRegisterDuty(2);">上一步</a>
										　</li>
								<li> 
								<!--currentPageId的判断为审核阶段只读判断 readOnly为审批阶段判断   另一处为 addBarForDuty.jsp  置灰添加和保存按钮 -->
								<a class="but_blue  main_buta"  ${currentPageId>3?"disabled='disabled'":""}  ${readOnly==1?"disabled='disabled'":""} 
											type="button" onclick="submitRegisterDuty(0);">保存</a>
								 
								 
								</li>
								<li>　
											<a class="but_blue main_buta" type="button" class="button"
												onclick="submitRegisterDuty(1);"  ${currentPageId>3?"disabled='disabled'":""}  ${readOnly==1?"disabled='disabled'":""}
												id="dutyNextId" value="已保存">下一步</a>
										　</li>
								<li>　
											<button class="but_gray close" type="button" onclick="exit();">退出</button>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
 	    </form>  
	</div>
</div>
<script>
 setTimeout("dutyUP()",100);

function dutyUP(){ 
	$("#step2").focus(); 
}
</script>

