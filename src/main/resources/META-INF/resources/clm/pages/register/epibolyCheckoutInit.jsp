<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!-- 外包数据校验定义页面 -->
<script type="text/javascript">
//从左侧移动到右侧
function moveselect(objId, targetId, flag, all) {

	var obj = document.getElementById(objId);
	var target = document.getElementById(targetId);
	if (flag) {
		// 判断all是否为1,唯一则调用转移所有的方法
		if (all == "1") {
			for ( var i = 0; i < obj.length; i++) {
				mot = obj.options[i].text;
				mov = obj.options[i].value;
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
			obj.options.length = 0;
		} else {
			while (obj.selectedIndex > -1) {
				mot = obj.options[obj.selectedIndex].text;
				mov = obj.options[obj.selectedIndex].value;
				obj.remove(obj.selectedIndex);
				var newoption = document.createElement("OPTION");
				newoption.text = mot;
				newoption.value = mov;
				target.add(newoption);
			}
		}
	} else {
		while (obj.selectedIndex > -1) {
			mot = obj.options[obj.selectedIndex].text;
			mov = obj.options[obj.selectedIndex].value;
			obj.remove(obj.selectedIndex);
			var newoption = document.createElement("OPTION");
			newoption.text = mot;
			newoption.value = mov;
			target.add(newoption);
			// 为报案机构的时候查询人员
			getInstitutions();
		}
	}
}

//点击确认方法
function saveOutCheck() {
	if($("#selectedCheckDefine", navTab.getCurrentPanel()).find("option").length == "0" ){
		alertMsg.warn("请选择外包录入字段");
		return;
	};
	$("#selectedCheckDefine option", navTab.getCurrentPanel()).each(function(){
		this.selected=true;
	});
	
	$("#checkDefineForm", navTab.getCurrentPanel()).submit();
}
/* function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
} */
</script>
<div class="pageHeader" layoutH="10">
<form id="checkDefineForm" action="clm/register/updateOutCheckDefine_CLM_epibolyCheckoutAction.action" method="post" class="pageForm required-validate"
	  			  onsubmit="return validateCallback(this, navTabAjaxDone)">
		<div class="panelPageFormContent">
			<div class="panelPageFormContent">
				<table>
					<tr>
						<td>
							<table>
								<tr>
								<td>
								<dl class='nowrap'>
									<dt style="margin-bottom: 10px">外包录入字段</dt>
								</dl>
							</td>
								</tr>
								<tr>
									<td>
									   <select class="combox title"  size="10" name="allCheckDefine" id="allCheckDefine"
										ondblclick="moveselect('allCheckDefine','selectedCheckDefine',true)"
										multiple style="width: 300px">
											<s:iterator value="checkDefineList" var="st"> 
												<option value="${outsourceFieldName}">${outsourceFieldDesc}</option>
											</s:iterator>
									</select></td>
								</tr>
							</table>
						</td>
						<td>
						<div style="margin: 0 30px 0 30px; float: left;">
									<div class="buttonActive">
										<div class="buttonContent">
											<button id="toRightO"
												onclick="moveselect('allCheckDefine','selectedCheckDefine',true)"
												type="button">&nbsp&nbsp&nbsp>&nbsp&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive"
										style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id="allToRightO"
												onclick="moveselect('allCheckDefine','selectedCheckDefine',true,'1')"
												type="button">&nbsp&nbsp>>&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive"
										style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id="toleftO"
												onclick="moveselect('selectedCheckDefine','allCheckDefine',true)"
												type="button">&nbsp&nbsp&nbsp<&nbsp&nbsp&nbsp</button>
										</div>
									</div>
									<div class="buttonActive"
										style="clear: left; margin: 5px 0px 0px;">
										<div class="buttonContent">
											<button id="allToLeftO"
												onclick="moveselect('selectedCheckDefine','allCheckDefine',true,'1')"
												type="button">&nbsp&nbsp<<&nbsp&nbsp</button>
										</div>
									</div>
								</div>
						</td>
						<td><table>
								<tr>
								<td>
								<dl class='nowrap'>
									<dt style="margin-bottom: 10px">需校验必录的字段</dt>
								</dl>
							</td>
								</tr>
								<tr>
									<td><select class="combox title"  size="10" id="selectedCheckDefine" name="checkDefineVO.outsourceFieldName"
										ondblclick="moveselect('selectedCheckDefine','allCheckDefine',true)"
										multiple style="width: 300px">
										<s:iterator value="outCheckDefineList" var="st"> 
												<option value="${outsourceFieldName}">${outsourceFieldDesc}</option>
											</s:iterator>
									</select></td>
								</tr>
							</table></td>
					</tr>
				</table>
			</div>
		</div>
</form>
<div class="formBarButton">
	<ul>
		<li><button class="but_blue" type="button" onclick="saveOutCheck();">确定</button></li>
		<li><button class="but_gray" type="button" onclick="exit()">退出</button></li>
	</ul>
</div>
</div>