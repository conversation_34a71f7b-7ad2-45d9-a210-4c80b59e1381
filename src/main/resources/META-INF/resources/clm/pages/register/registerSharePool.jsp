<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript" src="clm/pages/register/registerSharePool.js">
</script>
<div layoutH="0" id="reportCommPoolJsp">
	<!-- 查询事件访问路径 -->
	<form id="registerSharePoolJspForm"
		action="clm/register/findRegisterSharePool_CLM_registerSharePoolAction.action" method="post" 
		onsubmit="return navTabSearch(this,'registerSharePoolResult')"
		class="pagerForm required-validate" rel="registerSharePoolResult">
	<!-- 查询区域 -->
	<div class="panelPageFormContent" >
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询条件
					</h1>
				</div>
		    <div class="pageFormInfoContent" >  
				<dl  >	
					<dt>赔案号</dt>
					<dd>
						<input type="text" id="caseNo" name="requestVO.caseNo" value="${requestVO.caseNo}" size="25"  onkeyup="this.value=this.value.replace(/^ +| +$/g,'')"/>
					</dd> 
				</dl> 
				<%-- <dl>	
					<dt>出险人客户号</dt>
					<dd>
						<input type="text" id="customerNo" name="requestVO.customerNo" value="${requestVO.customerNo }" size="25" onkeyup="this.value=this.value.replace(/\s/g,'')"/>
					</dd> 
				</dl> --%>
				<dl>	
					<dt>出险人姓名</dt>
					<dd>
						<input type="text" id="customerName" name="requestVO.customerName" value="${requestVO.customerName }" size="25" onkeyup="this.value=this.value.replace(/\s/g,'')" onblur="resetStyle(this)" />
					</dd> 
				</dl>
       			 <dl>
					<dt>出险人性别</dt>
					<dd>
						<Field:codeTable  id="customerSex" name="requestVO.customerSex" value="${requestVO.customerSex }" 
   											 cssClass="notuseflagDelete combox title required comboxDD" nullOption="true" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2)"></Field:codeTable> 
					</dd> 			
				</dl>
                <dl>	
					<dt>证件号码</dt>
					<dd>
						<input type="text" id="customerId" name="requestVO.customerId" value="${requestVO.customerId }" size="25"  />
					</dd>
				</dl>
				<dl >	
					<dt>绩优等级</dt>
					<dd id="greenFlagCheckId"> 
                         <Field:codeTable  cssClass="combox title" name="requestVO.greenFlag" id="greenFlagId" 
  							value="${requestVO.greenFlag}" 
  							nullOption="true"  tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG" 
  							whereClause="green_flag_code in (6,5,4,3,22,21,2,0)" orderBy="decode(green_flag_code,'6','001','5','002','4','003','3','004','22','005','21','006','2','007','0','008', green_flag_code)"></Field:codeTable>
         			</dd>
          		</dl>
				<dl>	
					<dt>出险日期</dt>
					<dd> 
						<input type="expandDateYMD" flag="flag" class="date" id="accDate" name="requestVO.accDate" value="<s:property value="requestVO.accDateStr"/>" size="22" /> 
						<a class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
          		<dl>
					<dt>赔案状态</dt>
					<dd>
						<select class="combox title"  id="claimState" name="requestVO.claimState">
							<option <s:if test="requestVO.claimState eq ''">selected</s:if> value="">请选择</option>
							<option <s:if test="requestVO.claimState eq 1">selected</s:if> value="1">待立案</option>
							<option <s:if test="requestVO.claimState eq 2">selected</s:if> value="2">立案中</option>
							<option <s:if test="requestVO.claimState eq 3">selected</s:if> value="3">待复核</option>
							<option <s:if test="requestVO.claimState eq 4">selected</s:if> value="4">复核中</option>
							<option <s:if test="requestVO.claimState eq 5">selected</s:if> value="5">待立案确认</option>
						</select>
					</dd> 			
				</dl>
				<dl>
				   <dt>申请渠道</dt>
				   <dd>
				      <Field:codeTable  name="requestVO.channelCode" value="${requestVO.channelCode }" 
   											 cssClass="combox title" nullOption="true" tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" whereClause="channel_code in('09','07','03')"></Field:codeTable> 
				   </dd>
				</dl>
 				  
		   <div class="pageFormdiv">
				<button class="but_blue" type="button" onclick="change();">查询</button>
			</div>
		   </div>  
    </div>
    	</form>
	<!-- 显示数据列表区域 -->	
	<div >
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">共享池
					</h1>
				</div>
		<div class="tabdivclassbr" id="registerSharePoolResult">
			<table class="list main_dbottom" width="100%">
				<thead>
					<tr>
                   		<th nowrap>序号</th>
						<th nowrap>赔案号</th>
<!-- 						<th nowrap>出险人客户号</th>
 -->						<th nowrap>出险人姓名</th>
						<th nowrap>证件号码</th>
						<th nowrap>赔案状态</th>
						<th nowrap>机构</th>
						<th nowrap>绩优等级</th>
						<!-- th nowrap>受理渠道类型</th> -->
						<th nowrap>签收人</th>
						<th nowrap>签收时间</th>
						<th nowrap>申请渠道</th> 
						
<!-- 						<th width="100">任务ID</th> -->
					</tr>
				</thead>
				<tbody align="center">
						<s:if test="imageFlag != null">
								<tr>
									<td colspan="11">
										<div class="noRueryResult">请选择条件查询数据！</div>
									</td>
								</tr>
							</s:if>
							<s:elseif test="responseVOList == null || responseVOList.size()==0">
								<tr>
									<td colspan="11">
										<div class="noRueryResult">没有符合条件的查询结果！</div>
									</td>
								</tr>
							</s:elseif>
						<!-- 循环显示数据 -->
						<s:iterator value="responseVOList" status="st">
							<tr ondblclick="registClick('${caseId}','${bpmTaskId}',this)" >
								<td><s:property value="#st.index + 1" /></td>
								<td>
									   
											<s:property	value="caseNo" />
									 
								</td>
<%-- 								<td><s:property value="insuredId" /></td>
 --%>								<td><s:property value="customerName" /></td>
								<td><s:property value="customerCertiCode" /></td>
								<td>${caseStatusName}</td>
								<td><Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
								<td>
								     <s:if test="greenFlag==0 || greenFlag==null">否</s:if>
	                                 <s:else>
	                                     <Field:codeValue value="${greenFlag}" tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG"/>
	                                 </s:else>
							    </td>
								<td><s:property value="signerName" /></td>
								<td><s:date name="signTime" format="yyyy-MM-dd"/> </td>
								<td><Field:codeValue value="${channelCode}" tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" /></td>
<%-- 								<td><s:property value="bpmTaskId" /></td> --%>
                                
							</tr> 
						</s:iterator>
					</tbody>
   		   </table>
   		    <!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value},'registerSharePoolResult')"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab" rel="registerSharePoolResult"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
        </div>
   	</div>
   
   		<!-- 个人池显示数据列表区域 -->
	<!-- 个人池分页查询访问路径 -->
	<div id="registerSharePoolJsp">
 		<%@ include file="registerSharePoolSelf.jsp"%>
	</div>
   	<div class="formBarButton" >
		<ul>  
			<li>
				<button type="button" class="but_gray close">退出</button>
			</li>
		</ul>
	</div>
</div>

