//输入框的非空验证
function change(){
	var searOrganCode =$("#organCodePro", $.pdialog.getCurrent()).val();
	var currentOrganCode = $("#ocUse",navTab.getCurrentPanel()).val();
	if(searOrganCode.indexOf(currentOrganCode) == -1){
		alertMsg.error("只能查询当前用户机构及下属机构数据！");
		return false;
	}
	
	$("#registerSharePoolJspForm", navTab.getCurrentPanel()).submit();
}
function applyTask(){
	var strs = "";
	$("#registerSharePoolResult", navTab.getCurrentPanel()).find("table").find("tbody").find("tr").each(function (){
		//获取已经选中的共享池数据
		if($(this).find("td").eq(0).find("input").is(":checked")){
			strs = strs + "," + $(this).find("td").eq(0).find("input").val();
		}
	});
	if(strs.length>0){
		strs = strs.substring(1,strs.length);
		$.ajax({
			url:"clm/register/applyPersonal_CLM_directSharePoolAction.action",
			type:"POST",
			data:{"signShareToOner":strs},
			success:function(data){
				var json = eval("("+data+")");
				if(json.statusCode == '300') {
					alertMsg.error(json.message);
					return false;
				}
				if(json.statusCode == '200'){
					$("#registerSharePoolResult", navTab.getCurrentPanel()).find("table").find("tbody").find("tr").each(function (){
						//获取已经选中的共享池数据,
						if($(this).find("td").eq(0).find("input").is(":checked")){
							$(this).find("td").eq(0).find("input").parent().parent().remove();
						}
					});
					//重新加载个人池任务
					var rel = $("#registerSharePoolJsp", navTab.getCurrentPanel());
					rel.loadUrl("clm/register/findDirectSharePoolSelf_CLM_directSharePoolAction.action");
					alertMsg.correct("申请任务成功！");
				}
				
			}
		});
	}else{
		alertMsg.error("请选择共享池任务");
	}
	
}
//function registClick(caseId,bpmTaskId,obj){
//	
//	$(obj).remove();
//	 var str=caseId+","+bpmTaskId;
//		navTab.openTab("20283", "clm/sign/applyPersonal_CLM_applyPersonalTaskAction.action?flag=2&signShareToOner="+str+"&caseId="+caseId, {title:'立案登记'});
//		
//	 
//}

function redirectOther(url){
	navTab.openTab('20264','clm/sign/toDataCollectInit_CLM_claimRegisterAction.action?leftFlag=1&menuId=20264',{title:'立案登记'});
}