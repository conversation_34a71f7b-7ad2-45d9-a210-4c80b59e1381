 
//初始化加载银行编码
var caseId = $("#caseId", navTab.getCurrentPanel()).val();
var isOCRShow = $("#isOCRShow", navTab.getCurrentPanel()).val();
var payMode = $("#PayModeId", navTab.getCurrentPanel()).val();
if(payMode == "34" || payMode == "22"){
	$("#isBankOfDepositDiv", navTab.getCurrentPanel()).show();
}
$.ajax({
			'type':'post',
			'url':'clm/register/queryTankCode_CLM_toBenefitInputPageAction.action?caseId='+caseId+'&payMode='+payMode,
			'datatype':'json',
			'success':function(data){
				var claimTypeFlag = $("#claimTypeFlagBene", navTab.getCurrentPanel()).val();
				//姚老师确认只有选择领款人与受益人关系为本人的时候才有提示。所以初始化时不默认本人。
//				if(claimTypeFlag == "2"){ //说明没有身故理赔类型。
//					var payeeRelation = "00"; 
//					$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).val(payeeRelation);
//					$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).empty();
//					$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).append($("select#payeeRelation", navTab.getCurrentPanel()).find("option[value="+payeeRelation+"]").attr("title"));
//					$("select#payeeRelation", navTab.getCurrentPanel()).attr("value", payeeRelation);
//					claimFireEvent($("#payeeRelation",navTab.getCurrentPanel()));
//				}
				
				var bankVOList = eval("(" + data + ")");
				if($("select#bankCodeId").find("option")!=null && $("select#bankCodeId").find("option")!= ""){
					$('#bankCodeId option').remove();
				}
				var option = "<option value=''>请选择</option>";
				for(var i = 0; i < bankVOList.length; i++){
					option += "<option value='"+bankVOList[i].bankCode+"'>"+bankVOList[i].bankName+"</option>";
				}
				$("#bankCodeId", navTab.getCurrentPanel()).append(option);
				var bankCode = $("#hiddenBank", navTab.getCurrentPanel()).val();
				if($("#hiddenBank", navTab.getCurrentPanel()).val() != null && $("#hiddenBank", navTab.getCurrentPanel()).val() != ""){
					$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).val(bankCode);
					$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).append($("select#bankCodeId", navTab.getCurrentPanel()).find("option[value="+bankCode+"]").attr("title"));
					$("select#bankCodeId", navTab.getCurrentPanel()).attr("value", bankCode);
					 claimFireEvent($("#bankCodeId",navTab.getCurrentPanel()));
				}
			}
		});
//查询本次赔案相关领款人
$.ajax({
	url : "clm/register/queryPayeeName_CLM_toBenefitInputPageAction.action?claimPayeeVO.caseId="+caseId,
	global : false,
	type : "POST",
	dataType : "json",
	async : false,
	success : function(payeeVOList) {
		if($("#payeeName",navTab.getCurrentPanel()).find("option").eq(0).html()!="请选择"){
			var optionStr = "<option value=''>请选择</option>";
			for(var i=0; i <payeeVOList.length; i++){
				if(payeeVOList[i].beneId != ""){
						var payeeName = payeeVOList[i].payeeName;
						optionStr += "<option value='" + payeeName +"'></option>";
				}
			}	
			$("#payeeName",navTab.getCurrentPanel()).append(optionStr);
		}
		
	}
});
//存放银行账号
var bankAccount = "";
//用于判断银行账号是否录入两次,未录入时为false，录入第一次之后为true
var accountFlag = false;
//标志位，控制'添加受益人'按钮不能连续添加两次和控制先点击添加受益人按钮后才可以保存受益人与领款人信息
var flag = 0;
//受益比例计算是否正确标志
var beneRateFlag = false;
//用于判断是否存在受益人为出险人的时候不清空受益人姓名
var beneNameFlag = true;
//退出
/*function ext() {
	alertMsg.confirm("是否确定退出？", {
		okCall : function() {
			navTab.closeCurrentTab();
		}
	});
}*/

	 //控制银行账号不可以复制粘贴剪切
	$("input#accountNo", navTab.getCurrentPanel()).bind("copy paste", function() {
		return false;
	});
	$("input#payeeCertiNo", navTab.getCurrentPanel()).bind("copy paste", function() {
		return false;
	});
	$("input#beneCertiNo", navTab.getCurrentPanel()).bind("copy paste", function() {
		return false;
	});
	if($("#acceptDecision" , navTab.getCurrentPanel()).val() == "2" || $("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr").length == "0"){ //不予立案的情况
		//禁掉添加和保存按钮，还有radiao按钮。
		$("#saveBenePayee" , navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#addBenePayee" , navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", true);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
		 var obj=$("div#registerShowId", navTab.getCurrentPanel());
		//输入框 复选框   控制
		obj.find("input").each(function(){
			if(!$(this).is("[type='radio']")){
				$(this).attr("disabled",true);
			}
		});
		$("#hiddenCaseId", navTab.getCurrentPanel()).removeAttr("disabled");
		//单选按钮
		obj.find("input").each(function(){
			if($(this).is("[type='radio']")){
				$(this).attr("disabled",true);
			}
		});
		//下拉框
		obj.find("select").each(function(){
			$(this).attr("disabled",true);
		});
		//a标签
		obj.find("a").each(function(){
			$(this).attr("disabled",true);
		});
		$("#hiddenCaseId", navTab.getCurrentPanel()).removeAttr("disabled");
	} else if(isOCRShow != 1){
		//如果存在保单，选中第一个并带出信息
		if($("#infoFlagId", navTab.getCurrentPanel()).val() != "1"){
			if ($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr").length > 0) {
				$("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq(0)").find("td:eq(0)").find("input[type='radio']", navTab.getCurrentPanel()).attr("checked", true);
				$("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).click();
			}
		}
	} else {
		if($("#infoFlagId", navTab.getCurrentPanel()).val() != "1"){
			if ($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr").length > 0) {
				$("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq(0)").find("td:eq(0)").find("input[type='radio']", navTab.getCurrentPanel()).attr("checked", true);
				var value = $("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).val();
				var valueSplit = value.split(":");
				var caseId = valueSplit[0];
				var policyId = valueSplit[1];
				var busiItemId = valueSplit[2];
				var busiProdCode = valueSplit[3];
				var policyCode = $("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq(0)").find("td:eq(1)").text();
				// 将值赋值到form表单中
				$("#hiddenCaseId", navTab.getCurrentPanel()).val(caseId);
				$("#hiddenPolicyId", navTab.getCurrentPanel()).val(policyId);
				$("#hiddenBusiItemId", navTab.getCurrentPanel()).val(busiItemId);
				$("#hiddenPolicyCode", navTab.getCurrentPanel()).val(policyCode);
				$("#hiddenBusiProdCode", navTab.getCurrentPanel()).val(busiProdCode);
			}
			//受益人解除置灰
			$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", false);
			$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
			$("#beneRelation", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneSex", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
			$("#beneCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#clmtNation", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#beneCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payMole", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payDeno", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payAmount", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			//领款人解除置灰
			$("#payeeRelation", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payeeName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			//$("#payeeName", navTab.getCurrentPanel()).text($("#payeeName", navTab.getCurrentPanel()).prev().val());
			$("#payeeSex", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payeeCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
			$("#payeeCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#clmtNationL", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#PayModeId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#bankCodeId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#accountName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			$("#accountNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
			//保存按钮解除置灰
			$("#saveBenePayee", navTab.getCurrentPanel()).removeAttr("disabled");
			$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", false);
			$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
			//受益分子/分母默认为1
			$("#payMole", navTab.getCurrentPanel()).val(1);
			$("#payDeno", navTab.getCurrentPanel()).val(1);
			var actualPay = $("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq(0)").find("td:eq(4)").text();
			$("#payAmount", navTab.getCurrentPanel()).val(actualPay);	
		}
	}

//校验领款人身份证号格式是否正确
function checkpayeeCertiNo(){
	var payeeCertiType = $("#payeeCertiType",navTab.getCurrentPanel()).val();
	var payeeCertiNo = $("#payeeCertiNo",navTab.getCurrentPanel()).val();
	var clmtNationL = $("#clmtNationL",navTab.getCurrentPanel()).val();
	if(payeeCertiNo != null || payeeCertiNo != ""){
		if(payeeCertiType == ""){
			alertMsg.warn('请先填写证件类型再填写证件号码！');
			return;
		}
	}
	//港澳台校验国籍（香港 澳门 台湾  港澳台 校验规则不一样）
	if(payeeCertiType=="h" || payeeCertiType == "b" || payeeCertiType == "i" || payeeCertiType == "d"){
		if(!checkCertiNoAndNa(payeeCertiType,payeeCertiNo,clmtNationL)){
			return;
		}
	}else{
		if(!checkCertiNo(payeeCertiType,payeeCertiNo)){
			return;
		}
	}
	if(payeeCertiType == "0"){
		if(payeeCertiNo.length == 18){
			$("#payeeBirth",navTab.getCurrentPanel()).val(payeeCertiNo.substr(6,4) + "-" + payeeCertiNo.substr(10,2) + "-" + payeeCertiNo.substr(12,2));
			if(payeeCertiNo.substr(16,1)%2 == 0){
				 $("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("2");
			}else{
				$("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("1");
			}
		}else if(payeeCertiNo.length == 15){
			$("#payeeBirth",navTab.getCurrentPanel()).val("19" + payeeCertiNo.substr(6,2) + "-" + payeeCertiNo.substr(8,2) + "-" + payeeCertiNo.substr(10,2));
			if(payeeCertiNo.substr(14,1)%2 == 0){
				 $("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("2");
			}else{
				$("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("1");
			}
		}
	}
	
//	if(beneCertiType == "h"){
//		if(beneCertiNo.length == 18){
//			// 18位身份证：第7~14位数字表示出生年(四位)、月、日；第17位代表性别，奇数为男，偶数为女
//			$("#beneBirth",navTab.getCurrentPanel()).val(beneCertiNo.substr(6,4) + "-" + beneCertiNo.substr(10,2) + "-" + beneCertiNo.substr(12,2));
//			if(beneCertiNo.substr(16,1)%2 == 0){
//				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
//				getBeneInfoRegi();
//			}else{
//				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
//				getBeneInfoRegi();
//			}
//		}
//	}
}

//校验受益人身份证号格式是否正确
function checkbeneCertiNo(){
	var beneCertiType = $("#beneCertiType",navTab.getCurrentPanel()).val();
	var beneCertiNo = $("#beneCertiNo",navTab.getCurrentPanel()).val();
	var clmtNation = $("#clmtNation",navTab.getCurrentPanel()).val();
	if(beneCertiNo != null || beneCertiNo != ""){
		if(beneCertiType == ""){
			alertMsg.warn('请先填写证件类型再填写证件号码！');
			return;
		}
	}
	if(beneCertiType=="h" || beneCertiType == "b" || beneCertiType == "i" || beneCertiType == "d"){
		if(!checkCertiNoAndNa(beneCertiType,beneCertiNo,clmtNation)){
			return;
		}
	}else{
		if(!checkCertiNo(beneCertiType,beneCertiNo)){
			return;
		}
	}
	if(beneCertiType == "0"){
		if(beneCertiNo.length == 18){
			// 18位身份证：第7~14位数字表示出生年(四位)、月、日；第17位代表性别，奇数为男，偶数为女
			$("#beneBirth",navTab.getCurrentPanel()).val(beneCertiNo.substr(6,4) + "-" + beneCertiNo.substr(10,2) + "-" + beneCertiNo.substr(12,2));
			if(beneCertiNo.substr(16,1)%2 == 0){
				 $("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
				 getBeneInfoRegi();
			}else{
				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
				getBeneInfoRegi();
			}
		}else if(beneCertiNo.length == 15){
			// 15位身份证：第7~12位数字表示出生年(两位)、月、日；第15位代表性别，奇数为男，偶数为女
			$("#beneBirth",navTab.getCurrentPanel()).val("19" + beneCertiNo.substr(6,2) + "-" + beneCertiNo.substr(8,2) + "-" + beneCertiNo.substr(10,2));
			if(beneCertiNo.substr(14,1)%2 == 0){
				 $("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
				 getBeneInfoRegi();
			}else{
				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
				getBeneInfoRegi();
			}
		}
	}
//	if(beneCertiType == "h"){
//		if(beneCertiNo.length == 18){
//			// 18位身份证：第7~14位数字表示出生年(四位)、月、日；第17位代表性别，奇数为男，偶数为女
//			$("#beneBirth",navTab.getCurrentPanel()).val(beneCertiNo.substr(6,4) + "-" + beneCertiNo.substr(10,2) + "-" + beneCertiNo.substr(12,2));
//			if(beneCertiNo.substr(16,1)%2 == 0){
//				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
//				getBeneInfoRegi();
//			}else{
//				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
//				getBeneInfoRegi();
//			}
//		}
//	}
}

// 立案确认
function registerConfirm(caseId, caseNo, accidentNo) {
	var payMode = $("#PayModeId", navTab.getCurrentPanel()).val();
	var bankCode = $("#bankCodeId", navTab.getCurrentPanel()).val();
	var accountNo = $("#accountNo", navTab.getCurrentPanel()).val();
	var accountName = $("#accountName", navTab.getCurrentPanel()).val();
	var bankOfDeposit = $("#bankOfDepositId", navTab.getCurrentPanel()).val();
	
	if (payMode == "31" || payMode == "32"|| payMode == "34"|| payMode == "22") {
		if (bankCode == "" || bankCode == null) {
			alertMsg.error("银行编码必填");
			return;
		}
		if (accountNo == "" || accountNo == null) {
			alertMsg.error("银行账号必填");
			return;
		}
		if (accountName == "" || accountName == null) {
			alertMsg.error("银行账户名必填");
		return;
		}
	}
	if (payMode == "34" || payMode == "22") {
		if (bankOfDeposit == "" || bankOfDeposit == null) {
			alertMsg.error("开户行名称必填");
			return;
		}
	}
	
	var claimtype;
	$.ajax({
			'type':'post',
			'url':'clm/register/confirmIfInfo_CLM_claimMatchCalcAction.action?caseId='+caseId,
			'datatype':'json',
			'async':false,
			'success':function(data){
				var data = eval("(" + data + ")");
				claimtype=data.ClaimType1;
				for(var kay in data){
					if (kay=="1") {
  						alertMsg.info(data[kay]);
  						return false;
					}
				}
			}
			
	}); 
	 var itCheckFlag = false;
	 $.ajax({//校验赔案是否存在没有回销的IT问题件
			url : "clm/memo/checkAllItProbCase_CLM_memoAction.action?caseId="+caseId,
			type : "POST",
			dataType : "json",
			async : false,
			success : function (l){
				if (l.statusCode != DWZ.statusCode.ok) {//存在没有回销的IT问题件，进行阻断提示
					alertMsg.error("该赔案存在未回销的问题件，请核实");
					itCheckFlag = false;
					return false;
				}else{
					itCheckFlag = true;
				}
			}	
		});
	 if (!itCheckFlag) {
			return false;
		}
	var antiMoneyFlag = true;
	$.ajax({
		'type':'post',
		'url':'clm/register/checkAntiMoneyLaunderingRequired_CLM_toBenefitInputPageAction.action?caseId='+caseId,
		'datatype':'json',
		'async':false,
		'success':function(data){
			var data = eval("(" + data + ")");
			for(var kay in data){
				if (data[kay] == "1") {
					alertMsg.info("赔案的最终给付金额大于等于1万，请录入被保险人身份基本信息");
					antiMoneyFlag = false;
					return false;
				}
			}
		}
		
	});
	
	//137572 客户九要素一致性校验
	var customerNineElementFlag = true;
	$.ajax({
		'type':'post',
		'url':'clm/register/checkCustomerNineElement_CLM_toBenefitInputPageAction.action?caseId='+caseId,
		'datatype':'json',
		'async':false,
		'success':function(data){
			var data = eval("(" + data + ")");
			if (data.statusCode == 0) {
				alertMsg.info(data.message);
				customerNineElementFlag = false;
				return false;
			}
		}
	});
	if(!customerNineElementFlag){
		return false;
	}
	
	
	var checkAmlAgeLimitFlag = false;
	var checkAmlAgeLimitMessag = '';
	$.ajax({
		'type': 'post',
		'url': 'clm/paymentplan/checkAmlAgeLimit_CLM_paymentPlanAction.action?caseId=' + caseId,
		'datatype': 'json',
		'async': false,
		'success': function (data) {
			var data = eval("(" + data + ")");
			if (data.statusCode == 1) {
				checkAmlAgeLimitFlag = true;
				checkAmlAgeLimitMessag = data.message;
			}
		}
	});
	if (checkAmlAgeLimitFlag) {
		alertMsg.error(checkAmlAgeLimitMessag);
		return;
	}
	
	/*if(syr=="00"&&claimtype=="01"){
		alertMsg.error("您选择的受益人或领款人已身故，请重新填写！");
		return false;
	}*/
	
	if($("#acceptDecision", navTab.getCurrentPanel()).val() != "2"){
		if($("#isWaivedId", navTab.getCurrentPanel()).val() == "1"){ //金额为0的，与不予立案的情况一致。直接立案确认。
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/registerConfirm_CLM_toBenefitInputPageAction.action?caseNO="+caseNo);
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, registerConfirmAjaxDone)");
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
		} else {
			if(antiMoneyFlag){ //赔案金额大于10000且被保人信息不能为空
				$.ajax({
					'url':"clm/paymentplan/isClaimPayeeFind_CLM_paymentPlanAction.action?caseId="+caseId+"&registFlag=1",
					'type':'post',
					'datatype':'json',
					'success':function(data){
						var date = eval("(" + data + ")");
						if(date.statusCode == '300'){
							alertMsg.error(date.message);
						} else{
							var paymentPlanTR = $("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr");    
							if(paymentPlanTR.length > 0){
								for (var i = 0; i < date.length; i++) {
									if (date[i].payeeName == "" || date[i].payeeName == null) {
										alertMsg.error("领款人姓名必填");
										return;
									}
									if (date[i].payMode == "" || date[i].payMode == null) {
										alertMsg.error("支付方式必填");
										return;
									}
									if (date[i].payMode == "31" || date[i].payMode == "32"
											|| date[i].payMode == "34" || date[i].payMode == "22") {
										if (date[i].bankCode == "" || date[i].bankCode == null) {
											alertMsg.error("银行编码必填");
											return;
										}
										if (date[i].accountNo == "" || date[i].accountNo == null) {
											alertMsg.error("银行账号必填");
											return;
										}
										if (date[i].accountName == "" || date[i].accountName == null) {
											alertMsg.error("银行账户名必填");
											return;
										}
									}
									if ( date[i].payMode == "34" || date[i].payMode == "22") {
										if (date[i].bankOfDeposit == "" || date[i].bankOfDeposit == null) {
											alertMsg.error("开户行名称必填");
										return;
										}
									}
								}
							}
							
							//校验是否所有的险种都已经分配。
							var indexI;
							var trs = $("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr");
							if(trs.length > 1){
								for(var i = 0; i < trs.length; i++){
									if($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input:eq(1)").attr("checked") == "checked"){
										indexI = i;
										break;
									}
								}
							}
							for(var i = 0; i < trs.length; i++){
								if($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input:eq(1)").attr("checked") != "checked"){
									$("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input:eq(1)").attr("checked","checked");
									queryBenePayee($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input:eq(1)"));
								}
								if($("#claimBeneTbody", navTab.getCurrentPanel()).find("tr").length == 0){
									alertMsg.error("存在未作受益分配的支付记录，请检查。");
									return;
								}
							}
							if(trs.length > 1){
								$("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq("+indexI+")").find("td:eq(0)").find("input:eq(1)").attr("checked","checked");
								queryBenePayee($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq("+indexI+")").find("td:eq(0)").find("input:eq(1)"));
							}
							
							$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/registerConfirm_CLM_toBenefitInputPageAction.action?caseNO="+caseNo);
							$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, registerConfirmAjaxDone)");
							
							if ($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr").length > 0) {
								//判断所有的理赔金与结算项是否分配完
								var strValue = $("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).val();
								var valueSplit = strValue.split(":");
								var caseId = valueSplit[0];
								//控制是否返回
								var flag = false;
								$.ajax({
									url : "clm/paymentplan/assignMoneyFinishRegister_CLM_paymentPlanAction.action?claimPayVO.caseId="+caseId+"&claimPayVO.advanceFlag=0",
									global : false,
									type : "POST",
									dataType : "json",
									async : false,
									success : function (json){
										if (json.statusCode == DWZ.statusCode.ok) {
											flag = true;
											//受益人和领款人保存
											$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
											return true;
										} else {
											flag = false;
											alertMsg.error(json.message);
											return false;
										}
									}
								});
								if (!flag) {
									return false;
								}
							}
							
						}
					}
				});
				
			}
			
		}
		
	} else { //不予立案的情况下，直接提交，不用校验是否分配完
		$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/registerConfirm_CLM_toBenefitInputPageAction.action?caseNO="+caseNo);		
		$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, registerConfirmAjaxDone)");
		$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
	}
}

//立案确认回调函数
function registerConfirmAjaxDone(json) {
	var stauts = json.statusCode.split(",");
	if (stauts[0] == DWZ.statusCode.ok) {
		alertMsg.correct(json.message);
		//若立案确认成功，关闭立案页面
		
		 if(stauts[1] == "1"){ //如果为1的情况下跳转到外包案件池，否则跳转到立案共享池。
			 //跳转到外包案件池查询到为问题件的数据-------42990
				var url = "clm/register/outSourceCasePoolQuery_CLM_claimOutSourceManageAction.action?outsourceCaseVO.outsourceStatusCode=3";
				navTab.openTab("20524", url, {title:'外包案件池'});
		 } else {
			 var url = "clm/register/findRegisterSharePoolInit_CLM_registerSharePoolAction.action";
				navTab.openTab("20290", url, {title:'立案共享池'});
		 }
		//navTab.closeCurrentTab();
//		//先关闭访问理赔工作台页面
//		navTab.closeTab("20345");
//		//重新打开理赔工作台页面
//		var url = "clm/clmWorkPlatform/clmWorkPlatformInitialize_CLM_clmWorkPlatformAction.action";
//		navTab.openTab("20345", url, {title:'访问理赔工作台'});
	} else {
		alertMsg.error(json.message);
	}
}


//查询当前保单下领款人与受益人信息
function queryBenePayee(k) {
	
	flag = 0;
	$("#saveBenePayee", navTab.getCurrentPanel()).attr("disabled", "disabled");
	$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", true);
	$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	clearInfo();
	
	var value = $(k).val();
	var valueSplit = value.split(":");
	var caseId = valueSplit[0];
	var policyId = valueSplit[1];
	var busiItemId = valueSplit[2];
	var busiProdCode = valueSplit[3];
	var policyCode = $(k).parent().parent().find("td:eq(1)").text();
	// 将值赋值到form表单中
	$("#hiddenCaseId", navTab.getCurrentPanel()).val(caseId);
	$("#hiddenPolicyId", navTab.getCurrentPanel()).val(policyId);
	$("#hiddenBusiItemId", navTab.getCurrentPanel()).val(busiItemId);
	$("#hiddenPolicyCode", navTab.getCurrentPanel()).val(policyCode);
	$("#hiddenBusiProdCode", navTab.getCurrentPanel()).val(busiProdCode);
	
	$.ajax({
				url : "clm/register/queryBeneAndPayeeMessage_CLM_toBenefitInputPageAction.action?claimPayVO.caseId="
						+ caseId
						+ "&claimPayVO.policyId="
						+ policyId
						+ "&claimPayVO.busiItemId=" + busiItemId
						+ "&claimPayVO.busiProdCode=" + busiProdCode,
				global : false,
				type : "POST",
				dataType : "html",
				async : false,
				success : function(html) {
					$("#claimBeneTbody", navTab.getCurrentPanel()).empty();
					$("#claimBeneTbody", navTab.getCurrentPanel()).html(html).initUI();
					if($("#acceptDecision", navTab.getCurrentPanel()).val() == "2"){
						$("#addBenePayee", navTab.getCurrentPanel()).attr("disabled","disabled");
					} else {
						if($("#isWaivedId", navTab.getCurrentPanel()).val() == "1"){
							$("#addBenePayee", navTab.getCurrentPanel()).attr("disabled","disabled");
						}
					}
					// 剩余比例
					var rate = $("#rate", navTab.getCurrentPanel()).val();
					var customerBeneName = $("#customerBeneName", navTab.getCurrentPanel()).val();
					if(customerBeneName != ""){
						beneNameFlag = false;
						$("#beneName", navTab.getCurrentPanel()).val(customerBeneName);
					}
					$("#beneRate", navTab.getCurrentPanel()).val(rate);
				}
			});
}
//编辑
function edits(k) {
	//去掉只读（保存）
	$("#saveBenePayee", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", false);
	$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	var strValue = $("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).val();
	var valueSplit = strValue.split(":");
	var caseId = valueSplit[0];
	var listId = valueSplit[1];
	var claimBusiProdId = valueSplit[2];
	var claimBusiProdCode = valueSplit[3];
	var beneId = $(k).parent().parent().find("#beneId").val();
	var payeeId = $(k).parent().parent().find("#payeeId").val();
	var policyCode = $("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).parent().parent().find("td:eq(1)").text();
	var rel = $("#benePayeeInfo",navTab.getCurrentPanel());
	rel.loadUrl( "clm/register/benePayeeInfoView_CLM_toBenefitInputPageAction.action?beneVO.beneId="
							+ beneId + "&payeeVO.payeeId=" + payeeId
							+ "&claimPayVO.caseId=" + caseId
							+ "&claimPayVO.policyId=" + listId
							+ "&claimPayVO.busiItemId=" + claimBusiProdId
							+ "&claimPayVO.busiProdCode=" + claimBusiProdCode,
					"html",
					function() {
						// 回调函数为form赋值
						$("input#hiddenBeneId", navTab.getCurrentPanel()).val(beneId);
						$("input#hiddenPayeeId", navTab.getCurrentPanel()).val(payeeId);
						$("#hiddenCaseId", navTab.getCurrentPanel()).val(caseId);
						$("#hiddenPolicyId", navTab.getCurrentPanel()).val(listId);
						$("#hiddenBusiItemId", navTab.getCurrentPanel()).val(claimBusiProdId);
						$("#hiddenPolicyCode", navTab.getCurrentPanel()).val(policyCode);
						$("#hiddenbusiProdCode", navTab.getCurrentPanel()).val(claimBusiProdCode);
						$("#hiddenIsInstalment", navTab.getCurrentPanel()).val("1");

						// 重新计算剩余比例
						$.ajax({
									url : "clm/register/computeEditRats_CLM_toBenefitInputPageAction.action?claimPayVO.caseId="
											+ caseId
											+ "&claimPayVO.policyId="
											+ listId
											+ "&claimPayVO.busiItemId="
											+ claimBusiProdId
											+ "&claimPayVO.beneId=" + beneId
											+"&claimPayVO.advanceFlag=0",
									global : false,
									type : "POST",
									dataType : "json",
									async : false,
									success : function(json) {
										// 剩余比例
										beneRateFlag = true;
										$("#rate", navTab.getCurrentPanel()).val(json[0].residueRate);
										$("#beneRate", navTab.getCurrentPanel()).val(json[0].residueRate);
									}
								});
						var beneCertiEndHidentId =$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val();
						if(beneCertiEndHidentId != "" && beneCertiEndHidentId == "9999-12-31"){
						 	 $("#beneCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
						}
					    var payeeCertiEndHidentId =$("#payeeCertiEndHidentId", navTab.getCurrentPanel()).val();
					    if(payeeCertiEndHidentId != "" && payeeCertiEndHidentId == "9999-12-31"){
							 $("#payeeCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
						}
					    var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
						var payeeLegalPersonId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
						var beneEmail = $("#beneEmail", navTab.getCurrentPanel()).val();
						var payeeEmail = $("#payeeEmail", navTab.getCurrentPanel()).val();
					    if(beneLegalPersonId.length > 0){
					    	if(beneEmail.length > 0){
					    		$("#beneEmail", navTab.getCurrentPanel()).attr("disabled", true);
					    	}					    	
					    }
					    if(payeeLegalPersonId.length > 0){
					    	if(payeeEmail.length > 0){
					    		$("#payeeEmail", navTab.getCurrentPanel()).attr("disabled", true);
					    	}
					    }
					});
}
// 点击删除图标
function dels(k) {
	var strValue = $("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).val();
	var valueSplit = strValue.split(":");
	var caseId = valueSplit[0];
	var listId = valueSplit[1];
	var claimBusiProdId = valueSplit[2];
	var beneId = $(k).parent().parent().find("td:eq(1)").find("input#beneId").val();
	var payeeId = $(k).parent().parent().find("td:eq(2)").find("input#payeeId").val();
	alertMsg.confirm("请确认是否删除？",{
						okCall : function() {
							if (beneId == null && payeeId == null) {
								$(k).parent().parent().remove();
								flag = 0;
							} else {
								$.ajax({
											url : "clm/paymentplan/deleteBenePayeeInfos_CLM_toBenefitInputPageAction.action?claimPayVO.caseId="
													+ caseId
													+ "&claimPayVO.policyId="
													+ listId
													+ "&claimPayVO.busiItemId="
													+ claimBusiProdId
													+ "&claimPayVO.beneId="
													+ beneId
													+ "&claimPayVO.payeeId="
													+ payeeId,
											global : false,
											type : "POST",
											dataType : "json",
											success : function(s) {
												$(k).parent().parent().remove();
												if (s.statusCode == DWZ.statusCode.ok) {
													
													//重新进入领款人与受益人的页面
													document.getElementById("16").href="clm/register/BenefitInputPageInit_CLM_toBenefitInputPageAction.action?caseId="+caseId;
													$("#16",navTab.getCurrentPanel()).click();
													if ($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr").length > 0) {
														$("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq(0)").find("td:eq(0)").find("input[type='radio']", navTab.getCurrentPanel()).attr("checked", true);
														$("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).click();
													}
													
													$("#claimPayIdHidden",navTab.getCurrentPanel()).val("");
													$("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).click();
													//将所有页面的字段清空
													$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).val("");
													$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).empty();
													$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).append($("select#beneRelation", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#beneRelation").attr("value", "");
													claimFireEvent($("#beneRelation",navTab.getCurrentPanel()) );
													
													$("#beneName", navTab.getCurrentPanel()).val("");
													
													$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).val("");
													$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).empty();
													$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).append($("select#beneSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#beneSex").attr("value", "");
													claimFireEvent($("#beneSex",navTab.getCurrentPanel()) );
													$("#beneBirth", navTab.getCurrentPanel()).val("");
													
													$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).val("");
													$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).empty();
													$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).append($("select#beneCertiType", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#beneCertiType", navTab.getCurrentPanel()).attr("value", "");
													claimFireEvent($("#beneCertiType",navTab.getCurrentPanel()) );
													$("#beneCertiNo", navTab.getCurrentPanel()).val("");
													
													/*$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).val("");
													$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).empty();
													$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).append($("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).attr("value", "");*/
													
													$("#beneCertiStart", navTab.getCurrentPanel()).val("");
													$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
													$("#payMole", navTab.getCurrentPanel()).val("");
													$("#payDeno", navTab.getCurrentPanel()).val("");
													$("#payAmount", navTab.getCurrentPanel()).val("");

													$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).val("");
													$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).empty();
													$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).append($("select#payeeRelation", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#payeeRelation", navTab.getCurrentPanel()).attr("value", "");
													claimFireEvent($("#payeeRelation",navTab.getCurrentPanel()) );
													$("#payeeName", navTab.getCurrentPanel()).val("");
													$("#payPayeeRelationId", navTab.getCurrentPanel()).val("");
													
													$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("");
													$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
													$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "");
													claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
													$("#payeeBirth", navTab.getCurrentPanel()).val("");
													
													$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val("");
													$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
													$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", "");
													claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()) );
													$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
													
													/*$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
													$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
													$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", "");*/
													
													$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
													$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
													
													
													$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).val("");
													$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).empty();
													$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).append($("select#PayModeId", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
													$("select#PayModeId", navTab.getCurrentPanel()).attr("value", "");
													claimFireEvent($("#PayModeId",navTab.getCurrentPanel()) );
													$("#accountName", navTab.getCurrentPanel()).val("");
													$("#accountNo", navTab.getCurrentPanel()).val("");
													
													$("#saveBenePayee", navTab.getCurrentPanel()).attr("disabled", "disabled");
													$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", true);
													$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
													alertMsg.correct(s.message);
												} else {
													alertMsg.error(s.message);
												}
											}
										});
							}
						}
					});
}
// 保存受益人与领款人校验
function saveJudg(flag) {
	var BankCode = $("#bankCodeId",navTab.getCurrentPanel()).val();
	$("#hiddenBank",navTab.getCurrentPanel()).val(BankCode);
	$("#PayModeIdId",navTab.getCurrentPanel()).val($("#PayModeId",navTab.getCurrentPanel()).val());
	//受益人为法人取消disabled属性
	var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	var payeeLegalPersonId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
	if($('#contraryPayFlagCheckBoxId').is(':checked')) {
		if(!(payeeLegalPersonId.length > 0)){
			alertMsg.error("请点击领款人的法人信息录入按钮，保存法人信息,或者取消对公支付勾选！");
			return;
		}
	}
	if(beneLegalPersonId.length > 0){
		//受益人法人信息解除置灰
		$("#beneRelation", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#beneHolderRelation", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#beneName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneSex", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#beneBirth", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#beneCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payMole", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payDeno", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#beneCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneEmail", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	}else{
		$("#beneCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	}
	if(payeeLegalPersonId.length > 0){
		//领款人法人信息解除置灰
		$("#payeeRelation", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#payeeName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeName", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#payeeSex", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#payeeEmail", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	}
	var beneCertiType = $("#beneCertiType", navTab.getCurrentPanel()).val(); // 证件类型
	var payeeCertiType = $("#payeeCertiType", navTab.getCurrentPanel()).val(); // 证件类型
	if(beneLegalPersonId.length > 0){
		if(beneCertiType != "73" && beneCertiType != "8"){ //受益人是法人，证件类型只能选营业执照和其他。
			alertMsg.warn('受益人是法人，证件类型只能选营业执照和其他！');
			return;
		}
	}else{
		if(beneCertiType == "73" ){ //受益人不是法人，证件类型不能选营业执照。
			alertMsg.warn('受益人不是法人，证件类型不能选营业执照！');
			return;
		}
	}
	if(payeeLegalPersonId.length > 0){
		if(payeeCertiType != "73" && payeeCertiType != "8"){ //领款人是法人，证件类型只能选营业执照和其他。
			alertMsg.warn('领款人是法人，证件类型只能选营业执照和其他！');
			return;
		}
	}else{
		if(payeeCertiType == "73"){ //领款人不是法人，证件类型不能选营业执照。
			alertMsg.warn('领款人不是法人，证件类型不能选营业执照！');
			return;
		}
	}
	$("input[name='payeeVO.payeeName']", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#payeeName", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#payeeName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#beneBirth", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#beneBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#payeeBirth", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	var beneRelation = $("#beneRelation",navTab.getCurrentPanel()).val();
	if(beneRelation == "00"){
		$("#beneSex",navTab.getCurrentPanel()).removeAttr("disabled");
		if($("#beneSex", navTab.getCurrentPanel()).val()==null||$("#beneSex", navTab.getCurrentPanel()).val()==""){
			$("#beneSex", navTab.getCurrentPanel()).val($("#beneSex", navTab.getCurrentPanel()).prev().val());
		}
	}else{
		if($("#beneSex", navTab.getCurrentPanel()).val()==null||$("#beneSex", navTab.getCurrentPanel()).val()==""){
			$("#beneSex", navTab.getCurrentPanel()).val($("#beneSex", navTab.getCurrentPanel()).prev().val());
		}
	}
	var payeeRelation = $("#payeeRelation",navTab.getCurrentPanel()).val();
	if(payeeRelation == "00"){
		$("#payeeSex",navTab.getCurrentPanel()).removeAttr("disabled");
		if($("#beneSex", navTab.getCurrentPanel()).val()==null||$("#beneSex", navTab.getCurrentPanel()).val()==""){
			$("#payeeSex", navTab.getCurrentPanel()).val($("#beneSex", navTab.getCurrentPanel()).prev().val());
		}
	}else{
		if($("#payeeSex", navTab.getCurrentPanel()).val()==null||$("#payeeSex", navTab.getCurrentPanel()).val()==""){
			$("#payeeSex", navTab.getCurrentPanel()).val($("#payeeSex", navTab.getCurrentPanel()).prev().val());
		}
	}
	//领款人姓名赋值
	$("input[name='payeeVO.payeeName']", navTab.getCurrentPanel()).val($("#payeeName", navTab.getCurrentPanel()).prev().val());
	//身故类赔案校验
	/*var claimTypeFlag = $("#claimTypeFlagBene", navTab.getCurrentPanel()).val();
	if($("#beneRelation", navTab.getCurrentPanel()).val() == "00" && claimTypeFlag == "1") {
		alertMsg.error("身故类赔案，受益人与被保人关系不能为本人");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
			$("#beneRelation", navTab.getCurrentPanel()).focus();
		});
		return;
	}*/
	//校验受益人姓名录入合法性
	if($("#beneName", navTab.getCurrentPanel()).val() != ""){
		if(!checkName($("#beneName", navTab.getCurrentPanel()))){
			return;
		}
	}
	if($("#beneCertiType",navTab.getCurrentPanel()).val()=='h'){
		var beneCertiStart =  $("#beneCertiStart", navTab.getCurrentPanel()).val();
		var beneCertiEnd =  $("#beneCertiEnd", navTab.getCurrentPanel()).val();
		if(!checkEndYear(beneCertiStart,beneCertiEnd)){
			alertMsg.error("证件有效期间应为5年，请重新录入！");
			return;
		}
	}
	if($("#payeeCertiType",navTab.getCurrentPanel()).val()=='h'){
		var payeeCertiStart =  $("#payeeCertiStart", navTab.getCurrentPanel()).val();
		var payeeCertiEnd =  $("#payeeCertiEnd", navTab.getCurrentPanel()).val();
		if(!checkEndYear(payeeCertiStart,payeeCertiEnd)){
			alertMsg.error("证件有效期间应为5年，请重新录入！");
			return;
		}
	}
	if($("#beneCertiType",navTab.getCurrentPanel()).val()=='h'
		|| $("#beneCertiType",navTab.getCurrentPanel()).val()=='b'
		|| $("#beneCertiType",navTab.getCurrentPanel()).val()=='i'
		|| $("#beneCertiType",navTab.getCurrentPanel()).val()=='d'){
		if(!checkCertiNoAndNa($("#beneCertiType",navTab.getCurrentPanel()).val(),$("#beneCertiNo",navTab.getCurrentPanel()).val(),$("#clmtNation",navTab.getCurrentPanel()).val())){
			return;
		}
	}else{
		if(!checkCertiNo($("#beneCertiType",navTab.getCurrentPanel()).val(),$("#beneCertiNo",navTab.getCurrentPanel()).val())){
			return;
		}  
	}
	if($("#payeeCertiType",navTab.getCurrentPanel()).val()=='h'
		|| $("#payeeCertiType",navTab.getCurrentPanel()).val()=='b'
		|| $("#payeeCertiType",navTab.getCurrentPanel()).val()=='i'
		|| $("#payeeCertiType",navTab.getCurrentPanel()).val()=='d'){
		if(!checkCertiNoAndNa($("#payeeCertiType",navTab.getCurrentPanel()).val(),$("#payeeCertiNo",navTab.getCurrentPanel()).val(),$("#clmtNationL",navTab.getCurrentPanel()).val())){
			return;
		}
	}else{
		if(!checkCertiNo($("#payeeCertiType",navTab.getCurrentPanel()).val(),$("#payeeCertiNo",navTab.getCurrentPanel()).val())){
			return;
		}
	}
	if(flag == 2){
		presave = 1;
	}
	//校验受益人身份证号与出生日期，性别是否相符
	if($("#beneCertiType",navTab.getCurrentPanel()).val() == "01"){
		if($("#beneCertiNo",navTab.getCurrentPanel()).val().length == "18"){
			if($("#beneSex",navTab.getCurrentPanel()).val() != "" && $("#beneSex",navTab.getCurrentPanel()).val() % 2 != $("#beneCertiNo",navTab.getCurrentPanel()).val().substr(16,1) % 2){
				alertMsg.error("受益人性别不正确！");
				presave = 0;
				return;
			} else if($("#beneBirth",navTab.getCurrentPanel()).val() != "" && $("#beneBirth",navTab.getCurrentPanel()).val() != ($("#beneCertiNo",navTab.getCurrentPanel()).val().substr(6,4) + "-" + $("#beneCertiNo",navTab.getCurrentPanel()).val().substr(10,2) + "-" + $("#beneCertiNo",navTab.getCurrentPanel()).val().substr(12,2))){
				alertMsg.error("受益人出生日期不正确！");
				presave = 0;
				return;
			}
		}
		if($("#beneCertiNo",navTab.getCurrentPanel()).val().length == "15"){
			if($("#beneSex",navTab.getCurrentPanel()).val() != "" && $("#beneSex",navTab.getCurrentPanel()).val() % 2 != $("#beneCertiNo",navTab.getCurrentPanel()).val().substr(14,1) % 2){
				presave = 0;
				alertMsg.error("受益人性别不正确！");
				return;
			}else if($("#beneBirth",navTab.getCurrentPanel()).val() != "" && $("#beneBirth",navTab.getCurrentPanel()).val().substr(2,8) != ($("#beneCertiNo",navTab.getCurrentPanel()).val().substr(6,2) + "-" + $("#beneCertiNo",navTab.getCurrentPanel()).val().substr(8,2) + "-" + $("#beneCertiNo",navTab.getCurrentPanel()).val().substr(10,2))){
				presave = 0;
				alertMsg.error("受益人出生日期不正确！");
				return;
			}
		}
	}
	//校验领款人姓名录入合法性
//	if (isOCRShow == 1) {
//		if($("#payeeNameId", navTab.getCurrentPanel()).val() != ""){
//			if(!checkName($("#payeeNameId", navTab.getCurrentPanel()))){
//				return;
//			}
//		}
//	} else {
		if($("#payeeName", navTab.getCurrentPanel()).val() != ""){
			if(!checkName($("#payeeName", navTab.getCurrentPanel()))){
				return;
			}
		}
//	}
	//校验领款人身份证号与出生日期，性别是否相符
	if($("#payeeCertiType",navTab.getCurrentPanel()).val() == "01"){
		if($("#payeeCertiNo",navTab.getCurrentPanel()).val().length == "18"){
			if($("#payeeSex",navTab.getCurrentPanel()).val() != "" && $("#payeeSex",navTab.getCurrentPanel()).val() % 2 != $("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(16,1) % 2){
				alertMsg.error("领款人性别不正确！");
				presave = 0;
				return;
			} else if($("#payeeBirth",navTab.getCurrentPanel()).val() != "" && $("#payeeBirth",navTab.getCurrentPanel()).val() != ($("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(6,4) + "-" + $("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(10,2) + "-" + $("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(12,2))){
				alertMsg.error("领款人出生日期不正确！");
				presave = 0;
				return;
			}
		}
		if($("#payeeCertiNo",navTab.getCurrentPanel()).val().length == "15"){
			if($("#payeeSex",navTab.getCurrentPanel()).val() != "" && $("#payeeSex",navTab.getCurrentPanel()).val() % 2 != $("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(14,1) % 2){
				alertMsg.error("领款人性别不正确！");
				presave = 0;
				return;
			} else if ($("#payeeBirth",navTab.getCurrentPanel()).val() != "" && $("#payeeBirth",navTab.getCurrentPanel()).val().substr(2,8) != ($("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(6,2) + "-" + $("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(8,2) + "-" + $("#payeeCertiNo",navTab.getCurrentPanel()).val().substr(10,2))){
				alertMsg.error("领款人出生日期不正确！");
				presave = 0;
				return;
			}
		}
	}
	if($("#accountName", navTab.getCurrentPanel()).val() != ""){
		if(!checkName($("#accountName", navTab.getCurrentPanel()))){
			return;
		}
	}
	//从新计算受益分配金额。
	computePayAmounts();
	if(!checkCustomer()){
		presave = 0;
		return;
	}
	//为本人时，带出领款人的信息
	//getBeneInfoRegi();
	
	if(!getBeneInfoRegi_new()) {
		presave = 0;
		return;
	}
	//校验关系与性别规则
	if($("#beneSex", navTab.getCurrentPanel()).val() != ""){
		var beneRelation = $("#beneRelation",navTab.getCurrentPanel()).val();
		var insuredSex = $("#insuredSex",navTab.getCurrentPanel()).val();
		var beneSex = $("#beneSex", navTab.getCurrentPanel()).val();
		var insuredBirth = $("#insuredBirth",navTab.getCurrentPanel()).val();
		var beneBirth = $("#beneBirth",navTab.getCurrentPanel()).val().replace(/-/g, '/');
		if(beneRelation == "07"){
			if(insuredSex == "1" && beneSex == "1"){
				alertMsg.error("当受益人与出险人关系为夫妻时，关系性别应为男、女搭配");
				presave = 0;
				return;
			} else if(insuredSex == "2" && beneSex == "2"){
				alertMsg.error("当受益人与出险人关系为夫妻时，关系性别应为男、女搭配");
				presave = 0;
				return;
			}
		} else if(beneRelation == "01"){
			if(insuredSex == "2" || beneSex == "2"){
				alertMsg.error("当受益人与出险人关系为父子时，关系性别应为男");
				presave = 0;
				return;
			}
		} else if(beneRelation == "02"){
			if(insuredSex == "1"){
				if(beneSex != "2" || insuredBirth > beneBirth){
					alertMsg.error("当受益人与出险人关系为父女时，关系性别应为男、女搭配，且年长的客户性别须为男，年幼的客户性别须为女");
					presave = 0;
					return;
				}
			} else if(insuredSex =="2"){
				if(beneSex != "1" || insuredBirth < beneBirth){
					alertMsg.error("当受益人与出险人关系为父女时，关系性别应为男、女搭配，且年长的客户性别须为男，年幼的客户性别须为女");
					presave = 0;
					return;
				}
			}
		} else if(beneRelation =="04"){
			if(insuredSex == "1" || beneSex == "1"){
				alertMsg.error("当受益人与出险人关系为母女时，关系性别应为女");
				presave = 0;
				return;
			}
		} else if(beneRelation == "03"){
			if(insuredSex == "1"){
				if(beneSex != "2" || insuredBirth < beneBirth){
					alertMsg.error("当受益人与出险人关系为母子时，关系性别应为男、女搭配，且年长的客户性别须为女，年幼的客户性别须为男");
					presave = 0;
					return;
				}
			} else if(insuredSex =="2"){
				if(beneSex != "1" || insuredBirth > beneBirth){
					alertMsg.error("当受益人与出险人关系为母子时，关系性别应为男、女搭配，且年长的客户性别须为女，年幼的客户性别须为男");
					presave = 0;
					return;
				}
			}
		}
	}
	if($("#payeeSex", navTab.getCurrentPanel()).val() != ""){
		var payeeRelation = $("#payeeRelation",navTab.getCurrentPanel()).val();
		var payeeSex = $("#payeeSex", navTab.getCurrentPanel()).val();
		var beneSex = $("#beneSex", navTab.getCurrentPanel()).val();
		var beneBirth = $("#beneBirth",navTab.getCurrentPanel()).val().replace(/-/g, '/');
		var payeeBirth = $("#payeeBirth" , navTab.getCurrentPanel()).val();
		if(payeeRelation == "07"){
			if(beneSex == "1" && payeeSex == "1"){
				alertMsg.error("当领款人与受益人关系为夫妻时，关系性别应为男、女搭配");
				presave = 0;
				return;
			} else if(beneSex == "2" && payeeSex == "2"){
				alertMsg.error("当领款人与受益人关系为夫妻时，关系性别应为男、女搭配");
				presave = 0;
				return;
			}
		} else if(payeeRelation == "01"){
			if(beneSex == "2" || payeeSex == "2"){
				alertMsg.error("当领款人与受益人关系为父子时，关系性别应为男");
				presave = 0;
				return;
			}
		} else if(payeeRelation == "02"){
			if(beneSex == "1"){
				
				if(payeeSex != "2" || beneBirth > payeeBirth){
					alertMsg.error("当领款人与受益人关系为父女时，关系性别应为男、女搭配，且年长的客户性别须为男，年幼的客户性别须为女");
					presave = 0;
					return;
				}
			} else if(beneSex =="2"){
				if(payeeSex != "1" || beneBirth < payeeBirth){
					alertMsg.error("当领款人与受益人关系为父女时，关系性别应为男、女搭配，且年长的客户性别须为男，年幼的客户性别须为女");
					presave = 0;
					return;
				}
			}
		} else if(payeeRelation =="04"){
			if(beneSex == "1" || payeeSex == "1"){
				alertMsg.error("当领款人与受益人关系为母女时，关系性别应为女");
				presave = 0;
				return;
			}
		} else if(payeeRelation == "03"){
			if(beneSex == "1"){
				if(payeeSex != "2" || beneBirth < payeeBirth){
					alertMsg.error("当领款人与受益人关系为母子时，关系性别应为男、女搭配，且年长的客户性别须为女，年幼的客户性别须为男");
					presave = 0;
					return;
				}
			} else if(beneSex =="2"){
				if(payeeSex != "1" || beneBirth > payeeBirth){
					alertMsg.error("当领款人与受益人关系为母子时，关系性别应为男、女搭配，且年长的客户性别须为女，年幼的客户性别须为男");
					presave = 0;
					return;
				}
			}
		}
	}
	//领款人姓名中含有“法院”二字时，领款人与受益人关系必须为"其他"，否则保存时阻断提示
	if($("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") != -1 || ($("#payeeName", navTab.getCurrentPanel()).val()!=null && $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") != -1)){
		var payeeRelation = $("#payeeRelation",navTab.getCurrentPanel()).val();
		if(payeeRelation != "22"){
			alertMsg.error("当领款人为法院时，领款人与受益人关系必须为“其他”");
			presave = 0;
			return;
		}
	}
	
	//受益人、领款人姓名及银行账户去除前后空格处理
	$("#payeeName", navTab.getCurrentPanel()).val($("#payeeName", navTab.getCurrentPanel()).val().trim());
	$("#payeeName", navTab.getCurrentPanel()).prev().val($("#payeeName", navTab.getCurrentPanel()).prev().val().trim());
	$("#beneName", navTab.getCurrentPanel()).val($("#beneName", navTab.getCurrentPanel()).val().trim());
	$("#accountName", navTab.getCurrentPanel()).val($("#accountName", navTab.getCurrentPanel()).val().trim());
	
	var payeeCertiEnd = $("#payeeCertiEnd",navTab.getCurrentPanel()).val().replace(/-/g, '/');//领款人证件止期
	var beneCertiEnd = $("#beneCertiEnd",navTab.getCurrentPanel()).val().replace(/-/g, '/');//受益人证件止期
	var payeeCertiStart = $("#payeeCertiStart",navTab.getCurrentPanel()).val().replace(/-/g, '/');//领款人证件起期
	var beneCertiStart = $("#beneCertiStart",navTab.getCurrentPanel()).val().replace(/-/g, '/');//受益人证件起期
	var systemDateStringId = $("#systemDateStringId",navTab.getCurrentPanel()).val().replace(/-/g, '/');//当前时间
	
	var beneIsNotNull = $("#beneIsNotNull",navTab.getCurrentPanel()).val();
	//71408 金额大于等于10000必填
	var actualPay = $("#actualPay",navTab.getCurrentPanel()).val();
	if($('#beneCertiEndCheckBoxId').is(':checked')){
		$("#isBeneExtended").val("1");
	}else{
		$('#beneCertiEndCheckBoxId').val($("#beneCertiEnd").val());
	}
	if($('#payeeCertiEndCheckBoxId').is(':checked')){
		$("#isPayeeExtended").val("1");
	}else{
		$('#payeeCertiEndCheckBoxId').val($("#payeeCertiEnd").val());
	}
	if($('#contraryPayFlagCheckBoxId').is(':checked')) {
		$("#contraryPayFlag", navTab.getCurrentPanel()).val("1");
	}else{
		$("#contraryPayFlag", navTab.getCurrentPanel()).val("0");	
	}
	if ($("#beneRelation", navTab.getCurrentPanel()).val() == "" || $("#beneRelation", navTab.getCurrentPanel()).val() == null) {
		alertMsg.error("受益人与被保人关系必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
			$("#beneRelation", navTab.getCurrentPanel()).focus();
		});
	} else if ($("#beneName", navTab.getCurrentPanel()).val() == "" || $("#beneName", navTab.getCurrentPanel()).val() == null) {
		alertMsg.error("受益人姓名必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
			$("#beneName", navTab.getCurrentPanel()).focus();
		});
	} else if(($("#beneSex", navTab.getCurrentPanel()).val() == "" || $("#beneSex", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人性别必填");presave = 0;
	} else if(($("#beneBirth", navTab.getCurrentPanel()).val() == "" || $("#beneBirth", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){ 
		alertMsg.error("受益人出生日期必填");presave = 0;return;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#beneBirth", navTab.getCurrentPanel()).focus();
		});
	} else if(($("#beneCertiType", navTab.getCurrentPanel()).val() == "" || $("#beneCertiType", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
				alertMsg.error("受益人证件类型必填");presave = 0;
	} else if($("#beneCertiNo", navTab.getCurrentPanel()).val() == "" || $("#beneCertiNo", navTab.getCurrentPanel()).val() == null){
		alertMsg.error("受益人证件号码必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#beneCertiNo", navTab.getCurrentPanel()).focus();
		});
	} else if($("#beneCertiStart" , navTab.getCurrentPanel()).val() == "" || $("#beneCertiStart" , navTab.getCurrentPanel()).val() == null){
		alertMsg.error("受益人证件起期必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#beneCertiStart", navTab.getCurrentPanel()).focus();
		});
	} else if((($("#beneCertiEnd" , navTab.getCurrentPanel()).val() == "" || $("#beneCertiEnd" , navTab.getCurrentPanel()).val() == null) && !$('#beneCertiEndCheckBoxId').is(':checked'))||(($("#beneCertiType", navTab.getCurrentPanel()).val()==4||$("#beneCertiType", navTab.getCurrentPanel()).val()==0)&&!$('#beneCertiEndCheckBoxId').is(':checked')&&($("#beneCertiEnd" , navTab.getCurrentPanel()).val() == "" || $("#beneCertiEnd" , navTab.getCurrentPanel()).val() == null))){
		alertMsg.error("受益人证件止期必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
			$("#beneCertiEnd", navTab.getCurrentPanel()).focus();
		});
	}else if ($("#beneCertiType", navTab.getCurrentPanel()).val() == "e"&&$.trim($("#beneCertiStart" , navTab.getCurrentPanel()).val()) != ""&&$.trim($("#beneCertiEnd" , navTab.getCurrentPanel()).val()) != ""&&!checkCertiDate($("#beneCertiStart" , navTab.getCurrentPanel()).val(),$("#beneCertiEnd" , navTab.getCurrentPanel()).val())) { 
		return false;
	}else if ($("#beneCertiType", navTab.getCurrentPanel()).val() == "e" && !checkCertiNoToE($("#beneCertiNo", navTab.getCurrentPanel()).val(), $("#beneBirth", navTab.getCurrentPanel()).val())){
		return false;
	} else if(beneCertiStart >= systemDateStringId){ //受益人证件起期小于当前时间阻断提示
		alertMsg.error("当前操作日期不得早于等于受益人的证件有效起期。");presave = 0;
	}  else if(!$('#beneCertiEndCheckBoxId').is(':checked')&&beneCertiEnd <= systemDateStringId){ //受益人证件止期大于当前时间阻断提示
		alertMsg.error("受益人的证件有效止期不得早于等于当前操作日期。");presave = 0;
	} else if($("#payMole" , navTab.getCurrentPanel()).val() == "" || $("#payMole" , navTab.getCurrentPanel()).val() == null){
		alertMsg.error("受益分子必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#payMole", navTab.getCurrentPanel()).focus();
		});
	} else if($("#payDeno" , navTab.getCurrentPanel()).val() == "" || $("#payDeno" , navTab.getCurrentPanel()).val() == null){
		alertMsg.error("受益分母必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#payDeno", navTab.getCurrentPanel()).focus();
		});
	} else if (!beneRateFlag) {
		alertMsg.error("受益金额为空，受益分子、分母录入有问题，请重新输入！");presave = 0;
	} else if (beneIsNotNull==1 && actualPay>=10000 &&($("#beneJobIdFire", navTab.getCurrentPanel()).val() == "" || $("#beneJobIdFire", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("赔案的最终给付金额大于等于1万，受益人/领款人国籍、职业必录。");presave = 0;
	} else
		if (beneIsNotNull==1&&($("#benePhone", navTab.getCurrentPanel()).val() == "" || $("#benePhone", navTab.getCurrentPanel()).val() == null)
				&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人电话必填！");presave = 0;
	} else if (beneIsNotNull==1&&($("#beneProvince", navTab.getCurrentPanel()).val() == "" || $("#beneProvince", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人省必填！");presave = 0;			
	} else if (beneIsNotNull==1&&($("#beneCity", navTab.getCurrentPanel()).val() == "" || $("#beneCity", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人市必填！");presave = 0;
	} else if (beneIsNotNull==1&&($("#beneDistrict", navTab.getCurrentPanel()).val() == "" || $("#beneDistrict", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人区/县必填！");presave = 0;
	} else if (beneIsNotNull==1&&($("#beneAddress", navTab.getCurrentPanel()).val() == "" || $("#beneAddress", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人乡镇/街道必填！");presave = 0;
	} else if ($("#beneHolderRelation", navTab.getCurrentPanel()).val() == "" || $("#beneHolderRelation", navTab.getCurrentPanel()).val() == null) {
		alertMsg.error("投保人与受益人关系必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
			$("#beneHolderRelation", navTab.getCurrentPanel()).focus();
		});
	} else if (actualPay>=10000 &&($("#payeeJobIdFire", navTab.getCurrentPanel()).val() == "" || $("#payeeJobIdFire", navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("赔案的最终给付金额大于等于1万，受益人/领款人国籍、职业必录。");presave = 0;
	} else if (($("#payeePhone", navTab.getCurrentPanel()).val() == "" || $("#payeePhone", navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
		alertMsg.error("领款人电话必填！");presave = 0;
	} else if (($("#payeeProvince", navTab.getCurrentPanel()).val() == "" || $("#payeeProvince", navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人省必填！");presave = 0;
	} else if (($("#payeeCity", navTab.getCurrentPanel()).val() == "" || $("#payeeCity", navTab.getCurrentPanel()).val() == null)
		&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
		&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
		&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人市必填！");presave = 0;
	} else if (($("#payeeDistrict", navTab.getCurrentPanel()).val() == "" || $("#payeeDistrict", navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人区/县必填！");presave = 0;
	} else if (($("#payeeAddress", navTab.getCurrentPanel()).val() == "" || $("#payeeAddress", navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人乡镇/街道必填！");presave = 0;
	} else if($("#payeeRelation" , navTab.getCurrentPanel()).val() == "" || $("#payeeRelation" , navTab.getCurrentPanel()).val() == null){
		alertMsg.error("领款人与受益人关系必填");presave = 0;
	} else if(($("#payeeSex" , navTab.getCurrentPanel()).val() == "" || $("#payeeSex" , navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人性别必填");presave = 0;
	} else if(($("#payeeBirth" , navTab.getCurrentPanel()).val() == "" || $("#payeeBirth" , navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
		alertMsg.error("领款人出生日期必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#payeeBirth", navTab.getCurrentPanel()).focus();
		});
	} else if(($("#payeeCertiType" , navTab.getCurrentPanel()).val() == "" || $("#payeeCertiType" , navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人证件类型必填");presave = 0;
	} else if(($("#payeeCertiNo" , navTab.getCurrentPanel()).val() == "" || $("#payeeCertiNo" , navTab.getCurrentPanel()).val() == null)
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
		alertMsg.error("领款人证件号码必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#payeeCertiNo", navTab.getCurrentPanel()).focus();
		});
	} else if(($("#payeeCertiStart" , navTab.getCurrentPanel()).val() == "" || $("#payeeCertiStart" , navTab.getCurrentPanel()).val() == null)
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
		alertMsg.error("领款人证件起期必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#payeeCertiStart", navTab.getCurrentPanel()).focus();
		});
	} else if(((($("#payeeCertiEnd" , navTab.getCurrentPanel()).val() == "" || $("#payeeCertiEnd" , navTab.getCurrentPanel()).val() == null) && !$('#payeeCertiEndCheckBoxId').is(':checked'))||(($("#payeeCertiType", navTab.getCurrentPanel()).val()==4||$("#payeeCertiType", navTab.getCurrentPanel()).val()==0)&&!$('#payeeCertiEndCheckBoxId').is(':checked')&&($("#payeeCertiEnd" , navTab.getCurrentPanel()).val() == "" || $("#payeeCertiEnd" , navTab.getCurrentPanel()).val() == null)))
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
		alertMsg.error("领款人证件止期必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
			$("#payeeCertiEnd", navTab.getCurrentPanel()).focus();
		});
	}else if ($("#payeeCertiType" , navTab.getCurrentPanel()).val() == "e"&&$.trim($("#payeeCertiStart" , navTab.getCurrentPanel()).val()) != ""&&$.trim($("#payeeCertiEnd" , navTab.getCurrentPanel()).val()) != ""&&!checkCertiDate($("#payeeCertiStart" , navTab.getCurrentPanel()).val(),$("#payeeCertiEnd" , navTab.getCurrentPanel()).val())) { 
		return false;
	}else if ($("#payeeCertiType" , navTab.getCurrentPanel()).val() == "e" && !checkCertiNoToE($("#payeeCertiNo" , navTab.getCurrentPanel()).val(), $("#payeeBirth" , navTab.getCurrentPanel()).val())){
		return false;
	} else if(($("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 && $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1) 
			&& payeeCertiStart >= systemDateStringId){ //领款人证件起期小于当前时间阻断提示
		alertMsg.error("当前操作日期不得早于等于领款人的证件有效止期。");presave = 0;
	} else if(($("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 && $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1) 
			&&!$('#payeeCertiEndCheckBoxId').is(':checked')&&payeeCertiEnd <= systemDateStringId){ //领款人证件止期大于当前时间阻断提示
			alertMsg.error("领款人的证件有效止期不得早于等于当前操作日期。");presave = 0;
	} else if($("#PayModeId", navTab.getCurrentPanel()).val() == "" || $("#PayModeId", navTab.getCurrentPanel()).val()== null){
		alertMsg.error("支付方式必填");presave = 0;
	} else if ($("#payeeName", navTab.getCurrentPanel()).prev().val() == "" || $("#payeeName", navTab.getCurrentPanel()).val() == null) {
		alertMsg.error("领款人姓名必填");presave = 0;
		$("#alertMsgBox .toolBar .button").off().on("click",function(){
		    $("#payeeName", navTab.getCurrentPanel()).focus();
		});
	} else if ($("#payMode", navTab.getCurrentPanel()).val() == "" || $("#payMode", navTab.getCurrentPanel()).val() == "") {
		alertMsg.error("支付方式必填");presave = 0;
	} else if (actualPay>=10000 && ($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val() == "" || $("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val() == null)
			&& ($("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#payeeLegalPersonId", navTab.getCurrentPanel()).val() == "")
			&& $("#payeeName", navTab.getCurrentPanel()).val().indexOf("法院") == -1 
			&& $("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
			alertMsg.error("领款人国籍必填");presave = 0;
	} else if (actualPay>=10000 && ($("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).val() == "" || $("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).val() == null)
			&& ($("#beneLegalPersonId", navTab.getCurrentPanel()).val() == null || $("#beneLegalPersonId", navTab.getCurrentPanel()).val() == "")){
			alertMsg.error("受益人国籍必填");presave = 0;
	}else if ($("#beneName", navTab.getCurrentPanel()).val() == $("#payeeName", navTab.getCurrentPanel()).prev().val()) {
		if ($("#beneCertiNo", navTab.getCurrentPanel()).val() != $("#payeeCertiNo", navTab.getCurrentPanel()).val()) {
			alertMsg.error("同一领款人/受益人，证件号码必须一致");presave = 0;
		} else if ($("#PayModeId", navTab.getCurrentPanel()).val() == "32" || $("#PayModeId", navTab.getCurrentPanel()).val() == "40" || $("#PayModeId", navTab.getCurrentPanel()).val() == "34" || $("#PayModeId", navTab.getCurrentPanel()).val() == "22") {
			var bankCodeId = $("#bankCodeId", navTab.getCurrentPanel()).prev().val();
			var bankCodeIdArray = bankCodeId.split("-");
			//网上银行，开户行不能为空
			if($("#PayModeId", navTab.getCurrentPanel()).val() == "34" || $("#PayModeId", navTab.getCurrentPanel()).val() == "22"){
				if ($("#bankOfDepositId", navTab.getCurrentPanel()).val() == null || $("#bankOfDepositId", navTab.getCurrentPanel()).val() == "") {
					//alertMsg.error("支付方式为网上银行时,开户行名称必填");
					alertMsg.error("必须录入受益人，领款人及其银行账户信息");presave = 0;
				}
			}
			if(bankCodeIdArray[1]==null){
				alertMsg.error("银行编码字段录入不合法!");
				return false;
			}
			if(bankCodeIdArray[1]!=null){
				if((!/^[\d]+$/.test(bankCodeIdArray[0])&&bankCodeId.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(bankCodeIdArray[1]))||(bankCodeId.indexOf("-") == -1)||bankCodeId=="-请选择"){
					alertMsg.error("银行编码字段录入不合法!");
					return false;
				} 
			}
			if($("#bankCodeId", navTab.getCurrentPanel()).val() == ""){
				//alertMsg.error("支付方式为银行转帐（制返盘）/ 实时收费/网上银行时,银行编码必填");
				alertMsg.error("必须录入受益人，领款人及其银行账户信息");presave = 0;
			} else if ($("#accountName", navTab.getCurrentPanel()).val() == null || $("#accountName", navTab.getCurrentPanel()).val() == "") {
				//alertMsg.error("支付方式为银行转帐（制返盘）/ 实时收费/网上银行时,银行账户名必填");
				alertMsg.error("必须录入受益人，领款人及其银行账户信息");presave = 0;
			} else if ($("#accountNo", navTab.getCurrentPanel()).val() == null || $("#accountNo", navTab.getCurrentPanel()).val() == "") {
				//alertMsg.error("支付方式为银行转帐（制返盘）/ 实时收费/网上银行时,银行账号必填");
				alertMsg.error("必须录入受益人，领款人及其银行账户信息");presave = 0;
			} else if ($("#payeeName", navTab.getCurrentPanel()).prev().val() != $("#accountName", navTab.getCurrentPanel()).val()) {
				alertMsg.error("领款人姓名与银行账户持有人姓名必须一致");presave = 0;
			} else {
				$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/saveBenePayeeInf_CLM_toBenefitInputPageAction.action");
				$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, idAjaxDone)");
				$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
			}
		}else {
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/saveBenePayeeInf_CLM_toBenefitInputPageAction.action");
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, idAjaxDone)");
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
		}
	} else if ($("#PayModeId", navTab.getCurrentPanel()).val() == "32" || $("#PayModeId", navTab.getCurrentPanel()).val() == "40" || $("#PayModeId", navTab.getCurrentPanel()).val() == "34" || $("#PayModeId", navTab.getCurrentPanel()).val() == "22") {
		var bankCodeId = $("#bankCodeId", navTab.getCurrentPanel()).prev().val();
		var bankCodeIdArray = bankCodeId.split("-");
		if(bankCodeIdArray[1]==null){
			alertMsg.error("银行编码字段录入不合法!");
			return false;
		}
		if(bankCodeIdArray[1]!=null){
			if((!/^[\d]+$/.test(bankCodeIdArray[0])&&bankCodeId.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(bankCodeIdArray[1]))||(bankCodeId.indexOf("-") == -1)||bankCodeId=="-请选择"){
				alertMsg.error("银行编码字段录入不合法!");
				return false;
			} 
		}
		//网上银行，开户行必填
		if($("#PayModeId", navTab.getCurrentPanel()).val() == "34" || $("#PayModeId", navTab.getCurrentPanel()).val() == "22"){
			if ($("#bankOfDepositId", navTab.getCurrentPanel()).val() == null || $("#bankOfDepositId", navTab.getCurrentPanel()).val() == "") {
				//alertMsg.error("支付方式为网上银行时,开户行名称必填");
				alertMsg.error("必须录入受益人，领款人及其银行账户信息");presave = 0;
			}
		}
		if($("#bankCodeId", navTab.getCurrentPanel()).val() == ""){
			//alertMsg.error("支付方式为银行转帐（制返盘）/ 实时收费时,银行编码必填");
			alertMsg.error("必须录入受益人，领款人及其银行账户信息");
		} else if ($("#accountName", navTab.getCurrentPanel()).val() == null || $("#accountName", navTab.getCurrentPanel()).val() == "") {
			//alertMsg.error("支付方式为银行转帐（制返盘）/ 实时收费时,银行账户名必填");
			alertMsg.error("必须录入受益人，领款人及其银行账户信息");
		} else if ($("#accountNo", navTab.getCurrentPanel()).val() == null || $("#accountNo", navTab.getCurrentPanel()).val() == "") {
			//alertMsg.error("支付方式为银行转帐（制返盘）/ 实时收费时,银行账号必填");
			alertMsg.error("必须录入受益人，领款人及其银行账户信息");
		} else if ($("#payeeName", navTab.getCurrentPanel()).prev().val() != $("#accountName", navTab.getCurrentPanel()).val()) {
			alertMsg.error("领款人姓名与银行账户持有人姓名必须一致");
		} else {
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/saveBenePayeeInf_CLM_toBenefitInputPageAction.action");
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, idAjaxDone)");
			$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
		}
	}else {
		$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("action","clm/register/saveBenePayeeInf_CLM_toBenefitInputPageAction.action");
		$("#benePayeeInfoFor", navTab.getCurrentPanel()).attr("onsubmit","return validateCallbackBankCodeCom(this, idAjaxDone)");
		$("#benePayeeInfoFor", navTab.getCurrentPanel()).submit();
	}
}

//根据省查询市
function ProvinceChangeReportData(k) {
	//获取受益人与领款人关系---本人赋值受益人信息
	var payeeRelation = $("#payeeRelation", navTab.getCurrentPanel()).val();
	
	var cityReportId = $("#beneCity", navTab.getCurrentPanel());
	var province = $(k).val();
	$("#beneProvinceReserve", navTab.getCurrentPanel()).val($(k).val());
	$("#beneCity", navTab.getCurrentPanel()).prev().val("");
	$("#beneDistreact", navTab.getCurrentPanel()).prev().val("");
	
	if(payeeRelation == "00"){
		$("#payeeProvinceReserve", navTab.getCurrentPanel()).val($(k).val());
		$("#payeeCity", navTab.getCurrentPanel()).prev().val("");
		$("#payeeDistrict", navTab.getCurrentPanel()).prev().val("");
	}
	
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ province,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#beneCity", navTab.getCurrentPanel()).empty();
					$("#beneDistrict", navTab.getCurrentPanel()).empty();
					$("<option value=''>市</option>").appendTo(cityReportId);
					
					if(payeeRelation == "00"){
						$("#payeeCity", navTab.getCurrentPanel()).empty();
						$("#payeeDistrict", navTab.getCurrentPanel()).empty();
						$("<option value=''>市</option>").appendTo($("#payeeCity", navTab.getCurrentPanel()));
					}
					
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(cityReportId);
						
						if(payeeRelation == "00"){
							$(option1).appendTo($("#payeeCity", navTab.getCurrentPanel()));
						}
						
					}
				},
			});
	
}
//根据市查询县
function cityChageReportData(k) {
	//获取受益人与领款人关系---本人赋值受益人信息
	var payeeRelation = $("#payeeRelation", navTab.getCurrentPanel()).val();
	
	$("#beneCityReserve",navTab.getCurrentPanel()).val($(k).val());
	$("#beneDistreact",navTab.getCurrentPanel()).prev().val("");
	var city = $(k).val();
	var beneDistrict = $("#beneDistrict", navTab.getCurrentPanel());
	
	if(payeeRelation == "00"){
		$("#payeeCityReserve", navTab.getCurrentPanel()).val($(k).val());
		$("#payeeDistrict", navTab.getCurrentPanel()).prev().val("");
	}
	
	
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#beneDistrict", navTab.getCurrentPanel()).empty();
					$("<option value=''>区/县</option>").appendTo(beneDistrict);
					
					if(payeeRelation == "00"){
						$("#payeeDistrict", navTab.getCurrentPanel()).empty();
						$("<option value=''>区/县</option>").appendTo($("#payeeDistrict", navTab.getCurrentPanel()));
					}
					
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(beneDistrict);

						if(payeeRelation == "00"){
							$(option1).appendTo($("#payeeDistrict", navTab.getCurrentPanel()));
						}
						
					}
				},
			});
}
//为后台备用传值字段赋值
function DistreactChageReportData(k) {
	$("#beneDistreactReserve", navTab.getCurrentPanel()).val($(k).val());
}

var beneDistreactReserve = $("#beneProvinceReserve", navTab.getCurrentPanel()).val();
var beneCityReserve = $("#beneCityReserve", navTab.getCurrentPanel()).val();
var beneDistreactReserve = $("#beneDistreactReserve", navTab.getCurrentPanel()).val();


//初始化赋值市
var beneCityReportId = $("#beneCity", navTab.getCurrentPanel());
var beneAreaReportId = $("#beneDistrict", navTab.getCurrentPanel());
function cityAssign(province,city,area){
	//获取受益人与领款人关系---本人赋值受益人信息
	var payeeRelation = $("#payeeRelation", navTab.getCurrentPanel()).val();
	$.ajax({
		'type' : 'post',
		'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
				+ province,
		'datatype' : 'json',
		'async' : true,
		'success' : function(data) {
			var data = eval("(" + data + ")");
			$("#beneCity", navTab.getCurrentPanel())
					.empty();
			$(beneCityReportId).append(
					"<option value=''>市</option>");
			
			//如果关系是本人 赋值领款人
			if(payeeRelation == "00"){
				$("#payeeCity", navTab.getCurrentPanel()).empty();
				$("#payeeCity", navTab.getCurrentPanel()).append("<option value=''>市</option>");
			}
			
			for (var i = 0; i < data.length; i++) {
				if (data[i].code == city) {
					$("#beneCity", navTab.getCurrentPanel())
							.prev().val(
									data[i].code + "-"
											+ data[i].name);
					$(beneCityReportId).append(
							"<option value='" + city
									+ "' class = '"
									+ data[i].name + "'>"
									+ data[i].name
									+ "</option>");
				}
				
				
				//如果关系是本人 赋值领款人
				if(payeeRelation == "00"){
					if (data[i].code == city) {
						$("#payeeCity", navTab.getCurrentPanel())
								.prev().val(
										data[i].code + "-"
												+ data[i].name);
						$("#payeeCity", navTab.getCurrentPanel()).append("<option value='" + city
										+ "' class = '"+ data[i].name + "'>"+ data[i].name
										+ "</option>");
					}
					
				}
				
				
			}
			for (var i = 0; i < data.length; i++) {
				if (data[i].code != city) {
					var option1 = "<option value='"
							+ data[i].code + "'   class='"
							+ data[i].name + "' >"
							+ data[i].name + "</option>";
					$(beneCityReportId).append(option1);
				}
				
				//如果关系是本人 赋值领款人
				if(payeeRelation == "00"){
					if (data[i].code != city) {
						var option1 = "<option value='"
								+ data[i].code + "'   class='"
								+ data[i].name + "' >"
								+ data[i].name + "</option>";
						$("#payeeCity", navTab.getCurrentPanel()).append(option1);
					}
				}
				
				
			}
			$("#beneCity", navTab.getCurrentPanel()).val(
					city);
			
			//如果关系是本人 赋值领款人
			if(payeeRelation == "00"){
				$("#payeeCity", navTab.getCurrentPanel()).val(city);
			}
			
		},
	});
	setTimeout("areaAssign(" + city + " , "+ area +")", 0.0003);
	
}
//初始化赋值市
function areaAssign (city,area){
	//获取受益人与领款人关系---本人赋值受益人信息
	var payeeRelation = $("#payeeRelation", navTab.getCurrentPanel()).val();
	
	// 获取市为县赋值
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#beneDistrict", navTab.getCurrentPanel()).empty();
					$(beneAreaReportId).append("<option value=''>区/县</option>");
					
					//如果关系是本人 赋值领款人
					if(payeeRelation == "00"){
						$("#payeeDistrict", navTab.getCurrentPanel()).empty();
						$("#payeeDistrict", navTab.getCurrentPanel()).append("<option value=''>市</option>");
					}
					
					
					for (var i = 0; i < data.length; i++) {
						if (data[i].code == area) {
							$("#beneDistrict", navTab.getCurrentPanel()).prev()
									.val(data[i].code + "-" + data[i].name);
							$(beneAreaReportId).append(
									"<option value='" + area + "' class = '"
											+ data[i].name + "'>"
											+ data[i].name + "</option>");
						}
						
						//如果关系是本人 赋值领款人
						if(payeeRelation == "00"){
							if (data[i].code == area) {
								$("#payeeDistrict", navTab.getCurrentPanel()).prev().val(data[i].code + "-" + data[i].name);
								$("#payeeDistrict", navTab.getCurrentPanel()).append(
								"<option value='" + area + "' class = '"
										+ data[i].name + "'>"
										+ data[i].name + "</option>");
							}
						}
						
						
					}
					for (var i = 0; i < data.length; i++) {
						if (data[i].code != area) {
							var option1 = "<option value='" + data[i].code
									+ "'   class='" + data[i].name + "' >"
									+ data[i].name + "</option>";
							$(beneAreaReportId).append(option1);
						}
						
						//如果关系是本人 赋值领款人
						if(payeeRelation == "00"){
							if (data[i].code != area) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$("#payeeDistrict", navTab.getCurrentPanel()).append(option1);
							}
						}
						
						
					}
					$("#beneDistrict", navTab.getCurrentPanel()).val(area);
					//如果关系是本人 赋值领款人
					if(payeeRelation == "00"){
						$("#payeeDistrict", navTab.getCurrentPanel()).val(area);
					}
	
				},
			});
}


//71408 理赔反洗钱优化
//根据省查询市
function payeeProvinceChangeReportData(k) {
	var cityReportId = $("#payeeCity", navTab.getCurrentPanel());
	var province = $(k).val();
	$("#payeeProvinceReserve", navTab.getCurrentPanel()).val($(k).val());
	$("#payeeCity", navTab.getCurrentPanel()).prev().val("");
	$("#payeeDistreact", navTab.getCurrentPanel()).prev().val("");
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ province,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#payeeCity", navTab.getCurrentPanel()).empty();
					$("#payeeDistrict", navTab.getCurrentPanel()).empty();
					$("<option value=''>市</option>").appendTo(cityReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(cityReportId);
					}
				},
			});
	
}
//根据市查询县
function payeeCityChageReportData(k) {
	$("#payeeCityReserve",navTab.getCurrentPanel()).val($(k).val());
	$("#payeeDistreact",navTab.getCurrentPanel()).prev().val("");
	var city = $(k).val();
	var payeeDistrict = $("#payeeDistrict", navTab.getCurrentPanel());
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#payeeDistrict", navTab.getCurrentPanel()).empty();
					$("<option value=''>区/县</option>").appendTo(payeeDistrict);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(payeeDistrict);
					}
				},
			});
}
//为后台备用传值字段赋值
function payeeDistreactChageReportData(k) {
	$("#payeeDistreactReserve", navTab.getCurrentPanel()).val($(k).val());
}

var payeeCityReserve = $("#payeeCityReserve", navTab.getCurrentPanel()).val();
var payeeDistreactReserve = $("#payeeDistreactReserve", navTab.getCurrentPanel()).val();


//初始化赋值市
var payeeCityReportId = $("#payeeCity", navTab.getCurrentPanel());
var payeeAreaReportId = $("#payeeDistrict", navTab.getCurrentPanel());
function payeeCityAssign(province,city,area){
	
	$.ajax({
		'type' : 'post',
		'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
				+ province,
		'datatype' : 'json',
		'async' : true,
		'success' : function(data) {
			var data = eval("(" + data + ")");
			$("#payeeCity", navTab.getCurrentPanel())
					.empty();
			$("#payeeCity", navTab.getCurrentPanel()).append(
					"<option value=''>市</option>");
			for (var i = 0; i < data.length; i++) {
				if (data[i].code == city) {
					$("#payeeCity", navTab.getCurrentPanel())
							.prev().val(
									data[i].code + "-"
											+ data[i].name);
					$("#payeeCity", navTab.getCurrentPanel()).append(
							"<option value='" + city
									+ "' class = '"
									+ data[i].name + "'>"
									+ data[i].name
									+ "</option>");
				}
			}
			for (var i = 0; i < data.length; i++) {
				if (data[i].code != city) {
					var option1 = "<option value='"
							+ data[i].code + "'   class='"
							+ data[i].name + "' >"
							+ data[i].name + "</option>";
					$("#payeeCity", navTab.getCurrentPanel()).append(option1);
				}
			}
			$("#payeeCity", navTab.getCurrentPanel()).val(
					city);
		},
	});
	setTimeout("payeeAreaAssign(" + city + " , "+ area +")", 0.0003);
	
}
//初始化赋值市
function payeeAreaAssign (city,area){
	// 获取市为县赋值
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#payeeDistrict", navTab.getCurrentPanel()).empty();
					$("#payeeDistrict", navTab.getCurrentPanel()).append("<option value=''>区/县</option>");
					for (var i = 0; i < data.length; i++) {
						if (data[i].code == area) {
							$("#payeeDistrict", navTab.getCurrentPanel()).prev()
									.val(data[i].code + "-" + data[i].name);
							$("#payeeDistrict", navTab.getCurrentPanel()).append(
									"<option value='" + area + "' class = '"
											+ data[i].name + "'>"
											+ data[i].name + "</option>");
						}
					}
					for (var i = 0; i < data.length; i++) {
						if (data[i].code != area) {
							var option1 = "<option value='" + data[i].code
									+ "'   class='" + data[i].name + "' >"
									+ data[i].name + "</option>";
							$("#payeeDistrict", navTab.getCurrentPanel()).append(option1);
						}
					}
					$("#payeeDistrict", navTab.getCurrentPanel()).val(area);
				},
			});
}


function preSave() {
	var a = 0;
	var trs = $("#claimBeneTbody",navTab.getCurrentPanel()).find("tr");
	if($("#acceptDecision", navTab.getCurrentPanel()).val() == "2"){
		var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
		prev('15',caseId);
		return;
	}else{
		if(trs.length > 0){
			for(var i = 0; i < trs.length; i++){
				if($("#claimBeneTbody",navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input").attr("checked") != "checked"){
					var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
					prev('15',caseId);
					return;
				}
			}
		}else if(trs.length == 0){
			var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
			prev('15',caseId);
			return;
		}
			$("[type='text']",navTab.getCurrentPanel()).each(function(){
				if($(this).val()!="" && $(this).attr("name")!="residueRate" && $(this).attr("name")!="paymentPlanVO.caseNo" && $(this).attr("name")!="paymentPlanVO.accidentNo" ){
					a = 1;
				}
			});
			if(a == 1){
				a = 0;
				saveJudg(2);
			}else{ 
				var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
				prev('15',caseId);
			}
	}
}
// 输入俩次银行号
function trueFalse() {

	var accountNo = $("#accountNo", navTab.getCurrentPanel()).val();
	var accountSize = accountNo.length;
	var accountNoSize = accountNo.split('-');
	if(accountNoSize.length>2){
		alertMsg.error("请输入100位以内的纯数字，且至多包含一个连接符“-”的银行账号或者存折号");
	}
	if(accountSize<=100){
		if (bankAccount == "") {
			bankAccount = accountNo;
			$("#accountNo", navTab.getCurrentPanel()).val("");
			$("#accountNo", navTab.getCurrentPanel()).focus();
			accountFlag = true;
			return true;
			}
		if (bankAccount != "" && accountFlag) {
			if (bankAccount != accountNo) {
				bankAccount = "";
				$("#accountNo", navTab.getCurrentPanel()).val("");
				$("#accountNo", navTab.getCurrentPanel()).focus();
				accountFlag = false;
				alertMsg.error("两次输入银行账号不一致，请重新输入。");
				return false;
			} else {
				return true;
			}
		}
	}else{		
		$("#accountNo", navTab.getCurrentPanel()).val("");
		alertMsg.error("请输入100位以内的纯数字，且至多包含一个连接符“-”的银行账号或者存折号");
		return false;
	}
}
// 添加受益人
function addBen() {
	var trs = $("#claimBeneTbody",navTab.getCurrentPanel()).find("tr");
	console.log(trs);
	for(var  i = 0; i < trs.length; i++){
		if($("#claimBeneTbody",navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input").attr("checked") == "checked"){
			$("#claimBeneTbody",navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("input").removeAttr("checked");
		}
	}
	if (flag == 1) {
		alertMsg.error("页面有未处理的受益人数据，删除或者保存后可添加。");
		return false;
	}
	//查询的时候赋值
	var strValue = $("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).val();
	var valueSplit = strValue.split(":");
	var caseId = valueSplit[0]; 
	var policyId = valueSplit[1];
	var busiItemId = valueSplit[2];
	$("#hiddenCaseId", navTab.getCurrentPanel()).val(caseId);
	$.ajax({
				url : "clm/register/queryBeneName_CLM_toBenefitInputPageAction.action?claimPayVO.caseId="+caseId+"&claimPayVO.policyId="+policyId+"&claimPayVO.busiItemId="+busiItemId+"&claimPayVO.advanceFlag=0",
				type : "POST",
				dataType : "json",
				success : function(beneVOList) {
					// 点击新增按钮清空受益人信息和领款人信息 -start
					$("#claimPayIdHidden",navTab.getCurrentPanel()).val("");
					$("#beneRelation").selectMyComBox("");
					//医疗或重疾的情况下，不应该情空姓名。
					if(beneNameFlag == true){
						$("#beneName", navTab.getCurrentPanel()).val("");
					}
					$("#beneSex").selectMyComBox("");
					$("#beneBirth", navTab.getCurrentPanel()).val("");
					$("#beneCertiType").selectMyComBox("");
					$("#beneCertiNo", navTab.getCurrentPanel()).val("");
					$("#beneCertiStart", navTab.getCurrentPanel()).val("");
					$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
					$("#payMole", navTab.getCurrentPanel()).val("");
					$("#payDeno", navTab.getCurrentPanel()).val("");
					$("#payAmount", navTab.getCurrentPanel()).val("");
					$("#clmtNation", navTab.getCurrentPanel()).val("CHN");
					$("#clmtNation").selectMyComBox("CHN");
					$("#payeeRelation").selectMyComBox("");
					$("#payeeName", navTab.getCurrentPanel()).val("");
					$("#payPayeeRelationId", navTab.getCurrentPanel()).val("");
					$("#payeeSex").selectMyComBox("");
					$("#payeeBirth", navTab.getCurrentPanel()).val("");
					$("#payeeCertiType").selectMyComBox("");
					$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
					$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
					$("#PayModeId").selectMyComBox("");
					$("#accountName", navTab.getCurrentPanel()).val("");
					$("#accountNo", navTab.getCurrentPanel()).val("");
					// 点击新增按钮清空受益人信息和领款人信息 -end
					
					var optionStr = "<option value=''>请选择</option>";
					for(var i=0; i <beneVOList.length; i++){
						if(beneVOList[i].beneId != ""){
//							$.each(beneVOList, function(key, val) {
								var beneId = beneVOList[i].beneId;
								var beneName = beneVOList[i].beneName;
								optionStr += "<option value='" + beneId +"-"+1+"'>"+ beneName + "</option>";
//							});
						}
						if(beneVOList[i].clmtId != null && beneVOList[i].clmtId !=0){
//							$.each(beneVOList, function(key, val) {
								var beneId = beneVOList[i].clmtId;
								var beneName = beneVOList[i].clmtName;
								var trs = $("#claimBeneTbody",navTab.getCurrentPanel()).find("tr");
								if(trs.length > 0){
									for(var j = 0; j < trs.length; j++){
										var beneNamepage = $("#claimBeneTbody",navTab.getCurrentPanel()).find("tr:eq("+j+")").find("td:eq(1)").text();
										if(beneName != beneNamepage){
											optionStr += "<option value='" + beneId + "-"+2+"'>"+ beneName + "</option>";
										}
									}
								} else {
									optionStr += "<option value='" + beneId + "-"+2+"'>"+ beneName + "</option>";
								}
//							});
						}
					}					
					
					var insertHtml = "<tr align='center'>"
							+ "<td align='center'><select class='combox' id='selectBene' onchange='queryBenePayeeDetais();'>"
							+ optionStr
							+ "</select></td>"
							+ "<td></td>"
							+ "<td></td>"
							+ "<td></td>"
							+ "<td></td>"
							+ "<td></td>"
							+ "<td><a title='删除' class='btnDelPrivate' id='delButton' href='javascript:void(0);' onclick='dels(this);'>删除</a>"
							+ "</td>" + "</tr>";
					$("#claimBeneTbody", navTab.getCurrentPanel()).append(insertHtml);
					$("#claimBeneTbody", navTab.getCurrentPanel()).initUI();
					flag = 1;
					$("#saveBenePayee", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					
					
					//受益人解除置灰
					$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", false);
					$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
					$("#beneRelation", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#beneHolderRelation", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#beneName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#beneSex", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#beneBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#beneCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#beneCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					//$("#clmtNation", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#clmtNation", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					//claimFireEvent($("#clmtNation", navTab.getCurrentPanel()));
					$("#beneCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#beneCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payMole", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payDeno", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payAmount", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					//51699增加受益相关信息录入
					$("#beneJobIdFire", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					claimFireEvent($("#beneJobIdFire", navTab.getCurrentPanel()));
					$("#benePhone", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#beneProvince", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					claimFireEvent($("#beneProvince", navTab.getCurrentPanel()));
					$("#beneCity", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
					$("#beneCity", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
//					claimFireEvent($("#beneCity", navTab.getCurrentPanel()));
					$("#beneDistrict", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
					$("#beneDistrict", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
//					claimFireEvent($("#beneDistrict", navTab.getCurrentPanel()));
					$("#beneAddress", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					
					//领款人解除置灰
					$("#payeeRelation", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
//					$("#payeeName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payeeName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					//claimFireEvent($("#payeeName", navTab.getCurrentPanel()));
					$("#payeeSex", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#clmtNationL", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					//claimFireEvent($("#clmtNationL", navTab.getCurrentPanel()));
					$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#PayModeId", navTab.getCurrentPanel()).setMyComboxDisabled(false,"navTab");
					$("#bankCodeId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					claimFireEvent($("#bankCodeId", navTab.getCurrentPanel()));
					$("#accountName", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#accountNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					
					//71408增加领款人相关信息录入
					$("#payeeJobIdFire", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					claimFireEvent($("#payeeJobIdFire", navTab.getCurrentPanel()));
					$("#payeePhone", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					$("#payeeProvince", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					claimFireEvent($("#payeeProvince", navTab.getCurrentPanel()));
					$("#payeeCity", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
					$("#payeeCity", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
//					claimFireEvent($("#beneCity", navTab.getCurrentPanel()));
					$("#payeeDistrict", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
					$("#payeeDistrict", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
//					claimFireEvent($("#beneDistrict", navTab.getCurrentPanel()));
					$("#payeeAddress", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
					
				}
			});
}

// 切换下拉的受益人，查询详细信息
function queryBenePayeeDetais() {
	var strValue = $("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).val();
	var valueSplit = strValue.split(":");
	var caseId = valueSplit[0];
	var policyId = valueSplit[1];
	var busiItemId = valueSplit[2];
	var claimBusiProdCode = valueSplit[3];
	var beneId = $("select#selectBene option:selected", navTab.getCurrentPanel()).val();
	var beneIds = beneId.split("-");
	var policyCode = $("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).parent().parent().find("td:eq(1)").text();

	if (isNulOrEmpty(beneId)) {
		return false;
	}

	var rel = $("#benePayeeInfo", navTab.getCurrentPanel());
	rel.loadUrl(
					"clm/register/queryBenePayeeDetail_CLM_toBenefitInputPageAction.action?beneVO.beneIdFlag="
							+ beneId
							+ "&claimPayVO.caseId="
							+ caseId
							+ "&claimPayVO.policyId="
							+ policyId
							+ "&claimPayVO.busiItemId=" + busiItemId,
					"html", function() {
						// 回调函数为form赋值
						$("input#hiddenBeneId", navTab.getCurrentPanel()).val(beneIds[0]);
						$("#hiddenCaseId", navTab.getCurrentPanel()).val(caseId);
						$("#hiddenPolicyId", navTab.getCurrentPanel()).val(policyId);
						$("#hiddenBusiItemId", navTab.getCurrentPanel()).val(busiItemId);
						$("#hiddenPolicyCode", navTab.getCurrentPanel()).val(policyCode);
						$("#hiddenbusiProdCode", navTab.getCurrentPanel()).val(claimBusiProdCode);
						$("#hiddenIsInstalment", navTab.getCurrentPanel()).val("0");
						// 受益比例
						var rate = $("#rate", navTab.getCurrentPanel()).val();
						$("#beneRate", navTab.getCurrentPanel()).val(rate);
						computePayAmounts();
					});
}

// form提交完后的回调函数
function idAjaxDone(json) {
	if (json.statusCode == DWZ.statusCode.ok) {
		
		//重新进入领款人与受益人的页面
		var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
		document.getElementById("16").href="clm/register/BenefitInputPageInit_CLM_toBenefitInputPageAction.action?caseId="+caseId;
		$("#16",navTab.getCurrentPanel()).click();
		if ($("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr").length > 0) {
			$("#beneFitInputTbId", navTab.getCurrentPanel()).find("tr:eq(0)").find("td:eq(0)").find("input[type='radio']", navTab.getCurrentPanel()).attr("checked", true);
			$("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).click();
		}
		
		if(presave == 1){
			var caseId = $("input[name='claimPayVO.caseId']",navTab.getCurrentPanel()).val();
			presave = 0;
			prev('15',caseId);
			return;
		}
		$("input#hiddenBeneId", navTab.getCurrentPanel()).val("");
		$("input#hiddenPayeeId", navTab.getCurrentPanel()).val("");
		$("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).click();
		flag = 0;
		//将所有页面的字段清空
		$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).val("");
		$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).empty();
		$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).append($("select#beneRelation", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#beneRelation", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#beneRelation",navTab.getCurrentPanel()) );
		//医疗或重疾的情况下，不应该情空姓名。
		if(beneNameFlag == true){
			$("#beneName", navTab.getCurrentPanel()).val("");
		}
		
		$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).val("");
		$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).empty();
		$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).append($("select#beneSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#beneSex", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#beneSex",navTab.getCurrentPanel()) );		
		$("#beneBirth", navTab.getCurrentPanel()).val("");
		
		$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).val("");
		$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).empty();
		$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).append($("select#beneCertiType", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#beneCertiType", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#beneCertiType",navTab.getCurrentPanel()) );		
		$("#beneCertiNo", navTab.getCurrentPanel()).val("");
		
		/*$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).val("");
		$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).empty();
		$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).append($("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).attr("value", "");*/
		
		$("#beneCertiStart", navTab.getCurrentPanel()).val("");
		$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
		$("#payMole", navTab.getCurrentPanel()).val("");
		$("#payDeno", navTab.getCurrentPanel()).val("");
		$("#payAmount", navTab.getCurrentPanel()).val("");

		$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).append($("select#payeeRelation", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#payeeRelation", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#payeeRelation",navTab.getCurrentPanel()) );		
		$("#payeeName", navTab.getCurrentPanel()).val("");
		$("#payPayeeRelationId", navTab.getCurrentPanel()).val("");
		
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex").find("option[value="+""+"]").attr("title"));
		$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );	
		$("#payeeBirth", navTab.getCurrentPanel()).val("");
		
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType").find("option[value="+""+"]").attr("title"));
		$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()) );	
		$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
		
		/*$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']").find("option[value="+""+"]").attr("title"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", "");*/
		
		$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
		
		$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).append($("select#PayModeId", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#PayModeId", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#PayModeId",navTab.getCurrentPanel()) );	
		$("#accountName", navTab.getCurrentPanel()).val("");
		$("#accountNo", navTab.getCurrentPanel()).val("");
		$("#beneLegalPersonId", navTab.getCurrentPanel()).val("");
		$("#payeeLegalPersonId", navTab.getCurrentPanel()).val("");
		$("#contraryPayFlag", navTab.getCurrentPanel()).val("");
		
		$("#saveBenePayee", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", true);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
		alertMsg.correct(json.message);
	} else {
		alertMsg.error(json.message);
	}
}
// 计算受益金额
function computePayAmounts() {
	var payMole = $("#payMole", navTab.getCurrentPanel()).val();
	var payDeno = $("#payDeno", navTab.getCurrentPanel()).val();
	// 分子分母都不为空才可以计算比例，故判断受益分子分母是否为空
	if (isNulOrEmpty(payMole)) {
		return false;
	}
	if (isNulOrEmpty(payDeno)) {
		return false;
	}

	var payAmount = $("input:radio:checked[name='caseId']",navTab.getCurrentPanel()).parent().parent().find("td:eq(4)").text();
	var beneID = $("#hiddenBeneId", navTab.getCurrentPanel()).val();
	// 遍历获取所有的受益金额
	var amount = "";
	$("#claimBeneTbody tr", navTab.getCurrentPanel()).each(function() {
		// 判断如果是需要更新的数据，则该受益金额不进行相加
		if ($(this).find("td:eq(1)").find("input").val() != beneID) {
			amount += $(this).find("td:eq(3)").text() + ":";
		}
	});
	var claimPayId = $("#claimPayIdHidden", navTab.getCurrentPanel()).val();
	
	var value = $("input:radio:checked[name='caseId']", navTab.getCurrentPanel()).val();
	var valueSplit = value.split(":");
	var caseId = valueSplit[0];
	var policyId = valueSplit[1];
	var busiItemId = valueSplit[2];
	var busiProdCode = valueSplit[3];
	
	var data = "";
	if (!isNulOrEmpty(claimPayId)) {
		data = {"claimPayVO.advanceFlag":'0',"claimPayVO.claimPayId":claimPayId,"claimPayVO.payMole":payMole,"claimPayVO.payDeno":payDeno,"claimPayVO.payAmount":payAmount.trim(),"claimPayVO.allBenePay":amount,"claimPayVO.caseId":caseId,"claimPayVO.policyId":policyId,"claimPayVO.busiItemId":busiItemId,"claimPayVO.busiProdCode":busiProdCode};
	} else {
		data = {"claimPayVO.advanceFlag":'0',"claimPayVO.payMole":payMole,"claimPayVO.payDeno":payDeno,"claimPayVO.payAmount":payAmount.trim(),"claimPayVO.allBenePay":amount,"claimPayVO.caseId":caseId,"claimPayVO.policyId":policyId,"claimPayVO.busiItemId":busiItemId,"claimPayVO.busiProdCode":busiProdCode};
	}
	
	$.ajax({
		url : "clm/register/computeRats_CLM_toBenefitInputPageAction.action",
		global : false,
		type : "POST",
		dataType : "json",
		data: data,
		async:false,
		success : function (s){
			if (s.statusCode == DWZ.statusCode.error) {
				beneRateFlag = false;
				$("#payMole", navTab.getCurrentPanel()).val("");
				$("#payDeno", navTab.getCurrentPanel()).val("");
				$("#payAmount", navTab.getCurrentPanel()).val("");
				alertMsg.error("多个受益人的受益比例相加应为100%，请检查。");
			} else {
				beneRateFlag = true;
				//为受益比例赋值
				$("#payAmount", navTab.getCurrentPanel()).val(s.allBenePay);
//				 
//				$("#beneRateHidden", navTab.getCurrentPanel()).val(s.residueRate);
			}
		}
	});
}

//选择受益人与领款人关系为本人时，带出信息
function queryPayeeRelation(){
	var payeeRelation = $("#payeeRelation", navTab.getCurrentPanel()).val();
	$("#payPayeeRelationId", navTab.getCurrentPanel()).val(payeeRelation);
	var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	var payeeLegalPersonId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
	if(beneLegalPersonId.length <= 0){
	if(payeeRelation == "00"){
		//关系是本人置灰
		$("#payeeName", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeSex", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeSex",navTab.getCurrentPanel()).removeAttr("disabled");
		if($("#beneSex", navTab.getCurrentPanel()).val()==null||$("#beneSex", navTab.getCurrentPanel()).val()==""){
			$("#beneSex", navTab.getCurrentPanel()).val($("#beneSex", navTab.getCurrentPanel()).prev().val());
		}
		var beneName = $("#beneName", navTab.getCurrentPanel()).val();
		var beneSex = $("#beneSex", navTab.getCurrentPanel()).val();
		var beneBirth = $("#beneBirth", navTab.getCurrentPanel()).val();
		var beneCertiType = $("#beneCertiType", navTab.getCurrentPanel()).val();
		var beneCertiNo = $("#beneCertiNo", navTab.getCurrentPanel()).val();
		var beneNation = $("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).val();
		var beneCertiStart = $("#beneCertiStart", navTab.getCurrentPanel()).val();
		var beneCertiEnd = $("#beneCertiEnd", navTab.getCurrentPanel()).val();
		
		// 71408  获取受益人省市县 职业，电话
		var beneJobIdFire = $("#beneJobIdFire", navTab.getCurrentPanel()).val();
		var benePhone = $("#benePhone", navTab.getCurrentPanel()).val();
		var beneProvince = $("#beneProvince", navTab.getCurrentPanel()).val();
		var beneCity = $("#beneCity", navTab.getCurrentPanel()).val();
		var beneDistrict = $("#beneDistrict", navTab.getCurrentPanel()).val();
		var beneAddress = $("#beneAddress", navTab.getCurrentPanel()).val();
		
		//为领款人赋值
		$("#payeeName", navTab.getCurrentPanel()).prev().val(beneName.trim());
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val(beneSex);
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+beneSex+"]").attr("title"));
		$("select#payeeSex", navTab.getCurrentPanel()).attr("value", beneSex);
		claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
		
		$("#payeeBirth", navTab.getCurrentPanel()).val(beneBirth);
		
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val(beneCertiType);
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+beneCertiType+"]").attr("title"));
		$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", beneCertiType);
		claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()) );
		
		$("#payeeCertiNo", navTab.getCurrentPanel()).val(beneCertiNo);
		
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val(beneNation);
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+beneNation+"]").attr("title"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", beneNation);
		$("select[name='payeeVO.payeeNation']",navTab.getCurrentPanel())[0].fireEvent("onchange");
		
		$("#payeeCertiStart", navTab.getCurrentPanel()).val(beneCertiStart);
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val(beneCertiEnd);
		//判段受益人的长期复选框是否选中，如果选中，领款人也需要选中
		if($("#beneCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked") == "checked"){
			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","true");
		} else {
			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled");
		}
		//判断受益人的证件有效止期是否只读，如果只读领款人也需要只读
		if($("#beneCertiEnd",navTab.getCurrentPanel()).attr("disabled")){
			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","true");
		}else{
			$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled");
		}
		//71408 领款人增加省市县
		$("#payeePhone",navTab.getCurrentPanel()).val(benePhone);
		$("#payeeAddress",navTab.getCurrentPanel()).val(beneAddress);
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).val(beneJobIdFire);
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).append($("select#payeeJobIdFire", navTab.getCurrentPanel()).find("option[value="+beneJobIdFire+"]").attr("title"));
		$("select#payeeJobIdFire", navTab.getCurrentPanel()).attr("value", beneJobIdFire);
		claimFireEvent($("#payeeJobIdFire",navTab.getCurrentPanel()));
		$("a[name='payeeVO.payeeState']", navTab.getCurrentPanel()).val(beneProvince);
		$("a[name='payeeVO.payeeState']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeState']", navTab.getCurrentPanel()).append($("select#payeeProvince", navTab.getCurrentPanel()).find("option[value="+beneProvince+"]").attr("title"));
		$("select#payeeProvince", navTab.getCurrentPanel()).attr("value", beneProvince);
		claimFireEvent($("#payeeProvince",navTab.getCurrentPanel()));
		setTimeout("payeeCityAssign("+ beneProvince+ " , "+beneCity+ " , "+beneDistrict+")", 0.0003);
		payeeCityAssign(beneProvince,beneCity,beneDistrict);
		alertMsg.info("领款人与受益人关系为本人时，只需修改受益人信息即可！");
	} else {
		$("#payeeName", navTab.getCurrentPanel()).prev().removeAttr("disabled");
		if($("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") == -1){
		$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled");
		$("#payeeSex",navTab.getCurrentPanel()).setMyComboxDisabled(false);
		}
		//判断是否是重新录入新受益人，如果是不清除领款人信息
		var selectFlag = false;
		if (isOCRShow != 1) {
			var payeeSelectVal = $("#payeeName", navTab.getCurrentPanel()).prev().val();
			var payeeSelectOptionVal = "";
			$("#payeeName", navTab.getCurrentPanel()).find("option").each(function() {
				payeeSelectOptionVal =  payeeSelectOptionVal + "-" + $(this).val();
			});
			if(payeeSelectOptionVal.indexOf(payeeSelectVal)!=-1){
				selectFlag = true;
			}
		} 
		//判断是否是重新录入新受益人，如果不是不清除领款人信息
		if(!selectFlag){
			if (isOCRShow == 1) {
				$("#payeeNameId", navTab.getCurrentPanel()).val("");
			} else {
				$("#payeeName", navTab.getCurrentPanel()).val("");
			}
			if(payeeRelation == "07"){
				// 当关系为夫妻性别应为男女搭配
				if($("#beneSex", navTab.getCurrentPanel()).val() == "1"){
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("2");
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+"2"+"]").attr("title"));
					$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "2");
					claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				}else{
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("1");
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+"1"+"]").attr("title"));
					$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "1");
					claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				}
			}else if(payeeRelation == "01"){
				// 当关系为父子性别应都为男
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("1");
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+"1"+"]").attr("title"));
				$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "1");
				claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
			}else if(payeeRelation =="02"){
				//当关系为父女性别应为男女搭配
				if($("#beneSex", navTab.getCurrentPanel()).val() == "1"){
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("2");
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex").find("option[value="+"2"+"]").attr("title"));
					$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "2");
					claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				}else{
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("1");
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex").find("option[value="+"1"+"]").attr("title"));
					$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "1");
					claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				}
			}else if(payeeRelation == "04"){
				// 当关系为母女性别应都为女
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("2");
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+"2"+"]").attr("title"));
				$("select#payeeSex").attr("value", "2");
				claimFireEvent($("#payeeSex",navTab.getCurrentPanel())  );
			}else if(payeeRelation == "03"){
				// 当关系为母子时性别应为男女搭配
				if($("#beneSex", navTab.getCurrentPanel()).val() == "1"){
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("2");
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+"2"+"]").attr("title"));
					$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "2");
					claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				}else{
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("1");
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+"1"+"]").attr("title"));
					$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "1");
					claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				}
			}else{
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("");
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
				$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "");
				claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
			}
			$("#payeeBirth", navTab.getCurrentPanel()).val("");
			
			$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val("");
			$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
			$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
			$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", "");
			claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()) );
			
			$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
			
//			$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
//			$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
//			$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
//			$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", "");
//			$("select[name='payeeVO.payeeNation']",navTab.getCurrentPanel())[0].fireEvent("onchange");
			
			$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
			
			$("#PayModeIdId", navTab.getCurrentPanel()).val("");
//			$("select#PayModeId", navTab.getCurrentPanel()).attr("value", "");
//			claimFireEvent($("#PayModeId",navTab.getCurrentPanel()) );
			$("select#PayModeId", navTab.getCurrentPanel()).selectMyComBox("");
			
			$("#hiddenBank", navTab.getCurrentPanel()).val("");
			$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).val("");
			$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).empty();
			$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).append($("select#bankCodeId", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
			$("select#bankCodeId", navTab.getCurrentPanel()).attr("value", "");
//			$("select#bankCodeId", navTab.getCurrentPanel()).attr("value", "");
//			$("#bankCodeId",navTab.getCurrentPanel())[0].fireEvent("onchange");
			//银行编码
			$("#bankCodeId", navTab.getCurrentPanel()).val("");
			claimFireEvent($("#bankCodeId", navTab.getCurrentPanel()));
			$("#accountName", navTab.getCurrentPanel()).val("");
			$("#accountNo", navTab.getCurrentPanel()).val("");
		}
		
		
	}
	}
}

//判断payeeName是否包含法院二字，包含则清空指定字段：
//性别、出生日期、证件类型、领款人国籍、证件有效起期、证件有效止期、证件号码、领款人职业代码、领款人电话、领款人地址
$("#payeeNameRegister").die("blur");
$("#payeeNameRegister").live('blur',function (){
	if($("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") != -1){
		$("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeSex", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeBirth", navTab.getCurrentPanel()).val("");
		$("#payeeBirth", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
		$("#payeeCertiNo", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
		$("#payeeCertiStart", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("disabled", "disabled");
		
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']").find("option[value="+""+"]").attr("title"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).selectMyComBox("");
		claimFireEvent($("select[name='payeeVO.payeeNation']"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).append($("select#payeeJobIdFire", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#payeeJobIdFire", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#payeeJobIdFire",navTab.getCurrentPanel()));
		$("#payeeJobIdFire", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		
		$("#payeePhone", navTab.getCurrentPanel()).val("");
		$("#payeePhone", navTab.getCurrentPanel()).attr("disabled", true);
		$("#payeeProvince", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeProvince", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		$("#payeeCity", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeCity", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		$("#payeeDistrict", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeDistrict", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		$("#payeeAddress", navTab.getCurrentPanel()).val("");
		$("#payeeAddress", navTab.getCurrentPanel()).attr("disabled", true);
	}else{
		$("#payeeSex", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeePhone", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeJobIdFire", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeProvince", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeCity", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeDistrict", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeAddress", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	}
});

//通过领款人姓名查询详细信息
function findclaimPayeeByName(obj){
//	$(obj).prev().val()
	var payeeName = $(obj).prev().val();
	$.ajax({
		url : "clm/register/queryPayeeByName_CLM_toBenefitInputPageAction.action",
		global : false,
		type : "POST",
		data:{'claimPayeeVO.caseId':caseId,'claimPayeeVO.payeeName':payeeName}, 
		dataType : "json",
		async : false,
		success : function(claimPayeeVO) {
			//判断payeeName是否包含法院二字，包含则清空指定字段，否则在执行下边内容
			//性别、出生日期、证件类型、领款人国籍、证件有效起期、证件有效止期、证件号码、领款人职业代码、领款人电话、领款人地址
			if(claimPayeeVO.payeeName.indexOf("法院") != -1){
				$("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("");
				$("#payeeSex", navTab.getCurrentPanel()).setMyComboxDisabled(true);
				$("#payeeBirth", navTab.getCurrentPanel()).val("");
				$("#payeeBirth", navTab.getCurrentPanel()).attr("disabled","disabled");
				$("#payeeCertiType", navTab.getCurrentPanel()).selectMyComBox("");
				$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(true);
				$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
				$("#payeeCertiNo", navTab.getCurrentPanel()).attr("disabled","disabled");
				$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
				$("#payeeCertiStart", navTab.getCurrentPanel()).attr("disabled","disabled");
				$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
				$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","disabled");
				$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).val("");
				$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("disabled", "disabled");
				
				$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
				$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']").find("option[value="+""+"]").attr("title"));
				$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).selectMyComBox("");
				claimFireEvent($("select[name='payeeVO.payeeNation']"));
				$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
				
				$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).val("");
				$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).append($("select#payeeJobIdFire", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
				$("select#payeeJobIdFire", navTab.getCurrentPanel()).attr("value", "");
				claimFireEvent($("#payeeJobIdFire",navTab.getCurrentPanel()));
				$("#payeeJobIdFire", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
				
				$("#payeePhone", navTab.getCurrentPanel()).val("");
				$("#payeePhone", navTab.getCurrentPanel()).attr("disabled", true);
				$("#payeeProvince", navTab.getCurrentPanel()).selectMyComBox("");
				$("#payeeProvince", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
				$("#payeeCity", navTab.getCurrentPanel()).selectMyComBox("");
				$("#payeeCity", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
				$("#payeeDistrict", navTab.getCurrentPanel()).selectMyComBox("");
				$("#payeeDistrict", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
				$("#payeeAddress", navTab.getCurrentPanel()).val("");
				$("#payeeAddress", navTab.getCurrentPanel()).attr("disabled", true);
			}else{
				$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(false);
				$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				$("#payeePhone", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				$("#payeeJobIdFire", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
				$("#payeeProvince", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
				$("#payeeCity", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
				$("#payeeDistrict", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
				$("#payeeAddress", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
				//领款人与受益人关系置空
				$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).val("");
				$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
				$("select#payeeRelation", navTab.getCurrentPanel()).attr("value", "");
				claimFireEvent($("#payeeRelation",navTab.getCurrentPanel()) );
				
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val(claimPayeeVO.payeeSex);
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+claimPayeeVO.payeeSex+"]").attr("title"));
				$("select#payeeSex", navTab.getCurrentPanel()).attr("value", claimPayeeVO.payeeSex);
				claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
				$("#payeeBirth", navTab.getCurrentPanel()).val(claimPayeeVO.payeeBirthStr);
				
				$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val(claimPayeeVO.payeeCertiType);
				$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+claimPayeeVO.payeeCertiType+"]").attr("title"));
				$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", claimPayeeVO.payeeCertiType);
				 claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()));
				$("#payeeCertiNo", navTab.getCurrentPanel()).val(claimPayeeVO.payeeCertiNo);
				
				$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val(claimPayeeVO.payeeNation);
				$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+claimPayeeVO.payeeNation+"]").attr("title"));
				$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", claimPayeeVO.payeeNation);
				claimFireEvent($("select[name='payeeVO.payeeNation']"));
				$("#payeeCertiStart", navTab.getCurrentPanel()).val(claimPayeeVO.payeeCertiStartStr);
				$("#payeeCertiEnd", navTab.getCurrentPanel()).val(claimPayeeVO.payeeCertiEndStr);
				}
				//支付方式
				$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).val(claimPayeeVO.payMode);
				var hiddenpayMode = claimPayeeVO.payMode;
				$("#PayModeId", navTab.getCurrentPanel()).selectMyComBox(hiddenpayMode);
				
				$("#hiddenBank", navTab.getCurrentPanel()).val(claimPayeeVO.bankCode);
				var hiddenBank = claimPayeeVO.bankCode;
				$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).val(hiddenBank);
				$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).empty();
				$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+hiddenBank+"]").attr("title"));
				$("select#bankCodeId", navTab.getCurrentPanel()).attr("value", hiddenBank);
				claimFireEvent($("#bankCodeId",navTab.getCurrentPanel()) );
				$("#accountName", navTab.getCurrentPanel()).val(claimPayeeVO.accountName);
				$("#accountNo", navTab.getCurrentPanel()).val(claimPayeeVO.accountNo);
				//银行账号不改变直接保存进行赋值，防止银行账号为空
				$("#accountNo", navTab.getCurrentPanel()).next().val(claimPayeeVO.accountNo);
		}
	});
}

//查询受益人与被保人的关系
function queryCustomer(k){
	
	var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
	var beneRelation = $(k).val();
	var isOCRShow = $("#isOCRShow", navTab.getCurrentPanel()).val();
	if(beneRelation == "00"){ //为本人的时候带出信息
		$.ajax({
			url : "clm/register/queryCustomer_CLM_toBenefitInputPageAction.action?claimPayVO.caseId="+caseId,
			global : false,
			type : "POST",
			dataType : "json",
			success : function(customerVO) {
				$("#beneName", navTab.getCurrentPanel()).val(customerVO.customerName);
				$("#beneBirth", navTab.getCurrentPanel()).val(customerVO.customerBirthdayStr);
				$("#beneCertiNo", navTab.getCurrentPanel()).val(customerVO.customerCertiCode);
				$("#beneName", navTab.getCurrentPanel()).attr("readonly","readonly");
				$("#beneBirth", navTab.getCurrentPanel()).attr("disabled","disabled");
 				$("#beneSex",navTab.getCurrentPanel()).setMyComboxDisabled(true);
 				$("#beneSex",navTab.getCurrentPanel()).removeAttr("disabled");
//				var options = $("select#beneCertiType", navTab.getCurrentPanel()).find("option");
//				for(var i = 0; i < options.length; i++){
//					if($("select#beneCertiType", navTab.getCurrentPanel()).find("option:eq("+i+")").val() == customerVO.customerCertType){
//						$("select#beneCertiType", navTab.getCurrentPanel()).find("option:eq("+i+")").attr("selected","selected");
//					}
//				}
//				$("#beneCertiType",navTab.getCurrentPanel())[0].fireEvent("onchange");
				$("select#beneSex", navTab.getCurrentPanel()).selectMyComBox(customerVO.customerGender);
				$("select#beneCertiType", navTab.getCurrentPanel()).selectMyComBox(customerVO.customerCertType);
				$("#beneCertiStart", navTab.getCurrentPanel()).val(customerVO.custCertStarDateStr);
				$("#beneCertiEnd", navTab.getCurrentPanel()).val(customerVO.custCertEndDateStr);
				$("#clmtNation", navTab.getCurrentPanel()).val(customerVO.countryCode);
				claimFireEvent($("#clmtNation", navTab.getCurrentPanel()));
				$("#beneJobIdFire", navTab.getCurrentPanel()).selectMyComBox(customerVO.jobCode);
				claimFireEvent($("#beneJobIdFire", navTab.getCurrentPanel()));
				if(customerVO.mobileTel!=""&&customerVO.mobileTel!=null){
					$("#benePhone", navTab.getCurrentPanel()).val(customerVO.mobileTel);
				}else{
					$("#benePhone", navTab.getCurrentPanel()).val(customerVO.houseTel);
				}
				$("#beneProvince", navTab.getCurrentPanel()).val(customerVO.province);
				claimFireEvent($("#beneProvince", navTab.getCurrentPanel()));
				$("#beneCity", navTab.getCurrentPanel()).val(customerVO.city);
				claimFireEvent($("#beneCity", navTab.getCurrentPanel()));
				$("#beneDistrict", navTab.getCurrentPanel()).val(customerVO.distreact);
				claimFireEvent($("#beneDistrict", navTab.getCurrentPanel()));
				cityAssign(customerVO.province,customerVO.city,customerVO.distreact)
				$("#beneAddress", navTab.getCurrentPanel()).val(customerVO.street);

				//如果证件有效止期和起期相差五十年以上，止期默认为长期
				if(customerVO.custCertStarDateStr != "" &&  customerVO.custCertEndDateStr != ""){
					var beneCertiStarDate = new Date(customerVO.custCertStarDateStr .replace(/-/g, '/'));
					var beneCertiEndD = new Date(customerVO.custCertEndDateStr.replace(/-/g, '/'));
					var years = beneCertiEndD.getFullYear()-beneCertiStarDate.getFullYear();
					if(years > 50){
						//已有数据时，证件有效起止期差50年以上时显示空。
						$("#beneCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
						$("#beneCertiEnd",navTab.getCurrentPanel()).attr("disabled",true);
						$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
						$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val("9999-12-31");
					}
				}
//				var sex_options = $("select#beneSex", navTab.getCurrentPanel()).find("option");
//				for(var i = 0; i < sex_options.length; i++){
//					if($("select#beneSex", navTab.getCurrentPanel()).find("option:eq("+i+")").val() == customerVO.customerGender){
//						$("select#beneSex", navTab.getCurrentPanel()).find("option:eq("+i+")").attr("selected","selected");
//					}
//				}
//				$("#beneSex",navTab.getCurrentPanel())[0].fireEvent("onchange");
			}
		});
	} else if (isOCRShow != 1) {
		$("#beneName",navTab.getCurrentPanel()).val("");
		$("#beneName", navTab.getCurrentPanel()).removeAttr("readonly");
		$("#beneBirth", navTab.getCurrentPanel()).removeAttr("disabled");
		$("#beneSex",navTab.getCurrentPanel()).setMyComboxDisabled(false);
		var insuredSex = $("#insuredSex",navTab.getCurrentPanel()).val();
		if(beneRelation == "07"){
			// 当关系为夫妻时，性别应为男女搭配
			if(insuredSex == "1"){
				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
			}else{
				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
			}
		} else if(beneRelation == "01"){
			// 当关系性别为父子时，性别都为男
			 $("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
		} else if(beneRelation == "02"){
			// 当关系为父女时，性别应为男女搭配
			if(insuredSex == "1"){
				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
			}else{
				$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("1");
			}
		} else if(beneRelation == "04"){
			// 当关系性别为母女时，性别都为女
			$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("2");
		}else{
			$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).val("");
			$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).empty();
			$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).append($("select#beneSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
			$("select#beneSex", navTab.getCurrentPanel()).attr("value", "");
			claimFireEvent($("#beneSex",navTab.getCurrentPanel()) );
		}
		$("#beneBirth", navTab.getCurrentPanel()).val("");
		$("#beneCertiType", navTab.getCurrentPanel()).val("");
		$("#beneCertiNo", navTab.getCurrentPanel()).val("");
		$("#beneCertiStart", navTab.getCurrentPanel()).val("");
		$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
		$("#payMole", navTab.getCurrentPanel()).val("");
		$("#payDeno", navTab.getCurrentPanel()).val("");
		$("#payAmount", navTab.getCurrentPanel()).val("");
		$("#beneCertiType", navTab.getCurrentPanel()).selectMyComBox("");
		$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).append($("select#PayModeId", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#PayModeId", navTab.getCurrentPanel()).attr("value", "");
	}
}
//清空
function clearInfo() {

	$("input#hiddenBeneId", navTab.getCurrentPanel()).val("");
	$("input#hiddenPayeeId", navTab.getCurrentPanel()).val("");
	flag = 0;
	
	//将所有页面的字段清空
	$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).val("");
	$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).empty();
	$("a[name='beneVO.beneRelation']", navTab.getCurrentPanel()).append($("select#beneRelation", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#beneRelation", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#beneRelation",navTab.getCurrentPanel()) );
	
	$("#beneName", navTab.getCurrentPanel()).val("");
	
	$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).val("");
	$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).empty();
	$("a[name='beneVO.beneSex']", navTab.getCurrentPanel()).append($("select#beneSex", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#beneSex", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#beneSex",navTab.getCurrentPanel()) );
	$("#beneBirth", navTab.getCurrentPanel()).val("");
	
	$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).val("");
	$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).empty();
	$("a[name='beneVO.beneCertiType']", navTab.getCurrentPanel()).append($("select#beneCertiType", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#beneCertiType", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#beneCertiType",navTab.getCurrentPanel()) );
	$("#beneCertiNo", navTab.getCurrentPanel()).val("");
	
	/*$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).val("");
	$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).empty();
	$("a[name='beneVO.beneNation']", navTab.getCurrentPanel()).append($("select[name='beneVO.beneNation']").find("option[value="+""+"]").attr("title"));
	$("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).attr("value", "");*/
	
	$("#beneCertiStart", navTab.getCurrentPanel()).val("");
	$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
	$("#payMole", navTab.getCurrentPanel()).val("");
	$("#payDeno", navTab.getCurrentPanel()).val("");
	$("#payAmount", navTab.getCurrentPanel()).val("");

	$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).append($("select#payeeRelation", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#payeeRelation", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#payeeRelation",navTab.getCurrentPanel()) );
	$("#payeeName", navTab.getCurrentPanel()).val("");
	$("#payPayeeRelationId", navTab.getCurrentPanel()).val("");
	
	$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex").find("option[value="+""+"]").attr("title"));
	$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
	$("#payeeBirth", navTab.getCurrentPanel()).val("");
	
	$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()) );
	$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
	
	/*$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", "");*/
	
	$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
	$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
	
	$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).append($("select#payMode", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#payMode", navTab.getCurrentPanel()).attr("value", "");
	claimFireEvent($("#PayModeId",navTab.getCurrentPanel()) );
	
	$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).append($("select#bankCodeId", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
	$("select#bankCodeId").attr("value", "");
	 claimFireEvent($("#bankCodeId",navTab.getCurrentPanel()));
	$("#accountName", navTab.getCurrentPanel()).val("");
	$("#accountNo", navTab.getCurrentPanel()).val("");
	
	$("#beneLegalPersonId", navTab.getCurrentPanel()).val("");
	$("#saveBenePayee", navTab.getCurrentPanel()).attr("disabled", "disabled");
	$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", true);
	$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
}
//记录受益人出生日期是否有改动
var beneBirthy = $("#beneBirth", navTab.getCurrentPanel()).val();
//记录受益人证件有效起期是否有改动
var beneCertiStarty = $("#beneCertiStart", navTab.getCurrentPanel()).val();
//记录受益人证件证件类型是否有改动
var beneCertiTypey = $("#beneCertiType", navTab.getCurrentPanel()).val();

function addYear(date,i){
	var d1= new Date(date);
	d1.setFullYear(d1.getFullYear()+(i-1+1));
	return date2str(new Date(d1),"yyyy-MM-dd");
}
//时间转换
function date2str(x,y){
	var z={y:x.getFullYear(),
           M:x.getMonth()+1,
           d:x.getDate(),
           h:x.getHours(),
           m:x.getMinutes(),
           s:x.getSeconds()
	};
	return y.replace(/(y+|M+|d+|h+|m+|s+)/g,
			function(v){return ((v.length>1?"0":"")+eval('z.'+v.slice(-1))).slice(-(v.length>2?v.length:2))});
};

//带出是本人时修改的信息赋给领款人
function getBeneInfoRegi(){
	resetStyle("#beneName");
	var beneRelation = $("#beneRelation",navTab.getCurrentPanel()).val();
	if(beneRelation == "00"){
//		$("#beneSex",navTab.getCurrentPanel()).removeAttr("disabled");
		if($("#beneSex", navTab.getCurrentPanel()).val()==null||$("#beneSex", navTab.getCurrentPanel()).val()==""){
			$("#beneSex", navTab.getCurrentPanel()).val($("#beneSex", navTab.getCurrentPanel()).prev().val());
		}
	}
	//判断证件有效起期和出生日期和证件类型都不为空的时候计算证件有效止期
	var beneBirth = $("#beneBirth", navTab.getCurrentPanel()).val(); // 出生日期
	var beneCertiType = $("#beneCertiType", navTab.getCurrentPanel()).val(); // 证件类型
	var beneCertiStart = $("#beneCertiStart", navTab.getCurrentPanel()).val(); // 证件有效起期
	var beneCertiEnd = $("#beneCertiEnd", navTab.getCurrentPanel()).val(); // 证件有效止期
	if(beneBirth != "" && beneCertiType != "" && beneCertiStart != ""){
		if(beneBirthy != beneBirth || beneCertiStarty != beneCertiStart || beneCertiTypey != beneCertiType || beneBirthy == "" || beneCertiStarty == "" || beneCertiTypey == ""){
			beneBirthy=beneBirth;
			beneCertiStarty=beneCertiStart;
			beneCertiTypey=beneCertiType;
			//换算出客户的年龄（周岁,“客户出生日期”开始至“证件有效起期”经过的整年度）
			var dateAcc = new Date(beneCertiStart.replace(/-/g, '/'));
			var birth = $("#beneBirth",navTab.getCurrentPanel()).val();
			var dateBirth = new Date(birth.replace(/-/g, '/'));
			var age=0;
			var years = dateAcc.getFullYear()-dateBirth.getFullYear();
			var months = dateAcc.getMonth()-dateBirth.getMonth();
			var days = dateAcc.getDate()-dateBirth.getDate();
			if (months>0) {
				age=years;
			}else if (months<0) {
				age=years-1;
			}else if (days>=0) {
				age=years;
			}else {
				age=years-1;
			}
			//截取年份
			var payeeCertiStarts = beneCertiStart.substring(0,4);
			var payeeCertiBirth = beneBirth.substring(0,4);
			//月和天拼接
			var payeeCertiStartMDay = beneCertiStart.substring(5,7)+"/"+beneCertiStart.substring(8,10);
			var newBeneCertiEnd =  payeeCertiStarts+"/"+payeeCertiStartMDay;
			
			var payeeCertiBirthMDay = beneBirth.substring(5,7)+"/"+beneBirth.substring(8,10);
			var newBeneCertiBirth =  payeeCertiBirth+"/"+payeeCertiBirthMDay;
			$("#beneCertiEndCheckBoxId",navTab.getCurrentPanel()).removeAttr("checked");
			$("#beneCertiEnd",navTab.getCurrentPanel()).removeAttr("disabled");
			//先判断证件类型选则的是什么
			if(beneCertiType == "0"){ //证件类型为身份证。
				//如果证件有效止期和起期相差五十年以上，止期默认为长期
				if(beneCertiEnd != ""){
					var beneCertiEndD = new Date(beneCertiEnd.replace(/-/g, '/'));
					var years = beneCertiEndD.getFullYear()-dateAcc.getFullYear();
					if(years > 50){
						//已有数据时，证件有效起止期差50年以上时显示空。
						$("#beneCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
						$("#beneCertiEnd",navTab.getCurrentPanel()).attr("disabled",true);
						$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
						$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val("9999-12-31");
					}
				}
				
				if(0 <= eval(age) && eval(age) <= 15){
					//0-15岁，证件有效止期=证件有效起期+5年,可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(16 <= eval(age) && eval(age) <= 25){
					//16-25岁，证件有效止期=证件有效起期+10年,可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+10;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(26 <= eval(age) && eval(age) <= 45){
					//26-45岁，证件有效止期=证件有效起期+20年,可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+20;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,20)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,20));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(eval(age) >= 46){
					//46岁以上，证件有效止期=9999-12-31，“长期”勾选项默认为“已勾选”状态，可修改。
					$("#beneCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
					$("#beneCertiEnd",navTab.getCurrentPanel()).attr("disabled",true);
					$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val("9999-12-31");
				}
			} else if(beneCertiType == "h"){ //港澳台居民居住证。
					//证件有效止期=证件有效起期+5年,不可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
//					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
//					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
			} else if(beneCertiType == "1"){//证件类型为护照。
				if(0 <= eval(age) && eval(age) <= 15){
					//0-15岁，证件有效止期=证件有效起期+5年，可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(eval(age) >= 16){
					//16岁以上，证件有效止期=证件有效起期+10年，可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+10;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				}

			} else if(beneCertiType == "2"){//证件类型为军官证。
				//军官证有效止期按照下述规则自动生成：证件有效止期=证件有效起期+4年
				/*var payeeCertiStart = eval(payeeCertiStarts)+4;
				payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
				$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,4)); // 证件有效止期
				$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,4));
				$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
			} else if(beneCertiType == "b"){//港澳台通行证。
				//港澳台通行证证有效止期按照下述规则自动生成：证件有效止期=证件有效起期+5年，可修改
				/*var payeeCertiStart = eval(payeeCertiStarts)+5;
				payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
				$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
				$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
				$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
			} else if(beneCertiType == "4"){//出生证明。
				$.ajax({
					'type': 'post',
					'url': 'clm/paymentplan/checkAmlAgeLimit_CLM_claimDirectConnAction.action?caseId=' + $("#hiddenCaseId", $.pdialog.getCurrent()).val() + "&customerBirthdayStr=" + $("#beneBirth",$.pdialog.getCurrent()).val(),
					'datatype': 'json',
					'async': false,
					'success': function (data) {
						var data = eval("(" + data + ")");
						// alert(data.endDate)
						if (data.statusCode == 1 || data.statusCode == 2) {
							$("#beneCertiEnd", navTab.getCurrentPanel()).val(data.endDate); // 证件有效止期
							$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(data.endDate);
							$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
						} else {
							$("#beneCertiEnd", navTab.getCurrentPanel()).val("");
							$("#beneCertiEndHAuditId", navTab.getCurrentPanel()).val("");
							$("#beneCertiEnd",navTab.getCurrentPanel()).attr("disabled",false);
						}
					}
				});
			} else if (beneCertiType == "e"){
				if(0 <= eval(age) && eval(age) <= 18){
					//0-18岁，证件有效止期=证件有效起期+5年,可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				}else{
					//证件有效止期=证件有效起期+10年,可修改
					/*var payeeCertiStart = eval(payeeCertiStarts)+10;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;*/
					$("#beneCertiEnd", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10)); // 证件有效止期
					$("#beneCertiEndHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10));
					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				}
			}else if(beneCertiType == "8"){//其他证件类别.
				//其他证件类别有效止期按照下述规则自动生成：有效止期需手工录入
				$("#beneCertiEnd",navTab.getCurrentPanel()).attr("disabled",false);
			}
		}
	}
	
	
	var RelationRegis = $("#payeeRelation", navTab.getCurrentPanel()).val();
	//若为本人
	if (RelationRegis == "00") {
		var beneName = $("#beneName", navTab.getCurrentPanel()).val();
		var beneSex = $("#beneSex", navTab.getCurrentPanel()).val();
		var beneBirthAudit = $("#beneBirth", navTab.getCurrentPanel()).val();
		var beneCertiType = $("#beneCertiType", navTab.getCurrentPanel()).val();
		var beneCertiNo = $("#beneCertiNo", navTab.getCurrentPanel()).val();
		var beneNation = $("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).val();
		var beneCertiStart = $("#beneCertiStart", navTab.getCurrentPanel()).val();
		var beneCertiEnd = $("#beneCertiEnd", navTab.getCurrentPanel()).val();
		// 71408  获取受益人省市县 职业，电话
		var beneJobIdFire = $("#beneJobIdFire", navTab.getCurrentPanel()).val();
		var benePhone = $("#benePhone", navTab.getCurrentPanel()).val();
		var beneProvince = $("#beneProvince", navTab.getCurrentPanel()).val();
		var beneCity = $("#beneCity", navTab.getCurrentPanel()).val();
		var beneDistrict = $("#beneDistrict", navTab.getCurrentPanel()).val();
		var beneAddress = $("#beneAddress", navTab.getCurrentPanel()).val();
//		var checkedOrNot = $('#beneCertiEndCheckBoxId').is(':checked');
//		alert(checkedOrNot);
		
		//为领款人赋值
//		alert("~~"+$("#beneCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked") == "checked");
//		if($("#beneCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
//			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","true");
//		} else {
//			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
//			$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled");
//		}
		
		
		$("#payeeName", navTab.getCurrentPanel()).val(beneName);
		
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val(beneSex);
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+beneSex+"]").attr("title"));
		$("select#payeeSex", navTab.getCurrentPanel()).attr("value", beneSex);
		claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
//		 $("#payeeSex", navTab.getCurrentPanel()).selectMyComBox(beneSex);
		$("#payeeBirth", navTab.getCurrentPanel()).val(beneBirthAudit);
		
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val(beneCertiType);
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+beneCertiType+"]").attr("title"));
		$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", beneCertiType);
		 claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()));
		$("#payeeCertiNo", navTab.getCurrentPanel()).val(beneCertiNo);
		
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val(beneNation);
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+beneNation+"]").attr("title"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", beneNation);
		claimFireEvent($("select[name='payeeVO.payeeNation']"));
//		$("select[name='payeeVO.payeeNation']",navTab.getCurrentPanel())[0].fireEvent("onchange");
		$("#payeeCertiStart", navTab.getCurrentPanel()).val(beneCertiStart);
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val(beneCertiEnd);
		
		//71408 领款人增加省市县
		$("#payeePhone",navTab.getCurrentPanel()).val(benePhone);
		$("#payeeAddress",navTab.getCurrentPanel()).val(beneAddress);
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).val(beneJobIdFire);
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).append($("select#payeeJobIdFire", navTab.getCurrentPanel()).find("option[value="+beneJobIdFire+"]").attr("title"));
		$("select#payeeJobIdFire", navTab.getCurrentPanel()).attr("value", beneJobIdFire);
		claimFireEvent($("#payeeJobIdFire",navTab.getCurrentPanel()));
		$("a[name='payeeVO.payeeState']", navTab.getCurrentPanel()).val(beneProvince);
		$("a[name='payeeVO.payeeState']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeState']", navTab.getCurrentPanel()).append($("select#payeeProvince", navTab.getCurrentPanel()).find("option[value="+beneProvince+"]").attr("title"));
		$("select#payeeProvince", navTab.getCurrentPanel()).attr("value", beneProvince);
//		claimFireEvent($("#payeeProvince",navTab.getCurrentPanel()));
	}
}

//带出是本人时修改的信息赋给领款人(new)
function getBeneInfoRegi_new(){
	//领款人为法人
	var payeeLegalPersonId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
	if(!(payeeLegalPersonId.length > 0)){
	var RelationRegis = $("#payeeRelation", navTab.getCurrentPanel()).val();
	if($("#payeeCertiEndCheckBoxId").attr("checked") != "checked"){
		$("#payeeCertiEndHidentId", navTab.getCurrentPanel()).val($("#payeeCertiEnd", navTab.getCurrentPanel()).val());
	}
	if($("#beneCertiEndCheckBoxId").attr("checked") != "checked"){
		$("#beneCertiEndHidentId",navTab.getCurrentPanel()).val($("#beneCertiEnd", navTab.getCurrentPanel()).val());
	}
	//若为本人
	if (RelationRegis == "00") {
		var beneName = $("#beneName", navTab.getCurrentPanel()).val();
		var beneSex = $("#beneSex", navTab.getCurrentPanel()).val();
		var beneBirthAudit = $("#beneBirth", navTab.getCurrentPanel()).val();
		var beneCertiType = $("#beneCertiType", navTab.getCurrentPanel()).val();
		var beneCertiNo = $("#beneCertiNo", navTab.getCurrentPanel()).val();
		var beneNation = $("select[name='beneVO.beneNation']", navTab.getCurrentPanel()).val();
		var beneCertiStart = $("#beneCertiStart", navTab.getCurrentPanel()).val();
		var beneCertiEnd = $("#beneCertiEnd", navTab.getCurrentPanel()).val();
//		var checkedOrNot = $('#beneCertiEndCheckBoxId').is(':checked');
		//为领款人赋值
//		$("#payeeName", navTab.getCurrentPanel()).val(beneName);
//		if($("#beneCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked") == "checked"){
//			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
//			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","true");
//		} else {
//			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
//			$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled");
//		}
//		if(checkedOrNot){
//			$("#payeeCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
//		}else{
//			$("#payeeCertiEndCheckBoxId",navTab.getCurrentPanel()).removeAttr("checked");
//		}
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val(beneSex);
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex", navTab.getCurrentPanel()).find("option[value="+beneSex+"]").attr("title"));
		$("select#payeeSex", navTab.getCurrentPanel()).attr("value", beneSex);
		claimFireEvent($("#payeeSex",navTab.getCurrentPanel()) );
		$("#payeeBirth", navTab.getCurrentPanel()).val(beneBirthAudit);
		
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val(beneCertiType);
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType", navTab.getCurrentPanel()).find("option[value="+beneCertiType+"]").attr("title"));
		$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", beneCertiType);
		claimFireEvent($("#payeeCertiType",navTab.getCurrentPanel()) );
		if(beneCertiNo != $("#payeeCertiNo", navTab.getCurrentPanel()).val()) {
			alertMsg.error("同一领款人/受益人，证件号码必须一致");
			return false;
		}
		
		$("#payeeCertiNo", navTab.getCurrentPanel()).val(beneCertiNo);
		
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val(beneNation);
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).find("option[value="+beneNation+"]").attr("title"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).attr("value", beneNation);
		$("select[name='payeeVO.payeeNation']",navTab.getCurrentPanel())[0].fireEvent("onchange");
		$("#payeeCertiStart", navTab.getCurrentPanel()).val(beneCertiStart);
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val(beneCertiEnd);
	}
	return true;
	}
	return true;
}

//校验受益人与被保人的关系
function checkCustomer(){
	var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
	var errorflag = true;
	if($("#beneRelation", navTab.getCurrentPanel()).val() == "00"){
		$.ajax({
			url : "clm/register/queryCustomer_CLM_toBenefitInputPageAction.action?claimPayVO.caseId="+caseId,
			global : false,
			type : "POST",
			dataType : "json",
			async: false,
			success : function(customerVO) {
				if($("#beneName", navTab.getCurrentPanel()).val() != customerVO.customerName){
					alertMsg.error("受益人与被保人关系为本人，受益人与被保人姓名不一致");
					errorflag = false;
				}else if($("#beneBirth", navTab.getCurrentPanel()).val() != customerVO.customerBirthdayStr){
					alertMsg.error("受益人与被保人关系为本人，受益人与被保人出生日期不一致");
					errorflag = false;
				}else if($("select#beneSex", navTab.getCurrentPanel()).val() != customerVO.customerGender){
					alertMsg.error("受益人与被保人关系为本人，受益人与被保人性别不一致");
					errorflag = false;
				}
			},
			error : function(){ return false;},
		});
	}
	return errorflag;
}

//受益人长期勾选赋值
function beneCertiEndCheckBox(obj){
	var RelationRegis = $("#beneRelation", navTab.getCurrentPanel()).val();
	if($(obj).attr("checked") == "checked"){
		$("#beneCertiEndHidentId",navTab.getCurrentPanel()).val("9999-12-31");
		$("#beneCertiEnd",navTab.getCurrentPanel()).val("");
		$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		//若为本人
		if (RelationRegis == "00"&&$("#beneCertiType", navTab.getCurrentPanel()).val()!="4") {
			$("#payeeCertiEndHidentId", navTab.getCurrentPanel()).val("9999-12-31");
			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
		}
	} else {
		if (RelationRegis == "00"&&$("#beneCertiType", navTab.getCurrentPanel()).val()!="4") {
			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
			if($("#beneCertiType", navTab.getCurrentPanel()).val()!="0"){
				$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled");
			}
		}else if($("#beneCertiType", navTab.getCurrentPanel()).val()=="4"){
			$("#payeeCertiEndHidentId", navTab.getCurrentPanel()).val("9999-12-31");
			$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled");
			$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
		}
		$("#beneCertiEnd",navTab.getCurrentPanel()).val("");
		$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
		
	}
}
//领款人长期勾选赋值
function payeeCertiEndCheckBox(obj){
	if($(obj).attr("checked") == "checked"){
		$("#payeeCertiEnd",navTab.getCurrentPanel()).val("");
		$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val("9999-12-31");
		$("#payeeCertiEnd",navTab.getCurrentPanel()).attr("disabled",true);
	} else {
		$("#payeeCertiEnd",navTab.getCurrentPanel()).val("");
		$("#payeeCertiEnd",navTab.getCurrentPanel()).removeAttr("disabled");
	}
}


//记录领款人出生日期是否有改动
var payeeBirthy = $("#payeeBirth", navTab.getCurrentPanel()).val();
//记录领款人证件有效起期是否有改动
var payeeCertiTypey = $("#payeeCertiStart", navTab.getCurrentPanel()).val();
//记录领款人证件证件类型是否有改动
var payeeCertiStarty = $("#payeeCertiType", navTab.getCurrentPanel()).val();
function getpayeeInfoRegi(){
	//判断证件有效起期和出生日期和证件类型都不为空的时候计算证件有效止期
	var beneBirth = $("#payeeBirth", navTab.getCurrentPanel()).val(); // 出生日期
	var beneCertiType = $("#payeeCertiType", navTab.getCurrentPanel()).val(); // 证件类型
	var beneCertiStart = $("#payeeCertiStart", navTab.getCurrentPanel()).val(); // 证件有效起期
	if(beneBirth != "" && beneCertiType != "" && beneCertiStart != ""){
		if(payeeBirthy != beneBirth || payeeCertiTypey != beneCertiStart || payeeCertiStarty != beneCertiType || payeeBirthy == "" || payeeCertiTypey == "" || payeeCertiStarty == ""){
			payeeBirthy=beneBirth;
			payeeCertiTypey=beneCertiStart;
			payeeCertiStarty=beneCertiType;
			//换算出客户的年龄（周岁,“客户出生日期”开始至“证件有效起期”经过的整年度）
			var dateAcc = new Date(beneCertiStart.replace(/-/g, '/'));
			var birth = $("#payeeBirth",navTab.getCurrentPanel()).val();
			var dateBirth = new Date(birth.replace(/-/g, '/'));
			var age=0;
			var years = dateAcc.getFullYear()-dateBirth.getFullYear();
			var months = dateAcc.getMonth()-dateBirth.getMonth();
			var days = dateAcc.getDate()-dateBirth.getDate();
			if (months>0) {
				age=years;
			}else if (months<0) {
				age=years-1;
			}else if (days>=0) {
				age=years;
			}else {
				age=years-1;
			}
			//截取年份
			var payeeCertiStarts = beneCertiStart.substring(0,4);
			var payeeBirths = birth.substring(0,4);
			//月和天拼接
			var payeeCertiStartMDay = beneCertiStart.substring(5,7)+"-"+beneCertiStart.substring(8,10);
			var payeeBirthMDay = birth.substring(5,7)+"-"+birth.substring(8,10);
			$("#payeeCertiEndCheckBoxId",navTab.getCurrentPanel()).removeAttr("checked");
			$("#payeeCertiEnd",navTab.getCurrentPanel()).removeAttr("disabled");
			//先判断证件类型选则的是什么
			if(beneCertiType == "0"){ //证件类型为身份证。
				if(0 <= eval(age) && eval(age) <= 15){
					//0-15岁，证件有效止期=证件有效起期+5年,可修改
					var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(16 <= eval(age) && eval(age) <= 25){
					//16-25岁，证件有效止期=证件有效起期+10年,可修改
					var payeeCertiStart = eval(payeeCertiStarts)+10;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(26 <= eval(age) && eval(age) <= 45){
					//26-45岁，证件有效止期=证件有效起期+20年,可修改
					var payeeCertiStart = eval(payeeCertiStarts)+20;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(eval(age) >= 46){
					//46岁以上，证件有效止期=9999-12-31，“长期”勾选项默认为“已勾选”状态，可修改。
					$("#payeeCertiEndCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
					$("#payeeCertiEnd",navTab.getCurrentPanel()).attr("disabled",true);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
					$("#payeeCertiEndHidentId", navTab.getCurrentPanel()).val("9999-12-31");
				}
			} else if(beneCertiType == "h"){ //港澳台居民居住证。
					//证件有效止期=证件有效起期+5年,不可修改
					var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId", navTab.getCurrentPanel()).val(payeeCertiStartL);
//					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
			} else if(beneCertiType == "1"){//证件类型为护照。
				if(0 < eval(age) && eval(age) <= 15){
					//0-15岁，证件有效止期=证件有效起期+5年，可修改
					var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				} else if(eval(age) >= 16){
					//16岁以上，证件有效止期=证件有效起期+10年，可修改
					var payeeCertiStart = eval(payeeCertiStarts)+10;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				}

			} else if(beneCertiType == "2"){//证件类型为军官证。
				//军官证有效止期按照下述规则自动生成：证件有效止期=证件有效起期+4年
				var payeeCertiStart = eval(payeeCertiStarts)+4;
				payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
				$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
				$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
				$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
			} else if(beneCertiType == "b"){//港澳台通行证。
				//港澳台通行证证有效止期按照下述规则自动生成：证件有效止期=证件有效起期+5年，可修改
				var payeeCertiStart = eval(payeeCertiStarts)+5;
				payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
				$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
				$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
				$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
			} else if(beneCertiType == "4"){//出生证明。
				if(0 <= eval(age) && eval(age) <= 1){
					var payeeCertiBirth = eval(payeeBirths)+1;
					payeeCertiBirthL = payeeCertiBirth+"-"+payeeBirthMDay;
					$("#payeeCertiEnd", $.pdialog.getCurrent()).val(payeeCertiBirthL); // 证件有效止期
					$("#payeeCertiEndHidentId", $.pdialog.getCurrent()).val(payeeCertiBirthL);
					$("#payeeCertiEnd", $.pdialog.getCurrent()).attr("disabled",false);
				}else{
					//出生证明有效止期按照下述规则自动生成：证件有效止期=后台保存为“9999-12-31”，前台显示空，“长期”勾选项默认为“已勾选”				
					$("#payeeCertiEndCheckBoxId",$.pdialog.getCurrent()).attr("checked","checked");
					$("#payeeCertiEnd",$.pdialog.getCurrent()).attr("disabled",true);
					$("#payeeCertiEnd", $.pdialog.getCurrent()).val("");
					$("#payeeCertiEndHidentId", $.pdialog.getCurrent()).val("9999-12-31");
				}
			} else if(beneCertiType == "e"){ //证件类型为外国人永久居住身份证
				if(0 <= eval(age) && eval(age) <= 18){
					//0-18岁，证件有效止期=证件有效起期+5年,可修改
					var payeeCertiStart = eval(payeeCertiStarts)+5;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				}else{
					//证件有效止期=证件有效起期+10年,可修改
					var payeeCertiStart = eval(payeeCertiStarts)+10;
					payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
					$("#payeeCertiEnd", navTab.getCurrentPanel()).val(payeeCertiStartL); // 证件有效止期
					$("#payeeCertiEndHidentId",navTab.getCurrentPanel()).val(payeeCertiStartL);
					$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled",false);
				}
			} else if(beneCertiType == "8"){//
				//其他证件类别有效止期按照下述规则自动生成：有效止期需手工录入
				$("#payeeCertiEnd",navTab.getCurrentPanel()).attr("disabled",false);
			}
		}
	}
}

//支付方式为现金时清空银行信息
function onChangePayModeRegister(obj){
	var payMode = $("#PayModeId", navTab.getCurrentPanel()).val();
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	//选择支付方式，银行编码信息改变
		$.ajax({
			'type':'post',
			'url':'clm/register/queryTankCode_CLM_toBenefitInputPageAction.action?caseId='+caseId+'&payMode='+payMode,
			'datatype':'json',
			'success':function(data){
				var claimTypeFlag = $("#claimTypeFlagBene", navTab.getCurrentPanel()).val();
				var bankVOList = eval("(" + data + ")");
				if($("select#bankCodeId").find("option")!=null && $("select#bankCodeId").find("option")!= ""){
					$('#bankCodeId option').remove();
				}
				var option = "<option value=''>请选择</option>";
				for(var i = 0; i < bankVOList.length; i++){
					option += "<option value='"+bankVOList[i].bankCode+"'>"+bankVOList[i].bankName+"</option>";
				}
				$("#bankCodeId", navTab.getCurrentPanel()).append(option);
				var bankCode = $("#hiddenBank", navTab.getCurrentPanel()).val();
				if($("#hiddenBank", navTab.getCurrentPanel()).val() != null && $("#hiddenBank", navTab.getCurrentPanel()).val() != ""){
					$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).val(bankCode);
					$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).empty();
					$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).append($("select#bankCodeId", navTab.getCurrentPanel()).find("option[value="+bankCode+"]").attr("title"));
					$("select#bankCodeId", navTab.getCurrentPanel()).attr("value", bankCode);
					 claimFireEvent($("#bankCodeId",navTab.getCurrentPanel()));
				}
			}
		});
	
	if($("#PayModeId", navTab.getCurrentPanel()).val() == "10"){ //如果支付方式为现金的话清空银行的数据。
		//银行编码
		$("#bankCodeId", navTab.getCurrentPanel()).val("");
		$("#bankCodeId", navTab.getCurrentPanel()).val();
		claimFireEvent($("#bankCodeId", navTab.getCurrentPanel()));
		
		//银行账户名
		$("#accountName", navTab.getCurrentPanel()).val("");
		//银行账号
		$("#accountNo", navTab.getCurrentPanel()).val("");
		$("#accountNo", navTab.getCurrentPanel()).next().val("");
		//开户行
		$("#bankOfDepositId", navTab.getCurrentPanel()).val("");
	}
	//如果支付方式为网上银行，显示开户行名称；否则，不展示
	if($("#PayModeId", navTab.getCurrentPanel()).val() == "34" || $("#PayModeId", navTab.getCurrentPanel()).val() == "22"){
		$("#isBankOfDepositDiv", navTab.getCurrentPanel()).show();
		$("#bankOfDepositId", navTab.getCurrentPanel()).prev().attr("disabled",false);
	}else{
		$("#isBankOfDepositDiv", navTab.getCurrentPanel()).hide();
	}
}
//开户行账号放大镜走的方法。
function myBankOption(k) {
	var BankCode = $("#bankCodeId",navTab.getCurrentPanel()).val();
	if(BankCode!=null && BankCode!=''){
		if($(k).attr("href").indexOf("?")>0) {
			$(k).attr("href",$(k).attr("href").substring(0,$(k).attr("href").indexOf("?")));            
			$(k).attr("href",$(k).attr("href") + "?bankOfDepositVO.bankCode=" + BankCode);
		}else {
			$(k).attr("href",$(k).attr("href") + "?bankOfDepositVO.bankCode=" + BankCode);
		}	
	}else{
		$(k).attr("href","clm/register/bankDetails_CLM_toBenefitInputPageAction.action");
	}
}
//银行编码显示
//$(document).ready(function(){
//	var obj = $("#bankCodeId option", navTab.getCurrentPanel());
//	obj.each(function(){
//		var value = $(this).val();
//		var text = $(this).text();
//		if(value != "" && value != null){
//			$(this).text(value+"-"+text);
//		}
//	});
//});

//查询受益人法人录入信息
function queryLegalPersonInfoBene() {
	var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
	var policyId = $("#hiddenPolicyId", navTab.getCurrentPanel()).val();
	var busiItemId = $("#hiddenBusiItemId", navTab.getCurrentPanel()).val();
	var policyCode = $("#hiddenPolicyCode", navTab.getCurrentPanel()).val();
	var listId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	var beneId = $("#hiddenBeneId", navTab.getCurrentPanel()).val();
	var operationFlag = 'saveBenefit';
	var personFlag = 'bene';
	var url = "clm/register/legalPersonInfoInit_CLM_legalPersonAction.action?operationFlag="+operationFlag+"&personFlag="+personFlag+"&legalPersonInfoVO.caseId="+caseId+"&legalPersonInfoVO.policyId="+policyId+"&legalPersonInfoVO.busiItemId="+busiItemId+"&legalPersonInfoVO.policyCode="+policyCode+"&legalPersonInfoVO.listId="+listId+"&relaListId="+beneId;
	$("#legalPersonQueryBene", navTab.getCurrentPanel()).attr("href", url).click();
}

//查询领款人法人录入信息
function queryLegalPersonInfoPayee() {
	var caseId = $("#hiddenCaseId", navTab.getCurrentPanel()).val();
	var policyId = $("#hiddenPolicyId", navTab.getCurrentPanel()).val();
	var busiItemId = $("#hiddenBusiItemId", navTab.getCurrentPanel()).val();
	var policyCode = $("#hiddenPolicyCode", navTab.getCurrentPanel()).val();
	var listId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();		
	if(listId.length == 0){
		listId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
	}
	var payeeId = $("#hiddenPayeeId", navTab.getCurrentPanel()).val();
	var operationFlag = 'saveBenefit';
	var personFlag = 'payee';
	var url = "clm/register/legalPersonInfoInit_CLM_legalPersonAction.action?operationFlag="+operationFlag+"&personFlag="+personFlag+"&legalPersonInfoVO.caseId="+caseId+"&legalPersonInfoVO.policyId="+policyId+"&legalPersonInfoVO.busiItemId="+busiItemId+"&legalPersonInfoVO.policyCode="+policyCode+"&legalPersonInfoVO.listId="+listId+"&relaListId="+payeeId;
	$("#legalPersonQueryPayee", navTab.getCurrentPanel()).attr("href", url).click();
}

//初始化对公支付选中状态，和法人信息录入是否可点击状态
$(function(){
	//受益人为法人取消disabled属性
	var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	if(beneLegalPersonId.length > 0){
		$("#beneCertiType", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#beneCertiType", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
	}else{
		$("#beneCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	}
	var payeeLegalPersonId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
	if(payeeLegalPersonId.length > 0){
		$("#payeeCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	}
	if(beneLegalPersonId.length > 0){
		$("#legalPersonQueryBeneStyle", navTab.getCurrentPanel()).attr("disabled", false);
		$("#legalPersonQueryBeneStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
		
		//受益人法人信息去掉必须录入信息的required属性
		$("#beneSex", navTab.getCurrentPanel()).removeClass("required");
		$("#beneCertiType", navTab.getCurrentPanel()).removeClass("required");
		$("#beneNation", navTab.getCurrentPanel()).removeClass("required");
		$("#beneJobCode", navTab.getCurrentPanel()).removeClass("required");
		$("#benePhone", navTab.getCurrentPanel()).removeClass("required");
		$("#beneState", navTab.getCurrentPanel()).removeClass("required");
		$("#beneCity", navTab.getCurrentPanel()).removeClass("required");
		$("#beneDistrict", navTab.getCurrentPanel()).removeClass("required");
		$("#beneAddress", navTab.getCurrentPanel()).removeClass("required");
		//受益人法人信息置灰
		$("#beneRelation", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneHolderRelation", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneName", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#beneSex", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneBirth", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneCertiNo", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#beneCertiStart", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled", "disabled");
	}
	if(payeeLegalPersonId.length > 0){
		//领款人法人信息去掉必须录入信息的required属性
		$("#payeeSex", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeNation", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeJobCode", navTab.getCurrentPanel()).removeClass("required");
		$("#payeePhone", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeState", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeCity", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeDistrict", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeAddress", navTab.getCurrentPanel()).removeClass("required");
		//领款人法人信息置灰
		$("#payeeRelation", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#payeeName", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#payeeSex", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#payeeCertiNo", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled", "disabled");
	}
	var contraryPayFlag = $("#contraryPayFlag", navTab.getCurrentPanel()).val();
	if(contraryPayFlag == 1){
		$("#payeeRelation", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$("#payeeRelation", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	}
});
//对公支付选中方法
function chooseMethod(){
	clearInfoPayee();
	if($('#contraryPayFlagCheckBoxId').is(':checked')) {
		$("#payeeRelation", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
		$("#contraryPayFlag", navTab.getCurrentPanel()).val("1");
	}else{
		$("#payeeRelation", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);	
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
		$("#contraryPayFlag", navTab.getCurrentPanel()).val("0");	
	}
}

//清空
function clearInfoPayee() {
	//领款人法人信息添加必须录入信息的required属性
	$("#payeeSex", navTab.getCurrentPanel()).addClass("required");
	$("#payeeNation", navTab.getCurrentPanel()).addClass("required");
	$("#payeeJobCode", navTab.getCurrentPanel()).addClass("required");
	$("#payeePhone", navTab.getCurrentPanel()).addClass("required");
	$("#payeeState", navTab.getCurrentPanel()).addClass("required");
	$("#payeeCity", navTab.getCurrentPanel()).addClass("required");
	$("#payeeDistrict", navTab.getCurrentPanel()).addClass("required");
	$("#payeeAddress", navTab.getCurrentPanel()).addClass("required");
	//领款人法人信息取消置灰
	$("#payeeCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#payeeCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#payeeRelation", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#payeeName", navTab.getCurrentPanel()).prev().removeAttr("disabled","disabled");
	$("#payeeSex", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#payeeBirth", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
	$("#payeeCertiEndCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
	
	$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeRelation']", navTab.getCurrentPanel()).append($("select#payeeRelation").find("option[value="+""+"]").attr("title"));
	$("select#payeeRelation", navTab.getCurrentPanel()).attr("value", "");
	$("#payPayeeRelationAuditId", navTab.getCurrentPanel()).val("");
	
	$("#payeeName", navTab.getCurrentPanel()).prev().val("");
	$("#payeeName", navTab.getCurrentPanel()).val("");
	
	$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeSex']", navTab.getCurrentPanel()).append($("select#payeeSex").find("option[value="+""+"]").attr("title"));
	$("select#payeeSex", navTab.getCurrentPanel()).attr("value", "");
	
	$("#payeeBirth", navTab.getCurrentPanel()).val("");
	
	$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payeeCertiType']", navTab.getCurrentPanel()).append($("select#payeeCertiType").find("option[value="+""+"]").attr("title"));
	$("select#payeeCertiType", navTab.getCurrentPanel()).attr("value", "");
	
	$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
	 
	$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).selectMyComBox("CHN"); 
	
	$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
	$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");

	$("#payeeJobIdFire", navTab.getCurrentPanel()).selectMyComBox("");
	claimFireEvent($("#payeeJobIdFire", navTab.getCurrentPanel()));
	
	$("#payeePhone", navTab.getCurrentPanel()).val("");
	
	$("#payeeProvince", navTab.getCurrentPanel()).selectMyComBox("");
	claimFireEvent($("#payeeProvince", navTab.getCurrentPanel()));
	$("#payeeCity", navTab.getCurrentPanel()).selectMyComBox("");
	$("#payeeDistrict", navTab.getCurrentPanel()).selectMyComBox("");
	$("#payeeAddress", navTab.getCurrentPanel()).val("");
	
	$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.payMode']", navTab.getCurrentPanel()).append($("select#payMode").find("option[value="+""+"]").attr("title"));
	$("select#payMode", navTab.getCurrentPanel()).attr("value", "");
	
	$("#hiddenBankId", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).val("");
	$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).empty();
	$("a[name='payeeVO.bankCode']", navTab.getCurrentPanel()).append($("select#paymentBankCodeId").find("option[value="+""+"]").attr("title"));
	$("select#paymentBankCodeId", navTab.getCurrentPanel()).attr("value", "");
	
	$("#accountName", navTab.getCurrentPanel()).val("");
	$("#accountNo", navTab.getCurrentPanel()).val("");
	$("#payeeLegalPersonId", navTab.getCurrentPanel()).val("");
	$("#contraryPayFlag", navTab.getCurrentPanel()).val("");
}