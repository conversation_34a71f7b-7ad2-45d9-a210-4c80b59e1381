<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%
	String accType = request.getParameter("accType");
	String id = request.getParameter("id");
%>
 

<script type="text/javascript">
	  
	///该配按下产品定义的所有费用业务类型项目
  	var medicalFeeTypeList = "${medicalFeeTypeList}";
  	//alert(medicalFeeTypeList);
	var medicalFeeTypeMap = medicalFeeTypeList.substring(1,
			medicalFeeTypeList.length - 1)
			+ "";
	var feeItemList = medicalFeeTypeMap.split(","); 

	function calcAmountThis(obj) {
		
		// alert('111');
		
		var medFeeAmount = $(obj).val();
		var deductAmount = $(obj).parent().parent().find("td").eq(4).find(
				"input").val();
		var dutyFeeItem = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").val();
	
		var dutyFeeItemName = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").html();
		var exitValue = false;

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		for (var i = 0; i < feeItemList.length; i++) {
			//alert(dutyFeeItem+"---"+feeItemList[i]);
			if (trim(dutyFeeItem) == trim(feeItemList[i])) {
				 	//alert(dutyFeeItem+"---"+feeItemList[i]);
				exitValue = true;
			}
		}
		 
		//如果未定义 扣除金额自动调整为等于费用金额。  扣除原因自动记录为其他。扣除备注自动记录为：扣除【费用类型名称】【费用金额】元。
		if (!exitValue) {
			deductAmount = medFeeAmount;
			$(obj).parent().parent().find("td").eq(4).find("input").val(
					medFeeAmount);
			$(obj).parent().parent().find("td").eq(6).find("select").val("2");
			
			claimFireEvent($(obj).parent().parent().find("td").eq(6).find("select") );
			$(obj).parent().parent().find("td").eq(7).find("input").val(
					"扣除【" + dutyFeeItemName + "】【" + deductAmount + "】元");
		}else{
			$(obj).parent().parent().find("td").eq(4).find("input").val("0");
		}

		$(obj).parent().parent().find("td").eq(5).find("input").val(
				medFeeAmount - deductAmount);
	}
	function calcAmount2(obj) {
		var deductAmount = $(obj).val();
		var medFeeAmount = $(obj).parent().parent().find("td").eq(3).find(
				"input").val();
		
		 
		var dutyFeeItem = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").val(); 

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		for (var i = 0; i < feeItemList.length; i++) {
			if (trim(dutyFeeItem) == trim(feeItemList[i])) {
				exitValue = true;
			}
			
		} 
        
		$(obj).parent().parent().find("td").eq(5).find("input").val(
				medFeeAmount - deductAmount);
	}
	 
	
	
	

	function validateCallbackBillDuty(form, callback, confirmMsg) {
		
		// ******* 超时监控 Add HYP
		if (JudgeTimeOut()) {
			DWZ.loadLogin();
			return false;
		}
		// form 校验 若校验失败不允许提交
		var $form = $(form);
		if (!$form.valid()) {
			return false;
		}
		
		
		// ********添加自定义判断程序 add LiAnDong 
		var myOption = $form.attr("myOption");
		if (null != myOption && myOption != '') {
			var myFlag = eval(myOption);
			if (!myFlag)
				return myFlag;
		}
		
		var result = {data:$form.serializeArray()};
		var parent = $.pdialog.getCurrent() || navTab.getCurrentPanel();
		// *******交易管理监控 Add HYP
		if(dealSwitch){
			result = addHiddenAttrs(result, parent);
		}
		// 按钮添加审计功能
		if (btnSwitch) {
			result = addBtnHiddenAttrs(result, parent);
		}
		
		/*start  在请求参数中获取页面token ADD BY tanzl*/
		var $page = $(form).parents(".dialog");
		if($page.length == 0){
			$page = $(form).parents(".unitBox");
		}
		var tokenval=$page.find("input[name='token']").val();
		var tokenKey=$page.find("input[name=jspname]").val();
		//表单提交获取token和校验token标志
		result.data.push({'name':'token','value':tokenval});
		result.data.push({'name':'tokenKey','value':tokenKey});
		result.data.push({'name':'checkTokenFlag','value':'1'});
		/*token end*/
		
		var _submitFn = function() {
			$form.find(':focus').blur();
			
			$.ajax({
				type : form.method || 'POST',
				url : $form.attr("action"),
				data : result.data,
				dataType : "json",
				cache : false,
				success : callback || DWZ.ajaxDone,
				error : DWZ.ajaxError
			});
		}
		if (confirmMsg) {
			alertMsg.confirm(confirmMsg, {
				okCall : _submitFn
			});
		} else {
			_submitFn();
		}
		return false;
	}

	
function   countOutPatients(obj){
	//治疗类型
	  var   treatType=$(obj).parent().parent().parent().find("#cureType").val();
	//医院名称
	 var   hospitalId=$(obj).parent().parent().parent().find("#hospitalId").val();
	//开始时间treatStart
	 var   treatStart=$(obj).parent().parent().parent().find("#treatStart").val();
	//就诊科室
	 var   medicalDeptCode=$(obj).parent().parent().parent().find("#medicalDeptCode").val();
	  
	 var   case_id=$(obj).parent().parent().parent().find("#caseId").val();
	  
	 if(treatType=="0"&&hospitalId!=""&&treatStart!=""&&medicalDeptCode!=""&&case_id!=""){
	 
		  $.ajax({
				'type':'post',
				'url':'clm/register/countOutPatients_CLM_tClaimDutyRegisterAction.action?claimBillVO.caseId='+caseId+"&claimBillVO.treatType="+treatType+"&claimBillVO.hospitalId="+hospitalId+"&claimBillVO.treatStart="+treatStart+"&claimBillVO.medicalDeptCode="+medicalDeptCode,
			    'datatype':'json', 
				'success':function(data){
				 	var data = eval("(" + data + ")"); 
					   $(obj).parent().parent().parent().find("#outServiceNum").val(data.claimBillLength+1);
				 	} ,'error':function(){
					alertMsg.error("查询失败!");
				}
			});
	  
	  }
}


function deleteDetail(obj){
	var $button=$(obj).parent().parent().parent().parent().parent().find("button");
	if($(obj).parent().parent().siblings().length>0){
	     $(obj).parent().parent().remove();
	}else{ 
		$(obj).parent().parent().remove();
		$button.click();
	};
	 
}


if($("#accResultOneId", navTab.getCurrentPanel()).val()==""){
	$("#accResultOneId", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult1Id").val()); 
	$("#accResultOne", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult1").val()); 
}
if($("#accResultTTwoId", navTab.getCurrentPanel()).val()==""){
	$("#accResultTTwoId", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult2Id").val()); 
	$("#accResultTTwo", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult2").val()); 
}



   
</script>
<!--账单及费用明细  div start --> 
<div class="panel"> 
	<h1>账单及费用明细</h1>
       <div class="pageFormContent" id="dutyHospitalCostInitDetails">
        <form class="pageForm required-validate"     id="${pageFlag}FormId"
		  action="clm/register/saveDutyItem_CLM_tClaimDutyRegisterAction.action"
		   method="post" onsubmit="return validateCallback(this,dutyAjaxDone)">
		    <input type="hidden"   name="caseId"  value="${caseId}">
		    <input type="hidden" id="caseId" name="claimBillVO.caseId"  value="${caseId}">
		    <input type="hidden" name="claimBillVO.billType"  value="${claimBillVO.billType}">
		    <input type="hidden" name="claimBillVO.billId"  value="${claimBillVO.billId}">
		    <input type="hidden" name="pageFlag" value="${pageFlag}" />
		 <!--     <input type="hidden" name="claimTypecode[0]" value="1">
		     <input type="hidden" name="honspTypecode[0]" value="1">
		     <input type="hidden" name="honspTypecode[1]" value="2">
		     <input type="hidden" name="honspTypecode[2]" value="3"> -->
		     
			<dl style="width: 50%;"   sizset="0">
				<dt style="width: 37%;">
					账单号<font class="point" color="red">*</font>
				</dt>
				<dd title="2" style="width: 60%;"  
					sizset="0">
					<input name="claimBillVO.billNo" id="billNo"
						class="accountNumber textInput"
						onkeyup="this.value=this.value.replace(/\D/g,'')" type="text"  value="${claimBillVO.billNo }" />
				</dd>
			</dl>
			<dl style="width: 50%;"   sizset="1">
				<dt style="width: 37%;">
					医院名称<font class="point" color="red">*</font>
				</dt>
				<dd title="3" style="width: 60%;" 
					sizset="1">
					<input name="claimBillVO.hospitalCode" id="hospitalId"
						type="hidden" size="10"   value="${claimBillVO.hospitalCode}" /> <input
						name="claimBillVO.hospitalName" class="textInput"
						id="cureHospital" type="text"  value="${claimBillVO.hospitalCode}" />
					<a class="btnLook"
						href="clm/report/showHospital_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId="
						  lookupGroup="claimBillVO"></a>
				</dd>
			</dl>
			<dl style="width: 50%;"   sizset="2">
				<dt style="width: 37%;">医院等级</dt>
				<dd title="4" style="width: 60%;"  
					sizset="2">
					<input style="display: none;" name="claimBillVO.hospitalLevel" value="${claimBillVO.hospitalLevel}" class="textInput"
						type="text" size="10"  onpropertychange="changeInputToSpan(this)" /> 
				     <span></span>
				</dd>
			</dl>
			
			
			<s:if test="pageFlag eq 'highMedical'">
				<dl style="width: 50%;"   sizset="2">
					<dt style="width: 37%;">昂贵标志</dt>
					<dd title="4" style="width: 60%;"  
						sizset="2">
						
							 <input yesOrNo="yesOrNo" type="text"   style="display: none;"  onpropertychange="changeInputToSpan(this)"
									 name="claimBillVO.isCostly"  value="${claimBillVO.isCostly}"
									 size="10"  />
							  <span></span>
					    
					</dd>
			 	</dl>
			</s:if>
			
			
			<s:if test="pageFlag eq 'cancerMedical'">
				<dl style="width: 50%;"   sizset="2">
					<dt style="width: 37%;">防癌定点标志</dt>
					<dd title="4" style="width: 60%;"  
						sizset="2"> 
						<input yesOrNo="yesOrNo" style="display: none;" onpropertychange="changeInputToSpan(this)" name="claimBillVO.isCancer" readonly="readonly" value="${claimBillVO.isCancer}" class="textInput"
							type="text" size="10"  /> 
						<span></span>
					</dd>
			 	</dl>
			</s:if>
			 
			
			<dl style="width: 50%;">
				<dt style="width: 37%;">治疗类型<font class="point" color="red">*</font></dt>
				<dd title="5" style="width: 60%;">
				
				
				<Field:codeTable name="claimBillVO.treatType"  cssClass="combox title"  
										tableName="APP___CLM__DBUSER.T_CURE_TYPE" orderBy="substr(code,1,2)"
										nullOption="true" value="${claimBillVO.treatType}"   id="cureType" />
					 
				</dd>
			</dl>
			<dl style="width: 50%;">
				<dt style="width: 37%;">
					开始时间<font class="point" color="red">*</font>
				</dt>
				<dd title="6" style="width: 60%;">
					<input name="claimBillVO.treatStart" class="date textInput" value="<s:date name='claimBillVO.treatStart' format='yyyy-MM-dd' />"
						id="treatStart" onpropertychange="queryCalimDate(this);"
						type="expandDateYMD" maxLength="10"
						  datefmt="yyyy-MM-dd" /> <a
						class="inputDateButton" href="javascript:;"
						 >选择</a>
				</dd>
			</dl>
			<dl style="width: 50%;">
				<dt style="width: 37%;">
					结束时间<font class="point" color="red">*</font>
				</dt>
				<dd title="7" style="width: 60%;">
					<input name="claimBillVO.treatEnd" class="date textInput" value="<s:date name='claimBillVO.treatEnd' format='yyyy-MM-dd' />"
						id="treatEnd" onpropertychange="queryCalimDate(this);"
						type="expandDateYMD" maxLength="10"  datefmt="yyyy-MM-dd" /> <a
						class="inputDateButton" href="javascript:;"  >选择</a>
				</dd>
			</dl>
			<dl style="width: 50%;"  sizset="3">
				<dt style="width: 37%;">
					就诊科室<font class="point" color="red"><!-- * --></font>
				</dt>
				<dd title="8" style="width: 60%;"  
					sizset="3">
					<!-- 就诊科室 -->
					<Field:codeTable name="claimBillVO.medicalDeptCode"  cssClass="combox title"  id="medicalDeptCode" onChange="countOutPatients(this)"  
										tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT"  
										nullOption="true" value="${claimBillVO.medicalDeptCode}"   />
				</dd>
			</dl>
			<dl style="width: 50%;"  sizset="3">
				<dt style="width: 37%;">
					门诊次数<font class="point" color="red"><!-- * --></font>
				</dt>
				<dd title="8" style="width: 60%;"  
					sizset="3">
					<!-- 门诊次数 -->
					<input style="display: none;" name="claimBillVO.outpatientTotal" class="textInput" id="outServiceNum" type="text"  value="${claimBillVO.outpatientTotal}"  onpropertychange="changeInputToSpan(this)"   />
				    <span></span>
				</dd>
			</dl>
			<dl style="width: 50%;"  sizset="4">
				<dt style="width: 37%;">住院天数</dt>
				<dd title="9" style="width: 60%;"  
					sizset="4">
					<input style="display: none" name="claimBillVO.stayDays" value="${claimBillVO.stayDays}"
						class="satyDays textInput readonly" id="stayDays" type="text"
						readOnly="" onpropertychange="changeInputToSpan(this)"   />
						<span></span>
				</dd>
			</dl>
			<dl style="width: 50%;">
				<dt style="width: 37%;">距离意外事故发生日天数</dt>
				<dd title="10" style="width: 60%;">
				<span id="accidentDays2"></span>
				<%-- <input name="claimBillVO.stayDays" value="${claimBillVO.stayDays}"
						class="satyDays textInput readonly" id="stayDays" type="text"
						readOnly=""  /> --%></dd>
			</dl>
			<dl style="width: 50%;">
				<dt style="width: 37%;">距离出险日期天数</dt>
				<dd title="11" style="width: 60%;">
				<span id="stayDays2"></span>
				<%-- <input type="text" id="accidentDays" name="claimBillVO.accidentDays"
									value="${claimBillVO.accidentDays}" /> --%></dd>
			</dl>
			<dl style="width: 50%;"   sizset="5">
				<dt style="width: 37%;">出险结果</dt>
				<dd title="12" style="width: 60%;" 
					sizset="5">
					<input name="claimBillVO.accDetail"  value="${claimBillVO.accDetail}"
						class="textInput readonly" id="accResultOneId"
						style="width: 40px;" type="text" readOnly="" /> <input 
						name="claimBillVO.accident1Name" class="textInput"  value="<Field:codeValue tableName="APP___CLM__DBUSER.T_ACCIDENT1" value="${claimBillVO.accDetail}"/>"
						id="accResultOne" style="width: 120px;" type="text"
						  /> <a class="btnLook"
						href="clm/report/accResultQueryFollowInit_CLM_accResultQueryFollowAction.action"
						  lookupGroup="claimBillVO"></a>
				</dd>
			</dl>
			<dl style="width: 50%;"  sizset="7">
				<dt style="width: 37%;">疾病编码</dt>
				<dd title="13" style="width: 60%;" 
					sizset="7">
					<input name="claimBillVO.icdCode" class="textInput readonly" value="${claimBillVO.icdCode}"
						id="accResultTTwoId" style="width: 50px;" type="text" readOnly="" /> <input
						name="claimBillVO.accident2Name" class="textInput" value="<Field:codeValue tableName="APP___CLM__DBUSER.T_ACCIDENT2" value="${claimBillVO.icdCode}"/>"
						id="accResultTTwo" style="width: 120px;" type="text"  />
				</dd>
			</dl>
			<dl style="width: 50%;"  sizset="9">
				<dt style="width: 37%;">总费用</dt>
				<dd title="14" style="width: 60%;"  
					sizset="9">
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimBillVO.sumAmount" value="${claimBillVO.sumAmount}" id="sumAmountAll"
						class="textInput readonly" type="text" readOnly="" />
				    <span></span>
				</dd>
			</dl>
			<dl style="width: 50%;"  sizset="10">
				<dt style="width: 37%;">扣除费用</dt>
				<dd title="15" style="width: 60%;"  sizset="10">
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimBillVO.deductAmount"  value="${claimBillVO.deductAmount}" id="deductAmountAll"
						class="textInput readonly" type="text" readOnly=""  />
				    <span></span>
				</dd>
			</dl>
			<dl style="width: 50%;"   sizset="11" >
				<dt style="width: 37%;">理算金额</dt>
				<dd title="16" style="width: 60%;"  
					sizset="11">
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimBillVO.calcAmount"  value="${claimBillVO.calcAmount}" id="calcAmountAll"
						class="textInput readonly" type="text" readOnly=""  />
				     <span></span>
				</dd>
			</dl>
		 
		<!--医疗费用明细  div end  -->
 


<!--医疗费用明细  div start -->
<div class="panel" id="dutyHospitalCostInit">

	<h1>费用明细</h1>
	<div  >
     	<table class="list nowrap itemDetail" addButton="添  加"  hiddenAdd="hiddenAdd"  autoAdd="autoAdd" enterAdd="enterAdd"
			  style="border: 2px solid #CCC;">
			<thead>
				<tr>
					<th type="text" name="items[#index#].itemInt" readonly
						defaultVal="#index#" size="5">序号</th>
                    <th type="enum" name=""
							enumUrl="clm/pages/html/dutyMedFeeItem1.jsp" size="12">费用明细项目</th>
					<th type="enum" name="" enumUrl="clm/pages/html/operationPage.jsp"
						size="10" style="width: 10%">手术代码</th>

					<th type="enum" name=""
						enumUrl="clm/pages/html/dutyMedFeeAmount.jsp" size="10"
						style="width: 10%">费用金额</th>

					<th type="enum" name=""
						enumUrl="clm/pages/html/dutyDeductAmount.jsp" size="10"
						style="width: 10%">扣除费用</th>

					<th type="enum" name="" enumUrl="clm/pages/html/dutyCalcAmount.jsp"
						size="12">理算金额</th>

					<th type="enum" name=""
						enumUrl="clm/pages/html/dutyDeductReason.jsp" size="12">扣除原因</th>

					<!-- <th type="text" name="claimBillItemVOlist[#index#].deductRemark" size="12">扣除备注</th> -->
					<th type="enum" name=""
						enumUrl="clm/pages/html/dutyDeductRemark.jsp" size="12">扣除备注</th>
					<th type="enum" name=""
						enumUrl="clm/pages/html/dutyDelete.jsp" size="12">操作</th> 
				</tr>
			</thead>
			<tbody class="list" id="feeItemId">
				<s:if
					test="claimBillItemVOlist != null||claimBillItemVOlist.size()>0">
					<s:iterator value="claimBillItemVOlist" status="st">
						<tr>
							<td><input class="digits textInput" type="text" size="5"
								value="${st.index+1}" />
								 <input type="hidden" name="claimBillItemVOlist[${st.index}].billItemId"  value="${billItemId}">
								 <input type="hidden" name="claimBillItemVOlist[${st.index}].caseId"  value="${caseId}">
								</td>
                              <td>
									<Field:codeTable   cssClass="combox title" id="medFeeItem"
										name="claimBillItemVOlist[${st.index}].medFeeItem"
										tableName="APP___CLM__DBUSER.T_INVOICE_TYPE" nullOption="true"
										value="${medFeeItem}" whereClause="CODE like 'C%'" orderBy="code"/>
								 
                            </td>
							<td name="手术代码"><input id="operationCodeId"
								name="claimBillItemVOlist[${st.index}].operationCode"
								value="${operationCode}" size="10" type="hidden" /> <input
								id="operationNameId"
								name="claimBillItemVOlist[${st.index}].operationDesc"
								value="<Field:codeValue tableName="APP___CLM__DBUSER.T_OPERATION" value='${operationCode}'/>"
								type="text" /> <a class="btnLook"
								href="clm/report/queryOperationCode_CLM_hospitalInfoQueryAction.action"
								lookupGroup="claimBillItemVOlist[${st.index}]">查询手术代码</a></td>

							<td name="费用金额">
								<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
								<input type="text" id="feeAmount"
								name="claimBillItemVOlist[${st.index}].feeAmount" class="number"
								maxlength="18" onchange="calcAmountThis(this);" value="${feeAmount}" />
							</td>

							<td name="扣除费用">
								<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
								<input type="text" onchange="calcAmount2(this);" id="deductAmount"
								name="claimBillItemVOlist[${st.index}].deductAmount"
								class="number" maxlength="18" value="${deductAmount}" />
							</td>

							<td name="理算金额">
								<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
								<input type="text" id="calcAmount" 
								name="claimBillItemVOlist[${st.index}].calcAmount" maxlength="18"
								readonly="readonly" value="${calcAmount}" />
							</td>

							<td name="扣除原因"><Field:codeTable  cssClass="combox title" id="deductReason"
									name="claimBillItemVOlist[${st.index}].deductReason"
									tableName="APP___CLM__DBUSER.T_MEDICAL_FEE_DEDUCTION"
									nullOption="true" value="${deductReason}" /></td>

							<td name="扣除备注"><input type="text" id="deductRemark"
								name="claimBillItemVOlist[${st.index}].deductRemark"
								value="${deductRemark}" maxlength="1000" /></td>
							<td><a class="btnDel" href="javascript:void(0)" onclick="deleteDetail(this)"></a></td>
						</tr>
					</s:iterator>
				</s:if>
			</tbody>
		</table>

	 </div>
    </div>
   </form>
  </div>

</div>
<!--医疗费用明细  div end  -->


 

