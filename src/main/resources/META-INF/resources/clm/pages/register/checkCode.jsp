<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script  type="text/javascript">
function findCleckCode(){
		var oldCheckListFlag = $("#oldCheckListFlag",navTab.getCurrentPanel()).val();
		var checkCode = $(".checkCode", navTab.getCurrentPanel()||$.pdialog.getCurrent());
	  	for(var i=0;i<checkCode.size();i++){
		  	var checkCodeOption = $(checkCode[i]).find("option");
		  	if(checkCodeOption.size()<=1){
		   	 $.ajax({
		  			'type':'post',
		  			'url':'clm/report/queryAllChecklist_CLM_toReportCheckConfirmPageAction.action',
		  			'datatype':'json',
				 	'data':{'claimCaseVO.oldCheckListFlag':oldCheckListFlag},
		  			'success':function(data){
		  				var data = eval("(" + data + ")");
		  				$(checkCode[i-1]).empty();
		  				var option1 = "<option value='-1' class = ''>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </option>";
		  				for(var j = 0; j < data.length; j++){
		  					option1 += "<option value='"+data[j].code+"'   class='"+data[j].name+"' >"+data[j].code+'-'+data[j].name+"</option>";
		  				}
		  				
		  				var trs = $("#registerPage", navTab.getCurrentPanel()||$.pdialog.getCurrent()).find("tr");
		  				var i = trs.length - 2;
		  				var id = $("#registerPage", navTab.getCurrentPanel()||$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("select").attr("id");
		  				if(trs.length > 1){
			  				if(id == "checkCodeReportCopyRegister"){
			  					$("#registerPage", navTab.getCurrentPanel()||$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("select").attr("id","checkCodeReportCopy"+i+"");
			  				}
		  				}
		  				
		  				$("#checkCodeReportCopyRegister", navTab.getCurrentPanel()||$.pdialog.getCurrent()).loadMyComboxOptions(option1,'1');
		  			},
		  			'error':function(){
		  				alert("出错了！");
		  			}
		  		});  
		  	}
	     }
	}
$(document).ready(
		findCleckCode()
	);
</script>
 <select class="combox selectToInputdelete checkCode" style = "width:100px;" id="checkCodeReportCopyRegister"   name="claimChecklistVOs[#index#].checklistCode">
	<option value="-1" class=" ">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </option> 
</select>
