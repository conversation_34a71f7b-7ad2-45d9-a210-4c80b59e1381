﻿<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<style>
.spanbox1 span {width:22%}
.spanbox2 span {width:22%}
.spanbox3 span {width:25%}
.spanbox4 span {width:22%}
</style>
<script type="text/javascript" src="clm/pages/register/claimMatchCalcInit.js"></script>
<script type="text/javascript">

var isRisk='${claimCaseVO.isRisk}'; 
var acceptDecision='${claimCaseVO.acceptDecision}'; 

var notRegistDocFlag='${claimCaseVO.notRegistDocFlag}'; 
if(isRisk==1){
	$("#riskDiv", navTab.getCurrentPanel()).show();
}
if(acceptDecision==2){
	$("#isRiskDiv", navTab.getCurrentPanel()).show();
	$("#IsnotRegistDocFlag", navTab.getCurrentPanel()).show();
}else{
	$("#notRegistDocFlag", navTab.getCurrentPanel()).selectMyComBox("");
	$("#unDocReason", navTab.getCurrentPanel()).attr('');
}
if(notRegistDocFlag==1){
	$("#unDocReasonFlag", navTab.getCurrentPanel()).show();
}else{
	$("#unDocReason", navTab.getCurrentPanel()).attr('');
}
//故意欺诈风险
function riskChangeMatch(k){
	var risk =  $(k).val();
	if(risk==1){
		$("#riskDiv", navTab.getCurrentPanel()).show();
	}else{
		$("#riskDiv", navTab.getCurrentPanel()).hide();
// 		$("input[name='claimCaseVO.riskLabel']").each(function() {$(this).attr("checked", false);});
	}
}

var rejectReason='${claimCaseVO.rejectReason}';
 
if(rejectReason==""){
	  $("#rejectReason", navTab.getCurrentPanel()).setMyComboxDisabled(true);
}
 
//页面加载完毕执行的方法，页面加载时需要提示相应的提示信息：理赔已终止，无法匹配理算
/* var caseId='${caseId}'; 
	$.ajax({
		'url':"clm/register/queryMessage_CLM_claimMatchCalcAction.action?caseId="+caseId,
		 'type':'post',
		 'success':function(data){
			  if(data!='[]'){
			 	alertMsg.error(data);
			 }
		 }
	});  */
	
var dataMessage='${dataMessage}'; 
if(dataMessage!='[]'&&dataMessage!=''){
	// alertMsg.info(dataMessage);
	 
	   alertMsg.confirm(dataMessage,{
			 okCall:function(){
				 
			 }
	   });
}
	
if(document.readyState=="complete"){  
	var currentPageId= <%=session.getAttribute("currentPageId") %>;
	 if(currentPageId!="" && currentPageId>5 ){
		 onlyReadClaimMatchCalcInit();
	}
}

function myAjaxDown(json){
	//遍历map，将修改后的claim_liab_id更新
	var claimLiabTrList = $("#registerCalc>tr", navTab.getCurrentPanel());
	for(var i=0; i<claimLiabTrList.length;i++){
		var oldId = claimLiabTrList.eq(i).find("td").eq(0).find("[id='claimLiabId']").attr("value");
		for(var kay in json){
  			if (kay==oldId) {
  				var newId = json[kay];
  				claimLiabTrList.eq(i).find("td").eq(0).find("[id='claimLiabId']").attr("value",newId);
  			}
  		}
	}
	var acceptDecision = $("#acceptDecisionId", navTab.getCurrentPanel()).val();
	if(json.taxPremiumFlag=='0'||acceptDecision=='2'){
		$("#isDeductPrem", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#createDocumentButtion", navTab.getCurrentPanel()).attr("disabled","true");
	}else{
		var isdeductPremVal = $("#isDeductPrem", navTab.getCurrentPanel()).val();
		$("#isDeductPrem", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("#isDeductPrem", navTab.getCurrentPanel()).selectMyComBox(isdeductPremVal);
		$("#createDocumentButtion", navTab.getCurrentPanel()).attr("disabled",false);
	}
	
	if(json.statusCode == DWZ.statusCode.error) {
		 if(json.message && alertMsg){ 
			 alertMsg.error(json.message);
	 	 }
	} else if (json.statusCode == DWZ.statusCode.timeout) {
		 DWZ.loadLogin(json.flag);
	} else {
		if(json.data.status=='881') {
			alertMsg.warn(json.data.message);
		}
		if(presave == 1){
			presave = 0;
			prev('14','${caseId}');
		}else if(json.flag == 0){//表示为保存(需要提示)
			alertMsg.correct(json.message);
		}else{
			tonext('16','${caseId}','${claimCaseVO.caseNo}','${claimAccidentVO.accidentNo}'); 
		}
		
	};
}
/**
 * 产品测试快捷
 */
function keySend(event){
	if(event.ctrlKey && event.keyCode == 13){
		$.ajax({
			url : "clm/audit/summarizingPayMentInfo_CLM_contractDealAction.action?caseId="+caseId,
			global : false,
			type : "POST",
			dataType : "json",
			async : false,
			success : function (json){
				return true;
			}
		});
	}
}

//如果给付代码为不给，则将调整金额显示为0
var claimLiabTrList = $("#registerCalc>tr", navTab.getCurrentPanel());
for(var i=0; i<claimLiabTrList.length;i++){
	if(claimLiabTrList.eq(i).find(":checked").attr("value")==6){
		claimLiabTrList.eq(i).find("td").eq(12).empty();
		claimLiabTrList.eq(i).find("td").eq(12).append(0);
	}
}

//委派事件(修改给付代码触发事件)
$("#registerCalc [type='radio']" ).die("click");
$("#registerCalc [type='radio']" ).live('click',function (){
//获取责任理算信息结果集
	var liabTrList = $("#registerCalc>tr", navTab.getCurrentPanel());
	//获取理赔类型信息结果集
	var subLiabTrList= $("#regCalcClmSubCase>tr", navTab.getCurrentPanel());
	//获取保单计算信息结果集
	var sumPolicyTrList= $("#claimBusiProdTbody>tr", navTab.getCurrentPanel());
	//总赔付金额
	var adjustPay = 0;
	//拒赔金额
	var rejectPay = 0;
	//获取总给付金额和拒赔金额
	for(var i=0; i<liabTrList.length;i++){
		if(liabTrList.eq(i).find(":checked").attr("value")==6){
			rejectPay += liabTrList.eq(i).find("td").eq(12).html().trim()-1+1;
			liabTrList.eq(i).find("td").eq(12).empty();
			liabTrList.eq(i).find("td").eq(12).append(0); 
		}else{
			adjustPay += liabTrList.eq(i).find("td").eq(12).html().trim()-1+1;
			liabTrList.eq(i).find("td").eq(12).empty();
			liabTrList.eq(i).find("td").eq(12).append(liabTrList.eq(i).find("td").eq(0).find("[flag='adjustPay']").attr("value"));
		}
	}
	//修改赔案层数据
	$("#regCalcCase", navTab.getCurrentPanel()).find("tr td").eq(4).empty();
	$("#regCalcCase", navTab.getCurrentPanel()).find("tr td").eq(4).append(rejectPay);
	$("#regCalcCase", navTab.getCurrentPanel()).find("tr td").eq(0).empty();
	$("#regCalcCase", navTab.getCurrentPanel()).find("tr td").eq(0).append(adjustPay);
	$("#regCalcCase", navTab.getCurrentPanel()).find("tr td").eq(3).empty();
	$("#regCalcCase", navTab.getCurrentPanel()).find("tr td").eq(3).append(adjustPay+($("#regCalcCase").find("tr td").eq(2).html().trim()-1+1));
	//修改理赔类型层数据
	for(var i=0; i<subLiabTrList.length;i++){
		//保单合计理算金额
		var actualPay = 0;
		//汇总相同理赔类型责任的调整金额，赋予保单合计理算金额
		for(var j=0; j<liabTrList.length;j++){
			if(liabTrList.eq(j).find("td").eq(0).find("[flag='liabClaimType']").attr("value")==subLiabTrList.eq(i).find("td").eq(0).html().trim()){
				if(liabTrList.eq(j).find(":checked").attr("value")!=5){
					actualPay += liabTrList.eq(j).find("td").eq(12).html()-1+1;
					
				}
			}
		}
		subLiabTrList.eq(i).find("td").eq(2).empty();
		subLiabTrList.eq(i).find("td").eq(2).append(actualPay);
		//如果账单金额减去社保第三方小于保单合计理算金额，则核赔赔付金额为账单金额减去社保第三方
		/* var auditPayAmt = subLiabTrList.eq(i).find("td").eq(1).html().trim()-subLiabTrList.eq(i).find("td").eq(3).html().trim()-subLiabTrList.eq(i).find("td").eq(4).html().trim();
		if(subLiabTrList.eq(i).find("td").eq(2).html().trim()-1+1<auditPayAmt){
			auditPayAmt = subLiabTrList.eq(i).find("td").eq(2).html().trim()-1+1;
		} */
		subLiabTrList.eq(i).find("td").eq(5).empty();
		subLiabTrList.eq(i).find("td").eq(5).append(actualPay);
	}
	//汇总相同保单和险种下的责任调整金额之和,赋予保单计算信息的理算金额 
	for(var i=0; i<sumPolicyTrList.length;i++){
		var actualPay = 0;
		for(var j=0; j<liabTrList.length;j++){
			if(liabTrList.eq(j).find("td").eq(0).find("[flag='policyCode']").attr("value")==sumPolicyTrList.eq(i).find("td").eq(0).html().trim()
					&&liabTrList.eq(j).find("td").eq(1).html().trim()==sumPolicyTrList.eq(i).find("td").eq(2).html().trim()){
				if(liabTrList.eq(j).find(":checked").attr("value")!=5){
					actualPay += liabTrList.eq(j).find("td").eq(12).html()-1+1;
				}
			}
		}
		sumPolicyTrList.eq(i).find("td").eq(7).empty();
		sumPolicyTrList.eq(i).find("td").eq(7).append(actualPay);
	}
});
//如果为其他勾选时显示，否则隐藏。
if($("#riskLabelEight", navTab.getCurrentPanel()).attr("checked")=="checked"){
	$("#riskOtherReason", navTab.getCurrentPanel()).show();
} else {
	$("#riskOtherReason", navTab.getCurrentPanel()).hide();
}
if($("#cheatImplementationPersonnelSeven", navTab.getCurrentPanel()).attr("checked")=="checked"){
	$("#otherImplementationPersonnel", navTab.getCurrentPanel()).show();
} else {
	$("#otherImplementationPersonnel", navTab.getCurrentPanel()).hide();
}
var cheatDistinguishChannel = '${claimCaseCheatVO.cheatDistinguishChannel}';
if(cheatDistinguishChannel !="09"){
	$("#otherCheatOptionId", navTab.getCurrentPanel()).hide();
	$("#otherCheatOptionId",navTab.getCurrentPanel()).val("");
}else{
	$("#otherCheatOptionId", navTab.getCurrentPanel()).show();
}
</script>
<!-- 自动匹配理算初始化页面！ -->
<div class="main_tabdiv" id="claimMatchCalcInitDiv">
<div class="panelPageFormContent" onkeydown="keySend(event)">        
   <form id="pagerForms" method="post"  onsubmit="return validateCallback(this,myAjaxDown);"  novalidate="novalidate">
        	<div class="panelPageFormContent">
             <dl >
                 <dt>赔案号</dt>
                 <dd>
                    <input name="claimCaseVO.caseNo" type="text" size="30" value="${claimCaseVO.caseNo}" readonly="readonly"/>
                    <input name="claimCaseVO.caseId" value="${claimCaseVO.caseId}" type="hidden"/>   
                    <input id="caseId" type="hidden" value="${claimCaseVO.caseId}">                                     
                    <input id="createDocumentFlag" type="hidden" value="0">                                     
                 </dd>
             </dl> 
             <dl >
                 <dt>事件号</dt>
                 <dd>
                    <input name="claimAccidentVO.accidentNo" type="text" size="30" value="${claimAccidentVO.accidentNo}" readonly="readonly"/>
                    <input name="claimAccidentVO.accidentId" value="${claimAccidentVO.accidentId}" type="hidden" />                                        
                 </dd>
             </dl> 
            </div>
            <div class="panelPageFormContent main_tabdiv">     
             <s:if test="claimCaseVO.channelCode != '03'">
             <dl>
                 <dt>免材料标识</dt>
                 <dd>
                     <Field:codeTable cssClass="combox title"  name="claimCaseVO.materialFreeFlag" value="${claimCaseVO.materialFreeFlag}"  
                             tableName="APP___CLM__DBUSER.T_YES_NO" disabled="true" nullOption="true"></Field:codeTable>           
                 </dd>
             </dl>
             <dl>
                 <dt>电子签名标识</dt>
                 <dd>
                     <Field:codeTable cssClass="combox title"  name="claimCaseVO.signatureTraceFlag" value="${claimCaseVO.signatureTraceFlag}"  
                             tableName="APP___CLM__DBUSER.T_YES_NO" disabled="true"  nullOption="true"></Field:codeTable>           
                 </dd>
             </dl>
             </s:if>
             <dl>
                <dt>采集方式</dt>
                <dd>
                    <Field:codeValue tableName="APP___CLM__DBUSER.T_ACQUIST_WAY" value="${claimCaseVO.acquistWay}"/>
                </dd>
             </dl>
             <dl>
                 <dt><font>* </font>重复账单号标识</dt>
                 <dd>
                     <Field:codeTable cssClass="combox title"  name="claimCaseVO.repeatNumberFlag" value="${claimCaseVO.repeatNumberFlag}"  
                             tableName="APP___CLM__DBUSER.T_YES_NO" id="repeatNumberFlag" defaultValue="0"></Field:codeTable>                     
                 </dd>
             </dl>
             <dl >
                 <dt>案件标识</dt>
                 <dd>
					 <Field:codeTable cssClass="combox title"  id="caseFlag" name="claimCaseVO.caseFlag" value="${claimCaseVO.caseFlag}"
							tableName="APP___CLM__DBUSER.T_CASE_LEVEL" nullOption="true" whereClause="code in(1)"/>                  
                 </dd>
             </dl>  
             <dl >
                 <dt><font>* </font>立案结论</dt>
                 <dd>
                   <Field:codeTable cssClass="combox title"  id="acceptDecisionId"  name="claimCaseVO.acceptDecision" value="${claimCaseVO.acceptDecision}"  
                             tableName="APP___CLM__DBUSER.T_CLAIM_ACCEPT_DECISION" orderBy="code"  whereClause="code in(1,2)" onChange="clickAcceptDecisionId(this.value)" nullOption="true"></Field:codeTable>                     
                 </dd>
             </dl>  
            <dl >
                 <dt>不予立案原因</dt>
                 <dd>
                   <Field:codeTable cssClass="combox title"  id="rejectReason"  name="claimCaseVO.rejectReason" value="${claimCaseVO.rejectReason}"  whereClause="code in(02,03,06,07,08,09,99)" 
                             tableName="APP___CLM__DBUSER.T_CLAIM_REJECT_REASON" orderBy="substr(code,1,2)" nullOption="true" ></Field:codeTable>                                   
                 </dd>
             </dl>
			 <div style="display: none" id="IsnotRegistDocFlag">
             <dl>
                 <dt><font>* </font>是否生成不予立案通知书</dt>
                 <dd>
                   <Field:codeTable cssClass="combox title"  id="notRegistDocFlag"  name="claimCaseVO.notRegistDocFlag" value="${claimCaseVO.notRegistDocFlag}" 
                             tableName="APP___CLM__DBUSER.T_YES_NO" orderBy="substr(code,1,2)" nullOption="true"  onChange="isUnDocReason(this.value)"></Field:codeTable>                                                    </dd>
             </dl>
             </div>
             <div style="display: none" id="unDocReasonFlag">
             <dl style="width: 100%;height: auto;">
                 <dt>不生成通知书原因</dt>
                 <dd>
                    <textarea rows="3" cols="80" maxlength="2000" id ="unDocReason" name="claimCaseVO.unDocReason"  onpropertychange="if(typeof value==='string' && value.length>2000)value=value.substring(0,2000)">${claimCaseVO.unDocReason}</textarea>
                 </dd>
             </dl>
			</div>
             <dl >
                 <dt><font>* </font>实时支付</dt>
                 <dd>
                   <Field:codeTable cssClass="combox title" name="claimCaseVO.realtimePay" value="${claimCaseVO.realtimePay}" whereClause="1=1" 
                             tableName="APP___CLM__DBUSER.T_YES_NO" orderBy="yes_no*-1" ></Field:codeTable>                                   
                 </dd>
             </dl>    
             <dl>
                 <dt>业务员自保件</dt>
                 <dd>
                   <Field:codeTable cssClass="combox title" name="claimCaseVO.salesmanSelfInsurance" value="${claimCaseVO.salesmanSelfInsurance}" whereClause="1=1" 
                             tableName="APP___CLM__DBUSER.T_YES_NO" orderBy="yes_no*-1" disabled="true"></Field:codeTable>
                    <input type="hidden" value="${claimCaseVO.salesmanSelfInsurance}" name="claimCaseVO.salesmanSelfInsurance"/>                                            
                 </dd>
             </dl>
             <div style="display: none" id="isRiskDiv">
	             <dl>
					<dt>
						<a>赔案欺诈风险标签</a>
					</dt>
					<dd>
						
						<select id="isRisk" class="combox title" name="claimCaseVO.isRisk" onchange="riskChangeMatch(this);">
							<option value="0" <s:if test="claimCaseVO.isRisk == 0">selected</s:if>>无风险</option>
							<option value="1" <s:if test="claimCaseVO.isRisk == 1">selected</s:if>>有风险</option>
						</select>
					</dd>
				</dl>    
             </div>
				<s:if test="claimCaseVO.taxPremiumFlag==1">
					<dl>
						<dt>
							<a>在理赔金中扣除保费</a>
						</dt>
						<dd>
							<s:if test="claimCaseVO.acceptDecision==2">
								<Field:codeTable cssClass="combox title" id="isDeductPrem"
									name="claimCaseVO.isDeductPrem"
									value="${claimCaseVO.isDeductPrem}"
									tableName="APP___CLM__DBUSER.T_YES_NO" 
									nullOption="true" disabled="true"></Field:codeTable> 
							</s:if>
							<s:else>
								<Field:codeTable cssClass="combox title" id="isDeductPrem"
									name="claimCaseVO.isDeductPrem"
									value="${claimCaseVO.isDeductPrem}"
									tableName="APP___CLM__DBUSER.T_YES_NO" 
									nullOption="true"></Field:codeTable> 
							</s:else>
							
						</dd>
					</dl>
				</s:if>
			</div>
             <div style="display: none" id="riskDiv">
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">赔案欺诈风险项
						</h1>
					</div>
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">一级标签
						</h1>
					</div>
					
					<div class="panelPageFormContent">
						<dl style="width: 100%;height: auto">
								<dt></dt> 
								 <dd  style="width: 90%; margin-left: 10%" >
									<span class="spanbox3" style="width: 100%;">
										<span>
											<input type="checkbox"
											id="riskLabelNine" name="claimCaseVO.riskLabel" value="9" <s:if test='%{claimCaseVO.riskLabel.indexOf("9") >= 0}'>checked="checked"</s:if>/>故意虚构保险标的
										</span>
										<span>
											<input type="checkbox"
											id="riskLabelTwo" name="claimCaseVO.riskLabel" value="2" <s:if test='%{claimCaseVO.riskLabel.indexOf("2") >= 0}'>checked="checked"</s:if>/>编造未曾发生的保险事故
										</span>
										<span>
											<input  type="checkbox"
											id="riskLabelFour" name="claimCaseVO.riskLabel" value="4" <s:if test='%{claimCaseVO.riskLabel.indexOf("4") >= 0}'>checked="checked"</s:if>/>编造虚假的事故原因
										</span>
										<span>
											<input type="checkbox"
											id="riskLabelSix" name="claimCaseVO.riskLabel" value="6" <s:if test='%{claimCaseVO.riskLabel.indexOf("6") >= 0}'>checked="checked"</s:if>/>夸大损失程度
										</span>
										<span>
											<input type="checkbox"
											id="riskLabelTen" name="claimCaseVO.riskLabel" value="10" <s:if test='%{claimCaseVO.riskLabel.indexOf("10") >= 0}'>checked="checked"</s:if>/>故意造成保险事故
										</span>
										<span>
											<input type="checkbox"
											id="riskLabelOne" name="claimCaseVO.riskLabel" value="1" <s:if test='%{claimCaseVO.riskLabel.indexOf("10") < 0 && claimCaseVO.riskLabel.indexOf("11") < 0 && claimCaseVO.riskLabel.indexOf("1") >= 0}'>checked="checked"</s:if>/>故意不如实告知
										</span>
									</span>
								</dd>
							</dl>
						</div>
			
		             <div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">二级标签
						</h1>
					</div>
					<div class="panelPageFormContent">
						<dl style="width: 100%;height: auto">
								<dt></dt> 
								 <dd  style="width: 90%; margin-left: 10%" >
									<span class="spanbox3" style="width: 100%;">
										<span>
											<input type="checkbox"
											id="riskLabelThree" name="claimCaseVO.riskLabel"  value="3" <s:if test='%{claimCaseVO.riskLabel.indexOf("3")  >= 0}'>checked="checked"</s:if>/>伪造、编造索赔材料
										</span>
										<span>
											<input type="checkbox"
											id="riskLabelSeven" name="claimCaseVO.riskLabel" value="7" <s:if test='%{claimCaseVO.riskLabel.indexOf("7")  >= 0}'>checked="checked"</s:if>/>冒名顶替
										</span>
										<span>
											<input  type="checkbox"
											id="riskLabelFive" name="claimCaseVO.riskLabel" value="5" <s:if test='%{claimCaseVO.riskLabel.indexOf("5")  >= 0}'>checked="checked"</s:if>/>倒签单
										</span>
										<span>
											<input type="checkbox"
											id="riskLabelEleven" name="claimCaseVO.riskLabel" value="11" <s:if test='%{claimCaseVO.riskLabel.indexOf("11") >= 0}'>checked="checked"</s:if>/>同业高额投保未告知
										</span>
										<span>
											<input type="checkbox" onclick="riskLabelEightOther(this);"
											id="riskLabelEight" name="claimCaseVO.riskLabel" value="8" <s:if test='%{claimCaseVO.riskLabel.indexOf("8") >=0}'>checked="checked"</s:if>/>其他
										</span>
										<span>
											<input type="text" style="width:150%"
											id="riskOtherReason" name="claimCaseVO.riskOtherReason" maxlength="50" value="${claimCaseVO.riskOtherReason }" onblur="">
										</span>
									</span>
								</dd>
							</dl>
						</div>
						
					<div class="divfclass">
						<h1>
							<img src="clm/images/tubiao.png">欺诈案件信息项
						</h1>
					</div>
					
					<div class="panelPageFormContent main_tabdiv">
						<dl>
							<dt>作案性质</dt>
							<dd>
								<span><input name="claimCaseCheatVO.committingNature" id="committingNatureOne" type="radio" value="01"  <s:if test="claimCaseCheatVO.committingNature == 01">checked="checked"</s:if> />团伙</span>
								<span><input name="claimCaseCheatVO.committingNature" id="committingNatureTwo" type="radio" value="02"  <s:if test="claimCaseCheatVO.committingNature == 02">checked="checked"</s:if>  />个人</span>
							</dd>
						</dl>
						<dl>
							<dt>公检法立案</dt>
							<dd>
								<span><input name="claimCaseCheatVO.inspectionRegisterFlag" id="inspectionRegisterFlagOne" type="radio" value="1"  onclick="inspectionRegisterFlagClick(this.value)" <s:if test="claimCaseCheatVO.inspectionRegisterFlag == 1">checked="checked"</s:if>/>是</span>
								<span><input name="claimCaseCheatVO.inspectionRegisterFlag" id="inspectionRegisterFlagTwo" type="radio" value="0"  onclick="inspectionRegisterFlagClick(this.value)" <s:if test="claimCaseCheatVO.inspectionRegisterFlag != 1">checked="checked"</s:if>/>否</span>
							</dd>
						</dl>
						<dl>
							<dt>公检法立案日期</dt>
							<dd>
								<input type="expandDateYMD" name="claimCaseCheatVO.inspectionRegisterTime" id="inspectionRegisterTimeId" <s:if test="claimCaseCheatVO.inspectionRegisterFlag != 1"> disabled = "disabled" </s:if> class="date" value="<s:date name="claimCaseCheatVO.inspectionRegisterTime" format='yyyy-MM-dd' />"/>
							</dd>
						</dl>
						<dl>
							<dt>欺诈识别途径</dt>
							<dd>
								 <Field:codeTable cssClass="combox title"  name="claimCaseCheatVO.cheatDistinguishChannel" value="${claimCaseCheatVO.cheatDistinguishChannel}"  
								 onChange="checkOtherCheatOption(this.value);"
                             		tableName="APP___CLM__DBUSER.T_CHEAT_DISTINGUISH_CHANNEL" nullOption="true" id="cheatDistinguishChannelId"></Field:codeTable>
                             	  
							</dd>
							<dd><input style="width:150%" maxlength="20" type="text" name="claimCaseCheatVO.otherCheatOption" id="otherCheatOptionId" value="${ claimCaseCheatVO.otherCheatOption}"/></dd>
						</dl>
									<dl style="width: 100%;height: auto">
											<dt>欺诈实施人员</dt> 
											 <dd  style="width: 90%; margin-left: 10%" >
												<span class="spanbox3" style="width: 100%;">
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelOne" name="claimCaseCheatVO.cheatImplementationPersonnel" value="01" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("01") >=0}'>checked="checked"</s:if>/>内勤
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelTwo" name="claimCaseCheatVO.cheatImplementationPersonnel" value="02" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("02") >=0}'>checked="checked"</s:if>/>代理人
													</span>
													<span>
														<input  type="checkbox"
														id="cheatImplementationPersonnelThree" name="claimCaseCheatVO.cheatImplementationPersonnel" value="03" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("03") >=0}'>checked="checked"</s:if>/>投保人
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelFour" name="claimCaseCheatVO.cheatImplementationPersonnel" value="04" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("04") >=0}'>checked="checked"</s:if>/>被保险人
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelFive" name="claimCaseCheatVO.cheatImplementationPersonnel" value="05" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("05") >=0}'>checked="checked"</s:if>/>受益人
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelSix" name="claimCaseCheatVO.cheatImplementationPersonnel" value="06" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("06") >=0}'>checked="checked"</s:if>/>中介渠道
													</span>
													<span>
														<input type="checkbox"
														id="cheatImplementationPersonnelSeven" name="claimCaseCheatVO.cheatImplementationPersonnel" value="07" onclick="cheatImplementationPersonnelSevenRegister(this);" <s:if test='%{claimCaseCheatVO.cheatImplementationPersonnel.indexOf("07") >=0}'>checked="checked"</s:if>/>其他人员
													</span>
													<span>
														<input type="text" style="width:150%"
														id="otherImplementationPersonnel" maxlength="50" name="claimCaseCheatVO.otherImplementationPersonnel" value="${claimCaseCheatVO.otherImplementationPersonnel}" onblur="">
													</span>
												</span>
											</dd>
										</dl>
							</div>
						
            </div> 
            <div class="panelPageFormContent"> 
            	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">赔案计算信息
					</h1>
				</div>
            	<div class="tabdivclassbr">
	                <table class="list" width="100%" > 
	                <thead>
	                    <tr>
	                       <!--  理算金额 -->
	                        <th nowrap>赔付金额</th>
	                        <th nowrap>预付金额</th>
	                        <th nowrap>结算金额</th>
	                        <th nowrap>最终赔付金额</th>
	                        <th nowrap>拒赔金额</th>
	                    </tr>
	                </thead>
	                <tbody align="center" id="regCalcCase">
	                    <s:iterator value="%{claimCaseList}" status="st" id="claimCase">
	                    <tr>
	                        <td><s:property value="#claimCase.calcPay"/></td>
	                        <td><s:property value="#claimCase.advancePay"/></td>
	                        <td><s:property value="#claimCase.balancePay"/></td>
	                        <td><s:property value="#claimCase.actualPay"/></td>
	                        <td><s:property value="#claimCase.rejectPay"/></td>
	                    </tr>
	                    </s:iterator>
	                </tbody>
	            </table>
            </div>
        </div>
        
        <div class="panelPageFormContent">
        	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">理赔类型计算信息
					</h1>
				</div>
        	<div class="tabdivclassbr">
	            <table class="list"  width="100%">  
	                <thead>
	                    <tr>                   
	                        <th nowrap>理赔类型</th>
	                        <!-- 基础给付金额 -->
	                        <th nowrap>账单金额</th>
	                        <th nowrap>保单合计理算金额</th>
	                        <th nowrap>社保给付</th>
	                        <th nowrap>第三方给付</th>
	                        <!-- 实际给付金额 -->
	                        <th nowrap>核赔赔付金额</th>
	                    </tr>
	                </thead>
	                <tbody id="regCalcClmSubCase">
	                    <s:iterator value="claimTyepCalcList" status="st">
	                    <tr align="center">                  
	                        <td><s:property value="claimType"/></td>
	                        <td><s:property value="sumAmountBill"/></td>
	                        <td><s:property value="actualPay"/></td>                   
	                        <td><s:property value="paidAmount"/></td>
	                        <td><s:property value="sumAmount"/></td>
	                        <td><s:property value="actualPay"/></td>
	                    </tr>
	                    </s:iterator>
	                </tbody>
	            </table>
	        </div>
        </div>
        
        <div class="panelPageFormContent">
        	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">保单计算信息
					</h1>
				</div>
        	<div class="tabdivclassbr">    
	            <table class="list" width="100%" >
	                <thead>
	                    <tr>                   
	                        <th nowrap>保单号</th>
	                        <th nowrap>理赔类型</th>
	                        <!-- <th nowrap>保单险种号</th> -->
	                        <th nowrap>生效日期</th>
	                        <th nowrap>交至日期</th>
	                        <th nowrap>险种代码</th>
	                        <th nowrap>险种名称</th>
	                        <th nowrap>理算金额</th>
	                        <th nowrap>账户价值</th>
	                        <s:if test="claimCaseVO.showRiskFlag==1">
		                        <th nowrap>风险保额</th>                    
	                        </s:if>
	                    </tr>
	                </thead>
	                <tbody id="claimBusiProdTbody">
	                    <s:iterator value="%{claimProductList}" status="st"  id="claimBusiProd">
	                    <tr align="center">                 
	                        <td><s:property value="#claimBusiProd.policyCode"/></td>
	                        <td>
	                        <Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" value="${claimBusiProd.claimType}"/>
	                        </td>
	                        <%-- <td>
	                        <s:if test="#claimBusiProd.busiItemId != null">
	                        	<s:property value="#claimBusiProd.busiItemId"/>
	                        </s:if>
	                        </td> --%>                   
	                        <td><s:date name="#claimBusiProd.validDate" format="yyyy-MM-dd"/></td>
	                        <td><s:date name="#claimBusiProd.dueDate" format="yyyy-MM-dd"/></td>
	                        <td><s:property value="#claimBusiProd.busiProdCode"/></td>
	                        <td><s:property value="#claimBusiProd.productNameStd"/></td>
	                        <td><s:property value="#claimBusiProd.actualPay"/> </td>
	                        <td><s:property value="#claimBusiProd.accountValue"/></td>
	                        <s:if test="claimCaseVO.showRiskFlag==1">
	                        	<td><s:property value="#claimBusiProd.insuredAmount"/></td>
	                        </s:if>
	                    </tr>
	                    </s:iterator>
	                </tbody>
	            </table>  
	        </div>    
        </div>
        
        <div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">保项计算信息
					</h1>
				</div>
        	<div class="tabdivclassbr">
	            <table class="list" width="100%"  > 
	                <thead>
	                    <tr>                              
	                        <th nowrap>保单号</th>
	                        <th nowrap>险种代码</th>
	                        <th nowrap>保险责任</th>
	                        <th nowrap>责任起期</th>
	                        <th nowrap>责任止期</th>
	                        <th nowrap>宽限天数</th>
	                        <th nowrap>保额</th>
	                        <th nowrap>年度红利</th>
	                        <th nowrap>终了红利</th>
	                        <th nowrap>理算金额</th>
	                        <th nowrap>出险期间标识</th>
	                        <th nowrap>给付代码</th>   
	                        <th nowrap>调整金额</th>               
	                    </tr>
	                </thead>
	                <tbody id="registerCalc">
	                    <s:iterator value="%{queryClaimLiabList}"  status="st"  id="claimLiab">
	                    <tr align="center"  target="listIdInfo" rel="${listId}">
	                        <td>
	                        	<s:property value="#claimLiab.policyCode"/><%-- <input type="text" value='${claimLiabId}'> --%>
	                        	<input type="hidden" name='claimLiabVOs[${st.index}].claimLiabId' id="claimLiabId" value="<s:property value="#claimLiab.claimLiabId"/>">
	                        	<input type="hidden" flag="adjustPay" value="${claimLiab.adjustPay}"/>
	                        	<input type="hidden" flag="liabClaimType" value="${claimLiab.claimType}"/>
	                        	<input type="hidden" flag="policyCode" value="${claimLiab.policyCode}"/>
	                        	<input type="hidden" name='claimLiabVOs[${st.index}].liabId' value="<s:property value="#claimLiab.liabId"/>">
	                        	<input type="hidden" name='claimLiabVOs[${st.index}].caseId' value="<s:property value="#claimLiab.caseId"/>">
	                        	<input type="hidden" name='claimLiabVOs[${st.index}].itemId' value="<s:property value="#claimLiab.itemId"/>">
								<input type="hidden" name='claimLiabVOs[${st.index}].busiItemId' value="<s:property value="#claimLiab.busiItemId"/>">
								<input type="hidden" name='claimLiabVOs[${st.index}].busiProdCode' value="<s:property value="#claimLiab.busiProdCode"/>">
								<input type="hidden" name='claimLiabVOs[${st.index}].policyId' value="<s:property value="#claimLiab.policyId"/>">
								<input type="hidden" name='claimLiabVOs[${st.index}].productId' value="<s:property value="#claimLiab.productId"/>">
								<input type="hidden" name='claimLiabVOs[${st.index}].liabStartDate' value="<s:property value="#claimLiab.liabStartDate"/>">
								<input type="hidden" name='claimLiabVOs[${st.index}].liabEndDate' value="<s:property value="#claimLiab.liabEndDate"/>">
	                        </td>
	                        <td><s:property value="#claimLiab.busiProdCode"/></td>
	                        <td><s:property value="#claimLiab.liabName"/></td>                    
	                        <td><s:date name="#claimLiab.liabStartDate" format="yyyy-MM-dd"/></td>
	                        <td><s:date name="#claimLiab.liabEndDate" format="yyyy-MM-dd"/></td>
	                        <td>
	                        	<s:if test="#claimLiab.masterBusiItemId == null || #claimLiab.masterBusiItemId == ''">
	                        		<s:property value="#claimLiab.gracePeriod"/>
	                        	</s:if>
	                        </td>
	                        <td><s:property value="#claimLiab.proAmount"/></td>
	                        <td><s:property value="#claimLiab.proBonusSa"/></td>
							<td><s:property value="#claimLiab.payAmount"/></td>
	                        <td><s:property value="#claimLiab.calcPay"/></td>                    
	                        <td><s:property value=""/></td>
	                        <td>
	                            <s:if test="#claimLiab.liabConclusion == 6">	
								<span><input type="radio" name="claimLiabVOs[${st.index}].liabConclusion" value="1" >给</span>
								<span><input type="radio" name="claimLiabVOs[${st.index}].liabConclusion" value="6" checked >不给</span>
								</s:if>
								<s:elseif test="#claimLiab.liabConclusion != 6 && #claimLiab.liabConclusion != null">
									<span><input type="radio" name="claimLiabVOs[${st.index}].liabConclusion" value="1" checked >给</span>
									<span><input type="radio" name="claimLiabVOs[${st.index}].liabConclusion" value="6" >不给</span>
								</s:elseif>
								<s:else>
									<span><input type="radio" name="claimLiabVOs[${st.index}].liabConclusion" value="1" checked>给</span>
									<span><input type="radio" name="claimLiabVOs[${st.index}].liabConclusion" value="6" >不给</span>
								</s:else>
	                        </td>
	                        <td><s:property value="#claimLiab.adjustPay"/></td>
	                    </tr>
	                    </s:iterator>
	                </tbody>
	            </table>
	        </div>
        </div>  
    
                    
        <div class="formBarButton main_bottom">
				<ul>

					<li>
						<button class="but_blue" type="button"
							onclick="prev('14','${caseId}');">上一步</button>
					</li>

					<li>
						<button class="but_blue" type="button" onclick="saveClick(0)">保存</button>
					</li>
					<s:if test="claimCaseVO.taxPremiumFlag==1">
						<li>
							<button class="but_blue" id="createDocumentButtion" type="button" onclick="createDocument(this)" <s:if test="claimCaseVO.acceptDecision==2">disabled="true"</s:if>>生成缴费申请书</button>
						</li>
					</s:if>
					<li><a class="but_blue main_buta" width="1300" height="600"
						href="clm/register/autoMatchCalcLog_CLM_claimMatchCalcAction.action?leftFlag=0&menuId=${menuId}&caseId=${caseId}"
						lookupGroup="claimCaseVO">自动匹配理算日志</a></li>
					<li>
						<button class="but_blue" type="button" id="claimMatchNext"
							onclick="saveClick(1)">下一步</button>
					</li>

					<li>
						<button class="but_gray" type="button" onclick="exit()">退出</button>
					</li>
				</ul>
			</div> 
                  
    </form>
</div>
</div>