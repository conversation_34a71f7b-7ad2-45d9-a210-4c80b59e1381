<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript">
//==========初始化加载，查看时，取消勾选复选框===============//
if(jsonShowMap.length > 2){
	for (var j = 0; j < jsonShowMap.split(",").length; j++) {
	    var show =jsonShowMap.split(",")[j];
	    var checkboxid = "",checkboxname = "";
	    checkboxid = show.split("=")[0].trim();
	    checkboxname = show.split("=")[1].trim();
	    //社保给付
	    if(checkboxid == "social-Pay" && checkboxname == "show"){
	    	$("input[hiddenDivId='social-Pay']", navTab.getCurrentPanel()).attr("checked",true);
	    	$("#social-Pay", navTab.getCurrentPanel()).show("3000");
		}
	    //第三方给付
	    if(checkboxid == "third-Pay" && checkboxname == "show"){
	    	$("input[hiddenDivId='third-Pay']", navTab.getCurrentPanel()).attr("checked",true);
	    	$("#third-Pay", navTab.getCurrentPanel()).show("3000");
		}
	}
}
//===========================================================//
 

//单选按钮

function radiockeacdThird(obj){
	 
	//定位数据 
	var innerPage = $(obj).parent().parent().parent().parent()
			.parent();

	var pageFlag = $(innerPage).parent().attr("titles");
	var billType = $(innerPage).parent().attr("billType");

	var listId = $(obj).parent().find("#listId").val();
	
	 
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	 

  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
			+ pageFlag 
			+ '&caseId='
			+ caseId 
		 	+ '&claimBillPaidVO.listId=' + listId
		 	+'&claimInjuryVO.listId=' + listId;
	    $(innerPage).next().next().loadUrl(url);

	    $(innerPage).next().next().show("30000"); 

	   return;
}




function  deleteValuePaid(obj){
 
	
	var listId = $(obj).parent().find("#listId").val();
	 
	if(listId!=""&&listId!="0"){  
	$.ajax({ 'type':'post',
			'url':'clm/register/deleteClaimDutyInfo_CLM_tClaimDutyRegisterAction.action'+ '?claimBillPaidVO.listId=' + listId+'&claimTypecode[0]=5',
			'datatype':'json',
			'success':function(data){
				 if(data){ 
					 
					 $(obj).parent().parent().remove();
					 alertMsg.info("删除成功");
				 }else{
					 alertMsg.info("删除失败");
				 }
			    
			},
 			'error':function(){
 				alert("出错了！");
 			}
	 });
	}
	 
}

//用于伸缩按钮的伸缩功能
//$(document).ready(function(){
	  
$(".main_heading", $.pdialog.getCurrent()).off().toggle(function(){  
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
},function(){
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
 	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
});

//});

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]").bind("click",function(){
//		var id=$(this).attr("id").split("main_")[1];
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
}) 

</script>
         <div class="main_box">
                    <div class="main_heading"><h1><img src="images/tubiao.png">社保第三方给付录入<b class="maim_lpask"></b></h1></div>
                    <div class="main_lptwo" >
                     <div id="main_1" class="main_borderbg">
                            <ul class="main_ul">
                                <li class="clearfix">
                    		<div id="thirdPayNewsInt" class="pageFormContent">
                    					<div style="margin-left: 30px;">
											<input type="checkbox" name="otherTypecode[0]" value="0"
												hiddenDivId="social-Pay" onclick="smallChange(this)">社保给付 <input
												type="checkbox" name="otherTypecode[1]" value="1"
												hiddenDivId="third-Pay" onclick="smallChange(this)">第三方给付
										</div>
										<div class="panelPageFormContent" id="social-Pay" style="display: none"   titles="socialPay"  billType="5">
												<div class="divfclass" style="margin-left: 20px;">
														<h1>
															<img src="clm/images/three.png">社保给付信息录入
														</h1>
													</div>
											<!--  社保给付信息录入   div start  --> 
												<div class="tabdivclassbr"  style="overflow: auto;">
													<table class="list nowrap itemDetail" addButton="添 加"
														hiddenAdd="hiddenAdd" style="border: 2px solid #CCC;width: 100%;">
														<thead>
															<tr>
																<th >选择</th>
																<th >序号</th>
																<th  backValueFlag="feeCode" ><font class="point" color="red">* </font>费用代码</th>
																<th  backValueFlag="paidType" >给付类型</th>
																<th  backValueFlag="dataId" >收据编号</th>
																<th  backValueFlag="paidAmount" >报销金额</th>
																<th   backValueFlag="serviceOrgName" ><font class="point"
																	color="red">* </font>服务机构名称</th>
																<th  type="del" >操作</th>
															</tr>
														</thead>
														<tbody class="list" id="socialSBillPaidTBody">
															<s:iterator value="claimsocialSBillPaidVOlist" status="st"
																var="bp">
																<tr>
																	<td><input type='radio' name="radio" id='radio${st.index}'
																		value='0' onclick="radiockeacdThird(this)" class="MedRadio" />
																		<input type="hidden" value="${listId}" id="listId">
																	</td>
																	<td><input class="digits textInput" type="text"  style="width: 20px;"
																		value="${st.index+1}" /></td>
																	<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_CLAIM_FEE_CODE"  value="${bp.feeCode}" /></td>
																	<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_SOCIAL_PAY_TYPE"  
																         value="${bp.paidType}" /></td>
																	<td>${bp.dataId}</td>
																	<td>${bp.paidAmount}</td>
																	<td>${bp.serviceOrgName}</td>
																	<td><a class="btnDel" href="javascript:void(0)"  onclick="deleteValuePaid(this)"><input type="hidden" value="${listId}" id="listId"></td>
																</tr>
															</s:iterator>
														</tbody>
													</table> 
											   </div>
											<s:include value="addBarForDutyThirdPay.jsp" />
										</div>
										
										
										<div class="panelPageFormContent" id="third-Pay" titles="registerThirdPay"  billType="6" style="display: none">
												<div class="divfclass" style="margin-left: 20px;">
														<h1>
															<img src="clm/images/three.png">第三方给付信息录入
														</h1>
													</div>
											<!--  第三方给付信息录入   div start  -->  
											<div class="tabdivclassbr"  >
												<table class="list nowrap itemDetail" addButton="添 加" 
													hiddenAdd="hiddenAdd" style="width: 100%;">
													<thead>
														<tr>
															<th nowrap>选择</th>
															<th nowrap>序号</th>
															<th nowrap backValueFlag="feeCode">费用代码<font class="point" color="red">*</font></th>
															<th nowrap backValueFlag="paidType" size="10">给付类型</th>
															<th nowrap backValueFlag="dataId" >收据编号</th>
															<th nowrap backValueFlag="paidAmount">报销金额<font class="point" color="red">*</font>
															<th nowrap backValueFlag="serviceOrgName">服务机构名称<font class="point" color="red">*</font></th>
															<th nowrap type="del" >操作</th>
														</tr>
													</thead>
													<tbody class="list" id="thirdPayTBody">
														<s:iterator value="claimBillPaidVOlist" status="st" var="bpo">
															<tr>
																<td><input type="hidden" value="${listId}" id="listId"><input type='radio' name="radio" id='radio${st.index}'
																	value='0' onclick="radiockeacdThird(this)" class="MedRadio" /></td>
																<td><input class="digits textInput" type="text" size="5"
																	value="${st.index+1}" readonly="readonly" /></td>
																<td><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_FEE_CODE"
																		 value="${bpo.feeCode}"  /></td>
																<td> <Field:codeValue tableName="APP___CLM__DBUSER.T_THIRD_PAY_TYPE" value="${bpo.paidType}" /></td>
									                            <td> ${bpo.dataId}</td>
									                            <td> ${bpo.paidAmount} </td>
																<td> ${bpo.serviceOrgName} </td>
																  
																<td><input type="hidden" value="${listId}" id="listId" ><a class="btnDel" href="javascript:void(0)" onclick="deleteValuePaid(this)"></a></td>
															</tr>
														</s:iterator>
													</tbody>
												</table> 
										   </div>
										<s:include value="addBarForDutyThirdPay.jsp" />
									</div>
									</div>
									                    		
                    </li>
                    </ul>
                    </div>
                    </div>
         </div>           
