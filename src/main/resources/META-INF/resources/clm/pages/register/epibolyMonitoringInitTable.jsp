﻿<%@ page language="java" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script src="clm/pages/register/dist/echarts.js"></script>
<script src="clm/pages/register/epibolyMonitoringInit.js"></script>
<script type="text/javascript">
	
</script>
<div class="main_tabdiv">
            <form id="pagerForm" method="post" 
				action="clm/register/queryOutsourceCase_CLM_epibolyMonitoringAction.action">
				<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
				<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	        </form>
		<form
				action="clm/register/queryOutsourceCase_CLM_epibolyMonitoringAction.action"
				method="post" class="pageForm required-validate" rel="pagerForm" name="queryOutsourceCaseId"
				id="queryOutsourceCaseId" onsubmit="return  navTabSearch(this,'outsourceCaseTable')">
				<div class="pageFormInfoContent">
					<dl >
						<dt>管理机构</dt>
						<dd>
							<input style="width: 30px;border-right:0px" type="text" size="2" name="outsourceIfnoVO.organCode"
								id="menuBtn" value="${outsourceIfnoVO.organCode}" class="organ" clickId="monInitBtn"
								showOrgName="branchname" needAll="true" /> <input
								style="width:110px;" type="text" size="11" name="outsourceIfnoVO.organName"
								id="branchname" value="${outsourceIfnoVO.organName}" readOnly class="" />
							<a id="monInitBtn" class="btnLook" href="#" style="position: relative;"></a>
						</dd>
					</dl>
					<dl >
						<dt>外包商</dt>
						<dd>
							<s:select cssClass="combox title" name="outsourceIfnoVO.outsourceId" id="outsourceName_epibolyMonitoringInit"
								value="outsourceIfnoVO.outsourceId" list="outsourceIfnoVOList"
								listKey="outsourceId" listValue="outsourceName" headerKey=""
								headerValue="全部" >
							</s:select>
						</dd>
					</dl>
					<dl >
						<dt>外包案件类型</dt>
						<dd>
							<Field:codeTable cssClass="combox title" name="outsourceIfnoVO.outsourceTypeCode" id="outsourceTypeCode_epibolyMonitoringInit"
								value="${outsourceCaseVO.outsourceTypeCode }" allOption="true"
								tableName="APP___CLM__DBUSER.T_OUTSOURCE_TYPE"/>
								<input name="outsourceCaseVO.outsourceTypeCode" id="outsourceTypeCode_epibolyMonitoringInitHident" type="hidden">
						</dd>
					</dl>
					<dl >
						<dt>发包起期</dt>
						<dd>
							<input name="outsourceCaseVO.sendPackageTime" type="expandDateYMD"
								id="sendPackageTime_epibolyMonitoringInit"
								value='<s:date name="outsourceCaseVO.sendPackageTime" format="yyyy-MM-dd"/>'
								required="required" class='date' flag="flag" 
								dateFmt='yyyy-MM-dd' size="22" style="border-right:0px;"/> <a class="inputDateButton"
								href="javascript:;">选择</a> 
								<input type="text" id="sendPackageStartTimeH" name="outsourceCaseVO.sendPackageStartTimeH" style="width: 40px;" size="2" onblur="checktime(this)" maxlength="5" value="00:00"/>
						</dd>
					</dl>
					<dl >
						<dt>发包止期</dt>
						<dd>
							<input name="outsourceCaseVO.receivedPackageTime" type="expandDateYMD"
								id="receivedPackageTime_epibolyMonitoringInit"
								value='<s:date name="outsourceCaseVO.receivedPackageTime" format="yyyy-MM-dd"/>'
								class='date' flag="flag" class="date" dateFmt='yyyy-MM-dd'
								size="22" style="border-right:0px;"/> <a class="inputDateButton" href="javascript:;">选择</a>
							<input type="text" name="outsourceCaseVO.receivedPackageTimeH" size="2" onblur="checktime(this)"
								maxlength="5" id="receivedPackageTimeH" value="23:59" style="width: 40px;"/>
						</dd>
					</dl>
					<dl >
						<dt>外包方式</dt>
						<dd>
							<s:select list="#{'':'显示全部',1:'驻场外包',2:'离场外包',3:'数采自采'}" id="outsourceWay"
								cssClass="notuseflagDelete combox title comboxDD" name="outsourceCaseVO.outsourceWay">
							</s:select>
						</dd>
					</dl>
						<div class="pageFormdiv">
							<button type="button" class="but_blue"
										id="queryOutsourceCase_epibolyMonitoringInit" onclick="queryEpibolyMonitoringOn()">查询</button>
						</div>
				</div>
	<div class="tabdivclassbr main_tabdiv">			
	<table class="list main_dbottom" style="width: 100%;" >
		<thead>
			<tr align="center">
				<!-- <th>选择</th> -->
				<th nowrap>管理机构</th>
				<th nowrap>外包商</th>
				<th nowrap>外包方式</th>
				<th nowrap>外包案件类型</th>
				<th nowrap>发包件数</th>
				<th nowrap>回传成功件数</th>
				<th nowrap>未回传件数</th>
				<th nowrap>影像问题件件数</th>
				<th nowrap>其他问题件件数</th>
				<!-- <th>回传失败件数</th> -->
				<th nowrap>回传平均时效(H)</th>
				<!-- <th>外包采集字节量</th>
								<th>费用</th> -->
			</tr>
		</thead>
		<tbody>
		<s:if test="imageFlag != null">
							<tr>
								<td colspan="100">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="100">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
		<s:if test="currentPage.PageItems.size()>0">
			<s:iterator value="currentPage.PageItems" var="outsourceVar">
				<tr height="25" align="center"  <s:if test="warnFlag==1">style="background-color: red;"</s:if> >
					<!-- <td><div align="center">
											<input type="radio" name="claimLiabRadio" id="claimLiabRadio"
												onclick="showchart(this);" value="">
										</div></td> -->
					<td><div align="center">
							<Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.t_udmp_org" />
						</div></td>
					<td><div align="center">${outsourceName}</div></td>
					<td><Field:codeValue value="${outsourceWay}"
									tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY" /></td>
					<td><div align="center">
							<Field:codeValue value="${outsourceTypeCode}"
								tableName="APP___CLM__DBUSER.t_outsource_type" />
						</div></td>
					<td><div align="center">${outsourceNumber}</div></td>
					<td><div align="center">${succeedNumber}</div></td>
					<td><div align="center">${waitManageNumber}</div></td>
					<td><div align="center">${imageNumber}</div></td>
					<td><div align="center">${elseNumber}</div></td>
					<%-- <td><div align="center">${failNumber}</div></td> --%>
					<td><div align="center">${backTime}</div></td>
					<%-- <td><div align="center">${byteQuantity}</div></td>
									<td><div align="center">${expensesNumber}</div></td> --%>
					<td style="display: none"><div align="center">${outsourceId}</div></td>
				</tr>
			</s:iterator>
		</s:if>
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
				name="select" onchange="navTabPageBreak({numPerPage:this.value},'outsourceCaseTable')"
				value="currentPage.pageSize">
			</s:select>
			<span>条，共${currentPage.total}条</span>
		</div>
		<div class="pagination" targetType="navTab" id="outsourceCaseTablePage"
			totalCount="${currentPage.total}"
			numPerPage="${currentPage.pageSize}" pageNumShown="10"
			currentPage="${currentPage.pageNo}" rel="outsourceCaseTable">
		</div>
	</div>
	</div>	
   </form>
</div>
