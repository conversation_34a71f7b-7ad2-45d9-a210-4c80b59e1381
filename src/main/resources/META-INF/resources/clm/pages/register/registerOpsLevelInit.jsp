<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
  
<script type="text/javascript" language="javascript">
 
var departmentCode='${claimOperationVOList[0].departmentCode}';
var departmentName='${claimOperationVOList[0].departmentName}';
var operationItemCode='${claimOperationVOList[0].operationItemCode}';
var operationItemName='${claimOperationVOList[0].operationItemName}';
var operationLevel='${claimOperationVOList[0].operationLevel}';
var paymentRate='${claimOperationVOList[0].paymentRate}';
var hospitalCode='${claimOperationVOList[0].hospitalCode}';
var hospitalName='${claimOperationVOList[0].hospitalName}';
var remark='${claimOperationVOList[0].remark}';
$("#departmentCode",navTab.getCurrentPanel()).val(departmentCode);
$("#departmentName",navTab.getCurrentPanel()).val(departmentName);
$("#operationItemCode",navTab.getCurrentPanel()).val(operationItemCode);
$("#operationItemName",navTab.getCurrentPanel()).val(operationItemName);
$("#operationLevel",navTab.getCurrentPanel()).val(operationLevel);
$("#paymentRate",navTab.getCurrentPanel()).val(paymentRate);
$("#hospitalCodeId",navTab.getCurrentPanel()).val(hospitalCode);
$("#hospitalName",navTab.getCurrentPanel()).val(hospitalName);
$("#remark",navTab.getCurrentPanel()).val(remark); 

</script>

<div class="panelPageFormContent">
	<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">手术分级给付信息录入
				</h1>
			</div>
	<div class="pageFormContent" id="dutyHospitalCostInitDetails">
		<form class="pageForm required-validate" id="${pageFlag}FormId"
			action="clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
			method="post"
			onsubmit="return validateCallback(this,dutyAjaxDone)">
			<input type="hidden" name="claimOperationVOList[0].caseId" value="${caseId}">
			<input type="hidden" name="claimOperationVOList[0].operationId" value="${claimOperationVOList[0].operationId}">
			<input type="hidden" name="claimTypecode[0]" value="15">
			<input type="hidden" name="pageFlag" value="${pageFlag}">
			<input type="hidden"   name="caseId"  value="${caseId}">
			
			
			<dl>
				<dt>科室 </dt>
				<dd >
				<input type="text" class="textInput" name="claimOperationVOList[0].departmentName" id="departmentName"  value="${departmentName}" readonly="readonly" />
				<input type="hidden" name="claimOperationVOList[0].departmentCode" id="departmentCode" value="${departmentCode }">
				</dd>
			</dl>
			
			<dl>
				<dt>
				<font class="point" color="red">* </font>手术项目
				</dt>
				<dd    sizset="1">
					<input id="operationItemCode"
								name="claimOperationVOList[0].operationItemCode"
								value="${operationItemCode}" size="10" type="hidden" />
					<input id="operationItemName" 
								name="claimOperationVOList[0].operationItemName"
								value="${operationItemName }"
								type="text" />
					 <a class="btnLook"
								href="clm/report/queryOpsLevelItem_CLM_hospitalInfoQueryAction.action"
								lookupGroup="claimOperationVOList[0]">查询手术项目</a>
				</dd>
			</dl>
			
			<dl sizset="0">
				<dt>级别</dt>
				<dd   sizset="0">
					<input name="claimOperationVOList[0].operationLevel" id="operationLevel" value="${operationLevel}" class="textInput" readonly="readonly" 
						type="text"   />
				</dd>
			</dl>
			
			<dl sizset="0">
				<dt>给付比例</dt>
				<dd   sizset="0">
					<input name="claimOperationVOList[0].paymentRate" id="paymentRate" value="${paymentRate}" class="textInput" readonly="readonly"
						type="text"   />
				</dd>
			</dl>
			
			<dl>
				<dt><font class="point" color="red">* </font>手术日期 </dt>
				<dd  >
					<input name="claimOperationVOList[0].operationDate" class="date textInput"  value="<s:date name='claimOperationVOList[0].operationDate' format='yyyy-MM-dd' />"
						onpropertychange="queryCalimDate(this);" type="expandDateYMD" id="opsLevelDate"
						maxLength="10"  
						datefmt="yyyy-MM-dd" /><a class="inputDateButton"
						href="javascript:;"  >选择</a> 
				</dd>
			</dl>
			<dl sizset="1">
				<dt><font class="point" color="red">* </font>医疗机构名称</dt>
				<dd    sizset="1">
					<input name="claimOperationVOList[0].hospitalCode" id="hospitalCodeId"
						type="hidden" size="10"   value="${hospitalCode}" />
						<input id="hospitalName" 
								name="claimOperationVOList[0].hospitalName"
								value="${hospitalName}"
								type="text" />
					<a class="btnLook"
						href="clm/report/showHospital_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId="
						  lookupGroup="claimOperationVOList[0]"></a>
				</dd>
			</dl>
			<dl sizset="16" style="width: 100%; height: auto;">
					<dt>备注</dt>
					<dd   sizset="16">
						<textarea name="claimOperationVOList[0].remark" cols="73" rows="3"
							>${claimOperationVOList[0].remark }</textarea>
					</dd>
				</dl>
		</form>
	</div>
</div>