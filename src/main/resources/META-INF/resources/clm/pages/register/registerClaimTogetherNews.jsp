<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript">

</script>
<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">同时出险（连生保单）
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
									<div class="main_bqtitle">
				                         <h1><img src="clm/images/three.png">同时出险（连生保单）</h1>
				                     </div>
				                     	<div id="greatNewsInt" class="panelPageFormContent">
											<div class="panelPageFormContent">
											
												<dl>
													<dt><font class="point" color="red">*　</font>赔案号</dt>
													<dd>
														<input  id="caseNo" type="text" name="togetherCaseVO.caseNo" value="${togetherCaseVO.caseNo}" readonly="readonly" nullOption="true" >
													</dd>
												</dl> 
													
												<dl>
													<dt ><font class="point" color="red">*　</font>出险人</dt>
													<dd>
														<input  id="insuredName" type="text" name="togetherCaseVO.insuredName" value="${togetherCaseVO.insuredName}"  readonly="readonly" nullOption="true">
													</dd>
												</dl>
												
												<dl>
													<dt ><font class="point" color="red">*　</font>理赔类型</dt>
													<dd>
														<input  id="claimType" type="text" name="togetherCaseVO.claimTypeName"  value="${togetherCaseVO.claimTypeName}"  readonly="readonly" nullOption="true">
													</dd>
												</dl>
												
												<dl>
													<dt ><font class="point" color="red">*　</font>生效日期</dt>
													<dd>
														<input  id="validdateDate" type="text" name="togetherCaseVO.validdateDate"  value="<s:date name='togetherCaseVO.validdateDate' format='yyyy-MM-dd' />" readonly="readonly" nullOption="true">
													</dd>
												</dl>
												
												<dl>
													<dt ><font class="point" color="red">*　</font>出险日期</dt>
													<dd>
														<input  id="claimDate" type="text" name="togetherCaseVO.claimDate"  value="<s:date name='togetherCaseVO.claimDate' format='yyyy-MM-dd' />"  readonly="readonly">
													</dd>
												</dl>
												
												<dl>
													<dt >出险原因</dt>
													<dd>
														<input  id="claimReason" type="text" name="togetherCaseVO.accReasonName"  value="${togetherCaseVO.accReasonName}"  readonly="readonly">
													</dd>
												</dl>
												
												<dl>
													<dt >意外细节</dt>
													<dd>
														<input  id="accidentDetail" type="text" name="togetherCaseVO.accidentDetail"  value="${togetherCaseVO.accidentDetail}"  readonly="readonly">
													</dd>
												</dl>
												<dl>
													<dt >理算金额</dt>
													<dd>
														<input  id="calcPay" type="text" name="togetherCaseVO.calcPay"  value="${togetherCaseVO.calcPay}"  readonly="readonly">
													</dd>
												</dl>
												<dl>
													<dt >当前状态</dt>
													<dd>
														<input  id="caseState" type="text" name="togetherCaseVO.caseStatusName"  value="${togetherCaseVO.caseStatusName}" readonly="readonly">
													</dd>
												</dl>
												<dl>
													<dt >结案日期</dt>
													<dd>
														<input  id="endCaseTime" type="text" name="togetherCaseVO.endCaseTime"  value="<s:date name='togetherCaseVO.endCaseTime' format='yyyy-MM-dd' />"  readonly="readonly">
													</dd>
												</dl>

												
											</div>
										</div>
				                     
								</div>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
