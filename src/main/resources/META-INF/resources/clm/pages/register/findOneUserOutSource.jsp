<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
 
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/pages/taskmanage/findOneUserOutsource.js" ></script>
<script type="text/javascript">
	//根据条件查询操作人员
    function queryMultiOperatorOutsource(){
    	$("#queryMultiOperatorFormOutSource", $.pdialog.getCurrent()).submit();
    }
  	//直接调用udmp提供的机构树方法
    function organManaOutsourceOrgan(){
    	$("#operatorOrganOutsource", $.pdialog.getCurrent()).orgtreegrn($.pdialog.getCurrent()); 
    }
  	//直接调用udmp提供的机构树方法
    function organManaOutsourceOrganNull(){
    	$("#operatorOrganOutsource", $.pdialog.getCurrent()).orgtreegrn($.pdialog.getCurrent()); 
    }
  	//确认时带回信息
  	function operatorDialogOutsource(){
  		var realName = $("#operatorPermissionTbodyOutsource",$.pdialog.getCurrent()).find("input:radio:checked#operatorPermissionOutsource").parent().parent().find("td:eq(3)").text();
 		var userId = $("#operatorPermissionTbodyOutsource",$.pdialog.getCurrent()).find("input:radio:checked#operatorPermissionOutsource").parent().parent().find("td:eq(4)").find("input").val();
		var userName = $("#operatorPermissionTbodyOutsource",$.pdialog.getCurrent()).find("input:radio:checked#operatorPermissionOutsource").parent().parent().find("td:eq(2)").text();
		$.bringBack({realName:realName,userName:userName,userId:userId});
  	}
  	
</script>
<!-- 查作业人员页面 -->
<form id="pagerForm" method="post"
	  action="clm/register/queryMultiOperatorOutsource_CLM_claimOutSourceManageAction.action">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>

<div class=""   layoutH="32">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
	</div>
		<form id="queryMultiOperatorFormOutSource"
			action="clm/register/queryMultiOperatorOutsource_CLM_claimOutSourceManageAction.action"
			method="post" class="pageForm required-validate"
			onsubmit="return dialogSearch(this)" rel="pagerForm">
	<div class="pageFormInfoContent">
			
			<dl id="areaCode">
				<dt>操作机构</dt>
				
				<dd id="organFlagOutsource">
							<div id="1">
								<s:if test="userVO.organCode != null">
									<input style="width: 30px;border-right:0px" type="text" size="2" name="userVO.organCode"
									id="operatorOrganOutsource" value="<s:property value='userVO.organCode'/>"  onclick='organManaOutsourceOrganNull(this)' clickId="casePoolBtn"
									showOrgName="branchnameOutsource" needAll="true" /> <input
									style="width:110px;" type="text" size="11" readOnly 
									id="branchnameOutsource" value="<Field:codeValue value="${userVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
								</s:if>
								<s:else>
									<input style="width: 30px;border-right:0px" type="text" size="2" name="userVO.organCode"
									id="operatorOrganOutsource" value="<s:property value='userVO.organCode'/>"  clickId="casePoolBtn"
									showOrgName="branchnameOutsource" needAll="true" /> <input
									style="width:110px;" type="text" size="11" readOnly 
									id="branchnameOutsource" value="<Field:codeValue value="${userVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
								</s:else>
							</div>
							<div id="2">
								<a id="casePoolBtn" onclick="organManaOutsourceOrgan()" class="btnLook" href="#" style="position: relative;"></a>
							</div>
						</dd>
			</dl>
			<dl>
				<dt>操作人员用户名</dt>
				<dd>
				
					<input type="text" name=userVO.userName id="operatorOutsource" value="${userVO.userName}">
				</dd>
			</dl>
		<div class="pageFormdiv">
			<button type="button" id="queryMultiOperatorOutsource" onclick="queryMultiOperatorOutsource()" class="but_blue">查询</button>
		</div>
	</div>
		</form>
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">作业人员列表
		</h1>
	</div>
	<div class="tabdivclassbr">
		<table id="" class="list main_dbottom" style="width: 100%;">
			<thead>
				<tr align="center">
					<th nowrap>序号</th>
					<th nowrap>操作机构</th>
					<th nowrap>操作人员用户名</th>
					<th nowrap>操作人员姓名</th>
				</tr>
			</thead>
			<tbody id="operatorPermissionTbodyOutsource">
				<s:if test="imageFlag eq 1">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
 				<s:iterator value="currentPage.pageItems" status="st">
					<tr align="center">
						<td><s:property value='#st.index+1' /><input type="radio"
							id="operatorPermissionOutsource" name="r1"></td>
						<td>${organCode}</td>
						<td>${userName}</td>
						<td>${realName}</td>
						<td><input type="hidden" value="${userId}"></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="dialogPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="dialog"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
	<div class="pageFormdiv  main_popup">
		<button type="button" id="operatorDialogOutsource" onclick="operatorDialogOutsource()" class="but_blue">确认</button>
		<button type="button" id="exit" class="but_gray close">退出</button>
	</div>
</div>


