﻿<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%-- <script src="clm/pages/register/copyDwzDate.js"></script>
 --%><%-- <script src="clm/pages/register/copyDwzDataBase.js"></script> --%>
<script type="text/javascript"> 
	//==========初始化加载 没录入过数据，查看时，取消勾选复选框===============//
	 
	
	if (jsonShowMap.length > 2) {
		for (var j = 0; j < jsonShowMap.split(",").length; j++) {
			var show = jsonShowMap.split(",")[j];
			var checkboxid = "", checkboxname = "";
			checkboxid = show.split("=")[0].trim();
			checkboxname = show.split("=")[1].trim();
			//门诊/住院 
			if (checkboxid == "Hospital" && checkboxname == "show") {
				$("input[hiddenDivId='registerHospital']",
						navTab.getCurrentPanel()).attr("checked", true);
				$("#registerHospital", navTab.getCurrentPanel()).show("3000");
			}
			//全球高端医疗费用
			if (checkboxid == "HighEnd" && checkboxname == "show") {
				$("input[hiddenDivId='registerMedical1News']",
						navTab.getCurrentPanel()).attr("checked", true);
				$("#registerMedical1News", navTab.getCurrentPanel()).show(
						"3000");
			}
			//防癌医疗费用
			if (checkboxid == "CancerPre" && checkboxname == "show") {
				$("input[hiddenDivId='registerMedical2News']",
						navTab.getCurrentPanel()).attr("checked", true);
				$("#registerMedical2News", navTab.getCurrentPanel()).show(
						"3000");
			}
			//CAR-T细胞免疫治疗费用
			if (checkboxid == "CARCell" && checkboxname == "show") {
				$("input[hiddenDivId='registerMedical3News']",
						navTab.getCurrentPanel()).attr("checked", true);
				$("#registerMedical3News", navTab.getCurrentPanel()).show(
						"3000");
			}
		}
	}
	//若是迁移的数据可以走下面的这个方法
	if (isMigration == 1 && isHospitalFlag) {
		//门诊/住院 
		$("input[hiddenDivId='registerHospital']",
				navTab.getCurrentPanel()).attr("checked", true);
		$("#registerHospital", navTab.getCurrentPanel()).show("3000");
		
		//全球高端医疗费用
		$("input[hiddenDivId='registerMedical1News']",
				navTab.getCurrentPanel()).attr("checked", true);
		$("#registerMedical1News", navTab.getCurrentPanel()).show("3000");
		
		//防癌医疗费用
		$("input[hiddenDivId='registerMedical2News']",
				navTab.getCurrentPanel()).attr("checked", true);
		$("#registerMedical2News", navTab.getCurrentPanel()).show("3000");
		
		//CAR-T细胞免疫治疗费用
		$("input[hiddenDivId='registerMedical3News']",
				navTab.getCurrentPanel()).attr("checked", true);
		$("#registerMedical3News", navTab.getCurrentPanel()).show("3000");
	}
	
	countDays();
	
	////意外事故发生日期和  计算出险日期计 算
	function   countDays(){
	
	var accidentTime = $("#accDate", navTab.getCurrentPanel()).val(); 
	var claimTime = $("#claimDate", navTab.getCurrentPanel()).val(); 
	
	var claimAccReasonId1 = $("#claimAccReasonId",
			navTab.getCurrentPanel()).val(); 
	 
	
	$("[ids=registerHospitalTBody]").find("tr").each(function(){
		  
	     var start=	$(this).find("#treatStartTD").html();
	     var end=	$(this).find("#treatEndTD").html(); 
	    
	     if(start != null && end != null){
	    	 if(claimAccReasonId1==2){//出险原因是意外 
		 	   $(this).find("#accidentDays").html(DateDiff(start,accidentTime));
	    	 }
		 	$(this).find("#claimDays").html(DateDiff(start,claimTime));
		} 
		
	});
	
	
	}
	
	
	function DateDiff(sDate1,sDate2){
		
		var aDate,oDate1,oDate2,iDays;
		iDays=0;
		if(sDate1!=null&&sDate2!=null){ 
		aDate=sDate1.split("-");
		
	    oDate1 = new   Date(aDate[0],aDate[1] - 1,aDate[2]);  
		aDate=sDate2.split("-"); 
	    oDate2 = new   Date(aDate[0],aDate[1] - 1,aDate[2]); 
		 
		var dif = oDate1.getTime() - oDate2.getTime();
		var days = Math.round(dif / (24 * 60 * 60 * 1000));
		 
		iDays=parseInt(Math.abs(oDate1-oDate2)/1000/60/60/24);
		}
		
		return iDays;
	}
	
	
	
	 
	//单选按钮

	//删除医疗单证
	function deleteClaimBillInfo(obj) {

		var billId = "";

		billId = $(obj).parent().find("input").attr("value");
		 

		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		if (billId != "") {
			$
					.ajax({
						'type' : 'post',
						'url' : 'clm/register/deleteClaimBillInfo_CLM_tClaimDutyRegisterAction.action?claimBillVO.billId='
								+ billId + '&claimBillVO.caseId=' + caseId,
						'datatype' : 'json',
						'success' : function(data) {
							var data = eval("(" + data + ")");
							if (data.flag == true) {
								alertMsg.info("删除成功！");
								$(obj).parent().parent().remove();
								$("#hospitalcost", navTab.getCurrentPanel())
										.hide("3000", function() {
											$(this).empty();
										});
							} else {
								alertMsg.info("删除失败！");
							}
						},
						'error' : function() {
							alertMsg.error("出错了！");
						}
					});
		}
		
	}

	//结束时间改变方法
	function queryCalimDate(obj) {
		
		var workDate=$("#hiddenWorkDate", navTab.getCurrentPanel()).val();
		
		var treatStartCursor = document.activeElement.id;
		//是否是详细的列表
		//var thisPage = $(obj).parent().parent().parent().attr("id");
		if(treatStartCursor==$(obj).attr("id")){
			return false;
		}
		 var hospitalCodeTd = $(obj).parent().parent().parent();
		 
		//治疗类型

		var treatType = $(hospitalCodeTd).find("#cureType").val();
		//开始时间
		var startTime = $(hospitalCodeTd).find("#treatStart").val();
		if($(hospitalCodeTd).find("#treatStart").val()!=null){
		  startTime = $(hospitalCodeTd).find("#treatStart").val().replace(
				/-/g, '-');
		}else{ 
			return;
		}
		if (startTime.length != 0) {
			//获取事故日期
			var accidentTime = $("#accDate", navTab.getCurrentPanel()).val()
					.replace(/-/g, '-');

			var accidentTimes = accidentTime.split("-");
			var dt1 = new Date(accidentTimes[0],accidentTimes[1] - 1,accidentTimes[2]); 
			//获取出险日期
			var claimTime = $("#claimDate", navTab.getCurrentPanel()).val()
					.replace(/-/g, '-');
			
			var claimTimes = claimTime.split("-");
			var dt2 = new Date(claimTimes[0],claimTimes[1] - 1,claimTimes[2]);
			 
			//获取开始日期
			var startTime = $(hospitalCodeTd).find("#treatStart").val()
					.replace(/-/g, '-');
			var startTimes = startTime.split("-");
			var dt3 = new Date(startTimes[0],startTimes[1] - 1,startTimes[2]); 

			//获取结束日期
			var endTime="";
			  endTime = $(hospitalCodeTd).find("#treatEnd").val().replace(
					/-/g, '-');   

			var endTimes = endTime.split("-");
			var dt4 = new Date(endTimes[0],endTimes[1] - 1,endTimes[2]); 
			
			 //与当前时间校验
			if(workDate!=null){
			 
				var workDates = workDate.split("-");
				var dt6 = new Date(workDates[0],workDates[1] - 1,workDates[2]); 
				
				if(dt6.getTime()<dt3.getTime()){
					$(hospitalCodeTd).find("#treatStart").val(null);
					alertMsg.error("开始日期不能大于当前日期！");
					return;
				}
				if(dt6.getTime()<dt4.getTime()){
					$(hospitalCodeTd).find("#treatEnd").val(null);
					alertMsg.error("结束日期不能大于当前日期！");
					$("#treatStart", navTab.getCurrentPanel()).val("");
					$("#treatEnd", navTab.getCurrentPanel()).val("");
					return;
				}
			}  
			//住院天数（开始时间到结束时间之间的天数） 
			//计算门诊次数
			 
			//距离意外事故发生的天数
			var claimAccReasonId = $("#claimAccReasonId",
					navTab.getCurrentPanel()).val(); 
			if (claimAccReasonId == 2) {
				if (accidentTime != "") {
					var dif = dt3.getTime() - dt1.getTime();
					var days = Math.round(dif / (24 * 60 * 60 * 1000));
					if (days >= 0 && startTime != null) {
						$(hospitalCodeTd).find("#accidentDays2").html(days);
					} else {
						$(obj).val(null);
						alertMsg.error("开始日期必填且不小于事故日期！");
						return;
					}
				}
			}
			//距离医疗理赔类型出险日发生的天数
			if (claimTime != "") {
				var dif2 = dt3.getTime() - dt2.getTime();
				var times = Math.round(dif2 / (24 * 60 * 60 * 1000));
				if (times >= 0 && startTime != null) {
					$(hospitalCodeTd).find("#stayDays2").html(times);
				} else {
					$(obj).val(null);
					alertMsg.error("开始日期必填且不小于出险日期！");
					return;
				}
			}
			
			// MedRadioNum=0;
			if(treatType=="0"){
				//alert(treatType);
				if($(hospitalCodeTd).find("#treatStart").val()!="" 
						&& $(hospitalCodeTd).find("#treatEnd").val()!=$(hospitalCodeTd).find("#treatStart").val()){
					$(hospitalCodeTd).find("#treatEnd").val(startTime); 
				}
			}
			if (endTime != "") { 
				var dif = dt4.getTime() - dt3.getTime();
				var days = Math.round(dif / (24 * 60 * 60 * 1000));
				if (endTime >= startTime) {
					/* if (treatType == '0') {
						if (endTime > startTime) {
							alertMsg.error("如果治疗类型是“门诊”，开始日期必须等于结束日期！");
							//$(obj).val(null);
							//$(hospitalCodeTd).find("#treatEnd").val(startTime);
							$("#treatStart", navTab.getCurrentPanel()).val("");
							$("#treatEnd", navTab.getCurrentPanel()).val("");
						}
					} */
					if(treatType == 1){ //治疗类型是住院
						$(hospitalCodeTd).find("#stayDays").attr("value", days);
						$(hospitalCodeTd).find("#stayDaysHiddenId").attr("value", days);
					}					
					
				} else {
					if(endTime < startTime && treatType != '0'){
						alertMsg.error("开始日期不能大于结束日期！");
						$(obj).val(null);
						$(hospitalCodeTd).find("#treatEnd").val("");
						return;
					}
				}
			}
			
		}

	}
 

	function editOneLine(obj) {
		//定位数据 
		var innerPage = $(obj).parent().parent().parent().parent().parent().parent();

		var pageFlag = $(innerPage).parent().attr("titles");
		var billType = $(innerPage).parent().attr("billType");

		var billId = $(obj).parent().find("#billId").val();
		 
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	  

	  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
				+ pageFlag
				+ '&claimBillItemVO.caseId='
				+ caseId
				+ '&caseId='
				+ caseId
				+ '&claimBillVO.billType='
				+ billType
				+ '&claimBillItemVO.billId=' + billId
				+ '&claimBillVO.billId=' + billId;
		$(innerPage).next().next().loadUrl(url);

		$(innerPage).next().next().show("30000"); 

		return;
	}
	function editOneLine1(obj) {
		//定位数据 
		var innerPage = $(obj).parent().parent().parent().parent()
				.parent();

		var pageFlag = $(innerPage).parent().attr("titles");
		var billType = $(innerPage).parent().attr("billType");

		var billId = $(obj).parent().find("#billId").val();
		 
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		 

	  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
				+ pageFlag
				+ '&claimBillItemVO.caseId='
				+ caseId
				+ '&caseId='
				+ caseId
				+ '&claimBillVO.billType='
				+ billType
				+ '&claimBillItemVO.billId=' + billId
				+ '&claimBillVO.billId=' + billId;
		$(innerPage).next().next().loadUrl(url);

		$(innerPage).next().next().show("30000"); 

		return;
	}
	
	//用于伸缩按钮的伸缩功能
	// $(document).ready(function(){
		  
	   $(".main_heading", navTab.getCurrentPanel()).off().toggle(function(){  
	     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
			  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
		
	   },function(){
	     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
	    	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
	   });

	// });

	//多级标签的处理
	$(function(){    
		$("li>h5","[id^=main_]").bind("click",function(){
//	 		var id=$(this).attr("id").split("main_")[1];
		    var li=$(this).parent();
			var hasborder=$(this).attr("hasborder");
			
			if(li.hasClass("fold")){
				if (hasborder=="true"){
					$(this).parent().parent().parent().addClass("main_borderbg");
				}
				li.removeClass("fold");
				$(this).find("#two").removeClass("main_plus").addClass("main_minus");
				$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
				
				li.find(".main_foldContent").slideDown();
				
			}else{
				li.addClass("fold");
				if (hasborder=="true"){
					$(this).parent().parent().parent().removeClass("main_borderbg");
				}
				$(this).find("#two").removeClass("main_minus").addClass("main_plus");
				$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
				li.find(".main_foldContent").slideUp();
				
			}
		});
		
	})
	//门诊住院总费用，总调整金额显示-门诊/住院
	 function countValue(objTable){
	      var outPatient = 0;
		  var outCalc = 0;
		  var stay = 0;
		  var stayCalc = 0; 
	      $(objTable).find("#treatTypeTD").each(function(i,obj){
	    	  if($(this).text()=="门诊"){
	    		  outPatient+=   eval( $(this).parent().find("#sumAmount").text().trim()); 
	    		  outCalc+=eval( $(this).parent().find("#calcAmount").text().trim());
	    	  }else{
	    		     stay  +=    eval( $(this).parent().find("#sumAmount").text().trim()); 
	    		  stayCalc += eval( $(this).parent().find("#calcAmount").text().trim());
	    	  } 
	      });
	       $(objTable).parent().parent().find("#outPatient").text(outPatient);
	       $(objTable).parent().parent().find("#outCalc").text(outCalc);
	       $(objTable).parent().parent().find("#stay").text(stay);
	       $(objTable).parent().parent().find("#stayCalc").text(stayCalc);
	}
	 countValue($("[ids=registerHospitalTBody]")[0]); 
	  $("[ids=registerHospitalTBody]").load(function(){
		 countValue($("[ids=registerHospitalTBody]")[0]);
	 }) 
     // 门诊住院总费用，总调整金额显示-全球高端医疗费用
	 function countValue1(objTable1){
	      var outPatient1 = 0;
		  var outCalc1 = 0;
		  var stay1 = 0;
		  var stayCalc1 = 0; 
	      $(objTable1).find("#treatTypeTD").each(function(i,obj){
	    	  if($(this).text()=="门诊"){
	    		  outPatient1+=   eval( $(this).parent().find("#sumAmount1").text().trim()); 
	    		  outCalc1+=eval( $(this).parent().find("#calcAmount1").text().trim());
	    	  }else{
	    		     stay1  +=    eval( $(this).parent().find("#sumAmount1").text().trim()); 
	    		  stayCalc1 += eval( $(this).parent().find("#calcAmount1").text().trim());
	    	  } 
	      });
	       $(objTable1).parent().parent().find("#outPatient1").text(outPatient1);
	       $(objTable1).parent().parent().find("#outCalc1").text(outCalc1);
	       $(objTable1).parent().parent().find("#stay1").text(stay1);
	       $(objTable1).parent().parent().find("#stayCalc1").text(stayCalc1);   
	}
	 countValue1($("[ids=registerHospitalTBody]")[1]); 
	  $("[ids=registerHospitalTBody]").load(function(){
		 countValue1($("[ids=registerHospitalTBody]")[1]);
	 }) 
	
	 // 门诊住院总费用，总调整金额显示-全球高端医疗费用
      function countValue2(objTable2){
	      var outPatient2 = 0;
		  var outCalc2 = 0;
		  var stay2 = 0;
		  var stayCalc2 = 0; 
	      $(objTable2).find("#treatTypeTD").each(function(i,obj){
	    	  if($(this).text()=="门诊"){
	    		  outPatient2+=   eval( $(this).parent().find("#sumAmount2").text().trim()); 
	    		  outCalc2+=eval( $(this).parent().find("#calcAmount2").text().trim());
	    	  }else{
	    		     stay2  +=    eval( $(this).parent().find("#sumAmount2").text().trim()); 
	    		  stayCalc2 += eval( $(this).parent().find("#calcAmount2").text().trim());
	    	  } 
	      });
	       $(objTable2).parent().parent().find("#outPatient2").text(outPatient2);
	       $(objTable2).parent().parent().find("#outCalc2").text(outCalc2);
	       $(objTable2).parent().parent().find("#stay2").text(stay2);
	       $(objTable2).parent().parent().find("#stayCalc2").text(stayCalc2);       
	}
	 countValue2($("[ids=registerHospitalTBody]")[2]); 
	  $("[ids=registerHospitalTBody]").load(function(){
		 countValue2($("[ids=registerHospitalTBody]")[2]);
	 }) 
	 // 门诊住院总费用，总调整金额显示-全球高端医疗费用
      function countValue3(objTable3){
	      var outPatient3 = 0;
		  var outCalc3 = 0;
		  var stay3 = 0;
		  var stayCalc3 = 0; 
	      $(objTable3).find("#treatTypeTD").each(function(i,obj){
	    	  if($(this).text()=="门诊"){
	    		  outPatient3+=   eval( $(this).parent().find("#sumAmount3").text().trim()); 
	    		  outCalc3+=eval( $(this).parent().find("#calcAmount3").text().trim());
	    	  }else{
	    		     stay3  +=    eval( $(this).parent().find("#sumAmount3").text().trim()); 
	    		  stayCalc3 += eval( $(this).parent().find("#calcAmount3").text().trim());
	    	  } 
	      });
	       $(objTable3).parent().parent().find("#outPatient3").text(outPatient3);
	       $(objTable3).parent().parent().find("#outCalc3").text(outCalc3);
	       $(objTable3).parent().parent().find("#stay3").text(stay3);
	       $(objTable3).parent().parent().find("#stayCalc3").text(stayCalc3);       
	}
	  countValue3($("[ids=registerHospitalTBody]")[3]); 
	  $("[ids=registerHospitalTBody]").load(function(){
		  countValue3($("[ids=registerHospitalTBody]")[3]);
	 }) 
      
</script>
			<div class="main_box">
                    <div class="main_heading"><h1><img src="images/tubiao.png">医疗单证录入<b class="maim_lpask"></b></h1></div>
                    <div class="main_lptwo" >
                    <div id="main_1" class="main_borderbg">
                            <ul class="main_ul">
                                <li class="clearfix">
                    		<div style="margin-left: 30px;">
								<input type="checkbox" name="honspTypecode[1]" value="1"
									hiddenDivId="registerHospital" onclick="smallChange(this)">门诊/住院
								<input type="checkbox" name="honspTypecode[2]" value="2"
									hiddenDivId="registerMedical1News" onclick="smallChange(this)">全球高端医疗费用
								<input type="checkbox" name="honspTypecode[3]" value="3"
									hiddenDivId="registerMedical2News" onclick="smallChange(this)">防癌医疗费用
								<input type="checkbox" name="honspTypecode[4]" value="4"
									hiddenDivId="registerMedical3News" onclick="smallChange(this)">CAR-T细胞免疫治疗费用
								<input type="hidden" value="${billEntryFlag}" id="billEntryFlag"/>
							</div>		
                    	<div id="honspInt">
							
								<!-- 门诊/住院信息   div  start -->
								<div id="registerHospital" class="panelPageFormContent" style="display: none"
									titles="registerHonsp" billType="1">
							 			<div class="divfclass" style="margin-left: 20px;">
												<h1>
													<img src="clm/images/three.png">门诊/住院
												</h1>
											</div>
										<div class="tabdivclassbr" >
											<div style="overflow: auto;">
											<table class="list nowrap itemDetail" addButton="添  加"
												id="registerAddhas" hiddenAdd="hiddenAdd"
												style="width:100%;">
												<thead>
													<tr>
														<th type="enum" name="">选择</th>
							
														<th type="text">序号</th>
							
														<th backValueFlag="billNo"><font
															class="point" color="red">* </font>账单号</th>
							
														<th backValueFlag="treatType">治疗类型</th>
														<s:if test ="claimCaseVO.caseStatus >= 31">
															<th backValueFlag="hospitalCode">医院代码</th>
															<th backValueFlag="hospitalName">医院名称</th>
														</s:if>
														<s:if test ="claimCaseVO.caseStatus > 31">
															<th backValueFlag="medicalDeptCode">就诊科室</th>
														</s:if>
														<th backValueFlag="treatStart" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>开始时间</th>
							
														<th backValueFlag="treatEnd" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>结束时间</th>
														<th backValueFlag="outpatientTotal" ><font
															class="point" color="red">* </font>门诊次数</th>
							
														<th backValueFlag="stayDays" >住院天数</th>
							
														<th backValueFlag="accidentDays" >距离意外事故发生日天数</th>
							
														<th backValueFlag="claimDays" >距离出险日期天数</th>
							
							
														<th backValueFlag="sumAmount" >总费用</th>
							
														<th backValueFlag="deductAmount" >扣除费用</th>
							
														<th backValueFlag="calcAmount" >理算金额</th>
							
														<th >操作</th>
							
													</tr>
												</thead>
												<tbody class="list" ids="registerHospitalTBody">
													<input type="hidden" value="1">
													<s:iterator value="claimBillVOlist" status="st">
														<tr>
															<td><input type='radio' value='0'
																onclick="editOneLine(this)"  name="radio" class="MedRadio" /><input
																type="hidden" id="billId"  value="${billId}" /></td>
															<td> ${st.index+1} </td>
															<td>${billNo}</td>
															<s:if test="treatTypeCgFlag==\"1\"">
												       			<td id="treatTypeTD" style="color: red;"><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_CURE_TYPE" value="${treatType}" /><input type="hidden" value="${outpatientTreatType }"></td>
												       		</s:if>	
												       		<s:else>
																<td id="treatTypeTD"  ><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_CURE_TYPE" value="${treatType}" /><input type="hidden" value="${outpatientTreatType }"></td>
												       		</s:else>	
															<s:if test ="claimCaseVO.caseStatus >= 31">
																<s:if test="hospitalNameCgFlag==\"1\"">
																	<td style="color: red;">${hospitalCode}</td>
																	<td id="hospitalCode"  style="color: red;"><Field:codeValue tableName="APP___CLM__DBUSER.T_HOSPITAL" value="${hospitalCode}" /></td>
												       			</s:if>	
												       			<s:else>
												       				<td>${hospitalCode}</td>
																	<td id="hospitalCode"  ><Field:codeValue tableName="APP___CLM__DBUSER.T_HOSPITAL" value="${hospitalCode}" /></td>
												       			</s:else>	
															</s:if>
															<s:if test ="claimCaseVO.caseStatus > 31">
																<s:if test="medicalDeptCodeCgFlag==\"1\"">
																	<td id="medicalDeptCode" style="color: red;"><Field:codeValue tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" value="${medicalDeptCode}" /></td>
												       			</s:if>	
												       			<s:else>
																	<td id="medicalDeptCode"  ><Field:codeValue tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" value="${medicalDeptCode}" /></td>
												       			</s:else>
															</s:if>
															<s:if test="treatStartCgFlag==\"1\"">
																<td id="treatStartTD" style="color: red;"><s:date name='treatStart' format='yyyy-MM-dd' /></td>
												       		</s:if>	
												       		<s:else>
																<td id="treatStartTD"><s:date name='treatStart' format='yyyy-MM-dd' /></td>
												       		</s:else>
												       		<s:if test="treatEndCgFlag==\"1\"">
																<td id="treatEndTD" style="color: red;"><s:date name='treatEnd' format='yyyy-MM-dd' /></td>
												       		</s:if>	
												       		<s:else>
																<td id="treatEndTD"><s:date name='treatEnd' format='yyyy-MM-dd' /></td>
												       		</s:else>
												       		<s:if test="outpatientTotalCgFlag==\"1\"">
																<td id="counts" style="color: red;">${outpatientTotal}</td>
												       		</s:if>	
												       		<s:else>
																<td id="counts" >${outpatientTotal}</td>
												       		</s:else>
												       		<s:if test="stayDaysCgFlag==\"1\"">
																<td id="stayDays" style="color: red;">${stayDays}</td>
												       		</s:if>	
												       		<s:else>
																<td id="stayDays" >${stayDays}</td>
												       		</s:else>
												       		<s:if test="accidentDaysCgFlag==\"1\"">
																<td id="accidentDays" style="color: red;">${accidentDays}</td>
												       		</s:if>	
												       		<s:else>
																<td id="accidentDays" >${accidentDays}</td>
												       		</s:else>
												       		<s:if test="claimDaysCgFlag==\"1\"">
																<td id="claimDays" style="color: red;">${claimDays}</td>
												       		</s:if>	
												       		<s:else>
																<td id="claimDays" >${claimDays}</td>
												       		</s:else>
												       		<s:if test="sumAmountCgFlag==\"1\"">
																<td id="sumAmount" style="color: red;">${sumAmount}</td>
												       		</s:if>	
												       		<s:else>
																<td id="sumAmount" >${sumAmount}</td>
												       		</s:else>
												       		<s:if test="deductAmountCgFlag==\"1\"">
																<td style="color: red;">${deductAmount}</td>
												       		</s:if>	
												       		<s:else>
																<td>${deductAmount}</td>
												       		</s:else>
												       		<s:if test="calcAmountCgFlag==\"1\"">
																<td style="color: red;">${calcAmount}</td>
												       		</s:if>	
												       		<s:else>
																<td>${calcAmount}</td>
												       		</s:else>
															<td><input type="hidden" id="billId"  value="${billId}" />
																<a class="btnDel" href="javascript:void(0)"
																onclick="deleteClaimBillInfo(this)"></a></td>
														</tr>
													</s:iterator>
												</tbody>
											</table>
								 	   <div style="margin-top: 10px;">
										<table border="1" width="300" frame="void" rules=none>
											<tr>
												<th>门诊总费用:</th>
												<td align="left" width="100" id="outPatient"></td>
												<th>住院总费用:</th>
												<td id="stay"></td>
												</tr>
										</table>
										
									    </div>
									    <div style="margin-top: 10px;">
										<table border="1" width="300" frame="void" rules=none>
											<tr>
												<th>门诊调整金额:</th>
												<td align="left" width="100" id="outCalc"></td>
												<th>住院调整金额:</th>
												<td id="stayCalc"></td>
											 </tr>
										</table>
										</div>
									</div> 
    
									<!-- 门诊/住院信息   div  end -->
									</div>
									<s:include value="addBarForDutyNew.jsp" />
								</div>
							
							
							
							
								<!-- 全球高端医疗费用信息   div  start -->
								<div id="registerMedical1News" class="panelPageFormContent" style="display: none"
									titles="highMedical"   billType="2">
										<div class="divfclass" style="margin-left: 20px;">
												<h1>
													<img src="clm/images/three.png">全球高端医疗费用
												</h1>
											</div>
										<div class="tabdivclassbr" >
									    	<div style="overflow: auto;">
											<table class="list nowrap itemDetail" addButton="添  加"
												id="registerAddhas" hiddenAdd="hiddenAdd"
												style="width: 100%;">
												<thead>
													<tr>
														<th type="enum" name="" >选择</th>
							
														<th type="text" >序号</th>
							
														<th backValueFlag="billNo" ><font
															class="point" color="red">* </font>账单号</th>
															
														<th backValueFlag="isCostly" ><font
															class="point" color="red">* </font>昂贵医院标识</th>
															
														<th backValueFlag="appLiab" >申请保项</th>
														
														<th backValueFlag="liabType" >保项费用</th>
														
														<th backValueFlag="treatType">治疗类型</th>
							
														<th backValueFlag="treatStart" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>开始时间</th>
							
														<th backValueFlag="treatEnd" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>结束时间</th>
														<th backValueFlag="outpatientTotal" ><font
															class="point" color="red">* </font>门诊次数</th>
							
														<th backValueFlag="stayDays" >住院天数</th>
														
														<th backValueFlag="thirdPay" >第三方支付金额</th>
																					
														<th backValueFlag="medicalPay" >公费/社保支付金额</th>
														
														<th backValueFlag="sumAmount"  >总费用</th>
							
														<th backValueFlag="deductAmount" >扣除费用</th>
							
														<th backValueFlag="calcAmount"  >理算金额</th>
							
														<th >操作</th>
													</tr>
												</thead>
												<tbody class="list" ids="registerHospitalTBody">
													<input type="hidden" value="2">
													<s:iterator value="claimHighBillVOlist" status="st">
														<tr>
															<td><input type='radio' value='0'
																onclick="editOneLine(this)" name="radio" class="MedRadio" /><input
																type="hidden" id="billId"  value="${billId}" /></td>
															<td> ${st.index+1} </td>
															<td>${billNo}</td>
															<td>${isCostly}</td>
															<td><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_APP_LIAB" value="${appLiab}" /></td>
															<td><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_LIAB_TYPE" value="${LiabType}" /></td>
															<td id="treatTypeTD"  ><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_CURE_TYPE" value="${treatType}" /></td>
															<td  id="treatStartTD"><s:date name='treatStart' format='yyyy-MM-dd' /></td>
															<td id="treatEndTD"><s:date name='treatEnd' format='yyyy-MM-dd' /></td>
															<td id="counts"></td>
															<td id="stayDays">${stayDays}</td>
															<td id="thirdPay">${thirdPay}</td>
															<td id="medicalPay">${medicalPay}</td>
															<td id="sumAmount1">${sumAmount}</td>
															<td>${deductAmount}</td>
															<td id="calcAmount1">${calcAmount}</td>
															<td><input type="hidden" id="billId"  value="${billId}" />
																<a class="btnDel" href="javascript:void(0)"
																onclick="deleteClaimBillInfo(this)"></a></td>
														</tr>
													</s:iterator>
												</tbody>
											</table>
											 <div style="margin-top: 10px;">
										       <table border="1" width="300" frame="void" rules=none>
											       <tr>
												        <th>门诊总费用:</th>
												        <td align="left" width="100" id="outPatient1"></td>
												        <th>住院总费用:</th>
												        <td id="stay1"></td>
												   </tr>
										       </table>
										
									         </div>
									         <div style="margin-top: 10px;">
										        <table border="1" width="300" frame="void" rules=none>
											       <tr>
												       <th>门诊调整金额:</th>
												      <td align="left" width="100" id="outCalc1"></td>
												      <th>住院调整金额:</th>
												      <td id="stayCalc1"></td>
											      </tr>
										        </table>
									     </div>

								     </div>
									   
									</div>
									 
							
									  <s:include value="addBarForDuty.jsp" />
							
									<!-- 全球高端医疗费用信息   div  end -->
								</div>
							
								<!-- 全球高端医疗费用信息   div  end -->
							
								<!-- 防癌医疗费用信息   div  start -->
								<div id="registerMedical2News" class="panelPageFormContent" style="display: none"  titles="cancerMedical"   billType="3"> 
										<div class="divfclass" style="margin-left: 20px;">
												<h1>
													<img src="clm/images/three.png">防癌医疗费用
												</h1>
											</div>
										<div class="tabdivclassbr" >
											<div style="overflow: auto;">
											<table class="list nowrap itemDetail" addButton="添  加"
												id="registerAddhas" hiddenAdd="hiddenAdd"
												style="width: 100%;">
												<thead>
													<tr>
														<th type="enum" name="" >选择</th>
							
														<th type="text" >序号</th>
							
														<th backValueFlag="billNo" ><font
															class="point" color="red">* </font>账单号</th>
															
														<th backValueFlag="isCancer" ><font
															class="point" color="red">* </font>防癌定点医院标识</th>
							
														<th backValueFlag="treatType">治疗类型</th>
							
														<th backValueFlag="treatStart" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>开始时间</th>
							
														<th backValueFlag="treatEnd" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>结束时间</th>
														<th backValueFlag="outpatientTotal" ><font
															class="point" color="red">* </font>门诊次数</th>
							
														<th backValueFlag="stayDays" >住院天数</th>
							
														<th backValueFlag="otherPay" >第三方/公费/社保支付金额</th>
							
														<th backValueFlag="sumAmount" >总费用</th>
							
														<th backValueFlag="deductAmount"  >扣除费用</th>
							
														<th backValueFlag="calcAmount" >理算金额</th>
							
														<th>操作</th>
													</tr>
												</thead>
												<tbody class="list" ids="registerHospitalTBody">
													<input type="hidden" value="3">
													<s:iterator value="claimCancerBillVOlist" status="st">
														<tr>
															<td><input type='radio' value='0'
																onclick="editOneLine(this)" name="radio" class="MedRadio" /><input
																type="hidden" id="billId"  value="${billId}" /></td>
															<td> ${st.index+1} </td>
															<td>${billNo}</td>
															<td>${isCancer}</td>
															<td id="treatTypeTD" ><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_CURE_TYPE" value="${treatType}" /></td>
															<td id="treatStartTD"><s:date name='treatStart' format='yyyy-MM-dd' /></td>
															<td id="treatEndTD"><s:date name='treatEnd' format='yyyy-MM-dd' /></td>
															<td id="counts"></td>
															<td id ="stayDays">${stayDays}</td>
															<td id="otherPay">${otherPay}</td>
															 
															<td id="sumAmount2">${sumAmount}</td>
															<td>${deductAmount}</td>
															<td id="calcAmount2">${calcAmount}</td>
															<td><input type="hidden" id="billId"  value="${billId}" />
																<a class="btnDel" href="javascript:void(0)"
																onclick="deleteClaimBillInfo(this)"></a></td>
														</tr>
													</s:iterator>
												</tbody>
											</table>
											<div style="margin-top: 10px;">
										       <table border="1" width="300" frame="void" rules=none>
											       <tr>
												        <th>门诊总费用:</th>
												        <td align="left" width="100" id="outPatient2"></td>
												        <th>住院总费用:</th>
												        <td id="stay2"></td>
												   </tr>
										       </table>
										
									         </div>
									         <div style="margin-top: 10px;">
										        <table border="1" width="300" frame="void" rules=none>
											       <tr>
												       <th>门诊调整金额:</th>
												      <td align="left" width="100" id="outCalc2"></td>
												      <th>住院调整金额:</th>
												      <td id="stayCalc2"></td>
											      </tr>
										        </table>
									     </div>
										</div>	
										</div>
							
								 
									<s:include value="addBarForDuty.jsp" />
							
									<!-- 防癌医疗费用信息   div  end -->
								</div>
								
								
								<!--CAR-T细胞免疫治疗费用  div  start -->
								<div id="registerMedical3News" class="panelPageFormContent" style="display: none"  titles="CARCellMedical"   billType="4"> 
										<div class="divfclass" style="margin-left: 20px;">
												<h1>
													<img src="clm/images/three.png">CAR-T细胞免疫治疗费用
												</h1>
											</div>
										<div class="tabdivclassbr" >
											<div style="overflow: auto;">
											<table class="list nowrap itemDetail" addButton="添  加"
												id="registerAddhas" hiddenAdd="hiddenAdd"
												style="width: 100%;">
												<thead>
													<tr>
														<th type="enum" name="" >选择</th>
							
														<th type="text" >序号</th>
							
														<th backValueFlag="billNo" ><font
															class="point" color="red">* </font>账单号</th>
															
														<th backValueFlag="isCancer" ><font
															class="point" color="red">* </font>防癌定点医院标识</th>
							
														<th backValueFlag="treatType">治疗类型</th>
							
														<th backValueFlag="treatStart" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>开始时间</th>
							
														<th backValueFlag="treatEnd" dateFlag="dateFlag" ><font
															class="point" color="red">* </font>结束时间</th>
														<th backValueFlag="outpatientTotal" ><font
															class="point" color="red">* </font>门诊次数</th>
							
														<th backValueFlag="stayDays" >住院天数</th>
														
														<th backValueFlag="sumAmount" >总费用</th>
							
														<th backValueFlag="deductAmount"  >扣除费用</th>
							
														<th backValueFlag="calcAmount" >理算金额</th>
							
														<th>操作</th>
													</tr>
												</thead>
												<tbody class="list" ids="registerHospitalTBody">
													<input type="hidden" value="3">
													<s:iterator value="claimCARCellBillVOlist" status="st">
														<tr>
															<td><input type='radio' value='0'
																onclick="editOneLine(this)" name="radio" class="MedRadio" /><input
																type="hidden" id="billId"  value="${billId}" /></td>
															<td> ${st.index+1} </td>
															<td>${billNo}</td>
															<td>${isCancer}</td>
															<td id="treatTypeTD" ><Field:codeValue
																	tableName="APP___CLM__DBUSER.T_CURE_TYPE" value="${treatType}" /></td>
															<td id="treatStartTD"><s:date name='treatStart' format='yyyy-MM-dd' /></td>
															<td id="treatEndTD"><s:date name='treatEnd' format='yyyy-MM-dd' /></td>
															<td id="counts"></td>
															<td id ="stayDays">${stayDays}</td>
															<td id="sumAmount3">${sumAmount}</td>
															<td>${deductAmount}</td>
															<td id="calcAmount3">${calcAmount}</td>
															<td><input type="hidden" id="billId"  value="${billId}" />
																<a class="btnDel" href="javascript:void(0)"
																onclick="deleteClaimBillInfo(this)"></a></td>
														</tr>
													</s:iterator>
												</tbody>
											</table>
											<div style="margin-top: 10px;">
										       <table border="1" width="300" frame="void" rules=none>
											       <tr>
												        <th>门诊总费用:</th>
												        <td align="left" width="100" id="outPatient3"></td>
												        <th>住院总费用:</th>
												        <td id="stay3"></td>
												   </tr>
										       </table>
										
									         </div>
									         <div style="margin-top: 10px;">
										        <table border="1" width="300" frame="void" rules=none>
											       <tr>
												       <th>门诊调整金额:</th>
												      <td align="left" width="100" id="outCalc3"></td>
												      <th>住院调整金额:</th>
												      <td id="stayCalc3"></td>
											      </tr>
										        </table>
									     </div>
										</div>	
										</div>
							
								 
									<s:include value="addBarForDuty.jsp" />
							
									<!-- CAR-T细胞免疫治疗费用   div  end -->
								</div>
								
							
							
							</div>
							</li>
							</ul>
							</div>
         			</div>
        	</div>            


	<!--医疗单证录入  div 结束  -->