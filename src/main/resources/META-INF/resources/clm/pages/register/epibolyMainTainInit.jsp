<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript" src="clm/pages/register/epibolyMainTainInit.js"> </script>
<div class=""  layoutH="10">
	<form
		action="clm/register/queryOutsourceIfno_CLM_epibolyMainTainAction.action"
		method="post" class="pageForm required-validate"
		id="queryOutsourceIfnoIdTain" onsubmit="return  navTabSearch(this)">
		<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询条件
					</h1>
				</div>
			<div>
				<div class="pageFormInfoContent" >
					<dl >
						<dt>外包商标识</dt>
						<dd>
							<input type="text" value="${outsourceIfnoVO.outsourceFlag }" id="outsourceFlag"
								name="outsourceIfnoVO.outsourceFlag" />
						</dd>
					</dl>
					<dl >
						<dt>外包商名称</dt>
						<dd>
							<input type="text" value="${outsourceIfnoVO.outsourceName }" id="outsourceName"
								maxlength="120" name="outsourceIfnoVO.outsourceName" />
						</dd>
					</dl>
					<dl >
						<dt>有效标识</dt>
						<dd>
							<s:select list="#{'':'显示全部',0:'无效',1:'有效'}" cssClass="notuseflagDelete combox title comboxDD" id="effectiveFlag"
								name="outsourceIfnoVO.effectiveFlag">
							</s:select>
						</dd>
					</dl>
					<dl >
						<dt>外包商类型</dt>
						<dd>
							<s:select list="#{'':'显示全部',1:'医疗类外包商',2:'非医疗类外包商'}" id="vendorCode"
								cssClass="notuseflagDelete combox title comboxDD" name="outsourceIfnoVO.vendorCode">
							</s:select>
						</dd>
					</dl>
					<dl >
						<dt>外包方式</dt>
						<dd>
							<s:select list="#{'':'显示全部',1:'驻场外包',2:'离场外包',3:'数采自采'}" id="outsourceWay"
								cssClass="notuseflagDelete combox title comboxDD" name="outsourceIfnoVO.outsourceWay">
							</s:select>
						</dd>
					</dl>
					
					<div class="pageFormdiv">
						<button class="but_blue" type="button" onclick="queryEpibolyMain();">查询</button>
					</div>
				</div>
			</div>
		</div>
		<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询结果
					</h1>
				</div>
			<div >
				<div class="tabdivclassbr" >
							<table class="list" style="width: 100%;">
								<thead align="center">
									<tr>
										<th nowrap>选择</th>
										<th nowrap>序号</th>
										<th nowrap>外包商标识</th>
										<th nowrap>外包商名称</th>
										<th nowrap>有效标识</th>
										<th nowrap>外包商类型</th>
										<th nowrap>外包方式</th>
									</tr>
								</thead>
								<tbody id="outsourceVarId" align="center">
									<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="outsourceIfnoVOList == null || outsourceIfnoVOList.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
									<s:iterator value="outsourceIfnoVOList" status="var"
										var="outsourceVar">
										<tr>
											<td align="center"><input style="left: 48px;" type="radio" name="radio" value="0"
												onchange="noChecked(this)"
												onclick="showOutsourceInfo(this);" /></td>
											<td>${var.index+1}</td>
											<td>${outsourceFlag}</td>
											<td>${outsourceName}</td>
											<td style="display: none;">${caseAmountWarn}</td>
											<td><input type="hidden" value="${effectiveFlag }">
												<s:if test="effectiveFlag eq 1">
													有效
												</s:if> <s:else>
													无效
												</s:else></td>
											<td style="display: none;">${simpleCasePrice }</td>
											<td style="display: none;">${unSimpleCasePrice }</td>
											<td><input type="hidden" value="${vendorCode }">
												<s:if test="vendorCode eq 1">
													医疗类外包商
												</s:if> <s:else>
													非医疗类外包商
												</s:else></td>
											<td style="display: none;">${outsourceId }</td>
											<td><input type="hidden" value="${outsourceWay}"><Field:codeValue  tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY"
													value="${outsourceWay}" /></td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
				</div>
			</div>
		</div>
	</form>
	<form
		action="clm/register/addOutsourceIfno_CLM_epibolyMainTainAction.action"
		method="post" class="pageForm required-validate"
		id="addOutsourceIfnoId" onsubmit="return  validateCallback(this)">
		<div class="panelPageFormContent" id="outsourceIfnoDivId">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">外包商信息
					</h1>
				</div>
			<div>
				<div class="panelPageFormContent"  >
					<dl >
						<dt>
							<font color="red">* </font>外包商标识
						</dt>
						<dd>
							<input style="width: 30px;border-right:0px" type="text" readonly="readonly" value="LP" size="2"
								name="outsourceIfnoVO.outsourceFlag" id="outsourceFlagId" /> <input
								type="text" name="" size="8" value="理赔" style="width:110px;" readonly="readonly"/>
								<input  type="hidden" id="outsourceId" name="outsourceIfnoVO.outsourceId"/>
						</dd>
					</dl>
					<dl >
						<dt>
							<font color="red">* </font>外包商名称
						</dt>
						<dd>
							<input type="text" maxlength="30"
								name="outsourceIfnoVO.outsourceName" id="outsourceNameId" />
						</dd>
					</dl>
					<dl >
						<dt>案件数预警值</dt>
						<dd>
							<input type="text" name="outsourceIfnoVO.caseAmountWarn"
								id="caseAmountWarnId" onblur="checkzhengshu(this)" maxlength="8" />
						</dd>
					</dl>
					<dl >
						<dt>
							<font color="red">* </font>有效标识
						</dt>
						<dd>
							<select class="combox title"  id="effectiveFlagId" name="outsourceIfnoVO.effectiveFlag">
								<option value="1">有效</option>
								<option value="0">无效</option>
							</select>
						</dd>
					</dl>
					<dl >
						<dt>非医疗案件单价</dt>
						<dd>
							<input type="text" name="outsourceIfnoVO.simpleCasePrice"
								id="simpleCasePriceId" onblur="checkxiaoshu(this)" maxlength="15" />
						</dd>
					</dl>
					<dl >
						<dt>医疗案件单价</dt>
						<dd>
							<input type="text" name="outsourceIfnoVO.unSimpleCasePrice"
								id="unSimpleCasePriceId" onblur="checkxiaoshu(this)" maxlength="15" />
						</dd>
					</dl>
					<dl >
						<dt>
							<font color="red">* </font>外包商类型
						</dt>
						<dd>
							<select class="combox title"  id="vendorCodeId" name="outsourceIfnoVO.vendorCode">
								<option value="1">医疗类外包商</option>
								<option value="2">非医疗类外包商</option>
							</select>
						</dd>
					</dl>
					<dl >
						<dt>外包方式</dt>
						<dd>
						 	<Field:codeTable cssClass="notuseflagDelete combox title comboxDD"  name="outsourceIfnoVO.outsourceWay"  
                             tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY" id="outsourceWayId" nullOption="true"></Field:codeTable>     
						</dd>
					</dl>
				</div>
			</div>
		</div>
	</form>
	<div class="formBarButton">
		<ul>
			<li><button class="but_blue" type="button" onclick="saveOutsourceIfno();">保存</button></li>
			<li><button class="but_gray" type="button" onclick="exit()">退出</button></li>
		</ul>
	</div>
</div>