<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/pages/register/benefitInput.js"></script>

<script type="text/javascript">
$(function(){	
	//领款人姓名下来显示
	var payeeNameSelect  = '${payeeVO.payeeName}';
	//查询本次赔案相关领款人
	$.ajax({
		url : "clm/register/queryPayeeName_CLM_toBenefitInputPageAction.action?claimPayeeVO.caseId="+caseId,
		global : false,
		type : "POST",
		dataType : "json",
		async : false,
		success : function(payeeVOList) {
			if($("#payeeName",navTab.getCurrentPanel()).find("option").eq(0).html()!="请选择"){
				var optionStr = "<option value=''>请选择</option>";
				for(var i=0; i <payeeVOList.length; i++){
					if(payeeVOList[i].beneId != ""){
						var payeeName = payeeVOList[i].payeeName;
						optionStr += "<option value='" + payeeName +"'></option>";
					}
				}	
				$("#payeeName",navTab.getCurrentPanel()).append(optionStr);
			}
			if(payeeNameSelect!=null){
				$("#payeeName",navTab.getCurrentPanel()).find("option").each(function (){
					if ($(this).val() == payeeNameSelect) {
						$(this).attr("selected", "selected");
					}
				})
			}
		}
	});
	//省市县初始化
	var beneProvince = '${beneVO.beneProvince }';
	var beneCity = '${beneVO.beneCity }';
	var beneDistrict = '${beneVO.beneDistrict }';
	cityAssign(beneProvince,beneCity,beneDistrict);
	
	//省市县初始化
	var payeeRelation = $("#payeeRelation", navTab.getCurrentPanel()).val();
	if(payeeRelation != "00"){
		var payeeProvince = '${payeeVO.payeeState }';
		var payeeCity = '${payeeVO.payeeCity }';
		var payeeDistrict = '${payeeVO.payeeDistrict }';
		payeeCityAssign(payeeProvince,payeeCity,payeeDistrict);
	}
	//对公支付初始化
	var contraryPayFlag = '${claimPayVO.contraryPayFlag}';
	if(contraryPayFlag == 1){
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).attr("checked","checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",false);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",false);
	}else{
		$("#contraryPayFlagCheckBoxId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#legalPersonQueryPayeeStyle", navTab.getCurrentPanel()).attr("disabled",true);
		$("#legalPersonQueryPayeeStyleButton", navTab.getCurrentPanel()).attr("disabled",true);
	}
	//法人证件类型展示
	var beneLegalPersonId = '${beneVO.legalPersonId}';
	if(beneLegalPersonId.length > 0){
		$("#beneCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#beneCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	}	
	var payeeLegalPersonId = '${payeeVO.legalPersonId}';
	if(payeeLegalPersonId.length > 0){
		$("#payeeCertiType", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).prev().removeAttr("disabled", "disabled");
	}
	
	//性别、出生日期、国籍、职业代码、电话、地址
	var beneLegalPersonId = $("#beneLegalPersonId", navTab.getCurrentPanel()).val();
	var payeeLegalPersonId = $("#payeeLegalPersonId", navTab.getCurrentPanel()).val();
	if(beneLegalPersonId.length > 0){
		$("#beneSex", navTab.getCurrentPanel()).selectMyComBox("9");
		claimFireEvent($("#beneSex",navTab.getCurrentPanel()) );
		$("#beneSex", navTab.getCurrentPanel()).val("").trigger("change");
		$("#beneSex", navTab.getCurrentPanel()).removeClass("required");
		$("#beneSex", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneSex", navTab.getCurrentPanel()).attr("disabled","disabled");
	
		$("#beneBirth", navTab.getCurrentPanel()).attr("value","");
		$("#beneBirth", navTab.getCurrentPanel()).attr("disabled","disabled");
	
		$("#clmtNation", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#clmtNation", navTab.getCurrentPanel()).removeClass("required");
	
		$("#beneJobIdFire", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#beneJobIdFire", navTab.getCurrentPanel()).attr("value","");
		$("#beneJobIdFire", navTab.getCurrentPanel()).removeClass("required");
	
		$("#benePhone", navTab.getCurrentPanel()).attr("value","");
		$("#benePhone", navTab.getCurrentPanel()).removeClass("required");
		$("#benePhone", navTab.getCurrentPanel()).attr("disabled","disabled");
	
		$("#beneProvince", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#beneCity", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#beneDistrict", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#beneAddress", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#beneProvince", navTab.getCurrentPanel()).removeClass("required");
		$("#beneCity", navTab.getCurrentPanel()).removeClass("required");
		$("#beneDistrict", navTab.getCurrentPanel()).removeClass("required");
		$("#beneAddress", navTab.getCurrentPanel()).removeClass("required");
		$("#beneAddress", navTab.getCurrentPanel()).val("");
	}
	if(payeeLegalPersonId.length > 0){
		$("#payeeSex", navTab.getCurrentPanel()).selectMyComBox("9");
		claimFireEvent($("#beneSex",navTab.getCurrentPanel()) );
		$("#payeeSex", navTab.getCurrentPanel()).val("").trigger("change");
		$("#payeeSex", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeSex", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#payeeSex", navTab.getCurrentPanel()).attr("disabled","disabled");
		
		$("#payeeBirth", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeBirth", navTab.getCurrentPanel()).attr("value","");
		$("#payeeBirth", navTab.getCurrentPanel()).attr("disabled","disabled");

		$("#clmtNationL", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#clmtNationL", navTab.getCurrentPanel()).removeClass("required");

		$("#payeeJobIdFire", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#payeeJobIdFire", navTab.getCurrentPanel()).attr("value","");
		$("#payeeJobIdFire", navTab.getCurrentPanel()).removeClass("required");

		$("#payeePhone", navTab.getCurrentPanel()).attr("value","");
		$("#payeePhone", navTab.getCurrentPanel()).removeClass("required");
		$("#payeePhone", navTab.getCurrentPanel()).attr("disabled","disabled");

		$("#payeeProvince", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#payeeCity", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#payeeDistrict", navTab.getCurrentPanel()).selectToInputDisabled("disabled");
		$("#payeeAddress", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeProvince", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeCity", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeDistrict", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeAddress", navTab.getCurrentPanel()).removeClass("required");
		$("#payeeAddress", navTab.getCurrentPanel()).val("");
	}
});
	//为银行编码赋值
	var hiddenBank = $("#hiddenBank", navTab.getCurrentPanel()).val();
	$("#paymentBankCodeId", navTab.getCurrentPanel()).find("option").each(function() {
		if ($(this).val() == hiddenBank) {
			$(this).attr("selected", "selected");
		}
	});
	
	
</script>

<!-- 受益人领款人详细信息 -->
<form id="benePayeeInfoFor"
	action="clm/register/saveBenePayeeInf_CLM_toBenefitInputPageAction.action"
	method="post" class="pageForm required-validate"
	onsubmit="return validateCallback(this, idAjaxDone)">
	<input type="hidden" name="claimPayVO.caseId" id="hiddenCaseId"value=""> 
	<input type="hidden" name="paymentPlanVO.caseId" value="${claimPayVO.caseId}"> 
	<input type="hidden" name="claimPayVO.policyId" id="hiddenPolicyId" value=""> 
	<input type="hidden" name="claimPayVO.busiItemId" id="hiddenBusiItemId" value=""> 
	<input type="hidden" name="claimPayVO.busiProdCode" id="hiddenbusiProdCode" value="">
	<input type="hidden" name="claimPayVO.policyCode" id="hiddenPolicyCode" value=""> 
	<input type="hidden" name="claimPayVO.isInstalment" id="hiddenIsInstalment" value="1"> 
	<input type="hidden" name="beneVO.beneId" id="hiddenBeneId" value=""> 
	<input type="hidden" name="payeeVO.payeeId" id="hiddenPayeeId" value="${payeeVO.payeeId }">
	<input type="hidden" name="flag" id="claimPayflagId" value="">
	<input type="hidden" value="${infoFlag}" id="infoFlagId"/>
	<input type="hidden" value="${claimTypeFlag}" id="claimTypeFlagBene"/>
    <input type="hidden" name="isBeneExtended" id="isBeneExtended" value="0"/>
    <input type="hidden" name="isPayeeExtended" id="isPayeeExtended" value="0"/>
	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">受益人信息
					</h1>
				</div>
				<div id="contraryPayFlagSpan">
						<dl style="width: 100%;">
					<dt style="width: 10%;">
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<a id ="legalPersonQueryBeneStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoBene();"  >
						<button type="button" class="but_blue " id="legalPersonQueryBeneStyleButton">法人信息录入</button>
						</a>
			<a lookupGroup="beneVO" id ="legalPersonQueryBene" href="javaScript:void(0)" rel="legalPersonQueryBene" style="display:none;" width="1000" height="450"  > 法人信息录入</a>
					</dt>
					</dl>
					</div>
				<input type="hidden" name="beneVO.legalPersonId" value="${beneVO.legalPersonId}" id="beneLegalPersonId">
		<dl>
			<dt><font>* </font>受益人与被保人关系</dt>
			<dd>
			 	<Field:codeTable name="beneVO.beneRelation" id="beneRelation" cssClass="notuseflagDelete combox title selectChange"
					value="${beneVO.beneRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" onChange="queryCustomer(this);"  nullOption="true" 
					whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
			</dd>
		</dl>
		<!-- 71408 投保人与受益人关系  -->
	   	<dl>
			<dt><font>* </font>投保人与受益人关系</dt>
			<dd>
			 	<Field:codeTable name="claimPayVO.beneHolderRelation" id="beneHolderRelation" value="${claimPayVO.beneHolderRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" cssClass="notuseflagDelete combox title selectChange" nullOption="true" 
			   		whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
			</dd>
		</dl>

		
		<dl>
			<dt><font>* </font>姓名</dt>
			<dd>
				<input type="text" name="beneVO.beneName" id="beneName" <s:if test="beneVO.beneRelation==00">readonly="readonly"</s:if>
					value="${beneVO.beneName }" onblur="getBeneInfoRegi();"	/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>性别</dt>
			<dd>
				<s:if test="beneVO.beneRelation==00">
					<Field:codeTable cssClass="combox title" disabled="true" name="beneVO.beneSex" id="beneSex" value="${beneVO.beneSex}" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2,9)" nullOption="true" onChange="getBeneInfoRegi();"/>				
				</s:if>
				<s:else>
					<Field:codeTable cssClass="combox title"  name="beneVO.beneSex" id="beneSex" value="${beneVO.beneSex}" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2,9)" nullOption="true" onChange="getBeneInfoRegi();"/>
				</s:else>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>出生日期</dt>
			<dd>
				<input type="expandDateYMD" name="beneVO.beneBirth" id="beneBirth"  <s:if test="beneVO.beneRelation==00">disabled="disabled"</s:if>
					value="<s:date name="beneVO.beneBirth" format="yyyy-MM-dd"/>" onPropertyChange="getBeneInfoRegi();"/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>证件类型</dt>
			<dd>
				<Field:codeTable cssClass="combox title"  name="beneVO.beneCertiType" id="beneCertiType"
					value="${beneVO.beneCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0" 
					nullOption="true" onChange="getBeneInfoRegi();"
				    whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','73','8')"  orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','73','012','8','013', code)" />
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>证件号码</dt>
			<dd>
				<input type="text" name="beneVO.beneCertiNo" id="beneCertiNo"
					value="${beneVO.beneCertiNo }" onchange="getBeneInfoRegi();" onblur="checkbeneCertiNo();" onselectstart="return false" oncopy="return false" onpaste="return false"/>
			</dd>
		</dl>
		<dl>
			<dt><font color="red">* </font>受益人国籍</dt>
			<dd>
				<Field:codeTable cssClass="selectToInput"  name="beneVO.beneNation" id="clmtNation"
					value="${beneVO.beneNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" whereClause="country_code not in ('GAT')" defaultValue="CHN" nullOption="true" orderBy="decode(country_code,'CHN','A',country_code)"
					onChange="getBeneInfoRegi();"/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>证件有效起期</dt>
			<dd>
				<input type="expandDateYMD" name="beneVO.beneCertiStart" id="beneCertiStart" value="<s:date name="beneVO.beneCertiStart" format="yyyy-MM-dd"/>" onPropertyChange="getBeneInfoRegi();"/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>证件有效止期</dt>
			<dd>
				<input type="expandDateYMD" name="beneVO.beneCertiEnd"  
					id="beneCertiEnd" value="<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>" />
				<input type="hidden" name="beneVO.beneCertiEnd" id="beneCertiEndHidentId" value="<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>"/>	
					<input type="checkbox" id="beneCertiEndCheckBoxId" onclick="beneCertiEndCheckBox(this)"/>长期
			</dd>
		</dl>
		<!-- 51699增加受益相关信息录入 -->
		<dl>
			<dt><font>* </font>受益人职业代码</dt>
			<dd>
				<Field:codeTable  id="beneJobIdFire"
										name="beneVO.beneJobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
										whereClause="job_code not in ('Y001023') and display_order not in ('0')"  orderBy="DISPLAY_ORDER"
										nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
										value="${beneVO.beneJobCode}"/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>受益人电话</dt>
			<dd>
			   <input type="text" name="beneVO.benePhone"  id="benePhone"  value="${beneVO.benePhone }" onblur="checkPhoneAndMobile(this);"/>
			</dd>
		</dl>
		<div class="mian_site">
			<dl>
				<dt><font>* </font>受益人地址</dt>
			</dl>
			<div class="main_detail">
				<dl>
					<dd>
					   	<input type="hidden"  value="${beneVO.beneProvince }" id="beneProvinceReserve">
						<input type="hidden"  value="${beneVO.beneCity }" id="beneCityReserve">
						<input type="hidden"  value="${beneVO.beneDistrict }" id="beneDistreactReserve">
                           	<Field:codeTable cssClass="selectToInput"  name="beneVO.beneProvince" onChange="ProvinceChangeReportData(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="beneProvince" 
                           	value="${beneVO.beneProvince }"
                           	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
						<span>省/直辖市<font style="color:#FF0000">* </font></span>
					</dd>
				</dl>
				<dl>
					<dd>
						<select class="selectToInput" name="beneVO.beneCity" onchange="cityChageReportData(this);" id='beneCity' >
							</select>
						<span>市<font style="color:#FF0000">* </font></span>
					</dd>
				</dl>
				<dl>
					<dd>
						<select class="selectToInput" name="beneVO.beneDistrict" onchange="DistreactChageReportData(this);" id="beneDistrict" >
							</select>
						<span>区/县<font style="color:#FF0000">* </font></span>
					</dd>
				</dl>
				<dl>
					<dd>	
                           	<input name="beneVO.beneAddress" id="beneAddress" size="24" value="${beneVO.beneAddress}" style="width: 150px;">
						<span>乡镇/街道<font style="color:#FF0000">* </font></span>
					</dd>
				</dl>
			</div>
		</div>		
		<dl>
			<dt>电子邮箱</dt>
			<dd>
				<input type="text"  name="beneVO.beneEmail" id="beneEmail" value="${beneVO.beneEmail }">
			</dd>
		</dl>
		<dl >
			<dt >剩余比例</dt>
			<dd>
			 
				<input type="text" name="residueRate" id="beneRate" readonly="readonly">
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>受益分子</dt>
			<dd>
				<input type="text" name="claimPayVO.payMole" id="payMole" min="0"
					onblur="computePayAmounts();" value="${claimPayVO.payMole }" class="number"/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>受益分母</dt>
			<dd>
				<input type="text" name="claimPayVO.payDeno" id="payDeno" min="0"
					onblur="computePayAmounts();" value="${claimPayVO.payDeno }" class="number"/>
			</dd>
		</dl>
		<dl>
			<dt>受益金额</dt>
			<dd>
				<input type="hidden" name="claimPayVO.claimPayId"
					id="claimPayIdHidden" value="${claimPayVO.claimPayId }"> 
<!-- 					<input type="hidden" name="claimPayVO.beneRate" id="beneRateHidden"> -->
			
				
				<input type="text" name="claimPayVO.payAmount" id="payAmount"
					value="${claimPayVO.payAmount}" readonly="readonly">
			</dd>
		</dl>
<!-- 	</fieldset> -->

	<div class="divider" style="border-style: none;"></div>
		<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">领款人信息
					</h1>
				</div>
<!-- 	<fieldset style="border-style: solid;"> -->
<!-- 		<legend style="border-style: solid;">领款人信息</legend> -->
<input type="hidden" name="claimPayVO.contraryPayFlag" value="${claimPayVO.contraryPayFlag}" id="contraryPayFlag">
<input type="hidden" name="payeeVO.legalPersonId" value="${payeeVO.legalPersonId}" id="payeeLegalPersonId">
		<dl style="width: 100%;">
					<dt>
						<a disabled="true" id ="legalPersonQueryPayeeStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoPayee();">
						<button type="button" class="but_blue " id="legalPersonQueryPayeeStyleButton" disabled="disabled" >法人信息录入</button>
						</a>
			<a lookupGroup="payeeVO" id ="legalPersonQueryPayee" href="javaScript:void(0)" style="display:none;"  width="1000" height="450"  > 法人信息录入</a>
					</dt>
					<dt>
					对公支付，请勾选<input type="checkbox" id="contraryPayFlagCheckBoxId" onclick="chooseMethod();"/>
					</dt>
					</dl>
		<dl>
			<dt><font>* </font>领款人与受益人关系</dt>
			<dd>
				<s:if test="claimPayVO.payeeRelation != null && claimPayVO.payeeRelation != ''">
					<Field:codeTable onChange="queryPayeeRelation(this);"
						name="payeeVO.payeeRelation" id="payeeRelation"  cssClass="notuseflagDelete combox title selectChange"
						value="${claimPayVO.payeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" 
						whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
				</s:if>
				<s:else>
					<Field:codeTable onChange="queryPayeeRelation(this);"
						name="payeeVO.payeeRelation" id="payeeRelation"  cssClass="notuseflagDelete combox title selectChange"
						value="${payeeVO.payeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" 
						whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
				</s:else>
					<input type="hidden" name="claimPayVO.payeeRelation" id="payPayeeRelationId"/>
			</dd>
		</dl>
		<dl>
			<dt><font>* </font>姓名</dt>
			<dd id="payeeNameBenePayee">
					<input type="hidden" name="payeeVO.payeeName"  value="${payeeVO.payeeName }"  onblur="resetStyle(this)"/>
					<select class="selectToInput"  <s:if test="claimPayVO.payeeRelation==00">disabled="disabled"</s:if>  islegitimacy="true" id="payeeName" onchange="findclaimPayeeByName(this)" >
					</select>
			</dd>
		</dl>
		<dl>
			<dt>性别</dt>
			<dd>
				<s:if test="claimPayVO.payeeRelation==00">
					<Field:codeTable cssClass="combox title" disabled="true"  name="payeeVO.payeeSex" id="payeeSex"
						value="${payeeVO.payeeSex}" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2,9)" nullOption="true"
						/>
				</s:if>
				<s:else>
					<Field:codeTable cssClass="combox title"  name="payeeVO.payeeSex" id="payeeSex"
						value="${payeeVO.payeeSex}" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2,9)" nullOption="true"
						/>
				</s:else>
			</dd>
		</dl>
		<dl>
			<dt>出生日期</dt>
			<dd>
				<input type="expandDateYMD" onPropertyChange="getpayeeInfoRegi();" name="payeeVO.payeeBirth"
					id="payeeBirth" <s:if test="claimPayVO.payeeRelation==00">disabled="disabled"</s:if> 
					value="<s:date name="payeeVO.payeeBirth" format="yyyy-MM-dd"/>" />
			</dd>
		</dl>
		<dl>
			<dt>证件类型</dt>
			<dd>
				<Field:codeTable onChange="getpayeeInfoRegi();" cssClass="combox title"  name="payeeVO.payeeCertiType" id="payeeCertiType"
					value="${payeeVO.payeeCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE"
					 whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','73','8')"  orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','73','012','8','013', code)"  
					nullOption="true" />
			</dd>
		</dl>
		<dl>
			<dt>证件号码</dt>
			<dd>
				<input type="text" name="payeeVO.payeeCertiNo" id="payeeCertiNo"
					value="${payeeVO.payeeCertiNo }" onblur="checkpayeeCertiNo();" onselectstart="return false" oncopy="return false" onpaste="return false"/>
			</dd>
		</dl>
		<dl>
			<dt>领款人国籍</dt>
			<dd>
				<Field:codeTable cssClass="selectToInput"  name="payeeVO.payeeNation" id="clmtNationL" nullOption="true"
					value="${payeeVO.payeeNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" whereClause="country_code not in ('GAT')" defaultValue="CHN" orderBy="decode(country_code,'CHN','A',country_code)" />
			</dd>
		</dl>
		<dl>
			<dt>证件有效起期</dt>
			<dd>
				<input type="expandDateYMD" onPropertyChange="getpayeeInfoRegi();" name="payeeVO.payeeCertiStart"
					id="payeeCertiStart"
					value="<s:date name="payeeVO.payeeCertiStart" format="yyyy-MM-dd"/>"/>
			</dd>
		</dl>
		<dl>
			<dt>证件有效止期</dt>
			<dd>
				<input type="expandDateYMD" name="payeeVO.payeeCertiEnd" 
					id="payeeCertiEnd" gt="payeeCertiStart"
					value="<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>"/>
					<input type="hidden" name="payeeVO.payeeCertiEnd" id="payeeCertiEndHidentId" value="<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>"/>
					<input type="checkbox" id="payeeCertiEndCheckBoxId" onclick="payeeCertiEndCheckBox(this)"/>长期
			</dd>
		</dl>
		<!-- 71408 关于系统理赔环节反洗钱优化方案的需求 -->
				<dl>
					<dt>领款人职业代码</dt>
					<dd>
						<Field:codeTable  id="payeeJobIdFire"
												name="payeeVO.payeeJobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
												whereClause="job_code not in ('Y001023') and display_order not in ('0')" orderBy="DISPLAY_ORDER"
												nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
												value="${payeeVO.payeeJobCode}" />
					</dd>
				</dl>
				<dl>
					<dt>领款人电话</dt>
					<dd>
					   <input type="text" name="payeeVO.payeePhone" id="payeePhone" onblur="checkPhoneAndMobileNew(this,'领款人');"  value="${payeeVO.payeePhone }"/>
					</dd>
				</dl>
				<div class="mian_site">
							<dl>
								<dt>领款人地址</dt>
							</dl>
							<div class="main_detail">
								<dl>
									<dd>
									   	<input type="hidden" name="" value="${payeeVO.payeeState}" id="payeeProvinceReserve">
										<input type="hidden" name="" value="${payeeVO.payeeCity}" id="payeeCityReserve">
										<input type="hidden" name="" value="${payeeVO.payeeDistrict}" id="payeeDistreactReserve">
				                           	<Field:codeTable cssClass="selectToInput"  name="payeeVO.payeeState" onChange="payeeProvinceChangeReportData(this)" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="payeeProvince" 
				                           	value="${payeeVO.payeeState }" 
				                           	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
										<span>省/直辖市</span>
									</dd>
								</dl>
								<dl>
									<dd>
										<select class="selectToInput" name="payeeVO.payeeCity" onchange="payeeCityChageReportData(this)" id='payeeCity' >
											</select>
										<span>市</span>
									</dd>
								</dl>
								<dl>
									<dd>
										<select class="selectToInput" name="payeeVO.payeeDistrict" onchange="payeeDistreactChageReportData(this)" id="payeeDistrict" >
											</select>
										<span>区/县</span>
									</dd>
								</dl>
								<dl>
									<dd>	
				                           	<input name="payeeVO.payeeAddress" id="payeeAddress"  size="24" value="${payeeVO.payeeAddress}" style="width: 150px;">
										<span>乡镇/街道</span>
									</dd>
								</dl>
							</div>
						</div>	
		<dl>
			<dt>电子邮箱</dt>
			<dd>
				<input type="text"  name="payeeVO.payeeEmail" id="payeeEmail" value="${payeeVO.payeeEmail }">
			</dd>
		</dl>		
				
		<dl>
			<dt><font>* </font>支付方式</dt>
			<dd>
				<input type="hidden" id="PayModeIdId" value="${payeeVO.payMode}"/>
				<select onchange="onChangePayModeRegister(this)" class="combox title"  id="PayModeId" name="payeeVO.payMode">
							<option value="">请选择</option>
							<option value="32" <s:if test="%{payeeVO.payMode == 32}">selected</s:if>>银行转账(制返盘)</option>
							<option value="34" <s:if test="%{payeeVO.payMode == 34}">selected</s:if>>网上银行</option>
							<!-- <option value="21" <s:if test="%{payeeVO.payMode == 21}">selected</s:if>>现金支票</option> -->
							<option value="22" <s:if test="%{payeeVO.payMode == 22}">selected</s:if>>转账支票</option>
							<option value="10" <s:if test="%{payeeVO.payMode == 10}">selected</s:if>>现金</option>
							<!-- <option value="42" <s:if test="%{payeeVO.payMode == 42}">selected</s:if>>客户账户</option> -->
				</select>
			</dd>
		</dl>
		<dl>
					<dt>银行编码</dt>
					<dd>
					   <select class="selectToInput" name="payeeVO.bankCode" id="bankCodeId">
					   </select>  
					   <input type="hidden" name="hiddenBank" id="hiddenBank" value="${payeeVO.bankCode }">
					</dd>
				</dl>
		<dl>
			<dt>银行账户名</dt>
			<dd>
				<input type="text" name="payeeVO.accountName" id="accountName"
					value="${payeeVO.accountName }" />
			</dd>
		</dl>
		<dl>
			<dt>银行账号</dt>
			<dd>
				<input name="payeeVO.accountNo"  onkeyup="this.value=this.value.replace(/[^\d-]/g,'')"  onpaste="return false" oncontextmenu="return false" oncopy="return false" oncut="return false"
					id="accountNo" value="${payeeVO.accountNo }" onblur="trueFalse();" />
			</dd>
		</dl>
		<div style="display: none" id="isBankOfDepositDiv">
				<dl>
					<dt>开户行名称</dt>
					<dd>
						<input id=bankOfDepositId name="payeeVO.name" value="${payeeVO.name}" 
							postFiled="keywords" suggestFields="code,name" 
							suggestUrl="clm/pages/register/bankDetails.jsp" readonly
							lookupGroup="payeeVO"/>
							<a class="btnLook" 
							href="clm/register/bankDetails_CLM_toBenefitInputPageAction.action"
							lookupGroup="payeeVO" onclick="myBankOption(this);" id="btnLookBankOfDeposit">开户行信息查询</a>											
							<input id="correspondentNameId" name="payeeVO.bankOfDeposit" type="hidden" value="${payeeVO.bankOfDeposit}"/>	
					</dd>
				</dl>
			</div>
<!-- 	</fieldset> -->
	<!-- <div class="buttonActive">
		<div class="buttonContent">
			<button type="button" id="saveBenePayee" onclick="saveJudg();">保存受益人和领款人信息</button>
		</div>
	</div> -->
</form>

<script type="text/javascript">
//判断payeeName是否包含法院二字，包含则清空指定字段：
//性别、出生日期、证件类型、领款人国籍、证件有效起期、证件有效止期、证件号码、领款人职业代码、领款人电话、领款人地址
$(function(){
	if($("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") != -1){
		$("#payeeSex", navTab.getCurrentPanel()).attr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).attr("disabled", "disabled");
		
		$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeCertiNo", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).attr("disabled","disabled");
		
		$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiEndCheckBoxAuditId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#payeeCertiEndCheckBoxAuditId", navTab.getCurrentPanel()).attr("disabled", "disabled");
		
		$("#clmtNationL", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeJobIdFire", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		
		$("#payeePhone", navTab.getCurrentPanel()).attr("disabled", true);
		$("#payeeProvince", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeCity", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeDistrict", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeAddress", navTab.getCurrentPanel()).attr("disabled", true);
	}
});

$("#payeeNameBenePayee").die("blur");
$("#payeeNameBenePayee").live('blur',function (){
	if($("#payeeName", navTab.getCurrentPanel()).prev().val().indexOf("法院") != -1){
		$("#payeeSex", navTab.getCurrentPanel()).prev().val("");
		$("#payeeSex", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).val("");
		$("#payeeBirth", navTab.getCurrentPanel()).prev().attr("disabled", "disabled");
		$("#payeeBirth", navTab.getCurrentPanel()).attr("disabled", "disabled");
		
		$("#payeeCertiType", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		$("#payeeCertiNo", navTab.getCurrentPanel()).val("");
		$("#payeeCertiNo", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).val("");
		$("#payeeCertiStart", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).val("");
		
		$("#payeeCertiEnd", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#payeeCertiEndCheckBoxAuditId", navTab.getCurrentPanel()).removeAttr("checked");
		$("#payeeCertiEndCheckBoxAuditId", navTab.getCurrentPanel()).attr("disabled", "disabled");
		
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).append($("select[name='payeeVO.payeeNation']").find("option[value="+""+"]").attr("title"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).selectMyComBox("");
		claimFireEvent($("select[name='payeeVO.payeeNation']"));
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).val("");
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).empty();
		$("a[name='payeeVO.payeeJobCode']", navTab.getCurrentPanel()).append($("select#payeeJobIdFire", navTab.getCurrentPanel()).find("option[value="+""+"]").attr("title"));
		$("select#payeeJobIdFire", navTab.getCurrentPanel()).attr("value", "");
		claimFireEvent($("#payeeJobIdFire",navTab.getCurrentPanel()));
		$("#payeeJobIdFire", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		
		$("#payeePhone", navTab.getCurrentPanel()).val("");
		$("#payeePhone", navTab.getCurrentPanel()).attr("disabled", true);
		$("#payeeProvince", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeProvince", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		$("#payeeCity", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeCity", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		$("#payeeDistrict", navTab.getCurrentPanel()).selectMyComBox("");
		$("#payeeDistrict", navTab.getCurrentPanel()).prev().setMyComboxDisabled(true);
		$("#payeeAddress", navTab.getCurrentPanel()).val("");
		$("#payeeAddress", navTab.getCurrentPanel()).attr("disabled", true);
	}else{
		$("#payeeSex", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("#payeeSex", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeBirth", navTab.getCurrentPanel()).removeAttr("disabled","disabled");
		$("#payeeCertiType", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("select[name='payeeVO.payeeNation']", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeCertiNo", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiStart", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiEnd", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeCertiEndCheckBoxAuditId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeePhone", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		$("#payeeJobIdFire", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeProvince", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeCity", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeDistrict", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#payeeAddress", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		
		$("#payeeProvince", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("#payeeCity", navTab.getCurrentPanel()).setMyComboxDisabled(false);
		$("#payeeDistrict", navTab.getCurrentPanel()).setMyComboxDisabled(false);
	}
});

</script>