<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript" src="clm/pages/register/epibolyManageInit.js"></script>
	

<div class="pageHeader" layoutH="10">
	<form action="clm/register/queryOutsourceIfno_CLM_epibolyManageAction.action"
	method="post" class="pageForm required-validate" id="queryOutsourceIfnoId"
	onsubmit="return  navTabSearch(this)">
	<div class="panelPageFormContent">
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询条件
					</h1>
				</div>
		<div>
			<div class="pageFormInfoContent" >
				<!-- <dl>
					<dt>外包商标识</dt>
					<dd>
						<input type="text" name="outsourceIfnoVO.outsourceFlag" onkeyup="this.value=this.value.replace(/\s/g,'')" id="outsourceFlag"/>
					</dd>
				</dl> -->
				<dl>
					<dt>外包商名称</dt>
					<dd>
						<input type="text" name="outsourceIfnoVO.outsourceName" value="${outsourceIfnoVO.outsourceName}" onkeyup="this.value=this.value.replace(/\s/g,'')" id="outsourceName"/>
					</dd>
				</dl>
				<dl >
					<dt>管理机构</dt>
					<dd>
						<input style="width: 30px;border-right:0px" type="text" size="2" name="outsourceOrganMappingVO.organCode"
								id="menuBtn" value="${outsourceOrganMappingVO.organCode}" class="organ" clickId="epiManageBtn"
								showOrgName="branchname" needAll="true" /> 
						<input style="width:110px;" type="text" size="11" name="outsourceOrganMappingVO.organName"
								id="branchname" value="<Field:codeValue value="${outsourceOrganMappingVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" readOnly class="" />
						<a id="epiManageBtn" class="btnLook" href="#"></a>		
								
<%-- 									<input style="width: 30px;border-right:0px" id="organCode" name="outsourceOrganMappingVO.organCode"  value="${outsourceOrganMappingVO.organCode}" --%>
<!-- 										type="text" class="organ" clickId="menuBtn" showOrgName="organName"  -->
<!-- 										needAll="true" size="8" -->
<%-- 										value="<s:property value='organCode'/>"/> --%>
<!-- 									<input style="width:110px;" id="organName" type="text" readOnly size="15"  -->
<!-- 										name="outsourceOrganMappingVO.organName" -->
<%-- 										value="<Field:codeValue value="${outsourceOrganMappingVO.organCode}" --%>
<%-- 									tableName="APP___CLM__DBUSER.T_UDMP_ORG" />"/>  --%>
<!-- 									<a id="menuBtn" class="btnLook" href="#"></a> -->
											
					</dd>
				</dl>
				<dl >
					<dt>有效标识</dt>
					<dd>
						<s:select id="mappingFlag1" list="#{'':'显示全部',0:'无效',1:'有效'}" cssClass="notuseflagDelete combox title comboxDD" value="outsourceIfnoVO.mappingFlag" name="outsourceIfnoVO.mappingFlag">
					    </s:select>
					</dd>
				</dl>
				<dl >
					<dt>外包方式</dt>
					<dd>
						<s:select list="#{'':'显示全部',1:'驻场外包',2:'离场外包',3:'数采自采'}" id="outsourceWay"
								cssClass="notuseflagDelete combox title comboxDD" name="outsourceIfnoVO.outsourceWay">
						</s:select>
					</dd>
				</dl>
					<div class="pageFormdiv">
						<button class="but_blue" type="button" onclick="queryOutsourceIfno();">查询</button>
					</div>
			</div>
		</div>
	</div>
	<div >
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询结果
					</h1>
				</div>
		<div>
			<div class="tabdivclassbr"  >
						<table class="list" style="width: 100%;">
							<thead align="center">
								<tr>
									<th nowrap>选择</th>
									<th nowrap>序号</th>
									<th nowrap>外包商名称</th>
									<th nowrap>有效标识</th>
									<th nowrap>管理机构</th>
									<th nowrap>外包商标识</th>
									<th nowrap>外包商类型</th>
									<th nowrap>外包方式</th>
								</tr>
							</thead>
							<tbody id="outsourceVarId" align="center"	>
								<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="outsourceIfnoVOList == null || outsourceIfnoVOList.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
								<s:iterator value="outsourceIfnoVOList" status="var" var="outsourceVar">
									<tr>
									    <td style="display: none">${mappingId}</td>
										<td><input type="radio" name="radio" value="0" onclick="queryOutsourceInfo(this);"/></td>
										<td>${var.index+1}</td>
										 <td>${outsourceName}<input type="hidden" value="${outsourceId}"></td>
										<td>
											<s:if test="mappingFlag eq 1">
												<span>有效</span>
											</s:if>
											<s:else>
												<span>无效</span>
											</s:else>
										</td>
										<td style="display: none">${organCode}</td>
										<td><Field:codeValue value="${organCode}"
									tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
                                    <td>${outsourceFlag}</td>
                                    <td><s:if test="vendorCode eq 1">
												<span>医疗类外包商</span>
											</s:if>
											<s:else>
												<span>非医疗类外包商</span>
											</s:else></td>
									<td><input type="hidden" value="${outsourceWay}"><Field:codeValue  tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY"
													value="${outsourceWay}" /></td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
			</div>
		</div>
	</div>
	</form>
<form action="clm/register/addOutsourceIfno_CLM_epibolyManageAction.action"
	method="post" class="pageForm required-validate" id="addOutsourceIfnoId"
	onsubmit="return  validateCallback(this)">
	<div  class="panelPageFormContent"  id="outsourceIfnoDivId">
<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">外包商机构对照
					</h1>
				</div>
		<div>
			<div class="panelPageFormContent" >
				<dl>
					<dt>
						<font color="red">* </font>外包商名称
					</dt>
					<dd>
						<s:select cssClass="combox title" name="outsourceIfnoVO.outsourceId"  id="outsourceNameId"
						    list="outsourceNameList"
							listKey="outsourceId" listValue="outsourceName" headerKey="" headerValue="请选择" >
						</s:select>
					</dd>
				</dl>
				<input type="hidden" id="mappingId"   name="outsourceIfnoVO.mappingId"/>
				<dl>
					<dt>
						<font color="red">* </font>有效标识
					</dt>
					<dd>
					    <select id="mappingFlag" class="notuseflagDelete combox title comboxDD" name="outsourceIfnoVO.mappingFlag">
						    <option value="1">有效</option> 
						    <option value="0">无效</option>
					    </select>
					</dd>
				</dl>
			
				<dl>
					<dt>
						<font color="red">* </font>管理机构
					</dt>
					<dd>
						<input style="width: 30px;border-right:0px" type="text" size="2" name="outsourceIfnoVO.organCode"
								id="menuBtnId" value="<s:property value='outsourceCaseVO.organCode'/>" class="organ" clickId="epiManageBtn1"
								showOrgName="branchnameId" needAll="true" /> 
						<input style="width:110px;" type="text" size="11" name="claimBfSurveyPlanVO.branchname"
								id="branchnameId" value="<s:property value='claimBfSurveyPlanVO.branchname'/>" readOnly class="" />
						<a id="epiManageBtn1" class="btnLook" href="#"></a>
								
<!-- 							<input style="width: 30px;border-right:0px" id="branchCode" name="outsourceIfnoVO.organCode" -->
<!-- 								type="text" class="organ" clickId="menuBtn1" -->
<!-- 								showOrgName="branchname" needAll="true" size="8" -->
<%-- 								value="<s:property value='outsourceCaseVO.organCode'/>" /> <input style="width:90px;" --%>
<!-- 								id="branchname" type="text" readOnly size="15" -->
<!-- 								name="claimBfSurveyPlanVO.branchname" -->
<%-- 								value="<s:property value='claimBfSurveyPlanVO.branchname'/>" /> --%>
<!-- 								<a id="menuBtn1" class="btnLook" href="#"></a>  -->
					</dd>
				</dl>
				<dl >
					<dt>外包方式</dt>
					<dd>
						<Field:codeTable cssClass="notuseflagDelete combox title comboxDD"  name="outsourceIfnoVO.outsourceWay"  id="outsourceWayId"
                             tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY"  nullOption="true"></Field:codeTable>  
					</dd>
				</dl>
			</div>
		</div>
	</div>
	</form>
	<div class="formBarButton">
	<ul>
		<li><button class="but_blue" type="button" onclick="saveOutsourceIfnoVO();">保存</button></li>
		<li><button class="but_gray"  type="button" onclick="exit()">退出</button></li>
	</ul>
	</div>
</div>