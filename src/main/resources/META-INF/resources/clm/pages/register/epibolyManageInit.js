//新增外包商
function addOutsourceIfno() {
	$("#outsourceIfnoDivId", navTab.getCurrentPanel()).css("display", 'block');
}

// 保存
function saveOutsourceIfnoVO() {
		$("#outsourceNameId", navTab.getCurrentPanel()).removeAttr("disabled");
		if ($("#outsourceNameId", navTab.getCurrentPanel()).val() == "") {
			alertMsg.warn("请录入必录项。");
			$("#outsourceNameId", navTab.getCurrentPanel()).focus();
			return false;
		}
		if ($("#mappingFlag", navTab.getCurrentPanel()).val() == "") {
			alertMsg.warn("请录入必录项。");
			$("#mappingFlag", navTab.getCurrentPanel()).focus();
			return false;
		}
		if ($("#menuBtnId", navTab.getCurrentPanel()).val() == "") {
			alertMsg.warn("请录入必录项。");
			$("#menuBtnId", navTab.getCurrentPanel()).focus();
			return false;
		}
		var mappingId;
		var flag = false;
		$("#outsourceVarId", navTab.getCurrentPanel()).find("tr")
				.each(
						function() {
							if ($(this).find("td:eq(1)").find("input").attr("checked") == "checked") {
								mappingId = $(this).find("td:eq(0)").html();
								flag = true;
							}
						});
		if (flag) {
			$("#mappingId", navTab.getCurrentPanel()).val(mappingId);
		} else {
			$("#mappingId", navTab.getCurrentPanel()).val("");
		}
		var organCode = $("#menuBtnId", navTab.getCurrentPanel()).val();
		var mappingFlag = $("#mappingFlag", navTab.getCurrentPanel()).val();
		var outsourceId = $("#outsourceNameId", navTab.getCurrentPanel()).val();
		var mappingId = $("#mappingId", navTab.getCurrentPanel()).val();
		$.ajax({
					url : "clm/register/checkSimpleOutsource_CLM_epibolyManageAction.action",						
					type : 'post',
					data : {'outsourceOrganMappingVO.organCode':organCode,
							'outsourceOrganMappingVO.mappingFlag':mappingFlag,
							'outsourceOrganMappingVO.mappingId':mappingId,
							'outsourceIfnoVO.outsourceId':outsourceId},
					global : true,
					dataType : 'json',
					success : function(json) {
						if (json.statusCode == DWZ.statusCode.ok) {
							$("#addOutsourceIfnoId", navTab.getCurrentPanel()).submit();
							//$("#queryOutsource", navTab.getCurrentPanel()).click();
							$("#queryOutsourceIfnoId", navTab.getCurrentPanel()).submit();
						} else {
							alertMsg.error(json.message);
							return false;
						}
					}
				});	
}

// 校验数据是否重复
function check() {
	var name = $("#outsourceNameId", navTab.getCurrentPanel()).val();
	var flag = false;
	var organCode = $("#branchCode", navTab.getCurrentPanel()).val();
	$.ajax({
			url : "clm/register/checkOutsourceIfno_CLM_epibolyManageAction.action?outsourceIfnoVO.outsourceName="
					+ name
					+ "&outsourceIfnoVO.organCode=" + organCode,
			type : 'post',
			datatype : 'json',
			async : false,
			success : function(data) {
				var dataList = eval('('+data+')');
				if (dataList.bakPath == "false") {
					alertMsg.error("数据已存在！！");
					flag = false;
				}else{
					alertMsg.warn("可以插入数据！！");
					flag = true;
				}
			},
			error : function() {
				alertMsg.error("删除失败!");
			}
		});
	return flag;
}

// 查询
function queryOutsourceIfno() {
	var outsourceName = $("#outsourceName", navTab.getCurrentPanel()).val();
	var organCode = $("#organCode", navTab.getCurrentPanel()).val();
	var mappingFlag = $("#mappingFlag", navTab.getCurrentPanel()).val();
	var outsourceWay = $("#outsourceWay", navTab.getCurrentPanel()).val();
	/*if(isNorE(outsourceFlag)&&isNorE(outsourceName)&&isNorE(organCode)&&isNorE(mappingFlag)&&isNorE(outsourceWay)){
		alertMsg.warn("请至少输入一项查询条件");
		return;
	}*/
	$.ajax({
		url : "clm/register/queryOutsourceIfnoFlag_CLM_epibolyManageAction.action",
		type : 'post',
		data:$("#queryOutsourceIfnoId", navTab.getCurrentPanel()).serialize(),
		datatype : 'json',
		global : false,
		success : function(json) {
			var data = eval("(" + json + ")");
			if(data.message == "1"){
				alertMsg.error("所查询的外包商不存在，请重新输入查询条件。");
			}
			$("#queryOutsourceIfnoId", navTab.getCurrentPanel()).submit();
		}	
	});
}

function isNorE(s){
	if(s==null||s==""){
		return true;
	}else{
		return false;
	}
}

/**
 * 给外包商信息赋值
 */

function queryOutsourceInfo(obj) {
	if ($(obj, navTab.getCurrentPanel()).val() == 1) {
		$(obj, navTab.getCurrentPanel()).attr("value", "0");
		$(obj, navTab.getCurrentPanel()).removeAttr("checked");
		$("#outsourceNameId", navTab.getCurrentPanel()).selectMyComBox("");
		$("#mappingFlag", navTab.getCurrentPanel()).selectMyComBox("");
		$("#menuBtnId", navTab.getCurrentPanel()).val("");
		$("#branchnameId", navTab.getCurrentPanel()).val("");
	} else {
		$(":radio", navTab.getCurrentPanel()).attr("value","0");
		$(obj, navTab.getCurrentPanel()).attr("value","1");
		$(obj, navTab.getCurrentPanel()).attr("checked", "checked");
		var name = $(obj, navTab.getCurrentPanel()).parent().parent().find("td:eq(3)").find("[type=hidden]").val();
		var flag = $(obj, navTab.getCurrentPanel()).parent().parent().find("td:eq(4)").text();
		var organCode = $(obj, navTab.getCurrentPanel()).parent().parent().find("td:eq(5)").html();
		var organName = $(obj, navTab.getCurrentPanel()).parent().parent().find("td:eq(6)").html();
		var outsourceWay = $(obj).parent().parent().find("td:eq(9)").find("input").val();
		$("#outsourceNameId", navTab.getCurrentPanel()).selectMyComBox(name.trim());
		var flagCode;
		if (flag.trim() == "有效") {
			flagCode = 1;
		}
		if (flag.trim() == "无效") {
			flagCode = 0;
		}
		$("#mappingFlag", navTab.getCurrentPanel()).selectMyComBox(flagCode);
		$("#menuBtnId", navTab.getCurrentPanel()).val(organCode);
		$("#branchnameId", navTab.getCurrentPanel()).val(organName);
		$("#outsourceNameId", navTab.getCurrentPanel()).attr("disabled","disabled");
		$("#outsourceWayId", navTab.getCurrentPanel()).selectMyComBox(outsourceWay);
	}
}
/*function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
}*/