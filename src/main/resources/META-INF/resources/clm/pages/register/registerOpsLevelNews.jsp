<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript">

//用于伸缩按钮的伸缩功能
//$(document).ready(function(){
	  
$(".main_heading", navTab.getCurrentPanel()).off().toggle(function(){  
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
},function(){
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
 	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
});

//});

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]").bind("click",function(){
//		var id=$(this).attr("id").split("main_")[1];
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
})

	//单选按钮

	function radiockeacdOpsLevel(obj) {
		//定位数据 
		var innerPage = $(obj).parent().parent().parent().parent()
				.parent();
		var pageFlag = $(innerPage).parent().attr("titles");
		var billType = $(innerPage).parent().attr("billType");
		var operationId = $(obj).parent().find("#operationId").val();
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();	
	  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
				+ pageFlag 
				+ '&caseId='
				+ caseId 
			 	+ '&claimOperationVO.operationId=' + operationId
			 	+'&claimOperationVO.caseId=' + caseId;
		$(innerPage).next().next().loadUrl(url);
		$(innerPage).next().next().show("30000"); 
		return;
	}
	

	function  deleteValueOpsLevel(obj){
		 var operationId = $(obj).parent().find("#operationId").val();
			if(operationId!=""&&operationId!="0"){  
			$.ajax({ 'type':'post',
					'url':'clm/register/deleteClaimDutyInfo_CLM_tClaimDutyRegisterAction.action'+ '?claimOperationVO.operationId=' + operationId+'&claimTypecode[0]=15',
					'datatype':'json',
					'success':function(data){
						 if(data){ 
							$(obj).parent().parent().remove();
							 
							 alertMsg.info("删除成功");
						 }else{
							 alertMsg.info("删除失败");
						 }
					    
					},
		 			'error':function(){ 
		 				alert("出错了！");
		 			}
			 });
			} 
		}
	
	
</script>

		<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">手术分级给付信息
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
									<div class="main_bqtitle">
				                         <h1><img src="clm/images/three.png">手术分级信息</h1>
				                     </div>
				                     	<div class="panelPageFormContent">
												<div id="OpsLevelNewsInt" class="panelPageFormContent" titles="registerOpsLevel" billType="15">
													<div class="tabdivclassbr">
														<table class="list nowrap itemDetail" addButton="添  加"
															hiddenAdd="hiddenAdd" style="width: 100%;">
															<thead>
																<tr>
																	<th nowrap>选择</th>
																	<th nowrap>序号</th>
											
																	<th nowrap  backValueFlag="departmentName">科室</th>
											
																	<th nowrap backValueFlag="operationItemName"><font class="point"
																		color="red">* </font>手术项目</th>
											
																	<th nowrap backValueFlag="operationLevel">级别</th>
																	
																	<th nowrap backValueFlag="paymentRate">给付比例</th>
																	
																	<th nowrap backValueFlag="operationDate"  dateFlag="dateFlag">手术日期</th>
											
																	<th nowrap backValueFlag="hospitalCode">医疗机构名称</th>
											
																	<th nowrap>操作</th>
																</tr>
															</thead>
															<tbody class="list" id="OperationTBody">
																<s:iterator value="claimOperationVOList" status="st">
																	<tr>
																		<td><input type='radio' name="radio" id='radio${st.index}'
																			value='0' onclick="radiockeacdOpsLevel(this)" class="MedRadio" />
																			<input id="operationId" type="hidden" value="${operationId }">
																		</td>
											
																		<td><input class="digits textInput" type="text" size="5"
																			value="${st.index+1}" readonly="readonly" />
																		</td>
																		
																		<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_OPERATION_LEVEL"  value="${departmentCode}" /></td>
																		
																		<td>${operationItemName }</td>
																		
																		<td> ${operationLevel} </td>
																		
																		<td> ${paymentRate} </td>
																		
																		<td> <s:date name='operationDate' format='yyyy-MM-dd' /> </td>
																		
																		<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_HOSPITAL"  value="${hospitalCode}" /></td>
																		
																		<td><a class="btnDel" href="javascript:void(0)" onclick="deleteValueOpsLevel(this)"></a><input id="operationId" type="hidden" value="${operationId }"></td>
																	</tr>
																</s:iterator>
															</tbody>
														</table>
													</div>
												    <s:include value="addBarForDuty.jsp" />
												</div>
											</div>
				                     
								</div>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
