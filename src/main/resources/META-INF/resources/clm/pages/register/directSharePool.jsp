<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript" src="clm/pages/register/directSharePool.js">
</script>
<div layoutH="0" id="reportCommPoolJsp">
	<!-- 查询事件访问路径 -->
	<form id="registerSharePoolJspForm"
		action="clm/register/findDirectSharePool_CLM_directSharePoolAction.action" method="post" 
		onsubmit="return navTabSearch(this,'registerSharePoolResult')"
		class="pagerForm " rel="registerSharePoolResult">
	<!-- 查询区域 -->
	<div class="panelPageFormContent" >
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询条件
					</h1>
				</div>
		    <div class="pageFormInfoContent" >  
				<dl  >	
					<dt>赔案号</dt>
					<dd>
						<input type="text" id="caseNo" name="requestVO.caseNo" value="${requestVO.caseNo}" size="25"  onkeyup="this.value=this.value.replace(/^ +| +$/g,'')"/>
					</dd> 
				</dl> 
				<dl>
				   <dt>申请渠道</dt>
				   <dd>
				      <Field:codeTable  name="requestVO.channelCode" value="${requestVO.channelCode }" 
   											 cssClass="combox title" nullOption="true" tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" whereClause="channel_code in('09','07','03','11','12')"></Field:codeTable> 
				   </dd>
				</dl>
				<dl>
					<dt>签收机构</dt>
					<dd>
						<input type="hidden" value="${requestVO.organCode}" id="ocUse">
						<input name="requestVO.organCode" id="organCodePro" style="width: 30px; border-right: 0;"
							value="${requestVO.organCode}" type="text" class="organ" clickId="menuBtn" showOrgName="organNamePro" needAll="true" size="8"/>
						<input name="requestVO.organName" id="organNamePro" style="width: 110px" value="${requestVO.organName }"
							type="text" readOnly size="15" /> <a id="menuBtn" class="btnLook" href="#"></a>
					</dd>
				</dl>
				<dl>	
					<dt>出险人姓名</dt>
					<dd>
						<input type="text" id="customerName" name="requestVO.customerName" value="${requestVO.customerName }" size="25" onkeyup="this.value=this.value.replace(/\s/g,'')" onblur="resetStyle(this)" />
					</dd> 
				</dl>
		   <div class="pageFormdiv">
				<button class="but_blue" type="button" onclick="change();">查询</button>
			</div>
		   </div>  
    </div>
    	</form>
	<!-- 显示数据列表区域 -->	
	<div >
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">共享池
					</h1>
				</div>
		<div class="tabdivclassbr" id="registerSharePoolResult">
			<table class="list main_dbottom" width="100%">
				<thead>
					<tr>
                   		<th nowrap>序号</th>
						<th nowrap>赔案号</th>
						<th nowrap>签收机构</th>
						<th nowrap>报案方式</th>
						<th nowrap>服务商</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>签收日期</th>
					</tr>
				</thead>
				<tbody align="center">
					<s:if test="responseVOList == null || responseVOList.size()==0">
						<tr>
							<td colspan="12">
								<div class="noRueryResult">没有符合条件的查询结果！</div>
							</td>
						</tr>
					</s:if>
					<!-- 循环显示数据 -->
					<s:iterator value="responseVOList" status="st">
						<tr>
							<td><s:property value="#st.index + 1" /><input type="checkbox"  value="${bpmTaskId}"> </td>
							<td>
								<s:property value="caseNo" />
							</td>
							<td><Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
							<td><Field:codeValue value="${reportMode}" tableName="APP___CLM__DBUSER.T_REPORT_TYPE" /></td>
							<td><s:property value="serviceCommerce" /></td>
							<td><s:property value="customerName" /></td>
							<td><s:date name="signTime" format="yyyy-MM-dd"/></td>
						</tr> 
					</s:iterator>
				</tbody>
   		   </table>
   		    <!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value},'registerSharePoolResult')"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab" rel="registerSharePoolResult"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
        </div>
        <div class="pageFormdiv">
				<button class="but_blue" type="button" onclick="applyTask()">申请</button>
		</div>
   	</div>
   
   		<!-- 个人池显示数据列表区域 -->
	<!-- 个人池分页查询访问路径 -->
	<div id="registerSharePoolJsp">
 		<%@ include file="directSharePoolSelf.jsp"%>
	</div>
   	<div class="formBarButton" >
		<ul>  
			<li>
				<button type="button" class="but_gray close">退出</button>
			</li>
		</ul>
	</div>
</div>

