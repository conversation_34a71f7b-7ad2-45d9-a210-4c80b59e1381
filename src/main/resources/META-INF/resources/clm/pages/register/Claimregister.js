//进行上一步的标识
var presave = 0; 
	
//跳转到下一个选项卡方法，参数id为要跳转到的选项卡的Id，参数form为跳转前选项卡的form的id
function next(id,form,caseId){  
	var keyflag=false;
	 var mesage="";
	 var calcFlag=false;
	//将当前页面的参数传递给下一界面  
	if(id=='11'){
		$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/dataCollectInit_CLM_dataCollectAction.action?caseId="+caseId);
	}
	if(id=='12'){
		$("#"+"11", navTab.getCurrentPanel()).attr("href","clm/register/dataCollectInit_CLM_dataCollectAction.action?caseId="+caseId+"&tabRegister=1");
		$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/toRegisterCheckConfirmPageInit_CLM_toRegisterCheckConfirmPageAction.action?flag=8&caseId="+caseId);
	}
	if(id=='13'){
		$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/toDataCollectInit_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
	}
	if(id=='14'){
		//alert("asasd");
		/*var value = $("#dutyNextId", navTab.getCurrentPanel()).val();
		if (value != "已保存") {
			alertMsg.warn("请点击保存按钮");
			return;
		}*/
		var repeatedCaseFlag = false;
		//调用checkRepeatedCase方法校验重复账单
		$.ajax({
			 'url':"clm/audit/modifyRepeatedCase_CLM_auditConclusionAction.action",
			 'data':{"caseId":caseId},
			'type':'post', 
			'datatype':'json',
			'async':false,
			'success':function(data){
				//更新“重复账单号标志”
				var data = eval("(" + data + ")");
				for(var kay in data){
					if (kay=="1") {
						repeatedCaseFlag = true;
						alertMsg.error(data[kay]);
						return false;
					}
					if (kay=="2") {
						$.ajax({
							 'url':"clm/audit/checkRepeatedCase_CLM_auditConclusionAction.action",
							 'data':{"caseId":caseId},
							'type':'post', 
							'datatype':'json',
							'async':false,
							'success':function(data){
								//更新“重复账单号标志”
								$("#"+id, navTab.getCurrentPanel()).attr("href","clm/report/contractHangUpInitRegister_CLM_contractHangUpAction.action?caseId="+caseId);
							}
						});
					}
				}
					
			}
		});
		if (repeatedCaseFlag) {
			return;
		}
		
	}
	if(id=='15'){
		var flag = false;
		$.ajax({
  			'type':'post',
  			'url':'clm/register/judgeIfInfo_CLM_claimMatchCalcAction.action?caseId='+caseId,
  			'datatype':'json',
  			'async':false,
  			'success':function(data){
  				var data = eval("(" + data + ")");
				for(var kay in data){
					//如果kay为1则阻断，2不阻断，为了以后配置新产品提示信息
  					if (kay=="1") {
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
					}
  					if (kay=="2") {
  						alertMsg.info(data[kay]);
  					}
  					/*if(kay=="3"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="4"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="5"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="6"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="7"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  				    //添加提示不阻断
  					if(kay=="9"){
  						alertMsg.error(data[kay]);
  					}
  					if(kay=="10"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="11"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="12"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="13"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="14"){
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;
  					}
  					if(kay=="15"){//缺陷2656修改
  						alertMsg.info(data[kay]);
  					}
  					if(kay=="1786"){
  						alertMsg.error(data[kay]);
  					}
  					if(kay=="16"){//16只在审核匹配理算提示954,955产品
  						mesage=data[kay];
  						keyflag = true;
  						return false;
  					}
  					if(kay=="18"){//956产品规则 
  						mesage=data[kay];
  						keyflag = true;
  					}
  					if(kay=="20"){//978产品规则 
  						flag = true;
  						alertMsg.error(data[kay]);
  						return false;

  					}
  					if(kay=="21"){//762产品规则  提示信息
  						alertMsg.error(data[kay]);
  					}
  					if(kay=="22"){//521产品规则  提示信息
  						alertMsg.info(data[kay]);
  					}*/
				}
  			},
  			'error':function(){
  				alert("出错了！");
  			}
  		});
		if (flag) {
			return;
		}
		$("#"+id,navTab.getCurrentPanel()).attr("href","clm/register/claimMatchCalcInit_CLM_claimMatchCalcAction.action?caseId="+caseId);
		calcFlag=true;
	}
	if(keyflag){
		alertMsg.confirm(mesage,{okCall:function(){
			//将要跳转到的隐藏的选项卡显示
			$("#"+id,navTab.getCurrentPanel()).css("display","block");
			//调用选项卡的click方法，将页面跳转至下个选项卡中
			$("#"+id,navTab.getCurrentPanel()).click();
			
			//用于进度条显示
			$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
			$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
			$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
			$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
			$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
         }});
		return false;
	} 
	if(calcFlag){
		$.ajax({
			'url':"clm/register/copyPolicyData_CLM_claimMatchCalcAction.action?caseId="+caseId,
			 'type':'post',
			 'success':function(data){
				 $.ajax({
						'url':"clm/register/queryMessage_CLM_claimMatchCalcAction.action?caseId="+caseId,
						 'type':'post',
						 'success':function(data){
							 if(data!='[]'&&data!=''){
							 	// alertMsg.info(dataMessage);
						 	   alertMsg.confirm(data,{
						 			 okCall:function(){
						 				 
						 			 }
						 	   });
							 }
						 }
				});
				//将要跳转到的隐藏的选项卡显示
				$("#"+id,navTab.getCurrentPanel()).css("display","block");
				//调用选项卡的click方法，将页面跳转至下个选项卡中
				$("#"+id,navTab.getCurrentPanel()).click();
				
				//用于进度条显示
				$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
				$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
				$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
				$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
				$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
				return;
			 }
		});
	}
	//将要跳转到的隐藏的选项卡显示
	$("#"+id,navTab.getCurrentPanel()).css("display","block");
	//调用选项卡的click方法，将页面跳转至下个选项卡中
	$("#"+id,navTab.getCurrentPanel()).click();
	
	//用于进度条显示
	$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
	$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
	$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
	$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
	$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
	
}

function tonext(id,caseId,caseNo,accidentNo){
	var createDocumentFlag=$("#createDocumentFlag", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':"clm/register/queryMatchCalcNextMessage_CLM_claimMatchCalcAction.action?claimCaseVO.caseId="+caseId+"&claimCaseVO.createDocumentFlag="+createDocumentFlag,
		'type':'post',
		'success':function(data){
			var data = eval("(" + data + ")");
			if(data.statusCode!=null&&data.statusCode=="300"){
				alertMsg.error(data.message);
				flag = true;
			}else if(data.statusCode!=null&&data.statusCode=="485"){
				alertMsg.warn(data.message);
				$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/BenefitInputPageInit_CLM_toBenefitInputPageAction.action?caseId="+caseId+"&caseNo="+caseNo+"&accidentNo="+accidentNo);
				//将要跳转到的隐藏的选项卡显示
				$("#"+id,navTab.getCurrentPanel()).css("display","block");
				//调用选项卡的click方法，将页面跳转至下个选项卡中
				$("#"+id,navTab.getCurrentPanel()).click();
				
				//用于进度条显示
				$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
				$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
				$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
				$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
				$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
			}else{
				$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/BenefitInputPageInit_CLM_toBenefitInputPageAction.action?caseId="+caseId+"&caseNo="+caseNo+"&accidentNo="+accidentNo);
				//将要跳转到的隐藏的选项卡显示
				$("#"+id,navTab.getCurrentPanel()).css("display","block");
				//调用选项卡的click方法，将页面跳转至下个选项卡中
				$("#"+id,navTab.getCurrentPanel()).click();
				
				//用于进度条显示
				$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
				$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
				$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
				$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
				$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
			}
		}
	});
}



function prev(id,caseId){
		//将当前页面的参数传递给上一界面
		if(id=='11'){
			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/dataCollectInit_CLM_dataCollectAction.action?caseId="+caseId);
		}
		if(id=='12'){
			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/toRegisterCheckConfirmPageInit_CLM_toRegisterCheckConfirmPageAction.action?caseId="+caseId);
		} 
		if (id == '13') {
			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/toDataCollectInit_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			
		}
		if(id=='14'){
			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/report/contractHangUpInitRegister_CLM_contractHangUpAction.action?caseId="+caseId);
		}
		if(id=='15'){
			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/register/claimMatchCalcInit_CLM_claimMatchCalcAction.action?caseId="+caseId+"&isCalc=0");
		}
		//将当前页面的选项卡隐藏
		$("#"+(id-1+2),navTab.getCurrentPanel()).css("display","none");
		//调用选项卡的click方法，将页面跳转至下个选项卡中
		$("#" + id, navTab.getCurrentPanel()).click();
		
			//用于进度条显示
			$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
			$("#step"+(eval(id)+1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
			$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
			$("#n"+(eval(id)+1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
			$("#step"+(eval(id)+1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
}