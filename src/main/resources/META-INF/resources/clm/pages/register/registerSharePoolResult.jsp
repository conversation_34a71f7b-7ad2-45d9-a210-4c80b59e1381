<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript" src="clm/pages/register/registerSharePool.js">
</script>
<form id="pagerForm" method="post"
	action="clm/register/findRegisterSharePool_CLM_registerSharePoolAction.action?leftFlag=0&menuId=${menuId }">
	<input type="hidden" name="pageNum" vaule="${registerPoolPage.pageNo} " />
	<input type="hidden" name="numPerPage" value="${registerPoolPage.pageSize}" />
	<!-- 查询条件回调 -->
			<input type="hidden" name="requestVO.caseNo" value="${requestVO.caseNo }"  />
		   <input type="hidden" name="requestVO.customerName" value="${requestVO.customerName }"   />
		   <input type="hidden" name="requestVO.customerSex" value="${requestVO.customerSex }"   />	
		   <input type="hidden"   name="requestVO.customerId" value="${requestVO.customerId }"/>
		 	<input type="hidden" name="requestVO.greenFlag"  value="${requestVO.greenFlag }"   />
			<input type="hidden"   name="requestVO.accDate" 
								value='<s:property value="requestVO.accDateStr"/>'  />					 
</form>
<div class="tabdivclassbr">
	<table class="list main_dbottom" width="100%">
		<thead>
			<tr>
                 		<th nowrap>序号</th>
				<th nowrap>赔案号</th>
				<th nowrap>出险人姓名</th>
				<th nowrap>证件号码</th>
				<th nowrap>赔案状态</th>
				<th nowrap>机构</th>
				<th nowrap>绩优等级</th>
<!-- 				<th nowrap>受理渠道类型</th> -->
				<th nowrap>签收人</th>
				<th nowrap>签收时间</th>
				<th nowrap>申请渠道</th>
				
			</tr>
		</thead>
		<tbody align="center">
				<s:if test="imageFlag != null">
					<tr>
						<td colspan="11">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif test="responseVOList == null || responseVOList.size()==0">
					<tr>
						<td colspan="11">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>
			<!-- 循环显示数据 -->
			<s:iterator value="responseVOList" status="st">
				<tr ondblclick="registClick('${caseId}','${bpmTaskId}',this)" >
					<td><s:property value="#st.index + 1" /></td>
					<td><s:property	value="caseNo" /></td>
					<td><s:property value="customerName" /></td>
					<td><s:property value="customerCertiCode" /></td>
					<td>${caseStatusName}</td>
					<td><Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
					<td>
                      <s:if test="greenFlag==0 || greenFlag==null">否</s:if>
	                  <s:else>
	                      <Field:codeValue value="${greenFlag}" tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG"/> 
	                  </s:else>
                    </td>
					<td><s:property value="signerName" /></td>
					<td><s:date name="signTime" format="yyyy-MM-dd"/> </td>
					<td><Field:codeValue value="${channelCode}" tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" /></td>
					
				</tr> 
			</s:iterator>
		</tbody>
		   </table>
		    <!-- 分页查询区域 -->
<div class="panelBar">
	<div class="pages">
		<span>显示</span>
		<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
			name="select" onchange="navTabPageBreak({numPerPage:this.value},'registerSharePoolResult')"
			value="registerPoolPage.pageSize">
		</s:select>
		<span>条，共${registerPoolPage.total}条</span>
	</div>
	<div class="pagination" targetType="navTab" rel="registerSharePoolResult"
		totalCount="${registerPoolPage.total}"
		numPerPage="${registerPoolPage.pageSize}" pageNumShown="10"
		currentPage="${registerPoolPage.pageNo}"></div>
</div>
     </div>

