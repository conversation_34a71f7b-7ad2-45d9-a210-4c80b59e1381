<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript">

//用于伸缩按钮的伸缩功能
//$(document).ready(function(){
	  
$(".main_heading", $.pdialog.getCurrent()).off().toggle(function(){  
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
},function(){
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
 	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
});

//});

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]").bind("click",function(){
//		var id=$(this).attr("id").split("main_")[1];
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
})

/**
 * 生成重度失能复选框
 */ 
	var caseId=$("#caseId", $.pdialog.getCurrent()).val();
	$.ajax({
		'url':'clm/register/querySeriousDisability_CLM_tClaimDutyRegisterAction.action?caseId='+caseId,
		'type':'post',
		'data':{},
		'datatype':'json','success':function(data){
				var data = eval("(" + data + ")");
				for(var i=0;i<data.length;i++){
					var name=data[i].name;
					var code=data[i].code;
					var flag=data[i].flag;
					if(code.match('MUS')){
						if(flag=="0"){
							$("#seriousDisabilitycheckboxdiv",$.pdialog.getCurrent()).append("<span><input type='checkbox' checked='checked' class='seriousDisability' name='sdcheckboxCode' value='"+code+"'/>"+name+"</span>");
						}else{
							$("#seriousDisabilitycheckboxdiv",$.pdialog.getCurrent()).append("<span><input type='checkbox' class='seriousDisability' name='sdcheckboxCode' value='"+code+"'/>"+name+"</span>");
						}
						
					}else{
						if(flag=="0"){
							$("#adlboxdiv",$.pdialog.getCurrent()).append("<span><input type='checkbox' checked='checked' class='seriousDisability' name='adlboxCode' value='"+code+"'/>"+name+"</span>");
						}else{
							$("#adlboxdiv",$.pdialog.getCurrent()).append("<span><input type='checkbox' class='seriousDisability' name='adlboxCode' value='"+code+"'/>"+name+"</span>");
						}
					}
					
		  			}
				if(isApprove=="approve"){
					//readOnly("greatNewsInt");
					readOnly("approveDuty");
				}
			}
	});
 
</script>


		<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">重度失能录入
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
									<div class="main_bqtitle">
				                         <h1><img src="clm/images/three.png">重度失能信息录入</h1>
				                     </div>
				                     	<div id="greatNewsInt" class="panelPageFormContent">
											<div class="panelPageFormContent">
										
												<dl >
													<dt >肌力级别</dt>
												</dl>
												<div id="seriousDisabilitycheckboxdiv" style="width: 60%;float:left;margin-left: 150px;">
													<!-- <input type="hidden" id="sdcheckboxCode" name="sdcheckboxCode" > -->
												</div>
										
												<dl >
													<dt >ADL类型</dt>
												</dl>
												<div id="adlboxdiv" style="width: 70%;float:left;margin-left: 150px;">
													<!--  <input type="hidden" id="adlboxCode" name="adlboxCode" > -->
												</div>
											</div>
										</div>
				                     
								</div>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
