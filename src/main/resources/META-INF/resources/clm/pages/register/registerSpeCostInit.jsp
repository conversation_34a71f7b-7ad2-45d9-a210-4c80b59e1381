<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<!--账单及费用明细  div start -->
<script type="text/javascript" language="javascript">
 

function specialTypeChange(obj) {
	var relaCode = $(obj).attr("value"); 

	var surgeryCode = $("#specialCode1", navTab.getCurrentPanel());

	$.ajax({
				'type' : 'post',
				'url' : 'clm/register/querySpecialCode_CLM_tClaimDutyRegisterAction.action?SpecialTypeCode='
						+ relaCode,
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					surgeryCode.empty();
// 					surgeryCode.loadMyComboxOptions("<option value='-1' class = ''></option>");
					/* $(
							"<option value='-1' class = ''> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</option>")
							.loadMyComboxOptions(surgeryCode); */
					var option2 = "<option value='-1' class = ''></option>";
					for (var i = 0; i < data.length; i++) {
						option2 = option2 + "<option value='"+data[i].code+"'   class='"+data[i].name+"' >"
								+ data[i].code
								+ "-"
								+ data[i].name
								+ "</option>";
						//$(option2).loadMyComboxOptions(surgeryCode);
					}
					surgeryCode.loadMyComboxOptions(option2);
				},
				'error' : function() {
					alert("出错了！");
				}
			});
}
</script>

<div class="panelPageFormContent">
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">特种费用信息录入
					</h1>
				</div>
	<div class="panelPageFormContent" id="dutyHospitalCostInitDetails">
		<form class="pageForm required-validate" id="${pageFlag}FormId"
			action="clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
			method="post"
			onsubmit="return validateCallback(this,dutyAjaxDone)">
			<input type="hidden" name="claimSpecialVOlist[0].caseId" value="${caseId}">
			<input type="hidden" name="claimSpecialVOlist[0].specialId" value="${claimSpecialVOlist[0].specialId}">
			<input type="hidden" name="claimTypecode[0]" value="4">
			<input type="hidden" name="pageFlag" value="${pageFlag}">
			<input type="hidden"   name="caseId"  value="${caseId}">
			
			 
			<dl >
				<dt >
					<font class="point" color="red">* </font>特种费用类型
				</dt>
				<dd >
				<Field:codeTable onChange="specialTypeChange(this)"
									name="claimSpecialVOlist[0].specialType"
									tableName="APP___CLM__DBUSER.T_CLAIM_SPECIAL_TYPE"
									nullOption="true" cssClass="specialType combox title" value="${claimSpecialVOlist[0].specialType}" />
				</dd>
			</dl>
			<dl>
				<dt >
					<font class="point" color="red">* </font>特种费用代码
				</dt>
				<dd >
					<Field:codeTable id="specialCode1"
									name="claimSpecialVOlist[0].specialCode"  cssClass="combox title"
									tableName="APP___CLM__DBUSER.T_CLAIM_SPECIAL_CODE"
									nullOption="true" value="${claimSpecialVOlist[0].specialCode}"
									whereClause="special_type_code = '${claimSpecialVOlist[0].specialType}' " />
					 
				</dd>
			</dl>
			<dl sizset="0">
				<dt >金额</dt>
				<dd sizset="0">
					<input name="claimSpecialVOlist[0].sumAmount" value="${claimSpecialVOlist[0].sumAmount}" class="textInput"
						type="text"  onkeyup="this.value=this.value.replace('-','')" />
				</dd>
			</dl>
			<dl sizset="1">
				<dt >
					<font class="point" color="red">* </font>服务机构名称
				</dt>
				<dd sizset="1">
					<input name="claimSpecialVOlist[0].serviceOrgName"  value="${claimSpecialVOlist[0].serviceOrgName }"
						class="textInput" type="text"   />
				</dd>
			</dl>
			<dl >
				<dt >
					<font class="point" color="red">* </font>开始日期
				</dt>
				<dd >
					<input id="spcStartTime" name="claimSpecialVOlist[0].feeStart" class="date textInput"  value="<s:date name='claimSpecialVOlist[0].feeStart' format='yyyy-MM-dd' />"
						onpropertychange="queryCalimDateSpe(this);" type="expandDateYMD"
						maxLength="10"  
						datefmt="yyyy-MM-dd" /><a class="inputDateButton"
						href="javascript:;"  >选择</a> 
				</dd>
			</dl>
			<dl >
				<dt >
					<font class="point" color="red">* </font>结束日期
				</dt>
				<dd > 
					<input id="spcEndTime" name="claimSpecialVOlist[0].feeEnd" class="date textInput"value="<s:date name='claimSpecialVOlist[0].feeEnd' format='yyyy-MM-dd' />"
						onpropertychange="queryCalimDateSpe(this);" type="expandDateYMD"
						maxLength="10"  
						datefmt="yyyy-MM-dd" /><a class="inputDateButton"
						href="javascript:;"  >选择</a> 
				</dd>
			</dl> 
		</form>
	</div>

</div>
<!--医疗费用明细  div end  --> 
