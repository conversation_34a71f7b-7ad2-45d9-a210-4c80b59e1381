<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript" src="clm/pages/register/directSharePool.js">
</script>
<form id="pagerForm" method="post"
	action="clm/register/findDirectSharePool_CLM_directSharePoolAction.action?leftFlag=0&menuId=${menuId }">
	<input type="hidden" name="pageNum" vaule="${directPoolPage.pageNo} " />
	<input type="hidden" name="numPerPage" value="${directPoolPage.pageSize}" />
	<!-- 查询条件回调 -->
			<input type="hidden" name="requestVO.caseNo" value="${requestVO.caseNo }"  />
		   <input type="hidden" name="requestVO.customerName" value="${requestVO.customerName }"   />
</form>
<div class="tabdivclassbr">
	<table class="list main_dbottom" width="100%">
		<thead>
			<tr>
                <th nowrap>序号</th>
				<th nowrap>赔案号</th>
				<th nowrap>签收机构</th>
				<th nowrap>报案方式</th>
				<th nowrap>服务商</th>
				<th nowrap>出险人姓名</th>
				<th nowrap>签收日期</th>
			</tr>
		</thead>
		<tbody align="center">
				<s:if test="imageFlag != null">
					<tr>
						<td colspan="11">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif test="responseVOList == null || responseVOList.size()==0">
					<tr>
						<td colspan="11">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>
			<!-- 循环显示数据 -->
			<s:iterator value="responseVOList" status="st">
				<tr>
					<td><s:property value="#st.index + 1" /><input type="checkbox"  value="${bpmTaskId}"> </td>
					<td>
						<span <s:if test ='isRedFlag=="1"'>style="color:red"</s:if>><s:property value="caseNo" /></span>
					</td>
					<td><Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
					<td><Field:codeValue value="${reportMode}" tableName="APP___CLM__DBUSER.T_REPORT_TYPE" /></td>
					<td><s:property value="serviceCommerce" /></td>
					<td><s:property value="customerName" /></td>
					<td><s:date name="signTime" format="yyyy-MM-dd"/></td>
				</tr> 
			</s:iterator>
		</tbody>
		   </table>
		    <!-- 分页查询区域 -->
<div class="panelBar">
	<div class="pages">
		<span>显示</span>
		<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
			name="select" onchange="navTabPageBreak({numPerPage:this.value},'registerSharePoolResult')"
			value="directPoolPage.pageSize">
		</s:select>
		<span>条，共${directPoolPage.total}条</span>
	</div>
	<div class="pagination" targetType="navTab" rel="registerSharePoolResult"
		totalCount="${directPoolPage.total}"
		numPerPage="${directPoolPage.pageSize}" pageNumShown="10"
		currentPage="${directPoolPage.pageNo}"></div>
</div>
     </div>

