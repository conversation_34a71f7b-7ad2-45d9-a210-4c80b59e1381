<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%> 
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
 <script type="text/javascript">
	var reportMode =  $("#reportMode", navTab.getCurrentPanel()).val();
	var directClaimItemDetail =  $("#directClaimItemDetail", navTab.getCurrentPanel()).val();
	if(directClaimItemDetail == 1){
		$("#directDetail", navTab.getCurrentPanel()).show();
	}else{
		$("#directDetail", navTab.getCurrentPanel()).hide();
	}
 	//控制电票入账明细按钮
 	var billEntryFlag =  $("#billEntryFlag", navTab.getCurrentPanel()).val();
	if(billEntryFlag == 1){
		$("#elecBillEntryDetail", navTab.getCurrentPanel()).show();
	}else{
		$("#elecBillEntryDetail", navTab.getCurrentPanel()).hide();
	}  
	//查看费用明细
	function querydirectDetail() {
		var billId = "";
		var billNo = "";
		$("#registerHospital", navTab.getCurrentPanel()).find("#registerAddhas").find("tbody").find("tr").each(function(){
			if($(this).find("td").eq(0).find("input").eq(0).attr("checked")){
				billId = $(this).find("td").eq(0).find("input").eq(1).val();
				billNo = $(this).find("td").eq(2).html();
			}
		});
		//选中账单才可以查看
		if(billId!="" && billNo !=""){
			var caseId = "${caseId}";
			var url = "clm/register/queryClaimBillItemDetail_CLM_tClaimDutyRegisterAction.action?claimCaseVO.caseId="
					+ caseId+"&claimBillVO.billId=" + billId + "&claimBillVO.billNo=" + billNo;
			$("#directDetailDiv", navTab.getCurrentPanel()).attr("href", url).click();
		}
	}
	function pageAddNew(obj) {
	//	alert("sss");
	    var caseId = "${caseId}";
		var pageFlag=$(obj).parent().parent().attr("titles");
		var  billType=$(obj).parent().parent().attr("billType");
	//	alert(pageFlag+'+++'+billType);
	    //取消门诊/住院 列表单选按钮
	    if(pageFlag=="registerHonsp" && $("#registerHospitalTBody", navTab.getCurrentPanel()).find("tr").length > 0){
	    	$("#registerHospitalTBody", navTab.getCurrentPanel()).find("tr").each(function(){
	    		$(this).find("td:eq(0)").find("input:eq(0)").attr("checked", false);
	    	});
	    }
		var url ='clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='+pageFlag+'&claimBillItemVO.caseId='+caseId+'&caseId='
				+caseId+'&claimBillVO.billType='+billType;
	   // url ='clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?claimBillItemVO.billId=&claimBillItemVO.caseId='+caseId+'&flag=';

		$(obj).parent().next().loadUrl(url);

		$(obj).parent().next().show("30000");
		return;
	}
	
	
	
	function saveDutyDetail(obj) {
		var pageFlag=$(obj).parent().parent().attr("titles"); 
	 
		var isok=true;
		var isoks=true;
		
		if(pageFlag=="registerHonsp"||pageFlag=="highMedical"||pageFlag=="cancerMedical"){
			isok=  billValidate();  
		}
		if(pageFlag=="registerOpsLevel"){
			isoks = opsLevel();
		}
		if(pageFlag=="registerGreat"){
			var a = $("#specialCode").val();
			//alert(a);
			var isNineFive = $("#isNineFive").val();
			if(isNineFive == 1){//950产品提示信息
				if($("#specialCode").val()=="FT012"){
					alertMsg.confirm("如客户确诊的癌症为以下原发癌：肺癌、肝癌、骨癌、脑癌、胰腺癌时，请同时在特种疾病下选择相应代码，可以同时匹配特定恶性肿瘤确诊保险金?",{
					 	okCall:function(){
							//alert("7777");
							 console.log("isok:"+isok+"--"+"isoks:"+isoks);

								if(isok&&isoks){
									$("#errorFlag", navTab.getCurrentPanel()).val("true");  
									$("#"+pageFlag+"FormId", navTab.getCurrentPanel()).submit();
								}else{
									$("#errorFlag", navTab.getCurrentPanel()).val("false");  
								}
					 	}});
					return false;
				}
			}
		}

		if(isok&&isoks){
			$("#errorFlag", navTab.getCurrentPanel()).val("true");  
			$("#"+pageFlag+"FormId", navTab.getCurrentPanel()).submit();
		}else{
			$("#errorFlag", navTab.getCurrentPanel()).val("false");  
		}
	}
	
function opsLevel(){
	var isok = true;
	if($("#opsLevelDate", navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("手术日期必填");
		$("#alertMsgBox .toolBar .button").die().live("click",function(){
			$("#opsLevelDate", navTab.getCurrentPanel()).focus();
		});
		isok=false;
		return false;
	}
	if($("#hospitalCodeId", navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("医疗机构必填");
		$("#alertMsgBox .toolBar .button").die().live("click",function(){
			$("#hospitalCodeId", navTab.getCurrentPanel()).focus();
		});
		isok=false;
		return false;
	}
	return isok;
}
	
  function  billValidate(){
	  var  isok=true;
	  var $obj=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel());
	  if($obj == null){
		  return false;
	  }
	    if ($obj.find("#billNo").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#billNo").focus();
			});
			isok=false;
			return false;
		}
	    if ($obj.find("#hospitalId").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#hospitalId").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#cureType").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#cureType").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#treatStart").val() == "") {
			alertMsg.error("请录入必录项"); 
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#treatStart").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#treatEnd").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#treatEnd").focus();
			}); 
			isok=false;
			return false;
		}
		 ///医疗单证录入验证
		 //总费用
		var sumFee = 0.00;
		//扣除费用
		var sumDeduct = 0.00;
		//理算金额
		var calcFee = 0.00;
		//--------保存费用明细验证金额输入是否正确-------
		$("#feeItemId", navTab.getCurrentPanel()).find("tr").each(function(i,object) {
			if ($(this).find("#medFeeItem").val() == "") {
				alertMsg.error("费用明细项目必填！");
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#medFeeItem").focus();
				}); 
				isok=false;
				return false;
			}else{
				var feeFlag = false;
				if($(this).find("#medFeeItem").val()=='CC007'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC013'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC014'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC015'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC016'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}        
				if($(this).find("#medFeeItem").val()=='CC017'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}        
				if($(this).find("#medFeeItem").val()=='CC018'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				} 
				if (feeFlag) {
					alertMsg.error("手术代码必填！");
					$("#alertMsgBox .toolBar .button").die().live("click",function(){
						$(object).find("#operationNameId").focus();
					}); 
					isok=false;
					return false;
				}
				
			}
			if ($(this).find("#feeAmount").val() == "") {
				alertMsg.error("费用金额必填！");
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#feeAmount").focus();
				}); 
				isok=false;
				return false;
			}
			if ($(this).find("#expenseAmount").val() > 0) {
				if ($(this).find("#expenseRemark").val() == "") {
						alertMsg.error("自费备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#expenseRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if ($(this).find("#payAmount").val() > 0) {
				/* if ($(this).find("#deductReason").val() == "") {
				alertMsg.error("扣除原因必填！"); 
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#deductReason").focus();
				});
				isok=false;
				return false;
			    } */
				if ($(this).find("#payRemark").val() == "") {
						alertMsg.error("自付备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#payRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if ($(this).find("#otherAmount").val() > 0) {
				if ($(this).find("#otherRemark").val() == "") {
						alertMsg.error("其他备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#otherRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if($(this).find("#feeAmount").val() > 0){
				sumFee =  sumFee + parseFloat($(this).find("#feeAmount").val()) ;
			} 
			if($(this).find("#expenseAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#expenseAmount").val());
			} 
			if($(this).find("#payAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#payAmount").val());
			} 
			if($(this).find("#otherAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#otherAmount").val());
			} 
			
			//如果存在扣除费用则
			if($(this).find("#deductAmount").val() > 0){
				sumDeduct =   parseFloat($(this).find("#deductAmount").val());
			}  
			if($(this).find("#calcAmount").val() > 0){
				calcFee =  calcFee
				+ parseFloat($(this).find("#calcAmount").val()); 
			} 
		  }); 
		 
		   $("#sumAmountAll", navTab.getCurrentPanel()).val(sumFee);
		   $("#deductAmountAll", navTab.getCurrentPanel()).val(sumDeduct);
		   $("#calcAmountAll", navTab.getCurrentPanel()).val(calcFee);
			if(isNaN($("#deductAmountAll", navTab.getCurrentPanel()).val())){
				$("#deductAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if(isNaN($("#sumAmountAll", navTab.getCurrentPanel()).val())){
				$("#sumAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if(isNaN($("#calcAmountAll", navTab.getCurrentPanel()).val())){
				$("#calcAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if($("#calcAmountAll", navTab.getCurrentPanel()).val()){
				var calcAmountAll = $("#sumAmountAll", navTab.getCurrentPanel()).val() -  $("#deductAmountAll", navTab.getCurrentPanel()).val();
				$("#calcAmountAll", navTab.getCurrentPanel()).val(calcAmountAll);
			}
		 return isok;
		
	}
	 
  if($("#auditClaimCheckListInit", navTab.getCurrentPanel()).val()=="auditClaimCheckListInit"){
 
	$("#AddNewButton", navTab.getCurrentPanel()).remove();
	$("#SaveNewButton", navTab.getCurrentPanel()).remove();
	 $(".btnDel", navTab.getCurrentPanel()).attr("disabled","disabled");
 }
 
  if(document.readyState=="complete"){  
		var currentPageId= <%=session.getAttribute("currentPageId") %>;  
		 if((currentPageId!="" && currentPageId>3)||readOnly==1 ){
			  //置灰删除按钮 
			 $(".btnDel", navTab.getCurrentPanel()).each(function(index,objct){
				 $(objct).attr("disabled",true);
		  }); 
			
		}
  } 
	function dateInitHonsp(obj) {
		    var caseId = "${caseId}";
			var pageFlag=$(obj).parent().parent().attr("titles");
			if(pageFlag == "registerHonsp"){
				$.ajax({
					'type':'post',
					'url':'clm/register/initDutyItem_CLM_tClaimDutyRegisterAction.action',
					'data':{'caseId':caseId,'pageFlag':pageFlag},
					'datatype':'json',
					'success':function(json){
						if(json.statusCode == DWZ.statusCode.error) {
							  alertMsg.error("单证初始化失败！");  
						}else {
							next_audit('2',caseId);
							var id=json.pageFlag;
							var caseId1=json.caseId;
							if(id == "registerHonsp"){
								var rel = $("#registerHonsp", navTab.getCurrentPanel());
								rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId1);
							}	
							alertMsg.correct("单证初始化成功！");
						}
					},
				});
			}
			
		}
 </script>
<div id="pageFormdivID" class="pageFormdiv main_tabdiv" > 

	         <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""} class="button" onclick="pageAddNew(this)"  id="AddNewButton"  href="javascript:void(0);"  ><span>添加</span></a>
             <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""}  class="button" onclick="saveDutyDetail(this)" id="SaveNewButton" href="javascript:void(0);" ><span>保存</span></a>
             <s:if test ="claimCaseVO.caseStatus > 31 and claimCaseVO.caseStatus <= 61">
             <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""} class="button" onclick="dateInitHonsp(this)" href="javascript:void(0);" ><span>单证初始化</span></a>
			 </s:if>
			 <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""} class="button" href="#" onclick="querydirectDetail();" id="directDetail"  style="display: none;"><span>医疗费用明细清单</span></a>
			 <a id="directDetailDiv" href="#" rel="directDetailDiv"
									target="dialog" max='true' [mask=true ] style="display: none;"
									width="1000" height="450">医疗费用明细清单</a>
             <a id="elecBillEntryDetail" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""} class="but_blue main_buta"
					href=" clm/register/findBillItemEntry_CLM_tClaimDutyRegisterAction.action?caseId=${caseId}&caseNo=${claimCaseVO.caseNo}" style="display: none;"
				target="dialog" rel="page2" class="button"><span>电票入账明细</span></a>
</div> 	
<div id='hospitalcost' style='display: none'></div>
