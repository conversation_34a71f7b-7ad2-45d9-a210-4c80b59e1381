//用于治疗医院不允许手动输入只可以查询带出
function cureHospitalData(e) {
	var keynum = window.event ? e.keyCode : e.which;
	if(!(keynum=="37"||keynum=="39")){
		$("#inputHospital", navTab.getCurrentPanel()).val("");
		$("#cureHospital", navTab.getCurrentPanel()).val("");
	}
}
// 用于重大疾病不允许手动输入只可以查询带出
function seriousDiseaseDData(e) {
	var keynum = window.event ? e.keyCode : e.which;
	if(!(keynum=="37"||keynum=="39")){
		$("#seriousDiseaseData", navTab.getCurrentPanel()).val("");
		$("#seriousDiseaseCodeData", navTab.getCurrentPanel()).val("");
	}
}
// 页面初始化时改变国家的首选项
var options = $("#contryDatatId", navTab.getCurrentPanel()).find("option");
options[0].setAttribute("title", "国家");
if (options[0].text == "请选择") {
	options[0].text = "国家";
}
// 页面初始化时改变省/直辖市的首选项
var optionss = $("#provinceReportId", navTab.getCurrentPanel()).find("option");
optionss[0].setAttribute("title", "省/直辖市");
if (optionss[0].text == "请选择") {
	optionss[0].text = "省/直辖市";
}
// 申请人页面初始化时改变省/直辖市的首选项
var optionss = $("#provinceReportApplicantId", navTab.getCurrentPanel()).find(
		"option");
optionss[0].setAttribute("title", "省/直辖市");
if (optionss[0].text == "请选择") {
	optionss[0].text = "省/直辖市";
}

// 已有事故带回时国家赋值
function accContryCodeFirst() {
	var t = $("#contryDatatId", navTab.getCurrentPanel())[0];
	if (document.all) {
		t.fireEvent("onchange", event);
	} else {
		var evt = document.createEvent('HTMLEvents');
		evt.initEvent('change', true, true);
		t.dispatchEvent(evt);
	}
}

function queryPolicySign(customerId) {
	var url = "clm/common/initPolicQueryPage_CLM_commonQueryAction.action?customerId="
			+ customerId;
	$("#policQueryId", navTab.getCurrentPanel()).attr("href", url).click();
}

// 用于伸缩按钮的伸缩功能
$(document).ready(
		function() {
			$(".main_heading").toggle(
					function() {
						$(this).next(".main_lptwo").animate({
							height : 'toggle',
							opacity : 'toggle'
						}, "slow");
						if ($(this).find("b").attr("class") == "maim_lpask") {
							$(this).find("b").removeClass("maim_lpask")
									.addClass("maim_lpbubble");
						} else {
							$(this).find("b").removeClass("maim_lpbubble")
									.addClass("maim_lpask");
						}
						if ($("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).val() == "02") {
							$("#clmtPropertyCodeDataIdFordc",
									navTab.getCurrentPanel()).prev().attr(
									"style", "width: 35px;border-right:0px;");
							$("#contractBeneInfoDataIdFordc",
									navTab.getCurrentPanel()).prev().attr(
									"style", "width:55px;");
						} else {
							$("#contractBeneInfoDataIdFordc",
									navTab.getCurrentPanel()).setMyComboxHide(
									true);
						}
					},
					function() {
						$(this).next(".main_lptwo").animate({
							height : 'toggle',
							opacity : 'toggle'
						});
						if ($(this).find("b").attr("class") == "maim_lpask") {
							$(this).find("b").removeClass("maim_lpask")
									.addClass("maim_lpbubble");
						} else {
							$(this).find("b").removeClass("maim_lpbubble")
									.addClass("maim_lpask");
						}
					});

		});

function city11(city) {
	// 获取市为县赋值
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#areaReportId", navTab.getCurrentPanel()).empty();
					$(areaReportId).append("<option value=''>区/县</option>");
					for (var i = 0; i < data.length; i++) {
						if (data[i].code == area) {
							$("#areaReportId", navTab.getCurrentPanel()).prev()
									.val(data[i].code + "-" + data[i].name);
							$(areaReportId).append(
									"<option value='" + area + "' class = '"
											+ data[i].name + "'>"
											+ data[i].name + "</option>");
						}
					}
					for (var i = 0; i < data.length; i++) {
						if (data[i].code != area) {
							var option1 = "<option value='" + data[i].code
									+ "'   class='" + data[i].name + "' >"
									+ data[i].name + "</option>";
							$(areaReportId).append(option1);
						}
					}
					$("#areaReportId", navTab.getCurrentPanel()).val(area);
				},
			})
}

// 页面初始化的进行加载省市县的值
var caseId = $("#claimCaseNo", navTab.getCurrentPanel()).val();
var cityReportId = $("#cityReportId", navTab.getCurrentPanel());
var areaReportId = $("#areaReportId", navTab.getCurrentPanel());
// 出险原因为疾病的时候，清空意外细节信息。
if ($("#accReasonId", navTab.getCurrentPanel()).val() != "2") {
	$("#accidentDetailId", navTab.getCurrentPanel()).attr("disabled",
			"disabled");
} else {
	$("#accidentDetailId", navTab.getCurrentPanel()).remove("disabled");
}
if ($("#contryDatatId", navTab.getCurrentPanel()).val() == "CHN") { // 初始化上来如果是中国的话，那么进行省市县的初始化，否则不的。
	if (caseId != "") {
		var province = $("#accProvince1ReportIdData", navTab.getCurrentPanel())
				.val();
		var city = $("#accCity1ReportIdData", navTab.getCurrentPanel()).val();
		var area = $("#accDistreact1ReportIdData", navTab.getCurrentPanel())
				.val();
		$("#provinceReportId", navTab.getCurrentPanel()).val(province);
		claimFireEvent($("#provinceReportId", navTab.getCurrentPanel()));
		if (province != "") {
			// 根据省获取市。为市赋值。
			$.ajax({
						'type' : 'post',
						'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
								+ province,
						'datatype' : 'json',
						'async' : true,
						'success' : function(data) {
							var data = eval("(" + data + ")");
							$("#cityReportId", navTab.getCurrentPanel())
									.empty();
							$(cityReportId).append(
									"<option value=''>市</option>");
							for (var i = 0; i < data.length; i++) {
								if (data[i].code == city) {
									$("#cityReportId", navTab.getCurrentPanel())
											.prev().val(
													data[i].code + "-"
															+ data[i].name);
									$(cityReportId).append(
											"<option value='" + city
													+ "' class = '"
													+ data[i].name + "'>"
													+ data[i].name
													+ "</option>");
								}
							}
							for (var i = 0; i < data.length; i++) {
								if (data[i].code != city) {
									var option1 = "<option value='"
											+ data[i].code + "'   class='"
											+ data[i].name + "' >"
											+ data[i].name + "</option>";
									$(cityReportId).append(option1);
								}
							}
							$("#cityReportId", navTab.getCurrentPanel()).val(
									city);
						},
					});
			setTimeout("city11(" + city + ")", 0.0003);

			// setTimeOut(0.01);
			// $("#cityReportId" , navTab.getCurrentPanel())[0].fireEvent("onchange");
			// setTimeOut(0.1);
			// $("#areaReportId" , navTab.getCurrentPanel())[0].fireEvent("onchange");
		}
	} else { // 初始化上来的时候默认为北京市市辖区东城区。
		var province = "110000";
		var city = "110100";
		var area = "110101";
		$("#provinceReportId", navTab.getCurrentPanel()).val(province);
		claimFireEvent($("#provinceReportId", navTab.getCurrentPanel()));
		// 根据省获取市。为市赋值。
		$.ajax({
					'type' : 'post',
					'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
							+ province,
					'datatype' : 'json',
					'async' : true,
					'success' : function(data) {
						var data = eval("(" + data + ")");
						$("#cityReportId", navTab.getCurrentPanel()).empty();
						$("<option value=''>市</option>").appendTo(cityReportId);
						for (var i = 0; i < data.length; i++) {
							if (data[i].code == city) {
								$("#cityReportId", navTab.getCurrentPanel())
										.prev().val(
												data[i].code + "-"
														+ data[i].name);
								$("#cityReportId", navTab.getCurrentPanel())
										.val(data[i].code);
								$(
										"<option value='" + city
												+ "' class = '" + data[i].name
												+ "'>" + data[i].name
												+ "</option>").appendTo(
										cityReportId);
							}
						}
						for (var i = 0; i < data.length; i++) {
							if (data[i].code != city) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$(option1).appendTo(cityReportId);
							}
						}
						$("#cityReportId", navTab.getCurrentPanel()).val(city);
					},
				});
		// 获取市为县赋值
		$.ajax({
					'type' : 'post',
					'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
							+ city,
					'datatype' : 'json',
					'async' : true,
					'success' : function(data) {
						var data = eval("(" + data + ")");
						$("#areaReportId", navTab.getCurrentPanel()).empty();
						$("<option value=''>区/县</option>").appendTo(
								areaReportId);
						for (var i = 0; i < data.length; i++) {
							if (data[i].code == area) {
								$("#areaReportId", navTab.getCurrentPanel())
										.prev().val(
												data[i].code + "-"
														+ data[i].name);
								$("#areaReportId", navTab.getCurrentPanel())
										.val(data[i].code);
								$(
										"<option value='" + area
												+ "' class = '" + data[i].name
												+ "'>" + data[i].name
												+ "</option>").appendTo(
										areaReportId);
							}
						}
						for (var i = 0; i < data.length; i++) {
							if (data[i].code != area) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$(option1).appendTo(areaReportId);
							}
						}
						$("#areaReportId", navTab.getCurrentPanel()).val(area);
					},
				});
		// setTimeOut(0.01);
		// $("#cityReportId" ,
		// navTab.getCurrentPanel())[0].fireEvent("onchange");
		// setTimeOut(0.1);
		// $("#areaReportId" ,
		// navTab.getCurrentPanel())[0].fireEvent("onchange");
	}
} else {
	// 把省市县禁掉，为空。
	$("#provinceReportId", navTab.getCurrentPanel()).val("");
	$("#cityReportId", navTab.getCurrentPanel()).attr("value", "");
	$("#areaReportId", navTab.getCurrentPanel()).attr("value", "");
	$("#provinceReportId", navTab.getCurrentPanel()).attr("disabled",
			"disabled");
	$("#cityReportId", navTab.getCurrentPanel()).attr("disabled", "disabled");
	$("#areaReportId", navTab.getCurrentPanel()).attr("disabled", "disabled");
}

// 设置下一步中置灰后的标识
var bs = 0;

// 出险结果页面查询
function accidentVOResultPageData(k) {
	// 先给出险结果排序，防止出现重复的序号
	//获取出险原因
	var accReasonId = $("#accReasonId", navTab.getCurrentPanel()).val();
	var trs = $("#tbodysIdData", navTab.getCurrentPanel()).find("tr");
	for (var i = 0; i < trs.length; i++) {
		$("#tbodysIdData", navTab.getCurrentPanel()).find("tr:eq(" + i + ")")
				.find("td:eq(0)").find("input").val(i + 1);
	}
	$("#accidentResultBiaoIdData", navTab.getCurrentPanel()).val(
			$(k).parent().parent().find("td:eq(0)").find("input").val());
	$("#accidentVOResultPageIdData", navTab.getCurrentPanel())
			.attr("href",
					"clm/report/accResultQueryFollowInit_CLM_accResultQueryFollowAction.action?accReason="+accReasonId+"&reportFlag=data");
	$("#accidentVOResultPageIdData", navTab.getCurrentPanel()).click();
}
// 出险结果带回
function bringBackAccidentResultInfoData() {
	var index = $("#accidentResultBiaoIdData", navTab.getCurrentPanel()).val();// 定位到选择哪个出险结果。
	var indexNew = index - 1;
	var accident1CodID = $("#accident1CodIDData", navTab.getCurrentPanel())
			.val();
	var accident1NamID = $("#accident1NamIDData", navTab.getCurrentPanel())
			.val();
	var accident2CodID = $("#accident2CodIDData", navTab.getCurrentPanel())
			.val();
	var accident2NamID = $("#accident2NamIDData", navTab.getCurrentPanel())
			.val();
	$("#tbodysIdData", navTab.getCurrentPanel())
			.find("tr:eq(" + indexNew + ")").find("td:eq(1)").find(
					"input:eq(2)").val(accident1CodID);
	$("#tbodysIdData", navTab.getCurrentPanel())
			.find("tr:eq(" + indexNew + ")").find("td:eq(1)").find(
					"input:eq(3)").val(accident1NamID);
	$("#tbodysIdData", navTab.getCurrentPanel())
			.find("tr:eq(" + indexNew + ")").find("td:eq(2)").find(
					"input:eq(2)").val(accident2CodID);
	$("#tbodysIdData", navTab.getCurrentPanel())
			.find("tr:eq(" + indexNew + ")").find("td:eq(2)").find(
					"input:eq(3)").val(accident2NamID);
}

// $(function (){
var checked = $("#register", navTab.getCurrentPanel()).find("input:checked");
checked.click();

// var caseNo = $("#claimCaseNo",navTab.getCurrentPanel()).val();
// if(caseNo != ""){
// //判断申请人的属性默认值
// if($("#applicantNatureFlagDataId" , navTab.getCurrentPanel()).val() == "1"){
// //如果为1的话，有申请人信息
// if($("#clmtPropertyCodeDataIdFordcId" , navTab.getCurrentPanel()).val() == "01"){
// //为本人的时候，带出出险人信息
// $("#contractBeneInfoDataIdFordc" ,
// navTab.getCurrentPanel()).css("visibility","hidden");
// $.ajax({
// 'type':'post',
// 'url':'clm/sign/queryCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='+caseNo,
// 'datatype':'json',
// 'success':function(claimApplicantVO){
// var claimApplicantVO = eval("(" + claimApplicantVO + ")");
// $("#clmtName" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtName);
// $("#clmtInsurRelationFordc" , navTab.getCurrentPanel()).val("01");
// $("#clmtMp" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtMp);
// $("#clmtMail" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtMail);
// $("#clmtSex" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtSex);
// $("#clmtCertiType" ,
// navTab.getCurrentPanel()).val(claimApplicantVO.clmtCertiType);
// $("#clmtCertiNo" ,
// navTab.getCurrentPanel()).val(claimApplicantVO.clmtCertiNo);
// $("#clmtProfession" ,
// navTab.getCurrentPanel()).val(claimApplicantVO.clmtProfession);
// $("#clmtTel" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtTel);
// $("#clmtNation" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtNation);
// if(claimApplicantVO.peopleFlag == "1"){
// $("#clmtInsurRelationFordc" , navTab.getCurrentPanel()).val("01");
// $("#clmtInsurRelationFordc" , navTab.getCurrentPanel()).attr("disabled",true);
// } else {
// $("#peopleFlagId" ,
// navTab.getCurrentPanel()).val(claimApplicantVO.peopleFlag);
// }
// },
// });
// } else if($("#clmtPropertyCodeDataIdFordcId" , navTab.getCurrentPanel()).val() ==
// "02"){ //指定受益人的时候，带出保单受益人的信息。
// $("#contractBeneInfoDataIdFordc" ,
// navTab.getCurrentPanel()).css("visibility","visible");
// var contractBeneInfoId = $("#contractBeneInfoDataIdFordc" , navTab.getCurrentPanel());
// $.ajax({
// 'type':'post',
// 'url':'clm/sign/queryContractCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='+caseNo,
// 'datatype':'json',
// 'success':function(contractBeneVOs){
// var contractBeneVOs = eval("(" + contractBeneVOs + ")");
// $("<option value='' class = ''>请选择</option>").appendTo(contractBeneInfoId);
// for(var i = 0; i < contractBeneVOs.length;i++){
// if(contractBeneVOs[i].customerName != ""){
// var insertHtml = "<option
// value="+contractBeneVOs[i].customerName-contractBeneVOs[i].designation+">"+contractBeneVOs[i].customerName+"</option>";
// $(insertHtml).appendTo(contractBeneInfoId);
// }
// }
// },
// });
// }
// } else { //没有申请人信息
// //判断申请人的属性默认值
// if($("#applicantNatureDataId" , navTab.getCurrentPanel()).val() == "1"){
// //如果为1的话，申请人属性默认值为指定受益人
// $("#clmtPropertyCodeDataIdFordc" , navTab.getCurrentPanel()).val("02");
// } else { //申请人属性默认为本人
// $("#clmtPropertyCodeDataIdFordc" , navTab.getCurrentPanel()).val("01");
// }
// if($("#clmtPropertyCodeDataIdFordc" , navTab.getCurrentPanel()).val() == "01"){
// //为本人的时候，带出出险人信息
// $("#contractBeneInfoDataIdFordc" ,
// navTab.getCurrentPanel()).css("visibility","hidden");
// $.ajax({
// 'type':'post',
// 'url':'clm/sign/queryCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='+caseNo,
// 'datatype':'json',
// 'success':function(claimApplicantVO){
// var claimApplicantVO = eval("(" + claimApplicantVO + ")");
// $("#clmtName" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtName);
// $("#clmtInsurRelationFordc" , navTab.getCurrentPanel()).val("01");
// $("#clmtMp" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtMp);
// $("#clmtMail" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtMail);
// if(claimApplicantVO.peopleFlag == "1"){
// $("#clmtInsurRelationFordc" , navTab.getCurrentPanel()).val("01");
// $("#clmtInsurRelationFordc" , navTab.getCurrentPanel()).attr("disabled",true);
// } else {
// $("#peopleFlagId" ,
// navTab.getCurrentPanel()).val(claimApplicantVO.peopleFlag);
// }
// },
// });
// } else if($("#clmtPropertyCodeDataIdFordc" , navTab.getCurrentPanel()).val() ==
// "02"){ //指定受益人的时候，带出保单受益人的信息。
// $("#contractBeneInfoDataIdFordc" ,
// navTab.getCurrentPanel()).css("visibility","visible");
// var contractBeneInfoId = $("#contractBeneInfoDataIdFordc" ,  navTab.getCurrentPanel());
// $.ajax({
// 'type':'post',
// 'url':'clm/sign/queryContractCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='+caseNo,
// 'datatype':'json',
// 'success':function(contractBeneVOs){
// var contractBeneVOs = eval("(" + contractBeneVOs + ")");
// $("<option value='' class = ''>请选择</option>").appendTo(contractBeneInfoId);
// for(var i = 0; i < contractBeneVOs.length;i++){
// if(contractBeneVOs[i].customerName != ""){
// var insertHtml = "<option
// value="+contractBeneVOs[i].customerName-contractBeneVOs[i].designation+">"+contractBeneVOs[i].customerName+"</option>";
// $(insertHtml).appendTo(contractBeneInfoId);
// }
// }
// },
// });
// }
// }
// }

var trs = $("#tbodyIds", navTab.getCurrentPanel()).find("tr");
for (var i = 0; i < trs.length; i++) {
	var claimType = $("#tbodyIds", navTab.getCurrentPanel()).find(
			"tr:eq(" + i + ")").find("td:eq(0)").find("select").val();
	var caseNo = $("#claimCaseNo", navTab.getCurrentPanel()).val();
	if (claimType == "01") { // 身故的时候申请人属性默认为指定受益人
		$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).selectMyComBox("02");
		$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev().attr(
				"style", "width: 35px;border-right:1px;");
		if (caseNo != "") {
			var contractBeneInfoId = $("#contractBeneInfoDataIdFordc", navTab
					.getCurrentPanel());
			$.ajax({
						'type' : 'post',
						'url' : 'clm/sign/queryContractCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='
								+ caseNo,
						'datatype' : 'json',
						'async' : true,
						'success' : function(contractBeneVOs) {
							var contractBeneVOs = eval("(" + contractBeneVOs
									+ ")");
							var options = "";
							options = "<option value=''>请选择</option>";
							for (var i = 0; i < contractBeneVOs.length; i++) {
								if (contractBeneVOs[i].customerName != "") {
									options += "<option value='"
											+ contractBeneVOs[i].customerName
											- contractBeneVOs[i].designation
											- contractBeneVOs[i].customerGender
											+ "'>"
											+ contractBeneVOs[i].customerName
											+ "</option>";
								}
							}
							$("#contractBeneInfoDataIdFordc").loadMyComboxOptions(
									options, '1');
							$("#contractBeneInfoDataIdFordc",
									navTab.getCurrentPanel()).prev().attr(
									"style", "width:55px;");
						},
					});
		}
	} else {
		$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).selectMyComBox("01");
		if (caseNo != "") {
			$.ajax({
						'type' : 'post',
						'url' : 'clm/sign/queryCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='
								+ caseNo,
						'datatype' : 'json',
						'async' : true,
						'success' : function(claimApplicantVO) {
							var claimApplicantVO = eval("(" + claimApplicantVO
									+ ")");
							$("#clmtName", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtName);
							$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).selectMyComBox("00");
							$("#clmtMp", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtMp);
							// changePhoneDataa($("#clmtMp" ,
							// navTab.getCurrentPanel())[0]);
							$("#clmtMail", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtMail);
							$("#provinceReportApplicantId",
									navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtProvince);
							$("#provinceReportApplicantId",
									navTab.getCurrentPanel())
									.prev()
									.val(
											claimApplicantVO.clmtProvince
													+ "-"
													+ claimApplicantVO.clmtProvinceName);
							applicantSSX(claimApplicantVO.clmtProvinceName,
									claimApplicantVO.clmtCity,
									claimApplicantVO.clmtDistreact,
									claimApplicantVO.clmtProvince);
							$("#clmtStreet", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtStreet);
							$("#clmtSexIdApplicantData",
									navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtSex);
							if (claimApplicantVO.peopleFlag == "1") {
								$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).selectMyComBox("00");
							} else {
								$("#peopleFlagId", navTab.getCurrentPanel())
										.val(claimApplicantVO.peopleFlag);
							}
						},
					});
		}
	}
}

// });

// 进行保存操作
function autoMatchClacLogClick(flg) {
	if (bs == 1) {
		next('12', 'addApplicantForm', $("#caseId", navTab.getCurrentPanel()).val());
		return;
	}
	// 至少有一条申请人信息
	var shulength = $("#register", navTab.getCurrentPanel()).find("tr").length;
	if (shulength < 1) {
		alertMsg.error("至少有一条申请人信息!");
		return;
	}
	//检验申请日期字段不能大于当前系统时间
	debugger
	var sysDate = $("#sysTimeId", navTab.getCurrentPanel()).val().replace(/-/g, '/');
	var applyDate = $("#applyDate",navTab.getCurrentPanel()).val().replace(/-/g, '/');
	if(applyDate > sysDate){
		alertMsg.error("申请日期不能大于当前日期，请重新输入！");
		// return;
	}
	//#98117 4.7 校验受托人
	var trusteeNameId = $("#trusteeNameId",navTab.getCurrentPanel()).val();
	var trusteeCertiType = $("#trusteeCertiType",navTab.getCurrentPanel()).val();
	var trusteeCertiCode = $("#trusteeCertiCode",navTab.getCurrentPanel()).val();
	var assigneeStartDate = $("#assigneeStartDate",navTab.getCurrentPanel()).val();
	var assigneeEndDate = $("#assigneeEndDate",navTab.getCurrentPanel()).val();
	if(trusteeNameId != "" || trusteeCertiType != "" || trusteeCertiCode != ""
		|| assigneeStartDate != "" || assigneeEndDate != ""){
		if(trusteeNameId == ""){
			alertMsg.error("受托人姓名不能为空");
			return;
		}
		if(trusteeCertiType == ""){
			alertMsg.error("受托人证件类型不能为空");
			return;
		}
		if(trusteeCertiCode == ""){
			alertMsg.error("受托人证件号码不能为空");
			return;
		}
		if(assigneeStartDate == ""){
			alertMsg.error("受托人证件有效起期不能为空");
			return;
		}
		if(assigneeEndDate == "" && !$("#assigneeEndDateCheckBoxId", navTab.getCurrentPanel()).is(':checked')){
			alertMsg.error("受托人证件有效止期不能为空");
			return;
		}
		if (trusteeCertiType == "e"&&$.trim(assigneeStartDate) != ""&&$.trim(assigneeEndDate) != ""&&!checkCertiDate(assigneeStartDate,assigneeEndDate)){
			return false;
		}
		//证件类型为外国人永久居住身份证，校验证件号码
		if(trusteeCertiType == "e"){
			if(!(trusteeCertiCode.length == 15 || trusteeCertiCode.length == 18)){
				alertMsg.error("外国人永久居留身份证位数应为15位或18位。");
				return false;
			}
			if(trusteeCertiCode.length == 15){
				var check = /^[A-Z]{3}(\d)(?!\1{11})[\d]{11}$/.test(trusteeCertiCode);
				if (!check){
					alertMsg.error("外国人永久居留身份证证件号码为15位的，前三位必须为大写字母，后12位数字不能全相同。");
					return false;
				}
			}
			if(trusteeCertiCode.length == 18){
				var birthdayNew = trusteeCertiCode.substring(6,14);
				var reg = new RegExp("^9.{17}$");
				var check = reg.test(trusteeCertiCode);
				if (!check){
					alertMsg.error("外国人永久居留身份证证件号码为18位的，首位必须是9。");
					return false;
				}
			}
		}
	}
	if(assigneeStartDate != "" && assigneeEndDate != ""){
		var startNum = parseInt(assigneeStartDate.replace(/-/g,''),10);
		var endNum = parseInt(assigneeEndDate.replace(/-/g,''),10);
		if(endNum < startNum){
			alertMsg.error("证件有效止期需大于证件有效起期，请重新录入！");
			return;
		}
	}
	// 校验受托人姓名录入合法性
	if ($("#trusteeNameId", navTab.getCurrentPanel()).val() != "") {
		if (!checkName($("#trusteeNameId", navTab.getCurrentPanel()))) {
			return;
		}
	}
	// 校验受托人证件号码
	if ($("#trusteeCertiCode", navTab.getCurrentPanel()).val() != "") {
		if($("#trusteeCertiType", navTab.getCurrentPanel()).val()=="h"
			|| $("#trusteeCertiType", navTab.getCurrentPanel()).val()=="b"
			|| $("#trusteeCertiType", navTab.getCurrentPanel()).val()=="i"
			|| $("#trusteeCertiType", navTab.getCurrentPanel()).val()=="d"){
			//HHH  随便一个 不走国籍的校验，受托没有国籍，但是其他检验要走
			if(!checkCertiNoAndNa($("#trusteeCertiType",navTab.getCurrentPanel()).val(),$("#trusteeCertiCode",navTab.getCurrentPanel()).val(),"HHH")){
				return;
			}
		}else{
			if (!checkCertiNo($("#trusteeCertiType", navTab.getCurrentPanel())
					.val(), $("#trusteeCertiCode", navTab.getCurrentPanel()).val())) {
				return;
			}
		}
	}
	// 校验诊断医生姓名录入合法性
	if ($("#doctorNameId", navTab.getCurrentPanel()).val() != "") {
		if (!checkName($("#doctorNameId", navTab.getCurrentPanel()))) {
			return;
		}
	}
	
	
	var doctorname;
	var doctordcode;
	var doctortrs=$('#addDoctorTbodyId tr', navTab.getCurrentPanel());
	var doctorLength=doctortrs.length;
	for(var i=0;i<doctorLength;i++){
		doctorname=doctortrs.eq(i).find("td:eq(4)").find("input").val();
		doctordcode=doctortrs.eq(i).find("td:eq(5)").find("select").val();
 
		var doctordcode1=doctortrs.eq(i).find("td:eq(5)").find("[name='claimDoctorVO.code']").prev("input").val();//校验录入的规则性
		if(doctorname!=""||doctordcode!=""){
			 
			if(doctorname==""){
				alertMsg.error("请填写医生姓名！");
				return ;
			}
			if(doctordcode==""){
				alertMsg.error("请填写归属科室！");
				return ;
			}
			if(doctordcode1==""){
				alertMsg.error("请填写归属科室！");
				return ;
			}
			//校验归属科室录入正确性
			//if(doctordcode1!=""){
			//	if(!checkClaimData(doctordcode1)){
			//		return ;
			//	}
			//}
		}
	}
	
	
	// 校验受托人手机号码
	var assignMobile = $("#assignMobile", navTab.getCurrentPanel()).val();
	if (assignMobile != "" && assignMobile.length < 11) {
		alertMsg.error("受托人手机号不正确");
		return;
	}
	// 理赔类型不能为空 校验
	var trnum = 0;
	$("#tbodyIds tr").each(function() {
		trnum++;
	});
	if (trnum == 0) {
		alertMsg.error("理赔类型不能为空，请添加理赔类型！");
		return false;
	}
	if ($("#claimType", navTab.getCurrentPanel()).val() == "01"
			&& $("#clmtInsurRelationId", navTab.getCurrentPanel()).val() == "00") {
		alertMsg.error("当理赔类型为身故时，申请人与出险人的关系不能为本人。");
		return;
	}
	var checkresult = $("#checkresultCode", navTab.getCurrentPanel()).val();
	
	// 鉴定信息校验
	var identifyinfoRows = $('#addIdentifyinfoTbodyId tr', navTab.getCurrentPanel());
	for (var i=0; i<identifyinfoRows.length; i++) {
		var currentRow = identifyinfoRows.eq(i);
		var accreditingBodyName = currentRow.find("td:eq(1) input").val();
		var lient = currentRow.find("td:eq(3) input").val();
		var entrustingAppraisalMatter = currentRow.find("td:eq(4) input").val();
		
		if (accreditingBodyName != null && accreditingBodyName !== "" && accreditingBodyName.length >100) {
			alertMsg.error("“鉴定机构名称”最大支持输入100个字符！");
			return ;
		}
		if (lient != null && lient !== "" && lient.length >100) {
			alertMsg.error("“委托人/委托单位”最大支持输入100个字符！");
			return ;
		}
		if (entrustingAppraisalMatter != null && entrustingAppraisalMatter !== "" && entrustingAppraisalMatter.length >200) {
			alertMsg.error("“委托鉴定事项”最大支持输入200个字符！");
			return ;
		}
	}
	$("#Forms", navTab.getCurrentPanel()).attr(
			"action",
			'clm/register/updateDataCollents_CLM_dataCollectAction.action?flg='
			+ flg+'&accident.checkResult='+checkresult);

	var accReasonId = $("select#accReasonId", navTab.getCurrentPanel()).val();
	var clmJson = new Array();
	var brand = [ "claimType", "claimDate", "accReason" ];
	clmJson += '"' + "[";

	$("#tbodyIds tr", navTab.getCurrentPanel())
			.each(
					function() {
						clmJson += "{";
						$(this)
								.children("td:lt(3)")
								.each(
										function(i) {
											if (i == 0) {
												var Value = $(this)
														.find(
																"select[name='claimSubCaseVO.claimType']")
														.val();
												clmJson = clmJson + "'"
														+ brand[i] + "'" + ":'"
														+ Value + "',";
											}
											if (i == 1) {
												var Value = $(this).find(
														"input#accTime").val();
												clmJson = clmJson + "'"
														+ brand[i] + "'" + ":'"
														+ Value + "',";
											}
											if (i == 2) {
												clmJson = clmJson + "'"
														+ brand[i] + "'" + ":'"
														+ accReasonId + "',";
											}

										});
						clmJson = clmJson.substring(0, clmJson.length - 1);
						clmJson += "},";
					});
	clmJson = clmJson.substring(1, clmJson.length - 1);
	clmJson += "]";
	$("#tclaimJson", navTab.getCurrentPanel()).val(clmJson);
	//增加的重大疾病校验
	var clmJsonList = eval("(" + clmJson + ")");
	 for(var i = 0;i<clmJsonList.length;i++){
//		 if(clmJsonList[i].claimType=="03"){
//			 if($("#seriousDiseaseCodeData",navTab.getCurrentPanel()).val() == null|| $("#seriousDiseaseCodeData",navTab.getCurrentPanel()).val() == ""){
//				 alertMsg.error("当理赔类型为重大疾病时，重大疾病代码必填");
//				 return false;
//			 }
//		 }
		var qdjb = $("#seriousDiseaseData1", navTab.getCurrentPanel()).val();// 轻度疾病
		var zhongjb = $("#seriousDiseaseData2", navTab.getCurrentPanel()).val();// 中度疾病
		var zdjb = $("#seriousDiseaseData", navTab.getCurrentPanel()).val();// 重大疾病
		if (clmJsonList[i].claimType == "03") {
			if ((qdjb == null && zhongjb == null && zdjb == null)
					|| (qdjb == "" && zhongjb == "" && zdjb == "")) {
				alertMsg.error("当理赔类型为重大疾病时，重大疾病代码必填");
				return false;
			}
		}
		if ((qdjb != "" && zhongjb != "") || (qdjb != "" && zdjb != "")
				|| (zhongjb != "" && zdjb != "")) {
			alertMsg.error("轻度疾病、中度疾病、重大疾病只能录入其中一个，请重新录入。");
			return false;
		}
	 }
	// 用于校验事故日期和出险日期
	var accTimeAndaccidentTime = "NO";
	// 用于校验出险日期和系统日期
	var accTimeAndsystemDate = "NO";
	// 获取报案日期
	var rptrTime = $("#acceptTimeId", navTab.getCurrentPanel()).val().replace(/-/g, '/');
	var rptrTimes = rptrTime.split("/");
	var dt1 = new Date();
	dt1.setFullYear(rptrTimes[0]);
	dt1.setMonth(rptrTimes[1] - 1);
	dt1.setDate(rptrTimes[2]);
	// 获取事故日期
	var accidentTime = $("#accidentTimeIds", navTab.getCurrentPanel()).val().replace(/-/g, '/');
	var accidentTimes = accidentTime.split("/");
	var dt2 = new Date();
	dt2.setFullYear(accidentTimes[0]);
	dt2.setMonth(accidentTimes[1] - 1);
	dt2.setDate(accidentTimes[2]);

	var dif = dt1.getTime() - dt2.getTime();
	var days = dif / (24 * 60 * 60 * 1000);
	// 获取系统日期
	var myDate = new Date();
	var subStrMyDate = myDate.getYear() + "-"
			+ addZero(parseInt(myDate.getMonth()) + 1) + "-"
			+ addZero(parseInt(myDate.getDate()));
	var systemDate = subStrMyDate.replaceAll("-", "/");
	var sysTime = $("#sysTimeId", navTab.getCurrentPanel()).val().replace(/-/g, '/');
	// 获取出险日期
	var inputs = $("input#accTime", navTab.getCurrentPanel());
	for (var i = 0; i < inputs.length; i++) {
		if (inputs[i].value == "") {
			alertMsg.error("出险日期不能为空!");
			return;
		} else {
			var accTime = inputs[i].value.replace(/-/g, '/');
			for (var j = 0; j < i; j++) {
				var accTimeNext = inputs[j].value.replace(/-/g, '/');
				if (accTime > accTimeNext) {
					var temp = inputs[j];
					inputs[j] = inputs[i];
					inputs[i] = temp;
				}
			}
			if (accTime > sysTime) {// 如果出险日期大于系统日期，给出错误提示信息。
				accTimeAndsystemDate = "YES";
			}
			if (accidentTime > accTime) {// 如果事故日期大于出险日期，给出错误提示信息。

				accTimeAndaccidentTime = "YES";
			}
		}
	}
	// 页面必填项js校验
	if ($("#accidentTimeIds", navTab.getCurrentPanel()).val() == null
			|| $("#accidentTimeIds", navTab.getCurrentPanel()).val() == "") {
		alertMsg.error("事故日期不能为空!");
		$("#alertMsgBox .toolBar .button").off().on("click", function() {
			$("#accidentTimeIds", navTab.getCurrentPanel()).focus();
		});
		return;
	}
	if (accTimeAndsystemDate == "YES") {
		alertMsg.error("出险日期不能晚于当前日期，请重新输入。");
		return;
	}
	if (accTimeAndaccidentTime == "YES") {
		alertMsg.error("出险日期不能早于事故日期，请重新输入。");
		return;
	}
	if ($("#accReasonId", navTab.getCurrentPanel()).val() == null
			|| $("#accReasonId", navTab.getCurrentPanel()).val() == ""
			|| $("#accReasonId", navTab.getCurrentPanel()).prev().attr("value") == "") {
		alertMsg.error("出险原因不能为空!");
		$("#alertMsgBox .toolBar .button").off().on("click", function() {
			$("#accReasonId", navTab.getCurrentPanel()).focus();
		});
		return;
	}
	if ($("#accReasonId", navTab.getCurrentPanel()).val() == "2"
			|| $("#accReasonId", navTab.getCurrentPanel()).val() == "3") {
		if ($("#accidentDetail", navTab.getCurrentPanel()).val() == null
				|| $("#accidentDetail", navTab.getCurrentPanel()).val() == "") {
			alertMsg.error("当出险原因为意外或者工伤时，意外细节必填。");
			$("#alertMsgBox .toolBar .button").off().on("click", function() {
				$("#accidentDetail", navTab.getCurrentPanel()).focus();
			});
			return;
		}
	}
	//国家校验
	var contryStr = $("#contryDatatId", navTab.getCurrentPanel()).prev().val();
		var contryStrArray = contryStr.split("-");
		
	if(contryStrArray[1]==null){
		alertMsg.error("出险地点国家字段录入不合法!");
		return false;
	}
	if(contryStrArray[1]!=null){
		if((!/^[A-Z]+$/.test(contryStrArray[0])&&contryStr.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(contryStrArray[1]))||(contryStr.indexOf("-") == -1)||contryStr=="-国家"){
			alertMsg.error("出险地点国家字段录入不合法!");
			return false;
		} 
	}
	// 页面必填项js校验
	if ($("#contryDatatId", navTab.getCurrentPanel()).val() == "CHN") {
		var provinceStr = $("#provinceReportId", navTab.getCurrentPanel()).prev().val();
		var provinceStrArray = provinceStr.split("-");
		var cityStr = $("#cityReportId", navTab.getCurrentPanel()).prev().val();
		var cityStrArray = cityStr.split("-");
		var areaStr = $("#areaReportId", navTab.getCurrentPanel()).prev().val();
		var areaStrArray = areaStr.split("-");
		//下拉框校验
		if(provinceStrArray[1]==null){
			alertMsg.error("出险地点省字段录入不合法!");
			return false;
		}
		if(cityStrArray[1]==null){
			alertMsg.error("出险地点市字段录入不合法!");
			return false;
		}
		if(areaStrArray[1]==null){
			alertMsg.error("出险地点县字段录入不合法!");
			return false;
		}
		
		if ($("#provinceReportId", navTab.getCurrentPanel()).prev().val() == "-省/直辖市"
				|| $("#provinceReportId", navTab.getCurrentPanel()).prev()
						.val() == "") {
			alertMsg.error("出险地点省不能为空!");
			return false;
		}
		if ($("#cityReportId", navTab.getCurrentPanel()).prev().val() == "-市"
				|| $("#cityReportId", navTab.getCurrentPanel()).prev().val() == "") {
			alertMsg.error("出险地点市不能为空!");
			return false;
		}
		if ($("#areaReportId", navTab.getCurrentPanel()).prev().val() == ""
				|| $("#areaReportId", navTab.getCurrentPanel()).prev().val() == "-区/县") {
			alertMsg.error("出险地点县不能为空!");
			return false;
		}
		if((!/^[0-9]*$/.test(provinceStrArray[0])&&provinceStr.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(provinceStrArray[1]))||(provinceStr.indexOf("-") == -1)){
			alertMsg.error("出险地点省字段录入不合法!");
			return false;
		}
		if((!/^[0-9]*$/.test(cityStrArray[0])&&cityStr.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(cityStrArray[1]))||(cityStr.indexOf("-") == -1)){
			alertMsg.error("出险地点市字段录入不合法!");
			return false;
		}
		if((!/^[0-9]*$/.test(areaStrArray[0])&&areaStr.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(areaStrArray[1]))||(areaStr.indexOf("-") == -1)){
			alertMsg.error("出险地点县字段录入不合法!");
			return false;
		}
	}
	if ($("#accidentDescId", navTab.getCurrentPanel()).val() == null
			|| $("#accidentDescId", navTab.getCurrentPanel()).val() == "") {
		alertMsg.error("事故描述不能为空!");
		$("#alertMsgBox .toolBar .button").off().on("click", function() {
			$("#accidentDescId", navTab.getCurrentPanel()).focus();
		});
		return false;
	}
	var flag = false;
	var trs = $("#tbodysIdData", navTab.getCurrentPanel()).find("tr");
	var assresult;
	var assresult2;
	if (trs.length > 0) {
		var resultarr=new Array(trs.length);
		for (var i = 0; i < trs.length; i++) {
			assresult=$("#tbodysIdData", navTab.getCurrentPanel()).find(
					"tr:eq(" + i + ")").find("td:eq(1)").find("input:eq(2)")
					.val();
			assresult2=$("#tbodysIdData", navTab.getCurrentPanel()).find(
					"tr:eq(" + i + ")").find("td:eq(2)").find("input:eq(2)")
					.val();
			if (assresult == "") {
				alertMsg.error("出险结果1不能为空!");
				return false;
			}else{
				resultarr[i]=assresult+assresult2;
			}
		}
		//判断出现结果重复
		var nary = resultarr.sort();
		for(var i = 0; i < nary.length - 1; i++)
		{
		   if (nary[i] == nary[i+1])
		    {
			   alertMsg.error("出险结果存在重复!");
				return false;
		    }
		}
	} else {
		alertMsg.error("出险结果1不能为空!");
		return false;
	}
	if (flag) {
		return;
	}
	if (days > 10) {
		alertMsg.warn("您未在十日内报案，请选择保存或取消！");
		$("#Forms", navTab.getCurrentPanel()).submit();
//		alertMsg.confirm("您未在十日内报案，请选择保存或取消！", {
//			okCall : function() {
//				$("#Forms", navTab.getCurrentPanel()).submit();
//				// var caseId = $("#claimCaseVOcASEID", navTab.getCurrentPanel()).val();
//				// $.ajax({
//				// 'url':'clm/register/checkClaimTypeAndBusi_CLM_dataCollectAction.action?claimCaseVO.caseId='+caseId+'&claimSubCaseVO.clmJson='+clmJson,
//				// 'type':'post',
//				// 'success':function(data){
//				// var data = eval("(" + data + ")");
//				// if(data.status == "10" ){
//				// alertMsg.confirm("<理赔类型>没有匹配的责任，请检查！", {
//				// okCall : function() {
//				// $("#Forms").submit();
//				// }
//				// });
//				// } else {
//				// $("#Forms").submit();
//				// }
//				// },
//				//							
//				// });
//			}
//		});
		return;
	} else {
		$("#Forms", navTab.getCurrentPanel()).submit();
		// var caseId = $("#claimCaseVOcASEID", navTab.getCurrentPanel()).val();
		// $.ajax({
		// 'url':'clm/register/checkClaimTypeAndBusi_CLM_dataCollectAction.action?claimCaseVO.caseId='+caseId+'&claimSubCaseVO.clmJson='+clmJson,
		// 'type':'post',
		// 'success':function(data){
		// var data = eval("(" + data + ")");
		// if(data.status == "10" ){
		// alertMsg.confirm("<理赔类型>没有匹配的责任，请检查！", {
		// okCall : function() {
		// $("#Forms").submit();
		// }
		// });
		// } else {
		// $("#Forms").submit();
		// }
		// },
		//					
		// });
	}
}

//初始化时加载出险年龄
$(function(){
	$("#tbodyIds",navTab.getCurrentPanel()).find("#accTime").each(function(){
		countAgeRegis(this);
	});
});

// 获取出险年龄
function countAgeRegis(obj) {
	var now = $(obj).val();
	if (now.length != 0) {
		var dateNow = new Date(now.replace(/-/g, '/'));
		var birth = $("#accBirthIds", navTab.getCurrentPanel()).val();
		var dateBirth = new Date(birth.replace(/-/g, '/'));
		var age = 0;
		var years = dateNow.getFullYear() - dateBirth.getFullYear();
		var months = dateNow.getMonth() - dateBirth.getMonth();
		var days = dateNow.getDate() - dateBirth.getDate();
		if (months > 0) {
			age = years;
		} else if (months < 0) {
			age = years - 1;
		} else if (days >= 0) {
			age = years;
		} else {
			age = years - 1;
		}
		$(obj).parent().next().find('input').val(age);
	}
}

function myOptionDatacollect(k) {
	var accidentTimeId = $("#accidentTimeIds", navTab.getCurrentPanel()).val();
	var accReasonId = $("#accReasonId", navTab.getCurrentPanel()).val();
	var signDistreact = $("#areaReportId", navTab.getCurrentPanel()).val();
	if (signDistreact == null) {
		signDistreact = "";
	}
	var insuredId = $("#insuredIdD", navTab.getCurrentPanel()).val();
	var caseNo = $("#claimCaseNo", navTab.getCurrentPanel()).val();
	if ($(k).attr("href").indexOf("?") > 0) {
		$(k).attr("href",
				$(k).attr("href").substring(0, $(k).attr("href").indexOf("?")));
		$(k).attr(
				"href",
				$(k).attr("href") + "?claimAccidentVO.accDate="
						+ accidentTimeId + "&claimAccidentVO.accReason="
						+ accReasonId + "&claimAccidentVO.accStreet="
						+ signDistreact + "&claimAccidentVO.caseNo=" + caseNo
						+ "&claimAccidentVO.insuredId=" + insuredId);
	} else {
		$(k).attr(
				"href",
				$(k).attr("href") + "?claimAccidentVO.accDate="
						+ accidentTimeId + "&claimAccidentVO.accReason="
						+ accReasonId + "&claimAccidentVO.accStreet="
						+ signDistreact + "&claimAccidentVO.caseNo=" + caseNo
						+ "&claimAccidentVO.insuredId=" + insuredId);
	}
}

// 点击申请人的红叉子时进行删除该行
/*
 * function emty(del){ $(del).parent().parent().remove();
 *  }
 */
// 增加申请人
function addApplicant1() {
	if ($("#peopleFlagId", navTab.getCurrentPanel()).val() == "0") {
		if ($("#clmtInsurRelationFordc", navTab.getCurrentPanel()).val == "00") {
			alertMsg.error("出险人为投保人，与被保险人的关系不能选择本人。");
			return;
		}
	}
	if ($("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).val() != "01") {
		if ($("#clmtInsurRelationFordc", navTab.getCurrentPanel()).val() == "00") {
			alertMsg.error("申请人属性不是本人的时候，不能选择与被保险人关于为本人。");
			return;
		}
	}
	if ($("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).val() == "01") {
		if ($("#clmtInsurRelationFordc", navTab.getCurrentPanel()).val() != "00") {
			alertMsg.error("申请人属性是本人的时候，只能选择与被保险人关于为本人。");
			return;
		}
	}
	if($("#claimType",navTab.getCurrentPanel()).val() == "01" && $("#clmtInsurRelationFordc",navTab.getCurrentPanel()).val() == "00"){		
		alertMsg.error("当理赔类型为身故时，申请人与出险人关系不能为本人。");
		return;
	}
	var name = $("#clmtName", navTab.getCurrentPanel()).val();
	var listId = $("#listId", navTab.getCurrentPanel()).val();
	var clmtSex = $("#clmtSexIdApplicantData", navTab.getCurrentPanel()).val();
	var trs = $("#register", navTab.getCurrentPanel()).find("tr");
	if (listId == "") {
		for (var i = 0; i < trs.length; i++) {
			var nameOld = $("#register", navTab.getCurrentPanel()).find(
					"tr:eq(" + i + ")").find("td:eq(2)").text();
			if ($.trim(nameOld) == $.trim(name)) {
				alertMsg.info("申请人的姓名不能重复添加!");
				return;
			}
		}
	}
	var InsurRelation = $("#clmtInsurRelationFordc", navTab.getCurrentPanel()).val();
	var clmtPropertyCodeDataId = $("#clmtPropertyCodeDataIdFordc",
			navTab.getCurrentPanel()).val();
	var clmtMp = $("#clmtMp", navTab.getCurrentPanel()).val();
	var clmtCity = $("#cityReportApplicantId", navTab.getCurrentPanel()).prev()
			.val();
	var area = $("#areaReportApplicantId", navTab.getCurrentPanel()).prev()
			.val();
	var areas = area.split("-");
	var clmtCitys = clmtCity.split("-");
	var province = $("#provinceReportApplicantId", navTab.getCurrentPanel())
			.prev().val();
	var clmtStreet = $("#clmtStreet", navTab.getCurrentPanel()).val();

	if ($.trim(name) == "") {
		alertMsg.info("申请人姓名必填");
		return;
	} else {
		if (!checkName($("#clmtName"))) {
			return;
		}
	}
	if ($.trim(InsurRelation) == "") {
		alertMsg.info("与被保险人关系必填");
		return;
	}
	if ($.trim(clmtMp) == "") {
		alertMsg.info("申请人电话必填");
		return;
	}
	if (clmtMp.length < 11) {
		alertMsg.error("申请人手机号不正确");
		return;
	}
	
	var provinceStr1 = $("#provinceReportApplicantId", navTab.getCurrentPanel()).prev().val();
	var provinceStrArray1 = provinceStr1.split("-");
	var cityStr1 = $("#cityReportApplicantId", navTab.getCurrentPanel()).prev().val();
	var cityStrArray1 = cityStr1.split("-");
	var areaStr1 = $("#areaReportApplicantId", navTab.getCurrentPanel()).prev().val();
	var areaStrArray1 = areaStr1.split("-");
	//下拉框校验
	if(provinceStrArray1[1]==null){
		alertMsg.error("出险地点省字段录入不合法!");
		return false;
	}
	if(cityStrArray1[1]==null){
		alertMsg.error("出险地点市字段录入不合法!");
		return false;
	}
	if(areaStrArray1[1]==null){
		alertMsg.error("出险地点县字段录入不合法!");
		return false;
	}
	if((!/^[0-9]*$/.test(provinceStrArray1[0])&&provinceStr1.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(provinceStrArray1[1]))||(provinceStr1.indexOf("-") == -1)){
		alertMsg.error("出险地点省字段录入不合法!");
		return false;
	} else if((!/^[0-9]*$/.test(cityStrArray1[0])&&cityStr1.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(cityStrArray1[1]))||(cityStr1.indexOf("-") == -1)){
		alertMsg.error("出险地点市字段录入不合法!");
		return false;
	} else if((!/^[0-9]*$/.test(areaStrArray1[0])&&areaStr1.indexOf("-") != -1&&!/^[\u4e00-\u9fa5]+$/.test(areaStrArray1[1]))||(areaStr1.indexOf("-") == -1)){
		alertMsg.error("出险地点县字段录入不合法!");
		return false;
	}
	
	if ($.trim(province) == "" || $.trim(province) == "-省/直辖市") {
		alertMsg.info("请下拉选择省市县地址信息！");
		return;
	}
	if ($.trim(clmtCity) == "" || $.trim(clmtCity) == "-市") {
		alertMsg.info("市不能为空!");
		return;
	}
	if ($.trim(area) == "" || $.trim(area) == "-区/县") {
		alertMsg.info("区/县不能为空!");
		return;
	}
	if ($.trim(clmtStreet) == "") {
		alertMsg.info("乡镇/街道不能为空!");
		return;
	} else {
		if (!checkStreet(clmtStreet)) {
			return;
		}
	}
	var caseId = $("#claimCaseVOcASEID", navTab.getCurrentPanel()).val();
	$.ajax({
				'url' : 'clm/register/saveOrinsertApplicant_CLM_dataCollectAction.action?caseId='
						+ caseId,
				'type' : 'post',
				'data' : {
					'applicant.listId' : $("#listId", navTab.getCurrentPanel())
							.val(),
					'applicant.clmtName' : $("#clmtName",
							navTab.getCurrentPanel()).val(),
					'applicant.clmtInsurRelation' : $("#clmtInsurRelationFordc",
							navTab.getCurrentPanel()).val(),
					'applicant.clmtMp' : $("#clmtMp", navTab.getCurrentPanel())
							.val(),
					'applicant.clmtMail' : $("#clmtMail",
							navTab.getCurrentPanel()).val(),
					'applicant.clmtProvince' : $("#provinceReportApplicantId",
							navTab.getCurrentPanel()).val(),
					'applicant.clmtCity' : clmtCitys[0],
					'applicant.clmtDistreact' : areas[0],
					'applicant.clmtStreet' : $("#clmtStreet",
							navTab.getCurrentPanel()).val(),
					'applicant.caseId' : $("#claimCaseVOcASEID",
							navTab.getCurrentPanel()).val(),
					'applicant.clmtPropertyCode' : $("#clmtPropertyCodeDataIdFordc",
							navTab.getCurrentPanel()).val(),
					'applicant.clmtSex' : clmtSex
				},
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					if (data.statusCode == '300') {
						alertMsg.error(data.message);
						return;
					}
					$("#register", navTab.getCurrentPanel()).empty();
					var tr_all = '';
					for (var i = 0; i < data.length; i++) {
						var number = parseInt(i);
						var amount = number + 1;
						var tr = "<tr>"
								+ "<td><input type='radio' name='123' id='radio"
								+ data[i].listId
								+ "' onclick='findMessage("
								+ data[i].listId
								+ ")' value='0'/></td>"
								+ "<td><div align='center'>"
								+ amount
								+ "</div></td>"
								+ "<td><div align='center'>"
								+ data[i].clmtName
								+ "</div></td>"
								+ "<td><div align='center'>"
								+ data[i].clmtInsurRelationChinese
								+ "</div></td>"
								+ "<td><div align='center'>"
								+ data[i].clmtMp
								+ "</div></td>"
								+ "<td><div align='center'>"
								+ data[i].clmtMail
								+ "</div></td>"
								+ "<td><a class='btnDel'  href='javascript:;' onclick=todeleteApplicant("
								+ data[i].listId
								+ ") ref='current'>删除</a></td>";
						tr += "</tr>";
						tr_all = tr_all + tr;

					}
					$("#register", navTab.getCurrentPanel()).append(tr_all);

					$("#appliacntMessage", navTab.getCurrentPanel()).find(
							"input").val(null);
					$("#appliacntMessage", navTab.getCurrentPanel()).find(
							"select").val(null);

					// $("#op_combox_clmtProvince",navTab.getCurrentPanel()).find("a[value='"+110000+"']").click();
					// $("#op_combox_clmtCity",navTab.getCurrentPanel()).find("a[value='"+110100+"']").click();
					// $("#op_combox_clmtDistreact",navTab.getCurrentPanel()).find("a[value='"+110101+"']").click();
					// 保存后 隐藏 申请人信息录入 输入区域
					$(".main_heading", navTab.getCurrentPanel()).next(
							".main_lptwo").animate({
						height : 'toggle',
						opacity : 'toggle'
					});
					$(".main_heading", navTab.getCurrentPanel()).find("b")
							.removeClass("maim_lpask")
							.addClass("maim_lpbubble");
				},

			});

	// $("#applicantPanel", navTab.getCurrentPanel()).css("display","none");
	// $("#showButtonId",navTab.getCurrentPanel()).val("+");
};

// 为元素中的类选择器增加单机事件
$(function() {
	$(".btnDel").live("click", function() {
		var $tr = $(this).parent().parent();
		// var cla=$tr.parent().attr("class");
		$tr.remove();
		updateRowNum();
	});
});
function updateRowNum() {
	var row1 = $(".tbody", navTab.getCurrentPanel()).find("tr");
	var len1 = row1.length;
	for (var i = 0; i < len1; i++) {
		row1[i].cells[1].innerHTML = i + 1;
	}
}
/*
 * function updateRowNum(cla){ var row1="",b=0; if(cla=="tbody"){ row1 =
 * $(".tbody").find("tr"); b=1; }else if(cla=="tbodys"){ row1 =
 * $(".tbodys").find("tr"); } var len1 = row1.length; for(var i = 0;i<len1;i++){
 * row1[i].cells[b].innerHTML = i+1; } } function deleteRowNums(){ var row1 =
 * $(".tbodys").find("tr"); var len1 = row1.length; for(var i = 0;i<len1;i++){
 * row1[i].cells[0].innerHTML = i+1; } }
 */
// 查询申请人信息
function findMessage(obj) {
	// showPanelId();
	// $("#applicantPanel", navTab.getCurrentPanel()).css("display","block");
	// $("#showButtonId",navTab.getCurrentPanel()).val("-");
	if ($(".main_heading", navTab.getCurrentPanel()).find("b").attr("class") == "maim_lpbubble") {
		$(".main_heading", navTab.getCurrentPanel()).next(".main_lptwo")
				.animate({
					height : 'toggle',
					opacity : 'toggle'
				}, "slow");
		$(".main_heading", navTab.getCurrentPanel()).find("b").removeClass(
				"maim_lpbubble").addClass("maim_lpask");
	}
	var val = obj;
	$("#radio" + obj).attr("value", "1");
	$.ajax({
				'url' : 'clm/register/queryApplicant_CLM_dataCollectAction.action?listId='
						+ val,
				'type' : 'post',
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#clmtName", navTab.getCurrentPanel()).val(data.clmtName);
					$("#clmtInsurRelationFordc").selectMyComBox(
							data.clmtInsurRelation);
					$("#clmtMp", navTab.getCurrentPanel()).val(data.clmtMp);
					// changePhoneDataa($("#clmtMp",navTab.getCurrentPanel())[0]);
					$("#clmtStreet", navTab.getCurrentPanel()).val(
							data.clmtStreet);
					$("#clmtMail", navTab.getCurrentPanel()).val(data.clmtMail);
					$("#listId", navTab.getCurrentPanel()).val(data.listId);
					if (data.clmtSex == '0') {
						data.clmtSex = null;
					}
					$("#clmtSexIdApplicantData", navTab.getCurrentPanel()).val(
							data.clmtSex);
					var clmtInsurRelationName = "";
					if (data.clmtPropertyCode == "01") {
						clmtInsurRelationName = "本人";
						$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel())
								.prev().removeAttr("style");
						$("#clmtInsurRelationFordc", navTab.getCurrentPanel())
								.prev().attr("readonly", "readonly");
						$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).setMyComboxHide(true);
					} else if (data.clmtPropertyCode == "02") {
						$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).setMyComboxHide(false);
						clmtInsurRelationName = "指定受益人";
					} else if (data.clmtPropertyCode == "03") {
						$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel())
								.prev().removeAttr("style");
						clmtInsurRelationName = "法定受益人";
						$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).setMyComboxHide(true);
					} else if (data.clmtPropertyCode == "04") {
						$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel())
								.prev().removeAttr("style");
						clmtInsurRelationName = "其他";
						$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).setMyComboxHide(true);
					} else {
						$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel())
								.prev().removeAttr("style");
						clmtInsurRelationName = "";
						$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).setMyComboxHide(true);
					}
					$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).selectMyComBox(
							data.clmtPropertyCode);
					$("#cityReportApplicantId", navTab.getCurrentPanel()).prev().val(
							"");
					$("#areaReportApplicantId", navTab.getCurrentPanel()).prev().val(
							"");
					applicantSSX(data.clmtProvinceCode, data.clmtCity,
							data.clmtDistreact, data.clmtProvince);
				}
			});
}
function applicantSSX(clmtProvinceDataName, clmtCityData, clmtDistreactData,
		clmtProvince) {
	// 页面初始化的时候为申请人中的省市县赋值
	// 获取申请人中的省市县的值
	var cityReportApplicantId = $("#cityReportApplicantId", navTab
			.getCurrentPanel());
	var areaReportApplicantId = $("#areaReportApplicantId", navTab
			.getCurrentPanel());
	if (caseId != "") {
		$("#provinceReportApplicantId", navTab.getCurrentPanel()).prev().val(
				clmtProvince + "-" + clmtProvinceDataName);
		$("#provinceReportApplicantId", navTab.getCurrentPanel()).val(
				clmtProvince);
		if (clmtProvince != "") {
			// 根据省获取市。为市赋值。
			$.ajax({
						'type' : 'post',
						'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
								+ clmtProvince,
						'datatype' : 'json',
						'async' : true,
						'success' : function(data) {
							var data = eval("(" + data + ")");
							$("#cityReportApplicantId",
									navTab.getCurrentPanel()).empty();
							$("<option value=''>市</option>").appendTo(
									cityReportApplicantId);
							for (var i = 0; i < data.length; i++) {
								if (data[i].code == clmtCityData) {
									$("#cityReportApplicantId",
											navTab.getCurrentPanel()).prev()
											.val(
													data[i].code + "-"
															+ data[i].name);
									$("#cityReportApplicantId",
											navTab.getCurrentPanel()).val(
											data[i].code);
									$(
											"<option value='" + clmtCityData
													+ "' class = '"
													+ data[i].name + "'>"
													+ data[i].name
													+ "</option>").appendTo(
											cityReportApplicantId);
								}
							}
							for (var i = 0; i < data.length; i++) {
								if (data[i].code != clmtCityData) {
									var option1 = "<option value='"
											+ data[i].code + "'   class='"
											+ data[i].name + "' >"
											+ data[i].name + "</option>";
									$(option1).appendTo(cityReportApplicantId);
								}
							}
						},
					});
			// 获取市为县赋值
			$.ajax({
						'type' : 'post',
						'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
								+ clmtCityData,
						'datatype' : 'json',
						'async' : true,
						'success' : function(data) {
							var data = eval("(" + data + ")");
							$("#areaReportApplicantId",
									navTab.getCurrentPanel()).empty();
							$("<option value=''>区/县</option>").appendTo(
									areaReportApplicantId);
							for (var i = 0; i < data.length; i++) {
								if (data[i].code == clmtDistreactData) {
									$("#areaReportApplicantId",
											navTab.getCurrentPanel()).prev()
											.val(
													data[i].code + "-"
															+ data[i].name);
									$("#areaReportApplicantId",
											navTab.getCurrentPanel()).val(
											data[i].code);
									$(
											"<option value='"
													+ clmtDistreactData
													+ "' class = '"
													+ data[i].name + "'>"
													+ data[i].name
													+ "</option>").appendTo(
											areaReportApplicantId);
								}
							}
							for (var i = 0; i < data.length; i++) {
								if (data[i].code != clmtDistreactData) {
									var option1 = "<option value='"
											+ data[i].code + "'   class='"
											+ data[i].name + "' >"
											+ data[i].name + "</option>";
									$(option1).appendTo(areaReportApplicantId);
								}
							}
						},
					});
			$("#cityReportApplicantId", navTab.getCurrentPanel())[0]
					.fireEvent("onchange");
			// setTimeOut(0.1);
			$("#areaReportApplicantId", navTab.getCurrentPanel())[0]
					.fireEvent("onchange");
		}
	} else { // 初始化上来的时候默认为北京市市辖区东城区。
		var province = "110000";
		var city = "110100";
		var area = "110101";
		$("#provinceReportId", navTab.getCurrentPanel()).prev().val(province);
		claimFireEvent($("#provinceReportId", navTab.getCurrentPanel()));
		// 根据省获取市。为市赋值。
		$.ajax({
					'type' : 'post',
					'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
							+ province,
					'datatype' : 'json',
					'async' : true,
					'success' : function(data) {
						var data = eval("(" + data + ")");
						$("#cityReportApplicantId", navTab.getCurrentPanel())
								.empty();
						$("<option value=''>市</option>").appendTo(
								cityReportApplicantId);
						for (var i = 0; i < data.length; i++) {
							if (data[i].code == city) {
								$("#cityReportApplicantId",
										navTab.getCurrentPanel()).prev().val(
										data[i].code + "-" + data[i].name);
								$("#cityReportApplicantId",
										navTab.getCurrentPanel()).val(
										data[i].code);
								$(
										"<option value='" + city
												+ "' class = '" + data[i].name
												+ "'>" + data[i].name
												+ "</option>").appendTo(
										cityReportApplicantId);
							}
						}
						for (var i = 0; i < data.length; i++) {
							if (data[i].code != city) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$(option1).appendTo(cityReportApplicantId);
							}
						}
					},
				});
		// 获取市为县赋值
		$.ajax({
					'type' : 'post',
					'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
							+ city,
					'datatype' : 'json',
					'async' : true,
					'success' : function(data) {
						var data = eval("(" + data + ")");
						$("#areaReportApplicantId", navTab.getCurrentPanel())
								.empty();
						$("<option value=''>区/县</option>").appendTo(
								areaReportApplicantId);
						for (var i = 0; i < data.length; i++) {
							if (data[i].code == area) {
								$("#areaReportApplicantId",
										navTab.getCurrentPanel()).prev().val(
										data[i].code + "-" + data[i].name);
								$("#areaReportApplicantId",
										navTab.getCurrentPanel()).val(
										data[i].code);
								$(
										"<option value='" + area
												+ "' class = '" + data[i].name
												+ "'>" + data[i].name
												+ "</option>").appendTo(
										areaReportApplicantId);
							}
						}
						for (var i = 0; i < data.length; i++) {
							if (data[i].code != area) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$(option1).appendTo(areaReportApplicantId);
							}
						}
					},
				});
		$("#cityReportApplicantId", navTab.getCurrentPanel())[0]
				.fireEvent("onchange");
		// setTimeOut(0.1);
		$("#areaReportApplicantId", navTab.getCurrentPanel())[0]
				.fireEvent("onchange");
	}
}

// 删除申请人信息
function todeleteApplicant(listid) {
	var val = listid;
	$("#radio" + listid).parent().parent().remove();
	var check = $("#radio" + listid).attr("checked");
	if (check == "checked") {
		$("#appliacntMessage", navTab.getCurrentPanel()).find("input").val(null);
		$("#appliacntMessage", navTab.getCurrentPanel()).find("select").val(null);
		// $("#provinceReportApplicantId").selectMyComBox("");
		// var options="";
		// options="<option value=''>请选择</option>";
		// $("#cityReportApplicantId").loadMyComboxOptions(options,'1');
		// var option="";
		// option="<option value=''>请选择</option>";
		// $("#areaReportApplicantId").loadMyComboxOptions(option,'1');
	}

	$.ajax({
				'url' : 'clm/register/deleteApplicant_CLM_dataCollectAction.action?listId='
						+ val,
				'type' : 'post',
				'success' : function() {
					alertMsg.info("删除成功");
				},
				'error' : function() {
					alertMsg.info("删除失败,请联系管理员");

				}
			});

}

/*
 * $(".accResult1").live("change",function findAccResultDesc1(accResult1){ var
 * accResult1 = $(this); var accResult1Name = accResult1.next(); var accResult2 =
 * $(".accResult2"); var accResult2Name = accResult2.next(); var
 * accResult1Option = $(this).find("option"); var relaCode =
 * $("#accReasonId").val(); if(accResult1Option.size()<=1){ $.ajax({
 * 'type':'post',
 * 'url':'clm/register/findAccResultDesc1_CLM_dataCollectAction.action?relaCode='+relaCode,
 * 'datatype':'json', 'success':function(data){ var data = eval("(" + data +
 * ")"); accResult1.empty(); accResult1Name.attr("value","");
 * accResult2.empty(); accResult2Name.attr("value",""); $("<option value='-1'
 * class = ''>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 * </option>").appendTo(accResult1); $("<option value='-1' class =
 * ''>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
 * </option>").appendTo(accResult2); for(var i = 0; i < data.length; i++){ var
 * option1 = "<option value='"+data[i].code+"' class='"+data[i].name+"'
 * >"+data[i].code+"</option>"; $(option1).appendTo(accResult1); } },
 * 'error':function(){ alertMsg.error("出险结果1查询失败！"); } }); } });
 */
// 弹出是否
/*function exit() {
	alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？", {
		okCall : function() {
			navTab.closeCurrentTab();
		}
	});
}*/

// 长期勾选事件
$("#isLongValid", navTab.getCurrentPanel()).click(function() {
	$("#clmtCertiValidDate").attr("value", "");
	var check = $(this).attr("checked");
	if (check == "checked") {
		$(this).attr("value", "1");
		$("#clmtCertiValidDate", navTab.getCurrentPanel()).attr("disabled", true);
		$("#isLongValids", navTab.getCurrentPanel()).attr("disabled", true);
	} else {
		$(this).attr("value", "0");
		$("#clmtCertiValidDate", navTab.getCurrentPanel()).attr("disabled", false);
		$("#isLongValids", navTab.getCurrentPanel()).attr("disabled", false);
	}
});

// caseId不等于空的时候分别给省市县赋值
function select123(province, city, area, accCountryCode) {
	if (!accCountryCode) {
		accCountryCode = "CHN";
	}
	if (accCountryCode == "CHN") {
		$("#provinceReportId", navTab.getCurrentPanel()).removeAttr("disabled");
		claimFireEvent($("#provinceReportId", navTab.getCurrentPanel()));
		$("#cityReportId", navTab.getCurrentPanel()).removeAttr("disabled");
		claimFireEvent($("#cityReportId", navTab.getCurrentPanel()));
		$("#areaReportId", navTab.getCurrentPanel()).removeAttr("disabled");
		claimFireEvent($("#areaReportId", navTab.getCurrentPanel()));
		var accDistreactName1ReportId = $("#accDistreactName1DataId",
				navTab.getCurrentPanel()).val();
		// 为省赋值
		$("#provinceReportId", navTab.getCurrentPanel()).val(province);
		claimFireEvent($("#provinceReportId", navTab.getCurrentPanel()));
		var cityReportId = $("#cityReportId", navTab.getCurrentPanel());
		var areaReportId = $("#areaReportId", navTab.getCurrentPanel());
		// 根据省获取市。为市赋值。
		$.ajax({
					'type' : 'post',
					'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
							+ province,
					'datatype' : 'json',
					'async' : true,
					'success' : function(data) {
						var data = eval("(" + data + ")");
						$("#cityReportId", navTab.getCurrentPanel()).empty();
						$("<option value=''>市</option>").appendTo(cityReportId);
						for (var i = 0; i < data.length; i++) {
							if (data[i].code == city) {
								$("#cityReportId", navTab.getCurrentPanel())
										.prev().val(
												data[i].code + "-"
														+ data[i].name);
								$("#cityReportId", navTab.getCurrentPanel())
										.val(data[i].code);
								$(
										"<option value='" + city
												+ "' class = '" + data[i].name
												+ "'>" + data[i].name
												+ "</option>").appendTo(
										cityReportId);
							}
						}
						for (var i = 0; i < data.length; i++) {
							if (data[i].code != city) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$(option1).appendTo(cityReportId);
							}
						}
						$("#cityReportId", navTab.getCurrentPanel()).val(city);
					},
				});
		// 获取市为县赋值
		$.ajax({
					'type' : 'post',
					'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
							+ city,
					'datatype' : 'json',
					'async' : true,
					'success' : function(data) {
						var data = eval("(" + data + ")");
						$("#areaReportId", navTab.getCurrentPanel()).empty();
						$("<option value=''>区/县</option>").appendTo(
								areaReportId);
						for (var i = 0; i < data.length; i++) {
							if (data[i].code == area) {
								$("#areaReportId", navTab.getCurrentPanel())
										.prev().val(
												data[i].code + "-"
														+ data[i].name);
								$("#areaReportId", navTab.getCurrentPanel())
										.val(data[i].code);
								$(
										"<option value='" + area
												+ "' class = '" + data[i].name
												+ "'>" + data[i].name
												+ "</option>").appendTo(
										areaReportId);
							}
						}
						for (var i = 0; i < data.length; i++) {
							if (data[i].code != area) {
								var option1 = "<option value='" + data[i].code
										+ "'   class='" + data[i].name + "' >"
										+ data[i].name + "</option>";
								$(option1).appendTo(areaReportId);
							}
						}
						$("#areaReportId", navTab.getCurrentPanel()).val(area);
					},
				});
		// setTimeOut(0.01);
		// $("#cityReportId" ,
		// navTab.getCurrentPanel())[0].fireEvent("onchange");
		// setTimeOut(0.1);
		// $("#areaReportId" ,
		// navTab.getCurrentPanel())[0].fireEvent("onchange");
	} else {
		// 把省市县禁掉，为空。
		$("#provinceReportId", navTab.getCurrentPanel()).val("");
		$("#cityReportId", navTab.getCurrentPanel()).empty();
		$("#areaReportId", navTab.getCurrentPanel()).attr("value", "");
		$("#provinceReportId", navTab.getCurrentPanel()).attr("disabled",
				"disabled");
		$("#cityReportId", navTab.getCurrentPanel()).attr("disabled",
				"disabled");
		$("#areaReportId", navTab.getCurrentPanel()).attr("disabled",
				"disabled");
	}
}

// 已有事故带回信息。
function queryExistedEventAccident() {
	$("#flagIdId", navTab.getCurrentPanel()).val(11);
	// 查询已有事故带回时的处理。
	var provice = $("#accProvince1ReportIdData", navTab.getCurrentPanel())
			.val();
	var city = $("#accCity1ReportIdData", navTab.getCurrentPanel()).val();
	var distreact = $("#accDistreact1ReportIdData", navTab.getCurrentPanel())
			.val();
	var accCountryCode = $("#accCountryCode1ReportIdData",
			navTab.getCurrentPanel()).val();
	var accReason = $("#accReasonId", navTab.getCurrentPanel()).val();
	$("#contryDatatId", navTab.getCurrentPanel()).val(accCountryCode);
	claimFireEvent($("#contryDatatId", navTab.getCurrentPanel()));
	// 显示出险地点
	select123(provice, city, distreact, accCountryCode);
	// 为意外细节和治疗医院还有治疗情况赋值
	// 获取值
	var accidentDetail = $("#accidentDetailReportIdData",
			navTab.getCurrentPanel()).val();
	var accidentDetailName = $("#accidentDetailNameReportIdData",
			navTab.getCurrentPanel()).val();
	var cureHospital = $("#cureHospitalReportIdData", navTab.getCurrentPanel())
			.val();
	var cureHospitalName = $("#cureHospitalNameReportIdData",
			navTab.getCurrentPanel()).val();
	var cureStatus = $("#cureStatusReportIdData", navTab.getCurrentPanel())
			.val();
	var seriousDisease = $("#seriousDiseaseReportIdData",
			navTab.getCurrentPanel()).val();
	// 赋值
	$("#findaccidentDetails", navTab.getCurrentPanel()).val(accidentDetail);
	$("#accidentDetail", navTab.getCurrentPanel()).val(accidentDetailName);
	$("#cureStatusId").selectMyComBox(cureStatus);
	$("#accReasonId").selectMyComBox(accReason);
	$("#inputHospital", navTab.getCurrentPanel()).val(cureHospital);
	$("#cureHospital", navTab.getCurrentPanel()).val(cureHospitalName);
	$("#seriousDiseaseId", navTab.getCurrentPanel()).val(seriousDisease);
	// $("#contryDatatId" , navTab.getCurrentPanel()).val(accCountryCode);
}

// 修改事故日期的时候，置空事件号。
function onchangeReason(k) {
	var acte = document.activeElement.id;
	if (acte != "accidentTimeIds") {
		if ($(k).val() != $("#accDateReporIdData", navTab.getCurrentPanel())
				.val()) {
			if ($("#claimCaseNo", navTab.getCurrentPanel()).val() != "") { // 如果赔案不为空的话，那么事件号也不为空，判断事件号是否和其他赔案关联。
				$("#accDateReporIdData", navTab.getCurrentPanel()).val(
						$(k).val());
				// 获取事件ID。
				var accidentId = $("#accidentIdRegister",
						navTab.getCurrentPanel()).val();
				var caseNo = $("#claimCaseNo", navTab.getCurrentPanel())
						.val();
				var accidentTimeId = $("#accidentTimeIds" , navTab.getCurrentPanel()).val();
				var accReasonId = $("#accReasonId" , navTab.getCurrentPanel()).val();
				var insuredId = $("#claimAccInsuredId" , navTab.getCurrentPanel()).val();
				var accidentNoId = $("#accidentNoId" , navTab.getCurrentPanel()).val();
				// 判断这个事件与其他的赔案是否有关联，如果有关联，说明是已有事件。置空，否则的话不变。
				$.ajax({
							'type' : 'post',
							'url' : 'clm/report/accidentChangeCase_CLM_insertReportInfoInputAction.action?claimAccidentVO.accidentId='+accidentId+'&claimAccidentVO.caseNo='+caseNo+'&claimAccidentVO.accReason='+accReasonId+'&claimAccidentVO.accDate='+accidentTimeId+'&claimAccidentVO.insuredId='+insuredId+'&claimAccidentVO.accidentNo='+accidentNoId,
							'datatype' : 'json',
							'success' : function(data) {
								var data = eval("(" + data + ")");
								if(data.accidentNo == "" && data.accidentNo == null){
									$("#accidentNoIdId",navTab.getCurrentPanel()).val("");
									$("#accidentNoId",navTab.getCurrentPanel()).val("");
								} else {
									$("#accidentNoId",navTab.getCurrentPanel()).val(data.accidentNo);
									$("#accidentNoIdId",navTab.getCurrentPanel()).val(data.accidentNo);
								}
							},
						});
			} else {
				$("#accDateReporIdData", navTab.getCurrentPanel()).val(
						$(k).val());
				// 获取出险人ID和事故日期和出险原因，匹配是否存在一条已有事故。
				var insuredId = $("#claimAccInsuredId",
						navTab.getCurrentPanel()).val();
				var accDate = $("#accidentTimeIds",
						navTab.getCurrentPanel()).val();
				var accReason = $("#accReasonId", navTab.getCurrentPanel())
						.val();
				if(accDate.length == 10){
					$.ajax({
						'type' : 'post',
						'url' : 'clm/report/queryClaimAccidentIsOne_CLM_insertReportInfoInputAction.action?claimAccidentVO.insuredId='
							+ insuredId
							+ '&claimAccidentVO.accDate='
							+ accDate
							+ '&claimAccidentVO.accReason='
							+ accReason,
							'datatype' : 'json',
							'success' : function(data) {
								var data = eval("(" + data + ")");
								if (data.flag == "1") { // 说明存在仅有一条已有事故，那么带出相关信息。
									select123(data.accProvince,
											data.accCity,
											data.accDistreact,
											data.accCountryCode); // 给省市县赋值
									$("#accReasonId", navTab.getCurrentPanel()).selectMyComBox(
											data.accReason);// 给出险原因赋值
									$("#accidentTimeIds",
											navTab.getCurrentPanel()).val(
													data.accDateString);// 给事故日期赋值
									$("#accidentNoId",
											navTab.getCurrentPanel()).val(
													data.accidentNo);// 给事件号赋值
									$("#accidentNoIdId",
											navTab.getCurrentPanel()).val(
													data.accidentNo);// 给事件号赋值
									$("#accStreetReportId",
											navTab.getCurrentPanel()).val(
													data.accStreet);// 给乡镇街道赋值
									$("#accidentDescId",
											navTab.getCurrentPanel()).val(
													data.accDesc);// 给事故描述赋值
								} else {
									$("#accidentNoId",
											navTab.getCurrentPanel()).val(
											"");
									$("#accidentNoIdId",
											navTab.getCurrentPanel()).val(
											"");
								}
							},
					});
				}
			}
		}
	}
}

// 出现原因
function accidentReasonData() {
	//当出险原因改变时，清空所有的出险结果信息。
	var trs = $("#tbodysIdData", navTab.getCurrentPanel()).find("tr");
	for(var i = 0; i < trs.length; i++){
		$("#tbodysIdData", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("input:eq(2)").val("");
		$("#tbodysIdData", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("input:eq(3)").val("");
		$("#tbodysIdData", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(2)").find("input:eq(2)").val("");
		$("#tbodysIdData", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(2)").find("input:eq(3)").val("");
	}
	var relaCode = $("#accReasonId", navTab.getCurrentPanel()).val();
	if (relaCode != "2") {
		$("#findaccidentDetails", navTab.getCurrentPanel()).val("");
		$("#accidentDetail", navTab.getCurrentPanel()).val("");
		$("#accidentDetailId", navTab.getCurrentPanel()).attr("disabled",
				"disabled");
	} else {
		$("#accidentDetailId", navTab.getCurrentPanel()).removeAttr("disabled");
	}

	// 选择出险原因的时候，查看是否是已有事故，如果是的话解除关联，清空
	if ($("#claimCaseNo", navTab.getCurrentPanel()).val() != "") { // 如果赔案不为空的话，那么事件号也不为空，判断事件号是否和其他赔案关联。
		// 获取事件ID。
		var accidentId = $("#accidentIdRegister", navTab.getCurrentPanel())
		.val();
		var caseNo = $("#claimCaseNo", navTab.getCurrentPanel()).val();
		
		var accidentTimeId = $("#accidentTimeIds" , navTab.getCurrentPanel()).val();
		var accReasonId = $("#accReasonId" , navTab.getCurrentPanel()).val();
		var insuredId = $("#claimAccInsuredId" , navTab.getCurrentPanel()).val();
		var accidentNoId = $("#accidentNoId" , navTab.getCurrentPanel()).val();
		
		
		// 判断这个事件与其他的赔案是否有关联，如果有关联，说明是已有事件。置空，否则的话不变。
		$.ajax({
			'type' : 'post',
			'url' : 'clm/report/accidentChangeCase_CLM_insertReportInfoInputAction.action?claimAccidentVO.accidentId='+accidentId+'&claimAccidentVO.caseNo='+caseNo+'&claimAccidentVO.accReason='+accReasonId+'&claimAccidentVO.accDateString='+accidentTimeId+'&claimAccidentVO.insuredId='+insuredId+'&claimAccidentVO.accidentNo='+accidentNoId,
			'datatype' : 'json',
			'success' : function(data) {
				var data = eval("(" + data + ")");
				if(data.accidentNo == "" && data.accidentNo == null){
					$("#accidentNoIdId",navTab.getCurrentPanel()).val("");
					$("#accidentNoId",navTab.getCurrentPanel()).val("");
				} else {
					$("#accidentNoId",navTab.getCurrentPanel()).val(data.accidentNo);
					$("#accidentNoIdId",navTab.getCurrentPanel()).val(data.accidentNo);
				}
			},
		});
	} else {
		// 获取出险人ID和事故日期和出险原因，匹配是否存在一条已有事故。
		var insuredId = $("#claimAccInsuredId", navTab.getCurrentPanel())
		.val();
		var accDate = $("#accidentTimeIds", navTab.getCurrentPanel()).val();
		var accReason = $("#accReasonId", navTab.getCurrentPanel()).val();
		$.ajax({
			'type' : 'post',
			'url' : 'clm/report/queryClaimAccidentIsOne_CLM_insertReportInfoInputAction.action?claimAccidentVO.insuredId='
				+ insuredId
				+ '&claimAccidentVO.accDateString='
				+ accDate
				+ '&claimAccidentVO.accReason='
				+ accReason,
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					if (data.flag == "1") { // 说明存在仅有一条已有事故，那么带出相关信息。
						select123(data.accProvince, data.accCity,
								data.accDistreact, data.accCountryCode); // 给省市县赋值
						$("#accReasonId")
						.selectMyComBox(data.accReason);// 给出险原因赋值
						$("#accidentTimeIds", navTab.getCurrentPanel())
						.val(data.accDateString);// 给事故日期赋值
						$("#accidentNoId", navTab.getCurrentPanel())
						.val(data.accidentNo);// 给事件号赋值
						$("#accidentNoIdId", navTab.getCurrentPanel())
						.val(data.accidentNo);// 给事件号赋值
						$("#accStreetReportId",
								navTab.getCurrentPanel()).val(
										data.accStreet);// 给乡镇街道赋值
						$("#accidentDescId", navTab.getCurrentPanel())
						.val(data.accDesc);// 给事故描述赋值
					} else {
						$("#accidentNoId", navTab.getCurrentPanel())
						.val("");
						$("#accidentNoIdId", navTab.getCurrentPanel())
						.val("");
					}
				},
		});
	}
}
// 控制标签页输入项为只读
function onlyReadDataCollect() {

	bs = 1;
	// ------------DIV------------
	var obj = $("div#datacollectDiv");
	// 按钮
	obj.find("button").each(function() {
		//if ($(this).text() != "下一步") {
			$(this).attr("disabled", true);
		//}
	});
	// 输入框 复选框 单选按钮 控制
	obj.find("input").each(function() {
		if (!$(this).is("[type='radio']")) {
			$(this).attr("disabled", true);
		}
	});
	// 下拉框
	obj.find("select").each(function() {
		$(this).attr("disabled", true);
	});
	// a标签
	obj.find("a").each(function() {
		$(this).attr("disabled", true);
	});
	// 多行文本框控制
	obj.find("textarea").each(function() {
		$(this).attr("disabled", true);
	});
	setTimeout(function() {
		$("#addIdentifyinfoId", navTab.getCurrentPanel()).siblings(".button").find(
			"button").attr("disabled", "disabled");
	}, 1);
	setTimeout(function() {
		$("#addTable", navTab.getCurrentPanel()).siblings(".button").find(
				"button").attr("disabled", "disabled");
	}, 1);
	setTimeout(function() {
		$("#addDoctorId", navTab.getCurrentPanel()).siblings(".button").find(
				"button").attr("disabled", "disabled");
	}, 1);
	setTimeout(function() {
		$("#tableId", navTab.getCurrentPanel()).siblings(".button").find(
				"button").attr("disabled", "disabled");
	}, 1);
	// 出险年龄 赋值
	$("#tbodyIds tr", navTab.getCurrentPanel()).each(function() {
		var now = $(this).find("td:eq(1)").find("input").val(); // 出险日期
		if (now.length != 0) {
			var dateNow = new Date(now.replace(/-/g, '/'));
			var birth = $("#accBirthIds", navTab.getCurrentPanel()).val(); // 出险人出生日期
			var dateBirth = new Date(birth.replace(/-/g, '/'));
			var age = 0;
			var years = dateNow.getFullYear() - dateBirth.getFullYear();
			var months = dateNow.getMonth() - dateBirth.getMonth();
			var days = dateNow.getDate() - dateBirth.getDate();
			if (months > 0) {
				age = years;
			} else if (months < 0) {
				age = years - 1;
			} else if (days >= 0) {
				age = years;
			} else {
				age = years - 1;
			}
			$(this).find("td:eq(2)").find("input").val(age);
		}
	});
}

function applicantInfoCheck() {
	var name = $("#clmtName", navTab.getCurrentPanel()).val();
	var sex = $("#clmtSex", navTab.getCurrentPanel()).val();
	var InsurRelation = $("#clmtInsurRelationFordc", navTab.getCurrentPanel()).val();
	var HolderRelation = $("#clmtHolderRelation", navTab.getCurrentPanel())
			.val();
	var CertiType = $("#clmtCertiType", navTab.getCurrentPanel()).val();
	var clmtCertiValidDate = $("#clmtCertiValidDate", navTab.getCurrentPanel())
			.val();
	var isLongValid = $("#isLongValid", navTab.getCurrentPanel()).val();
	var CertiNo = $("#clmtCertiNo", navTab.getCurrentPanel()).val();
	var clmtMp = $("#clmtMp", navTab.getCurrentPanel()).val();
	var clmtTel = $("#clmtTel", navTab.getCurrentPanel()).val();
	var clmtCity = $("#cityReportApplicantId", navTab.getCurrentPanel()).val();
	var clmtStreet = $("#areaReportApplicantId", navTab.getCurrentPanel())
			.val();

	if ($.trim(name) == "") {
		alertMsg.info("申请人必填");
		return false;
	}
	if ($.trim(sex) == "") {
		alertMsg.info("性别必填");
		return false;
	}
	if ($.trim(InsurRelation) == "") {
		alertMsg.info("与被保险人关系必填");
		return false;
	}
	if ($.trim(HolderRelation) == "") {
		alertMsg.info("与投保险人关系人关系必填");
		return false;
	}
	if ($.trim(CertiType) == "") {
		alertMsg.info("证件类型必填");
		return false;
	}
	if ($.trim(CertiNo) == "") {
		alertMsg.info("证件号码必填");
		return false;
	}
	if ($.trim(isLongValid) == "0" && $.trim(clmtCertiValidDate) == ""
			&& $.trim(clmtCertiValidDate) == null) {
		alertMsg.info("证件期限必填");
		return false;
	}
	if ($.trim(clmtMp) == "") {
		alertMsg.info("手机号码必填");
		return false;
	}
	if ($.trim(clmtTel) == "") {
		alertMsg.info("固定电话必填");
		return false;
	}
	if ($.trim(clmtCity) == "") {
		alertMsg.info("省/直辖市必选");
		return false;
	}
	if ($.trim(clmtStreet) == "") {
		alertMsg.info("乡镇/街道不能为空!");
		return false;
	}
	return true;
}
// 回调函数
function mYDialogAjaxDone(json) {
	DWZ.ajaxDone(json);
	if (json.data.status == "1") {
		$("#nextIdData", navTab.getCurrentPanel()).attr("disabled", "disabled");
	} else {
		$("#nextIdData", navTab.getCurrentPanel()).removeAttr("disabled");
	}
	// 匹配责任
	if (json.data.status == "10") {
		alertMsg.warn('<理赔类型>没有匹配的责任，请检查！');
	}
}
// 重大疾病显示格式
$(document).ready(function() {
	var obj = $("#seriousDiseaseId option", navTab.getCurrentPanel());
	obj.each(function() {
		var value = $(this).val();
		var text = $(this).text();
		if (value != "") {
			$(this).text(value + "-" + text);
		}
	});
});
// 领款人银行显示银行代码
$(document).ready(function() {
	var obj = $("#clmtBank option", navTab.getCurrentPanel());
	obj.each(function() {
		var value = $(this).val();
		var text = $(this).text();
		$(this).text(value + "-" + text);
	});
});
//为后台备用传值字段赋值
function DistreactChageReportData(k) {
	$("#accDistreact1ReportIdDatafz", navTab.getCurrentPanel()).val($(k).val());
}
// 根据省查询市
function ProvinceChangeReportData(k) {
	var province = $(k).val();
	var cityReportId = $("#cityReportId", navTab.getCurrentPanel());
	$("#cityReportId", navTab.getCurrentPanel()).prev().val("");
	$("#accProvince1ReportIdDatafz", navTab.getCurrentPanel()).val($(k).val());
	$("#areaReportId", navTab.getCurrentPanel()).prev().val("");
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ province,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#cityReportId", navTab.getCurrentPanel()).empty();
					$("#areaReportId", navTab.getCurrentPanel()).empty();
					$("<option value=''>市</option>").appendTo(cityReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(cityReportId);
					}
				},
			});
	// setTimeOut(0.01);
	// $("#cityReportId" , navTab.getCurrentPanel())[0].fireEvent("onchange");
	// setTimeOut(0.1);
	// $("#areaReportId" , navTab.getCurrentPanel())[0].fireEvent("onchange");
}
// 根据市查询县
function cityChageReportData(k) {
	$("#accCity1ReportIdDatafz",navTab.getCurrentPanel()).val($(k).val());
	$("#areaReportId",navTab.getCurrentPanel()).prev().val("");
	var city = $(k).val();
	var areaReportId = $("#areaReportId", navTab.getCurrentPanel());
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#areaReportId", navTab.getCurrentPanel()).empty();
					$("<option value=''>区/县</option>").appendTo(areaReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(areaReportId);
					}
				},
			});
	// $("#areaReportId",navTab.getCurrentPanel())[0].fireEvent("onchange");
}
// 申请人属性值的问题
function clmtPropertyCodeChangeData(k) {
	var caseNo = $("#claimCaseNo", navTab.getCurrentPanel()).val();
	if ($(k).val() == "01") {
		$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev()
				.removeAttr("style");
		$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).selectMyComBox("00");
		$("#complaintGender", navTab.getCurrentPanel()).setMyComboxHide(true);
		if (caseNo != "") {
			$.ajax({
						'type' : 'post',
						'url' : 'clm/sign/queryCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='
								+ caseNo,
						'datatype' : 'json',
						'async' : true,
						'success' : function(claimApplicantVO) {
							var claimApplicantVO = eval("(" + claimApplicantVO
									+ ")");
							$("#clmtName", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtName);
							$("#clmtInsurRelationFordc").selectMyComBox("00");
							;
							$("#clmtMp", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtMp);
							$("#clmtMail", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtMail);
							if (claimApplicantVO.clmtSex == '0') {
								claimApplicantVO.clmtSex = null;
							}
							$("#clmtSexIdApplicantData",
									navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtSex);
							$("#provinceReportApplicantId",
									navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtProvince);
							$("#provinceReportApplicantId",
									navTab.getCurrentPanel())
									.prev()
									.val(
											claimApplicantVO.clmtProvince
													+ "-"
													+ claimApplicantVO.clmtProvinceName);
							applicantSSX(claimApplicantVO.clmtProvinceName,
									claimApplicantVO.clmtCity,
									claimApplicantVO.clmtDistreact,
									claimApplicantVO.clmtProvince);
							$("#clmtStreet", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtStreet);
							$("#clmtCertiType", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtCertiType);
							$("#clmtCertiNo", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtCertiNo);
							$("#clmtProfession", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtProfession);
							$("#clmtTel", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtTel);
							$("#clmtNation", navTab.getCurrentPanel()).val(
									claimApplicantVO.clmtNation);
							if (claimApplicantVO.peopleFlag == "1") {
								$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).selectMyComBox("00");
								$("#clmtInsurRelationFordc",
										navTab.getCurrentPanel()).attr(
										"readonly", "readonly");
							} else {
								$("#peopleFlagId", navTab.getCurrentPanel())
										.val(claimApplicantVO.peopleFlag);
							}
						},
					});
		}
	}
	if ($(k).val() == "02") {
		$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).setMyComboxHide(false);
		$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).remove("readonly");
		$("#clmtSexIdApplicantData", navTab.getCurrentPanel()).val("");
		$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev().attr(
				"style", "width: 35px;border-right:0px;");
		$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).prev().attr(
				"style", "width:55px;");
		var contractBeneInfoId = $("#contractBeneInfoDataIdFordc", navTab
				.getCurrentPanel());
		if (caseNo != "") {
			$.ajax({
						'type' : 'post',
						'url' : 'clm/sign/queryContractCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='
								+ caseNo,
						'datatype' : 'json',
						'success' : function(contractBeneVOs) {
							var contractBeneVOs = eval("(" + contractBeneVOs
									+ ")");
							var options = "";
							options = "<option value=''>请选择</option>";
							for (var i = 0; i < contractBeneVOs.length; i++) {
								if (contractBeneVOs[i].customerName != "") {
									options += "<option value='"
											+ contractBeneVOs[i].customerName
											- contractBeneVOs[i].designation
											- contractBeneVOs[i].customerGender
											+ "'>"
											+ contractBeneVOs[i].customerName
											+ "</option>";
								}
							}
							$("#contractBeneInfoDataIdFordc").loadMyComboxOptions(
									options, '1');
							$("#contractBeneInfoDataIdFordc",
									navTab.getCurrentPanel()).prev().attr(
									"style", "width:55px;");
						},
					});
		}
	}
	if ($(k).val() != "02") {
		$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev()
				.removeAttr("style");
		$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).remove("readonly");
		$("#contractBeneInfoDataIdFordc").setMyComboxHide(true);
		$("#clmtSexIdApplicantData", navTab.getCurrentPanel()).val("");
	}
}
// 保单下受益人的信息
function queryContractBeneInfoData(k) {
	var customers = $("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel())
			.val();
	var customerss = customers.split("-");
	$("#clmtName", navTab.getCurrentPanel()).val(customerss[0]);
	$("#clmtInsurRelationFordc").selectMyComBox(customerss[1]);
	// var logId = $(k).val();
	// $.ajax({
	// 'type':'post',
	// 'url':'clm/sign/queryContractCustomerInfoo_CLM_addSignCheckCaseAction.action?contractBeneVO.logId='+logId,
	// 'datatype':'json',
	// 'success':function(claimApplicantVO){
	// var claimApplicantVO = eval("(" + claimApplicantVO + ")");
	// $("#clmtName" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtName);
	// $("#clmtInsurRelationFordc" ,
	// navTab.getCurrentPanel()).val(claimApplicantVO.clmtInsurRelation);
	// $("#clmtMp" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtMp);
	// $("#clmtMail" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtMail);
	// $("#clmtSex" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtSex);
	// $("#clmtCertiType" ,
	// navTab.getCurrentPanel()).val(claimApplicantVO.clmtCertiType);
	// $("#clmtCertiNo" ,
	// navTab.getCurrentPanel()).val(claimApplicantVO.clmtCertiNo);
	// $("#clmtProfession" ,
	// navTab.getCurrentPanel()).val(claimApplicantVO.clmtProfession);
	// $("#clmtTel" , navTab.getCurrentPanel()).val(claimApplicantVO.clmtTel);
	// $("#clmtNation" ,
	// navTab.getCurrentPanel()).val(claimApplicantVO.clmtNation);
	// },
	// });
}
// 显示div
function showPanelId() {

	// $("#applicantPanel", navTab.getCurrentPanel()).css("display","block");
	// $("#showButtonId",navTab.getCurrentPanel()).val("-");
	if ($(".main_heading", navTab.getCurrentPanel()).find("b").attr("class") == "maim_lpbubble") {
		$(".main_heading", navTab.getCurrentPanel()).next(".main_lptwo")
				.animate({
					height : 'toggle',
					opacity : 'toggle'
				}, "slow");
		$(".main_heading", navTab.getCurrentPanel()).find("b").removeClass(
				"maim_lpbubble").addClass("maim_lpask");
	}

	$("#appliacntMessage", navTab.getCurrentPanel()).find("input").val(null);
	$("#appliacntMessage", navTab.getCurrentPanel()).find("select").val(null);
	$("#clmtSexIdApplicantData", navTab.getCurrentPanel()).val(null);

	var trs = $("#tbodyIds", navTab.getCurrentPanel()).find("tr");
	for (var i = 0; i < trs.length; i++) {
		var claimType = $("#tbodyIds", navTab.getCurrentPanel()).find(
				"tr:eq(" + i + ")").find("td:eq(0)").find("select").val();
		var caseNo = $("#claimCaseNo", navTab.getCurrentPanel()).val();
		if (claimType == "01") { // 身故的时候申请人属性默认为指定受益人
			$("#clmtPropertyCodeDataIdFordc").selectMyComBox("02");
			$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).css(
					"visibility", "visible");
			$("#contractBeneInfoDataIdFordc").setMyComboxHide(false);
			$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev().attr(
					"style", "width: 35px;border-right:1px;");
			$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).prev().attr(
					"style", "width:55px;");
			if (caseNo != "") {
				var contractBeneInfoId = $("#contractBeneInfoDataIdFordc", navTab
						.getCurrentPanel());
				$.ajax({
							'type' : 'post',
							'url' : 'clm/sign/queryContractCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='
									+ caseNo,
							'datatype' : 'json',
							'success' : function(contractBeneVOs) {
								var contractBeneVOs = eval("("
										+ contractBeneVOs + ")");
								var options = "";
								options = "<option value=''>请选择</option>";
								for (var i = 0; i < contractBeneVOs.length; i++) {
									if (contractBeneVOs[i].customerName != "") {
										options += "<option value='"
												+ contractBeneVOs[i].customerName
												- contractBeneVOs[i].designation
												- contractBeneVOs[i].customerGender
												+ "'>"
												+ contractBeneVOs[i].customerName
												+ "</option>";
									}
								}
								$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel())
										.loadMyComboxOptions(options, '1');
								$("#contractBeneInfoDataIdFordc",
										navTab.getCurrentPanel()).prev().attr(
										"style", "width:55px;");
							},
						});
			}
		} else {
			$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev()
					.removeAttr("style");
			$("#clmtPropertyCodeDataIdFordc").selectMyComBox("01");
			$("#contractBeneInfoDataIdFordc").setMyComboxHide(true);
			if (caseNo != "") {
				$.ajax({
							'type' : 'post',
							'url' : 'clm/sign/queryCustomerInfo_CLM_addSignCheckCaseAction.action?claimCaseVO.caseNo='
									+ caseNo,
							'datatype' : 'json',
							'success' : function(claimApplicantVO) {
								var claimApplicantVO = eval("("
										+ claimApplicantVO + ")");
								$("#clmtName", navTab.getCurrentPanel()).val(
										claimApplicantVO.clmtName);
								$("#clmtInsurRelationFordc", navTab.getCurrentPanel()).selectMyComBox("00");
								$("#clmtMp", navTab.getCurrentPanel()).val(
										claimApplicantVO.clmtMp);
								changePhoneDataa($("#clmtMp", navTab
										.getCurrentPanel())[0]);
								$("#provinceReportApplicantId",
										navTab.getCurrentPanel()).val(
										claimApplicantVO.clmtProvince);
								$("#provinceReportApplicantId",
										navTab.getCurrentPanel())
										.prev()
										.val(
												claimApplicantVO.clmtProvince
														+ "-"
														+ claimApplicantVO.clmtProvinceName);
								applicantSSX(claimApplicantVO.clmtProvinceName,
										claimApplicantVO.clmtCity,
										claimApplicantVO.clmtDistreact,
										claimApplicantVO.clmtProvince);
								$("#clmtStreet", navTab.getCurrentPanel()).val(
										claimApplicantVO.clmtStreet);
								$("#clmtMail", navTab.getCurrentPanel()).val(
										claimApplicantVO.clmtMail);
								$("#clmtSexIdApplicantData",
										navTab.getCurrentPanel()).val(
										claimApplicantVO.clmtSex);
								if (claimApplicantVO.peopleFlag == "1") {
									$("#clmtInsurRelationFordc")
											.selectMyComBox("00");
									$("#clmtInsurRelationFordc",
											navTab.getCurrentPanel()).attr(
											"readonly", "readonly");
								} else {
									$("#peopleFlagId", navTab.getCurrentPanel())
											.val(claimApplicantVO.peopleFlag);
								}
							},
						});
			}
		}
	}
}
// 收缩按钮 实现
function show(show) {
	if (show.value == "-") {
		if ($("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).val() == "02") {
			$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev().attr(
					"style", "width: 35px;border-right:0px;");
			$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).prev().attr(
					"style", "width:55px;");
			$("#contractBeneInfoDataIdFordc").setMyComboxHide(false);
		} else {
			$("#contractBeneInfoDataIdFordc").setMyComboxHide(true);
		}
		$(".main_heading", navTab.getCurrentPanel()).next(".main_lptwo")
				.animate({
					height : 'toggle',
					opacity : 'toggle'
				});
		$(".main_heading", navTab.getCurrentPanel()).find("b").removeClass(
				"maim_lpask").addClass("maim_lpbubble");
		// $("#applicantPanel", navTab.getCurrentPanel()).css("display","none");
		// show.value="+";
	} else if (show.value == "+") {
		if ($("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).val() == "02") {
			$("#clmtPropertyCodeDataIdFordc", navTab.getCurrentPanel()).prev().attr(
					"style", "width: 35px;border-right:0px;");
			$("#contractBeneInfoDataIdFordc", navTab.getCurrentPanel()).prev().attr(
					"style", "width:55px;");
			$("#contractBeneInfoDataIdFordc").setMyComboxHide(false);
		} else {
			$("#contractBeneInfoDataIdFordc").setMyComboxHide(true);
		}
		if ($(".main_heading", navTab.getCurrentPanel()).find("b")
				.attr("class") == "maim_lpbubble") {
			$(".main_heading", navTab.getCurrentPanel()).next(".main_lptwo")
					.animate({
						height : 'toggle',
						opacity : 'toggle'
					}, "slow");
			$(".main_heading", navTab.getCurrentPanel()).find("b").removeClass(
					"maim_lpbubble").addClass("maim_lpask");
		}
		// $("#applicantPanel",
		// navTab.getCurrentPanel()).css("display","block");
		// show.value="-";
	}
}
// 关于国家和省市县的约束，为国家的时候省市县可以选择，否则不能
function contryReportFunData(k) {
	var caseId = $("#claimCaseNo", navTab.getCurrentPanel()).val();
	var cityReportId = $("#cityReportId", navTab.getCurrentPanel());
	var areaReportId = $("#areaReportId", navTab.getCurrentPanel());
	if ($(k).val() == "CHN") { // 初始化上来如果是中国的话，那么进行省市县的初始化，否则不的。
		$("#provinceReportId", navTab.getCurrentPanel()).prev().removeAttr(
				"disabled");
		$("#cityReportId", navTab.getCurrentPanel()).prev().removeAttr(
				"disabled");
		$("#areaReportId", navTab.getCurrentPanel()).prev().removeAttr(
				"disabled");
		if (caseId != "") {
			var province = $("[name='accident.accProvince1']", navTab.getCurrentPanel()).val();
			var city = $("[name='accident.accCity1']", navTab.getCurrentPanel()).val();
			var area = $("[name='accident.accDistreact1']", navTab.getCurrentPanel()).val();
			$("#provinceReportId", navTab.getCurrentPanel()).val(province);
			claimFireEvent($("#provinceReportId", navTab.getCurrentPanel()));
			if (province != "") {
				// 根据省获取市。为市赋值。
				$.ajax({
							'type' : 'post',
							'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
									+ province,
							'datatype' : 'json',
							'success' : function(data) {
								var data = eval("(" + data + ")");
								$("#cityReportId", navTab.getCurrentPanel())
										.empty();
								$("<option value=''>市</option>").appendTo(
										cityReportId);
								for (var i = 0; i < data.length; i++) {
									if (data[i].code == city) {
										$("#cityReportId",
												navTab.getCurrentPanel())
												.prev().val(
														data[i].code + "-"
																+ data[i].name);
										$("#cityReportId",
												navTab.getCurrentPanel()).val(
												data[i].code);
										$(
												"<option value='" + city
														+ "' class = '"
														+ data[i].name + "'>"
														+ data[i].name
														+ "</option>")
												.appendTo(cityReportId);
									}
								}
								for (var i = 0; i < data.length; i++) {
									if (data[i].code != city) {
										var option1 = "<option value='"
												+ data[i].code + "'   class='"
												+ data[i].name + "' >"
												+ data[i].name + "</option>";
										$(option1).appendTo(cityReportId);
									}
								}
								$("#cityReportId", navTab.getCurrentPanel())
										.val(city);
							},
						});
				// 获取市为县赋值
				$.ajax({
							'type' : 'post',
							'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
									+ city,
							'datatype' : 'json',
							'success' : function(data) {
								var data = eval("(" + data + ")");
								$("#areaReportId", navTab.getCurrentPanel())
										.empty();
								$("<option value=''>区/县</option>").appendTo(
										areaReportId);
								for (var i = 0; i < data.length; i++) {
									if (data[i].code == area) {
										$("#areaReportId",
												navTab.getCurrentPanel())
												.prev().val(
														data[i].code + "-"
																+ data[i].name);
										$("#areaReportId",
												navTab.getCurrentPanel()).val(
												data[i].code);
										$(
												"<option value='" + area
														+ "' class = '"
														+ data[i].name + "'>"
														+ data[i].name
														+ "</option>")
												.appendTo(areaReportId);
									}
								}
								for (var i = 0; i < data.length; i++) {
									if (data[i].code != area) {
										var option1 = "<option value='"
												+ data[i].code + "'   class='"
												+ data[i].name + "' >"
												+ data[i].name + "</option>";
										$(option1).appendTo(areaReportId);
									}
								}
								$("#areaReportId", navTab.getCurrentPanel())
										.val(area);
							},
						});
				// setTimeOut(0.01);
				// $("#cityReportId" ,
				// navTab.getCurrentPanel())[0].fireEvent("onchange");
				// setTimeOut(0.1);
				// $("#areaReportId" ,
				// navTab.getCurrentPanel())[0].fireEvent("onchange");
			}
		} else { // 初始化上来的时候默认为北京市市辖区东城区。
			var province = "110000";
			var city = "110100";
			var area = "110101";
			$("#provinceReportId", navTab.getCurrentPanel()).val(province);
			// 根据省获取市。为市赋值。
			$.ajax({
						'type' : 'post',
						'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
								+ province,
						'datatype' : 'json',
						'success' : function(data) {
							var data = eval("(" + data + ")");
							$("#cityReportId", navTab.getCurrentPanel())
									.empty();
							$("<option value=''>市</option>").appendTo(
									cityReportId);
							for (var i = 0; i < data.length; i++) {
								if (data[i].code == city) {
									$("#cityReportId", navTab.getCurrentPanel())
											.prev().val(
													data[i].code + "-"
															+ data[i].name);
									$("#cityReportId", navTab.getCurrentPanel())
											.val(data[i].code);
									$(
											"<option value='" + city
													+ "' class = '"
													+ data[i].name + "'>"
													+ data[i].name
													+ "</option>").appendTo(
											cityReportId);
								}
							}
							for (var i = 0; i < data.length; i++) {
								if (data[i].code != city) {
									var option1 = "<option value='"
											+ data[i].code + "'   class='"
											+ data[i].name + "' >"
											+ data[i].name + "</option>";
									$(option1).appendTo(cityReportId);
								}
							}
							$("#cityReportId", navTab.getCurrentPanel()).val(
									city);
						},
					});
			// 获取市为县赋值
			$.ajax({
						'type' : 'post',
						'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
								+ city,
						'datatype' : 'json',
						'success' : function(data) {
							var data = eval("(" + data + ")");
							$("#areaReportId", navTab.getCurrentPanel())
									.empty();
							$("<option value=''>区/县</option>").appendTo(
									areaReportId);
							for (var i = 0; i < data.length; i++) {
								if (data[i].code == area) {
									$("#areaReportId", navTab.getCurrentPanel())
											.prev().val(
													data[i].code + "-"
															+ data[i].name);
									$("#areaReportId", navTab.getCurrentPanel())
											.val(data[i].code);
									$(
											"<option value='" + area
													+ "' class = '"
													+ data[i].name + "'>"
													+ data[i].name
													+ "</option>").appendTo(
											areaReportId);
								}
							}
							for (var i = 0; i < data.length; i++) {
								if (data[i].code != area) {
									var option1 = "<option value='"
											+ data[i].code + "'   class='"
											+ data[i].name + "' >"
											+ data[i].name + "</option>";
									$(option1).appendTo(areaReportId);
								}
							}
							$("#areaReportId", navTab.getCurrentPanel()).val(
									area);
						},
					});
			setTimeOut(0.02);
			$("#cityReportId", navTab.getCurrentPanel())[0]
					.fireEvent("onchange");
			setTimeOut(0.2);
			$("#areaReportId", navTab.getCurrentPanel())[0]
					.fireEvent("onchange");
		}
	} else {
		// 把省市县禁掉，为空。
		$("#provinceReportId", navTab.getCurrentPanel()).prev().val("");
		$("#cityReportId", navTab.getCurrentPanel()).prev().val("");
		$("#areaReportId", navTab.getCurrentPanel()).prev().val("");
		$("#provinceReportId", navTab.getCurrentPanel()).prev().attr(
				"disabled", "disabled");
		$("#cityReportId", navTab.getCurrentPanel()).prev().attr("disabled",
				"disabled");
		$("#areaReportId", navTab.getCurrentPanel()).prev().attr("disabled",
				"disabled");
	}
}
// 申请人的省市县连动
// 根据省查询市
function ProvinceChangeReportDataApplicant(k) {
	var province = $(k).val();
	var cityReportId = $("#cityReportApplicantId", navTab.getCurrentPanel());
	$("#cityReportApplicantId", navTab.getCurrentPanel()).prev().val("");
	$("#areaReportApplicantId", navTab.getCurrentPanel()).prev().val("");
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ province,
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#cityReportApplicantId", navTab.getCurrentPanel())
							.empty();
					$("#areaReportApplicantId", navTab.getCurrentPanel())
							.empty();
					$("<option value=''>市</option>").appendTo(cityReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(cityReportId);
					}
				},
			});
	// setTimeOut(0.01);
	// $("#cityReportApplicantId" ,
	// navTab.getCurrentPanel())[0].fireEvent("onchange");
	// setTimeOut(0.1);
	// $("#areaReportApplicantId" ,
	// navTab.getCurrentPanel())[0].fireEvent("onchange");
}
// 根据市查询县
function cityChageReportDataApplicant(k) {
	var city = $(k).val();
	var areaReportId = $("#areaReportApplicantId", navTab.getCurrentPanel());
	$("#areaReportApplicantId", navTab.getCurrentPanel()).prev().val("");
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#areaReportApplicantId", navTab.getCurrentPanel())
							.empty();
					$("<option value=''>区/县</option>").appendTo(areaReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(areaReportId);
					}
				},
			});
	// $("#areaReportId",navTab.getCurrentPanel())[0].fireEvent("onchange");
}
// 把老数据转化成带空格的
function changePhoneData(obj) {
	var phone = $("#clmtMp", navTab.getCurrentPanel());
	var formatType = "clearSpace";
	var $input = $("#clmtMp", navTab.getCurrentPanel());
	var value = $(phone).attr("value");
	$input.val("").focus().val(
			value.replace(/\D/g, '').replace(/(^\d{3}|\d{4}\B)/g, "$1 ")
					.substr(0, 13));
	var initVal = formatText(value, formatType);
	//
	// $input.removeAttr("name");
	// $input.parent().find("#beforeHtmlId").remove();
	// var beforeHtml= "<input id='beforeHtmlId' name=\"applicant.clmtMp\"
	// value='"+initVal+ "' type=\"hidden\" >"
	// $input.before(beforeHtml);
}

// 关于申请人是按照固定电话的校验还是按照手机号的校验
function changePhoneDataa(obj) {
	var e = arguments.callee.caller.arguments[0] || window.event;

	var keycode = e.keyCode;

	obj.value = obj.value.replace(/[^\d\+\-]/g, '');
	if (keycode == 8 || keycode == 37 || keycode == 39) {
		return;
	}

	// 点击保存后 手机号码验证或者固化验证 如果开始第一个数字是0则按照固化 若不是按照原来的手机验证
	var phone = $("#clmtMp", navTab.getCurrentPanel());
	if ($(phone).attr("value").substr(0, 1) == 0) {
		var isReplace = "<input   id=\"clmtMp\""
				+ " type=\"expandPhone\"  maxlength='17'  class=\"textInput exphone valid\"   name=\"phone\" value='"
				+ $(phone).attr('value')
				+ "'   onkeyup=\"changePhoneDataa(this)\">";

		$("#clmtMp", navTab.getCurrentPanel()).replaceWith(isReplace);

		var $input = $("#clmtMp", navTab.getCurrentPanel());
		var value = $(phone).attr("value");
		$("#clmtName", navTab.getCurrentPanel()).focus();
		$input.val("").focus().val(value);

	} else {
		var isReplace2 = "<input  id=\"clmtMp\""
				+ " type=\"expandMobile\"  maxlength='13'  class=\"textInput exmobile\"  name=\"mobile\"     onkeyup=\"changePhoneDataa(this)\">";

		$("#clmtMp", navTab.getCurrentPanel()).replaceWith(isReplace2);

		var formatType = "clearSpace";
		var $input = $("#clmtMp", navTab.getCurrentPanel());
		var value = $(phone).attr("value");

		$("#clmtName", navTab.getCurrentPanel()).focus();
		$input.val("").focus().val(
				value.replace(/\D/g, '').replace(/(^\d{3}|\d{4}\B)/g, "$1 ")
						.substr(0, 13));
		var initVal = formatText(value, formatType);
		// 删除之前的

		$input.parent().find("#beforeHtmlId").remove();

		var beforeHtml = "<input id='beforeHtmlId' name=\"applicant.clmtMp\" value='"
				+ initVal + "' type=\"hidden\" >"
		$input.before(beforeHtml);

	}
	;
}

function checkCertiCode(obj) {
	if ($(obj).val() != null || $(obj).val() != "") {
		if ($("#trusteeCertiType", navTab.getCurrentPanel()).val() == "") {
			alertMsg.warn('请先填写证件类型再填写证件号码！');
			return;
		}
	}
}

//证件有效止期长期按钮
function CertiEndCheckBoxAudit(obj){
	if($(obj).attr("checked") == "checked"){
		$("#assigneeEndDate",$.pdialog.getCurrent()).val("");
		$("#assigneeEndDateHidentId",$.pdialog.getCurrent()).val("9999-12-31");
		$("#assigneeEndDate",$.pdialog.getCurrent()).attr("disabled",true);
	}else{
		$("#assigneeEndDate",$.pdialog.getCurrent()).removeAttr("disabled");
	}
}

function addYear(date,i){
	var d1= new Date(date);
	d1.setFullYear(d1.getFullYear()+(i-1+1));
	return date2str(new Date(d1),"yyyy-MM-dd");
}

//时间转换
function date2str(x,y){
	var z={y:x.getFullYear(),
           M:x.getMonth()+1,
           d:x.getDate(),
           h:x.getHours(),
           m:x.getMinutes(),
           s:x.getSeconds()
	};
	return y.replace(/(y+|M+|d+|h+|m+|s+)/g,
			function(v){return ((v.length>1?"0":"")+eval('z.'+v.slice(-1))).slice(-(v.length>2?v.length:2))});
};

function assigneeInfoRegi(){
	//判断证件有效起期和出生日期和证件类型都不为空的时候计算证件有效止期
	var beneCertiType = $("#trusteeCertiType", navTab.getCurrentPanel()).val(); // 证件类型
	var beneCertiStart = $("#assigneeStartDate", navTab.getCurrentPanel()).val(); // 证件有效起期
	var beneCertiCode = $("#trusteeCertiCode", navTab.getCurrentPanel()).val(); // 证件号码	
	//截取年份
	var payeeCertiStarts = beneCertiStart.substring(0,4);
	//月和天拼接
	var payeeCertiStartMDay = beneCertiStart.substring(5,7)+"/"+beneCertiStart.substring(8,10);
	var newBeneCertiEnd =  payeeCertiStarts +"/"+ payeeCertiStartMDay;
	$("#assigneeEndDateCheckBoxId",navTab.getCurrentPanel()).removeAttr("checked");
	$("#assigneeEndDate",navTab.getCurrentPanel()).removeAttr("disabled");
	
	if(beneCertiType != "" && beneCertiStart != ""){
		var beneBirth = beneCertiCode.substring(6,10) +"-"+ beneCertiCode.substring(10,12) +"-"+ beneCertiCode.substring(12,14); // 出生日期
		
		//换算出客户的年龄（周岁,“客户出生日期”开始至“证件有效起期”经过的整年度）
		var dateAcc = new Date(beneCertiStart.replace(/-/g, '/'));
		var dateBirth = new Date(beneBirth.replace(/-/g, '/'));
		var age=0;
		var years = dateAcc.getFullYear()-dateBirth.getFullYear();
		var months = dateAcc.getMonth()-dateBirth.getMonth();
		var days = dateAcc.getDate()-dateBirth.getDate();
		if (months>0) {
			age=years;
		}else if (months<0) {
			age=years-1;
		}else if (days>=0) {
			age=years;
		}else {
			age=years-1;
		}		

	if(beneCertiType == "0"){
		if(0 <= eval(age) && eval(age) <= 15){
			//0-15岁，证件有效止期=证件有效起期+5年,可修改
			$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
			$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
			$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
		} else if(16 <= eval(age) && eval(age) <= 25){
			//16-25岁，证件有效止期=证件有效起期+10年,可修改
			$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10)); // 证件有效止期
			$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10));
			$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
		} else if(26 <= eval(age) && eval(age) <= 45){
			//26-45岁，证件有效止期=证件有效起期+20年,可修改
			$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,20)); // 证件有效止期
			$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,20));
			$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
		} else if(eval(age) >= 46){
			//46岁以上，证件有效止期=9999-12-31，“长期”勾选项默认为“已勾选”状态，可修改。
			$("#assigneeEndDateCheckBoxId",navTab.getCurrentPanel()).attr("checked","checked");
			$("#assigneeEndDate",navTab.getCurrentPanel()).attr("disabled",true);
			$("#assigneeEndDate", navTab.getCurrentPanel()).val("");
			$("#assigneeEndDateHidentId", navTab.getCurrentPanel()).val("9999-12-31");
		}
	} else if(beneCertiType == "h"){ //港澳台居民居住证。
			//证件有效止期=证件有效起期+5年,不可修改
			$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
			$("#assigneeEndDateHidentId", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
	//					$("#beneCertiEnd", navTab.getCurrentPanel()).attr("disabled",true);
	} else if(beneCertiType == "1"){//证件类型为护照。
		if(0 < eval(age) && eval(age) <= 15){
			//0-15岁，证件有效止期=证件有效起期+5年，可修改
			$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
			$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
			$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
		} else if(eval(age) >= 16){
			//16岁以上，证件有效止期=证件有效起期+10年，可修改
			var payeeCertiStart = eval(payeeCertiStarts)+10;
			payeeCertiStartL = payeeCertiStart+"-"+payeeCertiStartMDay;
			$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10)); // 证件有效止期
			$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,10));
			$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
		}
	} else if(beneCertiType == "2"){//证件类型为军官证。
		//军官证有效止期按照下述规则自动生成：证件有效止期=证件有效起期+4年
		$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,4)); // 证件有效止期
		$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,4));
		$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
	} else if(beneCertiType == "b"){//港澳台通行证。
		//港澳台通行证证有效止期按照下述规则自动生成：证件有效止期=证件有效起期+5年，可修改
		$("#assigneeEndDate", navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5)); // 证件有效止期
		$("#assigneeEndDateHidentId",navTab.getCurrentPanel()).val(addYear(newBeneCertiEnd,5));
		$("#assigneeEndDate", navTab.getCurrentPanel()).attr("disabled",false);
	} else if(beneCertiType == "8"){//其他证件类别.
		//其他证件类别有效止期按照下述规则自动生成：有效止期需手工录入
		$("#assigneeEndDate",navTab.getCurrentPanel()).attr("disabled",false);
	}
	
		
  }
  
}

