<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
 <script type="text/javascript">
 	function registerTaskForWard(id){
		navTab.openTab("20283","clm/register/toDataCollectInit_CLM_claimRegisterAction.action",{title:"立案登记",data:{caseId:id}});
	}
</script>
<div class="pageContent" id="registerSharePoolJsp">
	<!-- 个人池显示数据列表区域 -->
	<!-- 个人池分页查询访问路径 -->
	<form id="pagerForm" method="post"
		action="clm/register/findDirectSharePoolSelf_CLM_directSharePoolAction.action?leftFlag=0&menuId=${menuId }">
		<input type="hidden" name="pageNum" vaule="${directSharePoolResClientVOPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${directSharePoolResClientVOPage.pageSize}"/>
	</form>
	<form id="registerSharePoolJspFormSelf"
		action="clm/register/findDirectSharePoolSelf_CLM_directSharePoolAction.action?leftFlag=0&menuId=${menuId }"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm">
	<div >
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">个人工作池
					</h1>
				</div>
				
			<div class="tabdivclassbr">
				<table class="list sortable main_dbottom" width="100%">
					<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>赔案号</th>
						<th nowrap>签收机构</th>
						<th nowrap>报案方式</th>
						<th nowrap>服务商</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>签收日期</th>
					</tr>
				</thead>
				<tbody id="" align="center">
					<s:if test="directSharePoolResClientVOPage.PageItems == null || directSharePoolResClientVOPage.PageItems.size()==0">
						<tr>
							<td colspan="12">
								<div class="noRueryResult">没有符合条件的查询结果！</div>
							</td>
						</tr>
					</s:if>
					<!-- 循环显示数据 -->
						
					<s:iterator value="directSharePoolResClientVOPage.PageItems" status="st">
						<tr ondblclick="registerTaskForWard(${caseId})" style="height: 25px;" >
							<td><s:property value="#st.index + 1" /></td>
							<td>
								<span <s:if test ='isRedFlag=="1"'>style="color:red"</s:if>><s:property value="caseNo" /></span>
							</td>
							<td><Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
							<td><Field:codeValue value="${reportMode}" tableName="APP___CLM__DBUSER.T_REPORT_TYPE" /></td>
							<td><s:property value="serviceCommerce" /></td>
							<td><s:property value="customerName" /></td>
							<td><s:date name="signTime" format="yyyy-MM-dd"/></td>
						</tr>
					</s:iterator>
				</tbody>
				</table>
				<!-- 分页查询区域 -->
				<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
							name="select" onchange="navTabPageBreak({numPerPage:this.value},'registerSharePoolJsp')"
							value="directSharePoolResClientVOPage.pageSize">
						</s:select>
						<span>条，共${directSharePoolResClientVOPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab" rel="registerSharePoolJsp"
						totalCount="${directSharePoolResClientVOPage.total}"
						numPerPage="${directSharePoolResClientVOPage.pageSize}" pageNumShown="10"
						currentPage="${directSharePoolResClientVOPage.pageNo}"></div>
				</div>
			</div>
		</div>
		</form>
</div>