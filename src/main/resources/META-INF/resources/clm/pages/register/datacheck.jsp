<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript" src="clm/pages/register/datacheck.js"></script>
<form id="pagerForm" method="post"
	action="">
	<input type="hidden" name="pageNum" 

value="${currentPage.pageNo}" />
	<input type="hidden" name="numPerPage" 

value="${currentPage.pageSize}" />
</form>

<div class="pageHeader" layoutH="0">
	<form action="clm/register/saveDataCheck_dataCheckAction.action" method="post" onsubmit="return navTabSearch(this);">
	<div class="tabdivclassbr">
		<table class="list" width="100%" style="margin:10px;">
			<thead>
				<tr>
					<th nowrap>序号</th><th nowrap>理赔类型</th><th nowrap>险种</th><th nowrap>机构</th><th nowrap>操作人员</th><th nowrap>赔付金额(>)</th><th nowrap>时间段</th><th nowrap>抽取方式</th><th nowrap>操作</th>
				</tr>
			</thead>
			<tbody>
				<s:if test="dataCheckList.size() > 0">
					<s:iterator value="dataCheckList" status="st">
					<tr>
						<td>${ruleNo}</td><td>${claimsType }</td>
						<td></td><td></td><td>${operators}</td>
						<td>${damage}</td><td>${dateTime}</td><td>无</td>
						<td><a href="clm/register/deleteDataCheck_dataCheckAction.action?ruleNo=${ruleNo}" target="ajaxTodo">删除</a></td>
			  		</tr>
			  		</s:iterator>
			 	 </s:if> 
			</tbody>
		</table>
	</div>
	<div class="panelPageFormContent" >
    		<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">理赔类型
					</h1>
				</div>
            <div>
				<div class="pageFormContent">
					<s:iterator value="claimTypeList" status="st">
						<label><input type="checkbox" name="checkName" value="${claimType}"/>${typeDesc}</label>
       			 	</s:iterator>
       			 </div>
       			 <div>
       			 	<label style="margin-left:10px"><input type="checkbox" name="" class="checkboxCtrl" group="checkName"/>全选</label>
       			 </div>
        	</div>
    </div>
<!-- 	<fieldset style="margin:10px;"> -->
<!-- 		<legend>险种</legend> -->
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">险种
			</h1>
		</div>
		<table style="padding:20px 120px">
			<tr>
				<td>
					<div style="margin:10px">险种</div>
					<select  multiple="multiple" style="width:160px;height:150px;" id="select1">
					</select>
				</td>
				<td style="padding: 20px;">
					<br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="addOption(1)">></button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="addAll(1)">>></button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button"  style="width:25px" onclick="delOption(1)"><</button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="removeAll(1)"><<</button>
						</div>
					</div>
				</td>
				<td>
					<div style="margin:10px">参与复核的险种</div>
					<select  name="insureType" multiple="multiple" style="width:160px;height:150px;" id="select2">
					</select>
				</td>
			</tr>
		</table>
<!-- 	</fieldset> -->
<!-- 	<fieldset style="margin:10px;"> -->
<!-- 		<legend>机构</legend> -->
			<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">机构
			</h1>
		</div>
		<table style="padding:20px 120px">
			<tr>
				<td>
					<div style="margin:10px">机构</div>
					<select multiple="multiple" style="width:160px;height:150px;" id="select3">
					</select>
				</td>
				<td style="padding: 20px;">
					<br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="addOption(2)">></button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="addAll(2)">>></button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="delOption(2)"><</button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="removeAll(2)"><<</button>
						</div>
					</div>
				</td>
				<td>
					<div style="margin:10px">参与复核的机构</div>
					<select name="organizationName" multiple="multiple" style="width:160px;height:150px;" id="select4">
					</select>
				</td>
			</tr>
		</table>
<!-- 	</fieldset> -->
<!-- 	<fieldset style="margin:10px;"> -->
<!-- 		<legend>操作人员</legend> -->
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">操作人员
			</h1>
		</div>
		<table style="padding:20px 120px">
			<tr>
				<td>
					<div style="margin:10px">操作人员</div>
					<select multiple="multiple" style="width:160px;height:150px;" id="select5">
						<s:iterator value="userVOList" status="st">
							<option value="${userId }">${userId }-${realName }</option> 
						</s:iterator> 
					</select>
				</td>
				<td style="padding: 20px;">
					<br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="addOption(3)">></button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="addAll(3)">>></button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="delOption(3)"><</button>
						</div>
					</div>
					<br/><br/><br/>
					<div class="buttonActive">
						<div class="buttonContent">
							<button type="button" style="width:25px" onclick="removeAll(3)"><<</button>
						</div>
					</div>
				</td>
				<td>
					<div style="margin:10px">参与复核的操作人员</div>
					<select name="operator" multiple="multiple" style="width:160px;height:150px;" id="select6" >
					</select>
				</td>
			</tr>
		</table>
<!-- 	</fieldset> -->
	<div class="panel collapse" style="margin:10px;">
    		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">赔付金额
			</h1>
		</div>
           <div style="padding:20px;">赔付金额大于<input name="money"/>的赔案，需要进行复核</div>
    </div>
    <div class="panel collapse" style="margin:10px;">
    		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">时间段
			</h1>
		</div>
           <div style="padding:20px;">
				<div>
					<div class="pageFormContent">
						<label>重复</label>
						<label><input type="checkbox" name="c2" value="1"/>周一</label>
						<label><input type="checkbox" name="c2" value="2"/>周二</label>
						<label><input type="checkbox" name="c2" value="3"/>周三</label>
						<label><input type="checkbox" name="c2" value="4"/>周四</label>
						<label><input type="checkbox" name="c2" value="5"/>周五</label>
						<label><input type="checkbox" name="c2" value="6"/>周六</label>
	       			 	<label><input type="checkbox" name="c2" value="7"/>周日</label>
	       			 </div>
	       			 <div class="pageFormContent">
	       			 	<dl>
	       			 		<dt>开始时间</dt>
	       			 		<dd><input type="expandDateHmsRo" id="put1" name="data15" class="date" readonly="readonly" value=" "/></dd>
	       			 	</dl>
	       			 	<dl>
	       			 		<dd><input type="expandDateHmsRo" id="put2" name="data15" class="date" readonly="readonly" value=" "/></dd>
	       			 	</dl>
	       			 	<div class="buttonActive">
							<div class="buttonContent">
								<button type="button" onclick="Addtime()">添加</button>
							</div>
						</div>
	       			 </div>
	            </div>
	            <div>
					<table id="tab1" style="display:none" class ="list" border="0" cellspacing="0" cellpadding="0" width="50%">
							<tr>
								<th>重复</th><th>开始时间</th><th>结束时间</th><th>操作</th>
							</tr>
					</table>
				</div>
		  </div>
    </div>
    <div class="panel collapse" style="margin:10px;">
    	<h1>复核</h1>
           <div style="padding:20px;">每<input name="count"/>个案件复核一个</div>
    </div>
	<div class="formBarButton">
		<ul>
			<li>
				<button class="but_blue" type="submit" onclick="submits()">确定</button>
			</li>
			<li>
				<button class="but_gray" type="button" onclick="">退出</button> 
			</li>
		</ul>
	</div>
	</form>
</div>
