<%@ page language="java" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script src="clm/pages/register/dist/echarts.js"></script>
<script src="clm/pages/register/epibolyMonitoringInit.js"></script>
<script type="text/javascript">
$("#accordionPos>div:eq(3)").find("ul").find("li").each(function(){
	$(this).find("ul").find("li:eq(0)").find("ul").find("li").each(function(){
		if($(this).find("div").find("button").html()=="外包案件池"){
			var id=$(this).find("div").find("a").attr("id").split("Id")[1];
			$("#toOutSourceCasePoolIni").attr("rel",id);
		}
	});
});
function queryOutsourece(){
	navTab.openTab("20524", "clm/register/outSourceCasePoolIni_CLM_claimOutSourceManageAction.action", {title:'外包案件池'});
}
function isNorE(s){
	if(s==null||s==""){
		return true;
	}else{
		return false;
	}
}
</script>
<div class="main_tabsContent"  layoutH="10">
<!-- 	<div > -->
		<div id="outsourceCaseTable">
<%-- 			<%@ include file="epibolyMonitoringInitTable.jsp"%> --%>
		   <s:include value="epibolyMonitoringInitTable.jsp" />
		</div>
<!-- 	</div> -->
<!-- 	<div > -->
		<div id="qwer">
<%-- 		<%@ include file="epibolyMonitoringInitAndFee.jsp"%> --%>
			<s:include value="epibolyMonitoringInitAndFee.jsp" />
		</div>
<!-- 	</div> -->
	<div class="formBarButton">
		<ul>
			<li>
				<button title="外包案件池" class="but_blue" id="toOutSourceCasePoolIni" target="navTab" onclick="queryOutsourece()">进入外包案件池</button>
<!-- 				<a type="button" target="navTab" rel="" title="外包案件池" id="toOutSourceCasePoolIni" -->
<!-- 						class="but_blue main_buta"	href="clm/register/outSourceCasePoolIni_CLM_claimOutSourceManageAction.action?leftFlag=1&menuId=20524">进入外包案件池</a> -->
			</li>
			<li><button class="but_gray" type="button" onclick="exit()">退出</button></li>
		</ul>
	</div>
</div>
<%-- <h1>
	图表产生间隔&nbsp;&nbsp;&nbsp;外包商
		<s:select name="outsourceIfnoVO.outsourceName1"
			value="outsourceIfnoVO.outsourceName" list="outsourceIfnoVOList"
			listKey="outsourceName" listValue="outsourceName" headerKey=""
			headerValue="全部" onchange="showchart(this);">
		</s:select>
		<input type="radio"
		name="jiange" onclick="change(this)" value="按天">按天 <input
		type="radio" name="jiange" onclick="change(this)" value="按周">按周<input
		type="radio" name="jiange" onclick="change(this)" value="按月">按月
</h1>

<div id="main" style="height: 400px; border: 0px; display: none;"></div> --%>
