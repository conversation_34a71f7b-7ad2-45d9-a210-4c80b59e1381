<%@ page language="java" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script src="clm/pages/register/dist/echarts.js"></script>
<script src="clm/pages/register/epibolyMonitoringInit.js"></script>
<script type="text/javascript">
	
</script>
			<form id="pagerForm" method="post" 
				action="clm/register/queryLineChartInfoAndFee_CLM_epibolyMonitoringAction.action">
				<input type="hidden" name="pageNum" vaule="${currentPage1.pageNo} " />
				<input type="hidden" name="numPerPage" value="${currentPage1.pageSize}" />
	        </form>
<!-- <div > -->
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">外包费用统计
					</h1>
				</div>
			<form
				action="clm/register/queryLineChartInfoAndFee_CLM_epibolyMonitoringAction.action"
				method="post" class="pageForm required-validate" rel="pagerForm"
				id="queryOutsourceCaseIdMoney" onsubmit="return  navTabSearch(this,'qwer')">
				<div class="pageFormInfoContent">
					<dl >
						<dt>管理机构</dt>
						<dd>
							<input style="width: 30px;border-right:0px" type="text" size="2" name="outsourceIfnoVO.organCode"
								id="menuBtn1" value="${outsourceIfnoVO.organCode}" class="organ" clickId="monFeeBtn"
								showOrgName="branchname1" needAll="true" /> <input
								style="width:90px;" type="text" size="11" name="outsourceIfnoVO.organName"
								id="branchname1" value="${outsourceIfnoVO.organName}" readOnly class="" />
							<a id="monFeeBtn" class="btnLook" href="#" style="position: relative;"></a>
						</dd>
					</dl>
					<dl >
						<dt><font class="point" color="red">* </font>统计年份</dt>
<%-- 						<input value="${outsourceCaseVO.countTime}" type="hidden" id="countTimeYearId"/> --%>
						<dd>
							<select class="combox title"  name="outsourceCaseVO.year" id='yearId' >
								<s:iterator value="outsourceCaseVOList" status="var">
									<option value="${countTime}">${countTime}</option>
								</s:iterator>
							</select>
						</dd>
					</dl>
					<dl >
						<dt><font class="point" color="red">* </font>统计月份</dt>
						<dd>
							<select class="combox title" name="outsourceCaseVO.mounth"  id='mounthId' >
								<option <s:if test="outsourceCaseVO.mounth eq '01'">selected='true'</s:if> value='01'>1</option>
								<option <s:if test="outsourceCaseVO.mounth eq '02'">selected='true'</s:if> value='02'>2</option>
								<option <s:if test="outsourceCaseVO.mounth eq '03'">selected='true'</s:if> value='03'>3</option>
								<option <s:if test="outsourceCaseVO.mounth eq '04'">selected='true'</s:if> value='04'>4</option>
								<option <s:if test="outsourceCaseVO.mounth eq '05'">selected='true'</s:if> value='05'>5</option>
								<option <s:if test="outsourceCaseVO.mounth eq '06'">selected='true'</s:if> value='06'>6</option>
								<option <s:if test="outsourceCaseVO.mounth eq '07'">selected='true'</s:if> value='07'>7</option>
								<option <s:if test="outsourceCaseVO.mounth eq '08'">selected='true'</s:if> value='08'>8</option>
								<option <s:if test="outsourceCaseVO.mounth eq '09'">selected='true'</s:if> value='09'>9</option>
								<option <s:if test="outsourceCaseVO.mounth eq '10'">selected='true'</s:if> value='10'>10</option>
								<option <s:if test="outsourceCaseVO.mounth eq '11'">selected='true'</s:if> value='11'>11</option>
								<option <s:if test="outsourceCaseVO.mounth eq '12'">selected='true'</s:if> value='12'>12</option>
							</select>
						</dd>
					</dl>
					<dl >
						<dt>外包方式</dt>
						<dd>
							<s:select list="#{'':'显示全部',1:'驻场外包',2:'离场外包',3:'数采自采'}" id="outsourceWay"
								cssClass="notuseflagDelete combox title comboxDD" name="outsourceCaseVO.outsourceWay">
							</s:select>
						</dd>
					</dl>
					<div class="pageFormdiv">
						<button class="but_blue" onclick="queryOutsourceCaseIdMoney()" type="button" >查询</button>
					</div>
				</div>
	<div class="tabdivclassbr main_tabdiv">			
	<div style="overflow: auto">
	<table class="list main_dbottom" style="width: 100%;"   >
		<thead>
			<tr align="center">
				<th nowrap>管理机构</th>
				<th nowrap>外包商</th>
				<th nowrap>外包方式</th>
<!-- 				<th nowrap>发包件数</th> -->
<!-- 				<th nowrap>回传成功件数</th> -->
<!-- 				<th nowrap>未回传件数</th> -->
<!-- 				<th nowrap>回传平均时效(H)</th> -->
			    <th nowrap>回复件数（医疗案件）</th>
				<th nowrap>字节数（医疗案件）</th>
				<th nowrap>医疗案件单价</th>
				<th nowrap>回复件数（非医疗案件）</th>
				<th nowrap>字节数（非医疗案件）</th>
				<th nowrap>非医疗案件单价</th>
				<th nowrap>扣费金额</th>
				<th nowrap>应付外包费用</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="imageFlag != null">
							<tr>
								<td colspan="14">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage1.PageItems == null || currentPage1.PageItems.size()==0">
							<tr>
								<td colspan="14">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
			<s:iterator value="currentPage1.PageItems" var="outsourceVar">
				<tr >
					<td><Field:codeValue value="${organCode}" tableName="APP___CLM__DBUSER.t_udmp_org" /></div></td>
					<td>${outsourceName}</td>
					<td><Field:codeValue value="${outsourceWay}"
									tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY" /></td>	
					<td>${replyNumber}</td>
					<td>${simpleNumber}</td>
					<td>${simpleCasePrice}</td>
					<td>${unReplyNumber}</td>
					<td>${unSimpleNumber}</td>
					<td>${unSimpleCasePrice}</td>
					<td>${deductMoney}</td>
					<td>${expensesNumber}</td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	</div>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
				name="select" onchange="navTabPageBreak({numPerPage:this.value},'qwer')"
				value="currentPage1.pageSize">
			</s:select>
			<span>条，共${currentPage1.total}条</span>
		</div>
		<div class="pagination" targetType="navTab"
			totalCount="${currentPage1.total}"
			numPerPage="${currentPage1.pageSize}" pageNumShown="10"
			currentPage="${currentPage1.pageNo}" rel="qwer">
		</div>
	</div>
	</div>
	</form>
	    <div style="margin-top: 10px">
				<h1>
					<span style="color: red">&nbsp;&nbsp;&nbsp;注:外包监控中的应付外包费用仅做为参考，实付外包费用以最终结算结果为准。</span>
				</h1>
	    </div>
