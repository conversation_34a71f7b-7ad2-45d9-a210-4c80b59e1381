<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript" src="clm/pages/register/registerCheckConfirm.js"></script>
<div class="main_tabdiv" id="registerCheckconfirmDiv">
	<form id="registerCheckconfirmForm" class="pageForm required-validate" method="post"
	               onsubmit="return validateCallback(this, myAjaxDown);"  novalidate="novalidate">
		<div class="panelPageFormContent">
				<dl>
					<dt>赔案号</dt>
					<dd>
						<input name="" class="textInput readonly" type="text" size="" readonly="readonly" value="${claimCaseVO.caseNo }"/>
					</dd>
				</dl>
				<dl>
					<dt>事件号</dt>
					<dd>
						<input name="" class="textInput readonly" type="text" size="" value="${claimAccidentVO.accidentNo }" readonly="readonly"/>
					</dd>
				</dl>

				<input type="hidden" name="caseId" value="${caseId }"/>
				<input type="hidden" name="caseNo" value="${claimCaseVO.caseNo }"/>
				<input type="hidden" id="oldCheckListFlag" name="oldCheckListFlag" value="${claimCaseVO.oldCheckListFlag}"/>
			</div>
			<div class="main_tabdiv">
				<table class="list nowrap itemDetail myClassFlag " addButton="添加单证" width="100%" id="list">
					<thead>
						<tr>
<!-- 							<th type="enum" enumUrl="clm/pages/register/checkbox.html">选择</th> -->
							<th type="enum" name="claimChecklistVOs[#index#].checklistCode" enumUrl="clm/pages/register/checkCode.jsp">单证代码</th>
							<th type="enum" enumUrl="clm/pages/sign/input.jsp">单证名称</th>
							<th type="enum" name="claimChecklistVOs[#index#].docType" enumUrl="clm/pages/register/submitFormat.jsp">提交形式</th>
							<th type="enum" enumUrl="clm/pages/register/registerPageCount.jsp" class='required'>页数</th>
							<th type="enum" enumUrl="clm/pages/register/isReturnOriginal.html">是否退还原件</th>
							<th type="enum" enumUrl="clm/pages/register/checkExamineConclusion.html">单证检查结论</th>
							<th type="text" size="30" name="claimChecklistVOs[#index#].lackReason">不齐全原因</th>
							<th type="enum" enumUrl="clm/pages/register/operate.html">操作</th>
						</tr>
					</thead>
					<tbody id="registerPage">
						<s:if test="claimChecklistVOList.size() != 0">
							<s:iterator value="claimChecklistVOList" status="status" var="sta">
							<tr>
<%-- 								为了全选功能修改name属性，如果有其他功能不能实现了请修改回来。<input type="checkbox" flag="flag" name="claimChecklistVOs[${status.index }].checked" /> --%>
								<!-- <td  style="display: none;"><input type="checkbox" flag="flag" name="claimChecklistVOs" /></td> -->
								<td>
									<select class="combox selectToInputdelete checkCode" style = "width:100px;" name="claimChecklistVOs[<s:property value="#status.index"/>].checklistCode">
										<option value="-1" class=" ">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </option> 
										<s:iterator value="checklistVOs" status="status1" var="checklistVO">
											<s:if test="#sta.checklistCode==#checklistVO.code">
												<option id="change" value='<s:property value="#checklistVO.code"/>' class='<s:property value="#checklistVO.name"/>' selected><s:property value="#checklistVO.code"/></option>
											</s:if>
											<s:else>
												<option value='<s:property value="#checklistVO.code"/>' class='<s:property value="#checklistVO.name"/>'>
												  <s:property value="#checklistVO.code" />-<s:property value="#checklistVO.name"/>
												</option>
											</s:else>
										</s:iterator>
									</select>
								</td>
								<td>
									<input type="text" name="" value='<Field:codeValue tableName="APP___CLM__DBUSER.T_CHECKLIST" value="${sta.checklistCode}"/>' size=60 readonly="readonly"/>
								</td>
								<td>
									<label>
										<span>
											<input type="radio" name="claimChecklistVOs[${status.index }].docSubmMode" value="1" <s:if test="docSubmMode == 1">checked="checked"</s:if> onclick="updateRegisterCheckSubmitFormat(this);"/>原件
										</span>
										<span>
											<input type="radio" name="claimChecklistVOs[${status.index }].docSubmMode" value="2" <s:if test="docSubmMode == 2">checked="checked"</s:if> onclick="updateRegisterCheckSubmitFormat(this);"/>复印件
										</span>
										<input type="hidden" name="claimChecklistVOs[<s:property value="#status.index"/>].isManual" value="<s:property value="#sta.isManual"/>">
									</label>
								</td>
								<td><input type="text" size="2" name="claimChecklistVOs[${status.index }].listUnit" value="${listUnit }" onkeyup="this.value=this.value.replace(/\D/g,'')"/></td>
								<td>
									<label>
										<span>
											<input type="radio" name="claimChecklistVOs[${status.index }].isChecked" value="1" <s:if test="isChecked == 1">checked="checked"</s:if>/>是
										</span>
										<span>
											<input type="radio" name="claimChecklistVOs[${status.index }].isChecked" value="0" <s:if test="isChecked == 0">checked="checked"</s:if>/>否
										</span>
									</label>
								</td>
								<td>
									<label>
										<span>
											<input type="radio" name="claimChecklistVOs[${status.index }].checklistOption" value="1" <s:if test="checklistOption == 1">checked="checked"</s:if>/>齐全
										</span>
										<span>
											<input type="radio" name="claimChecklistVOs[${status.index }].checklistOption" value="2" <s:if test="checklistOption == 2">checked="checked"</s:if>/>不齐全
										</span>
									</label>
								</td>
								<td><input type="text" size="30" name="claimChecklistVOs[${status.index }].lackReason" value="${lackReason }"/></td>
								<td><a class="btnDel"></a></td>
							</tr>
							</s:iterator>
						</s:if>
						<s:elseif test="checklistVOList.size() != 0">
							<s:iterator value="checklistVOList" status="status" var="sta">
							<tr>
<%-- 								<td>为了全选功能修改name属性，如果有其他功能不能实现了请修改回来。<input type="checkbox" flag="flag" name="claimChecklistVOs[${status.index }].checked" /> --%>
<!-- 								<input type="checkbox" flag="flag" name="claimChecklistVOs" /></td> -->
								<td>
									<select class="combox selectToInputdelete checkCode" style = "width:100px;" name="claimChecklistVOs[<s:property value="#status.index"/>].checklistCode">
										<option value="-1" class=" ">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </option> 
										<s:iterator value="checklistVOs" status="status1" var="checklistVO">
											<s:if test="#sta.code==#checklistVO.code">
												<option id="change" value='<s:property value="#checklistVO.code"/>' class='<s:property value="#checklistVO.name"/>' selected><s:property value="#checklistVO.code"/></option>
											</s:if>
											<s:else>
												<option value='<s:property value="#checklistVO.code"/>' class='<s:property value="#checklistVO.name"/>'>
												  <s:property value="#checklistVO.code" />
												</option>
											</s:else>
										</s:iterator>
									</select>
								</td>
								<td>
									<input type="text" name="" value='<Field:codeValue tableName="APP___CLM__DBUSER.T_CHECKLIST" value="${sta.code}"/>' size=60/>
								</td>
								<td>
									<label>
									<span>
										<input type="radio" name="claimChecklistVOs[${status.index }].docSubmMode" value="1" checked="checked" onclick="updateRegisterCheckSubmitFormat(this);"/>原件
									</span>
									<span>
										<input type="radio" name="claimChecklistVOs[${status.index }].docSubmMode" value="2" onclick="updateRegisterCheckSubmitFormat(this);"/>复印件
									</span>	
										<input type="hidden" name="claimChecklistVOs[<s:property value="#status.index"/>].isManual" value="0">
									</label>
								</td>
								<td><input type="text" size="2" name="claimChecklistVOs[${status.index }].listUnit" onkeyup="this.value=this.value.replace(/\D/g,'')"/></td>
								<td>
									<label>
									<span>
										<input type="radio" name="claimChecklistVOs[${status.index }].isChecked" value="1" />是
									</span>
									<span>
										<input type="radio" name="claimChecklistVOs[${status.index }].isChecked" value="0" checked="checked"/>否
									</span>
									</label>
								</td>
								<td>
									<label>
									<span>
										<input type="radio" name="claimChecklistVOs[${status.index }].checklistOption" value="1" checked="checked"/>齐全
									</span>
									<span>
										<input type="radio" name="claimChecklistVOs[${status.index }].checklistOption" value="2"/>不齐全
									</span>
									</label>
								</td>
								<td><input type="text" size="30" name="claimChecklistVOs[${status.index }].lackReason"/></td>
								<td><a class="btnDel"></a></td>
							</tr>
							</s:iterator>
						</s:elseif>
					</tbody>
				</table>
				<div class="pageFormdiv main_tabdiv main_bottom">
					<button class="but_blue" type="button" style="font-weight: bold;" onClick="addTab1();">添加单证</button>
	<!-- 				<input type="text" name="dwz_rowNum_bak" class="textInput_bak" style="margin:2px;float:left;" value="1" size="2"/> -->
				</div>
			</div>
	<div class="formBarButton main_bottom">
			<ul>
				<li> <button class="but_blue" type="button" onclick="saveCheck(2)">上一步</button> </li>
				<li> <button class="but_blue" type="button" onclick="registerInvoicesScan()">扫描</button> </li>
				<li> <button class="but_blue" type="button" onclick="saveCheck(0);">保存</button> </li>
				<li> <button class="but_blue" type="button" title="赔案号条形码" onclick="registerPrintDocuments('${caseId}');">打印</button> </li>
				<li> <button class="but_blue" type="button" id="registerCheckNext" onclick="saveCheck(1);">下一步</button> </li>
				<li> <button class="but_gray" type="button" onclick="exit()">退出</button></li>
			</ul>
		</div>
	</form>
</div>
<div id="signPrintShow" style="display: block ;width: 0px; height: 0px"></div>
<script>
if(document.readyState=="complete"){  
	var currentPageId= <%=session.getAttribute("currentPageId") %>;
	 if(currentPageId!="" && currentPageId>2 ){
		 onlyReadRegisterCheckConfirm();
	}
}  
function myAjaxDown(ajaxMsg){
	 if(ajaxMsg.statusCode == DWZ.statusCode.error) {
			if(ajaxMsg.message && alertMsg) alertMsg.error(ajaxMsg.message);
	    }  else {
	    	if(presave == 1){//上一步的标识
	    		presave = 0;
	    		prev('11','${claimCaseVO.caseId}');
	    	}else if(ajaxMsg.message && alertMsg){
	    		alertMsg.correct(ajaxMsg.message);
	    	}else{
		    	next('13','confirmForm','${claimCaseVO.caseId}');
	    	}
		};
}
$(document).ready(function(){
	$(".checkCode" ).die().live("change",function(){
		var a = $(this).find("#change");
		a.attr("id","");
		a.empty();
		a.append(a.attr("value")+"-"+a.attr("class"));
		var index = $(this).context.selectedIndex;
		var checkCode = $(this).find("option").eq(index);
		$(this).prev().text(checkCode.attr("value"));
		$(this).parent().parent().parent().next().find("input").attr("value",checkCode.attr("class"));
		checkCode.empty();
		checkCode.append(checkCode.attr("value"));
		checkCode.attr("id","change");
	});
});
</script>
