<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%
	String accType = request.getParameter("accType");
	String id = request.getParameter("id");
%>

<script type="text/javascript">
	  
	///该配按下产品定义的所有费用业务类型项目
  	var medicalFeeTypeList = "${medicalFeeTypeList}";
  	//alert(medicalFeeTypeList);
	var medicalFeeTypeMap = medicalFeeTypeList.substring(1,
			medicalFeeTypeList.length - 1)
			+ "";
	var feeItemList = medicalFeeTypeMap.split(","); 

	function calcAmountThis(obj) {
			// alert('111');
	    if (obj.value < 0) { //不能录入负数
	    	$(obj).val("");
	    }
		var tdList = $(obj).parent().parent().find("td");
		var medFeeAmount = $(obj).val();
		var otherAmount = tdList.eq(9).find("input").val();
		var dutyFeeItem = tdList.eq(1).find("option:selected").val();
	
		var dutyFeeItemName = tdList.eq(1).find("option:selected").html();
		var exitValue = false;

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		if(feeItemList.length==1 && feeItemList[0] == ""){
			exitValue = true;
		}else{
			for (var i = 0; i < feeItemList.length; i++) {
				//alert(dutyFeeItem+"---"+feeItemList[i]);
				if (trim(dutyFeeItem) == trim(feeItemList[i])) {
					//alert(dutyFeeItem+"---"+feeItemList[i]);
					exitValue = true;
				}
			}
		}
		//如果未定义 扣除金额自动调整为等于费用金额。  扣除原因自动记录为其他。扣除备注自动记录为：扣除【费用类型名称】【费用金额】元。
		if (!exitValue == true) {
			otherAmount = medFeeAmount;
			 var vaseStatus = '${claimCaseVO.caseStatus}';
			  if( vaseStatus <= 31){
				$(obj).parent().parent().find("td").eq(9).find("input").val(medFeeAmount);	
				$(obj).parent().parent().find("td").eq(10).find("input").val(
						"扣除【" + dutyFeeItemName + "】【" + otherAmount + "】元");
			  }
			/* $(obj).parent().parent().find("td").eq(6).find("select").val("2");*/
 
			
		}else{
			$(obj).parent().parent().find("td").eq(9).find("input").val("0");
		}

		$(obj).parent().parent().find("td input.calcAmount").val(medFeeAmount - otherAmount);
		
		calcAmount2($(obj).parent().parent().find("td").eq(6).find("input"));
	}
	
	///高端/防癌医疗 
		function calcAmountThisHC(obj) { 
		 
		var medFeeAmount = $(obj).val();
		var deductAmount = $(obj).parent().parent().find("td").eq(5).find(
				"input").val();
		var dutyFeeItem = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").val();
	
		var dutyFeeItemName = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").html();
		var exitValue = false;

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		for (var i = 0; i < feeItemList.length; i++) {
			//alert(dutyFeeItem+"---"+feeItemList[i]);
			if (trim(dutyFeeItem) == trim(feeItemList[i])) {
				 	//alert(dutyFeeItem+"---"+feeItemList[i]);
				exitValue = true;
			}
		}
		 
		//如果未定义 扣除金额自动调整为等于费用金额。  扣除原因自动记录为其他。扣除备注自动记录为：扣除【费用类型名称】【费用金额】元。
		if (!exitValue) {
			deductAmount = medFeeAmount;
			$(obj).parent().parent().find("td").eq(5).find("input").val(
					medFeeAmount);
			var vaseStatus = '${claimCaseVO.caseStatus}';
			  if( vaseStatus <= 31){
			$(obj).parent().parent().find("td").eq(6).find("select").selectMyComBox("3");
			 
			$(obj).parent().parent().find("td").eq(7).find("input").val(
					"扣除【" + dutyFeeItemName + "】【" + deductAmount + "】元");				  
			  }
		}else{
			$(obj).parent().parent().find("td").eq(5).find("input").val("0");
		}

		$(obj).parent().parent().find("td").eq(8).find("input").val(
				medFeeAmount - deductAmount);
	}
	
		
	function queryCalimtreatType(obj) {
		if($("#treatStart", navTab.getCurrentPanel()).val()!='' && $(obj).val()=='0'){
			$("#treatEnd", navTab.getCurrentPanel()).val($("#treatStart", navTab.getCurrentPanel()).val());
		}
	}
	function calcAmount2(obj) {
		if (obj.value < 0) { //不能录入负数
	    	$(obj).val("");
	    }
		var tdList = $(obj).parent().parent().find("td");
	    //自费费用
		var expenseAmount = $(obj).parent().parent().find("td").eq(5).find("input").val();
		//自付费用
		var payAmount= $(obj).parent().parent().find("td").eq(7).find("input").val();
		//其他费用
		var otherAmount = $(obj).parent().parent().find("td").eq(9).find("input").val();
		//总费用
		var medFeeAmount = $(obj).parent().parent().find("td").eq(4).find(
				"input").val();
		if(expenseAmount==''){
			expenseAmount=0;
		}
		if(payAmount==''){
			payAmount=0;
		}
		if(otherAmount==''){
			otherAmount=0;
		}
		
		 
		var dutyFeeItem = $(obj).parent().parent().find("td").eq(1).find("option:selected").val(); 

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		/* for (var i = 0; i < feeItemList.length; i++) {
			if (trim(dutyFeeItem) == trim(feeItemList[i])) {
				exitValue = true;
			}
		} */ 
		$(obj).parent().parent().find("td input.calcAmount").val(medFeeAmount-payAmount-expenseAmount-otherAmount);
	}
	
	function clickControlRadio(obj) {
		var tdList = $(obj).parent().parent().find("td");
		//控费金额
		var dutyControlAmount = tdList.eq(11).find("input").val();
		var dutyControlRemark = tdList.eq(12).find("input").val();
		if(obj.value=='1'){
			tdList.eq(9).find("input").val(dutyControlAmount);
			tdList.eq(10).find("input").val(dutyControlRemark);
		}else{
			tdList.eq(9).find("input").val("");
			tdList.eq(10).find("input").val("");
		}
		//触发其他费用的change事件
		calcAmount2(obj);
	} 
	
	function calcAmount2HC(obj) {
		if (obj.value < 0) { //不能录入负数
	    	$(obj).val("");
	    }
		var tdList = $(obj).parent().parent().find("td");
	 	//总费用
		var medFeeAmount = $(obj).parent().parent().find("td").eq(4).find(
				"input").val();
		 
		$(obj).parent().parent().find("td").eq(8).find("input").val(medFeeAmount-obj.value);
	}
	
	  function cleanInput(obj){
		  var vaseStatus = '${claimCaseVO.caseStatus}';
		  if( vaseStatus <= 31){
			  var tdList = $(obj).parent().parent().find("td"); 
				for(var i=2;i<tdList.size();i++){
					tdList.eq(i).find("input").val(""); 
				} 
		  }
	}  
	
	

	function validateCallbackBillDuty(form, callback, confirmMsg) {
		
		// ******* 超时监控 Add HYP
		if (JudgeTimeOut()) {
			DWZ.loadLogin();
			return false;
		}
		// form 校验 若校验失败不允许提交
		var $form = $(form);
		if (!$form.valid()) {
			return false;
		}
		
		
		// ********添加自定义判断程序 add LiAnDong 
		var myOption = $form.attr("myOption");
		if (null != myOption && myOption != '') {
			var myFlag = eval(myOption);
			if (!myFlag)
				return myFlag;
		}
		
		var result = {data:$form.serializeArray()};
		var parent = $.pdialog.getCurrent() || navTab.getCurrentPanel();
		// *******交易管理监控 Add HYP
		if(dealSwitch){
			result = addHiddenAttrs(result, parent);
		}
		// 按钮添加审计功能
		if (btnSwitch) {
			result = addBtnHiddenAttrs(result, parent);
		}
		
		/*start  在请求参数中获取页面token ADD BY tanzl*/
		var $page = $(form).parents(".dialog");
		if($page.length == 0){
			$page = $(form).parents(".unitBox");
		}
		var tokenval=$page.find("input[name='token']").val();
		var tokenKey=$page.find("input[name=jspname]").val();
		//表单提交获取token和校验token标志
		result.data.push({'name':'token','value':tokenval});
		result.data.push({'name':'tokenKey','value':tokenKey});
		result.data.push({'name':'checkTokenFlag','value':'1'});
		/*token end*/
		
		var _submitFn = function() {
			$form.find(':focus').blur();
			
			$.ajax({
				type : form.method || 'POST',
				url : $form.attr("action"),
				data : result.data,
				dataType : "json",
				cache : false,
				success : callback || DWZ.ajaxDone,
				error : DWZ.ajaxError
			});
		}
		if (confirmMsg) {
			alertMsg.confirm(confirmMsg, {
				okCall : _submitFn
			});
		} else {
			_submitFn();
		}
		return false;
	}

	
function   countOutPatients(obj){
	//治疗类型
	  var   treatType=$(obj).parent().parent().parent().find("#cureType").val();
	//医院名称
	 var   hospitalId=$(obj).parent().parent().parent().find("#hospitalId").val();
	//开始时间treatStart
	 var   treatStart=$(obj).parent().parent().parent().find("#treatStart").val();
	//就诊科室
	 var   medicalDeptCode=$(obj).parent().parent().parent().find("#medicalDeptCode").val();
	  
	 var   case_id=$(obj).parent().parent().parent().find("#caseId").val();
	  
	 if(treatType=="0"&&hospitalId!=""&&treatStart!=""&&medicalDeptCode!=""&&case_id!=""){
	 
		  $.ajax({
				'type':'post',
				'url':'clm/register/countOutPatients_CLM_tClaimDutyRegisterAction.action?claimBillVO.caseId='+caseId+"&claimBillVO.treatType="+treatType+"&claimBillVO.hospitalId="+hospitalId+"&claimBillVO.treatStart="+treatStart+"&claimBillVO.medicalDeptCode="+medicalDeptCode,
			    'datatype':'json', 
				'success':function(data){
				 	var data = eval("(" + data + ")"); 
					   $(obj).parent().parent().parent().find("#outServiceNum").val(data.claimBillLength+1);
				 	} ,'error':function(){
					alertMsg.error("查询失败!");
				}
			});
	  
	  }
}


function deleteDetail(obj){
	var $button=$(obj).parent().parent().parent().parent().parent().find("button");
	if($(obj).parent().parent().siblings().length>0){
	     $(obj).parent().parent().remove();
	}else{ 
		$(obj).parent().parent().remove();
		$button.click();
	};
	 
}

//是否以社保身份就医
function sociMedicalTreatmentIsChecked(obj){
	if($(obj).attr("checked")== "checked"){
		$("#sociMedicalTreatment", navTab.getCurrentPanel()).val("1");
	}else{
		$("#sociMedicalTreatment", navTab.getCurrentPanel()).val("0");
	}
}


var sociMedicalTreatment = "${claimBillVO.sociMedicalTreatment}";
if(sociMedicalTreatment == 1){
	$("#sociMedicalTreatmentChecked", navTab.getCurrentPanel()).attr("checked",true);
}



//治疗类型门诊清空住院天数，住院清空门诊次数
$(document).ready(function(){
	  $("#cureType" ).die().live("change",function change(){
		     var cureType = $("#cureType", navTab.getCurrentPanel()).val();
		     if (cureType == 0){
		         $("#outpatientTreatTypeDLIdVO").css("display","none");
		    	 $("#outpatientTreatTypeDLId").css("display","block");
		     } else {
		    	 $("#outpatientTreatTypeDLIdVO").css("display","none");
		    	 $("#outpatientTreatTypeDLId").css("display","none");
		     }
		  	 //获取开始日期
		     var startTime = $("#treatStart", navTab.getCurrentPanel()).val().replace(/-/g, '-');
			 //获取结束日期
			 var endTime=$("#treatEnd", navTab.getCurrentPanel()).val().replace(/-/g, '-');
		  	 if(cureType==1){//住院
		  		$("#outServiceNum", navTab.getCurrentPanel()).val("");
		  	    if ($("#stayDaysHiddenId", navTab.getCurrentPanel()).val() != null && $("#stayDaysHiddenId", navTab.getCurrentPanel()).val()!="") {
		  	    	$("#stayDays", navTab.getCurrentPanel()).val($("#stayDaysHiddenId").val());
		  	    }
		  	    //自动算天数
		  	    if (startTime != "" && endTime != "") {
		  	    	var startTimes = startTime.split("-");
					var dt3 = new Date(startTimes[0],startTimes[1] - 1,startTimes[2]); 
					var endTimes = endTime.split("-");
					var dt4 = new Date(endTimes[0],endTimes[1] - 1,endTimes[2]);
		  	    	var dif = dt4.getTime() - dt3.getTime();
					var days = Math.round(dif / (24 * 60 * 60 * 1000));
		  	    	$("#stayDays", navTab.getCurrentPanel()).attr("value", days);
					$("#stayDaysHiddenId", navTab.getCurrentPanel()).attr("value", days);
		  	    }
		  	 }else if(cureType=='0'){//门诊
		  		$("#stayDays", navTab.getCurrentPanel()).val("");
		  		if ($("#outServiceNumHiddenId", navTab.getCurrentPanel()).val() != null && $("#outServiceNumHiddenId").val()!="") {
		  	    	$("#outServiceNum", navTab.getCurrentPanel()).val($("#outServiceNumHiddenId", navTab.getCurrentPanel()).val());
		  	    }
				if (endTime != "") {
					if (endTime >= startTime) {
							if (endTime > startTime) {
								alertMsg.error("如果治疗类型是“门诊”，开始日期必须等于结束日期！");
								$("#treatStart", navTab.getCurrentPanel()).val("");
								$("#treatEnd", navTab.getCurrentPanel()).val("");
								return;
							}
				    }
				}
		  	 }
	  });  
	  
}); 

if($("#accResultOneId", navTab.getCurrentPanel()).val()==""){
	$("#accResultOneId", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult1Id", navTab.getCurrentPanel()).val()); 
	$("#accResultOne", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult1", navTab.getCurrentPanel()).val()); 
}
if($("#accResultTTwoId", navTab.getCurrentPanel()).val()==""){
	$("#accResultTTwoId", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult2Id", navTab.getCurrentPanel()).val()); 
	$("#accResultTTwo", navTab.getCurrentPanel()).val($("#claimAccidentResultVOaccResult2", navTab.getCurrentPanel()).val()); 
}
if($("#hospitalId", navTab.getCurrentPanel()).val()==""){
	$("#hospitalId", navTab.getCurrentPanel()).val($("#claimCaseResultVOHospResultId", navTab.getCurrentPanel()).val()); 
	$("#cureHospital",navTab.getCurrentPanel()).val($("#claimCaseResultVOHospResult", navTab.getCurrentPanel()).val()); 
}
if($("#hospitalLevelName", navTab.getCurrentPanel()).val()==""){
	$("#hospitalLevelName", navTab.getCurrentPanel()).val($("#hospitalResultVOHospLevResultId").val()); 
}



function onEmptyChangeId(obj){
	if($(obj).val()==""){
		 $(obj).prev("#operationCodeId").val("");
	}
	
}
function onEmptyChangeId(obj){
	if($(obj).val()==""){
		 $(obj).prev("#drugDetailCodeId").val("");
	}
	
}
//高端医疗初始化调用



var pageFlag = navTab.getCurrentPanel().find("[name='pageFlag']").val();
if(pageFlag == 'highMedical'){
	liabTypeData();
}

//选择申请保项时，保项费用根据申请保项展示
function liabTypeData() {
	var liabType = $("#liabType", navTab.getCurrentPanel());
	 var $obj=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel());
	var appLiab = $obj.find("#appLiab").val();
	var liabType1 = $obj.find("#liabType").val();
	//var appLiab = $().parent().parent().parent().find("#appLiab").val();
	if(appLiab == ""){
		$("#liabType", navTab.getCurrentPanel()).empty();
		$("#liabType", navTab.getCurrentPanel()).attr("disabled","true");
	}else {
		$("#liabType", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
	}
	$("#appLiab", appLiab);
	$("#liabType", navTab.getCurrentPanel()).prev().val(""); 
	//var NullList = "3456";
		$.ajax({
			'type' : 'post',			
			'url' : 'clm/register/findSpeLiabType_CLM_tClaimDutyRegisterAction.action?claimBillVO.appLiab='+appLiab,
			'datatype' : 'json',
			'async' : true,
			'success' : function(data) {			
				var data = eval("(" + data + ")");
				$("#liabType", navTab.getCurrentPanel()).empty();
				$("#appLiab", navTab.getCurrentPanel()).empty();
				$("<option value=''>请选择</option>").appendTo(liabType);
				for (var i = 0; i < data.length; i++) {
					var option1 = "<option value='" +  data[i].code + "' >"
						+ data[i].name + "</option>";	
					$(option1).appendTo(liabType);
				}
				$("#liabType", navTab.getCurrentPanel()).val(liabType1);
			}
		});
		
}


//选择申请保项时，保项费用根据申请保项展示
function liabTypeReserveReportData(k) {
	var liabType = $("#liabType", navTab.getCurrentPanel());
	var appLiab = $(k).val();
	if(appLiab == ""){
		$("#liabType", navTab.getCurrentPanel()).prev().attr("disabled","true");
	}else {
		$("#liabType", navTab.getCurrentPanel()).prev().setMyComboxDisabled(false);
		$("#liabType", navTab.getCurrentPanel()).setMyComboxDisabled(false);
	}
	//var NullList = "3456";
		$("#appLiab", navTab.getCurrentPanel()).val($(k).val());
		$("#liabType", navTab.getCurrentPanel()).prev().val(""); 
		$.ajax({
			'type' : 'post',			
			'url' : 'clm/register/findSpeLiabType_CLM_tClaimDutyRegisterAction.action?claimBillVO.appLiab='+appLiab,
			'datatype' : 'json',
			'async' : true,
			'success' : function(data) {			
				var data = eval("(" + data + ")");
				$("#liabType", navTab.getCurrentPanel()).empty();
				$("#appLiab", navTab.getCurrentPanel()).empty();
				$("<option value=''>请选择</option>").appendTo(liabType);
				for (var i = 0; i < data.length; i++) {
					var option1 = "<option value='" +  data[i].code + "' >"
						+ data[i].name + "</option>";	
					$(option1).appendTo(liabType);
				}

			}
		});	
}
   
var nonRealBill = $("#nonRealBill", navTab.getCurrentPanel()).val();
var verifyResult = $("#verifyResult", navTab.getCurrentPanel()).val();
var verifyResultFlag = $("#verifyResultFlag", navTab.getCurrentPanel()).val();
if(nonRealBill!="" && nonRealBill!=null &&nonRealBill == 0 && verifyResultFlag != "" && verifyResultFlag != null ){
	 $("#verifyResultId",navTab.getCurrentPanel()).show();
} 
if(nonRealBill == 0 && verifyResult == 2){
	 $("#verifyResultDescId",navTab.getCurrentPanel()).show();
} 


 function showVerifyResult(){
	 var nonRealBill = $("#nonRealBill", navTab.getCurrentPanel()).val();
 	 var nonRealBillFlag = $("[name='nonReal']",navTab.getCurrentPanel()).val();
	 if(nonRealBill!="" && nonRealBill!=null && nonRealBill == 0 && nonRealBillFlag!="" && nonRealBillFlag!=null && nonRealBillFlag == 1){
		 $("#verifyResultId",navTab.getCurrentPanel()).show();
		 var verifyResult = $("#verifyResult", navTab.getCurrentPanel()).val();
		  $("#verifyResultFlag", navTab.getCurrentPanel()).val(verifyResult); 
		 if(verifyResult == 2){
			 $("#verifyResultDescId",navTab.getCurrentPanel()).show();	 
		 }
	 } else{
		 $("#verifyResultId",navTab.getCurrentPanel()).hide();
		 $("[name='claimBillVO.verifyResult']",navTab.getCurrentPanel()).val("");
		 $("#verifyResultDescId",navTab.getCurrentPanel()).hide();
		 $("[name='claimBillVO.verifyResultDesc']",navTab.getCurrentPanel()).val("");
	 }
	 $("#nonRealBillFlag", navTab.getCurrentPanel()).val(nonRealBill);
 }
 
 
 function showVerifyResultDesc(){
	 var verifyResult = $("#verifyResult", navTab.getCurrentPanel()).val();
	 $("#verifyResultFlag", navTab.getCurrentPanel()).val(verifyResult); 
	 if(verifyResult == 2){  
		 $("#verifyResultDescId",navTab.getCurrentPanel()).show();
	 }else{
		 $("#verifyResultDescId",navTab.getCurrentPanel()).hide();
	 }
 }
 function dutyControlAmountClick(){
	var caseId= $("#caseId",navTab.getCurrentPanel()).val();
	var url="clm/capacityClaim/capacityClaimPageInit_CLM_claimIntelPortraitAction.action?caseId="+caseId+"&claimIntelPortraitPageNum=5";
	$("#dutyControlAmountClick", navTab.getCurrentPanel()).attr("href",url).click();
 }

 
</script>
<!--账单及费用明细  div start --> 
<div> 
		<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">账单及费用明细
				</h1>
			</div>
        <form class="pageForm required-validate" id="${pageFlag}FormId"
		  action="clm/register/saveDutyItem_CLM_tClaimDutyRegisterAction.action"
		   method="post" onsubmit="return validateCallback(this,dutyAjaxDone)">
		    <input type="hidden"   name="caseId"  value="${caseId}"/>
		    <input type="hidden" id="caseId" name="claimBillVO.caseId"  value="${caseId}"/>
		    <input type="hidden" name="claimBillVO.billType"  value="${claimBillVO.billType}"/>
		    <input type="hidden" name="claimBillVO.billId"  value="${claimBillVO.billId}"/>
		    <input type="hidden" name="pageFlag" value="${pageFlag}" />
		    <input type="hidden" id="verifyResultFlag"  value="${claimBillVO.verifyResult}"/>
		 <!--     <input type="hidden" name="claimTypecode[0]" value="1">
		     <input type="hidden" name="honspTypecode[0]" value="1">
		     <input type="hidden" name="honspTypecode[1]" value="2">
		     <input type="hidden" name="honspTypecode[2]" value="3"> -->
		     
       <div class="panelPageFormContent" id="dutyHospitalCostInitDetails">
			<dl sizset="0">
				<dt>
					<font class="point" color="red">* </font>账单号
				</dt>
				<!-- onkeyup="this.value=this.value.replace(/\D/g,'')" -->
				<dd    sizset="0">
					<input name="claimBillVO.billNo" id="billNo"
						class="accountNumber textInput"   onchange="checkCountNo(this)"
						 type="text"  value="${claimBillVO.billNo }" />
				</dd>
			</dl>
			<dl sizset="0">
				<dt>
					<s:if test="claimBillVO.billType==2">
						服务商赔案号
					</s:if>
					<s:else>
						就诊号
					</s:else>
				</dt>
				<!-- onkeyup="this.value=this.value.replace(/\D/g,'')" -->
				<dd    sizset="0">
					<input name="claimBillVO.adminssionId" id="adminssionId"
						class="accountNumber textInput"  
						 type="text"  value="${claimBillVO.adminssionId }" />
				</dd>
			</dl>
			<dl sizset="1">
				<dt>
					<font class="point" color="red">* </font>医院名称
				</dt>
				<dd   sizset="1">
					<input name="claimBillVO.hospitalCode" id="hospitalId"
						type="hidden" size="10"   value="${claimBillVO.hospitalCode}" /> <input
						name="claimBillVO.hospitalName" class="textInput"
						id="cureHospital" type="text"  value="${claimBillVO.hospitalName}" />
					<a class="btnLook"
						href="clm/report/showHospital_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId="
						  lookupGroup="claimBillVO"></a>
				</dd>
			</dl>
			<dl sizset="2">
				<dt>医院等级</dt>
				<dd   sizset="2">
					<input style="display: none;" name="claimBillVO.hospitalLevel" value="${claimBillVO.hospitalLevel}" class="textInput"
						type="text" size="10"  onpropertychange="changeInputToSpan(this)"   id="hospitalLevelName"/> 
				     <span></span>
				</dd>
			</dl>
			
			
			<s:if test="pageFlag eq 'highMedical'">
				<dl sizset="2">
					<dt>昂贵标志</dt>
					<dd  sizset="2">
						
							 <input yesOrNo="yesOrNo" type="text"   style="display: none;"  onpropertychange="changeInputToSpan(this)"
									 name="claimBillVO.isCostly"  value="${claimBillVO.isCostly}"
									 size="10"  />
							  <span></span>
					    
					</dd>
			 	</dl>
			</s:if>
				<s:if test="pageFlag eq 'highMedical'">
					<dl>
						<dt>申请保项</dt>
						<dd>
						<input type="hidden" name="" value="${claimBillVO.appLiab}" id="appLiab"/>
						<Field:codeTable name="claimBillVO.appLiab"  cssClass="notuseflagDelete combox title"  
										tableName="APP___CLM__DBUSER.T_APP_LIAB"  nullOption="true" 
										 value="${claimBillVO.appLiab}"  onChange="liabTypeReserveReportData(this)"  />					 		
						</dd>
					</dl>
				</s:if>
				<s:if test="pageFlag eq 'highMedical'">
					<dl>
						<dt>保项费用</dt>
						<dd>
						<Field:codeTable name="claimBillVO.liabType"  cssClass="selectToInput"
										tableName="APP___CLM__DBUSER.T_LIAB_TYPE"  nullOption="true" 
										 value="${claimBillVO.liabType}"   id="liabType" />					 
						</dd>					
					</dl>
				</s:if>
			<s:if test="pageFlag eq 'cancerMedical'">
				<dl sizset="2">
					<dt>防癌定点标志</dt>
					<dd   sizset="2"> 
						<input yesOrNo="yesOrNo" style="display: none;" onpropertychange="changeInputToSpan(this)" name="claimBillVO.isCancer" readonly="readonly" value="${claimBillVO.isCancer}" class="textInput"
							type="text" size="10"  /> 
						<span></span>
					</dd>
			 	</dl>
			</s:if>
			 
			
			<dl>
				<dt><font class="point" color="red">* </font>治疗类型</dt>
				<dd  >
				
				<Field:codeTable name="claimBillVO.treatType"  cssClass="combox title"  
										tableName="APP___CLM__DBUSER.T_CURE_TYPE" orderBy="substr(code,1,2)"
										nullOption="true" value="${claimBillVO.treatType}" onChange="queryCalimtreatType(this)"  id="cureType" />
					 
				</dd>
			</dl>
			
			<s:if test="claimBillVO.treatType == 0">
			   <dl style="display:block;" id="outpatientTreatTypeDLIdVO">
                 <dt>门诊治疗类型</dt>
                 <dd>
                  <Field:codeTable name="claimBillVO.outpatientTreatType"  cssClass="combox title"  
                    tableName="APP___CLM__DBUSER.T_OUTPATIENT_TYPE"
                    nullOption="true" value="${claimBillVO.outpatientTreatType}" />
                 </dd>
              </dl>
			</s:if>
			
			<dl style="display:none;" id="outpatientTreatTypeDLId">
               <dt>门诊治疗类型</dt>
               <dd>
                  <Field:codeTable name="claimBillVO.outpatientTreatType"  cssClass="combox title"  
                    tableName="APP___CLM__DBUSER.T_OUTPATIENT_TYPE"
                    nullOption="true" value="${claimBillVO.outpatientTreatType}" orderBy="outpatient_type_code"/>
               </dd>
            </dl>
			    
			<dl>
				<dt>
					<font class="point" color="red">* </font>开始时间
				</dt>
				<dd >
					<input name="claimBillVO.treatStart" class="date textInput" value="<s:date name='claimBillVO.treatStart' format='yyyy-MM-dd' />"
						id="treatStart" onpropertychange="queryCalimDate(this);"
						type="expandDateYMD" maxLength="10"
						  datefmt="yyyy-MM-dd" /> <a
						class="inputDateButton" href="javascript:;"
						 >选择</a>
				</dd>
			</dl>
			<dl>
				<dt>
					<font class="point" color="red">* </font>结束时间
				</dt>
				<dd  >
					<input name="claimBillVO.treatEnd" class="date textInput" value="<s:date name='claimBillVO.treatEnd' format='yyyy-MM-dd' />"
						id="treatEnd" onpropertychange="queryCalimDate(this);"
						type="expandDateYMD" maxLength="10"  datefmt="yyyy-MM-dd" /> <a
						class="inputDateButton" href="javascript:;"  >选择</a>
				</dd>
			</dl>
			<dl sizset="3">
				<dt>
					就诊科室
				</dt>
				<dd   sizset="3">
					<!-- 就诊科室 -->
					<Field:codeTable name="claimBillVO.medicalDeptCode"  cssClass="selectToInput"  id="medicalDeptCode" onChange="countOutPatients(this)"  
										tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT"  
										nullOption="true" value="${claimBillVO.medicalDeptCode}"   />
				</dd>
			</dl>
			<dl sizset="3">
				<dt>
					门诊次数<font class="point" color="red"><!-- * --></font>
				</dt>
				<dd   sizset="3">
					<!-- 门诊次数 -->
					<input id="outServiceNumHiddenId" type="hidden"  value="${claimBillVO.outpatientTotal}" />
					<input style="display: none;" name="claimBillVO.outpatientTotal" class="textInput" id="outServiceNum" type="text"  value="${claimBillVO.outpatientTotal}"  onpropertychange="changeInputToSpan(this)"   />
				    <span></span>
				</dd>
			</dl>
			<dl sizset="4">
				<dt >住院天数</dt>
				<dd  sizset="4">
				    <input id="stayDaysHiddenId" type="hidden"  value="${claimBillVO.stayDays}" />
					<input style="display: none" name="claimBillVO.stayDays" value="${claimBillVO.stayDays}"
						class="satyDays textInput readonly" id="stayDays" type="text"
						readOnly="" onpropertychange="changeInputToSpan(this)"   />
						<span></span>
				</dd>
			</dl> 
			<s:if
				test=" pageFlag eq 'cancerMedical'  ">
				<dl sizset="2">
					<dt>第三方/公费/社保支付</dt>
					<dd sizset="2"> 
						<Field:codeTable cssClass="combox title"
							name="claimBillVO.otherFlag"
							tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true"
							value="${claimBillVO.otherFlag}" />
					</dd>
				</dl>
				<dl sizset="2">
					<dt>第三方/公费/社保支付金额</dt>
					<dd sizset="2"> 
						<input type="text" class="number" name="claimBillVO.otherPay"
							value="${claimBillVO.otherPay}" size="10" />

					</dd>
				</dl>
			</s:if>
			<s:if test="pageFlag eq 'highMedical'   ">
				<dl sizset="2">
					<dt>第三方/公费/社保支付</dt>
					<dd sizset="2"> 
						<Field:codeTable cssClass="combox title"
							name="claimBillVO.otherFlag"
							tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true"
							value="${claimBillVO.otherFlag}" />
					</dd>
				</dl>
				<dl sizset="2">
					<dt>第三方支付金额</dt>
					<dd sizset="2"> 
						<input type="text" class="number" name="claimBillVO.thirdPay"
							value="${claimBillVO.thirdPay}" size="10" />

					</dd>
				</dl>
				<dl sizset="2">
					<dt>公费/社保支付金额</dt>
					<dd sizset="2"> 
						<input type="text" class="number" name="claimBillVO.medicalPay"
							value="${claimBillVO.medicalPay}" size="10" />

					</dd>
				</dl>
			</s:if>


			<s:if test="pageFlag eq 'registerHonsp'   ">
				<dl>
					<dt>距离意外事故发生日天数</dt>
					<dd>
						<span id="accidentDays2"></span>
						<%-- <input name="claimBillVO.stayDays" value="${claimBillVO.stayDays}"
						class="satyDays textInput readonly" id="stayDays" type="text"
						readOnly=""  /> --%>
					</dd>
				</dl>
				<dl>
					<dt>距离出险日期天数</dt>
					<dd>
						<span id="stayDays2"></span>
						<%-- <input type="text" id="accidentDays" name="claimBillVO.accidentDays"
									value="${claimBillVO.accidentDays}" /> --%>
					</dd>
				</dl>
				<dl sizset="5">
					<dt>出险结果</dt>
					<dd sizset="5">
						<input name="claimBillVO.accDetail"
							value="${claimBillVO.accDetail}" class="textInput readonly"
							id="accResultOneId" style="width: 35px;" type="text" readOnly="" />
						<input name="claimBillVO.accident1Name" class="textInput"
							value="<Field:codeValue tableName="APP___CLM__DBUSER.T_ACCIDENT1" value="${claimBillVO.accDetail}"/>"
							id="accResultOne" style="width: 110px;" type="text" />
						<a class="btnLook"
							href="clm/report/accResultQueryFollowInit_CLM_accResultQueryFollowAction.action"
							lookupGroup="claimBillVO"></a>
					</dd>
				</dl>
				<dl sizset="7">
					<dt>疾病编码</dt>
					<dd sizset="7">
						<input name="claimBillVO.icdCode" class="textInput readonly"
							value="${claimBillVO.icdCode}" id="accResultTTwoId"
							style="width: 50px;" type="text" readOnly="" /> <input
							name="claimBillVO.accident2Name" class="textInput"
							value="<Field:codeValue tableName="APP___CLM__DBUSER.T_ACCIDENT2" value="${claimBillVO.icdCode}"/>"
							id="accResultTTwo" style="width: 120px;" type="text" />
					</dd>
				</dl>

			</s:if>
			<dl sizset="2">
				<dt>以社保身份就医</dt>
				<dd sizset="2"> 
					<input type="checkbox" onclick="sociMedicalTreatmentIsChecked(this)" id="sociMedicalTreatmentChecked"/>
					<input type="hidden" class="number" name="claimBillVO.sociMedicalTreatment" id="sociMedicalTreatment"
						value="${claimBillVO.sociMedicalTreatment}" size="10" />

				</dd>
			</dl>
			<s:if test="pageFlag eq 'registerHonsp'   ">
			<dl sizset="2">
				<dt>是否特需、国际部医疗费用</dt>
				<dd sizset="2"> 			
				<Field:codeTable name="claimBillVO.isSpecialInternational"  cssClass="combox title"  
										tableName="APP___CLM__DBUSER.T_IS_SPECIAL_INTERNATIONAL"  nullOption="true" value="${claimBillVO.isSpecialInternational}"   id="SpecialInternationalChecked" />
			</dl>
			</s:if>
			
			<s:if test="pageFlag eq 'CARCellMedical'   ">
				<dl>
					<dt>
						<font class="point" color="red">* </font>单采前检查时间
					</dt>
					<dd  >
						<s:if test="claimBillVO.preMimingInspectTime !=null  and claimBillVO.firstClaimCase">
							<input name="claimBillVO.preMimingInspectTime" class=" textInput" value="<s:date name='claimBillVO.preMimingInspectTime' format='yyyy-MM-dd' />"
								id="preMimingInspectTime" 
								type="text" maxLength="10"  datefmt="yyyy-MM-dd" readonly="readonly"/><a
								class="inputDateButton" href="javascript:;"  >选择</a>
						</s:if>
						<s:else>
							<input name="claimBillVO.preMimingInspectTime" class="date textInput" value="<s:date name='claimBillVO.preMimingInspectTime' format='yyyy-MM-dd' />"
								id="preMimingInspectTime" 
								type="expandDateYMD" maxLength="10"  datefmt="yyyy-MM-dd" /> <a
								class="inputDateButton" href="javascript:;"  >选择</a>
						</s:else>
					</dd>
				</dl>
				<dl>
					<dt>
						<font class="point" color="red">* </font>CAR-T细胞回输时间
					</dt>
					<dd  >
						<input name="claimBillVO.carCellTransTime" class="date textInput" value="<s:date name='claimBillVO.carCellTransTime' format='yyyy-MM-dd' />"
							id="carCellTransTime" 
							type="expandDateYMD" maxLength="10"  datefmt="yyyy-MM-dd" /> <a
							class="inputDateButton" href="javascript:;"  >选择</a>
					</dd>
				</dl>
			</s:if>
			
			<dl sizset="9">
				<dt>总费用</dt>
				<dd   sizset="9">
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimBillVO.sumAmount" value="${claimBillVO.sumAmount}" id="sumAmountAll"
						class="textInput readonly" type="text" readOnly="" />
				    <span></span>
				</dd>
			</dl>
			<dl sizset="10">
				<dt >扣除费用</dt>
				<dd  sizset="10">
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimBillVO.deductAmount"  value="${claimBillVO.deductAmount}" id="deductAmountAll"
						class="textInput readonly" type="text" readOnly=""  />
				    <span></span>
				</dd>
			</dl>
			<dl sizset="11" >
				<dt >理算金额</dt>
				<dd  sizset="11">
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimBillVO.calcAmount"  value="${claimBillVO.calcAmount}" id="calcAmountAll"
						class="textInput readonly" type="text" readOnly=""  />
				     <span></span>
				</dd>
			</dl>
			<dl>
				<dt>
					电票查验
				</dt>
				<dd>    
				        <input type="hidden" name="claimBillVO.elecBillCheck"  value="${claimBillVO.elecBillCheck}">
						<Field:codeTable name="elecBillCheck"  cssClass="combox title"   id="elecBillCheck" 
						tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true"  disabled="true" value="${claimBillVO.elecBillCheck}"  />
				</dd>
			</dl>
			<dl>
				<dt>
					疑似虚假票据
				</dt>
				<dd>
				        <input type="hidden" id="nonRealBillFlag" name="nonReal" value="${claimBillVO.nonRealBill}">
  						<Field:codeTable name="claimBillVO.nonRealBill"  cssClass="combox title"   id="nonRealBill"  onChange="showVerifyResult()" 
						tableName="APP___CLM__DBUSER.T_YES_NO" nullOption="true"  value="${claimBillVO.nonRealBill}" /> 
				</dd>
			</dl>
			<dl  style="display:none;" id="verifyResultId">
				<dt>
					<font class="point" color="red">* </font>核实结果
				</dt>
				<dd>
 						<Field:codeTable   cssClass="combox title"  name="result" id="verifyResult"   onChange="showVerifyResultDesc()" 
						tableName="APP___CLM__DBUSER.T_CLAIM_VERIFY_RESULT"  value="${claimBillVO.verifyResult}" />
						<input type="hidden" id="verifyResultFlag" name="claimBillVO.verifyResult" value="${claimBillVO.verifyResult}">  
				</dd>
			</dl>
			<dl   style="display:none;" id="verifyResultDescId">
				<dt>
					<font class="point" color="red">* </font>核实结果说明
				</dt>
				<dd>
					<input name="claimBillVO.verifyResultDesc" id="verifyResultDesc"
						class="textInput"  
						 type="text"  value="${claimBillVO.verifyResultDesc}" />
				</dd>
			</dl>
			<dl>
				<dt>电子票据代码</dt>
				<dd>
					<input  name="claimBillVO.elecBillId" value="${claimBillVO.elecBillId}" class="textInput" readOnly="readOnly"
						type="text" size="10"  id="elecBillId"/> 
				</dd>
			</dl>
			<dl>
				<dt>电子票据号码</dt>
				<dd>
					<input  name="claimBillVO.elecBillNum" value="${claimBillVO.elecBillNum}" class="textInput" readOnly="readOnly"
						type="text" size="10"  id="elecBillNum"/> 
				</dd>
			</dl>
			<dl>
				<dt>校验码</dt>
				<dd>
					<input  name="claimBillVO.checkCode" value="${claimBillVO.checkCode}" class="textInput" readOnly="readOnly"
						type="text" size="10"  id="checkCode"/>
				</dd>
			</dl>
			<dl>
				<dt>票据金额</dt>
				<dd>
					<input  name="claimBillVO.elecBillAmount" value="${claimBillVO.elecBillAmount}" class="textInput" readOnly="readOnly"
						type="text" size="10"  id="elecBillAmount"/> 
				</dd>
			</dl>
			<dl>
				<dt>
					开票日期
				</dt>
				<dd>
					<input  name="claimBillVO.elecBillDate" value="<s:date name='claimBillVO.elecBillDate' format='yyyy-MM-dd' />"  readOnly="readOnly"  
						type="text" size="10" id="elecBillDate"/> 
				</dd>
			</dl>
		 </div>
		<!--医疗费用明细  div end  -->
 


<!--医疗费用明细  div start -->
<div id="dutyHospitalCostInit">
		<div class="divfclass">
				<h1> 
					<img src="clm/images/tubiao.png">费用明细
				</h1>
			</div>
	<div class="tabdivclassbr" >
		<div style="overflow: auto;">
     	<table class="list nowrap itemDetail" addButton="添  加"  hiddenAdd="hiddenAdd"  autoAdd="autoAdd" enterAdd="enterAdd"
			  style="width:100%">
			<thead>
				<tr>
					<th nowrap type="enum" enumUrl="clm/pages/html/dutyInput.jsp">序号</th>
                    <th nowrap type="enum" name=""
							enumUrl="clm/pages/html/dutyMedFeeItem1.jsp">费用明细项目</th>
					<th nowrap type="enum" name="" enumUrl="clm/pages/html/operationPage.jsp"
						size="10">手术代码</th>
                    <th nowrap type="enum" name="" enumUrl="clm/pages/html/drugPage.jsp"
						size="10">药品（器械）明细</th>
								<s:if test="pageFlag eq 'registerHonsp'   ">
								     <th nowrap type="enum" name=""
											enumUrl="clm/pages/html/dutyMedFeeAmount.jsp"
											>费用金额</th>
									<th nowrap type="enum" name=""
										enumUrl="clm/pages/html/dutyExpenseAmount.jsp">自费费用</th>
									<th nowrap type="enum" name=""
										enumUrl="clm/pages/html/dutyExpenseRemark.jsp">自费备注</th>
									<th nowrap type="enum" name=""
										enumUrl="clm/pages/html/dutyPayAmount.jsp" >自付费用</th>
									<th nowrap type="enum" name=""
										enumUrl="clm/pages/html/dutyPayRemark.jsp">自付备注</th>
									<th nowrap type="enum" name=""
										enumUrl="clm/pages/html/dutyOtherAmount.jsp" >其他费用</th>
									<th nowrap type="enum" name=""
										enumUrl="clm/pages/html/dutyOtherRemark.jsp">其他备注</th>
									<s:if test='costControlFlag=="1"'>
										<th nowrap type="enum" name=""
											enumUrl="clm/pages/html/dutyControlAmount.jsp">
											<a href="javaScript:void(0)" onclick="dutyControlAmountClick()">控费金额</a>
											<a id="dutyControlAmountClick" href="#"  rel="capacityClaimAudit"  style="display: none;" target="navTab">智能理赔画像</a></th>
										<th nowrap type="enum" name=""
											enumUrl="clm/pages/html/dutyControlRemark.jsp">控费备注</th>
										<th nowrap type="enum" name=""
											enumUrl="clm/pages/html/dutyControlRadio.jsp">是否采纳</th>
									</s:if>
								</s:if>
								<s:else>
                         			<th nowrap type="enum" name=""
											enumUrl="clm/pages/html/dutyMedFeeAmount2.jsp" 
											>费用金额</th>
									<th type="enum" name=""
										enumUrl="clm/pages/html/dutyDeductAmount.jsp" 
										style="width: 10%">扣除费用</th>
									<th type="enum" name=""
										enumUrl="clm/pages/html/dutyDeductReason.jsp" >扣除原因</th>

									<!-- <th type="text" name="claimBillItemVOlist[#index#].deductRemark" size="12">扣除备注</th> -->
									<th type="enum" name=""
										enumUrl="clm/pages/html/dutyDeductRemark.jsp" >扣除备注</th>
								</s:else>
								<th nowrap type="enum" name="" enumUrl="clm/pages/html/dutyCalcAmount.jsp"
						>金额</th>

					<!-- <th type="enum" name=""
						enumUrl="clm/pages/html/dutyDeductReason.jsp" size="12">扣除原因</th> -->

					<!-- <th type="text" name="claimBillItemVOlist[#index#].deductRemark" size="12">扣除备注</th> -->
					<th type="enum" name=""
						enumUrl="clm/pages/html/dutyDelete.jsp" size="12">操作</th> 
				</tr>
			</thead>
			<tbody class="list" id="feeItemId">
				<s:if
					test="claimBillItemVOlist != null||claimBillItemVOlist.size()>0">
					<s:iterator value="claimBillItemVOlist" status="st">
						<tr>
							<td><input style="width: 40px;" class="digits textInput" type="text" size="5"
								value="${st.index+1}" />
								 <input type="hidden" name="claimBillItemVOlist[${st.index}].billItemId"  value="${billItemId}">
								 <input type="hidden" name="claimBillItemVOlist[${st.index}].caseId"  value="${caseId}">
							</td>
                            <td>
									<Field:codeTable   cssClass="selectToInput title" id="medFeeItem" onChange="cleanInput(this)"
										name="claimBillItemVOlist[${st.index}].medFeeItem"
										tableName="APP___CLM__DBUSER.T_INVOICE_TYPE" nullOption="true"
										value="${medFeeItem}" whereClause="CODE like 'C%'" orderBy="code"/>
								 
                            </td>
							<td name="手术代码"><input id="operationCodeId"
								name="claimBillItemVOlist[${st.index}].operationCode"
								value="${operationCode}"  type="hidden" /> <input style="width: 40px;"
								id="operationNameId" onkeyup="onEmptyChangeId(this)" readonly
								name="claimBillItemVOlist[${st.index}].operationDesc"
								value="<Field:codeValue tableName="APP___CLM__DBUSER.T_OPERATION" value='${operationCode}'/>"
								type="text" style="width: 100px;"/> <a class="btnLook"
								href="clm/report/queryOperationCode_CLM_hospitalInfoQueryAction.action"
								lookupGroup="claimBillItemVOlist[${st.index}]">查询手术代码</a></td>
								
								<td name="药品（器械）明细"><input id="drugDetailCodeId"
								name="claimBillItemVOlist[${st.index}].drugDetailId"
								value="${drugDetailId}"  type="hidden" /> <input style="width: 40px;"
								id="drugDetailNameId" onkeyup="onEmptyChangeId(this)" readonly
								name="claimBillItemVOlist[${st.index}].drugName"
								value="<Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_DRUG_DETAIL" value='${drugName}'/>"
								type="text" style="width: 100px;"/> <a class="btnLook"
								href="clm/report/queryDrugCode_CLM_hospitalInfoQueryAction.action"
								lookupGroup="claimBillItemVOlist[${st.index}]">药品（器械）明细</a></td>

                    <s:if test="pageFlag eq 'registerHonsp'   ">
								<td name="费用金额">
									<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
									<input type="text" id="feeAmount" 
									name="claimBillItemVOlist[${st.index}].feeAmount" class="number"
									maxlength="18" style="width: 40px;" onchange="calcAmountThis(this);" value="${feeAmount}" />
								</td>
											<td name="自费费用">
												<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
												<input type="text" onchange="calcAmount2(this);"
												id="expenseAmount"
												name="claimBillItemVOlist[${st.index}].expenseAmount"
												class="number" maxlength="18" style="width: 40px;"
												value="${expenseAmount}" />
											</td>
											<td name="自费备注"><input type="text" id="expenseRemark"
												name="claimBillItemVOlist[${st.index}].expenseRemark"
												value="${expenseRemark}" maxlength="1000"  style="width: 50px;" /></td>
											<td name="自付费用">
												<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
												<input type="text" onchange="calcAmount2(this);"
												id="payAmount"
												name="claimBillItemVOlist[${st.index}].payAmount"
												class="number" maxlength="18" style="width: 40px;"
												value="${payAmount}" />
											</td>
											<td name="自付备注"><input type="text" id="payRemark" style="width: 50px;"
												name="claimBillItemVOlist[${st.index}].payRemark"
												value="${payRemark}" maxlength="1000" /></td>
											<td name="其他费用">
												<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
												<input type="text" onchange="calcAmount2(this);"
												id="otherAmount"
												name="claimBillItemVOlist[${st.index}].otherAmount"
												class="number" maxlength="18" style="width: 40px;"
												value="${otherAmount}" />
											</td>
											<td name="其他备注"><input type="text" id="otherRemark"
												name="claimBillItemVOlist[${st.index}].otherRemark"
												value="${otherRemark}" maxlength="1000" style="width: 50px;" /></td>
											<s:if test='costControlFlag=="1"'>
												<s:if test='isCostControlDisabled=="0"'>
													<td name="控费费用">
														<input type="text"
														name="claimBillItemVOlist[${st.index}].costControlAmount"
														class="number" maxlength="18" style="width: 40px;"
														value="${costControlAmount}" />
													</td>
													<td name="控费备注"><input type="text"
														name="claimBillItemVOlist[${st.index}].costControlRemark"
														value="${costControlRemark}" maxlength="1000" style="width: 50px;" /></td>
													<td name="是否采纳">
														<input type='radio' value='1' name="claimBillItemVOlist[${st.index}].isAcceptable" onclick="clickControlRadio(this);" <s:if test='isAcceptable=="1"'>checked="checked"</s:if> /><span>是</span>
														<input type='radio' value='0' name="claimBillItemVOlist[${st.index}].isAcceptable" onclick="clickControlRadio(this);" <s:if test='isAcceptable=="0"'>checked="checked"</s:if> /><span>否</span>
													</td>
												</s:if>
												<s:else>
													<td name="控费费用">
														<input type="text" disabled="disabled"
														name="claimBillItemVOlist[${st.index}].costControlAmount"
														class="number" maxlength="18" style="width: 40px;"
														value="${costControlAmount}" />
													</td>
													<td name="控费备注"><input type="text" disabled="disabled"
														name="claimBillItemVOlist[${st.index}].costControlRemark"
														value="${costControlRemark}" maxlength="1000" style="width: 50px;" /></td>
													<td name="是否采纳">
														<input type='radio' value='1' name="claimBillItemVOlist[${st.index}].isAcceptable" onclick="" disabled="disabled"/><span>是</span>
														<input type='radio' value='0' name="claimBillItemVOlist[${st.index}].isAcceptable" onclick="" disabled="disabled"/><span>否</span>
													</td>
												</s:else>
											</s:if>
										</s:if>
										<s:else>
											<td name="费用金额">
												<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
												<input type="text" id="feeAmount"
												name="claimBillItemVOlist[${st.index}].feeAmount"
												class="number" maxlength="18" style="width: 40px;"
												onchange="calcAmountThisHC(this);" value="${feeAmount}" />
											</td>
											<td name="扣除费用">
												<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
												<input type="text" onchange="calcAmount2HC(this);"
												id="deductAmount"
												name="claimBillItemVOlist[${st.index}].deductAmount" style="width: 40px;"
												class="number" maxlength="18" value="${deductAmount}" />
											</td>

											<td name="扣除原因"><Field:codeTable
													cssClass="selectToInput" id="deductReason"  
													name="claimBillItemVOlist[${st.index}].deductReason"
													tableName="APP___CLM__DBUSER.T_MEDICAL_FEE_DEDUCTION"
													nullOption="true" value="${deductReason}" /></td>

											<td name="扣除备注"><input type="text" id="deductRemark" style="width: 50px;"
												name="claimBillItemVOlist[${st.index}].deductRemark"
												value="${deductRemark}" maxlength="1000" /></td>
										</s:else>
							<td name="理算金额">
								<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
								<input type="text" id="calcAmount" class="calcAmount"
								name="claimBillItemVOlist[${st.index}].calcAmount" style="width: 40px;" maxlength="18"
								readonly="readonly" value="${calcAmount}" />
							</td>
							<%-- <td name="扣除原因"><Field:codeTable  cssClass="combox" id="deductReason"
									name="claimBillItemVOlist[${st.index}].deductReason"
									tableName="APP___CLM__DBUSER.T_MEDICAL_FEE_DEDUCTION"
									nullOption="true" value="${deductReason}" /></td> --%>
							<td><a class="btnDel" href="javascript:void(0)" onclick="deleteDetail(this)"></a></td>
						</tr>
					</s:iterator>
				</s:if>
			</tbody>
		</table>
		</div>
	</div>
	 </div>
   </form>
</div>
<!--医疗费用明细  div end  -->


 

