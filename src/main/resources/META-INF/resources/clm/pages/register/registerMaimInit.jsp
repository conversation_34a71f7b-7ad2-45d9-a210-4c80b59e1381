<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%
	String accType = request.getParameter("accType");
	String id = request.getParameter("id");
%>


<script type="text/javascript"> 
// $(function(){
// 	$.ajax({
// 			'type':'post',
// 			'url':'clm/register/queryInjuryGradeByInjuryType_CLM_tClaimDutyRegisterAction.action?deformityGradeVO.deformityType='+'${claimInjuryVOlist[0].deformityType}',
// 			'datatype':'json',
// 			'success':function(data){
// 				var data = eval("(" + data + ")");
// 				$("#deformityGrade", navTab.getCurrentPanel()).empty();
// 				var options="";
// 	           options='<option value="">请选择</option>';
// 				for(var i = 0; i < data.length; i++){
// 					options+="<option value='"+data[i].deformityGrade+"'>"+data[i].deformityGradeName+"</option>";
// 				}
// 				$("#deformityGrade", navTab.getCurrentPanel()).loadMyComboxOptions(options,'1');
// 				 $("#complaintGender", navTab.getCurrentPanel()).selectMyComBox("${claimInjuryVOlist[0].deformityGrade}");
// 			},
// 			'error':function(){
// 				alert("出错了！");
// 			}
// 	})
// })
//自定义返回状态 
function dutyAjaxDoneInjury(json) {
	  
	if(json.statusCode == DWZ.statusCode.error) {
		if(json.message && alertMsg) alertMsg.error(json.message);
	}else {
		if(json.message && alertMsg) alertMsg.correct(json.message);
		
		var rel = $("#registerMaimNews", navTab.getCurrentPanel());
		rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		//  saveBackValueInjury(json.save,json.claimInjuryVO,json.pageFlag); 
		 
	};   
	 
}
 
 
//第一次联动
	function injuryTypeSelected(obj) {
	 
		 var  injuryType = $(obj).val();
		//是否是详细的列表
	 		 
		  var     datherTag=$(obj).parent().parent().parent().parent();
	 		 
	 		 
		 if(injuryType != null && injuryType != ""){
			 var  deformityGrade= $(datherTag).find("#deformityGrade");
			 var injuryCode1 = $(datherTag).find("#injuryCode1");
 			 var injuryCode2 = $(datherTag).find("#injuryCode2");
 			 var injuryRate = $(datherTag).find("#injuryRate");
			  
			 $.ajax({
		  			'type':'post',
		  			'url':'clm/register/queryInjuryGradeByInjuryType_CLM_tClaimDutyRegisterAction.action?deformityGradeVO.deformityType='+injuryType,
		  			'datatype':'json',
		  			'async':false,
		  			'success':function(data){
		  				var data = eval("(" + data + ")");
 		  				deformityGrade.empty();
 		  				injuryCode1.empty();
 		  				injuryCode2.empty();
 		  				var options="";
 		  	            options='<option value="">请选择</option>';
 		  				//触发事件
 		  				$(options).appendTo(injuryCode1);
 		  				$(options).appendTo(injuryCode2);
 		  				claimFireEvent($(injuryCode1));
 		  				claimFireEvent($(injuryCode2));
 		  				injuryRate.attr("value",null);
		  				
		  				for(var i = 0; i < data.length; i++){
		  					options+="<option value='"+data[i].deformityGrade+"'>"+data[i].deformityGradeName+"</option>";
		  				}
		  				$("#deformityGrade", navTab.getCurrentPanel()).loadMyComboxOptions(options,'1');
		  				 $("#deformityGrade", navTab.getCurrentPanel()).selectMyComBox("");
		  			},
		  			'error':function(){
		  				alert("出错了！");
		  			}
		  	}); 
		 } else { //清空伤残级别
			 $("#deformityGrade", navTab.getCurrentPanel()).selectMyComBox("");
		 }
	}
	//第二次联动
	function injuryGradeSelected(obj) {
		//是否是详细的列表injuryCode1 
		 var     datherTag=$(obj).parent().parent().parent().parent().parent();
		 var deformityType = $("#deformityType", navTab.getCurrentPanel()).val(); 
		 var deformityGrade = $(obj).val();
		 if(deformityType != null && deformityType != ""){
			 if(deformityGrade != null && deformityGrade != ""){
				 var injuryCode1 = $(datherTag).find("#injuryCode1");
				 
				 var injuryCode2 = $(datherTag).find("#injuryCode2");
				 var injuryRate = $(datherTag).find("#injuryRate");
				 $.ajax({
			  			'type':'post',
			  			'url':'clm/register/queryInjuryCode1Info_CLM_tClaimDutyRegisterAction.action?deformityCode1VO.deformityType='+deformityType+'&deformityCode1VO.deformityGrade='+deformityGrade,
			  			'datatype':'json',
			  			'async':false,
			  			'success':function(data){
			  				var data = eval("(" + data + ")");
			  				injuryCode1.empty();
			  				injuryCode2.empty();
			  				injuryRate.attr("value",null);
			  				$("<option value='' class = ''>请选择</option>").appendTo(injuryCode1); 
			  				$("<option value='' class = ''>请选择</option>").appendTo(injuryCode2); 
			  				for(var i = 0; i < data.length; i++){ 
			  					if("${claimInjuryVOlist[0].injuryCode1}"==data[i].deformityCode1){
			  					var option = "<option value='"+data[i].deformityCode1+"' selected='selected'  class='"+data[i].deformityCode1Name+"' >"+data[i].deformityCode1Name+"</option>";
			  					$(option).appendTo(injuryCode1);
			  					}else{
			  						var option = "<option value='"+data[i].deformityCode1+"' deformityRate='"+data[i].deformityRate+"'   class='"+data[i].deformityCode1Name+"' >"+data[i].deformityCode1Name+"</option>";
			  						$(option).appendTo(injuryCode1); 
			  					}
			  				} 
			  				claimFireEvent($(injuryCode1));
	 		  				claimFireEvent($(injuryCode2));
			  			},
			  			'error':function(){
			  				alert("出错了！");
			  			}
			  	});  
			 }
		 }
	}
	
	 
	//第三次联动
	//查询残疾给付比例与伤残代码2的信息
	function injuryCode1Selected(obj) {
		//是否是详细的列表
		 var     datherTag=$(obj).parent().parent().parent().parent();
		 
		 var deformityType = $("#deformityType", navTab.getCurrentPanel()).val(); 
		 var  deformityGrade= $("#deformityGrade", navTab.getCurrentPanel()).val();
		 var injuryCode1 = $(obj).val(); 
		 //如果本身有数据就直接赋值 
		 
		 $("#injuryRate", navTab.getCurrentPanel()).attr("value",$(obj).find("option:selected").attr("deformityRate") );
		 
		 if(deformityType != null && deformityType != ""&&deformityGrade != null && 
				 deformityGrade != ""&&injuryCode1 != null && injuryCode1 != ""){
					 var injuryCode2 = $(datherTag).find("#injuryCode2");
					 var injuryRate = $(datherTag).find("#injuryRate");
					  $.ajax({
				  			'type':'post',
				  			'url':'clm/register/queryInjuryCode2Info_CLM_tClaimDutyRegisterAction.action?deformityCode2VO.deformityType='+deformityType+'&deformityCode2VO.deformityGrade='+deformityGrade+'&deformityCode2VO.deformityCode1='+injuryCode1,
				  			'datatype':'json',
				  			'async':false,
				  			'success':function(data){
				  				var data = eval("(" + data + ")");
				  				if(data.length >= 1 && (deformityType == "0" || deformityType == "7" || deformityType == "8")){
					  				injuryCode2.empty();
					  				injuryRate.attr("value",null);
					  				$("<option value='' class = ''>请选择</option>").appendTo(injuryCode2); 
					  				for(var i = 0; i < data.length; i++){
					  					var option = "<option value='"+data[i].deformityCode2+"'   class='"+data[i].deformityCode2Name+"' >"+data[i].deformityCode2Name+"</option>";
					  					$(option).appendTo($("#injuryCode2", navTab.getCurrentPanel()));
					  				}
					  				claimFireEvent($(injuryCode2));
				  				} else {
				  					$(datherTag).find("#injuryRate").attr("value",data.deformityRate);
				  				}
				  				
				  			},
				  			'error':function(){
				  				alert("出错了！");
				  			}
			 	});  
		  }
	}
	//第四次联动
	//查询残疾给付比例
	function injuryCode2Selected(obj) {
		//是否是详细的列表
		 var datherTag=$(obj).parent().parent().parent().parent();
		 var deformityType = $("#deformityType", navTab.getCurrentPanel()).val(); 
		 var deformityGrade= $("#deformityGrade", navTab.getCurrentPanel()).val();
		 var injuryCode1 = $("#injuryCode1", navTab.getCurrentPanel()).val();
		 var injuryCode2 = $(obj).val();
		 if(deformityType != null && deformityType != ""&&deformityGrade != null && deformityGrade != ""&&injuryCode1 != null && injuryCode1 != ""&&injuryCode2 != null && injuryCode2 != ""){
			 
						 $.ajax({
					  			'type':'post',
					  			'url':'clm/register/queryDeformityRate_CLM_tClaimDutyRegisterAction.action?deformityCode2VO.deformityType='+deformityType+'&deformityCode2VO.deformityGrade='+deformityGrade+'&deformityCode2VO.deformityCode1='+injuryCode1+'&deformityCode2VO.deformityCode2='+injuryCode2,
					  			'datatype':'json',
					  			'success':function(data){
					  				var data = eval("(" + data + ")");
					  				 
				  					$("#injuryRate", navTab.getCurrentPanel()).attr("value",data.deformityRate);
					  			},
					  			'error':function(){
					  				alert("出错了！");
					  			}
					  	});  
		 }
		 
	}
///初始化查询
	function injuryGradeSelected2(obj) {
		//是否是详细的列表injuryCode1
		 var     datherTag=$(obj).parent().parent().parent().parent().parent();
		 var deformityType = $(datherTag).find("#deformityType").val(); 
		 var deformityGrade = $(obj).val();
		 if(deformityType != null && deformityType != ""){
			 if(deformityGrade != null && deformityGrade != ""){
				 var injuryCode1 = $(datherTag).find("#injuryCode1"); 
				 var injuryCode2 = $(datherTag).find("#injuryCode2");
				 var injuryRate = $(datherTag).find("#injuryRate");
				 $.ajax({
			  			'type':'post',
			  			'url':'clm/register/queryInjuryCode1Info_CLM_tClaimDutyRegisterAction.action?deformityCode1VO.deformityType='+deformityType+'&deformityCode1VO.deformityGrade='+deformityGrade,
			  			'datatype':'json',
			  			'success':function(data){
			  				var data = eval("(" + data + ")");
			  				injuryCode1.empty();
			  				injuryCode2.empty(); 
			  				$("<option value='' class = ''>请选择</option>").appendTo(injuryCode1); 
			  				for(var i = 0; i < data.length; i++){ 
			  					if("${claimInjuryVOlist[0].injuryCode1}"==data[i].deformityCode1){
			  					var option = "<option value='"+data[i].deformityCode1+"' selected='selected'  class='"+data[i].deformityCode1Name+"' >"+data[i].deformityCode1Name+"</option>";
			  					$(option).appendTo(injuryCode1);
			  					}else{
			  						var option = "<option value='"+data[i].deformityCode1+"' deformityRate='"+data[i].deformityRate+"'   class='"+data[i].deformityCode1Name+"' >"+data[i].deformityCode1Name+"</option>";
				  					$(option).appendTo(injuryCode1); 
			  					}
			  				}
			  				claimFireEvent($(injuryCode1));
	 		  				claimFireEvent($(injuryCode2));
			  			},
			  			'error':function(){
			  				alert("出错了！");
			  			}
			  	});  
			 }
		 }
	}
//查找带回伤残等级
	function searchLevel(){
		$("#searchLeve", navTab.getCurrentPanel()).attr("href","clm/pages/register/maimLevel.jsp");
	}
	 
</script>
<!--账单及费用明细  div start -->



<div class="panelPageFormContent">
		<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">伤残详细信息录入
					</h1>
				</div>
	<div class="panelPageFormContent" id="dutyHospitalCostInitDetails">
		<form class="pageForm" id="${pageFlag}FormId"
			action="clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
			method="post"
			onsubmit="return validateCallback(this,dutyAjaxDone)">
			<input type="hidden" name="claimInjuryVOlist[0].caseId"
				value="${caseId}"> <input type="hidden"
				name="claimInjuryVOlist[0].injuryId"
				value="${claimInjuryVOlist[0].injuryId}"> <input type="hidden"
				name="claimTypecode[0]" value="2">  
				<input type="hidden" name="pageFlag" value="${pageFlag}">
<input type="hidden"   name="caseId"  value="${caseId}">
			<dl sizset="0"> 
				<dt>
					<font class="point" color="red">* </font>伤残类型
				</dt>
				<dd sizset="0">
					<Field:codeTable cssClass="selectToInput title" id="deformityType"
						name="claimInjuryVOlist[0].deformityType"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_TYPE" whereClause="deformity_type in (0,1,2,3,4,5,6,7,8)" orderBy="deformity_type" nullOption="true"
						value="${claimInjuryVOlist[0].deformityType}" onChange="injuryTypeSelected(this);" />
				</dd>
			</dl>
			
			<dl sizset="1">
				<dt >
					<font class="point" color="red">* </font>伤残级别
				</dt>
				<dd sizset="1">
					<Field:codeTable cssClass="combox title" id="deformityGrade"
						name="claimInjuryVOlist[0].deformityGrade"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_GRADE" nullOption="true"
						value="${claimInjuryVOlist[0].deformityGrade}"
						whereClause="deformity_type = '${claimInjuryVOlist[0].deformityType}'" orderBy="decode(deformity_grade,10,9 ,deformity_grade)"
						onChange="injuryGradeSelected(this);" /><a id="searchLeve" class="btnLook" onclick="searchLevel();" lookupGroup="addClaimInjuryVO"></a>
				
				</dd>
			</dl>
			<dl sizset="2">
				<dt ><font class="point" color="red">* </font>伤残代码1</dt>
				<dd sizset="2">

					<Field:codeTable cssClass="selectToInput title" id="injuryCode1"
						name="claimInjuryVOlist[0].injuryCode1"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_CODE1" nullOption="true"
						value="${claimInjuryVOlist[0].injuryCode1}"
						whereClause="deformity_type = '${claimInjuryVOlist[0].deformityType}' and deformity_grade = '${claimInjuryVOlist[0].deformityGrade}'"
						onChange="injuryCode1Selected(this);" />
				</dd>
			</dl>
			<dl sizset="3">
				<dt >伤残代码2</dt>
				<dd sizset="3">

					<Field:codeTable cssClass="selectToInput title" id="injuryCode2"
						name="claimInjuryVOlist[0].injuryCode2"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_CODE2"
						value="${injuryCode2}"
						whereClause="deformity_type = '${claimInjuryVOlist[0].deformityType}' and deformity_grade = '${claimInjuryVOlist[0].deformityGrade}' and deformity_code1 = '${claimInjuryVOlist[0].injuryCode1}' "
						onChange="injuryCode2Selected(this);" />
				</dd>
			</dl>
			<dl sizset="4">
				<dt >参与度</dt>
				<dd sizset="4">
					<input name="claimInjuryVOlist[0].partDegree" value="${claimInjuryVOlist[0].partDegree }" class="textInput"
						id="partDegree" type="text" />
				</dd>
			</dl>
			<dl sizset="5">
				<dt >残疾给付比例</dt>
				<dd sizset="5"> 
					<input style="display: none;" onpropertychange="changeInputToSpan(this)"  name="claimInjuryVOlist[0].payRate" value="${claimInjuryVOlist[0].payRate }"
						class="injuryPayRate textInput readonly" id="injuryRate"
						type="text" readOnly="" />
				    <span></span>		
				</dd>
			</dl>
			<dl sizset="6">
				<dt >鉴定机构</dt>
				<dd sizset="6">
					<input name="claimInjuryVOlist[0].assessOrgan" value="${claimInjuryVOlist[0].assessOrgan }" class="textInput"
						type="text" />
				</dd>
			</dl>
			<dl >
				<dt >鉴定日期</dt>
				<dd >
					<input name="claimInjuryVOlist[0].assessDate" value="<s:date name='claimInjuryVOlist[0].assessDate' format='yyyy-MM-dd' />"
						class="date textInput" type="expandDateYMD" maxLength="10"
						datefmt="yyyy-MM-dd" /> <a class="inputDateButton"
						href="javascript:;">选择</a>
				</dd>
			</dl>
			<dl style="width: 100%;">
				<dt >备注</dt>
				<dd >
					<textarea name="claimInjuryVOlist[0].remark" value="${claimInjuryVOlist[0].remark}"  class="textInput"
						rows="4" cols="70"></textarea>
				</dd>
			</dl>
			<dl style="width: 100%;">
				<dt ></dt>
				<dd > 
				</dd>
			</dl>
			<dl style="width: 100%;">
				<dt ></dt>
				<dd > 
				</dd>
			</dl>
		</form>


	</div>
	<!--医疗费用明细  div end  -->
</div>
<script type="text/javascript"> 
/* if($("#deformityGrade", navTab.getCurrentPanel()).length>0){ 
	injuryGradeSelected2($("#deformityGrade", navTab.getCurrentPanel())); 
} */
</script>


