<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript">

//用于伸缩按钮的伸缩功能
$(document).ready(function(){
   $(".main_heading", navTab.getCurrentPanel()).off().toggle(function(){  
     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
   },function(){
     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
    	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
   });

});

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]", navTab.getCurrentPanel()).bind("click",function(){
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
})

if(isApprove=="approve"){
	//readOnly("generalNewsInt");
	readOnly("approveDuty");
}

$("#disabilityDetail", navTab.getCurrentPanel()).selectMyComBox('${claimDisabilityVO.disabilityDetail}');
$("#disability", navTab.getCurrentPanel()).selectMyComBox('${claimDisabilityVO.disabilityGrade}');

function selectChange(obj) {
	var code = $(obj).val();
	$.ajax({
			'url':'clm/register/getPayRateByOrderAndName_CLM_tClaimDutyRegisterAction.action?disabilityVO.code='+code,
			'type':'post',
			'datatype':'json',
			'success':function(data){
			   var data = eval("("+data+")");
			   var rate = data.rate;
			   $(obj).parent().parent().parent().parent().next().find("dd").find("input").attr("value",rate);
			 }
	});
}
function selectChangeDisability(obj){
	var code = $(obj).val();
	var disabilityDetail = $(obj).parent().parent().next().find("dd").find("select");
	$.ajax({
		'url':'clm/register/getDisabilityByOrder_CLM_tClaimDutyRegisterAction.action?disabilityVO.code='+code,
		'type':'post',
		'datatype':'json',
		'success':function(data){
		   var data = eval("("+data+")");
		   $("#disabilityDetail", navTab.getCurrentPanel()).empty();
		   var count=0;
		   var option="";
			  for(var code in data){
				  if(code!=$(obj).val()){
					  if(count==0){
				  		option = option+"<option value="+code+" selected>"+data[code]+"</option>";
					  		$.ajax({
								'url':'clm/register/getPayRateByOrderAndName_CLM_tClaimDutyRegisterAction.action?disabilityVO.code='+code,
								'type':'post',
								'datatype':'json',
								'success':function(json){
								   var json = eval("("+json+")");
								   var rate = json.rate;
								   $(obj).parent().parent().parent().parent().next().next().find("dd").find("input").attr("value",rate);
								 }
							});
					  }else{
				  		option = option+"<option value="+code+">"+data[code]+"</option>";
					  }
				  }
			  }
		   $("#disabilityDetail", navTab.getCurrentPanel()).loadMyComboxOptions(option,'1');
		 }
	});
}

claimFireEvent("#disabilityDetail", navTab.getCurrentPanel())

</script>

<div id="generalNewsInt" class="panelPageFormContent">

		<div class="main_box">
				<div class="main_heading"><h1><img src="clm/images/tubiao.png">一般失能录入
					<b class="maim_lpask"></b></h1></div>
					<div class="main_lptwo">
					<div id="main_1" class="main_borderbg">
                                    <ul class="main_ul">
                                       <li class="clearfix">
					<div class="main_foldContent">
					<div class="main_bqtabdivbr" style="margin-left: 10px;">
					<div class="main_bqtitle">
                         <h1><img src="clm/images/three.png">一般失能信息录入</h1>
                     </div>
                     
                     <!--一般失能   div start  -->
	<div class="panelPageFormContent">
		<dl >
			<dt >
				<font class="point" color="red">* </font>程度等级
			</dt>
			<dd >
				<select class="combox title" id="disability" name="claimDisabilityVO.disabilityGrade"  style="width: 60%" onchange="selectChangeDisability(this);">
					<s:iterator value="disabilityVOs">
						<s:if test="claimDisabilityVO.disabilityGrade==code">
							<option value="${code}" selected>${name}</option>	
						</s:if>
						<s:else>
							<option value="${code}">${name}</option>
						</s:else>
					</s:iterator>
				</select>
			</dd>
		</dl>

		<dl >
			<dt >损失程度</dt>
			<dd >
				<select class="combox title" id="disabilityDetail" style="width: 60%" name="claimDisabilityVO.disabilityDetail"
					onchange="selectChange(this);">
					<s:iterator value="disabilityVODetails">
						<s:if test="claimDisabilityVO.disabilityDetail==code">
							<option value="${code}" selected>${name}</option>	
						</s:if>
						<s:else>
							<option value="${code}">${name}</option>
						</s:else>
					</s:iterator>
				</select>
			</dd>
		</dl>

		<dl >
			<dt >给付比例</dt>
			<dd >
				<input type="text" readonly="readonly" name="claimDisabilityVO.payRate" size="25" value="${claimDisabilityVO.payRate}"/>
			</dd>
		</dl>
	</div>
                     
         </div>
         </div>
         </li>
         </ul>
         </div>
         </div>
         </div>
</div>
	
<!--一般失能信息 div  end  -->