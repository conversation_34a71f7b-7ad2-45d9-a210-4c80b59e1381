<%@ page language="java" pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript">
//71408 理赔反洗钱优化
//根据省查询市 
function antiMoneyProvinceChangeReportData(k) {
	var cityReportId = $("#antiMoneyCity", navTab.getCurrentPanel());
	var province = $(k).val();
	$("#antiMoneyProvinceReserve", navTab.getCurrentPanel()).val($(k).val());
	$("#antiMoneyCity", navTab.getCurrentPanel()).prev().val("");
	$("#antiMoneyDistreact", navTab.getCurrentPanel()).prev().val("");
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ province,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#antiMoneyCity", navTab.getCurrentPanel()).empty();
					$("#antiMoneyDistrict", navTab.getCurrentPanel()).empty();
					$("<option value=''>市</option>").appendTo(cityReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(cityReportId);
					}
				},
			});
	
}
//根据市查询县
function antiMoneyCityChageReportData(k) {
	$("#antiMoneyCityReserve",navTab.getCurrentPanel()).val($(k).val());
	$("#antiMoneyDistreact",navTab.getCurrentPanel()).prev().val("");
	var city = $(k).val();
	var antiMoneyDistrict = $("#antiMoneyDistrict", navTab.getCurrentPanel());
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#antiMoneyDistrict", navTab.getCurrentPanel()).empty();
					$("<option value=''>区/县</option>").appendTo(antiMoneyDistrict);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(antiMoneyDistrict);
					}
				},
			});
}
//为后台备用传值字段赋值
function antiMoneyDistreactChageReportData(k) {
	$("#antiMoneyDistreactReserve", navTab.getCurrentPanel()).val($(k).val());
}

var antiMoneyCityReserve = $("#antiMoneyCityReserve", navTab.getCurrentPanel()).val();
var antiMoneyDistreactReserve = $("#antiMoneyDistreactReserve", navTab.getCurrentPanel()).val();


//初始化赋值市
var antiMoneyCityReportId = $("#antiMoneyCity", navTab.getCurrentPanel());
var antiMoneyAreaReportId = $("#antiMoneyDistrict", navTab.getCurrentPanel());
function antiMoneyCityAssign(province,city,area){
	
	$.ajax({
		'type' : 'post',
		'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
				+ province,
		'datatype' : 'json',
		'async' : true,
		'success' : function(data) {
			var data = eval("(" + data + ")");
			$("#antiMoneyCity", navTab.getCurrentPanel())
					.empty();
			$("#antiMoneyCity", navTab.getCurrentPanel()).append(
					"<option value=''>市</option>");
			for (var i = 0; i < data.length; i++) {
				if (data[i].code == city) {
					$("#antiMoneyCity", navTab.getCurrentPanel())
							.prev().val(
									data[i].code + "-"
											+ data[i].name);
					$("#antiMoneyCity", navTab.getCurrentPanel()).append(
							"<option value='" + city
									+ "' class = '"
									+ data[i].name + "'>"
									+ data[i].name
									+ "</option>");
				}
			}
			for (var i = 0; i < data.length; i++) {
				if (data[i].code != city) {
					var option1 = "<option value='"
							+ data[i].code + "'   class='"
							+ data[i].name + "' >"
							+ data[i].name + "</option>";
					$("#antiMoneyCity", navTab.getCurrentPanel()).append(option1);
				}
			}
			$("#antiMoneyCity", navTab.getCurrentPanel()).val(
					city);
		},
	});
	setTimeout("antiMoneyAreaAssign(" + city + " , "+ area +")", 0.0003);
	
}
//初始化赋值市
function antiMoneyAreaAssign (city,area){
	// 获取市为县赋值
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#antiMoneyDistrict", navTab.getCurrentPanel()).empty();
					$("#antiMoneyDistrict", navTab.getCurrentPanel()).append("<option value=''>区/县</option>");
					for (var i = 0; i < data.length; i++) {
						if (data[i].code == area) {
							$("#antiMoneyDistrict", navTab.getCurrentPanel()).prev()
									.val(data[i].code + "-" + data[i].name);
							$("#antiMoneyDistrict", navTab.getCurrentPanel()).append(
									"<option value='" + area + "' class = '"
											+ data[i].name + "'>"
											+ data[i].name + "</option>");
						}
					}
					for (var i = 0; i < data.length; i++) {
						if (data[i].code != area) {
							var option1 = "<option value='" + data[i].code
									+ "'   class='" + data[i].name + "' >"
									+ data[i].name + "</option>";
							$("#antiMoneyDistrict", navTab.getCurrentPanel()).append(option1);
						}
					}
					$("#antiMoneyDistrict", navTab.getCurrentPanel()).val(area);
				},
			});
}

var antiMoneyState = '${claimAntiMoneyVO.antiMoneyState }';
var antiMoneyCity = '${claimAntiMoneyVO.antiMoneyCity }';
var antiMoneyDistrict = '${claimAntiMoneyVO.antiMoneyDistrict }';
antiMoneyCityAssign(antiMoneyState,antiMoneyCity,antiMoneyDistrict);


//判断受益人和领款人的身份证有效止期，勾选长期多选框
var antiMoneyCertiEndDate = $('#antiMoneyCertiEndDate',navTab.getCurrentPanel()).val();
if (antiMoneyCertiEndDate == '9999-12-31'){
	$("#antiMoneyCertiEndDate", navTab.getCurrentPanel()).val("");
	$("#antiMoneyCertiEndDate", navTab.getCurrentPanel()).attr("disabled","disabled");
	$("#antiMoneyCertiEndCheckBoxId", navTab.getCurrentPanel()).attr("checked",true);
}
</script>
<div id="newCustomerDiv">
	<form id="antiMoneyForm">
		<dl>
			<dt id="pholderInsureds">投保人与被保险人关系</dt>
			<dd>
				<Field:codeTable name="claimInsuredRelationVO.insuredHoliderRe"
					id="pholderInsured"
					value="${claimInsuredRelationVO.insuredHoliderRe}"
					tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
					cssClass="notuseflagDelete combox title  required"
					whereClause="1=1"
					orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
			</dd>
		</dl>
		<dl>
			<dt id="insuredBeneFiciarys">被保险人与受益人关系</dt>
			<dd>
				<Field:codeTable name="claimBeneVO.beneRelation"
					id="insuredBeneFiciary" value="${claimBeneVO.beneRelation}"
					tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
					cssClass="notuseflagDelete combox title  required"
					whereClause="1=1"
					orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
			</dd>
		</dl>
		<dl>
			<dt id="pholderBeneFiciarys">投保人与受益人关系</dt>
			<dd>
				<Field:codeTable name="claimPayVO.beneHolderRelation"
					id="pholderBeneFiciary" value="${claimPayVO.beneHolderRelation}"
					tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
					cssClass="notuseflagDelete combox title  required"
					whereClause="1=1"
					orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
			</dd>
		</dl>
		<dl>
			<dt id="beneFiciaryPayees">领款人与受益人关系</dt>
			<dd>
				<Field:codeTable name="claimPayVO.payeeRelation"
					id="beneFiciaryPayee" value="${claimPayVO.payeeRelation}"
					tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
					cssClass="notuseflagDelete combox title  required"
					whereClause="1=1"
					orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>姓名
			</dt>
			<dd>
				<input type="hidden" name="claimAntiMoneyVO.antiMoneyCustomerId"
					id="antiMoneyCustomerId"
					value="${claimAntiMoneyVO.antiMoneyCustomerId }" /> 
				<input type="text" name="claimAntiMoneyVO.antiMoneyName" id="antiMoneyName" value="${claimAntiMoneyVO.antiMoneyName }" /> 
				<input type="hidden" name="claimAntiMoneyVO.antiMoneyType" id="antiMoneyTypeNew" />
				<!-- 保单号 -->
				<input type="hidden" name="claimAntiMoneyVO.policyCode" id="policyCodes" />
				<!-- 赔案号 -->
				<input type="hidden" name="claimAntiMoneyVO.caseNo" id="caseNos" />
				<!-- 判断出险人是否是投保人 -->
				<input type="hidden" name="claimAntiMoneyVO.insident" id="insident" value="${claimAntiMoneyVO.insident }" />
				<!-- 领款人对应受益人 -->
				<input type="hidden" name="claimAntiMoneyVO.beneId" value="${claimAntiMoneyVO.beneId }" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>性别
			</dt>
			<dd>
				<Field:codeTable cssClass="combox title"
					name="claimAntiMoneyVO.antiMoneySex"
					value="${claimAntiMoneyVO.antiMoneySex }" id="antiMoneySex"
					tableName="APP___CLM__DBUSER.T_GENDER"
					whereClause="gender_code in(1,2)" nullOption="true" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>联系电话
			</dt>
			<dd>
				<input type="text" name="claimAntiMoneyVO.antiMoneyPhone"
					id="antiMoneyPhone" value="${claimAntiMoneyVO.antiMoneyPhone }" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>国籍
			</dt>
			<dd>
				<Field:codeTable cssClass="selectToInput"
					name="claimAntiMoneyVO.antiMoneyNation" id="antiMoneyNation"
					nullOption="true" value="${claimAntiMoneyVO.antiMoneyNation}"
					tableName="APP___CLM__DBUSER.T_COUNTRY" whereClause="1=1"
					defaultValue="CHN"
					orderBy="decode(country_code,'CHN','A',country_code)" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>职业代码
			</dt>
			<dd>
				<Field:codeTable id="antiMoneyJobCode"
					name="claimAntiMoneyVO.antiMoneyJobCode"
					tableName="APP___CLM__DBUSER.T_JOB_CODE"
					whereClause="job_code not in ('Y001023') and display_order not in ('0')" orderBy="DISPLAY_ORDER"
					nullOption="true"
					cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
					value="${claimAntiMoneyVO.antiMoneyJobCode }" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>证件类型
			</dt>
			<dd>
				<Field:codeTable cssClass="combox title"
					name="claimAntiMoneyVO.antiMoneyCertiType"
					value="${claimAntiMoneyVO.antiMoneyCertiType }"
					id="antiMoneyCertiType" tableName="APP___CLM__DBUSER.T_CERTI_TYPE"
					nullOption="true"
					whereClause="code in ('0','5','2','b','4','1','e','h','8')"
					orderBy="decode(code,'0','001','5','002','2','003','b','004','4','005','1','006','8','007', code)" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>证件号码
			</dt>
			<dd>
				<input type="text" name="claimAntiMoneyVO.antiMoneyCertiNo"
					id="antiMoneyCertiNo" value="${claimAntiMoneyVO.antiMoneyCertiNo }" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>证件有效起期
			</dt>
			<dd>
				<input type="expandDateYMD"
					name="claimAntiMoneyVO.antiMoneyCertiStarDate"
					id="antiMoneyCertiStarDate"
					value="<s:date name="claimAntiMoneyVO.antiMoneyCertiStarDate" format="yyyy-MM-dd"/>" />
			</dd>
		</dl>
		<dl>
			<dt>
				<font>* </font>证件有效止期
			</dt>
			<dd>
				<input type="expandDateYMD" id="antiMoneyCertiEndDate"
					onPropertyChange="getAntiMoneyCertiEndDate();"
					value="<s:date name="claimAntiMoneyVO.antiMoneyCertiEndDate" format="yyyy-MM-dd"/>" />
				<input type="hidden" name="claimAntiMoneyVO.antiMoneyCertiEndDate"
					id="antiMoneyCertiEndId"
					value="<s:date name="claimAntiMoneyVO.antiMoneyCertiEndDate" format="yyyy-MM-dd"/>" />
				<input type="checkbox" id="antiMoneyCertiEndCheckBoxId"
					onclick="antiMoneyCertiEndCheckBox(this)" />
			</dd>
		</dl>
		<div class="mian_site">
			<dl>
				<dt>
					<font>* </font>地址
				</dt>
			</dl>
			<div class="main_detail">
				<dl>
					<dd>
						<input type="hidden" name="" value="" id=""> <input
							type="hidden" name="" value="" id=""> <input
							type="hidden" name="" value="" id="">
						<Field:codeTable cssClass="selectToInput"
							name="claimAntiMoneyVO.antiMoneyState"
							onChange="antiMoneyProvinceChangeReportData(this)"
							nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT"
							id="antiMoneyState" value="${claimAntiMoneyVO.antiMoneyState }"
							whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)" />
						<span>省/直辖市<font style="color: #FF0000">* </font></span>
					</dd>
				</dl>
				<dl>
					<dd>
						<select class="selectToInput"
							name="claimAntiMoneyVO.antiMoneyCity"
							onchange="antiMoneyCityChageReportData(this)" id='antiMoneyCity'>
						</select> <span>市<font style="color: #FF0000">* </font></span>
					</dd>
				</dl>
				<dl>
					<dd>
						<select class="selectToInput"
							name="claimAntiMoneyVO.antiMoneyDistrict" 
							id="antiMoneyDistrict">
						</select> <span>区/县<font style="color: #FF0000">* </font></span>
					</dd>
				</dl>
				<dl>
					<dd>
						<input name="claimAntiMoneyVO.antiMoneyAddress"
							id="antiMoneyAddress" size="24"
							value="${claimAntiMoneyVO.antiMoneyAddress }"
							style="width: 150px;"> <span>乡镇/街道<font
							style="color: #FF0000">* </font></span>
					</dd>
				</dl>
			</div>
		</div>
	</form>
</div>
