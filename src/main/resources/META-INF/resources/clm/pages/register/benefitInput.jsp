<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/pages/register/benefitInput.js" ></script>
<script type="text/javascript" src="clm/pages/advancepay/bankCodeCom.js"></script>
 
<!-- 支付计划页面 --> 

<style type="text/css">
    /* 删除按钮样式 */
	a.btnDelPrivate{
		display:block; width:22px; height:20px; 
		text-indent:-1000px; overflow:hidden; 
		float:left; margin-right: 3px}
		a.btnDelPrivate{
		background-position: -23px 0;
		background-repeat: no-repeat;
		background-color: transparent;
		background-image: url("clm/images/imgX.gif");
		}
</style>
<div class="main_tabdiv">
		<div  id="registerShowId">
			<div class="panelPageFormContent">
			<dl>
				<dt>赔案号</dt>
				<dd>
				   <input type="text" name="paymentPlanVO.caseNo" id="caseNo" value='<s:property value="paymentPlanVO.caseNo"/>' readonly="readonly" />
				   <input type="hidden" name="paymentPlanVO.acceptDecision" value="${paymentPlanVO.acceptDecision}" id="acceptDecision"/>
				   <input type="hidden" name="paymentPlanVO.isWaived" value="${paymentPlanVO.isWaived}" id="isWaivedId"/>
				   <input type="hidden" name="systemDateString" value="${systemDateString}" id="systemDateStringId"/>
				   <input type="hidden" name="insuredVO.customerGender" value="${insuredVO.customerGender}" id="insuredSex"/>
				   <input type="hidden" name="insuredVO.birthDate" value="${insuredVO.customerBirthdayStr}" id="insuredBirth"/>
				   <input type="hidden" name="isOCRShow" value="${isOCRShow }" id="isOCRShow"/>
				   <input type="hidden" name="beneIsNotNull" value="${beneIsNotNull }" id="beneIsNotNull"/>
				   <input type="hidden" name="actualPay" value="${actualPay }" id="actualPay"/>
				</dd>
			</dl>
			<dl>
				<dt>事件号</dt>
				<dd>
					<input type="text" name="paymentPlanVO.accidentNo" id="accidentNo" value='<s:property value="paymentPlanVO.accidentNo"/>' readonly="readonly"/> 
				</dd>
			</dl>
			<dl>
				<dt>自助个团标识</dt> 
				<dd>
					<dd>
						<select class="combox" id="autoCaseType" disabled="disabled">
							<option value=""></option>
							<option value="0" <s:if test="paymentPlanVO.autoCaseType != null && paymentPlanVO.autoCaseType == 0">selected="selected"</s:if>>个险</option>
							<option value="1"<s:if test="paymentPlanVO.autoCaseType != null && paymentPlanVO.autoCaseType == 1">selected="selected"</s:if>>个险团险</option>
						</select>
					</dd>
				</dd>	
			</dl>
		</div>	
			<div class="tabdivclassbr main_tabdiv">
			<table class="list" style="width: 100%;" >
				<thead>
					<tr >
					    <th nowrap>选择</th>
						<th nowrap>保单号</th>
						<th nowrap>险种代码</th>
						<th nowrap>险种名称</th>
						<th nowrap>赔付金额</th>
						<th nowrap>分配情况</th>
					</tr>
				</thead>
				<tbody id="beneFitInputTbId">
					<s:iterator value="listPaymentPlanVO" status="st">
						<tr align="center">
							 <td>
							    <input type="hidden" name="policyId" id="policyId" value="${policyId }">
							 	<input type="radio" name="caseId" onclick="queryBenePayee(this);" value="${caseId }:${policyId}:${busiItemId}:${busiProdCode}">
							 </td>
							 <td>${policyCode }</td>
							 <td>${busiProdCode }</td>
							 <td>${productNameSys }</td>
							 <td>${actualPay }</td>
							 <td>
							 	<s:if test="assignFlag == 1">已分配</s:if>
							 	<s:else>未分配</s:else>
							 </td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			</div>
			<div class="divider" style="border-style: none;"></div>
			<div id="claimBene" class="tabdivclassbr main_tabdiv">
				<table id="claimBeneTable" class="list" style="width: 100%;" >
					<thead>
						<tr >
							<th nowrap>选择</th>
						    <th nowrap>受益人姓名</th>
							<th nowrap>领款人姓名</th>
							<th nowrap>受益金额</th>
							<th nowrap>受益比例</th>
							<th nowrap>支付方式</th>
							<th nowrap>操作</th>
						</tr>
					</thead>
					<tbody id="claimBeneTbody">
						
					</tbody>
				</table>
			</div>
				<div class="pageFormdiv main_tabdiv">
					<input type="hidden" value="${claimTypeFlag}" id="claimTypeFlagBene"/>
					<button class="but_blue" type="button" id="addBenePayee" onclick="addBen();">添加</button> 
					<button class="but_blue" type="button" id="saveBenePayee" disabled="disabled" onclick="saveJudg(0)">保存</button>
				</div>
			<div class="divider" style="border-style: none;"></div>
			<div id="benePayeeInfo" class="panelPageFormContent">
			<form id="pagerFrom" action="clm/register/registerConfirm_CLM_toBenefitInputPageAction.action" method="post" class="pageForm required-validate"
	  			  onsubmit="return validateCallbackBankCodeCom(this, idAjaxDone)">
	  		</form>
			<form id="benePayeeInfoFor" action="clm/register/saveBenePayeeInf_CLM_toBenefitInputPageAction.action" method="post" class="pageForm required-validate"
	  			  onsubmit="return validateCallbackBankCodeCom(this, idAjaxDone)">
	  			  <input type="hidden" name="claimPayVO.caseId" id="hiddenCaseId" value="${paymentPlanVO.caseId}">
	  			  <input type="hidden" name="claimPayVO.policyId" id="hiddenPolicyId" value="">
	  			  <input type="hidden" name="claimPayVO.busiItemId" id="hiddenBusiItemId" value="">
	  			  <input type="hidden" name="claimPayVO.policyCode" id="hiddenPolicyCode" value="">
	  			  <input type="hidden" name="claimPayVO.busiProdCode" id="hiddenBusiProdCode" value="">
	  			  <input type="hidden" name="claimPayVO.isInstalment" id="hiddenIsInstalment" value="0">
	  			  <input type="hidden" name="beneVO.beneId" id="hiddenBeneId" value="">
			      <input type="hidden" name="payeeVO.payeeId" id="hiddenPayeeId" value="">
			      <input type="hidden" name="flag" id="claimPayflagId" value="">
			      <input type="hidden" name="paymentPlanVO.caseId" value="${paymentPlanVO.caseId}"/>
<!-- 			      <input type="hidden" name="isBeneExtended" id="isBeneExtended" value="0"/> -->
<!-- 			      <input type="hidden" name="isPayeeExtended" id="isPayeeExtended" value="0"/> -->
			      <div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">受益人信息
					</h1>
				</div>
				<div id="contraryPayFlagSpan">
						<dl style="width: 100%;">
					<dt style="width: 12%;">
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<a disabled="true" id ="legalPersonQueryBeneStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoBene();"  >
						<button type="button" class="but_blue " id="legalPersonQueryBeneStyleButton" disabled="disabled" >法人信息录入</button>
						</a>
			<a lookupGroup="beneVO" id ="legalPersonQueryBene" href="javaScript:void(0)" rel="legalPersonQueryBene" style="display:none;" width="1000" height="450"  > 法人信息录入</a>
					</dt>
					</dl>
					</div>
				<input type="hidden" name="beneVO.legalPersonId" value="${beneVO.legalPersonId}" id="beneLegalPersonId">
			      <dl>
					<dt><font>* </font>受益人与被保人关系</dt>
					<dd>
					 	<Field:codeTable disabled="true" name="beneVO.beneRelation" id="beneRelation" value="${beneVO.beneRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" cssClass="notuseflagDelete combox title selectChange" nullOption="true" onChange="queryCustomer(this);" 
					   		whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
					</dd>
				</dl>
				<!-- 71408 投保人与受益人关系  -->
			   	<dl>
					<dt><font>* </font>投保人与受益人关系</dt>
					<dd>
					 	<Field:codeTable disabled="true" name="claimPayVO.beneHolderRelation" id="beneHolderRelation" value="${claimPayVO.beneHolderRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" cssClass="notuseflagDelete title combox" nullOption="true" 
					   		whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
					</dd>
				</dl>
				
				
				<dl>
					<dt><font>* </font>姓名</dt>
					<dd>
					   <input type="text" disabled="disabled" name="beneVO.beneName" id="beneName" value="${beneVO.beneName }" onblur="getBeneInfoRegi();"/>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>性别</dt>
					<dd>
					   <Field:codeTable disabled="true" cssClass="combox title"  name="beneVO.beneSex" id="beneSex" value="${beneVO.beneSex}" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2,9)" nullOption="true" onChange="getBeneInfoRegi();"/>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>出生日期</dt>
					<dd>
					   <input disabled="disabled" type="expandDateYMD" name="beneVO.beneBirth" id="beneBirth" value="<s:date name="beneVO.beneBirth" format="yyyy-MM-dd"/>" onPropertyChange="getBeneInfoRegi();"/>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>证件类型</dt>
					<dd>
					   <Field:codeTable disabled="true" cssClass="combox title"  name="beneVO.beneCertiType" id="beneCertiType" value="${beneVO.beneCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  nullOption="true" onChange="getBeneInfoRegi();"
					    	whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','73','8')"  orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','73','012','8','013', code)" />
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>证件号码</dt>
					<dd>
					   <input type="text" disabled="disabled" name="beneVO.beneCertiNo" id="beneCertiNo" value="${beneVO.beneCertiNo }" onblur="checkbeneCertiNo();" onchange="getBeneInfoRegi();" onselectstart="return false" oncopy="return false" onpaste="return false"/>
					</dd>
				</dl>
				<dl>
					<dt><font color="red">* </font>受益人国籍</dt>
					<dd>
					   <Field:codeTable disabled="true" cssClass="selectToInput"  name="beneVO.beneNation" onChange="getBeneInfoRegi();" id="clmtNation" value="${beneVO.beneNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" nullOption="true" whereClause="country_code not in ('GAT')" defaultValue="CHN" orderBy="decode(country_code,'CHN','A',country_code)"/>
					   
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>证件有效起期</dt>
					<dd>
					   <input onPropertyChange="getBeneInfoRegi();"  type="expandDateYMD"  name="beneVO.beneCertiStart"  id="beneCertiStart" value="<s:date name="beneVO.beneCertiStart" format="yyyy-MM-dd"/>"/>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>证件有效止期</dt>
					<dd>
					   <input type="expandDateYMD" name="beneVO.beneCertiEnd"  id="beneCertiEnd"  value="<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>"/>
					   <input type="hidden" name="beneVO.beneCertiEnd" id="beneCertiEndHidentId"  value="<s:date name="beneVO.beneCertiEnd" format="yyyy-MM-dd"/>"/>	
					   <input type="checkbox" id="beneCertiEndCheckBoxId" onclick="beneCertiEndCheckBox(this)"/>长期
					</dd>
				</dl>
				<!-- 51699增加受益相关信息录入 -->
				<dl>
					<dt><font>* </font>受益人职业代码</dt>
					<dd>
						<Field:codeTable  id="beneJobIdFire"
												name="beneVO.beneJobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
												whereClause="job_code not in ('Y001023') and display_order not in ('0')"  orderBy="DISPLAY_ORDER"
												nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
												value="${beneVO.beneJobCode}" disabled="true"/>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>受益人电话</dt>
					<dd>
					   <input type="text" name="beneVO.benePhone" disabled="disabled" id="benePhone"  value="${beneVO.beneJobCode }" onblur="checkPhoneAndMobile(this);"/>
					</dd>
				</dl>
				<div class="mian_site">
							<dl>
								<dt><font>* </font>受益人地址</dt>
							</dl>
							<div class="main_detail">
								<dl>
									<dd>
									   	<input type="hidden" name="" value="${beneVO.beneProvince}" id="beneProvinceReserve">
										<input type="hidden" name="" value="${beneVO.beneCity}" id="beneCityReserve">
										<input type="hidden" name="" value="${beneVO.beneDistrict}" id="beneDistreactReserve">
				                           	<Field:codeTable cssClass="selectToInput"  name="beneVO.beneProvince" onChange="ProvinceChangeReportData(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="beneProvince" 
				                           	value="${beneVO.beneProvince }" disabled="true"
				                           	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
										<span>省/直辖市<font style="color:#FF0000">* </font></span>
									</dd>
								</dl>
								<dl>
									<dd>
										<select class="selectToInput" disabled="disabled" name="beneVO.beneCity" onchange="cityChageReportData(this);" id='beneCity' >
											</select>
										<span>市<font style="color:#FF0000">* </font></span>
									</dd>
								</dl>
								<dl>
									<dd>
										<select class="selectToInput" disabled="disabled" name="beneVO.beneDistrict" onchange="DistreactChageReportData(this);" id="beneDistrict" >
											</select>
										<span>区/县<font style="color:#FF0000">* </font></span>
									</dd>
								</dl>
								<dl>
									<dd>	
				                           	<input name="beneVO.beneAddress" id="beneAddress" disabled="disabled" size="24" value="${beneVO.beneAddress}" style="width: 150px;">
										<span>乡镇/街道<font style="color:#FF0000">* </font></span>
									</dd>
								</dl>
							</div>
						</div>						
				
				<dl>
					<dt>电子邮箱</dt>
					<dd>
						<input type="text"  name="beneVO.beneEmail" id="beneEmail" value="${beneVO.beneEmail }">
					</dd>
				</dl>
				<dl>
					<dt>剩余比例</dt>
					<dd>
						<input type="text"  name="residueRate" id="beneRate" readonly="">
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>受益分子</dt>
					<dd>
					   <input disabled="disabled" name="claimPayVO.payMole" min="0" id="payMole" onblur="computePayAmounts();" value="${claimPayVO.payMole }" class="number"/>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>受益分母</dt>
					<dd>
					   <input disabled="disabled" name="claimPayVO.payDeno" id="payDeno" onblur="computePayAmounts();" value="${claimPayVO.payDeno }" class="number"/>
					</dd>
				</dl>
					   <input type="hidden" name="claimPayVO.claimPayId" id="claimPayIdHidden" value="${claimPayVO.claimPayId }">
				<dl>
					<dt>受益金额</dt>
					<dd>
						<input type="text" name="claimPayVO.payAmount" id="payAmount" readonly="readonly">
					</dd>
				</dl>
			      
			      <div class="divider" style="border-style: none;"></div>
			      <div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">领款人信息
					</h1>
						<div id="contraryPayFlagSpan">
						
					</div>
				</div>
					<input type="hidden" name="claimPayVO.contraryPayFlag" value="${claimPayVO.contraryPayFlag}" id="contraryPayFlag">
					<input type="hidden" name="payeeVO.legalPersonId" value="${payeeVO.legalPersonId}" id="payeeLegalPersonId">
			      	<dl style="width: 100%;">
					<dt>
						<a disabled="true" id ="legalPersonQueryPayeeStyle" href="javaScript:void(0)" onclick="queryLegalPersonInfoPayee();" >
						<button type="button" class="but_blue " id="legalPersonQueryPayeeStyleButton" disabled="disabled" >法人信息录入</button>
						</a>
			<a lookupGroup="payeeVO" id ="legalPersonQueryPayee" href="javaScript:void(0)" style="display:none;"  width="1000" height="450"  > 法人信息录入</a>
					</dt>
					<dt>
					对公支付，请勾选<input type="checkbox" id="contraryPayFlagCheckBoxId" onclick="chooseMethod();"/>
					</dt>
					</dl>
			      	<dl>
					<dt><font>* </font>领款人与受益人关系</dt>
					<dd>
						<s:if test="claimPayVO.payeeRelation != null && claimPayVO.payeeRelation != ''">
							<Field:codeTable disabled="true" onChange="queryPayeeRelation();" name="payeeVO.payeeRelation" id="payeeRelation" value="${claimPayVO.payeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" cssClass="notuseflagDelete combox title selectChange" 
								whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
						</s:if>
						<s:else>
							<Field:codeTable disabled="true" onChange="queryPayeeRelation();" name="payeeVO.payeeRelation" id="payeeRelation" value="${payeeVO.payeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" cssClass="notuseflagDelete combox title selectChange" 
								whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
						</s:else>	
						<input type="hidden" name="claimPayVO.payeeRelation" id="payPayeeRelationId"/>
						<%-- 	
						</s:if>
						<s:else>
								<Field:codeTable disabled="true" onChange="queryPayeeRelation();" name="payeeVO.payeeRelation" id="payeeRelation" value="${payeeVO.payeeRelation}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" cssClass="notuseflagDelete combox title selectChange" 
							whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
						</s:else>	
							<input type="hidden" name="claimPayVO.payeeRelation" id="payPayeeRelationId"/> --%>
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>姓名</dt>
					<dd id="payeeNameRegister">
					     <s:if test="isOCRShow == 1">
					     <input type="text"  name="payeeVO.payeeName"  value="${payeeVO.payeeName }"  onblur="resetStyle(this)"/>
							<select  disabled="disabled"  id="payeeName" onchange="findclaimPayeeByName(this)"  style="display: none">
							
							</select>
					     </s:if>
					     <s:else>
					     <input type="hidden"  name="payeeVO.payeeName"  value="${payeeVO.payeeName }" onblur="resetStyle(this)"/>
							<select class="selectToInput" disabled="disabled" islegitimacy="true"   id="payeeName" onchange="findclaimPayeeByName(this)"  >
							
							</select>
					     </s:else>
					  	 
					</dd>
				</dl>
				<dl>
					<dt>性别</dt>
					<dd>
						<Field:codeTable disabled="true" cssClass="combox title"  name="payeeVO.payeeSex" id="payeeSex" value="${payeeVO.payeeSex}" tableName="APP___CLM__DBUSER.T_GENDER" nullOption="true" whereClause="gender_code in(1,2,9)" />
					</dd>
				</dl>
				<dl>
					<dt>出生日期</dt>
					<dd>
					   <input disabled="disabled" onPropertyChange="getpayeeInfoRegi();" type="expandDateYMD" name="payeeVO.payeeBirth" id="payeeBirth" value="<s:date name="payeeVO.payeeBirth" format="yyyy-MM-dd"/>"/>
					</dd>
				</dl>
				<dl>
					<dt>证件类型</dt>
					<dd>
					   <Field:codeTable disabled="true" onChange="getpayeeInfoRegi();" cssClass="combox title"  name="payeeVO.payeeCertiType" id="payeeCertiType" value="${payeeVO.payeeCertiType}" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  nullOption="true"
					    	whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','73','8')"  orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','73','012','8','013', code)" />
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
					   <input type="text" disabled="disabled" name="payeeVO.payeeCertiNo" id="payeeCertiNo" value="${payeeVO.payeeCertiNo }" onblur="checkpayeeCertiNo();" onselectstart="return false" oncopy="return false" onpaste="return false"/>
					</dd>
				</dl>
				<dl>
					<dt>领款人国籍</dt>
					<dd>
					   <Field:codeTable cssClass="selectToInput" disabled="true" name="payeeVO.payeeNation" id="clmtNationL" value="${payeeVO.payeeNation}" tableName="APP___CLM__DBUSER.T_COUNTRY" nullOption="true" whereClause="country_code not in ('GAT')" defaultValue="CHN" orderBy="decode(country_code,'CHN','A',country_code)"/>
					</dd>
				</dl>
				<dl>
					<dt>证件有效起期</dt>
					<dd>
					   <input type="expandDateYMD" name="payeeVO.payeeCertiStart" id="payeeCertiStart" value="<s:date name="payeeVO.payeeCertiStart" format="yyyy-MM-dd"/>" onPropertyChange="getpayeeInfoRegi();"/>
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
					   <input type="expandDateYMD" name="payeeVO.payeeCertiEnd" id="payeeCertiEnd" gt="payeeCertiStart"  value="<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>"/>
					   <input type="hidden" name="payeeVO.payeeCertiEnd" id="payeeCertiEndHidentId" value="<s:date name="payeeVO.payeeCertiEnd" format="yyyy-MM-dd"/>"/>	
					   <input type="checkbox" id="payeeCertiEndCheckBoxId" onclick="payeeCertiEndCheckBox(this)"/>长期
					</dd>
				</dl>
				<!-- 71408 关于系统理赔环节反洗钱优化方案的需求 -->
				<dl>
					<dt>领款人职业代码</dt>
					<dd>
						<Field:codeTable  id="payeeJobIdFire"
												name="payeeVO.payeeJobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
												whereClause="job_code not in ('Y001023') and display_order not in ('0')"  orderBy="DISPLAY_ORDER"
												nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
												value="${payeeVO.payeeJobCode}" disabled="true"/>
					</dd>
				</dl>
				<dl>
					<dt>领款人电话</dt>
					<dd>
					   <input type="text" name="payeeVO.payeePhone" disabled="disabled" id="payeePhone"  value="${payeeVO.payeePhone }"/>
					</dd>
				</dl>
				<div class="mian_site">
							<dl>
								<dt>领款人地址</dt>
							</dl>
							<div class="main_detail">
								<dl>
									<dd>
									   	<input type="hidden" name="" value="${payeeVO.payeeState}" id="payeeProvinceReserve">
										<input type="hidden" name="" value="${payeeVO.payeeCity}" id="payeeCityReserve">
										<input type="hidden" name="" value="${payeeVO.payeeDistrict}" id="payeeDistreactReserve">
				                           	<Field:codeTable cssClass="selectToInput"  name="payeeVO.payeeState" onChange="payeeProvinceChangeReportData(this)" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="payeeProvince" 
				                           	value="${payeeVO.payeeState }" disabled="true"
				                           	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
										<span>省/直辖市</span>
									</dd>
								</dl>
								<dl>
									<dd>
										<select class="selectToInput" disabled="disabled" name="payeeVO.payeeCity" onchange="payeeCityChageReportData(this)" id="payeeCity" >
											</select>
										<span>市</span>
									</dd>
								</dl>
								<dl>
									<dd>
										<select class="selectToInput" disabled="disabled" name="payeeVO.payeeDistrict" onchange="" id="payeeDistrict" >
											</select>
										<span>区/县</span>
									</dd>
								</dl>
								<dl>
									<dd>	
				                           	<input name="payeeVO.payeeAddress" id="payeeAddress" disabled="disabled" size="24" value="${payeeVO.payeeAddress}" style="width: 150px;">
										<span>乡镇/街道</span>
									</dd>
								</dl>
							</div>
						</div>	
				
				<dl>
					<dt>电子邮箱</dt>
					<dd>
						<input type="text"  name="payeeVO.payeeEmail" id="payeeEmail" value="${payeeVO.payeeEmail }">
					</dd>
				</dl>
				<dl>
					<dt><font>* </font>支付方式</dt>
					<dd>
						<input type="hidden" id="PayModeIdId" value="${payeeVO.payMode}"/>
						<select disabled="disabled" onchange="onChangePayModeRegister(this)" class="combox title"  id="PayModeId" name="payeeVO.payMode">
							<option value="">请选择</option>
							<option value="32">银行转账(制返盘)</option>
							<option value="34">网上银行</option>
						   <!--  <option value="21">现金支票</option> -->
							<option value="22">转账支票</option>
							<option value="10">现金</option>
							<!-- <option value="42">客户账户</option> -->
						</select>
					</dd>
				</dl>
				<dl class="nowrap">
					<dt>银行编码</dt>
					<dd>
<%-- 					   <Field:codeTable cssClass="selectToInput"  name="payeeVO.bankCode" value="${payeeVO.bankCode}" tableName="APP___CLM__DBUSER.T_BANK" id="bankCodeId"  whereClause="BANK_CODE not in ('4000055')"/> --%>
					   <select disabled="disabled" class="selectToInput" name="payeeVO.bankCode" id="bankCodeId">
					   </select>
					   <input type="hidden" name="hiddenBank" id="hiddenBank" value="${payeeVO.bankCode }">
					</dd>
				</dl>
				<dl>
					<dt>银行账户名</dt>
					<dd>
					   <input disabled="disabled" type="text" name="payeeVO.accountName" id="accountName" value="${payeeVO.accountName }"/>
					</dd>
				</dl>
				<dl>
					<dt>银行账号</dt>
					<dd>
					   <input disabled="disabled" name="payeeVO.accountNo" id="accountNo" value="${payeeVO.accountNo }" onblur="trueFalse();" onkeyup="this.value=this.value.replace(/[^\d-]/g,'')"  onpaste="return false" oncontextmenu="return false" oncopy="return false" oncut="return false"/>
					</dd>
				</dl>
				<div style="display: none" id="isBankOfDepositDiv">
				<dl>
					<dt>开户行名称</dt>
					<dd>
					<input id=bankOfDepositId name="payeeVO.name" value="${payeeVO.name}" 
							postFiled="keywords" suggestFields="code,name" 
							suggestUrl="clm/pages/register/bankDetails.jsp" readonly
							lookupGroup="payeeVO"/>
							<a class="btnLook" 
							href="clm/register/bankDetails_CLM_toBenefitInputPageAction.action"
							lookupGroup="payeeVO" onclick="myBankOption(this);" id="btnLookBankOfDeposit">开户行信息查询</a>											
							<input id="correspondentNameId" name="payeeVO.bankOfDeposit" type="hidden" value="${payeeVO.bankOfDeposit}"/>
					</dd>
				</dl>
			</div>
			</form>
			 </div>
		</div>
	
	<div class="formBarButton main_bottom">
		<ul>
			<li> <button class="but_blue" type="button" onclick="setTimeout(preSave(),100)">上一步</button> </li>
			<%--<s:if test="isOCRShow == 1">
			 <li> <a id="" class="but_blue main_buta" 
					href="clm/register/findOCRInfo_CLM_toBenefitInputPageAction.action?caseId=${paymentPlanVO.caseId}"
					target="dialog" rel="page2" type="button" width="1000" height="520">OCR识别信息</a></li>
			</s:if>
			<s:else>
			<li> <a id="" class="but_blue main_buta"  disabled="disabled"
					href="clm/register/findOCRInfo_CLM_toBenefitInputPageAction.action?caseId=${paymentPlanVO.caseId}"
					target="dialog" rel="page2" type="button" width="1000" height="520">OCR识别信息</a></li>
			</s:else> --%>
			<li>
			<a id="" class="but_blue main_buta" 
					href="clm/register/findInsuredInfo_CLM_claimAntiMoneyLaunderingAction.action?claimCaseVO.caseId=${paymentPlanVO.caseId}"
					target="dialog" rel="page2" type="button" width="1100" height="520">被保险人身份基本信息</a></li>
			<li> <button class="but_blue" type="button"  class="submit" onclick="registerConfirm(${paymentPlanVO.caseId},${paymentPlanVO.caseNo},${paymentPlanVO.accidentNo})">立案确认</button> </li> 
			<li> <button class="but_gray" type="button" onclick="exit()">退出</button> </li>
		</ul>
	</div>
</div>