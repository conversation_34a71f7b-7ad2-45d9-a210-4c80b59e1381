<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<style type="text/css">
	table.list .white_bg{ background:#fff;}
	table.list .white_bg:hover{ background:#daf0fe;}
</style>
 <script type="text/javascript">
 var trs = $("#matchtTbodyId",$.pdialog.getCurrent()).find("tr");
 if(trs.length > 0){
	 for(var i = 0; i < trs.length; i++){
		 var trss = $("#matchtTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(6)").find("tr");
		 if(i%2==1){
// 			$("#matchtTbodyId #strTopbg"+(i+1)).css('background-color','white');
// 			$("#matchtTbodyId #strTopbg"+(i+1) +" #strchirld").css('background-color','white');
			$("#matchtTbodyId #strTopbg"+(i+1),$.pdialog.getCurrent()).attr('class','white_bg');
			$("#matchtTbodyId #strTopbg"+(i+1) +" #strchirld",$.pdialog.getCurrent()).attr('class','white_bg');
		 }
		 if(trss.length > 0){
			 for(var z = 0; z < trss.length; z++){
				 $("#matchtTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(6)").find("tr:eq("+z+")").find("td:eq(0)").attr("width","45%");
				 $("#matchtTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(6)").find("tr:eq("+z+")").find("td:eq(1)").attr("width","20%");
				 $("#matchtTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(6)").find("tr:eq("+z+")").find("td:eq(2)").attr("width","20%");
			 }
		 }
	 }
 }
</script>
<%@ page import="java.util.*"%>

		<title>自动匹配理算日志</title>
		
	<div layoutH="0">
			<div class="tabdivclassbr">
				<table class="list" width="100%" align="center" border="0" cellspacing="0" cellpadding="0'" >
					<thead>
						<tr>
							<th rowspan="2" align="center">序号</th>
							<th rowspan="2" align="center">保单号</th>
							<th rowspan="2" align="center">险种代码</th>
							<th rowspan="2" align="center">险种名称</th>
							<th rowspan="2" align="center">保险责任</th>
							<th align="center">理算日志</th>
						</tr>
						<tr>
						    <th align="center" style=" padding:0;">
						     <table width="100%" border="0" cellspacing="0" cellpadding="0" class="list" style="border:0px;">
						      <tr>
						        <th align="center"  width="45%" style="border-bottom:0">理赔相关因子</th>
						        <th align="center"  width="20%" style="border-bottom:0">理赔因子值</th>
						        <th align="center" width="20%" style="border-bottom:0"><div style="width: 64px; overflow-wrap: break-word;">理赔实际值</div></th>
						        <th align="center"  width="15%" style="border-bottom:0; border-right:0px;">匹配结果</th>
						      </tr>
						      </table>
						    </th>
					    </tr>
					</thead>
					<tbody id="matchtTbodyId">
					    <s:iterator value="claimLiabLogVOs" status="st" id="claimLiabLogVO">
					    <tr class="trbg" id="strTopbg${st.index+1}" <s:if test="#claimLiabLogVO.dsFlag==1"> style="background:#DDFFF1"</s:if>>
	                        <td  align="center" valign="middle"><s:property value="#st.index+1"></s:property></td>               
	                        <td  align="center" valign="middle"><s:property value="#claimLiabLogVO.policyCode"/></td>
	                        <td  align="center" valign="middle"><s:property value="#claimLiabLogVO.busiProdCode"/></td>
	                        <td  align="center" valign="middle"><s:property value="#claimLiabLogVO.productNameStd"/></td>
	                        <td  align="center" valign="middle">
	                        	 <s:if test="#claimLiabLogVO.liabId==9999">
	                        		豁免保险金
	                        	</s:if>
	                        	<s:else>
			                        <Field:codeValue value="${claimLiabLogVO.liabId}" tableName="APP___CLM__DBUSER.T_LIABILITY"/>
	                        	</s:else> 
	                        	
	                        </td>
	                        <td  align="center" valign="middle" style="padding:0;"height="100%">
	                       		<table style="width: 100%; border:0px;" border="0" cellspacing="0" cellpadding="0" class="list" height="100%">
								<!-- <tbody> -->	
									    <s:iterator value="#claimLiabLogVO.claimMatchJournaVOs" status="st" id="claimMatchJournaVO">
						                    <tr class="trbg" id="strchirld" <s:if test="#claimMatchJournaVO.dsFlag==1"> style="background:#DDFFF1"</s:if>>
						                    	<s:if test="#claimMatchJournaVO.relativeId==14">
							                        <td align="center"  width="45%"  >${claimMatchJournaVO.relativeName}</td>               
							                        <td align="center"  width="20%"  ><Field:codeValue value="${claimMatchJournaVO.relativeValue}" tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"/></td>
							                        <td align="center" ><div style="width: 54px; overflow-wrap: break-word;"><Field:codeValue value="${claimMatchJournaVO.clmValue}" tableName="APP___CLM__DBUSER.T_CLAIM_TYPE"/></div></td>
						                    	</s:if>
						                    	<s:elseif test="#claimMatchJournaVO.relativeId==13">
						                    		<td align="center" width="45%" >${claimMatchJournaVO.relativeName}</td>               
							                      	<td align="center" width="20%" ><Field:codeValue value="${claimMatchJournaVO.relativeValue}" tableName="APP___CLM__DBUSER.T_CLAIM_NATURE"/></td>
							                        <td align="center" ><div style="width: 54px; overflow-wrap: break-word;"><Field:codeValue value="${claimMatchJournaVO.clmValue}" tableName="APP___CLM__DBUSER.T_CLAIM_NATURE"/></div></td>
						                    	</s:elseif>
						                    	<s:else>
						                    	 	<td align="center" width="45%" >${claimMatchJournaVO.relativeName}</td>               
							                        <td align="center" 	width="20%" >${claimMatchJournaVO.relativeValue}</td>
							                        <td align="center" ><div style="width: 54px; overflow-wrap: break-word;">${claimMatchJournaVO.clmValue}</div></td>
							                    </s:else>
							                    <s:if test="#claimMatchJournaVO.matchResult==1">
							                    	<td align="center" width="15%" ><span>成功</span></td>
							                    </s:if>
							                    <s:else>
							                   		<td align="center" width="15%" style="color:red"><span>失败</span></td>
							                    </s:else>
						                    </tr>
										</s:iterator>
									<!-- </tbody> -->	
	
								</table>
	                        </td>
	                    </tr>
						</s:iterator>
					</tbody>
	
				</table><br/>
			</div>
			<div class="formBarButton">
				<button type="button" class="but_gray close">关闭</button>
			</div>
	</div>