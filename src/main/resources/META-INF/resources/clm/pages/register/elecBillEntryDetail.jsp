<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script type="text/javascript">

</script>

<div layoutH="36" class="pageHeader">


<div class="divfclass">
	<h1>
		<img src="clm/images/tubiao.png">电票入账明细
	</h1>
</div>
<div class="tabdivclassbr">
	<table class="list" style="border: 2px;" width="100%">
		<thead>
			<tr>
				<th nowrap>序号</th>
				<th nowrap>电子票据号码</th>
				<th nowrap>入账金额</th>
				<th nowrap>入账时间</th>
			</tr>
		</thead>
		<tbody>
			<s:if test="claimBillEntryVOs != null">
				<s:iterator value="claimBillEntryVOs" var="claimBillEntry" status="st">
					<tr>
						<td>
						<input type="hidden" value="${st.index}">
						${st.index+1}
						</td>
						<td>
						${claimBillEntry.elecBillNum}
						</td>
						<td>
						${claimBillEntry.entryAmount}
						</td>
						<td>
						<input type="text"  name="claimBillEntry.entryDate"  value="<s:date name='entryDate' format='yyyy-MM-dd' />" readonly="readonly" />
						</td>
					</tr>
				</s:iterator>
			</s:if>
		</tbody>
	</table>
</div>

	<div  class="formBarButton">
		<ul>
			<li><button class="but_gray" type="button" onclick="exitDialog();">退出</button></li>
		</ul>
	</div>

