﻿﻿<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<script type="text/javascript" language="javascript"
	src="clm/pages/register/Claimregister.js">
</script>
<script type="text/javascript">
	
	window.setTimeout(function(){
		var viewNum = ${viewNum};
		if(viewNum == 0){//数据采集
			$("#n11",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
			$("#11",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#12",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#13",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#14",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#15",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#16",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			
			$("#step11",navTab.getCurrentPanel()).find("div").attr("class","main_step");
			$("#step12",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step13",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step14",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step15",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step16",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		}
		if(viewNum == 1){//单证确认
			$("#n11",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n12",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
			$("#11",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#12",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#13",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#14",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#15",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#16",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			
			$("#step11",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step12",navTab.getCurrentPanel()).find("div").attr("class","main_step");
			$("#step13",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step14",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step15",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step16",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			
		}
		if(viewNum == 2){//责任明细
			$("#n12",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n11",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n13",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
			$("#11",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#12",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#13",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#14",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#15",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#16",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			
			$("#step11",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step12",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step13",navTab.getCurrentPanel()).find("div").attr("class","main_step");
			$("#step14",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step15",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step16",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		}
		if(viewNum == 3){//保单挂起/解挂
			$("#n13",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n12",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n11",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n14",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
			$("#11",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#12",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#13",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#14",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#15",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			$("#16",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			
			$("#step11",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step12",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step13",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step14",navTab.getCurrentPanel()).find("div").attr("class","main_step");
			$("#step15",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step16",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		}		
		if(viewNum == 4){//保单挂起/解挂
			$("#n14",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n15",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
			$("#n11",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n12",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n13",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#11",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#12",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#13",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#14",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#15",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#16",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","display:none");
			});
			
			$("#step11",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step12",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step13",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step14",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step15",navTab.getCurrentPanel()).find("div").attr("class","main_step");
			$("#step16",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
		}		
		if(viewNum == 5){//保单挂起/解挂
			$("#n11",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n12",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n13",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n14",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n15",navTab.getCurrentPanel()).find("div").attr("class","main_n2");
			$("#n16",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");
			$("#11",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#12",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#13",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#14",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#15",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			$("#16",navTab.getCurrentPanel()).each(function(){
				$(this).attr("style","");
			});
			
			$("#step11",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step12",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step13",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step14",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step15",navTab.getCurrentPanel()).find("div").attr("class","main_stepOther");
			$("#step16",navTab.getCurrentPanel()).find("div").attr("class","main_step");
		}		
	},100);




//获取页面的赔案号
function alwaysClaim(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/queryAlwaysClaim_CLM_commonQueryAction.action?caseId="+caseId;
	$("#comAlwaysClaim", navTab.getCurrentPanel()).attr("href",url).click();
} 
function policQueryPage(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/initPolicQueryPage_CLM_commonQueryAction.action?caseId="+caseId;
	$("#policQueryId", navTab.getCurrentPanel()).attr("href",url).click();
}

//影像库查询huangjh_wb 
function queryImageMsgFormECS() {
	$.post("clm/common/queryImageUrl_CLM_commonQueryAction.action",function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			var urlValue= data.message.replace(/\&/g,"%26");//替换掉&符号
			navTab.openTab("title", "clm/common/queryImageMsgUrl_CLM_commonQueryAction.action?urlValue="+urlValue, {title:'影像库查询'});
		}else{
			alertMsg.error("获取影像url失败");
		}
	},'json');
}

//备注信息查看
function getMemoRegister() {
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url = "clm/memo/memoView_CLM_memoAction.action?clmCaseVO.caseId="+caseId;
	$("#memoRegister", navTab.getCurrentPanel()).attr("href", url).click();
}
//获取页面的赔案号
function queryPolicyClause(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	/* var url="clm/common/queryPolicyListByCaseId_CLM_queryPolicyClauseAction.action?caseId="+185; //查询赔案关联的保单和险种*/
	var url="clm/common/queryPolicyNoByCaseId_CLM_queryPolicyClauseAction.action?caseId="+caseId;
	$("#comPolicyClause", navTab.getCurrentPanel()).attr("href",url).click();
}
//影像查询gaojun_wb 
function queryRegisClaimImages() {
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	$.post("clm/common/queryClaimImages_CLM_commonQueryAction.action",{caseId:caseId},function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			var urlValue= data.message;//替换掉&符号
			window.open(urlValue);
		}else{
		   alertMsg.error("调用影像接口失败");
		}
	},'json');
}
//查看风险保额  xuyz_wb add
function queryCustomerRiskRegister() {
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	var url = "clm/common/queryCustomerRisk_CLM_commonQueryAction.action?caseId="
			+ caseId;
	$("#queryCustomerRiskRegister", navTab.getCurrentPanel()).attr("href", url).click();
}
//查看报案、立案、签批信息
function caseQueryPage(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/caseCommonQueryPage_CLM_commonQueryAction.action?caseId="+caseId;
	$("#rePortRegisSignId", navTab.getCurrentPanel()).attr("href",url).click();		
} 


//签名比对 
function comparisonToRegister() {
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	 
	$.post("clm/common/signatureComparison_CLM_commonQueryAction.action",{caseId:caseId},function(data){
		if(data.message!=""){
			//不等于空点击返回的url
			window.open(data.message);
		}else{
			alertMsg.error("调用影像接口失败");
		}
	},'json');
}

//发起转办
function claimBizTurn(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/queryPolicyBizTurn_CLM_queryPolicyClauseAction.action?caseId="+caseId;
	$("#claimBizTurnId", navTab.getCurrentPanel()).attr("href",url).click();
}
//查看电子病历
function queryEMR(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/common/queryEMRNew_CLM_commonQueryAction.action?caseId="+caseId;
	$("#emr", navTab.getCurrentPanel()).attr("href",url).click();
}
//行业共享信息提示
function queryCiitcBackResult(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="clm/parameter/tradeShareInfo_CLM_ciitcLinkSwitchSetAction.action?caseId="+caseId;
	$("#ciitcBackResult", navTab.getCurrentPanel()).attr("href",url).click();
}

//电子签名轨迹
function querySignatureTrace(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
    var url = "clm/common/querySignatureTrace_CLM_commonQueryAction.action?caseId="+ caseId;

	$("#querySignatureTrace", navTab.getCurrentPanel()).attr("href", url).click();	
}


//垫付信息查看
function initAdvance(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	var url="	clm/advancepay/initAdvanceInfo_CLM_claimAdvanceInfoAction.action?caseId="+caseId;
	$("#init", navTab.getCurrentPanel()).attr("href",url).click();
}


</script>
<div layoutH="10" >
	<!-- 页面标题 -->
	<input type="hidden" id ="caseId" value="${caseId}">
	<div class="step_header"  >
	<table width="100%" border="0" cellspacing="0" cellpadding="0" >
	  <tr>
	    <td width="2%" id="n11"><div class="main_n1d">1</div></td>
	    <td  id="step11"><div class="main_step">数据采集</div></td>
	    <td width="2%" id="n12"><div class="main_n2">2</div></td>
	    <td  id="step12"><div class="main_stepOther">单证确认</div></td>
	    <td width="2%" id="n13"><div class="main_n2">3</div></td>
	    <td  id="step13"><div class="main_stepOther">责任明细</div></td>
	    <td width="2%" id="n14"><div class="main_n2">4</div></td>
	    <td  id="step14"><div class="main_stepOther">保单挂起/解挂</div></td>
	    <td width="2%" id="n15"><div class="main_n2">5</div></td>
	    <td  id="step15"><div class="main_stepOther">自动匹配理算</div></td>
	    <td width="2%" id="n16"><div class="main_n2">6</div></td>
	    <td  id="step16"><div class="main_stepOther">领款人与受益人录入</div></td>
	  </tr>
	  <tr>
	    <td height="20"></td>
	
	    
	  </tr>
	</table>
</div>
	
	
	<div class="pageContent">
		<div class="tabs" currentIndex="${viewNum}" eventType="click">
			<div id="claimRegisterCmdiv" style="display:none;" onmouseleave="hideAlertInfo(claimRegisterCmdiv);" onmouseup="MM_changeProp('claimRegisterCmdiv','','display','none','DIV')">
             		 <dl class="colummenu" id="claimRegisterCm">
						<!-- <dt><a href="javascript:;" target="dialog" rel="page2">查看报案/签收/立案信息</a></dt> -->
						<dt>
							<a href="#" onclick="caseQueryPage();">报案信息查看</a><a
								style="visibility: hidden;" id="rePortRegisSignId" href="#"
								target="navTab" title="报案信息查看"></a>
						</dt>
	                    <dt ><a href="javaScript:void(0)"  onclick="policQueryPage();">保单查询 </a><a id ="policQueryId" rel="policQueryId" href="#"  target="navTab" style="display:none;">保单查询</a></dt> 
	                    <!-- <dt ><a href="clm/common/initPolicQueryPage_CLM_commonQueryAction.action?caseId=188" target="dialog" rel="page2" width="1000" height="450">保单查询 </a></dt> -->
	                    <dt><a href="javaScript:void(0)" id="comAlwaysClaimFirst"  onclick="alwaysClaim()">既往赔案查询</a><a id="comAlwaysClaim" rel="comAlwaysClaim" href="#"  target="navTab" style="display:none;">既往赔案查询</a></dt>
	                    <dt><a href="javaScript:void(0)" id="memoAuditFirst" onclick="getMemoRegister();">备注信息查看</a>
	                    	 <a id="memoRegister" href="javaScript:void(0)"  target="dialog" [mask=true ] style="display:none;"  width="1000" height="450"  rel="memoRegister">备注信息查看</a></dt>
	                    <dt><a href="javaScript:void(0)" onclick="queryRegisClaimImages()">影像查询</a></dt>
	                    <dt >
	                    <a href="javaScript:void(0);" id="queryImageMsgInfo"
								 onclick="queryImageMsgFormECS()">影像库查询</a>
								 <a id="queryImageMsg" href="#" rel="queryImageMsg" target="navTab"
									[mask=true ] style="display: none;" width="1000" height="450">影像库查询</a>
	                    </dt>
                       <dt ><a href="sp_15.html" id="claimRISFirst"  target="dialog" [mask=true ]   width="1000" height="450"  rel="page2">查看再保意见</a></dt>
	                    <dt >
                        <a href="javaScript:void(0)"  onclick="queryCustomerRiskRegister();">查看风险保额</a>
					    <a id="queryCustomerRiskRegister" rel="queryCustomerRiskRegister" href="#" target="dialog" [mask=true ] style="display:none;"  width="1000" height="450" >查看风险保额</a>
					    </dt>
					    <dt>
							<a id="emrnew" href="javaScript:void(0)" onclick="queryEMR()">查看电子病历</a>
							<a id="emr" href="#" target="navTab" style="display: none;" rel="queryEMR" title="查看电子病历"></a>
						</dt>
						<dt>
							<a id="ciitcBack" href="javaScript:void(0)" onclick="queryCiitcBackResult()">行业共享信息提示</a>
							<a id="ciitcBackResult" href="#" target="navTab" style="display: none;" rel="queryCiitcBackResult" title="行业共享信息提示"></a>
						</dt>
	                    <dt ><a href="javascript:void(0)" onclick="comparisonToRegister();">签名比对</a></dt>
<%-- 						<dt ><a href="clm/common/checkIdentityInfo_CLM_commonQueryAction.action?caseId=${caseId}&checkType=1"   rel="page2" target="dialog" [mask=true ]    width="1000" height="450" >身份验真</a></dt>
 --%>			  		
						<dt>
							<a href="javaScript:void(0)" onclick="claimBizTurn()">发起转办</a><a
								id="claimBizTurnId" href="#" target="navTab"
								style="display: none;" rel="claimBizTurnId">发起转办</a>
						</dt>
						<dt>
							<a href="javaScript:void(0)" onclick="querySignatureTrace()" id="claimSignatureTrace">电子签名轨迹</a>
                            <a id="querySignatureTrace" rel="querySignatureTrace" href="#" target="dialog" [mask=true ] style="display:none;"  width="1000" height="450" >电子签名轨迹</a>					    						   
						</dt>
						<dt>
							<a id="initadvance" href="javaScript:void(0)" onclick="initAdvance()">垫付信息查看</a>
							<a id="init" href="#" target="navTab" style="display: none;" rel="queryEMR" title="垫付信息查看"></a>
						</dt>
 						</dl>
              </div>
			<div class="tabsHeader">
				<div class="tabsHeaderContent">
					<a class="button" style="float: right;" id="claimRegistChangeProp" href="#" ><span>!</span></a>
					<ul>
					    <li><a href="clm/register/dataCollectInit_CLM_dataCollectAction.action?caseId=${caseId}&tabRegister=1"  class="j-ajax" id="11" ><span>数据采集</span></a></li>
						<li><a href="clm/register/toRegisterCheckConfirmPageInit_CLM_toRegisterCheckConfirmPageAction.action?caseId=${caseId}" onclick=" this.href='clm/register/toRegisterCheckConfirmPageInit_CLM_toRegisterCheckConfirmPageAction.action?caseId=${caseId}&tabRegister=1'" class="j-ajax" id="12" style="display:none"><span>单证确认</span></a></li>  
						<li><a href="clm/register/toDataCollectInit_CLM_tClaimDutyRegisterAction.action?caseId=${caseId}" onclick=" this.href='clm/register/toDataCollectInit_CLM_tClaimDutyRegisterAction.action?caseId=${caseId}&tabRegister=1'" class="j-ajax" id="13" style="display:none"><span>责任明细</span></a></li>
						<li><a href="clm/report/contractHangUpInitRegister_CLM_contractHangUpAction.action?caseId=${caseId}" onclick=" this.href='clm/report/contractHangUpInitRegister_CLM_contractHangUpAction.action?caseId=${caseId}&tabRegister=1'" class="j-ajax" id="14" style="display: none"><span>保单挂起/解挂</span></a></li>
						<li><a href="clm/register/claimMatchCalcInit_CLM_claimMatchCalcAction.action?caseId=${caseId}" onclick=" this.href='clm/register/claimMatchCalcInit_CLM_claimMatchCalcAction.action?caseId=${caseId}&tabRegister=1'" class="j-ajax" id="15" style="display:none"><span>自动匹配理算</span></a></li> 
						<li><a href="clm/register/BenefitInputPageInit_CLM_toBenefitInputPageAction.action?caseId=${caseId}" onclick=" this.href='clm/register/BenefitInputPageInit_CLM_toBenefitInputPageAction.action?caseId=${caseId}&tabRegister=1'" class="j-ajax" id="16" style="display:none"><span>领款人与受益人录入</span></a></li>
					</ul>
				</div>
			</div>
			<div class="tabsContent main_tabsContent" style="padding:0">
				<div id="caseQueryTabs1">
					
				</div>
				
				<div id="caseQueryTabs2">
				
				</div>
				
		
				<div id="caseQueryTabs3">
				
				</div>
				<div id="caseQueryTabs4">
				
				</div>
				<div id="caseQueryTabs5">
				
				</div>
				<div id="caseQueryTabs6">
				
				</div>
			</div>
<!-- 			<div class="tabsFooter"> -->
<!-- 				<div class="tabsFooterContent"></div> -->
<!-- 			</div> -->
		</div>

	</div>
</div>


<script type="text/javascript" >

var  firstClick=0;
$('#claimRegistChangeProp', navTab.getCurrentPanel()).toggle(
		
		  function () {
			  if(firstClick==0){
				  onFirstClick();
				  firstClick=1;
				}  
			  queryMemo();
			  
			  if($('#claimRegisterCmdiv', navTab.getCurrentPanel()).css("display") == "none"){
				  MM_changeProp('claimRegisterCmdiv','','display','block','DIV');
				  } else {
					  MM_changeProp('claimRegisterCmdiv','','display','none','DIV');
				  }
			  
		  },
		  function () {
			  queryMemo();
			  if($('#claimRegisterCmdiv', navTab.getCurrentPanel()).css("display") == "none"){
				  MM_changeProp('claimRegisterCmdiv','','display','block','DIV');
				  } else {
					  MM_changeProp('claimRegisterCmdiv','','display','none','DIV');
				  }
		  }
);

function hideAlertInfo(obj) { //v9.0
	if($("div",navTab.getCurrentPanel()).attr("id")!=obj){
		$(obj).hide();
	}
}

function MM_changeProp(objId,x,theProp,theValue) { //v9.0
	$("#"+objId).css("position",'relative'); 
	$("#"+objId).css("z-index",'1'); 
	 
var obj = null; with (document){ if (getElementById)
obj = getElementById(objId); }
if (obj){
  if (theValue == true || theValue == false)
    eval("obj.style."+theProp+"="+theValue);
  else eval("obj.style."+theProp+"='"+theValue+"'");
}
}

//当第一次加载时 把没有内容的超链接变成灰色  ajax
function onFirstClick(){
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	  $.ajax({
		 'url' :'clm/common/queryLinkValue_CLM_commonQueryAction.action?caseID='+caseId,
		 'type' :'post',
		 'datatype' : 'json' ,
		 'success':function(data){
		    var data = eval("("+data+")");
		    if(data.statusCode!=null&&data.statusCode=="301"){
		    	firstClick=0;
		    }
		    if(data.channelCode=='12'||data.channelCode=='11'){//当案件申请渠道为：快赔时，【查看电子病历】突出显示为红色
		    	$("#emrnew",navTab.getCurrentPanel()).css("color","red");
		    }
		    if(data.ciitcBackResult=='0'){//当前赔案状态还不能判断案件类型（自核案件、单人审核案件、双人审核审批案件），则“行业共享信息提示”不做显示
		    	$("#ciitcBack",navTab.getCurrentPanel()).css('display','none');
		    }
		    if(data.alwaysSize=='0'){//无数据置灰   既往赔案查询
		    	$("#comAlwaysClaimFirst",navTab.getCurrentPanel()).attr("disabled",
		    			"disabled");
		    }
		    if(data.riSuggestSize=='0'){//  再保意见
		    	$("#claimRISFirst",navTab.getCurrentPanel()).attr("disabled",
		    			"disabled");
		    }
		    if(data.getSignatureTract == '0'){ //电子签名轨迹
		    	$("#claimSignatureTrace",navTab.getCurrentPanel()).attr("disabled",
    			"disabled");
		    }
		    
		  },
  		 'error':function(){
  				alert("出错了！");
  			} 
    	});	
	    
}

function  queryMemo(){
	
	var caseId=$("#caseId", navTab.getCurrentPanel()).val();
	  $.ajax({
		 'url' :'clm/common/queryMemoValue_CLM_commonQueryAction.action?caseID='+caseId,
		 'type' :'post',
		 'datatype' : 'json' ,
		 'success':function(data){
		    var data = eval("("+data+")"); 
		    if(data.memoSize=='1'){//   备注信息 
		    	$("#memoAuditFirst",navTab.getCurrentPanel()).css("color",
		    			"red");
		    }if(data.memoSize!='1'){//   备注信息 
		    	$("#memoAuditFirst",navTab.getCurrentPanel()).css("color",
    			"black");
          }
		    
		  },
		 'error':function(){
				alert("出错了！");
			} 
  	});	
}

</script>