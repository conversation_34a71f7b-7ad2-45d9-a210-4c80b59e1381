﻿<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript">
var approveReadOnly = '<%=request.getAttribute("approveReadOnly")%>' ;
if(approveReadOnly ==1){
	readOnly("registerShowId");
}
var qryQuitFlag = '<%=request.getAttribute("qryQuitFlag")%>' ;
if(qryQuitFlag ==1){
	readOnly("registerShowId");
}
function readOnly(obj){
	$("#"+obj, $.pdialog.getCurrent()).find("input").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("select").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("a").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("textarea").each(function(){
		$(this).attr("disabled",true);
	});
	$("#"+obj, $.pdialog.getCurrent()).find("button").each(function(){
		if(!($(this).html()=="退出")){
			$(this).attr("disabled",true);
		}
	});
	//单选按钮可以触发 用于显示详情
	$("#"+obj, $.pdialog.getCurrent()).find("input[type=radio]").each(function(){
		$(this).attr("disabled",false);
	});
}
function exitDialogOCR(){
	$.pdialog.closeCurrent();
}
	var insuredCertiStart = '<s:date name="claimInsuredVO.insuredCertiStarDate" format="yyyy-MM-dd"/>';
	var insuredCertiEnd = '<s:date name="claimInsuredVO.insuredCertiEndDate" format="yyyy-MM-dd"/>';
	var insuredCertiStartDate = new Date(insuredCertiStart.replace(/-/g, '/'));
	var insuredCertiEndDate = new Date(insuredCertiEnd.replace(/-/g, '/'));
	var insuredCertiStartYear = insuredCertiStartDate.getFullYear();
	var insuredCertiEndYear = insuredCertiEndDate.getFullYear();
	var years = insuredCertiEndYear-insuredCertiStartYear;
	if(years>50){
		$("#insuredCertiEndCheckBoxId",$.pdialog.getCurrent()).attr("checked","checked");
		$("#insuredCertiEndDate",$.pdialog.getCurrent()).val("");
		$("#insuredCertiEndDateId",$.pdialog.getCurrent()).val("9999-12-31");
		$("#insuredCertiEndDate",$.pdialog.getCurrent()).attr("disabled",true);
	}

//省市县初始化
var insuredProvince = '${claimInsuredVO.insuredState }';
var insuredCity = '${claimInsuredVO.insuredCity }';
var insuredDistrict = '${claimInsuredVO.insuredDistrict }';
insuredCityAssign(insuredProvince,insuredCity,insuredDistrict);



//根据省查询市
function insuredProvinceChangeReportData(k) {
	var cityReportId = $("#insuredCity", $.pdialog.getCurrent());
	var province = $(k).val();
	$("#insuredProvinceReserve", $.pdialog.getCurrent()).val($(k).val());
	$("#insuredCity", $.pdialog.getCurrent()).prev().val("");
	$("#insuredDistreact", $.pdialog.getCurrent()).prev().val("");
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ province,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#insuredCity", $.pdialog.getCurrent()).empty();
					$("#insuredDistrict", $.pdialog.getCurrent()).empty();
					$("<option value=''>市</option>").appendTo(cityReportId);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(cityReportId);
					}
				},
			});
	
}
//根据市查询县
function insuredCityChageReportData(k) {
	$("#insuredCityReserve",$.pdialog.getCurrent()).val($(k).val());
	$("#insuredDistreact",$.pdialog.getCurrent()).prev().val("");
	var city = $(k).val();
	var insuredDistrict = $("#insuredDistrict", $.pdialog.getCurrent());
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#insuredDistrict", $.pdialog.getCurrent()).empty();
					$("<option value=''>区/县</option>").appendTo(insuredDistrict);
					for (var i = 0; i < data.length; i++) {
						var option1 = "<option value='" + data[i].code
								+ "'   class='" + data[i].name + "' >"
								+ data[i].name + "</option>";
						$(option1).appendTo(insuredDistrict);
					}
				},
			});
}
//为后台备用传值字段赋值
function insuredDistreactChageReportData(k) {
	$("#insuredDistreactReserve", $.pdialog.getCurrent()).val($(k).val());
}

var insuredCityReserve = $("#insuredCityReserve", $.pdialog.getCurrent()).val();
var insuredDistreactReserve = $("#insuredDistreactReserve", $.pdialog.getCurrent()).val();


//初始化赋值市
var insuredCityReportId = $("#insuredCity", $.pdialog.getCurrent());
var insuredAreaReportId = $("#insuredDistrict", $.pdialog.getCurrent());
function insuredCityAssign(province,city,area){
	
	$.ajax({
		'type' : 'post',
		'url' : 'clm/report/province_CLM_insertReportInfoInputAction.action?districtVO.code='
				+ province,
		'datatype' : 'json',
		'async' : true,
		'success' : function(data) {
			var data = eval("(" + data + ")");
			$("#insuredCity", $.pdialog.getCurrent())
					.empty();
			$("#insuredCity", $.pdialog.getCurrent()).append(
					"<option value=''>市</option>");
			for (var i = 0; i < data.length; i++) {
				if (data[i].code == city) {
					$("#insuredCity", $.pdialog.getCurrent())
							.prev().val(
									data[i].code + "-"
											+ data[i].name);
					$("#insuredCity", $.pdialog.getCurrent()).append(
							"<option value='" + city
									+ "' class = '"
									+ data[i].name + "'>"
									+ data[i].name
									+ "</option>");
				}
			}
			for (var i = 0; i < data.length; i++) {
				if (data[i].code != city) {
					var option1 = "<option value='"
							+ data[i].code + "'   class='"
							+ data[i].name + "' >"
							+ data[i].name + "</option>";
					$("#insuredCity", $.pdialog.getCurrent()).append(option1);
				}
			}
			$("#insuredCity", $.pdialog.getCurrent()).val(
					city);
		},
	});
	setTimeout("insuredAreaAssign(" + city + " , "+ area +")", 0.0003);
	
}
//初始化赋值市
function insuredAreaAssign (city,area){
	// 获取市为县赋值
	$.ajax({
				'type' : 'post',
				'url' : 'clm/report/getCity_CLM_insertReportInfoInputAction.action?districtVO.code='
						+ city,
				'datatype' : 'json',
				'async' : true,
				'success' : function(data) {
					var data = eval("(" + data + ")");
					$("#insuredDistrict", $.pdialog.getCurrent()).empty();
					$("#insuredDistrict", $.pdialog.getCurrent()).append("<option value=''>区/县</option>");
					for (var i = 0; i < data.length; i++) {
						if (data[i].code == area) {
							$("#insuredDistrict", $.pdialog.getCurrent()).prev()
									.val(data[i].code + "-" + data[i].name);
							$("#insuredDistrict", $.pdialog.getCurrent()).append(
									"<option value='" + area + "' class = '"
											+ data[i].name + "'>"
											+ data[i].name + "</option>");
						}
					}
					for (var i = 0; i < data.length; i++) {
						if (data[i].code != area) {
							var option1 = "<option value='" + data[i].code
									+ "'   class='" + data[i].name + "' >"
									+ data[i].name + "</option>";
							$("#insuredDistrict", $.pdialog.getCurrent()).append(option1);
						}
					}
					$("#insuredDistrict", $.pdialog.getCurrent()).val(area);
				},
			});
}

function saveInsuredInfo(){
	
	var insuredCaseId = $("#insuredCaseId",$.pdialog.getCurrent()).val();
	if(insuredCaseId!=null&&insuredCaseId!=""){
		//必填校验
		var actualPay = $("#actualPay", $.pdialog.getCurrent()).val();
		var insuredName = $("#insuredName",$.pdialog.getCurrent()).val();
		var insuredSex = $("#insuredSex",$.pdialog.getCurrent()).val();
		var insuredPhone = $("#insuredPhone",$.pdialog.getCurrent()).val();
		var insuredNation = $("#insuredNation",$.pdialog.getCurrent()).val();
		var insuredJobCode = $("#insuredJobCode",$.pdialog.getCurrent()).val();
		var insuredCertiType = $("#insuredCertiType",$.pdialog.getCurrent()).val();
		var insuredCertiNo = $("#insuredCertiNo",$.pdialog.getCurrent()).val();
		var insuredCertiStarDate = $("#insuredCertiStarDate",$.pdialog.getCurrent()).val();
		var insuredCertiEndDate = $("#insuredCertiEndDate",$.pdialog.getCurrent()).val();
		var insuredState = $("#insuredState",$.pdialog.getCurrent()).val();
		var insuredCity = $("#insuredCity",$.pdialog.getCurrent()).val();
		var insuredDistrict = $("#insuredDistrict",$.pdialog.getCurrent()).val();
		var insuredAddress = $("#insuredAddress",$.pdialog.getCurrent()).val();
		//证件类型为外国人永久居住身份证，校验证件号码
		if (insuredCertiType == "e"){
			if(!(insuredCertiNo.length == 15 || insuredCertiNo.length == 18)){
				alertMsg.error("外国人永久居留身份证位数应为15位或18位。");
				return false;
			}
			if(insuredCertiNo.length == 15){
				var check = /^[A-Z]{3}(\d)(?!\1{11})[\d]{11}$/.test(insuredCertiNo);
				if (!check){
					alertMsg.error("外国人永久居留身份证证件号码为15位的，前三位必须为大写字母，后12位数字不能全相同。");
					return false;
				}
			}
			if(insuredCertiNo.length == 18){
				var birthdayNew = insuredCertiNo.substring(6,14);
				var reg = new RegExp("^9.{17}$");
				var check = reg.test(insuredCertiNo);
				if (!check){
					alertMsg.error("外国人永久居留身份证证件号码为18位的，首位必须是9。");
					return false;
				}
			}
		}
		//actualPay小于1000国籍职业代码必录
		if(actualPay>=10000 && (insuredNation=="" || insuredNation== null)){
			alertMsg.error("被保人国籍必填！");
		}else if(actualPay>=10000 && (insuredJobCode=="" || insuredJobCode== null)){
			alertMsg.error("被保人职业代码必填！");
		}else if(insuredName=="" || insuredName== null){
			alertMsg.error("被保人姓名必填！");
		}else if(insuredSex=="" || insuredSex== null){
			alertMsg.error("被保人性别必填！");
		}else if(insuredPhone=="" || insuredPhone== null){
			alertMsg.error("被保人电话必填！");
		}else if(insuredCertiType=="" || insuredCertiType== null){
			alertMsg.error("被保人证件类型必填！");
		}else if(insuredCertiNo=="" || insuredCertiNo== null){
			alertMsg.error("被保人证件号码必填！");
		}else if(insuredCertiStarDate=="" || insuredCertiStarDate== null){
			alertMsg.error("被保人证件起期必填！");
		}else if((insuredCertiEndDate=="" || insuredCertiEndDate== null) && !$("#insuredCertiEndCheckBoxId", $.pdialog.getCurrent()).is(':checked')){
			alertMsg.error("被保人证件止期必填！");
		}else if (insuredCertiType == "e"&&$.trim(insuredCertiStarDate) != ""&&$.trim(insuredCertiEndDate) != ""&&!checkCertiDate(insuredCertiStarDate,insuredCertiEndDate)){
			return false;
		}else if(insuredState=="" || insuredState== null){
			alertMsg.error("被保人省必填！");
		}else if(insuredCity=="" || insuredCity== null){
			alertMsg.error("被保人市必填！");
		}else if(insuredDistrict=="" || insuredDistrict== null){
			alertMsg.error("被保人区/县必填！");
		}else if(insuredAddress=="" || insuredAddress== null){
			alertMsg.error("被保人乡镇/街道必填！");
		}else{
			//港澳台校验国籍（香港 澳门 台湾  港澳台 校验规则不一样）
			if(insuredCertiType=="h" || insuredCertiType == "b" || insuredCertiType == "i" || insuredCertiType == "d"){
				if(!checkCertiNoAndNa(insuredCertiType,insuredCertiNo,insuredNation)){
					return;
				}
			}else{
				if(!checkCertiNo(insuredCertiType,insuredCertiNo)){
					return;
				}
			}

			// 针对被保人，当证件类型选择的是出生证明时，校验其年龄
			if (insuredCertiType == '4') {
				var claimInsuredId = $("#claimInsuredId ",$.pdialog.getCurrent()).val();
				var checkAmlAgeLimitFlag = false;
				var checkAmlAgeLimitMessag = '';
				$.ajax({
					'type': 'post',
					'url': 'clm/register/checkAmlAgeLimit_CLM_claimAntiMoneyLaunderingAction.action?claimInsuredVO.caseId=' + insuredCaseId + "&claimInsuredVO.claimInsuredId=" + claimInsuredId,
					'datatype': 'json',
					'async': false,
					'success': function (data) {
						var data = eval("(" + data + ")");
						if (data.statusCode == 0) {
							checkAmlAgeLimitFlag = true;
							checkAmlAgeLimitMessag = data.message;
						}
					}
				});
				if (checkAmlAgeLimitFlag) {
					alertMsg.error(checkAmlAgeLimitMessag);
					return;
				}
			}
			
			
			$.ajax({
				'url':"clm/register/saveInsuredInfo_CLM_claimAntiMoneyLaunderingAction.action",
				'type':'post',
				'data':$("#insuredForm", $.pdialog.getCurrent()).serialize(),
				'datatype':'json',
				'success':function(json){
					var data = eval("("+json+")");
					if(data.statusCode == '200'){
						alertMsg.correct("保存成功！");
					}else{
						alertMsg.error(data.message);
					}
				}
			}); 
		}
		
	}else{
		alertMsg.error("页面异常请重新初始化");
	}
}

function customerCertiEndCheckBoxAudit(obj){
	if($(obj).attr("checked") == "checked"){
		$("#insuredCertiEndDate",$.pdialog.getCurrent()).val("");
		$("#insuredCertiEndDateId",$.pdialog.getCurrent()).val("9999-12-31");
		$("#insuredCertiEndDate",$.pdialog.getCurrent()).attr("disabled",true);
	}else{
		$("#insuredCertiEndDate",$.pdialog.getCurrent()).val("");
		$("#insuredCertiEndDateId",$.pdialog.getCurrent()).val("");
		$("#insuredCertiEndDate",$.pdialog.getCurrent()).removeAttr("disabled");
	}
}

//记录被保人证件有效起期是否有改动
var insuredCertiStarDatey = $("#insuredCertiStarDate", $.pdialog.getCurrent()).val();
//记录被保人证件证件类型是否有改动
var insuredCertiTypey = $("#insuredCertiType", $.pdialog.getCurrent()).val();

function getFindInsuredInfo() {
	//判断证件有效起期和出生日期和证件类型都不为空的时候计算证件有效止期
	var insuredBirthStr = $("#insuredBirthStr", $.pdialog.getCurrent()).val(); // 出生日期
	var insuredCertiType = $("#insuredCertiType", $.pdialog.getCurrent()).val(); // 证件类型
	var insuredCertiStarDate = $("#insuredCertiStarDate", $.pdialog.getCurrent()).val(); // 证件有效起期
	if(insuredBirthStr != "" && insuredCertiType != "" && insuredCertiStarDate != ""){
		if(insuredCertiStarDatey != insuredCertiStarDate || insuredCertiTypey != insuredCertiType || insuredCertiStarDatey == "" || insuredCertiTypey == ""){
			insuredCertiStarDatey = insuredCertiStarDate;
			insuredCertiTypey = insuredCertiType;
			$("#insuredCertiEndCheckBoxId",$.pdialog.getCurrent()).removeAttr("checked");
			$("#insuredCertiEndDate",$.pdialog.getCurrent()).removeAttr("disabled");
			//判断证件类型
			//出生证明
			if(insuredCertiType == "4"){
				$.ajax({
					'type': 'post',
					'url': 'clm/paymentplan/checkAmlAgeLimit_CLM_claimDirectConnAction.action?caseId=' + $("#insuredCaseId",$.pdialog.getCurrent()).val() + "&customerBirthdayStr=" + insuredBirthStr,
					'datatype': 'json',
					'async': true,
					'success': function (data) {
						var data = eval("(" + data + ")");
						// alert(data.endDate)
						if (data.statusCode == 1 || data.statusCode == 2) {
							$("#insuredCertiEndDate", $.pdialog.getCurrent()).val(data.endDate);
							$("#insuredCertiEndDateId", $.pdialog.getCurrent()).val(data.endDate);
							$("#insuredCertiEndDate", $.pdialog.getCurrent()).attr("disabled", true);
						} else {
							$("#insuredCertiEndDate", $.pdialog.getCurrent()).val("");
							$("#insuredCertiEndDateId", $.pdialog.getCurrent()).val("");
							$("#insuredCertiEndDate", $.pdialog.getCurrent()).attr("disabled", false);
						}
					}
				});
			}
		}
	}
}

</script>
<div layoutH="0">
<div class="main_tabdiv">
	 <div  id="registerShowId" layoutH="0">
		<form method="post" id="insuredForm" action="clm/register/saveInsuredInfo_CLM_claimAntiMoneyLaunderingAction.action">
         <div class="panelPageFormContent">
             <div class="divfclass">
	               <h1> <img src="clm/images/tubiao.png">被保险人信息</h1>
		     </div>
				<dl>
					<dt>姓名</dt>
					<dd>
					   <input type="hidden" name="claimCaseVO.actualPay" id="actualPay" value="${claimCaseVO.actualPay }"/>
					   <input type="hidden" name="claimInsuredVO.caseId" id="insuredCaseId" value="${claimInsuredVO.caseId }"/>
					   <input type="hidden" name="claimInsuredVO.claimInsuredId" id="claimInsuredId" value="${claimInsuredVO.claimInsuredId }"/>
					   <input type="hidden" name="" id="insuredBirthStr" value="${claimInsuredVO.insuredBirthStr }"/>
					   <input type="text" name="claimInsuredVO.insuredName" id="insuredName" value="${claimInsuredVO.insuredName }"/>
					</dd>
				</dl>
				<dl>
					<dt>性别</dt>
					<dd>
					   <Field:codeTable  cssClass="combox title"  name="claimInsuredVO.insuredSex" id="insuredSex" value="${claimInsuredVO.insuredSex }" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2)" nullOption="true"/>
					</dd>
				</dl>
				<dl>
					<dt>联系电话</dt>
					<dd>
						<input type="text" name="claimInsuredVO.insuredPhone" id="insuredPhone" value="${claimInsuredVO.insuredPhone }" onblur="checkPhoneAndMobileNew(this,'被保人')"/>   
					</dd>
				</dl>
				<dl>
					<dt>国籍</dt>
					<dd>
						<Field:codeTable cssClass="selectToInput"  name="claimInsuredVO.insuredNation" id="insuredNation"
					value="${claimInsuredVO.insuredNation }" tableName="APP___CLM__DBUSER.T_COUNTRY" whereClause="country_code not in ('GAT')" defaultValue="CHN"  orderBy="decode(country_code,'CHN','A',country_code)"/>
					</dd>
				</dl>
				<dl>
					<dt>职业代码</dt>
					<dd>
					   <s:if test="#request.qryQuitFlag == null || #request.qryQuitFlag== ''">
						   	<Field:codeTable  id="insuredJobCode" name="claimInsuredVO.insuredJobCode" tableName="APP___CLM__DBUSER.T_JOB_CODE"
													whereClause="job_code not in ('Y001023') and display_order not in ('0')"  orderBy="DISPLAY_ORDER"
													nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" "
													value="${claimInsuredVO.insuredJobCode }"/>
					   </s:if>
					   <s:else>
					   		<input type="text" name="claimInsuredVO.insuredJobCode" id="insuredJobCode" value="${claimInsuredVO.insuredJobCode }"/>
					   </s:else>
					</dd>
				</dl>
				<dl>
					<dt>证件类型</dt>
					<dd>
					   <Field:codeTable cssClass="combox title"  name="claimInsuredVO.insuredCertiType" id="insuredCertiType" onChange="getFindInsuredInfo();" value="${claimInsuredVO.insuredCertiType }" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"  nullOption="true"
					    	whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)" />
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
					   <input type="text" name="claimInsuredVO.insuredCertiNo" id="insuredCertiNo" value="${claimInsuredVO.insuredCertiNo }"/>
					</dd>
				</dl>
				<dl>
					<dt>证件有效起期</dt>
					<dd>
					   <input type="expandDateYMD"  name="claimInsuredVO.insuredCertiStarDate" onPropertyChange="getFindInsuredInfo();" id="insuredCertiStarDate" value="<s:date name="claimInsuredVO.insuredCertiStarDate" format="yyyy-MM-dd"/>" />			   
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
						<input type="expandDateYMD"  name="claimInsuredVO.insuredCertiEndDate" id="insuredCertiEndDate"  value="<s:date name="claimInsuredVO.insuredCertiEndDate" format="yyyy-MM-dd"/>"/>
						<input type="hidden"  name="claimInsuredVO.insuredCertiEndDate" id="insuredCertiEndDateId"  value=""/>
						<input type="checkbox" id="insuredCertiEndCheckBoxId" onclick="customerCertiEndCheckBoxAudit(this)"/>长期
					</dd>
				</dl>
				<div class="mian_site">
					<dl>
						<dt><font>* </font>地址</dt>
					</dl>
					<div class="main_detail">
						<dl>
							<dd>
							   	<input type="hidden"  value="${claimInsuredVO.insuredState }" id="insuredStateReserve">
								<input type="hidden"  value="${claimInsuredVO.insuredCity }" id="insuredCityReserve">
								<input type="hidden"  value="${claimInsuredVO.insuredDistrict }" id="insuredDistrictReserve">
		                           	<Field:codeTable cssClass="selectToInput"  name="claimInsuredVO.insuredState" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="insuredState" 
		                           	value="${claimInsuredVO.insuredState }" onChange = "insuredProvinceChangeReportData(this)"
		                           	whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
								<span>省/直辖市<font style="color:#FF0000">* </font></span>
							</dd>
						</dl>
						<dl>
							<dd>
								<select class="selectToInput" name="claimInsuredVO.insuredCity" onchange="insuredCityChageReportData(this)" id="insuredCity" >
									</select>
								<span>市<font style="color:#FF0000">* </font></span>
							</dd>
						</dl>
						<dl>
							<dd>
								<select class="selectToInput"  name="claimInsuredVO.insuredDistrict" onchange="" id="insuredDistrict" >
									</select>
								<span>区/县<font style="color:#FF0000">* </font></span>
							</dd>
						</dl>
						<dl>
							<dd>	
		                           	<input name="claimInsuredVO.insuredAddress" id="insuredAddress"size="24" value="${claimInsuredVO.insuredAddress }" style="width: 150px;">
								<span>乡镇/街道<font style="color:#FF0000">* </font></span>
							</dd>
						</dl>
					</div>
				</div>	
		</div>
				
		<div width="100%" >
             <div class="divfclass">
	               <h1> <img src="clm/images/tubiao.png">投保人与被保险人关系</h1>
		     </div>
		<dl>
			<dd>	
				<table class="list" id="surveyApplyOperTable" width="45%">
					<thead>
						<tr align="center">
							<th nowrap>序号</th>
							<th nowrap>保单号</th>
							<th nowrap>投保人与被保险人关系</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="claimInsuredRelationVOs" var="status" status="st">
							<tr align="center">
								<td><s:property value="#st.index + 1" /></td>
								<td>${policyCode }
									<input type="hidden" name="claimInsuredRelationVOs[${st.index }].caseId" value="${caseId }">
									<input type="hidden" name="claimInsuredRelationVOs[${st.index }].policyCode" value="${policyCode }">
									<input type="hidden" name="claimInsuredRelationVOs[${st.index }].policyId" value="${policyId }">
								</td>
								<td>
									<Field:codeTable  name="claimInsuredRelationVOs[${st.index }].insuredHoliderRe" id="insuredHoliderRe" value="${insuredHoliderRe}" tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true" cssClass="notuseflagDelete combox title selectChange" 
									whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
								</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</dd>
		</dl>
		</div>
        <div class="formBarButton main_bottom">
        	   <button class="but_blue" type="button"  class="submit" onclick="saveInsuredInfo()">保存</button> 
               <s:if test="#request.qryQuitFlag == null || #request.qryQuitFlag== ''">
					<button class="but_gray" type="button" onclick="exitDialogOCR()">退出</button> 
			   </s:if>	
               
        </div>
	</form>
</div>
</div>
</div>