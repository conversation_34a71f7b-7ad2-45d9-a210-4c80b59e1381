<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%>
 	<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript">
var currentUserOrganCode = "<%=((com.nci.udmp.framework.presentation.vo.UserVO)session.getAttribute("sessUser")).getOrganCode()%>";
//分配任务
function distributeTaskOutsource(){
	var checked = $("#taskListBodyOutsource", navTab.getCurrentPanel()).find("input:checked");
	var userName = $("#userNameOutsource", navTab.getCurrentPanel()).val();
	var userId = $("#userIdhiddenOutsource", navTab.getCurrentPanel()).val();
	if(userName == "" || userName == null){
		alertMsg.error("请输入操作人员！");
		return;
	}
	if(userName!="" && userId == "" ){
		alertMsg.error("输入的用户名有误，请重新输入！");
		return;
	}
	
	if(checked==null || checked.size()==0){
		alertMsg.error("请勾选需要分配的赔案！");
		return;
	}
	var caseNo = "";
	for(var i=0;i<checked.size();i++){
		caseNo += $(checked[i]).parent().parent().children("td:eq(1)").text()+",";
	}
	$.ajax({
		url : "clm/register/allotTaskOutsource_CLM_claimOutSourceManageAction.action",
		type : "POST",
		dataType : "json",
		data:{'taskListVO.userName':userName,'taskListVO.caseNo':caseNo,'taskListVO.userId':userId},
		success : function (json){
			if(json.statusCode == '300') {
				alertMsg.error(json.message);
				return false;
			}
			if(json.statusCode == '200'){
				$("#queryOutsource",navTab.getCurrentPanel()).click();
				alertMsg.correct("分配成功");
			}
		}
	});
}

function onclickUserOutsource(){
	$("#lookUserOutsource", navTab.getCurrentPanel()).attr("href","clm/register/queryUserInitOutSource_CLM_claimOutSourceManageAction.action");
}
//根据用户名查询用户姓名
function queryMultiOperatorByName(obj){
	var operator = $(obj).val();
	$.ajax({
		url : "clm/handworkAssignTask/AjaxQueryMultiOperator_CLM_taskManageAction.action?userVO.userName="+operator,
		type : "POST",
		dataType : "json",
		gloal: false,
		async: false,
		success : function(json) { 
			var userId = json.userId;
			var realName = json.realName;
			if(realName==""){
				$("#userIdhiddenOutsource").val("");
				$("#realNameOutsource").val("");
			}else{
				$("#userIdhiddenOutsource").val(userId);
				$("#realNameOutsource").val(realName);
			}
		}
	});
}

function checktimeOutsource(obj) {
	var time = /^(0\d{1}|1\d{1}|2[0-3]):([0-5]\d{1})$/;
	if(trim($(obj).val())!=""){
		if (!time.test($(obj).val())) {
			alertMsg.error("时间格式不正确，请重新输入");
			$(obj).val("");
		}
	}
}
function query() {
// 	var branchname = $("#operatorOrgan", navTab.getCurrentPanel()).val(); //机构outsourceCaseVO.organCode
// 	var outsourceTypeCode = $("#outsourceTypeCode", navTab.getCurrentPanel()).prev().val(); //外包类型outsourceCaseVO.outsourceTypeCode
// 	var outsourceStatusCode = $("#outsourceStatusCode", navTab.getCurrentPanel()).val(); //外包状态outsourceCaseVO.outsourceStatusCode
// 	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val(); //赔案号outsourceCaseVO.caseNo
// 	var sendPackageStartTime = $("#sendPackageStartTime", navTab.getCurrentPanel()).val(); //发包起期outsourceCaseVO.sendPackageStartTime
// 	var sendPackageEndTime = $("#sendPackageEndTime", navTab.getCurrentPanel()).val(); //发包止期 outsourceCaseVO.sendPackageEndTime
// 	if(outsourceTypeCode){
// 		outsourceTypeCode=outsourceTypeCode.substring(0,1);
// 	}
// 	if(outsourceStatusCode){
// 		outsourceStatusCode=outsourceStatusCode.substring(0,1);
// 	}
// 	if(isNorE(caseNo)&&isNorE(sendPackageStartTime)&&isNorE(sendPackageEndTime)){
// 		alertMsg.error("请至少录入赔案号、发包起止日期中的一个查询条件!");
// 		return;
// 	}
// 	if(!isNorE(sendPackageStartTime)){
// 		if(isNorE(sendPackageEndTime)){
// 			alertMsg.error("发包起期不为空，必录入发包止期!");
// 			return;
// 		}
// 	}
// 	if(!isNorE(sendPackageEndTime)){
// 		if(isNorE(sendPackageStartTime)){
// 			alertMsg.error("发包止期不为空，必录入发包起期!");
// 			return;
// 		}
// 	}
	//获取机构信息是否为用户机构下属机构
	var operatorOrgan = $("#operatorOrgan", navTab.getCurrentPanel()).val();
	if(operatorOrgan.match(currentUserOrganCode)){
		$("#outSourceConfirmForm", navTab.getCurrentPanel()).submit();
	}else{
		alertMsg.error("只能查询本级及下级机构数据");
	}
}
function isNorE(s){
	if(s==null||s==""){
		return true;
	}else{
		return false;
	}
}

/**
 * 跳转到立案页面
 */
 function queryRegister(caseId,obj) {
		navTab.openTab("20283", "clm/sign/outSourceInfo_CLM_applyPersonalTaskAction.action?caseId="+caseId, {title:'立案登记'});
}
//直接调用udmp提供的机构树方法
function mySelectOrgan(){
	$("#operatorOrgan", $.pdialog.getCurrent()).orgtreegrn($.pdialog.getCurrent());
}

</script>
<div layoutH="0" id="outSourceCasePoolIni">
	<form id="pagerForm" method="post"
		action="clm/register/outSourceCasePoolQuery_CLM_claimOutSourceManageAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<form id="outSourceConfirmForm" class="pagerForm required-validate"
		novalidate="novalidate" method="post"
		onsubmit="return divSearch(this,'outSourceCasePoolIni')"
		action="clm/register/outSourceCasePoolQuery_CLM_claimOutSourceManageAction.action"
		rel="pagerForm">
		<%-- 					<input name="outsourceCaseVO.outsourceStatusCode111" type="text" value="${outsourceCaseVO.outsourceStatusCode}"/> --%>
		<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询条件
					</h1>
				</div>
			<div>
				<div class="pageFormInfoContent">
					
					<dl>
						<dt>机构</dt>
						<dd id="organFlag">
							<div id="1">
								<s:if test="outsourceCaseVO.organCode != null">
									<input style="width: 30px;border-right:0px" type="text" size="2" name="outsourceCaseVO.organCode"
									id="operatorOrgan" value="<s:property value='outsourceCaseVO.organCode'/>"  onclick='mySelectOrgan(this)' clickId="casePoolBtn"
									showOrgName="branchname" needAll="true" /> <input
									style="width:110px;" type="text" size="11" readOnly name="claimBfSurveyPlanVO.branchname"
									id="branchname" value="<Field:codeValue value="${outsourceCaseVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
								</s:if>
								<s:else>
									<input style="width: 30px;border-right:0px" type="text" size="2" name="outsourceCaseVO.organCode"
									id="operatorOrgan" value="<s:property value='outsourceCaseVO.organCode'/>" class="organ" clickId="casePoolBtn"
									showOrgName="branchname" needAll="true" /> <input
									style="width:110px;" type="text" size="11" readOnly name="claimBfSurveyPlanVO.branchname"
									id="branchname" value="<Field:codeValue value="${outsourceCaseVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" />
								</s:else>
							</div>
							<div id="2">
								<a id="casePoolBtn" onclick="mySelectOrgan1()" class="btnLook" href="#" style="position: relative;"></a>
							</div>
						</dd>
					</dl>
					<dl>
						<dt>外包商</dt>
						<dd>
							<select	class="notuseflagDelete combox title comboxDD" id="outsourceName" value="${outsourceCaseVO.outsourceId }" name="outsourceCaseVO.outsourceId" >
						   		<option value="">全部</option>
								<s:iterator value="outsourceIfnoVOList" status="st" id="var">
									<option value="${outsourceId}" <s:if test="outsourceCaseVO.outsourceId==#var.outsourceId">selected</s:if>>${outsourceName}</option>
								</s:iterator>
							</select>
						</dd>
					</dl>
					<dl id="outCaseType" style="display:;">
						<dt>外包类型</dt>
						<dd>
							<s:select cssClass="notuseflagDelete combox title comboxDD" id="outsourceTypeCode"
								name="outsourceCaseVO.outsourceTypeCode"
								list="#{'':'全部',1:'医疗案件',2:'非医疗案件'}" value="outsourceCaseVO.outsourceTypeCode"></s:select>
						</dd>
					</dl>
					<dl >
						<dt>外包方式</dt>
						<dd>
							<s:select list="#{'':'显示全部',1:'驻场外包',2:'离场外包',3:'数采自采'}" id="outsourceWay"
								cssClass="notuseflagDelete combox title comboxDD" name="outsourceCaseVO.outsourceWay">
							</s:select>
						</dd>
					</dl>
					<dl>
						<dt>外包状态</dt>
						<dd>
							<s:select cssClass="notuseflagDelete combox title comboxDD" id="outsourceStatusCode"
								list="#{'':'全部',1:'未回复',2:'非问题件',3:'问题件'}"
								value="outsourceCaseVO.outsourceStatusCode"
								name="outsourceCaseVO.outsourceStatusCode">
							</s:select>
						</dd>
					</dl>
					<dl >
						<dt >赔案号</dt>
						<dd >
							<input name="outsourceCaseVO.caseNo" type="text" id="caseNo"
								value="${outsourceCaseVO.caseNo}" onkeyup="this.value=this.value.replace(/\D/g,'')"/>
						</dd>
					</dl>
					<dl>
						<dt>发包起期</dt>
						<dd >
							<input type="expandDateYMD"
								name="outsourceCaseVO.sendPackageStartTime" size="22" id="sendPackageStartTime"
								class="date" style="border-right:0px;"
								value="<s:date name='outsourceCaseVO.sendPackageStartTime' format='yyyy-MM-dd' />" />
							<a class="inputDateButton" href="javascript:;">选择</a>
							<input type="text" id="sendPackageStartTimeH" name="outsourceCaseVO.sendPackageStartTimeH" style="width: 40px;top: -16px" size="2" onblur="checktimeOutsource(this)" maxlength="5" value="00:00"/>
						</dd>
					</dl>
					<dl>
						<dt>发包止期</dt>
						<dd >
							<input type="expandDateYMD" id="sendPackageEndTime"
								name="outsourceCaseVO.sendPackageEndTime" size="22" class="date" style="border-right:0px;"
								value="<s:date name='outsourceCaseVO.sendPackageEndTime' format='yyyy-MM-dd' />" />
							<a class="inputDateButton" href="javascript:;">选择</a>
							<input type="text" id="receivedPackageTimeH" name="outsourceCaseVO.receivedPackageTimeH" style="width: 40px;top: -16px" size="2" onblur="checktimeOutsource(this)" maxlength="5" value="${outsourceCaseVO.receivedPackageTimeH}"/>
						</dd>
					</dl>
					<div class="pageFormdiv">
						<button id="queryOutsource" class="but_blue" type="button" onclick="query()">查询</button>
					</div>
				</div>
			</div>
		</div>
	</form>
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">查询结果
					</h1>
				</div>
		<div class="tabdivclassbr">
			<div style="overflow: auto;">
			<table class="list main_dbottom" width="200%" align="center"  id="tablecheck" >
				<thead>
					<tr >
						<th nowrap>序号</th>
						<th nowrap>赔案号</th>
<!-- 						<th nowrap>片区</th> -->
						<th nowrap>机构</th>
						<th nowrap>外包类型</th>
						<th nowrap>外包方式</th>
						<th nowrap>外包商</th>
						<th nowrap>外包状态</th>
						<th nowrap>影像问题件状态
						</th>
						<th nowrap>案件状态</th>
						<th nowrap>发包时间</th>
						<th nowrap>收包时间</th>
						<th nowrap>回复时效(H)
						</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>绩优等级
						</th>
						<th nowrap>签收人</th>
						<th nowrap>签收时间</th>
						<th nowrap>挂起标识</th>
					</tr>
				</thead>
				<tbody id="taskListBodyOutsource">
					<s:if test="imageFlag != null">
							<tr>
								<td colspan="100">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
						</s:if>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="100">
									<div class="noRueryResult">没有符合条件的查询结果！</div>
								</td>
							</tr>
						</s:elseif>
				    <s:if test="currentPage.PageItems.size()!=0">
					<s:iterator value="currentPage.PageItems" status="st">
						<tr align="center">
							<td>
							<s:if test="caseStatus == 31  && isBpo == 1 && counterEmployeeFlag != 1">
							      <input type="checkbox" class="radioIndex" name="taskmanage" value="" />
							  </s:if>
								${st.index+1}</td>
							<td>
							   <s:if test="caseStatus == 31  && isBpo == 1 && counterEmployeeFlag != 1">
							      <a class="icon" href="#" onclick="queryRegister('${caseId}',this);" <s:if test ='isRedFlag=="1"'>style="color:red"</s:if>>${caseNo}</a>
							   </s:if>
							   <s:else>
								  <span <s:if test ='isRedFlag=="1"'>style="color:red"</s:if>>${caseNo}</span>
							   </s:else>
							</td>
<%-- 							<td><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_AREA" value="${areaCode }"/></td> --%>
							<td><Field:codeValue value="${organCode}"
									tableName="APP___CLM__DBUSER.T_UDMP_ORG" /></td>
							<td><Field:codeValue value="${outsourceTypeCode}"
									tableName="APP___CLM__DBUSER.T_OUTSOURCE_TYPE" /></td>
							<%-- <td><Field:codeValue value="${outsourceName}"
									tableName="APP___CLM__DBUSER.T_OUTSOURCE_IFNO" /></td> --%>
							<td><Field:codeValue value="${outsourceWay}"
									tableName="APP___CLM__DBUSER.T_OUTSOURCE_WAY" /></td>		
							<td>${outsourceName}</td>
							<td><Field:codeValue value="${outsourceStatusCode}"
									tableName="APP___CLM__DBUSER.T_OUTSOURCE_STATE" /></td>
							<td>
								<s:if test="imageIssueStatusCode == 0">
									<span>无</span>
								</s:if>
								 <s:elseif test="imageIssueStatusCode == 1">
								 	<span>待处理</span>
								 </s:elseif>
								 <s:elseif test="imageIssueStatusCode == 2">
								 	<span>已完成</span>
								 </s:elseif>
							<td><Field:codeValue value="${caseStatus}"
									tableName="APP___CLM__DBUSER.T_CASE_STATUS" /></td>
							<td><s:date name="sendPackageTime"
									format="yyyy-MM-dd HH:mm:SS" /></td>
							<td><s:date name="receivedPackageTime"
									format="yyyy-MM-dd HH:mm:SS" /></td>
							<td>${backTime}</td>
							<td>${customerName}</td>
							<td><Field:codeValue value="${greenFlag}" tableName="APP___CLM__DBUSER.T_YES_NO" /></td>
							<td>${realName}</td>
							<td><s:date name="signTime" format="yyyy-MM-dd HH:mm:SS" /></td>
							<td>
								<s:if test="policyFlag eq 1">
									是
								</s:if>
							</td>
						</tr>
					</s:iterator>
					</s:if>
				</tbody>
			</table>
			</div>
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
	</div>
	<div class="panelPageFormContent">
						<div class="divfclass">
							<h1><img src="clm/images/tubiao.png">任务分配</h1>
					   </div>
						<div>
							<dl>
								<dt><font >* </font>操作人员用户名</dt>
								<dd>
									<input id="userNameOutsource" postField="keyword"
										name="userVO.userName" type="text" onblur="queryMultiOperatorByName(this)"
										lookupGroup="userVO" suggestField="userName"
										size="20" /> 
									<a id="lookUserOutsource" class="btnLook" onclick="onclickUserOutsource();" lookupGroup="userVO">查找作业人员</a>
								</dd>
							</dl>
							<dl>
								<dt>操作人员姓名</dt>
								<dd>
									<input name="userVO.userId" type="hidden" id="userIdhiddenOutsource" size="20" readOnly />
									<input name="userVO.realName" type="text" id="realNameOutsource" size="20" readOnly />
								</dd>
							</dl>
						</div>
				</div>
	<div class="formBarButton">
		<ul>
			<li>
				<button class="but_blue" type="button" id="assignTaskOutsource" onclick="distributeTaskOutsource();">分配任务</button>
			</li>
			<li><button class="but_gray" type="button" onclick="exit();">退出</button></li>
		</ul>
	</div>
</div>


