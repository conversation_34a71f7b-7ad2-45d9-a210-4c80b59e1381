<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0"> 

<style type="text/css">       
  	.short{width:70px!important} 
  	.lang{width:280px!important} 
  	.short2{width:100px!important} 
</style>
<script type="text/javascript" language="javascript" src="clm/pages/register/datacollect.js"></script> 
<script type="text/javascript" >
/* //用来解决tab页签跳转存在的问题
function myNavTabAjaxDone(json){
	DWZ.ajaxDone(json);
    $("#caseNoId", navTab.getCurrentPanel()).val(json.data.CASE_NO);
    $("#accidentNoId", navTab.getCurrentPanel()).val(json.data.ACCIDENT_NO);
    $("#caseIdId", navTab.getCurrentPanel()).val(json.data.CASE_ID);
} */
dwz_comboxDD_myarray = new Array('clmtProvince','clmtCity','clmtDistreact');
dwz_comboxDD_mybox = navTab.getCurrentPanel();

function queryPolicyMsg(customerId){
	var url="clm/common/initPolicQueryPage_CLM_commonQueryAction.action?customerId="+customerId;
	$("#queryPolicyMsg", navTab.getCurrentPanel()).attr("href",url).click();
}

//隐藏dwz封装的添加中的input
function hidentRowNum(){
	$("[name='dwz_rowNum']").css("display","none");
}
setTimeout('hidentRowNum()',100);

function nextToDuty(caseId) {
	
	//用于校验事故日期和出险日期
	var accTimeAndaccidentTime = "NO";
	//用于校验出险日期和系统日期
	var accTimeAndsystemDate = "NO";
	// 获取报案日期
	var rptrTime = $("#acceptTimeId",navTab.getCurrentPanel()).val().replace(/-/g, '/');
	var rptrTimes = rptrTime.split("/");
	var dt1 = new Date();
	dt1.setFullYear(rptrTimes[0]);
	dt1.setMonth(rptrTimes[1] - 1);
	dt1.setDate(rptrTimes[2]);
	//获取事故日期
    var accidentTime = $("#accidentTimeIds",navTab.getCurrentPanel()).val().replace(/-/g, '/');
	var accidentTimes = accidentTime.split("/");
	var dt2 = new Date();
	dt2.setFullYear(accidentTimes[0]);
	dt2.setMonth(accidentTimes[1] - 1);
	dt2.setDate(accidentTimes[2]);

	var dif = dt1.getTime() - dt2.getTime();
	var days = dif / (24 * 60 * 60 * 1000);
    //获取系统日期
    var myDate =new Date();
    var subStrMyDate=myDate.getYear()+"-"+addZero(parseInt(myDate.getMonth())+1)+"-"+addZero(parseInt(myDate.getDate()));
    var systemDate=subStrMyDate.replaceAll("-","/");
    var sysTime=$("#sysTimeId",navTab.getCurrentPanel()).val().replace(/-/g, '/');
    //获取出险日期
	 var inputs = $("input#accTime",navTab.getCurrentPanel());
	 for(var i = 0;i < inputs.length; i++){
//			  if(inputs[i].value == ""){
//				 alertMsg.error("出险日期不能为空!");
//				 return;
//			 }else{ 
				 var accTime = inputs[i].value.replace(/-/g, '/');
				 for(var j = 0; j < i; j++){
						 var accTimeNext = inputs[j].value.replace(/-/g, '/');
						 if(accTime > accTimeNext){
							 var temp = inputs[j];
							 inputs[j] = inputs[i];
							 inputs[i] = temp;
						 }
				 }
				if(accTime>sysTime){//如果出险日期大于系统日期，给出错误提示信息。
					 accTimeAndsystemDate="YES";
				 }
				 if(accidentTime>accTime){//如果事故日期大于出险日期，给出错误提示信息。
					 
					 accTimeAndaccidentTime="YES";
				 } 
//			 }
	 }
	//页面必填项js校验	
	 var shulength = $("#register",navTab.getCurrentPanel()).find("tr").length;	
	 var flag = false;
	 var trs = $("#tbodysIdData" , navTab.getCurrentPanel()).find("tr");
/* 	 if(trs.length > 0){
		for(var i = 0; i < trs.length; i++){
			if($("#tbodysIdData" , navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(1)").find("input").val() == ""){
				alertMsg.error("出险结果1不能为空!");
				return false;
			}
		}
	 } else {
		 alertMsg.error("出险结果1不能为空!");
		 return false; 
	 }
	 if(flag){
		 return;
	 } */
  	 var accReasonId = $("select#accReasonId",navTab.getCurrentPanel()).val();
  	 var clmJson = new Array();
	 var brand = ["claimType","claimDate","accReason"];
	 clmJson += '"'+"[";
		$("#tbodyIds tr", navTab.getCurrentPanel()).each(function() {
			clmJson += "{";
							$(this).children("td:lt(3)").each(function(i) {
								if(i==0) {
									var Value = $(this).find("select[name='claimSubCaseVO.claimType']").val();
									clmJson = clmJson + "'" + brand[i]
											+ "'" + ":'"
											+ Value + "',";
								}
								if(i==1) {
									var Value = $(this).find("input#accTime").val();
									clmJson = clmJson + "'" + brand[i]
											+ "'" + ":'"
											+ Value + "',";
								}
								if(i == 2) {
									clmJson = clmJson + "'" + brand[i]
									+ "'" + ":'"
									+ accReasonId + "',";
								}
								
							});
							clmJson = clmJson.substring(0, clmJson.length - 1);
							clmJson += "},";
						}
				);
		//进行添加操作
				
		clmJson = clmJson.substring(1, clmJson.length - 1);
		clmJson += "]" ;
		$("#tclaimJson", navTab.getCurrentPanel()).val(clmJson);
	 if (days > 10) {
		 /* alertMsg.confirm("您未在十日内报案，请选择保存或取消！", {
			 okCall : function() { */
				 $.ajax({
			 			'type':'post',
			 			'url':'clm/report/claimReportInfoInputNext_CLM_insertReportInfoInputAction.action?claimCaseVO.caseId='+caseId+'&claimSubCaseVO.clmJson='+clmJson,
			 			'datatype':'json',
			 			'async':false,
			 			'success':function(data){
			 				var json = eval('(' + data + ')');
			 				if(!json.message == ""){
			 					alertMsg.warn(json.message);
			 					alertMsg.confirm(json.message, {
			 						okCall : function() {
			 							next('12','addApplicantForm',caseId);
			 						}
			 					});
			 				} else {
			 					next('12','addApplicantForm',caseId);
			 				}
			 			}
					});
		/* 	 }
		 }); */
		 return;
	 } else {
		 $.ajax({
	 			'type':'post',
	 			'url':'clm/report/claimReportInfoInputNext_CLM_insertReportInfoInputAction.action?claimCaseVO.caseId='+caseId+'&claimSubCaseVO.clmJson='+clmJson,
	 			'datatype':'json',
	 			'success':function(str){
	 				var customerVO = eval("(" + str + ")");
	 				if(!customerVO.message == ""){
	 					alertMsg.warn(customerVO.message);
	 					alertMsg.confirm(customerVO.message, {
	 						okCall : function() {
	 							next('12','addApplicantForm',caseId);
	 						}
	 					});
	 				} else {
	 					next('12','addApplicantForm',caseId);
	 				}
	 			}
			});
	 }
}

 if(document.readyState=="complete"){  
	var currentPageId= <%=session.getAttribute("currentPageId") %>;
	if(currentPageId!="" && currentPageId>1 ){
		onlyReadDataCollect();
	}
}  
 

 function myAjaxDown(json){
	 //回显页面的事件号
	 $("#accidentNoId",navTab.getCurrentPanel()).val(json.data.ACCIDENT_NO);
	 $("#accidentNoIdId",navTab.getCurrentPanel()).val(json.data.ACCIDENT_NO);
	if(json.statusCode == DWZ.statusCode.error) {
		if(json.message && alertMsg) alertMsg.error(json.message+"失败");
	} else if (json.statusCode == DWZ.statusCode.timeout) {
		  DWZ.loadLogin(json.flag);
	} else {
		if(json.message && alertMsg){
			if(json.data.status == '10' && json.data.status == "1"){
				alertMsg.warn("你查询的出险人名下在事故发生日时无有效保单。并且<理赔类型>没有匹配的责任，请检查！");
			} else if(json.data.status == "1"){
				alertMsg.warn("你查询的出险人名下在事故发生日时无有效保单。");
			} else if(json.data.status == '10'){
				alertMsg.warn("<理赔类型>没有匹配的责任，请检查！");
			}else {
				alertMsg.correct(json.message);
			}
		} else{
			if(json.data.status == '881'){
				alertMsg.warn(json.data.message);
			}
		  	nextToDuty('${caseId}');
		}
	};
 }
 //认可医院
 function hospitalSignHtmlChange(){
		var hospitalSignHtml = $("#hospitalSignHtml",navTab.getCurrentPanel()).val();
		if(hospitalSignHtml==1){
			$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).html("是");
			$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).css("color","#000000");
		}else if(hospitalSignHtml==0){
			$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).html("否");
			$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).css("color","#FF0000");
		}else{
			$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).html("");
			$("#hospitalSignHtmlInput",navTab.getCurrentPanel()).css("color","#000000");
		}
	}
</script>
<style type="text/css">
    /* 删除按钮样式 */
	a.btnDelPrivate{
		display:block; width:22px; height:20px; 
		text-indent:-1000px; overflow:hidden; 
		float:left; margin-right: 3px}
		a.btnDelPrivate{
		background-position: -23px 0;
		background-repeat: no-repeat;
		background-color: transparent;
		background-image: url("clm/images/imgX.gif");
		}
</style>
<div id="datacollectDiv" class="main_tabdiv">
	<!-- 页面带进来的赔案号，事件号，只读 -->
		<div  class="panelPageFormContent" >
		<dl>	
			<dt>赔案号</dt>
				<dd>
					<input name="" type="text" size="30"
							value="${claimCaseVO.caseNo}" readonly="readonly" id="claimCaseNo"
							class="readonly" />
					
					 <input name="" type="hidden" size="17" value="${claimCaseVO.caseNo }" readonly="readonly" id="claimCaseNo"/> 
					 <input id="caseId" type="hidden" value="${claimCaseVO.caseId}">
					 <input id="applicantNatureDataId" type="hidden" value="${claimSubCaseVO.applicantNature}">
					 <input id="applicantNatureFlagDataId" type="hidden" value="${claimSubCaseVO.applicantNature}">
					 <input id="peopleFlagId" type="hidden">
					 <input type="hidden" id="insertByNameRegisterId" value="${claimDoctorVO.insertByName}" />
					 <input type="hidden" id="insertByRegisterId" value="${claimDoctorVO.insertBy}">
				</dd>
		</dl> 
		<dl>	
			<dt>事件号</dt>
			<dd>
				<input name="accident.accidentNo" type="text" size="30"
							value="${accident.accidentNo}" readonly="readonly"
							id="accidentNoId" class="readonly" />
				<input name="accident.accidentNo" type="hidden" size="17" value="${accident.accidentNo}" readonly="readonly" id="accidentNoId"/> <input type="hidden" id="flagIdId" name="accident.flag" disabled="disabled"/>
				<input type="hidden" id="accidentIdRegister"  value="${accident.accidentId}"/>
			</dd>
		</dl>
		<a lookupGroup="accidentVO" id="accidentVOResultPageIdData" width="800" height="400"  href="" class="btnLook" style="visibility: hidden;">查询出险结果</a>
		</div>
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">申请人信息
					</h1>
				</div>
				<form method="post" class="pageForm required-validate"  onsubmit ="return validateCallback(this, dialogAjaxDone);" novalidate="novalidate">
				<div class="tabdivclassbr">
				<table  class ="list" id = "table" style="width: 100%">
			   		 <thead>
						 <tr >
						   <th nowrap>选择</th>
						   <th nowrap>序号</th>
						   <th nowrap>申请人姓名</th>
						   <th nowrap>与出险人关系</th>
						   <th nowrap>申请人电话</th>
						   <th nowrap>电子邮箱</th>
						   <th nowrap>操作</th>
						</tr>
			 		 </thead>
				    <tbody id="register" class="tbody" >
                     <s:iterator value="applicantVOlist" status="st" var="applicantVar">
								<tr >
									<td>
										<input type="radio" name="123" id="radio${listId}" onclick="findMessage(${listId});" value="0"/>
										<!-- <s:if test="#st.index==0">checked
										</s:if>  -->
									</td>
									<td>
										<div>${st.index+1}</div>
									</td>
									<td>
										<div><s:property value="clmtName"></s:property></div>
									</td>
									<td><div>
												<Field:codeValue tableName="APP___CLM__DBUSER.T_LA_PH_RELA" value="${applicantVar.clmtInsurRelation}" ></Field:codeValue>
												<input id="clmtInsurRelationId" value="${applicantVar.clmtInsurRelation}" type="hidden" />
											</div>
									</td>
									<td>
										<div><s:property value="clmtMp"></s:property></div>
									</td>
									<td>
										<div><s:property value="clmtMail"></s:property></div>
									</td>
									<td>
										<a class="btnDel" href="javascript:;" onclick="todeleteApplicant(${listId})" ref="current">删除</a>
										<input type="hidden" id="clmtPropertyCodeDataIdId" value="${clmtPropertyCode}">
									</td>
								</tr>
						</s:iterator>
						
                    </tbody>																		
				</table>
				</div>
					  <div class="pageFormdiv main_tabdiv">
							<button class="but_blue" type="button" onclick="showPanelId()">添加</button>
							<button class="but_gray" type="button" onclick="addApplicant1()">保存</button>
							<input id="clmtSexIdApplicantData" value="" type="hidden" />
				      </div>
				     
				     <div class="main_box">
						<div class="main_heading"><h1><img src="clm/images/tubiao.png">申请人信息录入
							<b class="maim_lpbubble"></b></h1></div>
							<div class="main_lptwo" style="display: none;">
							<p><div id="applicantPanel" class="panelPageFormContent">	
				 <input type="hidden" name="claimCaseVO.caseId" value="${claimCaseVO.caseId}" id="claimCaseVOcASEID" />
				<div class="panelPageFormContent" id="appliacntMessage" >
                    <input name="applicant.listId" id="listId" value="${applicant.listId}" type="hidden"> 
					<dl >
						<dt><font class="point" color="red"></font>申请人属性</dt> 
							<dd >
								<select class="combox title"  name="applicant.clmtPropertyCode" id="clmtPropertyCodeDataIdFordc" onchange="clmtPropertyCodeChangeData(this)">
									<option value="">请选择</option>
									<option value="01">本人</option>
									<option value="02">指定受益人</option>
									<option value="03">法定受益人</option>
									<option value="04">其他</option>
								</select>
								<select class="combox title" id="contractBeneInfoDataIdFordc" onchange="queryContractBeneInfoData(this);" style="visibility:hidden;">
								</select>
							</dd>
					</dl>
					<dl >
						<dt ><font class="point" color="red">* </font>申请人姓名</dt>
						<dd ><input name="applicant.clmtName"  type="text" id="clmtName" onblur="resetStyle(this)"  /></dd>
					</dl>
<!-- 					<dl style="width: 32%"> -->
<!-- 						<dt style="width: 32%">性别<font class="point" color="red">*</font></dt> -->
<!-- 						<dd style="width: 60%"> -->
<%-- 							 <Field:codeTable cssClass="combox"  id="clmtSex" name="applicant.clmtSex" tableName="APP___CLM__DBUSER.T_GENDER" whereClause="gender_code in(1,2)" value="${applicant.clmtSex}" />  --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
					<dl >
						<dt ><font class="point" color="red">* </font>与出险人关系</dt>
						<dd >
							 <Field:codeTable cssClass="combox title" nullOption="true"  name="applicant.clmtInsurRelation" tableName="APP___CLM__DBUSER.T_LA_PH_RELA"  id="clmtInsurRelationFordc"
							 		whereClause="1=1" orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
						</dd>
					</dl>
<!-- 					<dl style="width: 32%"> -->
<!-- 						<dt style="width: 32%">证件类型<font class="point" color="red">*</font></dt> -->
<!-- 						<dd style="width: 60%"> -->
<%-- 							 <Field:codeTable cssClass="combox"  name="applicant.clmtCertiType" tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"   id="clmtCertiType" value="${applicant.clmtCertiType}" --%>
<%-- 							  whereClause="code in ('0','5','4','1','2','3','81','82')"  orderBy="decode(code,'0','1','5','2','4','3','1','4','2','5','3','6','81','7','82','8', code)" />  --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 					<dl style="width: 32%"> -->
<!-- 						<dt style="width: 32%">证件号码<font class="point" color="red">*</font></dt> -->
<%-- 						<dd style="width: 60%"><input name="applicant.clmtCertiNo" id="clmtCertiNo" value="${applicant.clmtCertiNo}" /></dd> --%>
<!-- 					</dl> -->
<!-- 					<dl style="width: 32%"> -->
<!-- 						<dt style="width: 32%">证件有效期<font class="point" color="red">*</font></dt> -->
<!-- 						<dd style="width: 60%"> -->
<!-- 							<input type="expandDateYMD" name="applicant.clmtCertiValidDate" id="clmtCertiValidDate"  size="17" readonly="readonly"/> -->
<!-- 							<a class="inputDateButton" id="isLongValids" href="javascript:;">选择</a> -->
<!-- 							 <input name="applicant.isLongValid" id="isLongValid" type="checkbox" value="0"/>长期  -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 					<dl style="width: 32%"> -->
<!-- 						<dt style="width: 32%">与投保人关系<font class="point" color="red">*</font></dt> -->
<!-- 						<dd style="width: 60%"> -->
<%--                         <Field:codeTable cssClass="combox"  name="applicant.clmtHolderRelation" tableName="APP___CLM__DBUSER.T_LA_PH_RELA"  id="clmtHolderRelation" value="${applicant.clmtHolderRelation}"/>   --%>
<!-- 						</dd> -->
<!-- 					</dl> -->
					<dl >
						<dt ><font class="point" color="red">* </font>申请人电话</dt>
						<dd >
							<input id="clmtMp" name="applicant.clmtMp" type="expandMobile" />
<!-- 							<input id="clmtMp" name="applicant.clmtMp" type="expandMobile" name="mobile"  onkeyup="changePhoneDataa(this)"/> -->
						</dd>
					</dl>
<!-- 					<dl style="width: 32%"> -->
<!-- 					<dt style="width: 32%">职业</dt> -->
<!-- 					<dd style="width: 60%"> -->
<!-- 					<input name="applicant.clmtProfession" id="clmtProfession" type="text" size="20" /> -->
<%-- 					     <Field:codeTable cssClass="combox"  name="applicant.clmtProfession" tableName="APP___CLM__DBUSER.T_JOB_CODE"  id="clmtProfession"/> --%>
<!-- 					</dd> -->
<!-- 				  </dl> -->
					  <dl >
						<dt >电子邮件</dt>
						<dd ><input name="applicant.clmtMail" id="clmtMail" size="20" class="email" class="required" /></dd>
					  </dl>
<!-- 				  <dl style="width: 32%"> -->
<!-- 						<dt style="width: 32%">固定电话</dt> -->
<%-- 						<dd style="width: 60%"><input id="clmtTel" size="20" name="applicant.clmtTel" value="${applicant.clmtTel}" /></dd> --%>
<!-- 					</dl> -->
<!-- 				  <dl style="width: 32%"> -->
<!-- 					<dt style="width: 32%">国籍</dt> -->
<!-- 					<dd style="width: 60%"> -->
<%-- 					<Field:codeTable cssClass="combox"  name="applicant.clmtNation" tableName="APP___CLM__DBUSER.T_COUNTRY"  id="clmtNation" defaultValue="CHN" value="${applicant.clmtNation}" whereClause="1=1"  orderBy="decode(country_code,'CHN','A',country_code)"/>   --%>
<!-- 					</dd> -->
<!-- 				  </dl> -->
<!-- 				   <dl style="width: 32%"> -->
<!-- 					<dt style="width: 32%">领款人银行</dt> -->
<!-- 					<dd style="width: 60%"> -->
<%-- 						 <Field:codeTable cssClass="combox"  name="applicant.clmtBank" tableName="APP___CLM__DBUSER.T_BANK" id="clmtBank" nullOption="true" value="${applicant.clmtBank}" /> --%>
<!-- 					</dd> -->
<!-- 				  </dl>  -->
<!-- 				  <dl style="width: 32%"> -->
<!-- 					<dt style="width: 32%">银行户名</dt> -->
<%-- 					<dd style="width: 60%"><input name="applicant.clmtAccountName" id="clmtAccountName"class="" type="text" size="20" value="${applicant.clmtAccountName}"  /></dd> --%>
<!-- 				  </dl> -->
<!-- 				  <dl style="width: 32%"> -->
<!-- 					<dt style="width: 32%">银行账号</dt> -->
<%-- 					<dd style="width: 60%"><input name="applicant.clmtAccount" id="clmtAccount" size="17" style="width:127px"  type="expandBankAccount" name="bankAccount" value="${applicant.clmtAccount}" /></dd> --%>
<!-- 				  </dl> -->

					<div class="mian_site">
						<dl>
							<dt><font>* </font>联系地址</dt>
						</dl>
						<div class="main_detail">
							<dl><dd>
									<Field:codeTable cssClass="selectToInput"  name="claimAccidentVO.accProvince" onChange="ProvinceChangeReportDataApplicant(this);" tableName="APP___CLM__DBUSER.T_DISTRICT" nullOption="true" id="provinceReportApplicantId" whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
								<input id="clmtProvinceData" value="${applicant.clmtProvince}" type="hidden">
								<input id="clmtCityData" value="${applicant.clmtCity}" type="hidden">
								<input id="clmtDistreactData" value="${applicant.clmtDistreact}" type="hidden">
							<span>-</span></dd></dl>
							<dl><dd>
								<select class="selectToInput"  onchange="cityChageReportDataApplicant(this);" id='cityReportApplicantId' name="claimAccidentVO.accCity">
									</select>
							<span>-</span></dd></dl>
							<dl><dd>
								<select class="selectToInput"  id="areaReportApplicantId" name="applicant.clmtDistreact">
									</select>
							<span>-</span></dd></dl>
							<dl><dd><input name="applicant.clmtStreet"  id="clmtStreet" value="乡镇/街道${applicant.clmtStreet}" style="width: 178px"/></dd></dl>
						</div>
					</div>	
			  </div>
			 </div></p>				
						</div>
					</div>
			 </form>
		<form method="post" id = "Forms" class="pageForm required-validate"  onsubmit ="return validateCallback(this, myAjaxDown);" novalidate="novalidate">          	  				
				<input type="hidden" name="claimCaseVO.caseId" value="${claimCaseVO.caseId}" id="claimCaseVOcASEID" />
				<input type="hidden" name="claimCaseVO.caseNo" size="30" value="${claimCaseVO.caseNo}" /> 
				<input type="hidden" name="accident.accidentNo" size="30" value="${accident.accidentNo}" id="accidentNoIdId"/> 
				<input type="hidden" name="claimCaseVO.accidentId" value="${claimCaseVO.accidentId}" size="20" id="accidentIdId"/>
				<input type="hidden" name="claimCaseVO.insuredId" value="${claimCaseVO.insuredId}" size="20" id="insuredIdD"/>
				<input type="hidden"  value="${claimCaseVO.insuredId}" id="insuredId"/>	
				<input type="hidden" id="expiryDate" name="claimCaseVO.expiryDate" value="${claimCaseVO.expiryDate}" size="20" /> 
			    <input type="hidden" id="acceptTimeId" name="claimCaseVO.acceptTime" value="<s:date name='claimCaseVO.acceptTime' format='yyyy-MM-dd'/>" size="20" /> 
			    <input type="hidden" id="sysTimeId" name="claimCaseVO.systemDateString" value="${claimCaseVO.systemDateString}"/>
<!-- 			    <input type="hidden" name="claimCaseVO.caseStatus" value="31"/> -->
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">案件申请信息
					</h1>
				</div>
				<div >
				<div >
				 <dl >
					<dt >申请类型</dt>
					<dd >
						 <Field:codeTable cssClass="combox title"  name="claimCaseVO.caseApplyType"
								tableName="APP___CLM__DBUSER.T_APPLY_TYPE" 
								value="${claimCaseVO.caseApplyType}" />
					</dd >
				  </dl>	
				  <dl>
				      <dt>申请渠道</dt>
				      <dd>
				         <div class="main_datawhite"><Field:codeValue tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" value="${claimCaseVO.channelCode}" /></div>
				      </dd>
				  </dl>
				  <dl >
					<dt >申请日期</dt>
					<dd >
						<input id="applyDate" type="expandDateYMD" name="claimCaseVO.applyDate" value="<s:date name='claimCaseVO.applyDate' format='yyyy-MM-dd'/>" class="date" />
						<a class="inputDateButton" id="applyDateId" href="javascript:;">选择</a>
					</dd>
				  </dl>
				  <dl >
					<dt >受理时间</dt>
					<dd >
						<div class="main_datawhite" title="<s:date name='claimCaseVO.acceptTime' format='yyyy-MM-dd'/>"><s:date name='claimCaseVO.acceptTime' format='yyyy-MM-dd'/></div>
					</dd>
				</dl>
				  <dl>
					<dt >管理机构</dt>
					<dd >
						<input style="width: 30px;border-right:0px" id="" type="text" value="${claimCaseVO.organCode}" size="5" readOnly /> 
						<input style="width:110px;" title="${claimCaseVO.organName}" id="" type="text" value="${claimCaseVO.organName}" readOnly size="60" />
					</dd>
				</dl>
				 <%-- <dl >
					<dt >受理人</dt>
					<dd >
							<div class="main_datawhite" title="${claimCaseVO.userRealName}">${claimCaseVO.userRealName}</div>
						<input name="claimCaseVO.acceptorId"
							value="${claimCaseVO.acceptorId}" id="" type="hidden">
					</dd>
				</dl> --%>
				<dl >
					<dt >签收人</dt>
					<dd >
							<%-- ${claimCaseVO.signerCode}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${claimCaseVO.signerName} --%>
							<input style="width: 90px;border-right:0px" id="" type="text" value="${claimCaseVO.signerCode}" size="5" readOnly /> 
							<input style="width:90px;"  id="" type="text" value="${claimCaseVO.signerName}" readOnly size="5" />
					</dd>
				</dl>
				<dl >
					<dt >立案人</dt>
					<dd >
						<%-- ${claimCaseVO.registerCode}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${claimCaseVO.registerName} --%>
						<input style="width: 90px;border-right:0px" id="" type="text" value="${claimCaseVO.registerCode}" size="5" readOnly /> 
						<input style="width:90px;"  id="" type="text" value="${claimCaseVO.registerName}" readOnly size="5" />
					</dd>
				</dl>
				 <dl >
					<dt >绩优等级</dt>
						<s:if test="claimCaseVO.greenFlag==0 || claimCaseVO.greenFlag==null">
						<dd >
							<Field:codeTable  cssClass="combox title" name="claimCaseVO.greenFlag" 
  							value="0"  disabled="true"
  							 tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG" />
						</dd>
						</s:if>
						<s:else>
						<dd >
							<Field:codeTable  cssClass="combox title" name="claimCaseVO.greenFlag" 
  							value="${claimCaseVO.greenFlag}"  disabled="true"
  							 tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG" />
						</dd>
						</s:else>
				 </dl>
			  </div>
			  </div>
			  </div>
		
		 <div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">受托人信息
					</h1>
				</div>
		<div>
			<div class="panelPageFormContent" >
                 <dl >
					<dt >受托人类型</dt>
					<dd >
						<Field:codeTable cssClass="combox title"  id="trusteeTypeSign"
							name="claimCaseVO.trusteeType" tableName="APP___CLM__DBUSER.T_TRUSTEE_TYPE"
							value="${claimCaseVO.trusteeType}" nullOption="true" />
					</dd>
				</dl>
               <dl >
					<dt >受托人代码</dt>
					<dd >
						<input name="claimCaseVO.trusteeCode" type="text" id=""
							value="${claimCaseVO.trusteeCode}" />
					</dd>
				</dl>
			    <dl >
					<dt >受托人姓名</dt>
					<dd >
						<input name="claimCaseVO.trusteeName" type="text" onblur="resetStyle(this)" id="trusteeNameId"
							value='<s:property value="claimCaseVO.trusteeName"/>' id="" />
					</dd>
				</dl>
			    <dl >
					<dt >手机</dt>
					<dd >
						<input name="claimCaseVO.trusteeMp" 
							value="${claimCaseVO.trusteeMp}" type="expandMobile" name="mobile" id="assignMobile" onkeyup="this.value=this.value.replace(/\D/g)"/>
					</dd>
				</dl>
			    <dl >
				   <dt >固定电话</dt>
				   <dd ><input name="claimCaseVO.trusteeTel" type="expandPhone" value="${claimCaseVO.trusteeTel}" /></dd>
			    </dl>
			    <dl >
				   <dt >证件类型</dt>
				   <dd >
       			 	<Field:codeTable cssClass="combox title"  name="claimCaseVO.trusteeCertiType" id="trusteeCertiType"
 						tableName="APP___CLM__DBUSER.T_CERTI_TYPE" nullOption="true"  value="${claimCaseVO.trusteeCertiType}"
 						 whereClause="code in ('0','5','2','4','1','e','h','i','d','f','g','8')"  
									orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','i','008','d','009','f','010','g','011','8','012', code)"  /> 
			       </dd>
			    </dl>
			    <dl >
				   <dt >证件号码</dt>
				   <dd ><input name="claimCaseVO.trusteeCertiCode" id="trusteeCertiCode"   value="${claimCaseVO.trusteeCertiCode}" onblur="checkCertiCode(this)" /></dd>
			    </dl>
			   <dl >
					<dt >上门签收日期</dt>
					<dd >
						<input id="" type="expandDateYMD" name="claimCaseVO.doorSignTime" value="<s:date name='claimCaseVO.doorSignTime' format='yyyy-MM-dd'/>"
									 class="date" />
						<a class="inputDateButton"  id="doorSignTimeId"  href="javascript:;">选择</a>
					</dd>
				</dl>
				<!-- #98117 4.7 立案-->
				<dl>
					<dt>证件有效起期</dt>
					<dd>
						<input type="expandDateYMD" name="claimCaseVO.assigneeStartDate" id="assigneeStartDate" value="<s:date name="claimCaseVO.assigneeStartDate" format="yyyy-MM-dd"/>" />			
					</dd>
				</dl>
				<dl>
					<dt>证件有效止期</dt>
					<dd>
						<input type="expandDateYMD" name="claimCaseVO.assigneeEndDate" id="assigneeEndDate" value="<s:date name="claimCaseVO.assigneeEndDate" format="yyyy-MM-dd"/>" />			
					   	<input type="hidden" name="claimCaseVO.assigneeEndDate" id="assigneeEndDateHidentId"/>	
					   	<input type="checkbox" id="assigneeEndDateCheckBoxId" onclick="CertiEndCheckBoxAudit(this)"/>长期
					</dd>
				</dl>
			</div>
		</div>
		</div> 
		<div class="panelPageFormContent">
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">出险人信息
					</h1>
				</div>
			<div class="tabdivclassbr" >
				<table class="list" style="width: 100%;">
					<thead>
						<tr>
							<th nowrap>出险人姓名</th>
<!-- 							<th nowrap>客户号</th>
 -->							<th nowrap>证件号码</th>
							<th nowrap>性别</th>
							<th nowrap>出生日期</th>
						</tr>
					</thead>
					<tbody id="claimAccidentCUSInfo">
						<tr>
							<td>${customerVO.customerName }</td>
<%-- 							<td><a href="javaScript:void(0)"  onclick="queryPolicySign(${customerVO.customerId});">${customerVO.customerId}</a><a id ="queryPolicyMsg" href="#" lookupGroup=""></a></td>
 --%>							<td>${customerVO.customerCertiCode }</td>
							<td>
								<s:if test="customerVO.customerGender == 1">
									<input type="hidden" name="customerVO.customerGender" id="" size="20" value="1" />
									        男
								</s:if>
								<s:if test="customerVO.customerGender == 2">
									<input type="hidden" name="customerVO.customerGender" id="" size="20"
										value="2">
										女
								</s:if>
								<s:if test="customerVO.customerGender==9">
									<input type="hidden" size="20" value="9"
										name="customerVO.customerGender" />
										未知
								</s:if>
							</td> 
							<td><s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' /></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	<div class="panelPageFormContent">
        	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">出险信息
					</h1>
				</div>
			<div class="panelPageFormContent" >
				<dl >
							<dt >出险人姓名</dt>
							<dd >
									<div class="main_datawhite">${customerVO.customerName}</div>
								 <input id="claimAccInsuredId" name="customerVO.customerId"
									value="${customerVO.customerId}" type="hidden" /> 
							</dd>
				</dl>
			    <dl >
							<dt >证件号码</dt>
							<dd >
									<div class="main_datawhite">${customerVO.customerCertiCode }</div>
									<input id="accBirthIds" type="hidden" name="customerVO.customerBirthday"
									value="<s:date name='customerVO.customerBirthday' format='yyyy-MM-dd' />" class="date" />
							</dd>
				</dl>
			    <dl >
				   <dt >性别</dt>
				   <dd >
						 <s:if test="customerVO.customerGender == 1">
								<div class="main_datawhite">男</div>
						</s:if>
						<s:if test="customerVO.customerGender == 2">
							         <div class="main_datawhite">女</div>
						</s:if>
						<s:if test="customerVO.customerGender==9">
							         <div class="main_datawhite">未知</div>
						</s:if> 
				   </dd>
			    </dl>
			    <dl >
				   <dt ><font class="point" color="red">* </font>事故日期</dt>
				   <dd >
				   		  <input onPropertychange="onchangeReason(this);" id="accidentTimeIds" type="expandDateYMD"  class="date" name="accident.accDate" value="<s:date name='accident.accDate' format='yyyy-MM-dd'/>"
							 							postField="keyword"
							 							suggestUrl="clm/report/pages/report/queryExistedEvent.jsp"
													    lookupGroup="accident"> <a class="btnLook" id="accDateId"
													href="clm/report/queryExistedEventInit_CLM_insertReportInfoInputAction.action"
													lookupGroup="accident" onclick="myOptionDatacollect(this);">查询已有事件</a>
									<!-- 用于已有事故返回接值 -->
									<input type="hidden" id="accDateReporIdData" value="<s:date name='accident.accDate' format='yyyy-MM-dd'/>">
									<input name="accident.accidentDetail" id="accidentDetailReportIdData" type="hidden">
									<input name="accident.accidentDetailName" id="accidentDetailNameReportIdData" type="hidden">
									<input name="accident.cureHospital" id="cureHospitalReportIdData" type="hidden">
									<input name="accident.cureHospitalName" id="cureHospitalNameReportIdData" type="hidden">
									<input name="accident.cureStatus" id="cureStatusReportIdData" type="hidden">
									<input name="accident.seriousDisease" id="seriousDiseaseReportIdData" type="hidden"> 
				   </dd>
			    </dl>
			    <dl >
							<dt >治疗情况</dt>
							<dd >
								<Field:codeTable cssClass="combox title"  id="cureStatusId" name="claimCaseVO.cureStatus"
									value="${claimCaseVO.cureStatus}" tableName="APP___CLM__DBUSER.T_CURE_STATUS" orderBy="substr(code,1,2)" nullOption="true"/>
							</dd>
			    </dl>
			    
			     <dl >
							<dt >治疗医院</dt>
							<dd >
								<input id="inputHospital" name="claimCaseVO.cureHospital" value="${claimCaseVO.cureHospital}" size="10" type="hidden" /> 
								<input  id="hospitalSignHtml" name="claimCaseVO.hospitalSignHtml" value="${hospitalServiceVO.hospitalSignHtml}" type="hidden"
								 onpropertychange="hospitalSignHtmlChange()"/>
								<input readonly id="cureHospital" name="claimCaseVO.hospitalName" value="${hospitalServiceVO.hospitalName}" type="text" postField="keyword"  
									suggestFields="hospitalLevel,hospitalName"
									suggestUrl="clm/report/pages/report/bringBackHospital.jsp"
									lookupGroup="claimCaseVO" /> 
									<a class="btnLook" id="cureHospitalId" href="clm/report/showHospital_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId=${menuId}" 
									lookupGroup="claimCaseVO" width="900" height="450">查询治疗医院</a>
							</dd>
				 </dl>
			     <dl >
							<dt ><font class="point" color="red">* </font>出险原因</dt>
							<dd >
								<Field:codeTable cssClass="combox title"  id="accReasonId" name="accident.accReason"
									value="${accident.accReason}" tableName="APP___CLM__DBUSER.T_CLAIM_NATURE" whereClause="code in (1,2)" onChange="accidentReasonData();" orderBy="code"/>
							</dd>
				</dl>
			    <dl >
							<dt >意外细节</dt>
							<dd >
								<input id="findaccidentDetails" name="claimCaseVO.accidentDetail"
									value="${claimCaseVO.accidentDetail}" type="hidden" />
								<input id="accidentDetail" name="claimCaseVO.DetailDesc" 
									value="${accidentDetailVO.detailDesc}" type="text"
									 postField="keyword" readonly
									suggestFields="accDetailCode,accDetailDesc"
									suggestUrl="clm/report/pages/report/findAccidentDetails.jsp"
									lookupGroup="claimCaseVO" /> <a class="btnLook" id="accidentDetailId"
									href="clm/report/queryPage_CLM_accidentDetailsAction.action?leftFlag=0&menuId=${menuId}"
									lookupGroup="claimCaseVO">查询意外细节</a>
							</dd>
				</dl> 
			    <dl >
					<dt >轻度疾病</dt>
					<dd >
						<input id="seriousDiseaseData1"  name="claimCaseVO.ligName" value="${laTypeVO.ligName}" 
							postField="keyword" suggestFields="code1,name1" readonly
							suggestUrl="clm/pages/report/findSeriousDisease.jsp"
							lookupGroup="claimCaseVO" /><a class="btnLook"
							href="clm/report/querySeriousDisease_CLM_insertReportInfoInputAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=1"
							lookupGroup="claimCaseVO" id="btnLookSeriousDiseaseId1">查询轻度疾病</a>
						<input id="seriousDiseaseCodeData1" name="claimCaseVO.seriousDisease1" value="${claimCaseVO.seriousDisease1}" type="hidden"/>
					</dd>
			    </dl>
			    <dl >
					<dt >中度疾病</dt>
					<dd >
						<input id="seriousDiseaseData2"  name="claimCaseVO.midName" value="${laTypeVO.midName}" 
							postField="keyword" suggestFields="code2,name2" readonly
							suggestUrl="clm/pages/report/findSeriousDisease.jsp"
							lookupGroup="claimCaseVO" /><a class="btnLook"
							href="clm/report/querySeriousDisease_CLM_insertReportInfoInputAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=2"
							lookupGroup="claimCaseVO" id="btnLookSeriousDiseaseId2">查询中度疾病</a>
						<input id="seriousDiseaseCodeData2" name="claimCaseVO.seriousDisease2" value="${claimCaseVO.seriousDisease2}" type="hidden"/>
					</dd>
			    </dl>
			    <dl >
							<dt >重大疾病</dt>
							<dd >
<%-- 								<Field:codeTable cssClass="combox"  id="seriousDiseaseId" --%>
<%-- 									name="claimCaseVO.seriousDisease" --%>
<%-- 									value="${claimCaseVO.seriousDisease}" nullOption="true" tableName="APP___CLM__DBUSER.T_LA_TYPE" whereClause="1 = 1" orderBy="code"/> --%>
							
								<input id="seriousDiseaseData"  name="claimCaseVO.name" value="${laTypeVO.name}" 
									postField="keyword" suggestFields="code,name" readonly
									suggestUrl="clm/pages/report/findSeriousDisease.jsp"
									lookupGroup="claimCaseVO" /><a class="btnLook"
									href="clm/report/querySeriousDisease_CLM_insertReportInfoInputAction.action?leftFlag=0&menuId=${menuId}&btnLookSeriousDiseaseId=3"
									lookupGroup="claimCaseVO" id="btnLookSeriousDiseaseId">查询重大疾病</a>
								<input id="seriousDiseaseCodeData" name="claimCaseVO.seriousDisease" value="${claimCaseVO.seriousDisease}" type="hidden"/>
							</dd>
			    </dl>
				<!-- 最新需求去掉 -->
<!-- 			    <dl style="width:32%"> -->
<!-- 				   <dt style="width:32%">医生姓名</dt> -->
<%-- 				   <dd style="width:60%"> <input name="claimCaseVO.doctorName" id="doctorName" value="${claimCaseVO.doctorName}" type="text" /></dd> --%>
<!-- 			    </dl> -->
<!-- 			    <dl style="width:32%"> -->
<!-- 				   <dt style="width:32%">科室</dt> -->
<!-- 				   <dd style="width:60%"> -->
<%-- 					  <Field:codeTable  name="claimCaseVO.medDept" tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" nullOption="true" cssClass="notuseflagDelete combox comboxDD" defaultValue=" " showInput="true" showInputId="showInput" value="${claimCaseVO.medDept }" orderBy="code"/> --%>
<%-- 					  <input id="showInput" type="text" value="${medicalDeptVO.name }"> --%>
<!-- 				   </dd> -->
<!-- 			    </dl> -->

				<div class="mian_site">
					<dl>
						<dt><font>* </font>出险地点</dt>
					</dl>
					<div class="main_detail">
						<dl><dd>
							<input type="hidden" name="accident.accProvince1" value="${accident.accProvince}" id="accProvince1ReportIdData">
                             	<input type="hidden" name="accident.accCity1" value="${accident.accCity}" id="accCity1ReportIdData">
                             	<input type="hidden" name="accident.accDistreact1" value="${accident.accDistreact}" id="accDistreact1ReportIdData">
                             	<input type="hidden" name="accident.accCountryCode1" value="${accident.accCountryCode}" id="accCountryCode1ReportIdData">
                             	<input type="hidden" name="accident.accDistreactName1" value="${accident.accDistreactName}" id="accDistreactName1DataId">
                             	<input type="hidden" name="accident.accCityfz" value="${accident.accCity}" id="accCity1ReportIdDatafz">
                             	<input type="hidden" name="accident.accDistreactfz" value="${accident.accDistreact}" id="accDistreact1ReportIdDatafz">
                             	<input type="hidden" name="accident.accProvincefz" value="${accident.accProvince}" id="accProvince1ReportIdDatafz">
                             	<Field:codeTable cssClass="selectToInput" nullOption="true" onChange="contryReportFunData(this);" name="accident.accCountryCode" value="${accident.accCountryCode}" tableName="APP___CLM__DBUSER.T_COUNTRY" id="contryDatatId" defaultValue="CHN"/>
						<span>-</span></dd></dl>
						<dl><dd>
							<Field:codeTable cssClass="selectToInput"  name="accident.accProvince" onChange="ProvinceChangeReportData(this);" nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT" id="provinceReportId" whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)"/>
						<span>-</span></dd></dl>
						<dl><dd>
							<select class="selectToInput"  onchange="cityChageReportData(this);" id='cityReportId' name="accident.accCity">
									</select>
						<span>-</span></dd></dl>
						<dl><dd>	
							<select class="selectToInput" onchange="DistreactChageReportData(this);" id="areaReportId" name="accident.accDistreact">
									</select>
						<span>-</span></dd></dl>
						<dl><dd>
						<s:if test="accident.accStreet != null || caccident.accStreet != ''">
                             			<input name="accident.accStreet" id="accStreetReportId" size="24" value="${accident.accStreet}" style="width: 200px;">
                             		</s:if>
                             		<s:else>
	                             		<input name="accident.accStreet" id="accStreetReportId" size="24" value="乡镇/街道${accident.accStreet}" style="width: 200px;">
                             		</s:else></dd></dl>
					</div>
				</div>	
				<dl>
						<dt>认可医院标识</dt>
						<dd>
							<s:if test='hospitalServiceVO.isDesignated == 0'>
								<div id="hospitalSignHtmlInput" class="main_datawhite" style="color: #FF0000">否</div>
							</s:if>
							<s:elseif test='hospitalServiceVO.isDesignated == 1'>
								<div id="hospitalSignHtmlInput" class="main_datawhite">是</div>
							</s:elseif>
							<s:else>
								<div id="hospitalSignHtmlInput" class="main_datawhite"></div>
							</s:else>
						</dd>
				</dl>
				<dl >
					<dt style="width: 155px;">社保状态</dt>
					<dd >
						<s:if test='sociSecu == "0"'>
									<div class="main_datawhite">否</div>
								</s:if>
								<s:elseif test='sociSecu == "1"'>
									<div class="main_datawhite">是</div>
								</s:elseif>
								<s:else>
									<div class="main_datawhite"></div>
								</s:else>
					</dd>							 
				</dl>
				<dl>
					<dt style="width: 155px;">确诊时间</dt>
					<dd>
						<input type="expandDateYMD" id="diagnosisTime" name="claimCaseVO.diagnosisTime" class="date" value="<s:date name='claimCaseVO.diagnosisTime' format='yyyy-MM-dd'/>" /><a class="inputDateButton" href="javascript:;">选择</a>
					</dd>							 
				</dl>
				<%-- <dl>
					<dt style="width: 155px;">身故日期</dt>
					<dd>
						<input type="expandDateYMD" id="dieDate" name="claimCaseVO.dieDate" class="date" value="<s:date name='claimCaseVO.dieDate' format='yyyy-MM-dd'/>" /><a class="inputDateButton" href="javascript:;">选择</a>
					</dd>							 
				</dl>	 --%>
				<dl>
					<dt style="width: 155px;">是否在商业保司理赔</dt>
					<dd>
						<Field:codeTable cssClass="combox title" id="insurancePaidFlag"  name="claimCaseVO.insurancePaidFlag" tableName="APP___CLM__DBUSER.T_YES_NO" value="${claimCaseVO.insurancePaidFlag }" whereClause="1=1" orderBy="YES_NO"/>
					</dd>							 
				</dl>	
			    <dl style="width: 100%;height: auto">
	       				<dt ><font class="point" color="red">* </font>事故描述</dt>
	       				<dd ><textarea  class="required" name="accident.accDesc" id="accidentDescId" rows="2" cols="100" maxlength="1000" onpropertychange="if(value.length>1000) value=value.substring(0,1000)" >${accident.accDesc }</textarea></dd>
                </dl> 
				</div>
		</div>
		<div class="panelPageFormContent main_tabdiv">
			<div class="tabdivclassbr">
					<table id="addDoctorId" style="margin-bottom: 20px;padding-bottom: 10px;"  class="list nowrap itemDetail" addButton="添加医生信息">
						<thead>
							<tr>
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="9" width="0%" fieldClass="digits short">序号</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/diacrisisCode.html" size="18" >诊断编码</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/diacrisisDesc.html" size="18" >诊断名称</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/diacrisisTime.html" size="18" width="20%">诊断时间</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/diacrisisDoctorName.html" size="18" width="20%">诊断医生姓名</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/belongOffices.jsp" size="18">归属科室</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/tcmFlag.jsp" size="18">中西医标识</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/medicalType.jsp" size="18" >就诊类型</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/lastUpdateName.jsp" size="18" width="20%">最后一次修改人</th>
								<th type="del" width="5%">操作</th>
							</tr>
						</thead>
						<tbody id="addDoctorTbodyId">
							<s:if test="claimDoctorVOs==null||claimDoctorVOs.size() == 0">
								<tr>
									<td >
										<input name="items[0].itemInt" class="digits textInput focus short" type="text" size="9"  value="1" readonly="readonly"/>
									</td>
									<td>
										<div align="center">
											<input type="text" class="short2" name="claimDoctorVO.diagnosisCode" id="diagnosisCodeId" value="" />
										</div>
									</td>
									<td>
										<div align="center">
											<input type="text" class="short2" name="claimDoctorVO.diagnosisDesc" id="diagnosisDescId" value="" />
										</div>
									</td>
									<td>
										<div align="center">
											<input id="" class= "short2" type="expandDateYMD" name="claimDoctorVO.diagTimeStr" value=""
											class="date"/>
										</div>
									</td>
									
									<td>
										<div align="center">
											<input class="short2" type="text" name="claimDoctorVO.doctorName" id="doctorNameId" value="" onblur="resetStyle(this)" />
										</div>
									</td>
									<td>
										<div align="center">
											<Field:codeTable name="claimDoctorVO.code" tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" nullOption="true" cssClass="notuseflagDelete selectToInput title comboxDD" defaultValue="  " orderBy="code"/>
<!-- 	 					  					<input id="showInput" type="text" readonly="readonly"> -->
 					  					</div>
									</td>
									<td>
										<div align="center">
											<select class="combox title short2"  name="claimDoctorVO.tcmFlagStr" id="tcmFlagId" >
												<option value="0">中</option>
												<option value="1">西</option>
											</select>
 					  					</div>
									</td>
									<td>
										<div align="center">
											<select class="combox title short2"  name="claimDoctorVO.medicalTypeStr" id="medicalTypeId">
												<option value="0">门诊</option>
												<option value="1">住院</option>
											</select>
 					  					</div>
									</td>
									<td>
										<span>${claimDoctorVO.insertByName}</span>
<%-- 										<input type="text"  value="${claimDoctorVO.insertByName}" /> --%>
										<input type="hidden"  value="${claimDoctorVO.insertBy}">
									</td>
									<td>
										<a class="btnDel"></a>
									</td>
								</tr>
							</s:if>
							<s:else> 
							<s:iterator value="claimDoctorVOs" status="sub" var="claimDoctor">
								<tr>
									<td >
										<input name="items[${sub.index+1}].itemInt" class="digits textInput focus short" type="text" size="9"  value="${sub.index+1}" readonly="readonly"/>
									</td>
									<td>
										<div align="center">
											<input type="text" class="short2" name="claimDoctorVO.diagnosisCode" id="diagnosisCodeId" value="${claimDoctor.diagnosisCode}" />
										</div>
									</td>
									<td>
										<div align="center">
											<input type="text" class="short2" name="claimDoctorVO.diagnosisDesc" id="diagnosisDescId" value="${claimDoctor.diagnosisDesc}" />
										</div>
									</td>
									<td>
										<input id="" class= "short2" type="expandDateYMD" name="claimDoctorVO.diagTimeStr" value="<s:date name='#claimDoctor.diagTime' format='yyyy-MM-dd'/>"
											class="date"/>
									</td>
									<td>
										<input type="text" class= "short2"  name="claimDoctorVO.doctorName" id="doctorNameId" value="${claimDoctor.doctorName}" onblur="resetStyle(this)" />
									</td>
									<td>
										<Field:codeTable  name="claimDoctorVO.code" tableName="APP___CLM__DBUSER.T_MEDICAL_DEPT" nullOption="true" cssClass="notuseflagDelete selectToInput title comboxDD" defaultValue="  " showInput="true" showInputId="showInput${sub.index}" orderBy="code" value="${claimDoctor.code}"/>
<%--  					  					<input id="showInput${sub.index}" type="text" readonly="readonly" value="${claimDoctor.name}"> --%>
									</td>
									<td>
										<div align="center">
											<select class="combox title short2"  name="claimDoctorVO.tcmFlagStr" id="tcmFlagId" showInputId="showInput${sub.index}">
												<option value="0" <s:if test='#claimDoctor.tcmFlag == 0'>selected = "selected"</s:if> >中</option>
												<option value="1" <s:if test='#claimDoctor.tcmFlag == 1'>selected = "selected"</s:if> >西</option>
											</select>
 					  					</div>
									</td>
									<td>
										<div align="center">
											<select class="combox title short2"  name="claimDoctorVO.medicalTypeStr" id="medicalTypeId" showInputId="showInput${sub.index}">
												<option value="0" <s:if test='#claimDoctor.medicalType == 0'>selected = "selected"</s:if> >门诊</option>
												<option value="1" <s:if test='#claimDoctor.medicalType == 1'>selected = "selected"</s:if> >住院</option>
											</select>
 					  					</div>
									</td>
									<td>
										<span>${claimDoctor.insertByName}</span>
<%-- 										<input type="text" value="${claimDoctor.insertByName}" /> --%>
										<input type="hidden" value="${claimDoctor.insertBy}">
									</td>
									<td>
										<a class="btnDel"></a>
									</td>
								</tr>
							</s:iterator>
						 </s:else> 
					</tbody>
				</table>
		</div>

			<div class="tabdivclassbr">
				<table id="addIdentifyinfoId" style="margin-bottom: 20px;padding-bottom: 10px;"  class="list nowrap itemDetail" addButton="添加鉴定信息">
					<thead>
					<tr>
						<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="9" width="0%" fieldClass="digits short">序号</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/accreditingBodyName.html" size="18" >鉴定机构名称</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/sociolCreditCode.html" size="18" >统一社会信用代码</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/lient.html" size="18" width="20%">委托人/委托单位</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/entrustingAppraisalMatter.html" size="18" width="20%">委托鉴定事项</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/acceptanceDate.html" size="18">受理日期</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/appraisalDate.html" size="18">鉴定日期</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/appraisalReportDate.html" size="18" >鉴定报告出具日期</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/appraiserName.html" size="18" >司法鉴定人姓名</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/unitCertiCode.html" size="18" >执业证号</th>
						<th type="enum" name=""
							enumUrl="clm/pages/html/lastUpdateName.jsp" size="18" width="20%">最后一次修改人</th>
						<th type="del" width="5%">操作</th>
					</tr>
					</thead>
					<tbody id="addIdentifyinfoTbodyId">
					<s:if test="claimIdentifyInfoVOs==null||claimIdentifyInfoVOs.size() == 0">
						<tr>
							<td >
								<input name="items[0].itemInt" class="digits textInput focus short" type="text" size="9"  value="1" readonly="readonly"/>
								<input type="hidden" name="claimIdentifyInfoVO.lineNumber" value="1" />
								<input type="hidden" name="claimIdentifyInfoVO.listIdStr" />
							</td>
							<td>
								<div align="center">
									<input type="text" class="short2" name="claimIdentifyInfoVO.accreditingBodyName" id="accreditingBodyNameId" value="" />
								</div>
							</td>
							<td>
								<div align="center">
									<input type="text" class="short2" name="claimIdentifyInfoVO.sociolCreditCode" id="sociolCreditCodeId" value="" />
								</div>
							</td>
							<td>
								<div align="center">
									<input type="text" class="short2" name="claimIdentifyInfoVO.lient" id="lientId" value="" />
								</div>
							</td>
							<td>
								<div align="center">
									<input type="text" class="short2" name="claimIdentifyInfoVO.entrustingAppraisalMatter" id="entrustingAppraisalMatterId" value="" />
								</div>
							</td>
							<td>
								<div align="center">
									<input class= "short2" type="expandDateYMD" name="claimIdentifyInfoVO.acceptanceDateStr" value=""
										   class="date"/>
								</div>
							</td>
							<td>
								<div align="center">
									<input class= "short2" type="expandDateYMD" name="claimIdentifyInfoVO.appraisalDateStr" value=""
										   class="date"/>
								</div>
							</td>
							<td>
								<div align="center">
									<input class= "short2" type="expandDateYMD" name="claimIdentifyInfoVO.appraisalReportDateStr" value=""
										   class="date"/>
								</div>
							</td>
							<td>
								<div align="center">
									<input type="text" class="short2" name="claimIdentifyInfoVO.appraiserName" id="appraiserNameId" value="" />
								</div>
							</td>
							<td>
								<div align="center">
									<input type="text" class="short2" name="claimIdentifyInfoVO.unitCertiCode" id="unitCertiCodeId" value="" />
								</div>
							</td>
							<td>
								<span>${claimIdentifyInfo.endUpdateByName}</span>
							</td>
							<td>
								<a class="btnDel"></a>
							</td>
						</tr>
					</s:if>
					<s:else>
						<s:iterator value="claimIdentifyInfoVOs" status="sub" var="claimIdentifyInfo">
							<tr>
								<td >
									<input name="items[${sub.index+1}].itemInt" class="digits textInput focus short" type="text" size="9"  value="${sub.index+1}" readonly="readonly"/>
								</td>
								<td>
						 			<div align="center">
										<input type="text" class="short2" name="claimIdentifyInfoVO.accreditingBodyName" id="accreditingBodyNameId" value="${claimIdentifyInfo.accreditingBodyName}" />
										<input type="hidden" name="claimIdentifyInfoVO.lineNumber" value="1" />
										<input type="hidden" name="claimIdentifyInfoVO.listIdStr" value="${claimIdentifyInfo.listId}"/>
									</div>
								</td>
								<td>
									<div align="center">
										<input type="text" class="short2" name="claimIdentifyInfoVO.sociolCreditCode" id="sociolCreditCodeId" value="${claimIdentifyInfo.sociolCreditCode}" />
									</div>
								</td>
								<td>
									<div align="center">
										<input type="text" class="short2" name="claimIdentifyInfoVO.lient" id="lientId" value="${claimIdentifyInfo.lient}" />
									</div>
								</td>
								<td>
									<div align="center">
										<input type="text" class="short2" name="claimIdentifyInfoVO.entrustingAppraisalMatter" id="entrustingAppraisalMatterId" value="${claimIdentifyInfo.entrustingAppraisalMatter}" />
									</div>
								</td>
								<td>
									<input class= "short2" type="expandDateYMD" name="claimIdentifyInfoVO.acceptanceDateStr" value="<s:date name='#claimIdentifyInfo.acceptanceDate' format='yyyy-MM-dd'/>"
										   class="date"/>
								</td>
								<td>
									<input class= "short2" type="expandDateYMD" name="claimIdentifyInfoVO.appraisalDateStr" value="<s:date name='#claimIdentifyInfo.appraisalDate' format='yyyy-MM-dd'/>"
										   class="date"/>
								</td>
								<td>
									<input class= "short2" type="expandDateYMD" name="claimIdentifyInfoVO.appraisalReportDateStr" value="<s:date name='#claimIdentifyInfo.appraisalReportDate' format='yyyy-MM-dd'/>"
										   class="date"/>
								</td>
								<td>
									<div align="center">
										<input type="text" class="short2" name="claimIdentifyInfoVO.appraiserName" id="appraiserNameId" value="${claimIdentifyInfo.appraiserName}" />
									</div>
								</td>
								<td>
									<div align="center">
										<input type="text" class="short2" name="claimIdentifyInfoVO.unitCertiCode" id="unitCertiCodeId" value="${claimIdentifyInfo.unitCertiCode}" />
									</div>
								</td>
								<td>
									<span>${claimIdentifyInfo.endUpdateByName}</span>
								</td>
								<td>
									<a class="btnDel"></a>
								</td>
							</tr>
						</s:iterator>
					</s:else>
					</tbody>
				</table>
			</div>
	
			<div class="tabdivclassbr">
			 <input name="claimAccidentResultVO.caseId" value="${caseId}" type="hidden">
        			 <table  class="list nowrap itemDetail" addButton="添加出险结果" id="addTable" style="margin-bottom: 20px;padding-bottom: 10px; width: 100%;">
						<thead>
							<tr>
								<th type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="9" width="0%" fieldClass="digits short">序号</th>
								<th type="enum" name="claimAccidentResultVO.accResult1"
									enumUrl="clm/pages/html/accidentResultOne.jsp?accType=" size="12">出险结果1</th>
								<th type="enum" name="claimAccidentResultVO.accResult2"
									enumUrl="clm/pages/html/accidentResultTwo.jsp" size="12">出险结果2</th>
								<th type="enum" name="claimAccidentResultVO.accResult3"
									enumUrl="clm/pages/html/accidentResultThree.jsp" size="12">出险结果3</th>
								<th type="enum" name=""
									enumUrl="clm/pages/html/lastUpdateName.jsp" size="18" >最后一次修改人</th>
								<th type="del">操作</th>
							</tr>
						</thead>
						<tbody class="tbodys" id="tbodysIdData">
							<input type="hidden" id="accidentResultBiaoIdData">
							<input type="hidden" name="accidentVO.accident1Code" id="accident1CodIDData">
							<input type="hidden" name="accidentVO.accident1Name" id="accident1NamIDData">
							<input type="hidden" name="accidentVO.accident2Code" id="accident2CodIDData">
							<input type="hidden" name="accidentVO.accident2Name" id="accident2NamIDData">
							<input type="hidden" name="accidentVO.checkresultCode"
								id="checkresultCode">
							<s:if test="listResultVo==null||listResultVo.size()==0">
								<tr class="unitBox">
									<td>
										<input name="items[0].itemInt" class="digits textInput focus short" type="text" size="9"  value="1" readonly="readonly"/>
									</td>
									<td >
										<!-- 添加俩个输入框和添加的一致 -->
										<input type="hidden"/>
										<input type="hidden"/>
										<input class="short"  readonly="readonly" name="claimAccidentResultVO.accResult1" id="accResult1Data" onclick="accidentVOResultPageData(this);">
										 <input class="lang"  readonly="readonly" name="claimAccidentResultVO.accResultDesc1" id="accResult1NameData" type="text"  onclick="accidentVOResultPageData(this);"/>
									</td>
									<td >	
										<!-- 添加俩个输入框和添加的一致 -->
										<input type="hidden"/>
										<input type="hidden"/>
										<input class="short"  readonly="readonly" name="claimAccidentResultVO.accResult2" id="accResult2Data" onclick="accidentVOResultPageData(this);">
										<input class="lang"  readonly="readonly" name="claimAccidentResultVO.accResultDesc2" type="text" id="accResult2NameData" onclick="accidentVOResultPageData(this);"/>
									</td>
									<td >	
										<!-- 添加俩个输入框和添加的一致 -->
										<input type="hidden"/>
										<input type="hidden"/>
										<input class="short"  readonly="readonly"  name="claimAccidentResultVO.accResult3" id="accResult3Data" onclick="accidentVOResultPageData(this);">
									    <input class="lang"  readonly="readonly"  name="claimAccidentResultVO.accResult3Name" type="text" id="accResult3NameData" onclick="accidentVOResultPageData(this);"/>
									</td>
									<td>
										<span></span>
									</td>
									<td><a class="btnDel"></a></td>
								</tr>
							</s:if>
							<s:else>
								<s:iterator value="listResultVo" var="rlist" status="st">
									<tr>
										<td>
											<input name="items[${st.index+1}].itemInt" class="digits textInput focus short" type="text" size="9"  value="${st.index+1}" readonly="readonly"/>
										</td>
										<td >
											<!-- 添加俩个输入框和添加的一致 -->
											<input type="hidden"/>
											<input type="hidden"/>
											<input class="short"  readonly="readonly" value="${rlist.accResult1}" name="claimAccidentResultVO.accResult1" id="accResult1Data" onclick="accidentVOResultPageData(this);">
										    <input class="lang"  readonly="readonly" value="${rlist.accResultDesc1}" name="claimAccidentResultVO.accResultDesc1" id="accResult1NameData" type="text"  onclick="accidentVOResultPageData(this);"/>
										</td>
										<td >	
											<!-- 添加俩个输入框和添加的一致 -->
											<input type="hidden"/>
											<input type="hidden"/>
											<input class="short"  readonly="readonly" value="${rlist.accResult2}" name="claimAccidentResultVO.accResult2" id="accResult2Data" onclick="accidentVOResultPageData(this);">
										    <input class="lang"  readonly="readonly" value="${rlist.accResultDesc2}" name="claimAccidentResultVO.accResultDesc2" type="text" id="accResult2NameData" onclick="accidentVOResultPageData(this);"/>
										</td>
										<td >	
											<!-- 添加俩个输入框和添加的一致 -->
											<input type="hidden"/>
											<input type="hidden"/>
											<input class="short"  readonly="readonly" value="${rlist.accResult3}" name="claimAccidentResultVO.accResult3" id="accResult3Data" onclick="accidentVOResultPageData(this);">
										    <input class="lang"  readonly="readonly" value="${rlist.accResult3Name}" name="claimAccidentResultVO.accResult3Name" type="text" id="accResult3NameData" onclick="accidentVOResultPageData(this);"/>
										</td>
										<td>
											<span>${rlist.updateName}</span>
										</td>
										<td><a class="btnDel"></a></td>
									</tr>
								</s:iterator>
							</s:else>
						</tbody>
					</table>

</div>
			<div class="tabdivclassbr">
				<input name="claimSubCaseVO.caseId" value="${caseId}" type="hidden" />
		<table id="tableId"   id="flag"
						class="list nowrap itemDetail" addButton="添加理赔类型" width="100%" style="margin-bottom: 10px;padding-bottom: 10px;">
						<thead>
							<tr>
								<th type="enum" name="claimSubCaseVO.claimType"
									enumUrl="clm/pages/html/claimType.jsp" size="12">理赔类型</th>
								<th type="enum" name="claimSubCaseVO.claimDate"
									enumUrl="clm/pages/html/accidentTimeregis.html" size="12">出险日期</th>
								<th type="enum" name="claimSubCaseVO.accAgeString"
									enumUrl="clm/pages/html/accidentAge.html" size="12">出险年龄</th>
								<th type="del" width="60">操作</th>
							</tr>
						</thead>
						<tbody id="tbodyIds">
							<input type="hidden" id="tclaimJson" name="claimSubCaseVO.clmJson" value=""/>
							<s:if test="subCaseVoList==null||subCaseVoList.size()==0">
								<tr>
									<td >
										<Field:codeTable cssClass="combox title" nullOption='true'  name="claimSubCaseVO.claimType"  id="claimType"  
											value="${claimType}" tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" whereClause="code not in (11)"  orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)"></Field:codeTable>
									</td>
									<td>
										<input id="accTime" type="expandDateYMD" name="claimSubCaseVO.claimDate" value="" 
											class="date" onPropertychange="countAgeRegis(this)" />
										<a class="inputDateButton" id="claimDateId"  href="javascript:;">选择</a>
									</td>
									
									<td>
										<input type="text" name="claimSubCaseVO.accAgeString"
										readonly="readonly" value="" />
									</td>
									<td>
										<a class="btnDel"></a>
									</td>
								</tr>
							</s:if>
							<s:else> 
							<s:iterator value="subCaseVoList" status="sub" var="subCase">
								<tr>
									<td>
										<Field:codeTable cssClass="combox title" nullOption='true'  name="claimSubCaseVO.claimType"   id="claimType"
										value='${subCase.claimType}' tableName="APP___CLM__DBUSER.T_CLAIM_TYPE" whereClause="code not in (11)"  orderBy=" decode(code,'08',1,'03',2,'10',3,'04',4,'02',5,'01',6,'11',7,'06',8,'07',9)"></Field:codeTable>
										
									</td>
										
									 <td>
										<input id="accTime" type="expandDateYMD" name="claimSubCaseVO.claimDate" value="<s:date name='#subCase.claimDate' format='yyyy-MM-dd'/>"
											class="date" onPropertychange="countAgeRegis(this)"/>
										<a class="inputDateButton" id="claimDateId" href="javascript:;">选择</a>
									</td> 
									
									<td>
										<input type="text" name="claimSubCaseVO.accAgeString" readonly="readonly"
											value="${subCase.accAgeString}" />
									</td>
									<td>
										<a class="btnDel"></a>
									</td>
								</tr>
							</s:iterator>
						 </s:else> 
					</tbody>
				</table>
		</div>
</div>
</form>
		<!-- 页面底部：保存、下一步、退出 -->
		<div class="formBarButton main_bottom">								
			<ul>
				<li> <button class="but_blue" type="button" onclick="autoMatchClacLogClick(0)">保存</button> </li>
				<li> <button class="but_blue" type="button" class="button" id="nextIdData" onclick="autoMatchClacLogClick(1)">下一步</button> </li>
				<li> <button class="but_gray" type="button" onclick="exit()" class="close">退出</button> </li>
			</ul>	
		</div>   
</div>