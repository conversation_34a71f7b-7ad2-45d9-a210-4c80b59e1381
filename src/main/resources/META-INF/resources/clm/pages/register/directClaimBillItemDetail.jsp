<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
 
<script type="text/javascript" src="clm/pages/taskmanage/queryTaskTraceInit.js">
</script>
<style type="text/css">  
.plus{text-align: center; border: 1px solid #3a9cdf; color: #3a9cdf; width: 12px; height: 12px; line-height: 9px; padding: 0px; font-size: 12px; font-weight: bold; background:#fff;}
</style>
<script type="text/javascript">

$(document).ready(function(){
	   $("[id^=showBillItem_]").unbind("click");
	   $("[id^=showBillItem_]").toggle(function(){
	     $("[id^=" + $(this).attr("id") + "_]").animate({height: 'toggle', opacity: 'toggle'}, "slow");
	   },function(){
	     $("[id^=" + $(this).attr("id") + "_]").animate({height: 'toggle', opacity: 'toggle'});
	   });
	});

	//多级标签的处理
	$(function(){ 
		$("#directClaimBillItemDetail").find("[id^=showBillItem_]").unbind("click");
		//绑定特殊伸缩行
		$("#directClaimBillItemDetail").find("[id^=showBillItem_]").bind("click",function(){
			if($(this).hasClass("showBillItemClass")){
				$(this).removeClass("showBillItemClass");
				$(this).find("td").eq(0).find("input").val("-");
				$("[id^=" + $(this).attr("id") + "_]").slideDown();
			}else{
				$(this).addClass("showBillItemClass");
				$(this).find("td").eq(0).find("input").val("+");
				$("[id^=" + $(this).attr("id") + "_]").slideUp();
				
			}
		});
	});


</script>
<div class="pageHeader" layoutH="10" style="background: #fafafa">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">费用明细
		</h1>
	</div>
	<div >
		<div  class="pageFormInfoContent">
			<dl>
				<dt><font>* </font>赔案号</dt>
				<dd>
					<input type="text" name="claimCaseVO.caseNo"
						id="taskTraceCaseNo" disabled="disabled" value="${claimCaseVO.caseNo}" maxlength="11"/>
					<input type="hidden" name="claimCaseVO.caseId"
						id="caseId" value="${claimCaseVO.caseId}" />
				</dd>
			</dl>
			<dl>
				<dt><font>* </font>账单号</dt>
				<dd>
					<input type="text" name="claimBillVO.billNo"
						id="billNo" disabled="disabled" value="${claimBillVO.billNo}" />
				</dd>
			</dl>
		</div>
		
		<div class="tabdivclassbr">
			<table class="list" width="100%">
				<thead>
					<tr>
						<th nowrap></th> <!-- 不能删 -->
						<th nowrap>序号</th>
						<th nowrap>目录类别</th>
						<th nowrap>费用类别</th>
						<th nowrap>发票费用项分类名称</th>
						<th nowrap>缴费时间</th>
						<th nowrap>医院收费项目编码</th>
						<th nowrap>医院收费项目名称</th>
						<th nowrap>药品及诊疗项目来源</th>
						<th nowrap>规格</th>
						<th nowrap>剂型名</th>
						<th nowrap>单价</th>
						<th nowrap>数量</th>
						<th nowrap>金额</th>
						<th nowrap>自付比例</th>
						<th nowrap>医保限价</th>
						<th nowrap>项目等级</th>
						<th nowrap>汇总金额</th>
					</tr>
				</thead>
				<tbody id="directClaimBillItemDetail">
					<s:if test="claimBillItemVOlist == null || claimBillItemVOlist.size()==0">
						<tr>
							<td colspan="20">
								<div class="noRueryResult">没有符合条件的查询结果！</div>
							</td>
						</tr>
					</s:if>
					
					<s:iterator value="claimBillItemVOlist" status="st">
						<tr align="center" id="showBillItem_${st.index+1 }" showChildTaskCodeClass="showChildTaskCodeClass" class="showBillItemClass">
							<td><input type="button"  style="text-align: center;" value="+" class="plus"></td>
							<td>${st.index+1}</td>
							<td></td>
							<td>${medFeeItem }</td>
							<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_INVOICE_TYPE" 
										value="${medFeeItem}" />
							</td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td></td>
							<td>${feeAmount }</td>
							<!-- 循环乐约明细 -->
							<s:iterator value="claimBillItemDetailVOs" status="stSub" var="detail">
								<s:if test ="#detail.isCostRes == 1">
									<tr id="showBillItem_${st.index+1 }_${stSub.index+1}" style="display: none;background-color:#FF0000;" >
								</s:if>
								<s:else>
									<tr id="showBillItem_${st.index+1 }_${stSub.index+1}" style="display: none;" >
								</s:else>
									<td></td>
									<td>${stSub.index+1}</td>
									<td><Field:codeValue tableName="APP___CLM__DBUSER.T_CLAIM_LISTCAT" value="${listCat}"/></td>
									<td>${medFeeItem }</td>
									<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_INVOICE_TYPE" 
										value="${medFeeItem}" /></td>
									<td><s:date name="recipeDate" format="yyyy-MM-dd HH:mm:ss"/> </td>
									<td>${hospitalChargeCode}</td>
									<td>${hospitalChargeName}</td>
									
									<td>
										<s:if test="feeItemSource==1">
											社保
										</s:if>
										<s:if test="feeItemSource==2">
											医院
										</s:if>
									</td>
									<td>${itemSpecification}</td>
									<td>${dosageForm}</td>
									
									<td>${price}</td>
									<s:if test="quantitySh!=null">
									    <td>${quantitySh}</td>
									</s:if>
									<s:else>
									    <td>${quantity}</td>
									</s:else>
									<td>${money}</td>
									<td>${selfPayRatio}</td>
									<td>${medLimitedPrice}</td>
									<td>
										<s:if test="chargeLevel==1">
											甲类
										</s:if>
										<s:if test="chargeLevel==2">
											乙类
										</s:if>
										<s:if test="chargeLevel==3">
											丙类
										</s:if>
									</td>
									<td></td>
								</tr>
							</s:iterator>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
		<div class="divfclass" >
			<dl>
				<dt></dt>
				<dd>
					累计总金额：${claimCaseVO.directSumFeeAmount} 元
				</dd>
			</dl>
		</div>
		
		<div class="formBarButton">
			<ul>
				<li>
					<s:if test="#request.qryQuitFlag == null || #request.qryQuitFlag== ''">
						<button type="button" onclick="exitDialog();"  class="but_gray">退出</button>
					</s:if>
				</li>
			</ul>
		</div>
	</div>
</div>