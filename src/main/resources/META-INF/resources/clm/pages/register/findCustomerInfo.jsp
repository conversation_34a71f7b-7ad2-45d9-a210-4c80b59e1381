﻿﻿﻿<%@ page language="java" pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
﻿﻿﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript">
var actury=null;
function queryCaseCustomer(){ 
	//查询校验
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	//保单号
	var policyCode = $("#policyCodeSel", navTab.getCurrentPanel()).val();
	if(caseNo==null || caseNo ==""){
		alertMsg.error("请录入赔案号。");
		$("#caseNo",navTab.getCurrentPanel()).focus();
		return false;
	}
	if(policyCode==null || policyCode ==""){
		alertMsg.error("请选择保单号");
		return false;
	}
	$.ajax({
		'type':'post',
		'url':'clm/register/checkAntiMoneyLaunderingStatus_CLM_claimAntiMoneyLaunderingAction.action?claimCaseVO.caseNo='+caseNo,
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			for(var kay in data){
				if(data["actulyPay"] == "NO"){
					actury="NO";
				}
				if (data["caseStatus"] != "80") {
					queryCustomerFlag = false;
					alertMsg.error("赔案非结案状态，请确认是否录入正确");
					return false;
				}
			}
			queryCustomerT();
		}
		
	});
}
function queryCustomerT(){
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	var antiMoneyType = $("#antiMoneyType", navTab.getCurrentPanel()).val();
	var antiMoneyBeneID = "";
	var antiMoneyPayeeID = "";
	var antiMoneyID = "";
	var beneId = "";
	if(antiMoneyType =="1"){
		antiMoneyBeneID = $("#antiMoneyBeneNameSel", navTab.getCurrentPanel()).val();
		antiMoneyID = $("#antiMoneyBeneNameSel", navTab.getCurrentPanel()).val();
	}else if(antiMoneyType =="2"){
		antiMoneyID = $("#antiMoneyPayeeNameSel", navTab.getCurrentPanel()).val();
		antiMoneyPayeeID = $("#antiMoneyPayeeNameSel", navTab.getCurrentPanel()).val();
		beneId = $("#antiMoneyBeneNameSel", navTab.getCurrentPanel()).val();
		
	}
	var policyCode = $("#policyCodeSel", navTab.getCurrentPanel()).val();
	
	if(antiMoneyType ==null || antiMoneyType ==""){
		alertMsg.error("请选择客户身份。");
		return false;
	}
	if(antiMoneyType =="1" && (antiMoneyBeneID==null || antiMoneyBeneID== "")){
		alertMsg.error("请选择需要修改的受益人姓名。");
		return false;
	}
	if(antiMoneyType =="2" && (antiMoneyPayeeID==null || antiMoneyPayeeID== "")){
		alertMsg.error("请选择需要修改的领款人及其对应的受益人姓名。");
		return false;
	}
	if(beneId==""&&antiMoneyType =="2"){
		alertMsg.error("请选择需要修改的领款人及其对应的受益人姓名。");
		return false;
	}
	$.ajax({
		'type':'post',
		'url':'clm/register/queryAntiMoneyCustomerInfo_CLM_claimAntiMoneyLaunderingAction.action',
		'datatype':'html',
		'data': {"claimAntiMoneyVO.caseNo":caseNo,
			"claimAntiMoneyVO.antiMoneyType" : antiMoneyType,
			"claimAntiMoneyVO.antiMoneyCustomerId" : antiMoneyID,
			"claimAntiMoneyVO.policyCode" : policyCode,
			"claimAntiMoneyVO.beneId" : beneId,
			},
		'async':false,
		'success':function(html){
			$("#newCustomerDiv", navTab.getCurrentPanel()).empty();
			$("#newCustomerDiv", navTab.getCurrentPanel()).html(html).initUI();
		}
		
	});

    //根据保单号和客户身份信息查询修改历史记录列表
	$.ajax({
		'type':'post',
		'url':'clm/register/queryClaimUserHistory_CLM_claimUserHistoryAction.action',
		'datatype':'html',
		'data': {"claimUserHistoryVO.clientIdentity" : antiMoneyType,
			     "claimUserHistoryVO.policyCode" : policyCode,
			     "claimAntiMoneyVO.caseNo":caseNo,
			     },
		'success':function(html){
			//通过ajax动态加载列表数据
			$("#hirstyList", navTab.getCurrentPanel()).empty();
			$("#hirstyList", navTab.getCurrentPanel()).html(html).initUI();
		}

	});

	/* chenghl01_wb修改开始 2021-12-31*/
	if(antiMoneyType=="1"){
		//受益人
		$("#pholderInsured", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	//	$("#insuredBeneFiciary", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	//	$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
 		$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().removeAttr("disabled",""); 
		$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
		$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'>受益人与领款人关系</dt>").initUI();
		$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'><font>* </font>被保险人与受益人关系</dt>").initUI();
		$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'><font>* </font>投保人与受益人关系</dt>").initUI();
	}else if(antiMoneyType=="2"){
		//领款人
		$("#pholderInsured", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
		$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'><font>* </font>受益人与领款人关系</dt>").initUI();
		$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'>被保险人与受益人关系</dt>").initUI();
		$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'>投保人与受益人关系</dt>").initUI();
	}else if(antiMoneyType=="3"){
		//获取出险人和投保人关系
		var insident = $("#insident", navTab.getCurrentPanel()).val();
		//被保险人
		$("#pholderInsured", navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		if(insident == '0'){
			$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'><font>* </font>投保人与被保险人关系</dt>").initUI();
		}else{
			$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
		}
		$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'>受益人与领款人关系</dt>").initUI();
		$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'>被保险人与受益人关系</dt>").initUI();
		$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'>投保人与受益人关系</dt>").initUI();
	}
	/* 修改结束*/
	//根据id赋值
	document.getElementById('caseNos').value = caseNo;
	document.getElementById('policyCodes').value = policyCode;
}

function queryCustomerChange(obj){
	$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).setMyComboxDisabled("disabled");
	$("#antiMoneyPayeeNameSel",navTab.getCurrentPanel()).setMyComboxDisabled("disabled");
	/* chenghl01_wb修改开始 2021-12-31*/
	if(antiMoneyType=="1"){
		//受益人
		$("#pholderInsured", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
		$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'>受益人与领款人关系</dt>").initUI();
		$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'><font>* </font>被保险人与受益人关系</dt>").initUI();
		$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'><font>* </font>投保人与受益人关系</dt>").initUI();
	}else if(antiMoneyType=="2"){
		//领款人
		$("#pholderInsured", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
		$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'><font>* </font>受益人与领款人关系</dt>").initUI();
		$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'>被保险人与受益人关系</dt>").initUI();
		$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'>投保人与受益人关系</dt>").initUI();
	}else if(antiMoneyType=="3"){
		//获取出险人和投保人关系
		var insident = $("#insident", navTab.getCurrentPanel()).val();
		//被保险人
		$("#pholderInsured", navTab.getCurrentPanel()).prev().removeAttr("disabled","");
		$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
		if(insident == '0'){
			$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'><font>* </font>投保人与被保险人关系</dt>").initUI();
		}else{
			$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
		}
		$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'>受益人与领款人关系</dt>").initUI();
		$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'>被保险人与受益人关系</dt>").initUI();
		$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'>投保人与受益人关系</dt>").initUI();
	}
	/* 修改结束*/
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	if($(obj).val() =="1" || $(obj).val() =="2"){
		$.ajax({
			url : "clm/register/queryCustomerName_CLM_claimAntiMoneyLaunderingAction.action",
			global : false,
			type : "POST",
			data: {"claimAntiMoneyVO.caseNo":caseNo,
					"claimAntiMoneyVO.antiMoneyType" : $(obj).val()
				},
			dataType : "json",
			async : false,
			success : function(claimAntiMoneyVOList) {
				if($(obj).val() =="1"){
					$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).empty();
					var optionStr = "<option value=''>请选择</option>";
					for(var i=0; i <claimAntiMoneyVOList.length; i++){
						if(claimAntiMoneyVOList[i].caseId != ""){
								var antiMoneyName = claimAntiMoneyVOList[i].antiMoneyName;
								var antiMoneyId = claimAntiMoneyVOList[i].antiMoneyCustomerId;
								optionStr += "<option value='" + antiMoneyId +"'>"+antiMoneyName+"</option>";
						}
					}	
			    	$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).loadMyComboxOptions(optionStr,'1');
			    	$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).setMyComboxDisabled("");
				}else{
					$("#antiMoneyPayeeNameSel",navTab.getCurrentPanel()).empty();
						var optionStr = "<option value=''>请选择</option>";
						for(var i=0; i <claimAntiMoneyVOList.length; i++){
							if(claimAntiMoneyVOList[i].caseId != ""){
									var antiMoneyName = claimAntiMoneyVOList[i].antiMoneyName;
									var antiMoneyId = claimAntiMoneyVOList[i].antiMoneyCustomerId;
									optionStr += "<option value='" + antiMoneyId +"'>"+antiMoneyName+"</option>";
							}
						}	
				    $("#antiMoneyPayeeNameSel",navTab.getCurrentPanel()).loadMyComboxOptions(optionStr,navTab.getCurrentPanel());
				    $("#antiMoneyPayeeNameSel",navTab.getCurrentPanel()).setMyComboxDisabled("");
				}
				
			}
		});
	}else{
		$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).empty();
		$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).loadMyComboxOptions("<option value=''>请选择</option>",'1');
		$("#antiMoneyPayeeNameSel",navTab.getCurrentPanel()).empty();
		$("#antiMoneyPayeeNameSel",navTab.getCurrentPanel()).loadMyComboxOptions("<option value=''>请选择</option>",'1');
	}
	//每次出发改变事件清空
	emptyValue("newCustomerDiv");
}
function queryCustomerByPayee(){
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	var antiMoneyID = $("#antiMoneyPayeeNameSel", navTab.getCurrentPanel()).val();
	$.ajax({
		url : "clm/register/queryCustomerNameByPayee_CLM_claimAntiMoneyLaunderingAction.action",
		global : false,
		type : "POST",
		data: {"claimAntiMoneyVO.caseNo":caseNo,
			   "claimAntiMoneyVO.antiMoneyCustomerId" : antiMoneyID,
			},
		dataType : "json",
		async : false,
		success : function(claimAntiMoneyVOList) {
			$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).empty();
			var optionStr = "<option value=''>请选择</option>";
			if(claimAntiMoneyVOList.length>1){
				for(var i=0; i <claimAntiMoneyVOList.length; i++){
					if(claimAntiMoneyVOList[i].caseId != ""){
							var antiMoneyName = claimAntiMoneyVOList[i].antiMoneyName;
							var antiMoneyId = claimAntiMoneyVOList[i].antiMoneyCustomerId;
							optionStr += "<option value='" + antiMoneyId +"'>"+antiMoneyName+"</option>";
					}
				}	
			}else{
				for(var i=0; i <claimAntiMoneyVOList.length; i++){
					if(claimAntiMoneyVOList[i].caseId != ""){
							var antiMoneyName = claimAntiMoneyVOList[i].antiMoneyName;
							var antiMoneyId = claimAntiMoneyVOList[i].antiMoneyCustomerId;
							optionStr = "<option value='" + antiMoneyId +"'>"+antiMoneyName+"</option>";
					}
				}	
			}
	    	$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).loadMyComboxOptions(optionStr,'1');
	    	$("#antiMoneyBeneNameSel",navTab.getCurrentPanel()).setMyComboxDisabled("");
		}
	});
}

//清空
function emptyValue(obj){
	$("#"+obj, $.pdialog.getCurrent()).find("input").each(function(){
		$(this).attr("value","");
	});
	$("#"+obj, $.pdialog.getCurrent()).find("select").each(function(){
		$(this).attr("value","");
	});
}

function saveCustomer(){
	
	getAntiMoneyCertiEndDate();
	
	var antiMoneyCustomerId = $("#antiMoneyCustomerId",navTab.getCurrentPanel()).val();
	if(antiMoneyCustomerId!=null && antiMoneyCustomerId!=""){
		var antiMoneyType = $("#antiMoneyType", navTab.getCurrentPanel()).val();
		if(antiMoneyType != null && antiMoneyType !=""){
			$("#antiMoneyTypeNew", navTab.getCurrentPanel()).val(antiMoneyType);
		}else{
			alertMsg.error("客户身份必填！");
		}
		//必填校验
		var antiMoneyName = $("#antiMoneyName",navTab.getCurrentPanel()).val();
		var antiMoneyPhone = $("#antiMoneyPhone",navTab.getCurrentPanel()).val();
		var antiMoneyNation = $("#antiMoneyNation",navTab.getCurrentPanel()).val();
		var antiMoneyJobCode = $("#antiMoneyJobCode",navTab.getCurrentPanel()).val();
		var antiMoneyCertiType = $("#antiMoneyCertiType",navTab.getCurrentPanel()).val();
		var antiMoneyCertiStarDate = $("#antiMoneyCertiStarDate",navTab.getCurrentPanel()).val();
		var antiMoneyCertiEndDate = $("#antiMoneyCertiEndId",navTab.getCurrentPanel()).val();
		var antiMoneyState = $("#antiMoneyState",navTab.getCurrentPanel()).val();
		var antiMoneyCity = $("#antiMoneyCity",navTab.getCurrentPanel()).val();
		var antiMoneyDistrict = $("#antiMoneyDistrict",navTab.getCurrentPanel()).val();
		var antiMoneyAddress = $("#antiMoneyAddress",navTab.getCurrentPanel()).val();
		var antiMoneyCertiNo = $("#antiMoneyCertiNo",navTab.getCurrentPanel()).val();
		if(antiMoneyType=="1"){
			//受益人
			var Insuredbeneficiary = $("#insuredBeneFiciary", navTab.getCurrentPanel()).val();
			var pholderbeneficiary = $("#pholderBeneFiciary", navTab.getCurrentPanel()).val();
			if(Insuredbeneficiary=="" || Insuredbeneficiary== null){
				alertMsg.error("请选择被保人与受益人关系！");
				return false;
			}
			if(pholderbeneficiary=="" || pholderbeneficiary== null){
				alertMsg.error("请选择投保人与受益人关系！");
				return false;
			}
		}else if(antiMoneyType=="2"){
			//领款人
			var beneficiarypayee = $("#beneFiciaryPayee", navTab.getCurrentPanel()).val();
			if(beneficiarypayee=="" || beneficiarypayee== null){
				alertMsg.error("请选择受益人与领款人关系！");
				return false;
			}
		}else if(antiMoneyType=="3"){
			//获取出险人和投保人关系
			var insident = $("#insident", navTab.getCurrentPanel()).val();
			if(insident == '0'){
				//被保险人
				var pholderinsured = $("#pholderInsured", navTab.getCurrentPanel()).val();
				if(pholderinsured=="" || pholderinsured== null){
					alertMsg.error("请选择投保人人与被保险人关系！");
					return false;
				}
			}
		}
		//证件类型为外国人永久居住身份证，校验证件号码
		if (antiMoneyCertiType == "e"){
			if(!(antiMoneyCertiNo.length == 15 || antiMoneyCertiNo.length == 18)){
				alertMsg.error("外国人永久居留身份证位数应为15位或18位。");
				return false;
			}
			if(antiMoneyCertiNo.length == 15){
				var check = /^[A-Z]{3}(\d)(?!\1{11})[\d]{11}$/.test(antiMoneyCertiNo);
				if (!check){
					alertMsg.error("外国人永久居留身份证证件号码为15位的，前三位必须为大写字母，后12位数字不能全相同。");
					return false;
				}
			}
			if(antiMoneyCertiNo.length == 18){
				var birthdayNew = antiMoneyCertiNo.substring(6,14);
				var reg = new RegExp("^9.{17}$");
				var check = reg.test(antiMoneyCertiNo);
				if (!check){
					alertMsg.error("外国人永久居留身份证证件号码为18位的，首位必须是9。");
					return false;
				}
			}
		}
		
		if(actury!="NO" && antiMoneyNation=="" || antiMoneyNation== null){
			alertMsg.info("客户国籍必填！");
		}else if(actury!="NO" && antiMoneyJobCode=="" || antiMoneyJobCode== null){
			alertMsg.info("客户职业代码必填！");
		}else if(antiMoneyName=="" || antiMoneyName== null){
			alertMsg.info("客户姓名必填！");
			document.getElementById('antiMoneyName').focus();
		}else if(antiMoneyPhone=="" || antiMoneyPhone== null){
			alertMsg.info("客户电话必填！");
			document.getElementById('antiMoneyPhone').focus();
		}else if(antiMoneyCertiType=="" || antiMoneyCertiType== null){
			alertMsg.info("客户证件类型必填！");
		}else if(antiMoneyCertiStarDate=="" || antiMoneyCertiStarDate== null){
			alertMsg.info("客户证件起期必填！");
		}else if(antiMoneyCertiEndDate=="" || antiMoneyCertiEndDate== null){
			alertMsg.info("客户证件止期必填！");
		}else if (antiMoneyCertiType == "e"&&!checkCertiDate(antiMoneyCertiStarDate,antiMoneyCertiEndDate)) { 
			return false;
		}else if(antiMoneyState=="" || antiMoneyState== null){
			alertMsg.info("客户省必填！");
		}else if(antiMoneyCity=="" || antiMoneyCity== null){
			alertMsg.info("客户市必填！");
		}else if(antiMoneyDistrict=="" || antiMoneyDistrict== null){
			alertMsg.info("客户区/县必填！");
		}else if(antiMoneyAddress=="" || antiMoneyAddress== null){
			alertMsg.info("客户国籍必填！");
		}else{
			alertMsg.confirm("请务必检查核对，确保修改后客户身份基本信息与影像件信息一致！", {
                okCall : function() {
                	$.ajax({
        				'url':"clm/register/saveAntiMoneyCustomerInfo_CLM_claimAntiMoneyLaunderingAction.action",
        				'type':'post',
        				'data':$("#antiMoneyForm", navTab.getCurrentPanel()).serialize(),
        				'success':function(json){
        					var data = eval("("+json+")");
        					if(data.statusCode == '200'){
        						queryCaseCustomer();//调用查询
        						alertMsg.correct("保存成功！");
        					/* chenghl01_wb修改开始 2021-12-31*/
	        					if(antiMoneyType=="1"){
	        						//受益人
	        						$("#pholderInsured", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
	        						$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
	        						$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
	        						$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'>受益人与领款人关系</dt>").initUI();
	        						$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'><font>* </font>被保险人与受益人关系</dt>").initUI();
	        						$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'><font>* </font>投保人与受益人关系</dt>").initUI();
	        					}else if(antiMoneyType=="2"){
	        						//领款人
	        						$("#pholderInsured", navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().removeAttr("disabled","");
	        						$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
	        						$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'><font>* </font>受益人与领款人关系</dt>").initUI();
	        						$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'>被保险人与受益人关系</dt>").initUI();
	        						$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'>投保人与受益人关系</dt>").initUI();
	        					}else if(antiMoneyType=="3"){
	        						//获取出险人和投保人关系
	        						var insident = $("#insident", navTab.getCurrentPanel()).val();
	        						//被保险人
	        						$("#pholderInsured", navTab.getCurrentPanel()).prev().removeAttr("disabled","");
	        						$("#beneFiciaryPayee",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#insuredBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						$("#pholderBeneFiciary",navTab.getCurrentPanel()).prev().attr("disabled","disabled");
	        						if(insident == '0'){
	        							$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'><font>* </font>投保人与被保险人关系</dt>").initUI();
	        						}else{
	        							$("#pholderInsureds", navTab.getCurrentPanel()).html("<dt id='pholderInsureds'>投保人与被保险人关系</dt>").initUI();
	        						}
	        						$("#beneFiciaryPayees", navTab.getCurrentPanel()).html("<dt id='beneFiciaryPayees'>受益人与领款人关系</dt>").initUI();
	        						$("#insuredBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='insuredBeneFiciarys'>被保险人与受益人关系</dt>").initUI();
	        						$("#pholderBeneFiciarys", navTab.getCurrentPanel()).html("<dt id='pholderBeneFiciarys'>投保人与受益人关系</dt>").initUI();
	        					}
        				    }
        					if(data.statusCode == '300'){
        						alertMsg.info(data.message);
        						return false;
        					}
        					/* 修改结束*/
        				}
        			});

                }
            });
		}
	}else{
		alertMsg.info("页面异常请重新初始化");
	}
}


//鼠标移出事件去重查询保单信息
function findInsurancePolicy() {
	var caseNo = $("#caseNo", navTab.getCurrentPanel()).val();
	if(caseNo==null || caseNo ==""){
		return false;
	}
    //鼠标移出回调,根据赔案号去重查询保单列表信息
	$.ajax({
		'type':'post',
		'url':'clm/register/findInsurancePolicy_CLM_claimAntiMoneyLaunderingAction.action?claimCaseVO.caseNo='+caseNo,
		'datatype':'json',
		'success':function(data){
			$("#policyCodeSel",navTab.getCurrentPanel()).empty();
			var bToObj=JSON.parse(data);
			var optionStr = "";
			for(var i=0; i <bToObj.map.length; i++){
				if(bToObj.map[i].policyCode != ""){
					if(i!=0){
						    optionStr += "<option value='" + bToObj.map[i].policyCode +"'></option>";
					}else{
						optionStr += "<option selected='selected' value='" + bToObj.map[i].policyCode +"'></option>";
					}
				}
			}
			if(bToObj.map.length == 0){
				alertMsg.error("没有查询到此赔案相关保单信息，请检查后在试！");
				return false;
			}
			$("#policyCodeSel",navTab.getCurrentPanel()).append(optionStr);
			claimFireEvent($("#policyCodeSel", navTab.getCurrentPanel()));
			
			//置空客户身份及客户姓名
			$("#antiMoneyType",navTab.getCurrentPanel()).selectMyComBox("");
			$("#antiMoneyNameSel",navTab.getCurrentPanel()).loadMyComboxOptions("<option value=''>请选择</option>",'1');
			
		}
		
	});
	//每次出发改变事件清空
	emptyValue("newCustomerDiv");
}
function antiMoneyCertiEndCheckBox(obj){
	if($(obj).attr("checked") == "checked"){
		$("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).val("");
		$("#antiMoneyCertiEndId",navTab.getCurrentPanel()).val("9999-12-31");
		$("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).attr("disabled","disabled");
	} else {
		$("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).val("");
		$("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).removeAttr("disabled","");
	}
}
function getAntiMoneyCertiEndDate(){
	if($("#antiMoneyCertiEndCheckBoxId").attr("checked") == "checked"){
		$("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).val("");
		$("#antiMoneyCertiEndId",navTab.getCurrentPanel()).val("9999-12-31");
		$("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).attr("disabled","true");
	}else{
		$("#antiMoneyCertiEndId",navTab.getCurrentPanel()).val($("#antiMoneyCertiEndDate",navTab.getCurrentPanel()).val());
	}
}
</script>
<body>
	<div layouth="10">
		<div class="panelPageFormContent">
			<div width="100%">
				<dl style="width: 100%">
					<dt style="width: 50px">赔案号</dt>
					<dd>
						<input type="text" name="claimAntiMoneyVO.caseNo"
							onblur="findInsurancePolicy();" id="caseNo" />
					</dd>
				</dl>
				<dl>
					<dt style="width: 50px">保单号</dt>
					<dd>
						<select class="selectToInput" id="policyCodeSel" islegitimacy=true>
						</select>
					</dd>
				</dl>
				<dl>
					<dt style="width: 50px">客户身份</dt>
					<dd>
						<select class="combox title" id="antiMoneyType"
							name="claimAntiMoneyVO.antiMoneyType"
							onchange="queryCustomerChange(this)">
							<option value="">请选择</option>
							<option value="1">受益人</option>
							<option value="2">领款人</option>
							<option value="3">被保险人</option>
						</select>
					</dd>
				</dl>
				<dl>
					<dt style="width: 60px">领款人姓名</dt>
					<dd>
						<select class="combox" id="antiMoneyPayeeNameSel" onchange="queryCustomerByPayee()" disabled="disabled">
						</select>
					</dd>
				</dl>
				<dl>
					<dt style="width: 60px">受益人姓名</dt>
					<dd>
						<select class="combox" id="antiMoneyBeneNameSel" disabled="disabled">
						</select>
					</dd>
				</dl>
			</div>
		</div>
		<div class="formBarButton main_bottom">
			<button class="but_blue" type="button" onclick="queryCaseCustomer()">查询</button>
		</div>
		<div class="main_tabdiv">
			<div class="panelPageFormContent">
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">修改录入区
					</h1>
				</div>
				<div class="panelPageFormContent">
					<div id="newCustomerDiv">
						<form id="antiMoneyForm">
							<!-- chenghl01_wb修改与2021-12-27 -->
							<dl>
								<dt>投保人与被保险人关系</dt>
								<dd>
									<Field:codeTable name="claimInsuredRelationVO.insuredHoliderRe"
										id="pholderInsured"
										value="${claimInsuredRelationVO.insuredHoliderRe}"
										
										tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
										cssClass="notuseflagDelete combox title  required"
										whereClause="1=1"
										orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
								</dd>
							</dl>
							<dl>
								<dt>被保险人与受益人关系</dt>
								<dd>
									<Field:codeTable name="claimBeneVO.beneRelation"
										id="insuredBeneFiciary" value="${claimBeneVO.beneRelation}"
										
										tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
										cssClass="notuseflagDelete combox title  required"
										whereClause="1=1"
										orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
								</dd>
							</dl>
							<dl>
								<dt>投保人与受益人关系</dt>
								<dd>
									<Field:codeTable name="claimPayVO.beneHolderRelation"
										id="pholderBeneFiciary"
										value="${claimPayVO.beneHolderRelation}"
										
										tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
										cssClass="notuseflagDelete combox title  required"
										whereClause="1=1"
										orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
								</dd>
							</dl>
							<dl>
								<dt>领款人与受益人关系</dt>
								<dd>
									<Field:codeTable name="claimPayVO.payeeRelation"
										id="beneFiciaryPayee" value="${claimPayVO.payeeRelation}"
										
										tableName="APP___CLM__DBUSER.T_LA_PH_RELA" nullOption="true"
										cssClass="notuseflagDelete combox title  required"
										whereClause="1=1"
										orderBy="decode(relation_code,'00','001','07','002','25','003','24','004','01','005','02','006','03','007','04','008','08','009','09','010','10','011','11','012','05','013','12','014','13','015','14','016','15','017','16','018','17','019','18','020','19','021','20','022','21','023','23','024','22','025',relation_code)" />
								</dd>
							</dl>
							<!-- 修改完毕 -->
							<dl>
								<dt>
									<font>* </font>姓名
								</dt>
								<dd>
									<input type="hidden"
										name="claimAntiMoneyVO.antiMoneyCustomerId"
										id="antiMoneyCustomerId"
										value="${claimAntiMoneyVO.antiMoneyCustomerId }" /> <input
										type="hidden" name="claimAntiMoneyVO.antiMoneyType"
										id="antiMoneyTypeNew" /> <input type="text"
										name="claimAntiMoneyVO.antiMoneyName" id="antiMoneyName"
										value="${claimAntiMoneyVO.antiMoneyName }" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>性别
								</dt>
								<dd>
									<Field:codeTable cssClass="combox title"
										name="claimAntiMoneyVO.antiMoneySex" id="antiMoneySex"
										tableName="APP___CLM__DBUSER.T_GENDER"
										whereClause="gender_code in(1,2)" nullOption="true" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>联系电话
								</dt>
								<dd>
									<input type="text" name="claimAntiMoneyVO.antiMoneyPhone"
										id="antiMoneyPhone" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>国籍
								</dt>
								<dd>
									<input type="text" name="claimAntiMoneyVO.antiMoneyNation"
										id="antiMoneyNation" value="" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>职业代码
								</dt>
								<dd>
									<Field:codeTable id="antiMoneyJobCode"
										name="claimAntiMoneyVO.antiMoneyJobCode"
										tableName="APP___CLM__DBUSER.T_JOB_CODE"
										whereClause="job_code not in ('Y001023') and display_order not in ('0')" orderBy="DISPLAY_ORDER"
										nullOption="true"
										cssClass="notuseflagDelete selectToInput comboxDD"
										defaultValue=" " value="" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>证件类型
								</dt>
								<dd>
									<Field:codeTable cssClass="combox title"
										name="claimAntiMoneyVO.antiMoneyCertiType"
										id="antiMoneyCertiType"
										tableName="APP___CLM__DBUSER.T_CERTI_TYPE" defaultValue="0"
										nullOption="true"
										whereClause="code in ('0','5','2','4','1','e','h','d','8')"
										orderBy="decode(code,'0','001','5','002','2','003','4','004','1','005','e','006','h','007','d','008','8','009', code)" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>证件号码
								</dt>
								<dd>
									<input type="text" name="claimAntiMoneyVO.antiMoneyCertiNo"
										id="antiMoneyCertiNo" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>证件有效起期
								</dt>
								<dd>
									<input type="expandDateYMD"
										name="claimAntiMoneyVO.antiMoneyCertiStarDate"
										id="antiMoneyCertiStarDate" />
								</dd>
							</dl>
							<dl>
								<dt>
									<font>* </font>证件有效止期
								</dt>
								<dd>
									<input type="expandDateYMD" id="antiMoneyCertiEndDate" /> <input
										type="hidden" name="claimAntiMoneyVO.antiMoneyCertiEndDate"
										id="antiMoneyCertiEndId" /> <input type="checkbox"
										id="antiMoneyCertiEndCheckBoxId"
										onclick="antiMoneyCertiEndCheckBox(this)" />
								</dd>
							</dl>
							<div class="mian_site">
								<dl>
									<dt>
										<font>* </font>地址
									</dt>
								</dl>
								<div class="main_detail">
									<dl>
										<dd>
											<input type="hidden" name="" value="" id=""> <input
												type="hidden" name="" value="" id=""> <input
												type="hidden" name="" value="" id="">
											<Field:codeTable cssClass="selectToInput"
												name="claimAntiMoneyVO.antiMoneyState" onChange=""
												nullOption="true" tableName="APP___CLM__DBUSER.T_DISTRICT"
												id="antiMoneyState"
												whereClause="code in (440000,650000,540000,120000,210000,370000,140000,320000,350000,330000,820000,420000,410000,520000,360000,150000,500000,630000,640000,310000,460000,110000,510000,430000,230000,450000,220000,130000,530000,340000,620000,610000,710000,810000)" />
											<span>省/直辖市<font style="color: #FF0000">* </font></span>
										</dd>
									</dl>
									<dl>
										<dd>
											<select class="selectToInput"
												name="claimAntiMoneyVO.antiMoneyCity" onchange="antiMoneyCityChageReportData(this)"
												id='antiMoneyCity'>
											</select> <span>市<font style="color: #FF0000">* </font></span>
										</dd>
									</dl>
									<dl>
										<dd>
											<select class="selectToInput"
												name="claimAntiMoneyVO.antiMoneyDistrict" onchange=""
												id="antiMoneyDistrict">
											</select> <span>区/县<font style="color: #FF0000">* </font></span>
										</dd>
									</dl>
									<dl>
										<dd>
											<input name="claimAntiMoneyVO.antiMoneyAddress"
												id="antiMoneyAddress" size="24"
												value="${claimAntiMoneyVO.antiMoneyAddress }"
												style="width: 150px;"> <span>乡镇/街道<font
												style="color: #FF0000">* </font></span>
										</dd>
									</dl>
								</div>
							</div>
						</form>
					</div>
					<div class="formBarButton main_bottom">
						<button class="but_blue" type="button" onclick="saveCustomer()">保存</button>
						<button class="but_gray" type="button" onclick="">退出</button>
					</div>
				</div>
			</div>
		</div>
		<!-- 显示数据列表区域 -->
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">历史修改列表
			</h1>
		</div>
		<div class="tabdivclassbr" id="registerSharePoolResult">
			<table class="list main_dbottom" width="100%" id="hirstyList">
				<thead>
					<tr>
						<th nowrap>用户姓名</th>
						<th nowrap>用户编号</th>
						<th nowrap>用户所属机构</th>
						<th nowrap>修改时间</th>
						<th nowrap>保单号</th>
						<th nowrap>客户身份</th>
						<th nowrap>修改内容</th>
					</tr>
				</thead>
				<tbody align="center">
					<s:if test="imageFlag != null">
						<tr>
							<td colspan="12">
								<div class="noRueryResult">请选择条件查询数据！</div>
							</td>
						</tr>
					</s:if>
					<s:elseif
						test="claimUserHistoryVOList == null || claimUserHistoryVOList.size()==0">
						<tr>
							<td colspan="12">
								<div class="noRueryResult">没有符合条件的查询结果！</div>
							</td>
						</tr>
					</s:elseif>
					<!-- 循环显示数据 -->
					<s:iterator value="claimUserHistoryVOList" status="st">
						<tr>
							<td><s:property value="realName" /></td>
							<td><s:property value="userName" /></td>
							<td><s:property value="unitName" /></td>
							<td><s:property value="modifTime" /></td>
							<td><s:property value="policyCode" /></td>
							<td><s:property value="clientIdentity" /></td>
							<td><s:property value="modifyContent" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
	</div>
</body>