<%@ page language="java" pageEncoding="UTF-8"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript"
	src="${ctx}/clm/pages/taskmanage/findOneUser.js"></script>
<script type="text/javascript">

	//查找接收人并带回方法
	$("#operatorDialog1", $.pdialog.getCurrent()).click(
			function() {
				var deformityType = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(1)").find("input").val();
	            var payRate = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(2)").find("input").val();
				var deformityGrade = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(2)").find("input").val();
				var injuryCode1 = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(3)").text();
				var injuryCode2 = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(5)").text();
				var payRate2 = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(4)").find("input").val();
				var icfCode = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(3)").find("input").val();
				var icfCode2 = $("#levelbody1",
						$.pdialog.getCurrent()).find(
						"input:radio:checked#select").parent()
						.parent().find("td:eq(5").find("input").val();
				if(injuryCode1 == null || injuryCode1 == ""){
					alertMsg.warn('请选择一条信息！');
					return false;
				} 	
				$.bringBack({
					deformityType : deformityType,
					payRate : payRate,
					injuryCode1 : injuryCode1,
					injuryCode2 : injuryCode2,
					deformityGrade : deformityGrade,
					icfCode :icfCode,
					icfCode2 : icfCode2,
					payRate2 : payRate2,
  				});
 				$("#deformityType").val(deformityType);
 				claimFireEvent($("#deformityType"));
				$("#deformityGrade").selectMyComBox(deformityGrade);
				claimFireEvent($("#deformityGrade"));
				$("#injuryCode1").val(injuryCode1); 
				claimFireEvent($("#injuryCode1"));
				$("#injuryCode2").val(injuryCode2);
				claimFireEvent($("#injuryCode2"));
				
			});
//确认按钮-校验是否选择信息
	$("#queryLevel", $.pdialog.getCurrent()).click(function() {
		 var addDeformityType = $("#deformityType3", $.pdialog.getCurrent()).val(); 
		 if(isNulOrEmpty(addDeformityType)){
			alertMsg.warn("请输入伤残类型!"); 
			return false;
		}
		$("#queryLevelForm", $.pdialog.getCurrent()).submit();
		
		
	});
	function manaOperatorMySelectOrgan1() {
		$("#operatorOrganMana", $.pdialog.getCurrent()).orgtreegrn(
				$.pdialog.getCurrent());
	}
	//关联查询
	function deformityType3Change(){ 
		var deformityType2 = $("#deformityType3", $.pdialog.getCurrent()).val();
		
		$.ajax({
			'url':'clm/parameter/findDeformityGradeByTypeId_CLM_claimDeformityGradeAction.action?typeId='+deformityType2,
			'type':'post',
			'datatype':'json',
			'success':function(data){
				//清空
				$("#deformityGrade3", $.pdialog.getCurrent()).empty();
				var json = eval("("+data+")");
				var html="";
				    html="<option value=''>请选择</option>";
				for(var i=0;i<json.length;i++){
					html +="<option value='" + json[i].deformityGrade +"'>" + json[i].deformityGradeName + "</option>";
				}
				 $("#deformityGrade3").loadMyComboxOptions(html,"", "dialog");
			}
		});
	}; 

</script>
<!-- 查找多个作业人员页面 -->
<form id="pagerForm" method="post"
	action="clm/register/maimLevel_CLM_tClaimDutyRegisterAction.action">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo} " />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>

<div class="" layoutH="32">
	<div class="main_heading">
		<h1>
			<img src="clm/images/tubiao.png">伤残信息查询 <b class="maim_lpask"></b>
		</h1>
	</div>
	<form id="queryLevelForm"
			action="clm/register/maimLevel_CLM_tClaimDutyRegisterAction.action"
			method="post" class="pageForm required-validate"
			onsubmit="return dialogSearch(this)" rel="pagerForm">
		<div class="pageFormInfoContent">
			<dl sizset="0">  
				<dt>
					<font class="point" color="red">* </font>伤残类型
				</dt>
				<dd>
					<Field:codeTable cssClass="combox title" id="deformityType3"
						value="${addClaimInjuryVO.deformityType}"
						name="addClaimInjuryVO.deformityType"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_TYPE"
						onChange="deformityType3Change()"
						whereClause="deformity_type in (0,1,2,3,4,5,6,7)"
						nullOption="true"></Field:codeTable>
				</dd>
			</dl>
			<dl sizset="1">
				<dt>伤残级别</dt>
			   <s:if test=" addClaimInjuryVO.deformityType != null">
				<dd sizset="1">
					<Field:codeTable cssClass="combox title" id="deformityGrade3"
						value="${addClaimInjuryVO.deformityGrade}"
						name="addClaimInjuryVO.deformityGrade"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_GRADE" whereClause="deformity_type = ${addClaimInjuryVO.deformityType}"  nullOption="true"></Field:codeTable>
				</dd>
				</s:if>
				<s:else>
				<dd sizset="1">
					<Field:codeTable cssClass="combox title" id="deformityGrade3"
						value="${addClaimInjuryVO.deformityGrade}"
						name="addClaimInjuryVO.deformityGrade"
						tableName="APP___CLM__DBUSER.T_DEFORMITY_GRADE" nullOption="true"></Field:codeTable>
				</dd>
				</s:else>
			</dl>
			<dl sizset="2">
				<dt>伤残代码1</dt>
				<dd>
                   <input type="text" name="addClaimInjuryVO.injuryCode1" id="injuryCode1"
						value="${addClaimInjuryVO.injuryCode1}">
				
				</dd>
			</dl>
			
			<dl sizset="">
				<dt>伤残代码1名称</dt>
				<dd>
				<input type="text" name="addClaimInjuryVO.injuryCode1Name" id="injuryCode1Name"
						value="${addClaimInjuryVO.injuryCode1Name}">
				</dd>
			</dl>
			
			<dl sizset="2">
				<dt>伤残代码2</dt>
				<dd>
				<input type="text" name="addClaimInjuryVO.injuryCode2" id="injuryCode2"
						value="${addClaimInjuryVO.injuryCode2}">
				</dd>
			</dl>
			
			<dl sizset="2">
				<dt>伤残代码2名称</dt>
				<dd>
				<input type="text" name="addClaimInjuryVO.injuryCode2Name" id="injuryCode2Name"
						value="${addClaimInjuryVO.injuryCode2Name}">
				</dd>
			</dl>
			
			<div class="pageFormdiv">
				<button type="button" id="queryLevel" class="but_blue">查询</button>
			</div>
		</div>
	</form>

	<div class="tabdivclassbr">
		<table id="" class="list main_dbottom" style="width: 100%;">
			<thead>
				<tr align="center">
				    <th nowrap>选择</th>
					<th nowrap>伤残类型</th>
					<th nowrap>伤残级别</th>
					<th nowrap>伤残代码1</th>
					<th nowrap>伤残代码1名称</th>
					<th nowrap>伤残代码2</th>
					<th nowrap>伤残代码2名称</th>
				</tr>
			</thead>
			<tbody id="levelbody1">
				<s:if test="imageFlag eq 1">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</s:if>
				<s:elseif
					test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">没有符合条件的查询结果！</div>
						</td>
					</tr>
				</s:elseif>
				<s:iterator value="currentPage.pageItems" status="st">
					<tr align="center">
					    <td align="center"><input type="radio" name="selectResult"
							id="select";"></td>
						<td>${deformityTypeName }<input type="hidden" value="${deformityType}"></td>
						<td>${deformityGradeName }<input type="hidden" value="${deformityGrade}"></td>
						<td>${injuryCode1 }<input type="hidden" value="${icfCode}"></td>
						<td>${injuryCode1Name }</td>
						<td>${injuryCode2 }<input type="hidden" value="${icfCode2}"></td>
						<td>${injuryCode2Name }</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>


		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="dialogPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="dialog"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
	<div class="pageFormdiv  main_popup">
		<button type="button" id="operatorDialog1" class="but_blue">确认</button>
		<button type="button" id="exit" class="but_gray close">关闭</button>
	</div>
</div>


