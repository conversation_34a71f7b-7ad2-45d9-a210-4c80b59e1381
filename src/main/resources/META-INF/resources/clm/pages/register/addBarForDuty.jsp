<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%> 
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
 <script type="text/javascript">
 
	 
	function pageAddNew(obj) {
	//	alert("sss");
	    var caseId = "${caseId}";
		var pageFlag=$(obj).parent().parent().attr("titles");
		var  billType=$(obj).parent().parent().attr("billType");
	//	alert(pageFlag+'+++'+billType);
	    //取消门诊/住院 列表单选按钮
	    if(pageFlag=="registerHonsp" && $("#registerHospitalTBody", navTab.getCurrentPanel()).find("tr").length > 0){
	    	$("#registerHospitalTBody", navTab.getCurrentPanel()).find("tr").each(function(){
	    		$(this).find("td:eq(0)").find("input:eq(0)").attr("checked", false);
	    	});
	    }
		var url ='clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='+pageFlag+'&claimBillItemVO.caseId='+caseId+'&caseId='
				+caseId+'&claimBillVO.billType='+billType;
	   // url ='clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?claimBillItemVO.billId=&claimBillItemVO.caseId='+caseId+'&flag=';

		$(obj).parent().next().loadUrl(url);

		$(obj).parent().next().show("30000");
		return;
	}
	

// 确认函数，连续弹出多个确认框
function confirmSequence(messages,pageFlag) {
    // 递归结束条件：所有消息都已经展示过了
    if (messages.length == 0) {
		$("#errorFlag", navTab.getCurrentPanel()).val("true");  
		$("#"+pageFlag+"FormId", navTab.getCurrentPanel()).submit();
        return true; // 假设所有确认框都被接受
    }
 
    // 取出消息队列的第一个消息
    var message = messages.shift();
 
    // 显示下一个确认框
     alertMsg.confirm(message,{
	 	okCall:function(){
	 		 return confirmSequence(messages,pageFlag);
 	},cancelCall:function(){
 		return false;
 	}}
);
}
	
	function saveDutyDetail(obj) {
		var pageFlag=$(obj).parent().parent().attr("titles"); 
	 
		var isok=true;
		var isoks=true;
		var bedFeeConfirmFlag = false;
		var messages=[];
		if(pageFlag=="registerHonsp"||pageFlag=="highMedical"||pageFlag=="cancerMedical"){
			isok=  billValidate();  
		}
		bedFeeConfirmFlag = bedFeeConfirm(messages);
		var confirmFlag = false;
		var CARCellConfirmFlag = false;
		if(pageFlag=="CARCellMedical"){
			var caseId = "${caseId}";
			isok=  pageFlagbillValidate(pageFlag);  
			var $obj=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel());
			var preMimingInspectTime = $obj.find("#preMimingInspectTime").val();
			var carCellTransTime = $obj.find("#carCellTransTime").val();
			var treatStart = $obj.find("#treatStart").val();
			var treatEnd = $obj.find("#treatEnd").val();
			$.ajax({
	  			'type':'post',
	  			'url':'clm/register/checkCARDate_CLM_tClaimDutyRegisterAction.action',
	  			'datatype':'json',
	  			'data':{'claimBillVO.caseId':caseId,
	  				'claimBillVO.preMimingInspectTime':preMimingInspectTime,
	  				'claimBillVO.carCellTransTime':carCellTransTime,
	  				'claimBillVO.treatStart':treatStart, 
	  				'claimBillVO.treatEnd':treatEnd}, 
	  			'async':false,
	  			'success':function(data){
	  				var data = eval("(" + data + ")");
 	  				for(var kay in data){
 	  					//如果kay为1则阻断，2不阻断,选择提示
 	  					if (kay=="1") {
 	  						alertMsg.error(data[kay]);
 	  						CARCellConfirmFlag = true;
 	  						return false;
 						}
 	  					if (kay=="2") {
 	  						confirmFlag  = true;
 	  						messages.push("账单费用超出治疗期，请检查确认！");
 	  					}
 	  					if (kay=="data") {
 	  						alertMsg.error(data[kay]);
 	  						CARCellConfirmFlag = true;
 	  						return false;
 	  					}
 	  				}
	  			},
	  			'error':function(){
	  				alert("出错了！");
	  			}
	  		});
		}
		if(CARCellConfirmFlag){
			return false;
		}


		



		if(pageFlag=="registerOpsLevel"){
			isoks = opsLevel();
		}
		if(pageFlag=="registerGreat"){
			var a = $("#specialCode").val();
			var isNineFive = $("#isNineFive").val();
			if(isNineFive == 1){//950产品提示信息
				if($("#specialCode").val()=="FT012"){
					messages.push("如客户确诊的癌症为以下原发癌：肺癌、肝癌、骨癌、脑癌、胰腺癌时，请同时在特种疾病下选择相应代码，可以同时匹配特定恶性肿瘤确诊保险金?");
				}
			}
		}

		if (isok&&isoks){
			if(messages.length>0){
				confirmSequence(messages,pageFlag);
				return false;
			}else{
				$("#errorFlag", navTab.getCurrentPanel()).val("true");  
				$("#"+pageFlag+"FormId", navTab.getCurrentPanel()).submit();
				return false;
			}
		}else{
			$("#errorFlag", navTab.getCurrentPanel()).val("false");
			return false;
		}
		
		if(isok&&isoks){
			$("#errorFlag", navTab.getCurrentPanel()).val("true");  
			$("#"+pageFlag+"FormId", navTab.getCurrentPanel()).submit();
		}else{
			$("#errorFlag", navTab.getCurrentPanel()).val("false");  
		}
	}
	
function opsLevel(){
	var isok = true;
	if($("#opsLevelDate", navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("手术日期必填");
		$("#alertMsgBox .toolBar .button").die().live("click",function(){
			$("#opsLevelDate", navTab.getCurrentPanel()).focus();
		});
		isok=false;
		return false;
	}
	if($("#hospitalCodeId", navTab.getCurrentPanel()).val() == ""){
		alertMsg.error("医疗机构必填");
		$("#alertMsgBox .toolBar .button").die().live("click",function(){
			$("#hospitalCodeId", navTab.getCurrentPanel()).focus();
		});
		isok=false;
		return false;
	}
	return isok;
}
	
  function  billValidate(){
	  var  isok=true;
	  var $obj=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel());
	  if($obj == null){
		  return false;
	  }
	    if ($obj.find("#billNo").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#billNo").focus();
			});
			isok=false;
			return false;
		}
	    if ($obj.find("#hospitalId").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#hospitalId").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#cureType").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#cureType").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#treatStart").val() == "") {
			alertMsg.error("请录入必录项"); 
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#treatStart").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#treatEnd").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#treatEnd").focus();
			}); 
			isok=false;
			return false;
		}
		 ///医疗单证录入验证
		 //总费用
		var sumFee = 0.00;
		//扣除费用
		var sumDeduct = 0.00;
		//理算金额
		var calcFee = 0.00;
		//--------保存费用明细验证金额输入是否正确-------
		$("#feeItemId", navTab.getCurrentPanel()).find("tr").each(function(i,object) {
			if ($(this).find("#medFeeItem").val() == "") {
				alertMsg.error("费用明细项目必填！");
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#medFeeItem").focus();
				}); 
				isok=false;
				return false;
			}else{
				var feeFlag = false;
				if($(this).find("#medFeeItem").val()=='CC007'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC013'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC014'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC015'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC016'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}        
				if($(this).find("#medFeeItem").val()=='CC017'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}        
				if($(this).find("#medFeeItem").val()=='CC018'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				} 
				if (feeFlag) {
					alertMsg.error("手术代码必填！");
					$("#alertMsgBox .toolBar .button").die().live("click",function(){
						$(object).find("#operationNameId").focus();
					}); 
					isok=false;
					return false;
				}
				
			}
			if ($(this).find("#feeAmount").val() == "") {
				alertMsg.error("费用金额必填！");
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#feeAmount").focus();
				}); 
				isok=false;
				return false;
			}
			
			if ($(this).find("#expenseAmount").val() > 0) {
				if ($(this).find("#expenseRemark").val() == "") {
						alertMsg.error("自费备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#expenseRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if ($(this).find("#payAmount").val() > 0) {
				/* if ($(this).find("#deductReason").val() == "") {
				alertMsg.error("扣除原因必填！"); 
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#deductReason").focus();
				});
				isok=false;
				return false;
			    } */
				if ($(this).find("#payRemark").val() == "") {
						alertMsg.error("自付备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#payRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if ($(this).find("#otherAmount").val() > 0) {
				if ($(this).find("#otherRemark").val() == "") {
						alertMsg.error("其他备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#otherRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if($(this).find("#feeAmount").val() > 0){
				sumFee =  sumFee + parseFloat($(this).find("#feeAmount").val()) ;
			} 
			if($(this).find("#expenseAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#expenseAmount").val());
			} 
			if($(this).find("#payAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#payAmount").val());
			} 
			if($(this).find("#otherAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#otherAmount").val());
			} 
			
			//如果存在扣除费用则
			if($(this).find("#deductAmount").val() > 0){
				sumDeduct =   parseFloat($(this).find("#deductAmount").val());
			}  
			if($(this).find("#calcAmount").val() > 0){
				calcFee =  calcFee
				+ parseFloat($(this).find("#calcAmount").val()); 
			} 
		  }); 
		 
		   $("#sumAmountAll", navTab.getCurrentPanel()).val(sumFee);
		   $("#deductAmountAll", navTab.getCurrentPanel()).val(sumDeduct);
		   $("#calcAmountAll", navTab.getCurrentPanel()).val(calcFee);
			if(isNaN($("#deductAmountAll", navTab.getCurrentPanel()).val())){
				$("#deductAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if(isNaN($("#sumAmountAll", navTab.getCurrentPanel()).val())){
				$("#sumAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if(isNaN($("#calcAmountAll", navTab.getCurrentPanel()).val())){
				$("#calcAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if($("#calcAmountAll", navTab.getCurrentPanel()).val()){
				var calcAmountAll = $("#sumAmountAll", navTab.getCurrentPanel()).val() -  $("#deductAmountAll", navTab.getCurrentPanel()).val();
				$("#calcAmountAll", navTab.getCurrentPanel()).val(calcAmountAll);
			}
		 return isok;
		
	}
  function bedFeeConfirm(messages){
	
 	var  bedFeeConfirmFlag=false;
 	  var $obj=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel());
	  if($obj == null){
		  return false;
	 }
		$("#feeItemId", navTab.getCurrentPanel()).find("tr").each(function(i,object) {
		var feeAmount= $(this).find("#feeAmount").val();
			 //获取开始日期
		     var startTime = $("#treatStart", navTab.getCurrentPanel()).val().replace(/-/g, '-');
			 //获取结束日期
			 var endTime=$("#treatEnd", navTab.getCurrentPanel()).val().replace(/-/g, '-');
			 var startTimes = startTime.split("-");
			 var dt3 = new Date(startTimes[0],startTimes[1] - 1,startTimes[2]); 
			 var endTimes = endTime.split("-");
			 var dt4 = new Date(endTimes[0],endTimes[1] - 1,endTimes[2]);
		  	 var dif = dt4.getTime() - dt3.getTime();
			 var days = Math.round(dif / (24 * 60 * 60 * 1000));
			 if(days==0){
				 days=1;
			 } 
	      var speBusiProd;
	      var caseId = "${caseId}";
	      $.ajax({
	      	'type':'post',
	      	'url':'clm/register/checkSpeBusiProd_CLM_tClaimDutyRegisterAction.action',
	      	'datatype':'json',
	      	'data':{'claimBillVO.caseId':caseId}, 
	      	'async':false,
	      	'success':function(data){
	      		var data = eval("(" + data + ")");
	      			for(var kay in data){
	      				//如果kay为1为返回对应的险种
	      				if (kay=="1") {
	      					speBusiProd=data[kay];
	      				}
	      			}
	      	},
	      	'error':function(){
	      		alert("出错了！");
	      		return false;
	      	}
	      });
		if(speBusiProd=="A04"){
		 	 if($(this).find("#feeAmount").val() /days   > "800" && $(this).find("#treatType").val()!=0 &&($(this).find("#medFeeItem").val()=='CO004')){
					bedFeeConfirmFlag = true;
					messages.push("陪床费限800元/日，请注意。");
				    return true;
				}
		}else{
		 	 if($(this).find("#feeAmount").val() /days   > "1500" && $(this).find("#treatType").val()!=0 &&($(this).find("#medFeeItem").val()=='CR001' ||$(this).find("#medFeeItem").val()=='CR002' ||$(this).find("#medFeeItem").val()=='CR003' )){
					bedFeeConfirmFlag = true;
					messages.push("床位费录入金额大于1500元/天，请核实！");
				    return true;
				}
			}
	 	}); 
	return bedFeeConfirmFlag;
 }
	
 
  function  pageFlagbillValidate(pageflag){
	  var  isok=true;
	  var $obj=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel());
	  if($obj == null){
		  return false;
	  }
	    if ($obj.find("#billNo").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#billNo").focus();
			});
			isok=false;
			return false;
		}
	    if ($obj.find("#hospitalId").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#hospitalId").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#cureType").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#cureType").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#treatStart").val() == "") {
			alertMsg.error("请录入必录项"); 
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#treatStart").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#treatEnd").val() == "") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#treatEnd").focus();
			}); 
			isok=false;
			return false;
		}
	    if ($obj.find("#preMimingInspectTime").val() == "" && pageflag == "CARCellMedical") {
			alertMsg.error("请录入必录项");
			$("#alertMsgBox .toolBar .button").die().live("click",function(){
				$obj.find("#preMimingInspectTime").focus();
			}); 
			isok=false;
			return false;
		}
		 ///医疗单证录入验证
		 //总费用
		var sumFee = 0.00;
		//扣除费用
		var sumDeduct = 0.00;
		//理算金额
		var calcFee = 0.00;
		//--------保存费用明细验证金额输入是否正确-------
		$("#feeItemId", navTab.getCurrentPanel()).find("tr").each(function(i,object) {
			if ($(this).find("#medFeeItem").val() == "") {
				alertMsg.error("费用明细项目必填！");
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#medFeeItem").focus();
				}); 
				isok=false;
				return false;
			}else{
				var feeFlag = false;
				if($(this).find("#medFeeItem").val()=='CC007'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC013'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC014'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC015'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}
				if($(this).find("#medFeeItem").val()=='CC016'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}        
				if($(this).find("#medFeeItem").val()=='CC017'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				}        
				if($(this).find("#medFeeItem").val()=='CC018'&&$(this).find("#operationCodeId").val()==""){
					feeFlag = true;
				} 
				if (feeFlag) {
					alertMsg.error("手术代码必填！");
					$("#alertMsgBox .toolBar .button").die().live("click",function(){
						$(object).find("#operationNameId").focus();
					}); 
					isok=false;
					return false;
				}
				
			}
			if ($(this).find("#feeAmount").val() == "") {
				alertMsg.error("费用金额必填！");
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#feeAmount").focus();
				}); 
				isok=false;
				return false;
			}
			
			if ($(this).find("#expenseAmount").val() > 0) {
				if ($(this).find("#expenseRemark").val() == "") {
						alertMsg.error("自费备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#expenseRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if ($(this).find("#payAmount").val() > 0) {
				/* if ($(this).find("#deductReason").val() == "") {
				alertMsg.error("扣除原因必填！"); 
				$("#alertMsgBox .toolBar .button").die().live("click",function(){
					$(object).find("#deductReason").focus();
				});
				isok=false;
				return false;
			    } */
				if ($(this).find("#payRemark").val() == "") {
						alertMsg.error("自付备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#payRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if ($(this).find("#otherAmount").val() > 0) {
				if ($(this).find("#otherRemark").val() == "") {
						alertMsg.error("其他备注必填！");
						$("#alertMsgBox .toolBar .button").die().live("click",function(){ 
							$(object).find("#otherRemark").focus();
						});
						isok=false;
						return false;
				}
			} 
			if($(this).find("#feeAmount").val() > 0){
				sumFee =  sumFee + parseFloat($(this).find("#feeAmount").val()) ;
			} 
			if($(this).find("#expenseAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#expenseAmount").val());
			} 
			if($(this).find("#payAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#payAmount").val());
			} 
			if($(this).find("#otherAmount").val() > 0){
				sumDeduct =   sumDeduct
				+ parseFloat($(this).find("#otherAmount").val());
			} 
			
			//如果存在扣除费用则
			if($(this).find("#deductAmount").val() > 0){
				sumDeduct =   parseFloat($(this).find("#deductAmount").val());
			}  
			if($(this).find("#calcAmount").val() > 0){
				calcFee =  calcFee
				+ parseFloat($(this).find("#calcAmount").val()); 
			} 
		  }); 
		 
		   $("#sumAmountAll", navTab.getCurrentPanel()).val(sumFee);
		   $("#deductAmountAll", navTab.getCurrentPanel()).val(sumDeduct);
		   $("#calcAmountAll", navTab.getCurrentPanel()).val(calcFee);
			if(isNaN($("#deductAmountAll", navTab.getCurrentPanel()).val())){
				$("#deductAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if(isNaN($("#sumAmountAll", navTab.getCurrentPanel()).val())){
				$("#sumAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if(isNaN($("#calcAmountAll", navTab.getCurrentPanel()).val())){
				$("#calcAmountAll", navTab.getCurrentPanel()).val(0);
			}
			if($("#calcAmountAll", navTab.getCurrentPanel()).val()){
				var calcAmountAll = $("#sumAmountAll", navTab.getCurrentPanel()).val() -  $("#deductAmountAll", navTab.getCurrentPanel()).val();
				$("#calcAmountAll", navTab.getCurrentPanel()).val(calcAmountAll);
			}
		 return isok;
		
	}
	 
  if($("#auditClaimCheckListInit", navTab.getCurrentPanel()).val()=="auditClaimCheckListInit"){
 
	$("#AddNewButton", navTab.getCurrentPanel()).remove();
	$("#SaveNewButton", navTab.getCurrentPanel()).remove();
	 $(".btnDel", navTab.getCurrentPanel()).attr("disabled","disabled");
 }
 
  if(document.readyState=="complete"){  
		var currentPageId= <%=session.getAttribute("currentPageId") %>;  
		 if((currentPageId!="" && currentPageId>3)||readOnly==1 ){
			  //置灰删除按钮 
			 $(".btnDel", navTab.getCurrentPanel()).each(function(index,objct){
				 $(objct).attr("disabled",true);
		  }); 
			
		}
  } 
 
	//控制电票入账明细按钮
	var billEntryFlag =  $("#billEntryFlag", navTab.getCurrentPanel()).val();
	if(billEntryFlag == 1){
		$("#ElecBillButton", navTab.getCurrentPanel()).show();
	}else{
		$("#ElecBillButton", navTab.getCurrentPanel()).hide();
	}
  
 </script>
<div id="pageFormdivID" class="pageFormdiv main_tabdiv" > 

	         <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""} class="button" onclick="pageAddNew(this)"  id="AddNewButton"  href="javascript:void(0);"  ><span>添加</span></a>
             <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""}  class="button" onclick="saveDutyDetail(this)" id="SaveNewButton" href="javascript:void(0);" ><span>保存</span></a>
             <a class="but_blue main_buta" ${currentPageId>3?"disabled='disabled'":""} ${readOnly==1?"disabled='disabled'":""}  class="button"  id="ElecBillButton" href="clm/register/findBillItemEntry_CLM_tClaimDutyRegisterAction.action?caseId=${caseId}&caseNo=${claimCaseVO.caseNo}" style="display: none;" target="dialog" rel="page2" ><span>电票入账明细</span></a>		 
</div> 	
<div id='hospitalcost' style='display: none'></div>
