/*function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
}*/

function queryEpibolyMonitoringOn(){
	var organName = $("#menuBtn", navTab.getCurrentPanel()).val();
	var outsourceName = $("#outsourceName_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	var sendPackageTime = $("#sendPackageTime_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	var receivedPackageTime = $("#receivedPackageTime_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	var outsourceTypeCode = $("#outsourceTypeCode_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	var sendPackageStartTimeH = $("#sendPackageStartTimeH", navTab.getCurrentPanel()).val();
	var receivedPackageTimeH = $("#receivedPackageTimeH", navTab.getCurrentPanel()).val();
	if(outsourceTypeCode == "all"){
		$("#outsourceTypeCode_epibolyMonitoringInitHident", navTab.getCurrentPanel()).val("");
		outsourceTypeCode = "";
	} else {
		$("#outsourceTypeCode_epibolyMonitoringInitHident", navTab.getCurrentPanel()).val(outsourceTypeCode);
	}
	if(isNorE(sendPackageTime) || isNorE(receivedPackageTime)){
		alertMsg.error("请至少录入发包起止日期条件!");
		return ;
	}
//	$.ajax({
//		url : "clm/register/epibolyMonitorIngInitFlag_CLM_epibolyMonitoringAction.action",
//		global : false,
//		data :{"outsourceIfnoVO.organCode":organName ,
//			"outsourceIfnoVO.outsourceName":outsourceName,
//			"outsourceCaseVO.sendPackageTime":sendPackageTime,
//			"outsourceCaseVO.receivedPackageTime":receivedPackageTime,
//			"outsourceCaseVO.outsourceTypeCode":outsourceTypeCode,
//			"outsourceCaseVO.sendPackageStartTimeH":sendPackageStartTimeH,
//			"outsourceCaseVO.receivedPackageTimeH":receivedPackageTimeH},
//		type : "POST",
//		dataType : "json",
//		success : function(json) {
//			if(json.warnFlag == "1"){
//				alertMsg.error("查询结果不存在，请重新输入查询条件。");
//			} else {
//				$("#queryOutsourceCaseId", navTab.getCurrentPanel()).submit();
//			}
//		}
//	});
	$("#queryOutsourceCaseId", navTab.getCurrentPanel()).submit();
}
//查询
function queryOutsourceCase() {
	$("#queryOutsourceCaseId", navTab.getCurrentPanel()).submit();
}
//外包费用的查询
function queryOutsourceCaseIdMoney(){
	var mounthId = $("#mounthId", navTab.getCurrentPanel()).val();
	var yearId = $("#yearId", navTab.getCurrentPanel()).val();
	if(isNulOrEmpty(yearId)) {
		alertMsg.warn("统计年份不能为空。");
		return false;
	} else if(isNulOrEmpty(yearId)) {
		alertMsg.warn("统计月份不能为空。");
		return false;
	} else {
		$("#queryOutsourceCaseIdMoney", navTab.getCurrentPanel()).submit();
	}
}

//$("#queryOutsourceCase_epibolyMonitoringInit",navTab.getCurrentPanel()).click(function() {
	// 取发包起止日期
	/*var sendPackageTime = $("#sendPackageTime_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	var receivedPackageTime = $("#receivedPackageTime_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	if(isNulOrEmpty(sendPackageTime)) {
			alertMsg.warn("发包起期不能为空。");
			return false;
	}
	if(isNulOrEmpty(receivedPackageTime)) {
		alertMsg.warn("发包止期不能为空。");
		return false;
	}*/
//});

//$("#queryOutsourceCaseAndFee_epibolyMonitoringInit",navTab.getCurrentPanel()).click(function() {
//	// 取统计时间
//	var countTime = $("#sendPackageTime1_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
//	if(isNulOrEmpty(countTime)) {
//			alertMsg.warn("统计时间不能为空。");
//			return false;
//	} else {
//		// 取当前时间
//		var date = new Date();
//		var year = date.getFullYear();
//		var month = date.getMonth() + 1;
//		var flag = false;
//		if (countTime.split("-")[0] > year) {
//			flag = true;
//		} else if (countTime.split("-")[0] == year) {
//			if (countTime.split("-")[1] > month) {
//				flag = true;
//			}
//		}
//		if (flag) {
//			alertMsg.warn("统计时间不能大于今天。");
//			return false;
//		}
//	}
	
	/*// 取管理机构
	var organCode = $("#menuBtn1",navTab.getCurrentPanel()).val();*/
	//$("#queryOutsourceCaseId11111",navTab.getCurrentPanel()).submit();
/*	$.ajax({
		url : 'clm/register/queryLineChartInfoAndFee_CLM_epibolyMonitoringAction.action?outsourceCaseVO.organCode='
				+ organCode 
				+ '&outsourceCaseVO.countTime='
				+ countTime,
		type : 'post',
		global : false,
		dataType : 'html',
		success : function(data) {
			alert(data);
			$("#qwee", navTab.getCurrentPanel()).append(data);
		}
	});*/
	
//});

function checktime(obj) {
	var time = /^(0\d{1}|1\d{1}|2[0-3]):([0-5]\d{1})$/;
	if(trim($(obj).val())!=""){
		if (!time.test($(obj).val())) {
			alert("时间格式不正确，请重新输入");
			$(obj).val("");
		}
	}
}
function showchart(obj) {
	$("#main", navTab.getCurrentPanel()).attr("style", "height:400px; border: 0px;");
	$("[name=jiange]", navTab.getCurrentPanel()).eq(0).attr("checked", "checked");
	change($("[name=jiange]", navTab.getCurrentPanel()).eq(0));
}
function change(obj) {
	var intervalType = null;
	var data = null;
	// 取发包起止日期
	var sendPackageTime = $("#sendPackageTime_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	var receivedPackageTime = $("#receivedPackageTime_epibolyMonitoringInit", navTab.getCurrentPanel()).val();
	
	if(isNulOrEmpty(sendPackageTime)) {
		alertMsg.warn("发包起期不能为空。");
		return false;
	}
	if(isNulOrEmpty(receivedPackageTime)) {
		alertMsg.warn("发包止期不能为空。");
		return false;
	}
	
	var oDate1 = sendPackageTime.split("-");
	var oDate2 = receivedPackageTime.split("-");
	var strDateSend = new Date(oDate1[0], oDate1[1] - 1, oDate1[2]);
	var strDateReceived = new Date(oDate2[0], oDate2[1] - 1, oDate2[2]);
	var number = parseInt(Math.abs(strDateReceived-strDateSend))/1000/60/60/24; // 取两个时间相差天数
	var time = new Array();
	
	if ($(obj).val() == "按天") {
		intervalType = 1;
		for (var i = 0; i < number + 1; i++) {
			var date = new Date((strDateSend/1000+86400*i)*1000); // 时间加一天
			time[i] = parseInt(date.getMonth()) + 1 + "/" + date.getDate() + "/" + date.getFullYear();
		}
		
		data = [ {
			type : 'category',
			boundaryGap : true,
			data : time 
		} ];
	}
	if ($(obj).val() == "按月") {
		intervalType = 2;
		number = parseInt(oDate2[0])*12 + parseInt(oDate2[1]) - parseInt(oDate1[0])*12 - parseInt(oDate1[1]); // 取两个时间相差月数
		for (var i = 0; i < number + 1; i++) {
			if (i != 0) {
				strDateSend.setMonth(strDateSend.getMonth() + 1);
			}
			time[i] = parseInt(strDateSend.getMonth()) + 1 + "/" + strDateSend.getFullYear();
		}
		data = [ {
			type : 'category',
			boundaryGap : true,
			data : time
		} ];
	}
	if ($(obj).val() == "按周") {
		intervalType = 3;
		var j = -1;
		for (var i = 0; i < number + 1;) {
			j++;
			
			var m = null;
			if (i == 0) {
				var week = strDateSend.getDay();  
				if (week == 0) {  
				    m = 0;  
				} else if (week == 1) {  
			        m = 6;  
				} else if (week == 2) {  
				    m = 5;  
				} else if (week == 3) {  
					m = 4;
				} else if (week == 4) {  
					m = 3;
				} else if (week == 5) {  
					m = 2;
				} else if (week == 6) {  
					m = 1; 
				}  
			} else {
				m = 6;
			}
			if (parseInt(i + m) >= parseInt(number + 1)) {
				m = parseInt(number - i );
			}
			var string = parseInt(strDateSend.getMonth()) + 1 + "/" + strDateSend.getDate();
			strDateSend.setDate(strDateSend.getDate() + m);
			string += "-" + parseInt(strDateSend.getMonth() + 1) + "/" + strDateSend.getDate();
			time[j] = string;
			strDateSend.setDate(strDateSend.getDate() + 1);
			
			i += m + 1;
		}
		
		data = [ {
			type : 'category',
			boundaryGap : true,
			data : time
		} ];
	}
	/*if ($("input:radio:checked", navTab.getCurrentPanel()).size()==1) {
		return false;
	}
	// 获取外包商id
	var outsourceId = $("input:radio:checked", navTab.getCurrentPanel()).parent().parent().find("td:eq(13)").text();
	// 获取外包案件类型
	var outsourceTypeCode = $("input:radio:checked", navTab.getCurrentPanel()).parent().parent().find("td:eq(3)").text();*/
	
	// 获取外包商id
	var outsourceId = $("[name='outsourceIfnoVO.outsourceName1']", navTab.getCurrentPanel()).val();
	if (!outsourceId) {
		alertMsg.warn("请选择一个外包商。");
		return false;
	}
	
	var succeedNumberArray = new Array();
	var waitManageNumberArray = new Array();
	var imageNumberArray = new Array();
	var elseNumberArray = new Array();
	$.ajax({
		url : 'clm/register/queryLineChartInfo_CLM_epibolyMonitoringAction.action?outsourceCaseVO.outsourceId='
				+ outsourceId 
				+ '&outsourceCaseVO.sendPackageTime='
				+ sendPackageTime
				+ '&outsourceCaseVO.receivedPackageTime='
				+ receivedPackageTime
				+ '&outsourceCaseVO.intervalType='
				+ intervalType,
		type : 'post',
		global : false,
		dataType : 'json',
		success : function(json){
			for (var i = 0; i < json.length; i++) {
				succeedNumberArray[i] = json[i].succeedNumber;
				waitManageNumberArray[i] = json[i].waitManageNumber;
				imageNumberArray[i] = json[i].imageNumber;
				elseNumberArray[i] = json[i].elseNumber;
			}
			
			// 折线图
			require.config({
				paths : {
					echarts : 'clm/pages/register/dist'
				}
			});
			require([ 'echarts', 'echarts/chart/line' ], function(ec) {
				var myChart = ec.init(document.getElementById('main'));
				var option = {

					title : {
						text : '图表1-发包案件数',
						y : 'bottom',
						x : 'center'

					},
					tooltip : {
						trigger : 'item'
					},
					legend : {
						x : 'right',
						y : 'center',
						data : [ '成功件数', '', '待处理件数', '', '影像问题件数', '', '其他问题件数']
					},
					/*
					 * xAxis : [ { type : 'category', boundaryGap : true } ],
					 */
					yAxis : [ {
						type : 'value',
						axisLabel : {
							formatter : '{value}'
						}
					} ],
					series : [ {
						name : '成功件数',
						type : 'line',
						data : succeedNumberArray,
						markPoint : {
							data : [
							// {type : 'max', name: '最大值'},
							// {type : 'min', name: '最小值'}
							]
						},
						markLine : {
							data : [
							// {type : 'average', name: '平均值'}
							]
						}
					}, {
						name : '待处理件数',
						type : 'line',
						data : waitManageNumberArray,
						markPoint : {
							data : [
							// {name : '周最低', value : -2, xAxis: 1, yAxis: -1.5}
							]
						},
						markLine : {
							data : [
							// {type : 'average', name : '平均值'}
							]
						}
					}, {
						name : '影像问题件数',
						type : 'line',
						data : imageNumberArray,
						markPoint : {
							data : [
							// {name : '周最低', value : -2, xAxis: 1, yAxis: -1.5}
							]
						},
						markLine : {
							data : [
							// {type : 'average', name : '平均值'}
							]
						}
					}, {
						name : '其他问题件数',
						type : 'line',
						data : elseNumberArray,
						markPoint : {
							data : [
							// {name : '周最低', value : -2, xAxis: 1, yAxis: -1.5}
							]
						},
						markLine : {
							data : [
							// {type : 'average', name : '平均值'}
							]
						}
					} ]
				};
				option.xAxis = data;
				myChart.setOption(option);
			});
			
		}
	});
	
}
