var bs = 0;

$(function(){
		$(".btnDel", navTab.getCurrentPanel()).click(function(){
			var $tr=$(this).parent().parent();
			$tr.remove();
		});
	});

function registerInvoicesScan(){
	$.ajax({
		'type':'post',
		'url':'clm/register/invoicesScan_CLM_registerCheckConfirmAction.action',
		'data':$("#registerCheckconfirmForm",navTab.getCurrentPanel()).serialize(),
		'datatype':'json',
		'success':function(data){
			var data = eval("(" + data + ")");
			if(data.statusCode==300){
				alertMsg.error(data.message);
			}
			if(data.statusCode==200){
				window.open(data.message);
			}
		//	$("#checklistScan", navTab.getCurrentPanel()).attr("href",data);
//			$("#checklistScan", navTab.getCurrentPanel()).click();
		},
		'error':function(){
			alertMsg.error("系统出险异常，请联系管理员");
		}
	});  
}
function saveCheck(flg){ 
	if(bs == 1 && flg == 1){
		next('13','confirmForm',$("[name='caseId']", navTab.getCurrentPanel()).val());
		return;
	} else if(bs == 1 && flg == 2){
		prev('11',$("[name='caseId']", navTab.getCurrentPanel()).val());
		return;
	}
	var trs = $("#registerPage" ,navTab.getCurrentPanel()).find("tr");
	for(var i = 0; i < trs.length; i++){
		if($("#registerPage",navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").find("select").val() == "-1"){
			alertMsg.warn("请填写单证编码");
			return;
		}
		if(flg != 2){
			if($("#registerPage",navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(3)").find("input[type='text']").val() == ""){
				alertMsg.warn("页数为必填项请录入");
				$("#registerPage",navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(3)").find("input[type='text']").focus();
				return;
			}
		}
	}
	$('input[flag="flag"]', navTab.getCurrentPanel()).each(function(){
		if($(this).prop('checked') != false){
			$(this).val(1);
		}
	});
	var flag=false;
	//获取所有被选中的复选框
	var focusFlag = 0;//光标位置标记
	for(var i = 0; i < trs.length; i++){
		if(flg != 2){
			var registerChecklistOption = $("#registerPage" ,navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(5)").find("input:radio:checked").val();
			var registerLackReason=$("#registerPage" ,navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(6)").find("input").val();
			if(registerChecklistOption=="2"){
				if($.trim(registerLackReason)==""){
					alertMsg.error("请录入必录项");
					flag=true;
					$("#alertMsgBox .toolBar .button").off().on("click",function(){
						$("#registerPage", navTab.getCurrentPanel()).find("tr").eq(i).find("td").eq(6).find("input").focus();
					});
					return false;
				}
			}
		}
		focusFlag = focusFlag + 1;
	}
	if(flag){
		return;
	}
	$("#registerCheckconfirmForm",navTab.getCurrentPanel()).attr("action","clm/register/saveCheck_CLM_registerCheckConfirmAction.action?flg="+flg);
	
	
	$('#registerCheckconfirmForm', navTab.getCurrentPanel()).submit();
	if(flg == 2){
		presave = 1;
	}
	
}
//点击退出
/*function closeRegisterCheckConfirm(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
}*/
//打印按钮
function registerPrintDocuments(caseId){
	$.post("clm/sign/printDocuments_CLM_signCheckConfirmAction.action",{caseId:caseId},function(data){
		var html="";
		for(var i = 0;i<data.length;i++){
			if(data[i].jobId !="" && data[i].error!=""){
				
			}else{
				html+="<object  type=\"application/x-java-applet\" >"+
				"<param name=\"code\" value=\"SipRpcltApplet.class\" />"+
				"<param name=\"archive\" value=\"SipRpclt.jar\" />" +
				"<param name=\"reqId\" value='"+data[i].jobId+"' />"+
				"<param name=\"server\" value='"+data[i].serviceIp+"'/>"+
				"<param name=\"operation\" value='"+data[i].printType+"' />"+
				"</object>";
			}
		}
		if(html!=""){
			$("#signPrintShow", navTab.getCurrentPanel()).html(html);
			alertMsg.correct("单证打印成功");
		}
	},'json');	
}
//控制标签页输入项为只读
function onlyReadRegisterCheckConfirm(){
	bs = 1;
	//------  所有的-------
	var obj=$("div#registerCheckconfirmDiv",navTab.getCurrentPanel());
	//按钮
	obj.find("button").each(function(){
		//if($(this).text()!="上一步"){
			$(this).attr("disabled",true);
		//}
		//if($(this).text()=="下一步"){
		//	$(this).attr("disabled",false);
		//}
		//if($(this).text()=="上一步"){
		//	$(this).attr("disabled",false);
		//}
	});
	//a标签
	obj.find("a").each(function(){
		$(this).attr("disabled",true);
	});
	//textarea标签
	obj.find("textarea").each(function(){
		$(this).attr("disabled",true);
	});
	obj.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
}
//更改提交形式
function updateRegisterCheckSubmitFormat(obj){
	if($(obj).is("[value=1]")){
		$(obj).parent().parent().next().next().find("input[value=0]").attr("checked",true);
	}else if($(obj).is("[value=2]")){
		$(obj).parent().parent().next().next().find("input[value=1]").attr("checked",true);
	}
}

//点击假添加按钮触发正按钮
function addTab1(){
	var num = $(".textInput_bak", navTab.getCurrentPanel()).val();
	$(".myClassFlag", navTab.getCurrentPanel()).prev().val(num);
	$(".myClassFlag", navTab.getCurrentPanel()).prev().prev().children().children().click();
}

$(document).ready(function(){
//添加单证按钮移动到输入框下端js
	setTimeout(function(){
		$(".myClassFlag", navTab.getCurrentPanel()).prev().css("display","none");
		$(".myClassFlag", navTab.getCurrentPanel()).prev().prev().css("display","none");
	},100);
});

