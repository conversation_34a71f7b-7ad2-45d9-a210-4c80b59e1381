	function showOutsourceInfo(obj) {
		var value = $(obj).val();
		if (value == "1") {
			$(obj).attr("checked", false);
			$(obj).val("0");
			$("#outsourceId",navTab.getCurrentPanel()).val("");
			$("#outsourceNameId",navTab.getCurrentPanel()).val("");
			$("#outsourceNameId",navTab.getCurrentPanel()).attr("disabled", false);
			$("#caseAmountWarnId",navTab.getCurrentPanel()).val("");
			$("#effectiveFlagId",navTab.getCurrentPanel()).val("1");
			$("#simpleCasePriceId",navTab.getCurrentPanel()).val("");
			$("#unSimpleCasePriceId",navTab.getCurrentPanel()).val("");
			$("#vendorCodeId",navTab.getCurrentPanel()).val("1");
		} else {
			$(obj).val("1");
			var outsourceFlag = $(obj).parent().parent().find("td:eq(2)").text();
			var outsourceName = $(obj).parent().parent().find("td:eq(3)").text();
			var caseAmountWarn = $(obj).parent().parent().find("td:eq(4)").text();
			var effectiveFlag = $(obj).parent().parent().find("td:eq(5)").find("input").val();
			var simpleCasePrice = $(obj).parent().parent().find("td:eq(6)").text();
			var unSimpleCasePrice = $(obj).parent().parent().find("td:eq(7)").text();
			var vendorCode = $(obj).parent().parent().find("td:eq(8)").find("input").val();
			var outsourceWay = $(obj).parent().parent().find("td:eq(10)").find("input").val();
			var id = $(obj).parent().parent().find("td:eq(9)").text();
			$("#outsourceId",navTab.getCurrentPanel()).val(id);
			$("#outsourceFlagId",navTab.getCurrentPanel()).val(outsourceFlag);
			$("#outsourceNameId",navTab.getCurrentPanel()).val(outsourceName);
//			$("#outsourceNameId",navTab.getCurrentPanel()).attr("disabled", true);
			$("#caseAmountWarnId",navTab.getCurrentPanel()).val(caseAmountWarn);
			$("#effectiveFlagId",navTab.getCurrentPanel()).val(effectiveFlag);
//			$("#effectiveFlagId",navTab.getCurrentPanel())[0].fireEvent("onchange");
			$("#effectiveFlagId",navTab.getCurrentPanel()).selectMyComBox(effectiveFlag);
			$("#simpleCasePriceId",navTab.getCurrentPanel()).val(simpleCasePrice);
			$("#unSimpleCasePriceId",navTab.getCurrentPanel()).val(unSimpleCasePrice);
			$("#vendorCodeId",navTab.getCurrentPanel()).val(vendorCode);
//			$("#vendorCodeId",navTab.getCurrentPanel())[0].fireEvent("onchange");
			$("#vendorCodeId",navTab.getCurrentPanel()).selectMyComBox(vendorCode);
			$("#outsourceWayId",navTab.getCurrentPanel()).selectMyComBox(outsourceWay);
		}
	}
	
	function checkxiaoshu(obj){
		var n=/^[0-9]{1}[0-9]{0,11}(\.[0-9]{0,2}){0,2}$/;
		if($(obj).val()!=""){
			if(!n.test($(obj).val())){
				alertMsg.warn("请输入一个最大为999999999999.99的值");
				$(obj).val("");
				return flase;
			}
		}
	}
	function checkzhengshu(obj){
		var r =/^[0-9]*[1-9][0-9]*$/;
		if($(obj).val()!=""){
			if(!r.test($(obj).val())){
				alertMsg.warn("请输入一个最大为99999999的值");
				$(obj).val("");
				return flase;
			}
		}
	}
	
	function noChecked(obj) {
		$(obj).val('0');
	}
	function saveOutsourceIfno() {	
		var from = $("#addOutsourceIfnoId",navTab.getCurrentPanel());
		var radio=$("#outsourceVarId",navTab.getCurrentPanel()).find("tr").find("td:eq(0)").find("input:radio[name='radio']:checked");
		var outsourceName = $("#outsourceNameId",navTab.getCurrentPanel()).val();
		var effectiveFlagId = $("#effectiveFlagId",navTab.getCurrentPanel()).val();
		var vendorCodeId = $("#vendorCodeId",navTab.getCurrentPanel()).val();
		var outsourceId = $("#outsourceId",navTab.getCurrentPanel()).val();
		if(isNulOrEmpty(outsourceName)){
			alertMsg.warn("外包商名称必填。");
			$("#outsourceNameId",navTab.getCurrentPanel()).focus();
			return false;
		}
		if(isNulOrEmpty(effectiveFlagId)){
			alertMsg.warn("有效标识必填。");
			$("#effectiveFlagId",navTab.getCurrentPanel()).focus();
			return false;
		}
		if(isNulOrEmpty(vendorCodeId)){
			alertMsg.warn("外包商类型必填。");
			$("#vendorCodeId",navTab.getCurrentPanel()).focus();
			return false;
		}
		if(effectiveFlagId){
			effectiveFlagId=effectiveFlagId.substring(0,1);
		}
		if(vendorCodeId){
			vendorCodeId=vendorCodeId.substring(0,1);
		}
		$.ajax({ 
			url : "clm/register/checkOutsourceName_CLM_epibolyMainTainAction.action?outsourceIfnoVO.outsourceName="+encodeURI(encodeURI(outsourceName))+"&outsourceIfnoVO.effectiveFlag="+effectiveFlagId+"&outsourceIfnoVO.vendorCode="+vendorCodeId+"&outsourceIfnoVO.outsourceId="+outsourceId,
			global : false,
			type : "POST",
			dataType : "json",
			success : function(json){
				if (json.statusCode != DWZ.statusCode.ok) {	
					alertMsg.warn("系统中已有该外包商，请勿重复添加");
					return false;
				}else{
					if (radio.val()) {
						radio.parent().parent().find("td:eq(4)").html($("#caseAmountWarnId",navTab.getCurrentPanel()).val());
						radio.parent().parent().find("td:eq(5)").find("input").val($("#effectiveFlagId",navTab.getCurrentPanel()).val());
						if($("#effectiveFlagId",navTab.getCurrentPanel()).val()==1){
							radio.parent().parent().find("td:eq(5)").html("<input type='hidden' value='1'>有效");
						}else{
							radio.parent().parent().find("td:eq(5)").html("<input type='hidden' value='0'>无效");
						}
						radio.parent().parent().find("td:eq(6)").html($("#simpleCasePriceId",navTab.getCurrentPanel()).val());
						radio.parent().parent().find("td:eq(7)").html($("#unSimpleCasePriceId",navTab.getCurrentPanel()).val());
						radio.parent().parent().find("td:eq(8)").find("input").val($("#vendorCodeId",navTab.getCurrentPanel()).val());
						if($("#vendorCodeId",navTab.getCurrentPanel()).val()==1){
							radio.parent().parent().find("td:eq(8)").html("<input type='hidden' value='1'>医疗类外包商");
						}else{
							radio.parent().parent().find("td:eq(8)").html("<input type='hidden' value='2'>非医疗类外包商");
						}
						from.attr("action","clm/register/updateOutsourceIfno_CLM_epibolyMainTainAction.action");
						from.submit();
					} else {
						from.attr("action","clm/register/addOutsourceIfno_CLM_epibolyMainTainAction.action");
						from.submit();
						$("#outsourceId",navTab.getCurrentPanel()).val("");
						$("#outsourceNameId",navTab.getCurrentPanel()).val("");
						$("#outsourceNameId",navTab.getCurrentPanel()).attr("disabled", false);
						$("#caseAmountWarnId",navTab.getCurrentPanel()).val("");
						$("#effectiveFlagId",navTab.getCurrentPanel()).val("1");
						$("#simpleCasePriceId",navTab.getCurrentPanel()).val("");
						$("#unSimpleCasePriceId",navTab.getCurrentPanel()).val("");
						$("#vendorCodeId",navTab.getCurrentPanel()).val("1");
					} 
				}
			}
		});		
		}
//	function checkName(obj) {
//		var effectiveFlagId = $("#effectiveFlagId",navTab.getCurrentPanel()).val();
//		var vendorCodeId = $("#vendorCodeId",navTab.getCurrentPanel()).val();
//		if(effectiveFlagId){
//			effectiveFlagId=effectiveFlagId.substring(0,1);
//		}
//		if(vendorCodeId){
//			vendorCodeId=vendorCodeId.substring(0,1);
//		}
//		if(trim($(obj).val())!=""){
//	    $.ajax({ 
//			url : "clm/register/checkOutsourceName_CLM_epibolyMainTainAction.action?outsourceIfnoVO.outsourceName="+encodeURI(encodeURI($(obj).val()))+"&outsourceIfnoVO.effectiveFlag="+effectiveFlagId+"&outsourceIfnoVO.vendorCode="+vendorCodeId,
//			global : false,
//			type : "POST",
//			dataType : "json",
//			success : function(json){
//				if (json.statusCode != DWZ.statusCode.ok) {	
//					alertMsg.warn("系统中已有该外包商，请勿重复添加");
//					$(obj).val("");
//					return false;
//				}
//			}
//		});		
//		}else{
//			$(obj).val("");
//		}
//	}
	/*function exit(){
		alertMsg.confirm("是否确定退出？",{
			okCall:function(){
				navTab.closeCurrentTab();
			}
		});
	}*/
	//查询数据
	function queryEpibolyMain(){
		var outsourceFlag = $("#outsourceFlag", navTab.getCurrentPanel()).val();
		var outsourceName = $("#outsourceName", navTab.getCurrentPanel()).val();
		var effectiveFlag = $("#effectiveFlag", navTab.getCurrentPanel()).val();
		var vendorCode = $("#vendorCode", navTab.getCurrentPanel()).val();
		var outsourceWay = $("#outsourceWay", navTab.getCurrentPanel()).val();
		if(isNorE(outsourceFlag)&&isNorE(outsourceName)
				&&isNorE(effectiveFlag)&&isNorE(vendorCode)&&isNorE(outsourceWay)){
			alertMsg.warn("请至少输入一项查询条件");
			return;
		}
		$.ajax({
			url : "clm/register/queryOutsourceIfnoFlag_CLM_epibolyMainTainAction.action",
			type : 'post',
			data:$("#queryOutsourceIfnoIdTain", navTab.getCurrentPanel()).serialize(),
			datatype : 'json',
			global : false,
			success : function(json) {
				var data = eval("(" + json + ")");
				if(data.message == "1"){
					alertMsg.error("所查询的外包商不存在，请重新输入查询条件。");
				} 
				$("#queryOutsourceIfnoIdTain", navTab.getCurrentPanel()).submit();
			}	
		});
	}

	function isNorE(s){
		if(s==null||s==""){
			return true;
		}else{
			return false;
		}
	}