
	//险种、机构、操作人员在下拉框里的单个添加、批量添加、单个删除、批量删除
	function resetAll(){
		$('#testid', navTab.getCurrentPanel()).attr('value','');
		$('#testname', navTab.getCurrentPanel()).attr('value','');
	}
	var zuobox;
	var youbox;
	//获取下拉框对象 
	function share(num){
		if(num==1){
			zuobox=document.getElementById("select1");
			youbox=document.getElementById("select2");
		}
		if(num==2){
			zuobox=document.getElementById("select3");
			youbox=document.getElementById("select4");
		}
		if(num==3){
			zuobox=document.getElementById("select5");
			youbox=document.getElementById("select6");
		}
	}
	//将左边选中的下拉选项添加到右边的下拉框中
	function addOption(num){
		share(num);
		var text=zuobox.options[zuobox.selectedIndex].text;
		var value=zuobox.options[zuobox.selectedIndex].value;
		youbox.options.add(new Option(text,value));
	}
	//将左边所有的下拉选项都添加到右边的下拉框中
	function addAll(num){
		share(num);
		youbox.options.length=0;
		var size=zuobox.options.length;
		for(var i=0;i<size;i++){
			var value=zuobox.options[i].text;
			youbox.options.add(new Option(value,value));
		}
	}
	//删除右边选中的下拉选项
	function delOption(num){
		share(num);
		index=youbox.selectedIndex;
		youbox.options.remove(index);
	}
	//删除右边下拉框的所有下拉选项
	function removeAll(num){
		share(num);
		youbox.options.length=0;
	}
	
	//添加时间段信息
	var id=1;
	function Addtime(){
		var list=document.getElementsByName("c2");
		var tab1=document.getElementById("tab1");
		var put1=document.getElementById("put1").value;
		var put2=document.getElementById("put2").value;
		tab1.style.display="block";
		var checkList=[];
		for(var i=0;i<list.length;i++){
			if(list[i].checked){
				checkList.push(list[i].value);
			}
		}
		for(var i=0;i<checkList.length;i++){
			if(checkList[0]==6 && checkList[1]==7){
				if(i==1){
					var tr=tab1.insertRow();
					tr.id="t"+id;
					var td1=tr.insertCell(0);
					var td2=tr.insertCell(1);
					var td3=tr.insertCell(2);
					var td4=tr.insertCell(3);
					td1.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.ruleValue\" value=\"周末\"/>"+"周末";
					td2.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.subParaStart\" value=\""+put1+"\"/>"+put1;
					td3.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.subParaEnd\" value=\""+put2+"\"/>"+put2;
					td4.innerHTML="<a onclick=\"delRow('"+tr.id+"')\">删除</a>";
					id++;
				}
			}else if(checkList[0]==1 && checkList[1]==2 && checkList[2]==3 
					&& checkList[3]==4 && checkList[4]==5){
				if(i==4){
					var tr=tab1.insertRow();
					tr.id="t"+id;
					var td1=tr.insertCell(0);
					var td2=tr.insertCell(1);
					var td3=tr.insertCell(2);
					var td4=tr.insertCell(3);
					td1.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.ruleValue\" value=\"工作日\"/>"+"工作日";
					td2.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.subParaStart\" value=\""+put1+"\"/>"+put1;
					td3.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.subParaEnd\" value=\""+put2+"\"/>"+put2;
					td4.innerHTML="<a onclick=\"delRow('"+tr.id+"')\">删除</a>";
					id++;
				}
			}else{
				for(var i=0;i<list.length;i++){
					if(list[i].checked){
						var tr=tab1.insertRow();
						tr.id="t"+id;
						var td1=tr.insertCell(0);
						var td2=tr.insertCell(1);
						var td3=tr.insertCell(2);
						var td4=tr.insertCell(3);
						td1.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.ruleValue\" value=\""+list[i].nextSibling.nodeValue+"\"/>"+list[i].nextSibling.nodeValue;
						td2.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.subParaStart\" value=\""+put1+"\"/>"+put1;
						td3.innerHTML="<input  style=\"display:none\" name=\"checkParaVO.subParaEnd\" value=\""+put2+"\"/>"+put2;
						td4.innerHTML="<a onclick=\"delRow('"+tr.id+"')\">删除</a>";
						id++;
					}
				}
			}
		}
			
		
	}
	//删除一行tr
	function delRow(id){
		var tr=document.getElementById(id);
		var tbody=tr.parentNode;
		tbody.removeChild(tr); 
	}
	//将右边下拉框所有的下拉选项都设为选中
	function submits(){
		var youbox1=document.getElementById("select2");
		var youbox2=document.getElementById("select4"); 
		var youbox3=document.getElementById("select6");
		for(var i=0;i<youbox1.options.length;i++){
			youbox1.options[i].selected=true;
		}
		for(var i=0;i<youbox2.options.length;i++){
			youbox2.options[i].selected=true;
		} 
		for(var i=0;i<youbox3.options.length;i++){
			youbox3.options[i].selected=true;
		}
	}
	