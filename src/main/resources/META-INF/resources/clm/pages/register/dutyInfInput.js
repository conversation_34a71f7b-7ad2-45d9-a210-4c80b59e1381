queryClaimSubCase();

/**
 * 根据理赔类型 ==生成左侧复选框
 */
function queryClaimSubCase(){
	 
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/register/queryClaimType_CLM_tClaimDutyRegisterAction.action?caseId='+caseId,
		'type':'post',
		'data':{},'datatype':'json','success':function(data){
			var data = eval("(" + data + ")");
	      // data=['01','02','03','04','05','06','07','08','09','10','11','12'];
		     	
			for(var i=0;i<data.length;i++){
			     //var claimType=data[i];

			 	var claimType=data[i].claimType;
				if(claimType.match('02')||claimType.match('03')){
					// 02:伤残    03 :重大疾病 ====>伤残  特种费用
					if(claimType.match('02')){
						if($("[hiddenDivId='registerMaimNews']", navTab.getCurrentPanel()).html()==null){
							$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[2]' value='2' hiddenDivId='registerMaimNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>伤残 </br>");
						}
					}
					if($("[hiddenDivId='registerOpsNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[3]' value='3' hiddenDivId='registerOpsNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' >特定手术/疾病/给付 <br>");
					}
					if($("[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='registerSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>特种费用 <br>");
					}
				}else if(claimType.match('04')){
					// 04:高残 
					if($("[hiddenDivId='registerMaimNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[2]' value='2' hiddenDivId='registerMaimNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>伤残 </br>");
					}
					// 04:高残 ===> 同时出险（连生保单）
					if($("[hiddenDivId='registerClaimTogetherNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[16]' value='16' hiddenDivId='registerClaimTogetherNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>同时出险（连生保单） </br>");
					}
				} else if(claimType.match('08')){
					//08:医疗====>医疗单证录入、社保第三方给付、特种费用、手术分级给付
					if($("[hiddenDivId='registerHonsp']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[1]' value='1' hiddenDivId='registerHonsp' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>医疗单证录入 <br>");
					}
					if($("[hiddenDivId='registerOpsLevelNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[15]' value='15' hiddenDivId='registerOpsLevelNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>手术分级<br>");
					}
					if($("[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='registerSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>特种费用 <br>");
					}
					if($("[hiddenDivId='registerThirdPayNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[5]' value='5' hiddenDivId='registerThirdPayNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>社保第三方给付 <br>");
					}	
				}else if (claimType.match('10')){
					//10:特种疾病=====>特定手术/疾病/给付、特种费用
					if($("[hiddenDivId='registerOpsNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[3]' value='3' hiddenDivId='registerOpsNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' >特定手术/疾病/给付 <br>");
					}
					if($("[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='registerSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>特种费用 <br>");
					}
					
				}else if (claimType.match('06')){
					//06:一般失能======>一般失能
					if($("[hiddenDivId='registerGeneralNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[7]' value='7' hiddenDivId='registerGeneralNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>一般失能 <br>");
					}
				}else if (claimType.match('07')){
					//07:重度失能======>重度失能
					if($("[hiddenDivId='registerGreatNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[8]' value='8' hiddenDivId='registerGreatNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>重度失能 <br>");
					}
				}else if (claimType.match('01')){
					//01:身故======>同时出险（连生保单）
					if($("[hiddenDivId='registerClaimTogetherNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[16]' value='16' hiddenDivId='registerClaimTogetherNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>同时出险（连生保单） <br>");
					}
				}
	  		}
		}
	});
}
 
//点击左侧复选框显示右边详细信息
function smallChange(obj){
	 
	var caseId = $("#caseId", navTab.getCurrentPanel()).attr("value");
	var id = $(obj).attr("hiddenDivId");
	if($("#"+id, navTab.getCurrentPanel()).html()!=''){
		 
		if($(obj).is(":checked")){ 
			$("#"+id, navTab.getCurrentPanel()).find(".MedRadio:checked").attr("checked",false);
			$("#"+id, navTab.getCurrentPanel()).show("3000");  
			
			 /* if($("#"+id, navTab.getCurrentPanel()).find(".MedRadio").length>0){
				  //赋值  //赋值为hidden标识   编辑明细页面做了处理
			   $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").attr("title","hidden");
			   $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").click();
			    
			  }else{
			      $("#"+id, navTab.getCurrentPanel()).find("button[type='button']").click();
			      $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").click();
 
			  } */
			  // setTimeout('addNewcheck("'+id+'")', 250);
 
			 
		}else{
			
			 $("#hospitalcost", navTab.getCurrentPanel()).hide("3000",
						function() {
							$(this).empty();
		     });
 			$("#"+id, navTab.getCurrentPanel()).hide("3000");
		}
	}else{
		///下半部分先关掉
var readOnly =$("#readOnly", navTab.getCurrentPanel()).val();
		  var  idd="";
		if(id == "registerHonsp"){
			idd="registerHonsp";
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerHonsp", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerMaimNews"){
			idd="registerMaimNews";
			var rel = $("#registerMaimNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerMaimNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerOpsNews"){
			idd="registerOpsNews";
			var rel = $("#registerOpsNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerOpsNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerOpsLevelNews"){
			idd="registerOpsLevelNews";
			var rel = $("#registerOpsLevelNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsLevelNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerOpsLevelNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerSpeCostNews"){
			idd="registerSpeCostNews";
			var rel = $("#registerSpeCostNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerSpeCostNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerThirdPayNews"){
			idd="registerThirdPayNews";
			var rel = $("#registerThirdPayNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerThirdPayNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerGeneralNews"){
			idd="registerGeneralNews";
			var rel = $("#registerGeneralNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterGeneralNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerGeneralNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerGreatNews"){
			idd="registerGreatNews";
			var rel = $("#registerGreatNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterGreatNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerGreatNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerClaimTogetherNews"){
			idd="registerClaimTogetherNews";
			var rel = $("#registerClaimTogetherNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterClaimTogetherNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerClaimTogetherNews", navTab.getCurrentPanel()).show("3000");
		} 
		
		// setTimeout('addNewcheck("'+idd+'")', 250);
		
	}
}


function addNewcheck(idd){
	  
	if($("#"+idd, navTab.getCurrentPanel()).find(".MedRadio").length>0){
		  //赋值为hidden标识   编辑明细页面做了处理
	      $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").attr("title","hidden");
		  $("#"+idd, navTab.getCurrentPanel()).find(".MedRadio:last").click();
		  
		  $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html("");
	  }else{
	      $("#"+idd, navTab.getCurrentPanel()).find("button[type='button']").click();
	      $("#"+idd, navTab.getCurrentPanel()).find(".MedRadio:last").click();
  }
	
}

//根据直接给付代码 查询职业类别和职业给付系数
function queryJobLevelByJobCode(obj){
	var jobCode=$(obj).attr("value");
	if (jobCode == '') {
	    $(obj).closest("dl").next().find("input[name='data']").attr("value","");
		$(obj).closest("dl").next().next().find("input").attr("value","");
		$("#oldJobCode", navTab.getCurrentPanel()).attr("value","");
		$("#oldJobCategoryName", navTab.getCurrentPanel()).attr("value","");
		$("#oldJobUwLevel", navTab.getCurrentPanel()).attr("value","");
	} else {
		$.ajax({
			'type':'post',
			'url':'clm/register/queryOccupationRateByCode_CLM_tClaimDutyRegisterAction.action',
			'data':{'jobCodeVO.jobCode':jobCode},
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				 $(obj).closest("dl").next().find("input[name='claimOccupationRateVO.jobCategory']").attr("value",data.jobCategory);
				 $(obj).closest("dl").next().find("input[name='data']").attr("value",data.jobCategoryName);
				 $(obj).closest("dl").next().next().find("input").attr("value",data.jobUwLevel);
				 $("#oldJobCode", navTab.getCurrentPanel()).attr("value",data.oldJobCode);
				 $("#oldJobCategoryName", navTab.getCurrentPanel()).attr("value",data.oldJobCategoryName);
				 $("#oldJobUwLevel", navTab.getCurrentPanel()).attr("value",data.oldJobUwLevel);
			},
		});
	}
}
//页面加减号的显示和隐藏
function show(show){
	if(show.value=="-"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).hide("3000");
		show.value="+";
	}else if (show.value=="+"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).show("3000");
		show.value="-";
	}
}
$(".btnDel", navTab.getCurrentPanel()).live("click",function(){
	$(this).closest("tr").remove();
});
//上一步 先保存页面信息  再跳转
function dutyInfInit_prev(id,caseId){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确认回到上一步？",{
		okCall:function(){ 
			backValue();
			var submitDuty = $("#submitRegisterDuty", navTab.getCurrentPanel());
			$.ajax({
				'type':'post',
				'url':'clm/register/saveClaimDutyRegisterCheckBox_CLM_tClaimDutyRegisterAction.action',
				'data':$(submitDuty).serialize(),
				'datatype':'json',
				'async':false,
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode == '300') {
						alertMsg.error("执行保存方法失败异常："+data.message);
						return false;
					}
					if(data.statusCode == '200'){
						prev(id,caseId);
					}
				},'error':function(){
					alertMsg.error("执行保存方法失败!");
				}
			});
	
	 }
	}); 
} 
//保存按钮
function submitRegisterDuty(flag){
	if(flag == "1" || flag == "0"){
	//针对疑似虚假票增加校验
	var nonRealBillFlag = $("#nonRealBillFlag", navTab.getCurrentPanel()).val();
	if(nonRealBillFlag=="1"){
		alertMsg.info("该案件有发票疑似虚假票据，请确认是否已调查核实"); 
	   }
	}
	//若个案风险识别接口返回了明细数据，简易审核、普通审核的责任明细页面【下一步】及自动审核处理需要校验【医疗费用明细清单】按钮必点
	var judgeDetailFlag  = true;
	if(flag == "1" || flag == "3"){
		if($("#auditFlag", navTab.getCurrentPanel()).size() > 0){
			$.ajax({
	  			'type':'post',
	  			'url':'clm/register/judgeDetailLookFlag_CLM_tClaimDutyRegisterAction.action?claimCaseVO.caseId=' + caseId,
	  			'datatype':'json',
	  			'async':false,
	  			'success':function(data){
	  				var json = eval("(" + data + ")");
	  				for(var kay in json){
	  					if(kay == "0"){
	  						judgeDetailFlag  = false;
	  						alertMsg.error(json[kay]);
						}
					}
	  			},
	  			'error':function(){
	  				alert("执行校验医疗费用明细清单按钮必点方法失败！");
	  			}
	  		});
			if(!judgeDetailFlag){
				return false;
			}
		}
	}
	if(flag == "2"){
		///最外层  toauditpage 上的标识
		if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
			//审核页面
			prev_audit('1',caseId);
			return;
		}else{
		   ///立案页面
			prev('12',caseId);
		   return;
		}
	}
	backValue(); 
	
	 setTimeout(function(){
		 if($("#errorFlag", navTab.getCurrentPanel()).val()=="false"){
			 throw "stop";
	     }  
		 submitRegisterDutyTimeOut(flag);
		 
	 },1000) ; 
	//
	$("div", navTab.getCurrentPanel()).scrollTop(0);
}

function submitRegisterDutyTimeOut(flag){
	var occupationRate = $("input#claimOccupationRateVOoccupationRate").val();
	if (occupationRate =='z') {
		alertMsg.error("不接受拒保类职业变更，请在保项层人工处理。"); 
		return false;
	}
	
	var submitDuty = $("#submitRegisterDuty", navTab.getCurrentPanel());
	if($("#registerClaimSubCase", navTab.getCurrentPanel()).find("input[type='checkbox']#ageDealFlag").is(":checked")){
		var insuBirth = $("#insuBirthIdId",navTab.getCurrentPanel()).val();
		var insuRealBirth = $("#insuRealBirth",navTab.getCurrentPanel()).val();
		if(insuRealBirth == ""){
			alertMsg.error("勾选年龄误告处理，请录入出险人真实出生日期！"); 
			$("#alertMsgBox .toolBar .button").on("click",function(){ 
				$("#insuRealBirth",navTab.getCurrentPanel()).focus();
			});
			return false;
		}else{
			// 获取系统日期 直接获取右上角显示日期
			var workDate =$("#hiddenWorkDate", navTab.getCurrentPanel()).val();
			if(insuRealBirth > workDate){
				alertMsg.error("出险人真实出生日期不能晚于当前日期！");
				return false;
			}
			if(insuRealBirth == insuBirth){
				alertMsg.error("出险人真实出生日期不能等于当前出生日期！");
				return false;
			}
		}
	} 
	var caseIdList = $("#submitRegisterDuty").find("[name='caseId']");
	caseIdList.each(function(i,obj){
		if(i!=0){
			$(obj).attr("disabled","disabled");
		}
	});

	$.ajax({
		'type':'post',
		'url':'clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action?allSave=1',
		'data':$(submitDuty).serialize(),
		'datatype':'json',
		'success':function(data){
			 
			var dataMsg = eval("(" + data + ")");
			
			var caseIdList = $("#submitRegisterDuty").find("[name='caseId']");
			caseIdList.each(function(i,obj){
				if(i!=0){
					$(obj).attr("disabled","");
				}
			});
			 	    
			var caseId = $("#caseId", navTab.getCurrentPanel()).val();
			 
			if (initFlag == "1") {
				$.ajax({
					url : "clm/advancepay/advanceCalc_CLM_advancePayAction.action?claimCaseVO.caseId="+caseId,
					type : "POST",
					dataType : "json",
					success : function (message){
						if (message.statusCode == DWZ.statusCode.ok) {
							  
							if(flag == 2){//表示为上一步操作
								///最外层  toauditpage 上的标识
								if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
									//审核页面
									prev_audit('1',caseId);
								}else{
								   ///立案页面
								   dutyInfInit_prev('12',caseId);
								}
							}else if(flag == 1){//表示为下一步操作
								////调用checkRepeatedCase方法校验重复账单
								if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
									//审核页面
									if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
										 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
												okCall :   function(){
									              next_audit('3',caseId);
												}
										 });
									}else{
										next_audit('3',caseId);
									}
								}else{
									//立案
									//立案责任明细增加下一步查   重复账单及校验操作。 next()中
									if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
										 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
												okCall :   function(){
													next('14','addApplicantForm',caseId);
												}
										 });
									}else{
										next('14','addApplicantForm',caseId);
									}
									
									
								}
							} else if(flag == 3){
								$("span#advanceInput", navTab.getCurrentPanel()).parent().click();
								 alertMsg.close();
								 autoAuditNew();
							} else{//表示为保存操作
								 $("span#advanceInput", navTab.getCurrentPanel()).parent().click();
								 alertMsg.close();
							}
						} else {
							alertMsg.close();
							alertMsg.error(message.message);
						}
					}
				});
			} else {
				var data = eval("(" + data + ")");
				if (data.statusCode == DWZ.statusCode.ok) {
					if(flag == 2){//表示为上一步操作
						if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
							//审核页面
							prev_audit('1',caseId);
						}else{
						   ///立案页面
						   dutyInfInit_prev('12',caseId);
						}
						 
					}else if(flag == 1){//表示为下一步操作
						//调用checkRepeatedCase方法校验重复账单
						if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
							//审核页面
							
							if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
								 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
										okCall :   function(){
											next_audit('3',caseId);
										}
								 });
							}else{
								next_audit('3',caseId);
							}
						 }else{
							 if(dataMsg.MSG_CLM_WRN_AUDIT_032 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_032 != "null"){
									alertMsg.error(dataMsg.MSG_CLM_WRN_AUDIT_032);
							 }else {
								 if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
									 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
											okCall :   function(){
												next('14','addApplicantForm',caseId);
											}
									 });
								}else{
									next('14','addApplicantForm',caseId);
								}
							 }
						 }
					} else if(flag == 3){
						$("span#advanceInput", navTab.getCurrentPanel()).parent().click();
						 alertMsg.close();
						 autoAuditNew();
					}else{//表示为保存操作
						/*alertMsg.close();
						alertMsg.correct(data.message);*/
					}
				} else {
					alertMsg.error(data.message);
				}
			}
			
		},
	});
}

 
function onlyReadDuty(){
	//------  所有的-------
	var obj=$("div#registerAskForInfo",navTab.getCurrentPanel());
	//按钮
	 
	obj.find("button").each(function(){ 
		  $(this).attr("disabled",true);
	});
	//a标签
	obj.find("a").each(function(){ 
		if(!($(this).html()=="下一步"||$(this).html()=="上一步")){ 
			$(this).attr("disabled",true);
		}
	
	}); 
	//textarea标签
	obj.find("textarea").each(function(){
		$(this).attr("disabled",true);
	});
	obj.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//回退页面单选按钮可以触发 用于显示详情
	obj.find("input[type=radio]").each(function(){
		$(this).attr("disabled",false);
	});
	
}



//页面加减号的显示和隐藏
function showDiv(show){
	 
	if(show.value=="-"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).hide("3000");
		show.value="+";
	}else if (show.value=="+"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).show("3000");
		show.value="-";
	}
}



//自定义返回状态 
function dutyMyAjaxDone(json) { 
	
	if(json.statusCode == DWZ.statusCode.error) {
		if(json.message && alertMsg) alertMsg.error(json.message);
	} else if (json.statusCode == DWZ.statusCode.timeout) {
		//if(alertMsg) alertMsg.error(json.message || DWZ.msg("sessionTimout"), {okCall:DWZ.loadLogin});
		//else 
		//注掉提示信息 ，直接弹出登录窗口 Liandong
			DWZ.loadLogin(json.flag);
	} else {
		if(json.message && alertMsg) alertMsg.correct(json.message);
		closeInit();
	};
  
}


//自定义返回状态 
function dutyAjaxDone(json) {  
	 
	if(json.statusCode == DWZ.statusCode.error) {
		 //校验的关键
		 $("#errorFlag", navTab.getCurrentPanel()).val("false"); 
		  if(json.message && alertMsg){alertMsg.error(json.message)};  
		    
	}else { 
		$("#errorFlag", navTab.getCurrentPanel()).val("true");  
		 if(json.message && alertMsg) alertMsg.correct(json.message);
		var id=json.pageFlag;
		var caseId=json.caseId;
		if(id == "registerHonsp"){
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="registerMaim"){
			var rel = $("#registerMaimNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			 
		}
		if(id=="registerGreat"){
			var rel = $("#registerOpsNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="registerOpsLevel"){
			var rel = $("#registerOpsLevelNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsLevelNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="registerSpeCost"){
		 
			var rel = $("#registerSpeCostNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="socialPay"||id=="registerThirdPay"){ 
			if(id=="socialPay"){ 
				jsonShowMap="social-Pay=show"
			}
			if(id=="registerThirdPay"){ 
				jsonShowMap="third-Pay=show" 
					
			} 
			var rel = $("#registerThirdPayNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}   
		if(id=="highMedical"||id=="registerHonsp"||id=="cancerMedical"||id=="CARCellMedical"){
			if(id=="highMedical"){
				jsonShowMap="HighEnd=show"
			}if(id=="registerHonsp"){
				jsonShowMap="Hospital=show"
			}
			if(id=="cancerMedical"){
				jsonShowMap="CancerPre=show"
			}
			if(id=="CARCellMedical"){
				jsonShowMap="CARCell=show"
			}
			
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
	};   
	 
}
  

//数据返回某一行 
function backValue() {

	if ($("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).children().length > 0) {
		// 清除错误信息
		$("#errorFlag", navTab.getCurrentPanel()).val("true");
		// 先把展开的保存

		$("#pageFormdivID", navTab.getCurrentPanel()).find("#SaveNewButton").each(function(i, obj) {
			console.log($(obj).parent().parent().attr("titles"));
			if ($(obj).parent().next().html() != "") {
				$(obj).click();
				setTimeout(function() {
					console.log($("#errorFlag",navTab.getCurrentPanel()).val());
					if ($("#errorFlag",navTab.getCurrentPanel()).val() == "false") {
						console.log("stop");
						throw "stop";
					} else {
						// (保存方法中如果错误则在页面上的id="errorFlag"
						// 赋值 如果验证不通过不继续往下走)
						//
						$("#dutyHospitalCostInitDetails",navTab.getCurrentPanel()).html("");

						$("#hospitalcost",navTab.getCurrentPanel()).hide("3000",function() {
											$(this).empty();
						});
					}

				}, 500);
				
				//小保存方法存在阻断提示，以下不执行
				if($("#errorFlag", navTab.getCurrentPanel()).val()=="false"){
					return;
				}
			}
		});
	}
	
	//小保存方法存在阻断提示，不予通过
	if($("#errorFlag", navTab.getCurrentPanel()).val()=="false"){
		$("#errorFlag", navTab.getCurrentPanel()).val("true");
		return;
	}
	
	var flag = $("#dutyNextIdFlag", navTab.getCurrentPanel()).val();
	//每次恢复为空
	if (flag != "" && flag != null) {
		submitRegisterDutyTimeOut(flag);
	}

}
	//关闭
	function  closeInit(){
		
       $("#hospitalcost", navTab.getCurrentPanel()).hide("3000",function(){
	     	$(this).empty();
	    });
		
	}
	///输入框--去除
	function changeInputToSpan(obj){ 
	 
		$(obj).next().html($(obj).val()); 
		
		///高端医疗进行处理
		if($(obj).attr("yesOrNo")=="yesOrNo"){
			if($(obj).val()=="0"){
				$(obj).next().html("否"); 
			}
			if($(obj).val()=="1"){
				$(obj).next().html("是"); 
			}
		 
		}
		
	}
	
	
	//责任明细自定义
	function validateCallbackDuty(form, callback, confirmMsg) {
		
		// ******* 超时监控 Add HYP
		if (JudgeTimeOut()) {
			DWZ.loadLogin();
			return false;
		}
		
		// form 校验 若校验失败不允许提交
		var $form = $(form);
		/*if (!$form.valid()) {
			return false;
		}*/
		
		// ********添加自定义判断程序 add LiAnDong 
		var myOption = $form.attr("myOption");
		if (null != myOption && myOption != '') {
			var myFlag = eval(myOption);
			if (!myFlag)
				return myFlag;
		}
		
		var result = {data:$form.serializeArray()};
		var parent = $.pdialog.getCurrent() || navTab.getCurrentPanel();
		// *******交易管理监控 Add HYP
		if(dealSwitch){
			result = addHiddenAttrs(result, parent);
		}
		// 按钮添加审计功能
		if (btnSwitch) {
			result = addBtnHiddenAttrs(result, parent);
		}
		
		/*start  在请求参数中获取页面token ADD BY tanzl*/
		var $page = $(form).parents(".dialog");
		if($page.length == 0){
			$page = $(form).parents(".unitBox");
		}
		var tokenval=$page.find("input[name='token']").val();
		var tokenKey=$page.find("input[name=jspname]").val();
		//表单提交获取token和校验token标志
		result.data.push({'name':'token','value':tokenval});
		result.data.push({'name':'tokenKey','value':tokenKey});
		result.data.push({'name':'checkTokenFlag','value':'1'});
		/*token end*/
		
		var _submitFn = function() {
			$form.find(':focus').blur();
			
			$.ajax({
				type : form.method || 'POST',
				url : $form.attr("action"),
				data : result.data,
				dataType : "json",
				cache : false,
				async : false, 
				success : function(data){//对返回301用户session过期进行处理，使其返回到登陆页
					if (data.statusCode==DWZ.statusCode.timeout){
						DWZ.loadLogin(data.flag);
					} else {
						callback(data) || DWZ.ajaxDone(data);
					}
				},
				error : function(obj){
					 
				}
			});
		}
		if (confirmMsg) {
			alertMsg.confirm(confirmMsg, {
				okCall : _submitFn
			});
		} else {
			_submitFn();
		}
		return false;
	}

/*	var alertMsg = {
			_boxId: "#alertMsgBox",
			_bgId: "#alertBackground",
			_closeTimer: null,

			_types: {error:"error", info:"info", warn:"warn", correct:"correct", confirm:"confirm"},

			_getTitle: function(key){
				return $.regional.alertMsg.title[key];
			},

			_keydownOk: function(event){
				if (event.keyCode == DWZ.keyCode.ENTER) event.data.target.trigger("click");
				return false;
			},
			_keydownEsc: function(event){
				if (event.keyCode == DWZ.keyCode.ESC) event.data.target.trigger("click");
			},
			*//**
			 * 
			 * @param {Object} type
			 * @param {Object} msg
			 * @param {Object} buttons [button1, button2]
			 *//*
			_open: function(type, msg, buttons){
				$(this._boxId).remove();
				var butsHtml = "";
				if (buttons) {
					for (var i = 0; i < buttons.length; i++) {
						var sRel = buttons[i].call ? "callback" : "";
						butsHtml += DWZ.frag["alertButFrag"].replace("#butMsg#", buttons[i].name).replace("#callback#", sRel);
					}
				}
				var boxHtml = DWZ.frag["alertBoxFrag"].replace("#type#", type).replace("#title#", this._getTitle(type)).replace("#message#", msg).replace("#butFragment#", butsHtml);
				$(boxHtml).appendTo("body").css({top:-$(this._boxId).height()+"px",'z-index':5000}).animate({top:"0px"}, 500);
						
				if (this._closeTimer) {
					clearTimeout(this._closeTimer);
					this._closeTimer = null;
				}
				if (this._types.info == type || this._types.correct == type){
					this._closeTimer = setTimeout(function(){alertMsg.close()}, buttons[0].clearTime || 3500);
				} else {
					$(this._bgId).show();
				}
				
				var jButs = $(this._boxId).find("a.button");
				var jCallButs = jButs.filter("[rel=callback]");
				var jDoc = $(document);
				
				for (var i = 0; i < buttons.length; i++) {
					if (buttons[i].call) jCallButs.eq(i).click(buttons[i].call);
					if (buttons[i].keyCode == DWZ.keyCode.ENTER) {
						jDoc.bind("keydown",{target:jButs.eq(i)}, this._keydownOk);
					}
					if (buttons[i].keyCode == DWZ.keyCode.ESC) {
						jDoc.bind("keydown",{target:jButs.eq(i)}, this._keydownEsc);
					}
				}
				setTimeout(function(){
					function createIframe(target,zindex){
						var iframe = document.createElement('iframe');
						iframe.src = "javascript:false";
						iframe.frameBorder="0";
						iframe.className = 'shoudClose';
						iframe.style.position = "absolute";
						iframe.style.top = "0px";
						iframe.style.left = target.offsetLeft + "px";
						iframe.style.width = target.offsetWidth + "px";
						iframe.style.height = target.offsetHeight + "px";
						iframe.style.zIndex = zindex;
						target.parentNode.appendChild(iframe);
					}
					createIframe(document.getElementById('alertMsgBox'),document.getElementById('alertMsgBox').style.zIndex - 1);
				},480);
			},
			close: function(){
				if($(".shoudClose", navTab.getCurrentPanel()).length){
					$(".shoudClose", navTab.getCurrentPanel()).remove();
				}
				$(document).unbind("keydown", this._keydownOk).unbind("keydown", this._keydownEsc);
				$(this._boxId).animate({top:-$(this._boxId).height()}, 500, function(){
					$(this).remove();
				});
				$(this._bgId).hide();
			},
			error: function(msg, options) {
				this._alert(this._types.error, msg, options);
			},
			info: function(msg, options) {
				this._alert(this._types.info, msg, options);
			},
			warn: function(msg, options) {
				this._alert(this._types.warn, msg, options);
			},
			correct: function(msg, options) {
				this._alert(this._types.correct, msg, options);
			},
			_alert: function(type, msg, options) {
				 // 添加clearTime
				var op = {okName:$.regional.alertMsg.butMsg.ok, okCall:function(){
					if(options){
						options.find("input[class*=required]").each(function(){
							if($(this).val()==""){
								$(this).focus();
								return false;
							}
						});
					}
				},clearTime:null};
				$.extend(op, options);
				var buttons = [
				    // 添加clearTime
					{name:op.okName, call: op.okCall, keyCode:DWZ.keyCode.ENTER,clearTime:op.clearTime}
				];
			   //############################Add LiAnDong Begin########################
				if(msg.indexOf("Http status: 500")>0 || msg.indexOf("Http status: 404")>0){
					msg = "系统出现异常！请联系运维人员。";
				}else if(msg.indexOf("Http status: 12029")>0){
					msg = "您的网络已中断或系统服务器已停止。";
				}else if(msg.indexOf("ajaxOptions: error")>0){
					msg = "服务器运行异常！请联系运维人员。";
				}
			   //############################Add LiAnDong Begin########################
				this._open(type, msg, buttons);
			},
			*//**
			 * 
			 * @param {Object} msg
			 * @param {Object} options {okName, okCal, cancelName, cancelCall}
			 *//*
			confirm: function(msg, options) {
				var op = {okName:$.regional.alertMsg.butMsg.ok, okCall:null, cancelName:$.regional.alertMsg.butMsg.cancel, cancelCall:null};
				$.extend(op, options);
				var buttons = [
					{name:op.okName, call: op.okCall, keyCode:DWZ.keyCode.ENTER},
					{name:op.cancelName, call: op.cancelCall, keyCode:DWZ.keyCode.ESC}
				];
				this._open(this._types.confirm, msg, buttons);
			}
		};*/

	/*function  exit(){ 
		alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
		      navTab.closeCurrentTab();
		 	 }
		 });  
	}*/
	//账单号输入验证
	
	 function checkCountNo(obj){
		 var cc=$(obj).val();
			if( typeof($(obj).val())=='undefined'){
				return true;
			}
			var name = $(obj).val(); 
			var pattern = new RegExp("[《》`~!@#$^&*()=|{}':;',\\[\\]<>/?~！@#￥……&*（）;—|{}【】‘；：”“'。，、？·（+ \ / () * < % ? ;）\\\\]");
			
			 
			var checkName=true;
			var len = 0; 
			 
			var justName=true;
			
			for(var i = 0 ; i < name.length; i++){
				var a = name.charAt(i); 
				//判断是否包含不能包含除数字、字母、汉字、-、_和.之外的其他字符
				 if(pattern.test(a)){
					 checkName=false;
				 }
				//录入的内容不能仅为 - _ .的其中一种或几种字符                  当存在 - _ .之外的字符就不提示 
				 if(a!="."&&a!="-"&&a!="_"){
					 justName=false;
				 }
			}
			 
			
			if(!checkName){
				$(obj).attr("style","border:#ff0000 1px solid");
				  alertMsg.error("不能包含除数字、字母、汉字、-、_和.之外的其他字符");
				  $(obj).val("");
				  return false;
			}else if($(obj).val()!=""&&justName){
				  $(obj).attr("style","border:#ff0000 1px solid");
				  alertMsg.error("录入的内容不能仅为 - _ .的其中一种或几种字符");
				  $(obj).val("");
				  return false;
			}else{
				$(obj).removeAttr("style");
			}
			var re = new RegExp("^[0-9]*$");//^[0-9][0-9a-zA-Z]*$
			var flag="0"; 
			for(var i=0;i<cc.length;i++){
				var ss=cc.charAt(i);
				if (re.test(ss)){
					flag="1";
				}
			}
			if (flag!=1){
				$(obj).attr("style","border:#ff0000 1px solid");
				alertMsg.error("录入的内容必须包含数字!");
				$(obj).val("");
				return false;
			}
				
			return true;
		
		}
	//-------------------------------------自动审核---------------------------------------------------
	 var waitPaymentTime = null;
	 
	 
	 
		//自动审核优化
		 function autoAuditNew(){
				var caseId = $("#caseId",navTab.getCurrentPanel()).val();
				var easyCase = $("#easyCaseId",navTab.getCurrentPanel()).val();
				var auditPermissionName = $("#autoauditPermissionName", navTab.getCurrentPanel()).val();
				var calcPayFlag = $("#calcPayFlag",navTab.getCurrentPanel()).val();
				var autoAuditSaveStop = 1;
				if(easyCase!=null && easyCase!=""){//普通审核
					autoAuditSaveStop = 0;
				}
				//自动审核,处理匹配理算、处理保项赔付结论、豁免处理、合同处理结论、保单结算
				$.ajax({
					'url':'clm/audit/autoAuditSave_CLM_claimAutoAuditAction.action?caseId='+caseId+"&easyCase="+easyCase+"&autoAuditSaveStop="+autoAuditSaveStop+"&calcPayFlag="+calcPayFlag,
					'type':'POST',
					'datatype':'json',
					'success':function(data){	
						var data=eval("("+data+")");			
						if(data[0].message1=="1"){
							if(easyCase!=null && easyCase!=""){//普通审核
								if (data[0].autoAuditStatus=="change") {
									alertMsg.confirm("当前审核结论已超出您的处理权限，将释放回审核共享池。",{
										okCall:function(){
											//审核任务释放回审核共享池
											releaseAuditTask(caseId);
										},
										cancelCall : function() {
												//将赔案审核权限还原
											$.ajax({
												url : "clm/audit/reChangePermission_CLM_claimMatchResultAction.action?claimCaseVO.auditPermissionName="+auditPermissionName+"&claimCaseVO.caseId="+caseId,
												type : "POST",
												dataType : "json", 
												success : function (json){
													if (json.statusCode == DWZ.statusCode.ok) {
														autoAuditSaveStop = 1;
														$.ajax({
															url : "clm/audit/autoAuditSave_CLM_claimAutoAuditAction.action?caseId="+caseId+"&easyCase="+easyCase+"&autoAuditSaveStop="+autoAuditSaveStop,
															type : "POST",
															dataType : "json", 
															success : function (json){
																commoncaseautoAuditSave(caseId,data);																
															}
														});
													}
												}
											});
											
									   }
									});
								}else{
									commoncaseautoAuditSave(caseId,data);
								}
							}else{ //简易案件
								var flag = false;
								$.ajax({
						  			'type':'post',
						  			'url':'clm/register/judgeIfInfo_CLM_claimMatchCalcAction.action?caseId='+caseId,
						  			'datatype':'json',
						  			'async':false,
						  			'success':function(data){
						  				var data = eval("(" + data + ")");
					 	  				for(var kay in data){
					 	  					//如果kay为1则阻断，2不阻断，为了以后配置新产品提示信息
					 	  					if (kay=="1") {
					 	  						flag = true;
					 	  						alertMsg.error(data[kay]);
					 	  						return false;
					 						}
					 	  					if (kay=="2") {
					 	  						alertMsg.info(data[kay]);
					 	  					}
					 	  					if (kay=="485") {
					 	  						setTimeout(function(){
					 	  						alertMsg.info(data[kay]);
					 	  						},2000) ;
					 	  					}
					 	  				}
						  			},
						  			'error':function(){
						  				alert("出错了！");
						  			}
						  		}); 
								if (flag) {
									return;
								}
								$("#3", navTab.getCurrentPanel()).attr("href","clm/audit/claimMatchResult_CLM_claimMatchResultAction.action?caseId="+caseId+"&isCalc=0&easyCase=");
								$("#3",navTab.getCurrentPanel()).css("display","block");
								//调用选项卡的click方法，将页面跳转至下个选项卡中
								var auditMatchRel = $("#caseQueryTabs3", navTab.getCurrentPanel());
								//重新加载展示最新数据
								auditMatchRel.loadUrl($("#3",navTab.getCurrentPanel()).attr("href"));
								//页面加载了不在进行click(); 本次任务进入审核click()有效，本次任务如果经历此次页面，click()事件没有初始化
								//调用选项卡的click方法，将页面跳转至下个选项卡中
								$("#3",navTab.getCurrentPanel()).click();
								
								//用于进度条显示
								$("#step3",navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
								$("#step2",navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
								$("#n3",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
								$("#n2",navTab.getCurrentPanel()).find("div").attr("class","main_later");
								$("#step2",navTab.getCurrentPanel()).find("div").attr("class","main_laterline");									
									if(next_autoAuditNew('4',caseId)){

										var antiMoneyFlag = true;
										$.ajax({
											'type':'post',
											'url':'clm/register/checkAntiMoneyLaunderingRequired_CLM_toBenefitInputPageAction.action?caseId='+caseId,
											'datatype':'json',
											'async':false,
											'success':function(data){
												var data = eval("(" + data + ")");
												claimtype=data.ClaimType1;
												for(var kay in data){
													if (data[kay] == "1") {
														alertMsg.info("赔案的最终给付金额大于等于1万，请录入被保险人身份基本信息");
														antiMoneyFlag = false;
														return false;
													}
												}
											}
											
										});
										
										if(!antiMoneyFlag){
											return false;
										}
										if (next_autoAuditNew('5',caseId)) {
											return true;
										} 
										return true;
									}
									
							}
						}else if(data[0].message1=="0"){
							alertMsg.error("自动审核处理失败！");
						}
					}
				});
				
			}
		 
		 
		 
		 
		//跳转到下一个选项卡方法，参数id为要跳转到的选项卡的Id，参数form为跳转前选项卡的form的id
		 function next_autoAuditNew(id,caseId){
		 	var easyCase = $("#easyCaseId",navTab.getCurrentPanel()).val();
		 	if(easyCase!=null && easyCase!=""){
		 		if((id-lastNum*1)>1){
		 			lastNum=lastNum*1+id*1-lastNum*1-1;
		 		}
		 		 
		 		if(id > (lastNum*1+1)){
		 			lastNum++;
		 		}
		 		if(id=='5'){
		 			//++count;
		 			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/audit/remitInit_CLM_remitManageAction.action?caseId="+caseId+"&flag=0");
		 		}
		 		if(id=='6'){
		 			
		 			//++count;
		 			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/audit/contractDealInit_CLM_contractDealAction.action?caseId="+caseId);
		 		}
		 		if(id=='7'){
	 	 			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/paymentplan/paymentPlanView_CLM_paymentPlanAction.action?paymentPlanVO.caseId="+caseId+"&easyCase=1");
		 		}
		 		
		 		if(id=='8'){
		 			$.ajax({
		 				url : "clm/audit/summarizingPayMentInfo_CLM_contractDealAction.action?caseId="+caseId,
		 				global : false,
		 				type : "POST",
		 				dataType : "json",
		 				async : false,
		 				success : function (json){
//		 					return true;
		 				}
		 			});
		 			
		 			//判断该赔案下的分期计划是否有数据，若没有计划，则更新受益分配中的分期标识为否
		 			var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		 			//方法执行完成标识
		 			var judgeSuccessFlag = false;
		 			$.ajax({
		 				url : "clm/paymentplan/judgeIsInstalment_CLM_paymentPlanAction.action?claimPayVO.caseId="+caseId+"&claimPayVO.advanceFlag=0",
		 				global : false,
		 				type : "POST",
		 				dataType : "json",
		 				async : false,
		 				success : function (json){
		 					judgeSuccessFlag = true;
		 				}
		 			});
		 			if(judgeSuccessFlag){
		 				//判断所有的理赔金与结算项是否分配完
		 				var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		 				if(caseId!=null&&caseId!=""){
		 					//控制是否返回
			 				var flag = false;
			 				$.ajax({
			 					url : "clm/paymentplan/assignMoneyFinish_CLM_paymentPlanAction.action?claimPayVO.caseId="+caseId+"&claimPayVO.advanceFlag=0",
			 					global : false,
			 					type : "POST",
			 					dataType : "json",
			 					async : false,
			 					success : function (json){
			 						if (json.statusCode == DWZ.statusCode.ok) {
			 							flag = true;
			 							$("#"+id, navTab.getCurrentPanel()).attr("href","clm/audit/auditConclusionInit_CLM_auditConclusionAction.action?caseId="+caseId+"&easyCase=1");
			 						} else {
			 							flag = false;
			 							alertMsg.error(json.message);
			 							return false;
			 						}
			 					}
			 				});
			 				if (!flag) {
			 					lastNum--;
			 					return false;
			 				}
		 				}
		 			}
		 			
		 		}
		 	}else{
		 		if((id-lastNum*1)>1){
		 			lastNum=lastNum*1+id*1-lastNum*1-1;
		 		}
		 		 
		 		if(id > (lastNum*1+1)){
		 			lastNum++;
		 		}
		 		count = id-1+1;
		 		if(id=='4'){
	 	 			$("#"+id, navTab.getCurrentPanel()).attr("href","clm/paymentplan/paymentPlanView_CLM_paymentPlanAction.action?paymentPlanVO.caseId="+caseId+"&easyCase=");
		 		}
		 		
		 		if(id=='5'){
		 			$.ajax({
		 				url : "clm/audit/summarizingPayMentInfo_CLM_contractDealAction.action?caseId="+caseId,
		 				global : false,
		 				type : "POST",
		 				dataType : "json",
		 				async : false,
		 				success : function (json){
		 				}
		 			});
		 			var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		 			//方法执行完成标识
		 			var judgeSuccessEasyFlag = false;
		 			$.ajax({
		 				url : "clm/paymentplan/judgeIsInstalment_CLM_paymentPlanAction.action?claimPayVO.caseId="+caseId+"&claimPayVO.advanceFlag=0",
		 				global : false,
		 				type : "POST",
		 				dataType : "json",
		 				async : false,
		 				success : function (json){
		 					judgeSuccessEasyFlag = true;
		 				}
		 			});
		 			
		 			if(judgeSuccessEasyFlag){
		 				//判断所有的理赔金与结算项是否分配完
		 				var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		 				//控制是否返回
		 				var flag = false;
		 				$.ajax({
		 					url : "clm/paymentplan/assignMoneyFinish_CLM_paymentPlanAction.action?claimPayVO.caseId="+caseId+"&claimPayVO.advanceFlag=0",
		 					global : false,
		 					type : "POST",
		 					dataType : "json",
		 					async : false,
		 					success : function (json){
		 						if (json.statusCode == DWZ.statusCode.ok) {
		 							flag = true;
		 							$("#"+id, navTab.getCurrentPanel()).attr("href","clm/audit/auditConclusionInit_CLM_auditConclusionAction.action?caseId="+caseId);
		 						} else {
		 							flag = false;
		 							alertMsg.error(json.message);
		 							return false;
		 						}
		 					}
		 				});
		 				if (!flag) {
		 					return false;
		 				}
		 			}
		 		}
		 	}
		 	//将要跳转到的隐藏的选项卡显示
		 	$("#"+id,navTab.getCurrentPanel()).css("display","block");
		 	//调用选项卡的click方法，将页面跳转至下个选项卡中
		 	$("#"+id,navTab.getCurrentPanel()).click();
		 	
		 	//防止第一次进入审核 自动审核到最后页面，click()不刷新当前页面
		 	var rel = $("#caseQueryTabs"+id, navTab.getCurrentPanel());
			if(rel.html()!=""){
				//下一步重新加载
				rel.loadUrl($("#"+id,navTab.getCurrentPanel()).attr("href"));
			}
		 	
		 	//用于进度条显示
		 	$("#step"+id,navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
		 	$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
		 	$("#n"+id,navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
		 	$("#n"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_later");
		 	$("#step"+(id-1),navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
		 	//用于进度条显示
			return true;
		 	
		 }
		 
		 function commoncaseautoAuditSave(caseId,dataValue){
			//调查合议二核页面可跳过 没有初始化的保存数据，或者下一步校验,不加载但是需要展示标签
				$("#3",navTab.getCurrentPanel()).css("display","block");
//				判断是否需要提示信息且进行匹配理算
				var flag = false;
				$.ajax({
		  			'type':'post',
		  			'url':'clm/register/judgeIfInfo_CLM_claimMatchCalcAction.action?caseId='+caseId,
		  			'datatype':'json',
		  			'async':false,
		  			'success':function(data){
		  				var data = eval("(" + data + ")");
	 	  				for(var kay in data){
	 	  					//如果kay为1则阻断，2不阻断，为了以后配置新产品提示信息
	 	  					if (kay=="1") {
	 	  						flag = true;
	 	  						alertMsg.error(data[kay]);
	 	  						return false;
	 						}
	 	  					if (kay=="2") {
	 	  						alertMsg.info(data[kay]);
	 	  					}
	 	  					if (kay=="485") {
	 	  						setTimeout(function(){
	 	  						alertMsg.info(data[kay]);
	 	  						},2000) ;
	 	  					}
	 	  				}
		  			},
		  			'error':function(){
		  				alert("出错了！");
		  			}
		  		}); 
				//阻断提示 直接返回
				if (flag) {
					return;
				}
				//isCalc=0只进行初始化，不进行匹配理算处理
				$("#4", navTab.getCurrentPanel()).attr("href","clm/audit/claimMatchResult_CLM_claimMatchResultAction.action?caseId="+caseId+"&isCalc=0&easyCase=1");
				$("#4",navTab.getCurrentPanel()).css("display","block");
				$("#4",navTab.getCurrentPanel()).click();
				
				//防止第一次进入审核 自动审核到最后页面，click()不刷新当前页面
			 	var rel = $("#caseQueryTabs4", navTab.getCurrentPanel());
				 
				if(rel.html()!=""){
			//下一步重新加载
					rel.loadUrl($("#4",navTab.getCurrentPanel()).attr("href"));
				}
				
				//用于进度条显示
				$("#step4",navTab.getCurrentPanel()).find("div").attr("class","main_step"); 
				$("#step3",navTab.getCurrentPanel()).find("div").attr("class","main_laterline");	
				$("#n4",navTab.getCurrentPanel()).find("div").attr("class","main_n1d");		 
				$("#n3",navTab.getCurrentPanel()).find("div").attr("class","main_later");
				$("#step3",navTab.getCurrentPanel()).find("div").attr("class","main_laterline");
				//查询产品规则消息提醒
				$.ajax({
		  			'type':'post',
		  			'url':'clm/audit/queryProductRuleMessage_CLM_claimMatchResultAction.action?caseId='+caseId,
		  			'datatype':'json',
		  			'async':false,
		  			'success':function(data){
		  				var data = eval("(" + data + ")");
		  				for(var kay in data){
//							var taskId = data[i];
		  					if (kay=="1") {
		  						alertMsg.confirm(data[kay], {
		  							okCall : function() {
		  								flag = false;
		  							},
		  							cancelCall : function() {
		  								flag = true;
		  								alertMsg.error("请等待首次癌症放化疗保险金给付后继续操作！");
		  							}
		  						});
		  						return false;
							}
		  					if (kay=="3") {
		  						alertMsg.confirm(data[kay], {
		  							okCall : function() {
		  								flag = false;
		  							},
		  							cancelCall : function() {
		  								flag = true;
		  								alertMsg.error("请等待首次质子重离子治疗及靶向治疗保险金给付后继续操作！");
		  							}
		  						});
		  						return false;
							}
		  					if (kay=="2") {
		  						flag = true;
		  						alertMsg.error(data[kay]);
		  						return false;
		  					}
		  					if (kay=="3") {
		  						flag = true;
		  						alertMsg.error(data[kay]);
		  						return false;
		  					}
		  					if (kay=="5") {
		  						flag = true;
		  						alertMsg.error(data[kay]);
		  						return false;
		  					}
						}
		  			},
		  			'error':function(){
		  				alert("出错了！");
		  			}
		  		});
				if(flag){
					return;
				}
				//豁免页面可跳过 没有初始化的保存数据，或者下一步校验，展示出来就行
				if(dataValue[0].isWaived=="1"){
					$("#5",navTab.getCurrentPanel()).css("display","block");
					next_autoAuditNew('5',caseId);
				}
				if (next_autoAuditNew('6',caseId)) {
					waitPaymentTime = new Date().getTime();
						if(next_autoAuditNew('7',caseId)){
							var forFlag = true;
							var rel = $("#caseQueryTabs7", navTab.getCurrentPanel());
					 		while(forFlag){
					 			if(rel.html()!=""){
					 				forFlag = false;
					 				break;
					 			}else{
					 				var newDateFlag =  new Date().getTime();
							 		if((newDateFlag-waitPaymentTime)>4000){
							 			forFlag = true;
										 break;
									}
						 		}
					 		}

					 		var antiMoneyFlag = true;
					 		$.ajax({
					 			'type':'post',
					 			'url':'clm/register/checkAntiMoneyLaunderingRequired_CLM_toBenefitInputPageAction.action?caseId='+caseId,
					 			'datatype':'json',
					 			'async':false,
					 			'success':function(data){
					 				var data = eval("(" + data + ")");
					 				claimtype=data.ClaimType1;
					 				for(var kay in data){
					 					if (data[kay] == "1") {
					 						alertMsg.info("赔案的最终给付金额大于等于1万，请录入被保险人身份基本信息");
					 						antiMoneyFlag = false;
					 						return false;
					 					}
					 				}
					 			}
					 			
					 		});
					 		if(!antiMoneyFlag){
					 			return false;
					 		}
					 		
							if (next_autoAuditNew('8',caseId)) {
								return true;
							} 
						}
					
				} 
		 }
		 
		 
		//审核任务释放回共享池
			function releaseAuditTask(caseId){
				var auditTaskId = $("#auditTaskId", navTab.getCurrentPanel()).val();
				$.ajax({
					url : "clm/audit/releaseAuditTask_CLM_claimMatchResultAction.action?claimCaseVO.caseId="+caseId+"&claimCaseVO.taskId="+auditTaskId,
					type : "POST",
					dataType : "json", 
					success : function (s){
						if (s.statusCode == DWZ.statusCode.ok) {
							//释放成功，关闭当前页面
							navTab.closeCurrentTab();
							//重新分配后刷新访问理赔工作台，防止重复点击
							//刷新访问工作台，保持原有数据
					       $("a[title='访问理赔工作台']").find("span").unbind().click(function(){
					    	   var flashFlag = false;
						     	queryWorkMsgData();  
						     	if($("#auditTask", navTab.getCurrentPanel()).html()!=null){
							     	var selectVal = $("#problemCheckListId", navTab.getCurrentPanel()).val();
							     	var selectPriorityClaimVal = $("#priorityClaimListId", navTab.getCurrentPanel()).val();
							     	$.ajax({
							     		'url':'clm/clmWorkPlatform/problemCheckListCount_CLM_clmWorkPlatformAction.action?problemVal='+selectVal+"&priorityClaimVal="+selectPriorityClaimVal,
							     		'type':'post',
							     		'datatype':'json',
							     		async: false,
							     		'success':function(data){
							     			var count = eval("("+data+")");
							     			$("#auditTaskId", navTab.getCurrentPanel()).text("审核("+count+")");
							     			flashFlag = true;
							     		}
							     	});
							     	if(flashFlag){
							     		var rel = $("#personalTaskDetail", navTab.getCurrentPanel());
						     			rel.loadUrl("clm/clmWorkPlatform/queryAuditTaskMessages_CLM_clmWorkPlatformAction.action?problemVal="+selectVal+"&priorityClaimVal="+selectPriorityClaimVal);
							     	}
						     	} 
					       });
					       $("a[title='访问理赔工作台']").find("span").click();
						} else {
							//释放失败，提示错误信息
							alertMsg.error(s.message);
						}
					}
				});
			}
	 
	 