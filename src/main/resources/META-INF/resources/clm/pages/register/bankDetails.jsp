<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<script type="text/javascript">
	//查询
	function querybankDetail(){
		if($("#pageBankName",$.pdialog.getCurrent()).val().trim() == ""){
			alertMsg.info("缺少银行信息，请录入银行代码后查询开户行信息！");	
			return;
		}
		$("#bankOfDepositForm",$.pdialog.getCurrent()).submit();
		/*  if($("#pageBankName",$.pdialog.getCurrent()).val().trim() != "" && $("#correspondentNoCom",$.pdialog.getCurrent()).val().trim() == ""&&$("#correspondentNameCom",$.pdialog.getCurrent()).val().trim() == ""){
			alertMsg.info("请录入查询条件!");	
			return;
		}else{
			
		} 
		if(currentPage.PageItems == null || currentPage.PageItems.size()==0){
			alertMsg.info("系统中不存在该记录，请重新录入查询条件");	
			return;
		} 
		if($("#correspondentNoCom",$.pdialog.getCurrent()).val().trim() == ""&&$("#correspondentNameCom",$.pdialog.getCurrent()).val().trim() == ""){
			alertMsg.info("请录入查询条件");	
			return;
		} */
	}
	//选择 获取当前行的值
	function Radio(k){
		 var trs = $("#bankOfDepositTbodyId",$.pdialog.getCurrent()).find("tr");
		 for(var i = 0; i < trs.length; i++){
			 if($("#bankOfDepositTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("input").val() == "1"){
				 $("#bankOfDepositTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("input").val(0);
			 }
		 }
		 $(k).val(1); //为选中的附上1的值，以此来判断是选中还是没有选中
		 var bankCode = $(k).parent().parent().find("td:eq(1)").text();
		 var bankName = $(k).parent().parent().find("td:eq(2)").text();
		 var correspondentNo = $(k).parent().parent().find("td:eq(3)").text();
		 var correspondentName = $(k).parent().parent().find("td:eq(4)").text();
		 $("#newBankCodeId",$.pdialog.getCurrent()).val(bankCode);
		 $("#bankNameId",$.pdialog.getCurrent()).val(bankName);
		 $("#correspondentNoId",$.pdialog.getCurrent()).val(correspondentNo);
		 $("#correspondentNameId",$.pdialog.getCurrent()).val(correspondentName);
	}
	//带回信息
	function bankOfDepositF(){
		var flag = "0";
		 var trs = $("#bankOfDepositTbodyId",$.pdialog.getCurrent()).find("tr");
		 for(var i = 0; i < trs.length; i++){
			 if($("#bankOfDepositTbodyId",$.pdialog.getCurrent()).find("tr:eq("+i+")").find("td:eq(0)").find("input").val() == "1"){
				 flag = "1";
				 var bankCode = $("#newBankCodeId",$.pdialog.getCurrent()).val();
				 var bankName = $("#bankNameId",$.pdialog.getCurrent()).val();
				 var correspondentNo =  $("#correspondentNoId",$.pdialog.getCurrent()).val();
				 var correspondentName = $("#correspondentNameId",$.pdialog.getCurrent()).val();
					$.bringBack({
						name:correspondentName,
						bankOfDeposit:correspondentNo
				    });
				
			}
		 }
			 if(flag != "1"){
				 alertMsg.error("至少选择一条!");
			 }
	}
</script>

<form id="pagerForm" action="clm/register/bankDetails_CLM_toBenefitInputPageAction.action?leftFlag=0" method="post">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<div layoutH="50">
	<form id="bankOfDepositForm" class="pagerForm required-validate" rel="pagerForm" action="clm/register/bankDetails_CLM_toBenefitInputPageAction.action?leftFlag=0" 
	method="post" onsubmit="return dialogSearch(this);">
	
	<div class="pageFormInfoContent">
		<input type="hidden" id="newBankCodeId">
		<input type="hidden" id="bankNameId">
		<input type="hidden" id="correspondentNoId">
		<input type="hidden" id="correspondentNameId">
		<!-- 显示主页面录入的银行 -->
		<dl>
			<dt>银行</dt>
			<dd>
				<input id="pageBankName" name="bankOfDepositVO.pageBankName" value="${pageBankName}"  readonly="readonly" disabled/>
			</dd>
		</dl>
		<!-- 查询条件的输入框 -->
	 	<input type="hidden" id="pageBankCodeCom" name="bankOfDepositVO.bankCode" value="${bankOfDepositVO.bankCode}" /> 
		<dl>
			<dt>开户行名称</dt>
			<dd>
				<input type="text" id="correspondentNameCom" name="bankOfDepositVO.correspondentName" value="${currentPage.paramObject.correspondentName}"/>
			</dd>
		</dl>
		<dl>
			<dt>联行号</dt>
			<dd>
				<input type="text" id="correspondentNoCom" name="bankOfDepositVO.correspondentNo" value="${currentPage.paramObject.correspondentNo}"/>
			</dd>
		</dl>
		<div class="pageFormdiv">
			<button class="but_blue" type="button" onclick="querybankDetail();">查询</button>
		</div>
	</div>
		<div class="tabdivclassbr main_tabdiv">
		<table class="list main_dbottom" width="100%" align="center" border="0" cellspacing="0" cellpadding="0'">
								<thead>
									<tr>
										<th nowrap>选择</th>
										<th nowrap>银行代码</th>
										<th nowrap>银行名称</th>
										<th nowrap>联行号</th>
										<th nowrap>开户行名称</th>
									</tr>
								</thead>
								<tbody id="bankOfDepositTbodyId">
								<s:if test="pageBankName == null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">缺少银行信息，请录入银行代码后查询开户行信息！</div>
								</td>
							</tr>
						</s:if>
								<s:elseif test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请录入查询条件！</div>
								</td>
							</tr>
						</s:elseif>
						<s:elseif test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">系统中不存在该记录，请重新录入查询条件！</div>
								</td>
							</tr>
						</s:elseif>
									<s:iterator value="currentPage.PageItems" status="VOList">
										<tr>
											<td align="center"><input name="radio" type="radio" onclick="Radio(this)" value="0"></td>
											<td align="center"><s:property value="bankCode" /></td>
											<td align="center"><s:property value="bankName" /></td>
											<td align="center"><s:property value="correspondentNo" /></td>
											<td align="center"><s:property value="correspondentName" /></td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
							<div class="panelBar">
								<div class="pages">
									<span>显示</span><!-- onchange="dialogPageBreak({numPerPage:this.value})" -->
									<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50',100:'100'}"
										name="select" onchange="dialogPageBreak({numPerPage:this.value})"
										value="currentPage.pageSize">
									</s:select>
									<span>条，共${currentPage.total}条</span>
								</div>
								<div class="pagination" targetType="dialog"
									totalCount="${currentPage.total}"
									numPerPage="${currentPage.pageSize}" pageNumShown="10"
									currentPage="${currentPage.pageNo}"></div>
								</div>
							</div>
					</form>
		<div class="formBarButton main_popup" >
				<ul >
					<li>
						<button class="but_blue" type="button" onclick="bankOfDepositF()">确认</button>
					</li>
					<li>
						<button class="close but_gray" type="submit" >关闭</button>
					</li>
				</ul>
			</div>
</div>

