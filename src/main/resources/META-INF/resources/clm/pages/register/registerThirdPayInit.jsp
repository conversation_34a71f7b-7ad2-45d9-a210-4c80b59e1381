<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%
	String accType = request.getParameter("accType");
	String id = request.getParameter("id");
%>

<!--账单及费用明细  div start -->


<script type="text/javascript" language="javascript">
 
  function  inspectionBIll(obj2){
	
	  var obj=$("#sumAmountForBill"+obj2, navTab.getCurrentPanel());
	 
	  var dateIdForBill=$("#dateIdForBill"+obj2, navTab.getCurrentPanel()).val();
	  
	  if(!checkCountNo($("#dateIdForBill"+obj2, navTab.getCurrentPanel()))){
		  return false;
	  } 
	  
	  var caseId=$("#caseIdForBill"+obj2, navTab.getCurrentPanel()).val();
// 	  if(caseStatus!="31"){
		  if(caseId!=""&&dateIdForBill!=""){
			  $.ajax({
					'type':'post',
					'url':'clm/register/inspectionBIll_CLM_tClaimDutyRegisterAction.action?claimBillVO.caseId='+caseId+"&claimBillVO.billNo="+dateIdForBill+"&claimBillPaidVO.sumAmount="+$(obj).val(),
				    'datatype':'json', 
					'success':function(data){
						 
						var data = eval("(" + data + ")");
						if(data.statusCode == '300') { 
							alertMsg.error(data.message); 
							if(obj2=="Social"){
								$("#sumAmountForBillSocial").attr("value","");
							}else if(obj2=="ThirdPay"){
								$("#sumAmountForBillThirdPay").attr("value","");
							}
						} 
					} ,'error':function(){
						alertMsg.error("查询失败!");
					}
				}); 
		  }   
// 	  }
  }
  
  
 
  
</script>

<div class="panelPageFormContent">

	<s:if test=" pageFlag  eq 'socialPay'">
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">社保给付信息录入
					</h1>
				</div>
		<div class="panelPageFormContent" id="dutyHospitalCostInitDetails">

			<form class="pageForm required-validate" id="${pageFlag}FormId"
				action="clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
				method="post"
				onsubmit="return validateCallback(this,dutyAjaxDone)">
				<input type="hidden" name="claimsocialSBillPaidVOlist[0].caseId" id="caseIdForBillSocial"
					value="${caseId}"> <input type="hidden"
					name="claimsocialSBillPaidVOlist[0].listId"
					value="${claimBillPaidVOlist[0].listId}"> 
					<input
					type="hidden" name="claimTypecode[0]" value="5"> 
					<input
					type="hidden" name="otherTypecode[0]" value="0"> 
					<input type="hidden" name="pageFlag" value="${pageFlag}">
                    <input type="hidden"   name="caseId"  value="${caseId}"/>  
				<dl >
					<dt >
						<font class="point" color="red">* </font>费用明细项目
					</dt>
					<dd >  
						<Field:codeTable name="claimsocialSBillPaidVOlist[0].othFeeItem"  cssClass="combox title"
							tableName="APP___CLM__DBUSER.T_OTHER_FEE_ITEM" 
							value="${claimBillPaidVOlist[0].othFeeItem}" />
					</dd>
				</dl>
				<dl >
					<dt >
						<font class="point" color="red">* </font>费用代码
					</dt>
					<dd >
						<Field:codeTable name="claimsocialSBillPaidVOlist[0].feeCode" cssClass="combox title"  value="${claimBillPaidVOlist[0].feeCode }"
							tableName="APP___CLM__DBUSER.T_CLAIM_FEE_CODE"   
							whereClause="code =  '1' " />
					</dd>
				</dl>
				<dl >
					<dt >给付类型</dt>
					<dd >
						<Field:codeTable name="claimsocialSBillPaidVOlist[0].paidType" orderBy="code" id="viewInputSocialSelect" cssClass="combox title" onChange="viewInputValue(this)"
							tableName="APP___CLM__DBUSER.T_SOCIAL_PAY_TYPE"  whereClause="code not in ('10')"
							value="${claimBillPaidVOlist[0].paidType}" />
					</dd>
				</dl>
				
				<dl sizset="0">
					<dt ><font class="point" color="red">* </font>收据编号</dt>
					<dd sizset="0">
						<!--数据编号        必录  class="required"  -->
						<input name="claimsocialSBillPaidVOlist[0].dataId"  id="dateIdForBillSocial"    onchange="inspectionBIll('Social')"
							value="${claimBillPaidVOlist[0].dataId }"
							class="textInput" type="text" maxLength="30" />
					</dd>
				</dl>
				
				<dl sizset="0">
					<dt >就诊号</dt>
					<dd sizset="0">
						<!--数据编号        必录  class="required"  -->
						<input name="claimsocialSBillPaidVOlist[0].diagnosisNum"  id="diagnosisNumForBillSocial" 
							value="${claimBillPaidVOlist[0].diagnosisNum }"
							class="textInput" type="text" maxLength="30" />
					</dd>
				</dl>
				
				  <dl  sizset="1">
					<dt ><font class="point" color="red">* </font>医疗总费用</dt>
					<dd sizset="1">
						<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].sumAmount"  id="sumAmountForBillSocial" onchange="inspectionBIll('Social')"
							value="${claimBillPaidVOlist[0].sumAmount }"
							class="number textInput" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl  sizset="2">
					<dt ><font class="point" color="red">* </font>新农合报销金额</dt>
					<dd sizset="2">
						<input name="claimsocialSBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }"   class="number textInput selectSocialView selectSocialView2" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt ><font class="point" color="red">* </font>医疗保险基金支付总金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].paidAmount"
							value="${claimBillPaidVOlist[0].paidAmount }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView10 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >统筹基金支付总金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].tcFeeTotal"
							value="${claimBillPaidVOlist[0].tcFeeTotal }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >医保基金支付金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].ybjjFee"
							value="${claimBillPaidVOlist[0].ybjjFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >职工大额补助金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].zgdeFee"
							value="${claimBillPaidVOlist[0].zgdeFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >居民大病支付金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].jmdbFee"
							value="${claimBillPaidVOlist[0].jmdbFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >公务员医疗补助金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].gwyFee"
							value="${claimBillPaidVOlist[0].gwyFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >医疗救助支付金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].yljzFee"
							value="${claimBillPaidVOlist[0].yljzFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >优抚补助金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].yfbzFee"
							value="${claimBillPaidVOlist[0].yfbzFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >财政兜底金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].czddFee"
							value="${claimBillPaidVOlist[0].czddFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >其他基金支付金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].qtjjFee"
							value="${claimBillPaidVOlist[0].qtjjFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >单病种标识</dt>
					<dd sizset="3">
						<Field:codeTable cssClass="combox title selectSocialView selectSocialView8 selectSocialView1 selectSocialView9"  name="claimsocialSBillPaidVOlist[0].dbzFlag"
								tableName="APP___CLM__DBUSER.T_YES_NO" 
								value="${claimBillPaidVOlist[0].dbzFlag}" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >医院分担金额</dt>
					<dd sizset="3">
						<input name="claimsocialSBillPaidVOlist[0].hospitalFee"
							value="${claimBillPaidVOlist[0].hospitalFee }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="3">
					<dt >异地就医标识</dt>
					<dd sizset="3">
						<Field:codeTable cssClass="combox title"  name="claimsocialSBillPaidVOlist[0].differentAreaFlag"
								tableName="APP___CLM__DBUSER.T_YES_NO" 
								value="${claimBillPaidVOlist[0].differentAreaFlag}" />
					</dd>
				</dl>
				
				<dl sizset="4">
					<dt >
						<font class="point" color="red">* </font>服务机构名称
					</dt>
					<dd sizset="4">
						<input name="claimsocialSBillPaidVOlist[0].serviceOrgName"
							value="${claimBillPaidVOlist[0].serviceOrgName }"
							class=" textInput" type="text" maxLength="200" />
					</dd>
				</dl>
				  
				<dl sizset="13">
					<dt >自付1</dt>
					<dd sizset="13">
						<!--自付1     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].selfPay1"
							value="${claimBillPaidVOlist[0].selfPay1 eq null?0:claimBillPaidVOlist[0].selfPay1  }"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView10 selectSocialView9 selectSocialView2" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="14">
					<dt >自付2</dt>
					<dd sizset="14">
						<!--自付2     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].selfPay2"
							value="${claimBillPaidVOlist[0].selfPay2 eq null?0:claimBillPaidVOlist[0].selfPay2}"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView10 selectSocialView9 selectSocialView2" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="15">
					<dt >自费</dt>
					<dd sizset="15">
						<!--自费     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].ownExpense"
							value="${ claimBillPaidVOlist[0].ownExpense eq null?0:claimBillPaidVOlist[0].ownExpense}"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView10 selectSocialView9 selectSocialView2" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="17">
					<dt >医保起付线费用</dt>
					<dd sizset="17">
						<!--自费     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].medicarePayLine"
							value="${ claimBillPaidVOlist[0].medicarePayLine eq null?0:claimBillPaidVOlist[0].medicarePayLine}"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView10 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="16">
					<dt >备注</dt>
					<dd sizset="16">
						<input name="claimsocialSBillPaidVOlist[0].remark"
							value="${claimBillPaidVOlist[0].remark }" class="textInput"
							type="text" size="12" />
					</dd>
				</dl>
				<dl sizset="18">
					<dt >个人账户支付</dt>
					<dd sizset="18">
						<!--个人账户支付   必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].accountFundMoney"
							value="${claimBillPaidVOlist[0].accountFundMoney eq null?0:claimBillPaidVOlist[0].accountFundMoney}"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView2" type="text" maxLength="18" />
					</dd>
				</dl>
			</form>

		</div>
		<!--医疗费用明细  div end  -->
		<script type="text/javascript" language="javascript">

		claimFireEvent($("#viewInputSocialSelect", navTab.getCurrentPanel()) );
		
function viewInputValue(obj){
	$(".selectSocialView", navTab.getCurrentPanel()).parent().parent().css("display","none");
	$(".selectSocialView", navTab.getCurrentPanel()).attr("disabled","disabled");
	$(".selectSocialView"+$(obj).val(), navTab.getCurrentPanel()).parent().parent().css("display","");
	$(".selectSocialView"+$(obj).val(), navTab.getCurrentPanel()).removeAttr("disabled");
}
</script>
	</s:if>

	<s:if test=" pageFlag  eq 'registerThirdPay'">
		<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">社保第三方给付录入
					</h1>
				</div>
		<div class="pageFormContent panelContent"
			id="dutyHospitalCostInitDetails" sizset="0">
			<form class="pageForm required-validate" id="${pageFlag}FormId"
				action="clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
				method="post"
				onsubmit="return validateCallback(this,dutyAjaxDone)">
				<input type="hidden" name="claimBillPaidVOlist[0].caseId" id="caseIdForBillThirdPay"
					value="${caseId}" > 
					
					 <input type="hidden"
					name="claimBillPaidVOlist[0].listId"
					value="${claimBillPaidVOlist[0].listId}"> 
					<input
					type="hidden" name="claimTypecode[0]" value="5"> 
					<input
					type="hidden" name="otherTypecode[0]" value="1"> 
					<input type="hidden" name="pageFlag" value="${pageFlag}">
                <input type="hidden"   name="caseId"  value="${caseId}">

				<dl >
					<dt >
						<font class="point" color="red">* </font>费用明细项目
					</dt>
					<dd >
						<Field:codeTable name="claimBillPaidVOlist[0].othFeeItem" cssClass="combox title"
							tableName="APP___CLM__DBUSER.T_OTHER_FEE_ITEM"  
							value="${claimBillPaidVOlist[0].othFeeItem}" />
					</dd>
				</dl>
				<dl >
					<dt >
						<font class="point" color="red">* </font>费用代码
					</dt>
					<dd >
						<Field:codeTable name="claimBillPaidVOlist[0].feeCode" cssClass="combox title"
							tableName="APP___CLM__DBUSER.T_CLAIM_FEE_CODE"  
							whereClause="code = '2'" />
					</dd>
				</dl>
				<dl >
					<dt >给付类型</dt>
					<dd >
						<Field:codeTable name="claimBillPaidVOlist[0].paidType" orderBy="code" cssClass="combox title" id="viewInputSelect"   onChange="viewInputValue(this)"
							tableName="APP___CLM__DBUSER.T_THIRD_PAY_TYPE"  
							value="${claimBillPaidVOlist[0].paidType}" />
					</dd>
				</dl>
				<dl sizset="0">
					<dt ><font class="point" color="red">* </font>收据编号</dt>
					<dd sizset="0">
						<!--数据编号        必录  class="required"  -->
						<input name="claimBillPaidVOlist[0].dataId" id="dateIdForBillThirdPay" onchange="inspectionBIll('ThirdPay')"
							value="${claimBillPaidVOlist[0].dataId }"
							class="textInput" type="text" maxLength="30" />
					</dd>
				</dl>
				<dl sizset="1">
					<dt ><font class="point" color="red">* </font>医疗总费用</dt>
					<dd sizset="1">
						<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].sumAmount" id="sumAmountForBillThirdPay" onchange="inspectionBIll('ThirdPay')"
							value="${claimBillPaidVOlist[0].sumAmount }"
							class="number textInput" type="text" maxLength="18" />
					</dd>
				</dl>
			<%-- 	<dl  sizset="2">
					<dt ><font class="point" color="red">* </font>新农合报销金额</dt>
					<dd sizset="2">
						<input name="claimBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }"   class="number textInput selectView selectView2" type="text" maxLength="18" />
					</dd>
				</dl>
			<dl sizset="3">
					<dt><font class="point" color="red">* </font>医疗保险基金支付金额</dt>
					<dd title="8" sizset="3">
						<input name="claimBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }"  class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl> --%>
				<!-- 城镇居民 -->
				<dl sizset="3">
					<dt><font class="point" color="red">* </font>医疗保险基金支付金额</dt>
					<dd title="9" sizset="3">
						<input name="claimBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }"  class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="4">
					<dt >
						<font class="point" color="red">* </font>服务机构名称
					</dt>
					<dd sizset="4">
						<input name="claimBillPaidVOlist[0].serviceOrgName" value="${claimBillPaidVOlist[0].serviceOrgName}"
							
							class="textInput" type="text" maxLength="200" />
					</dd>
				</dl>
				<dl sizset="5">
					<dt>门诊发生金额</dt>
					<dd title="10" sizset="5">
						<input name="claimBillPaidVOlist[0].departAmount" value="${claimBillPaidVOlist[0].departAmount }"  class="number textInput selectView selectView3" type="text" maxLength="18" " />
					</dd>
				</dl>
				<dl sizset="6">
					<dt>统筹基金支付金额</dt>
					<dd title="11" sizset="6">
						<input name="claimBillPaidVOlist[0].fundAmount" value="${claimBillPaidVOlist[0].fundAmount}"   class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="7">
					<dt>大额互助资金支付</dt>
					<dd title="12" sizset="7">
						<input name="claimBillPaidVOlist[0].largeHelpAmount" value="${claimBillPaidVOlist[0].largeHelpAmount}" class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="8">
					<dt >大额互助年度内累计支付</dt>
					<dd title="13" sizset="8">
						<input name="claimBillPaidVOlist[0].largeHelpYearAmount" value="${claimBillPaidVOlist[0].largeHelpYearAmount}"  class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="9">
					<dt>统筹基金年度内累计支付</dt>
					<dd title="14" sizset="9">
						<input name="claimBillPaidVOlist[0].fundYearAmount" value="${claimBillPaidVOlist[0].fundYearAmount}" class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
							<dl sizset="10">
					<dt><font class="point" color="red">* </font>生育门诊支付</dt>
					<dd title="15" sizset="10">
						<input name="claimBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }" class="number textInput selectView selectView4" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="11">
					<dt><font class="point" color="red">* </font>生育住院支付</dt>
					<dd title="16" sizset="11">
						<input name="claimBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }" class="number textInput selectView selectView5" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="12">
					<dt ><font class="point" color="red">* </font>报销金额 </dt>
					<dd title="17" sizset="12">
						<input name="claimBillPaidVOlist[0].paidAmount"
							value="${claimBillPaidVOlist[0].paidAmount }"
							class="number textInput  selectView selectView6" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="13">
					<dt >自付1</dt>
					<dd sizset="13">
						<!--自付1     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].selfPay1"   
							value="${claimBillPaidVOlist[0].selfPay1 eq null?0:claimBillPaidVOlist[0].selfPay1 }"
							class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="14">
					<dt >自付2</dt>
					<dd sizset="14">
						<!--自付2     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].selfPay2"
							value="${claimBillPaidVOlist[0].selfPay2 eq null?0:claimBillPaidVOlist[0].selfPay2  }"
							class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="15">
					<dt >自费</dt>
					<dd sizset="15">
						<!--自费     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].ownExpense"
							value="${claimBillPaidVOlist[0].ownExpense eq null?0:claimBillPaidVOlist[0].ownExpense }"
							class="number textInput selectView selectView3" type="text" maxLength="18" />
					</dd>
				</dl>
				
				<!-- 城镇居民 -->
				<%-- <dl sizset="13">
					<dt >自付1</dt>
					<dd >
						<!--自付1     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].selfPay1"   
							value="${claimBillPaidVOlist[0].selfPay1 eq null?0:claimBillPaidVOlist[0].selfPay1 }"
							class="number textInput selectView selectView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="14">
					<dt >自付2</dt>
					<dd >
						<!--自付2     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].selfPay2"
							value="${claimBillPaidVOlist[0].selfPay2 eq null?0:claimBillPaidVOlist[0].selfPay2  }"
							class="number textInput selectView selectView9" type="text" maxLength="18" />
					</dd>
				</dl>
				<dl sizset="15">
					<dt >自费</dt>
					<dd >
						<!--自费     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimBillPaidVOlist[0].ownExpense"
							value="${claimBillPaidVOlist[0].ownExpense eq null?0:claimBillPaidVOlist[0].ownExpense }"
							class="number textInput selectView selectView9" type="text" maxLength="18" />
					</dd>
				</dl> --%>
				<!-- 城镇居民 -->
				<dl sizset="16">
					<dt >备注</dt>
					<dd sizset="16">
						<input name="claimBillPaidVOlist[0].remark"
							value="${claimBillPaidVOlist[0].remark }" class="textInput"
							type="text" size="12" />
					</dd>
				</dl>
				<dl sizset="17">
					<dt >医保起付线费用</dt>
					<dd sizset="17">
						<!--自费     必录  class="required" 浮点数 class="number" 整数  class="digits" -->
						<input name="claimsocialSBillPaidVOlist[0].medicarePayLine"
							value="${ claimBillPaidVOlist[0].medicarePayLine eq null?0:claimBillPaidVOlist[0].medicarePayLine}"
							class="number textInput selectSocialView selectSocialView8 selectSocialView1 selectSocialView10 selectSocialView9" type="text" maxLength="18" />
					</dd>
				</dl>
					<dl sizset="2">
					<dt><font class="point" color="red">* </font>自费报销金额</dt>
					<dd title="7" sizset="2">
						<input name="claimBillPaidVOlist[0].paidAmount" value="${claimBillPaidVOlist[0].paidAmount }"   class="number textInput selectView selectView7" type="text" maxLength="18" />
					</dd>
				</dl>
			</form>
		</div>
		<script type="text/javascript" language="javascript">

		claimFireEvent($("#viewInputSelect", navTab.getCurrentPanel()) );
		
function viewInputValue(obj){
	$(".selectView", navTab.getCurrentPanel()).parent().parent().css("display","none");
	$(".selectView", navTab.getCurrentPanel()).attr("disabled","disabled");
	$(".selectView"+$(obj).val(), navTab.getCurrentPanel()).parent().parent().css("display","");
	$(".selectView"+$(obj).val(), navTab.getCurrentPanel()).removeAttr("disabled");
}
</script>
	</s:if>
</div>

