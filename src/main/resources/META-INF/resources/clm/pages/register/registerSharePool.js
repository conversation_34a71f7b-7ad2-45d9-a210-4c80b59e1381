//输入框的非空验证
function change(){
	var caseNo = $("#caseNo" , navTab.getCurrentPanel()).val();
	var customerNo = $("#customerNo" , navTab.getCurrentPanel()).val();
	var customerName = $("#customerName" , navTab.getCurrentPanel()).val();
	var customerSex = $("#customerSex" , navTab.getCurrentPanel()).val();
	var customerId = $("#customerId" , navTab.getCurrentPanel()).val();
	var accDate = $("#accDate" , navTab.getCurrentPanel()).val();
	var claimState = $("#claimState" , navTab.getCurrentPanel()).val();
	
	if(caseNo.length!=0 && caseNo.length!=11){
		alertMsg.error("请输入正确赔案号");
		return;
	}
	if(caseNo.length!=0 && caseNo.substr(0,1)!=9){
		alertMsg.error("请输入正确赔案号");
		return;
	}
	if(customerName != ""){
		if(!checkName($("#customerName", navTab.getCurrentPanel()))){
			return;
		}
	}
//	if(caseNo=="" && customerNo=="" && customerName=="" 
//		&& customerId==""){
//		alertMsg.error ("请至少录入赔案号、证件号码、出险人姓名、客户号中的一个查询条件！");
//		return false;
//	}else{
		$("#registerSharePoolJspForm", navTab.getCurrentPanel()).submit();
//		return true;
//	}
}
function registClick(caseId,bpmTaskId,obj){
	
	$(obj).remove();
	 var str=caseId+","+bpmTaskId;
		navTab.openTab("20283", "clm/sign/applyPersonal_CLM_applyPersonalTaskAction.action?flag=2&signShareToOner="+str+"&caseId="+caseId, {title:'立案登记'});
		
	 
}

function redirectOther(url){
	//http://localhost:8080/ls/clm/register/toDataCollectInit_CLM_claimRegisterAction.action?leftFlag=1&menuId=20264
	navTab.openTab('20264','clm/sign/toDataCollectInit_CLM_claimRegisterAction.action?leftFlag=1&menuId=20264',{title:'立案登记'});
// 	alert(url);
}