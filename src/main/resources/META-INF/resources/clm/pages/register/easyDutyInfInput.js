queryClaimSubCase();

/**
 * 根据理赔类型 ==生成左侧复选框
 */
function queryClaimSubCase(){
	 
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	$.ajax({
		'url':'clm/register/queryClaimType_CLM_tClaimDutyRegisterAction.action?caseId='+caseId,
		'type':'post',
		'data':{},'datatype':'json','success':function(data){
			var data = eval("(" + data + ")");
	      // data=['01','02','03','04','05','06','07','08','09','10','11','12'];
		     	
			for(var i=0;i<data.length;i++){
			     //var claimType=data[i];

			 	var claimType=data[i].claimType;
				if(claimType.match('02')||claimType.match('03')){
					// 02:伤残    03 :重大疾病 ====>伤残  特种费用
					if(claimType.match('02')){
						if($("[hiddenDivId='registerMaimNews']", navTab.getCurrentPanel()).html()==null){
							$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[2]' value='2' hiddenDivId='registerMaimNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>伤残 </br>");
						}
					}
					if($("[hiddenDivId='registerOpsNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[3]' value='3' hiddenDivId='registerOpsNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' >特定手术/疾病/给付 <br>");
					}
					if($("[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='registerSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>特种费用 <br>");
					}
				}else if(claimType.match('04')){
					// 04:高残 
					if($("[hiddenDivId='registerMaimNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[2]' value='2' hiddenDivId='registerMaimNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>伤残 </br>");
					}
				} else if(claimType.match('08')){
					//08:医疗====>医疗单证录入、社保第三方给付、特种费用、手术分级给付
					if($("[hiddenDivId='registerHonsp']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[1]' value='1' hiddenDivId='registerHonsp' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>医疗单证录入 <br>");
					}
					if($("[hiddenDivId='registerOpsLevelNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[15]' value='15' hiddenDivId='registerOpsLevelNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>手术分级<br>");
					}
					if($("[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='registerSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>特种费用 <br>");
					}
					if($("[hiddenDivId='registerThirdPayNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[5]' value='5' hiddenDivId='registerThirdPayNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>社保第三方给付 <br>");
					}
					
				}else if (claimType.match('10')){
					//10:特种疾病=====>特定手术/疾病/给付、特种费用
					if($("[hiddenDivId='registerOpsNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[3]' value='3' hiddenDivId='registerOpsNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)' >特定手术/疾病/给付 <br>");
					}
					if($("[hiddenDivId='registerSpeCostNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[4]' value='4' hiddenDivId='registerSpeCostNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>特种费用 <br>");
					}
					
				}else if (claimType.match('06')){
					//06:一般失能======>一般失能
					if($("[hiddenDivId='registerGeneralNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[7]' value='7' hiddenDivId='registerGeneralNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>一般失能 <br>");
					}
				}else if (claimType.match('07')){
					//07:重度失能======>重度失能
					if($("[hiddenDivId='registerGreatNews']", navTab.getCurrentPanel()).html()==null){
						$("#registerClaimSubCase", navTab.getCurrentPanel()).append("<input type='checkbox' name='claimTypecode[8]' value='8' hiddenDivId='registerGreatNews' ChangeTypecode='claimTypecode' onclick='smallChange(this)'>重度失能 <br>");
					}
				}
	  		}
		}
	});
}
 
//点击左侧复选框显示右边详细信息
function smallChange(obj){
	 
	var caseId = $("#caseId", navTab.getCurrentPanel()).attr("value");
	var id = $(obj).attr("hiddenDivId");
	if($("#"+id, navTab.getCurrentPanel()).html()!=''){
		 
		if($(obj).is(":checked")){ 
			$("#"+id, navTab.getCurrentPanel()).find(".MedRadio:checked").attr("checked",false);
			$("#"+id, navTab.getCurrentPanel()).show("3000");  
			
			 /* if($("#"+id, navTab.getCurrentPanel()).find(".MedRadio").length>0){
				  //赋值  //赋值为hidden标识   编辑明细页面做了处理
			   $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").attr("title","hidden");
			   $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").click();
			    
			  }else{
			      $("#"+id, navTab.getCurrentPanel()).find("button[type='button']").click();
			      $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").click();
 
			  } */
			  // setTimeout('addNewcheck("'+id+'")', 250);
 
			 
		}else{
			
			 $("#hospitalcost", navTab.getCurrentPanel()).hide("3000",
						function() {
							$(this).empty();
		     });
 			$("#"+id, navTab.getCurrentPanel()).hide("3000");
		}
	}else{
		///下半部分先关掉
var readOnly =$("#readOnly", navTab.getCurrentPanel()).val();
		  var  idd="";
		if(id == "registerHonsp"){
			idd="registerHonsp";
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerHonsp", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerMaimNews"){
			idd="registerMaimNews";
			var rel = $("#registerMaimNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerMaimNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerOpsNews"){
			idd="registerOpsNews";
			var rel = $("#registerOpsNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerOpsNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerOpsLevelNews"){
			idd="registerOpsLevelNews";
			var rel = $("#registerOpsLevelNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsLevelNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerOpsLevelNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerSpeCostNews"){
			idd="registerSpeCostNews";
			var rel = $("#registerSpeCostNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerSpeCostNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerThirdPayNews"){
			idd="registerThirdPayNews";
			var rel = $("#registerThirdPayNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerThirdPayNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerGeneralNews"){
			idd="registerGeneralNews";
			var rel = $("#registerGeneralNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterGeneralNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerGeneralNews", navTab.getCurrentPanel()).show("3000");
		}
		if(id=="registerGreatNews"){
			idd="registerGreatNews";
			var rel = $("#registerGreatNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterGreatNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId+"&readOnly="+readOnly);
			$("#registerGreatNews", navTab.getCurrentPanel()).show("3000");
		} 
		
		// setTimeout('addNewcheck("'+idd+'")', 250);
		
	}
}


function addNewcheck(idd){
	  
	if($("#"+idd, navTab.getCurrentPanel()).find(".MedRadio").length>0){
		  //赋值为hidden标识   编辑明细页面做了处理
	      $("#"+id, navTab.getCurrentPanel()).find(".MedRadio:last").attr("title","hidden");
		  $("#"+idd, navTab.getCurrentPanel()).find(".MedRadio:last").click();
		  
		  $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html("");
	  }else{
	      $("#"+idd, navTab.getCurrentPanel()).find("button[type='button']").click();
	      $("#"+idd, navTab.getCurrentPanel()).find(".MedRadio:last").click();
  }
	
}

//根据直接给付代码 查询职业类别和职业给付系数
function queryJobLevelByJobCode(obj){
	var jobCode=$(obj).attr("value");
	if (jobCode == '') {
	    $(obj).closest("dl").next().find("input[name='data']").attr("value","");
		$(obj).closest("dl").next().next().find("input").attr("value","");
	} else {
		$.ajax({
			'type':'post',
			'url':'clm/register/queryOccupationRateByCode_CLM_tClaimDutyRegisterAction.action',
			'data':{'jobCodeVO.jobCode':jobCode},
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				 $(obj).closest("dl").next().find("input[name='claimOccupationRateVO.jobCategory']").attr("value",data.jobCategory);
				 $(obj).closest("dl").next().find("input[name='data']").attr("value",data.jobCategoryName);
				 $(obj).closest("dl").next().next().find("input").attr("value",data.jobUwLevel);
			},
		});
	}
}
//页面加减号的显示和隐藏
function show(show){
	if(show.value=="-"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).hide("3000");
		show.value="+";
	}else if (show.value=="+"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).show("3000");
		show.value="-";
	}
}
$(".btnDel", navTab.getCurrentPanel()).live("click",function(){
	$(this).closest("tr").remove();
});
//上一步 先保存页面信息  再跳转
function dutyInfInit_prev(id,caseId){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确认回到上一步？",{
		okCall:function(){ 
			backValue();
			var submitDuty = $("#submitRegisterDuty", navTab.getCurrentPanel());
			$.ajax({
				'type':'post',
				'url':'clm/register/saveClaimDutyRegisterCheckBox_CLM_tClaimDutyRegisterAction.action',
				'data':$(submitDuty).serialize(),
				'datatype':'json',
				'async':false,
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode == '300') {
						alertMsg.error("执行保存方法失败异常："+data.message);
						return false;
					}
					if(data.statusCode == '200'){
						prev(id,caseId);
					}
				},'error':function(){
					alertMsg.error("执行保存方法失败!");
				}
			});
	
	 }
	}); 
} 
//保存按钮
function submitRegisterDuty(flag){
	if(flag == "2"){
		///最外层  toauditpage 上的标识
		if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
			//审核页面
			prev_audit('1',caseId,$("#easyCase",navTab.getCurrentPanel()).val());
			return;
		}else{
		   ///立案页面
			prev('12',caseId);
		   return;
		}
	}
	backValue(); 
	
	 setTimeout(function(){
		 if($("#errorFlag", navTab.getCurrentPanel()).val()=="false"){
			 throw "stop";
	     }  
		 submitRegisterDutyTimeOut(flag);
		 
	 },1000) ; 
	//
	$("div", navTab.getCurrentPanel()).scrollTop(0);
}

function submitRegisterDutyTimeOut(flag){
	var occupationRate = $("input#claimOccupationRateVOoccupationRate").val();
	if (occupationRate =='z') {
		alertMsg.error("不接受拒保类职业变更，请在保项层人工处理。"); 
		return false;
	}
	
	var submitDuty = $("#submitRegisterDuty", navTab.getCurrentPanel());
	if($("#registerClaimSubCase", navTab.getCurrentPanel()).find("input[type='checkbox']#ageDealFlag").is(":checked")){
		var insuBirth = $("#insuBirthIdId",navTab.getCurrentPanel()).val();
		var insuRealBirth = $("#insuRealBirth",navTab.getCurrentPanel()).val();
		if(insuRealBirth == ""){
			alertMsg.error("勾选年龄误告处理，请录入出险人真实出生日期！"); 
			$("#alertMsgBox .toolBar .button").on("click",function(){ 
				$("#insuRealBirth",navTab.getCurrentPanel()).focus();
			});
			return false;
		}else{
			// 获取系统日期 直接获取右上角显示日期
			var workDate =$("#hiddenWorkDate", navTab.getCurrentPanel()).val();
			if(insuRealBirth > workDate){
				alertMsg.error("出险人真实出生日期不能晚于当前日期！");
				return false;
			}
			if(insuRealBirth == insuBirth){
				alertMsg.error("出险人真实出生日期不能等于当前出生日期！");
				return false;
			}
		}
	} 

	$.ajax({
		'type':'post',
		'url':'clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action?allSave=1',
		'data':$(submitDuty).serialize(),
		'datatype':'json',
		'success':function(data){
			 
			var dataMsg = eval("(" + data + ")");
			 	    
			var caseId = $("#caseId", navTab.getCurrentPanel()).val();
			
			var easyCase = $("#easyCase", navTab.getCurrentPanel()).val();
			 
			if (initFlag == "1") {
				$.ajax({
					url : "clm/advancepay/advanceCalc_CLM_advancePayAction.action?claimCaseVO.caseId="+caseId,
					type : "POST",
					dataType : "json",
					success : function (message){
						if (message.statusCode == DWZ.statusCode.ok) {
							  
							if(flag == 2){//表示为上一步操作
								///最外层  toauditpage 上的标识
								if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
									//审核页面
									prev_audit('1',caseId,easyCase);
								}else{
								   ///立案页面
								   dutyInfInit_prev('12',caseId);
								}
							}else if(flag == 1){//表示为下一步操作
								if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
									//审核页面
									if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
										 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
												okCall :   function(){
									              next_audit('3',caseId,easyCase);
												}
										 });
									}else{
										next_audit('3',caseId,easyCase);
									}
								}else{
									//立案
									
									if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
										 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
												okCall :   function(){
													next('14','addApplicantForm',caseId);
												}
										 });
									}else{
										next('14','addApplicantForm',caseId);
									}
									
									
								}
							}else{//表示为保存操作
								 $("span#advanceInput", navTab.getCurrentPanel()).parent().click();
								 alertMsg.close();
								  alertMsg.correct(message.message); 
							}
						} else {
							alertMsg.close();
							alertMsg.error(message.message);
						}
					}
				});
			} else {
				var data = eval("(" + data + ")");
				if (data.statusCode == DWZ.statusCode.ok) {
					if(flag == 2){//表示为上一步操作
						if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
							//审核页面
							prev_audit('1',caseId,easyCase);
						}else{
						   ///立案页面
						   dutyInfInit_prev('12',caseId);
						}
						 
					}else if(flag == 1){//表示为下一步操作
						if($("#auditFlag", navTab.getCurrentPanel()).size()>0){
							//审核页面
							
							if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
								 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
										okCall :   function(){
											next_audit('3',caseId,easyCase);
										}
								 });
							}else{
								next_audit('3',caseId,easyCase);
							}
						 }else{
							 
							 if(dataMsg.MSG_CLM_WRN_AUDIT_031 != ""&&dataMsg.MSG_CLM_WRN_AUDIT_031 != "null"){
								 alertMsg.confirm(dataMsg.MSG_CLM_WRN_AUDIT_031, {
										okCall :   function(){
											next('14','addApplicantForm',caseId);
										}
								 });
							}else{
								next('14','addApplicantForm',caseId);
							}
						 }
					}else{//表示为保存操作
						/*alertMsg.close();
						alertMsg.correct(data.message);*/
					}
				} else {
					alertMsg.error(data.message);
				}
			}
			
		},
	});
}

 
function onlyReadDuty(){
	//------  所有的-------
	var obj=$("div#registerAskForInfo",navTab.getCurrentPanel());
	//按钮
	 
	obj.find("button").each(function(){ 
		  $(this).attr("disabled",true);
	});
	//a标签
	obj.find("a").each(function(){ 
		if(!($(this).html()=="下一步"||$(this).html()=="上一步")){ 
			$(this).attr("disabled",true);
		}
	
	}); 
	//textarea标签
	obj.find("textarea").each(function(){
		$(this).attr("disabled",true);
	});
	obj.find("input").each(function(){
		$(this).attr("disabled",true);
	});
	obj.find("select").each(function(){
		$(this).attr("disabled",true);
	});
	//回退页面单选按钮可以触发 用于显示详情
	obj.find("input[type=radio]").each(function(){
		$(this).attr("disabled",false);
	});
	
}



//页面加减号的显示和隐藏
function showDiv(show){
	 
	if(show.value=="-"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).hide("3000");
		show.value="+";
	}else if (show.value=="+"){
		$("#"+show.hiddenDivId, navTab.getCurrentPanel()).show("3000");
		show.value="-";
	}
}



//自定义返回状态 
function dutyMyAjaxDone(json) { 
	
	if(json.statusCode == DWZ.statusCode.error) {
		if(json.message && alertMsg) alertMsg.error(json.message);
	} else if (json.statusCode == DWZ.statusCode.timeout) {
		//if(alertMsg) alertMsg.error(json.message || DWZ.msg("sessionTimout"), {okCall:DWZ.loadLogin});
		//else 
		//注掉提示信息 ，直接弹出登录窗口 Liandong
			DWZ.loadLogin(json.flag);
	} else {
		if(json.message && alertMsg) alertMsg.correct(json.message);
		closeInit();
	};
  
}


//自定义返回状态 
function dutyAjaxDone(json) {  
	 
	if(json.statusCode == DWZ.statusCode.error) {
		 //校验的关键
		 $("#errorFlag", navTab.getCurrentPanel()).val("false"); 
		  if(json.message && alertMsg){alertMsg.error(json.message)};  
		    
	}else { 
		$("#errorFlag", navTab.getCurrentPanel()).val("true");  
		 if(json.message && alertMsg) alertMsg.correct(json.message);
		var id=json.pageFlag;
		var caseId=json.caseId;
		if(id == "registerHonsp"){
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="registerMaim"){
			var rel = $("#registerMaimNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterMaimNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
			 
		}
		if(id=="registerGreat"){
			var rel = $("#registerOpsNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="registerOpsLevel"){
			var rel = $("#registerOpsLevelNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterOpsLevelNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="registerSpeCost"){
		 
			var rel = $("#registerSpeCostNews", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterSpeCostNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
		if(id=="socialPay"||id=="registerThirdPay"){ 
			if(id=="socialPay"){ 
				jsonShowMap="social-Pay=show"
			}
			if(id=="registerThirdPay"){ 
				jsonShowMap="third-Pay=show" 
					
			} 
			var rel = $("#registerThirdPayNews",navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterThirdPayNews_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}   
		if(id=="highMedical"||id=="registerHonsp"||id=="cancerMedical"){
			if(id=="highMedical"){
				jsonShowMap="HighEnd=show"
			}if(id=="registerHonsp"){
				jsonShowMap="Hospital=show"
			}
			if(id=="cancerMedical"){
				jsonShowMap="CancerPre=show"
			}
			var rel = $("#registerHonsp", navTab.getCurrentPanel());
			rel.loadUrl("clm/register/queryRegisterHonsp_CLM_tClaimDutyRegisterAction.action?caseId="+caseId);
		}
	};   
	 
}
  

//数据返回某一行
function  backValue(){
	         
	if($("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).children().length>0){
	//清除错误信息
		$("#errorFlag", navTab.getCurrentPanel()).val("true");
		//先把展开的保存
	 
	 $("#pageFormdivID", navTab.getCurrentPanel()).find("#SaveNewButton").each(function(){
		 $(this).click();
		 
		 setTimeout(function(){
			 if($("#errorFlag", navTab.getCurrentPanel()).val()=="false"){
				 throw "stop";
		     }  
			 
			 //(保存方法中如果错误则在页面上的id="errorFlag" 赋值 如果验证不通过不继续往下走)
				
			 	//
			 $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html(""); 
			
			 $("#hospitalcost", navTab.getCurrentPanel()).hide("3000",
						function() {
							$(this).empty();
			 });
			 
		 },500) ;
		 
		
		 
	 });
	
	 
	}
	 
	 
}
	//关闭
	function  closeInit(){
		
       $("#hospitalcost", navTab.getCurrentPanel()).hide("3000",function(){
	     	$(this).empty();
	    });
		
	}
	///输入框--去除
	function changeInputToSpan(obj){ 
	 
		$(obj).next().html($(obj).val()); 
		
		///高端医疗进行处理
		if($(obj).attr("yesOrNo")=="yesOrNo"){
			if($(obj).val()=="0"){
				$(obj).next().html("否"); 
			}
			if($(obj).val()=="1"){
				$(obj).next().html("是"); 
			}
		 
		}
		
	}
	
	
	//责任明细自定义
	function validateCallbackDuty(form, callback, confirmMsg) {
		
		// ******* 超时监控 Add HYP
		if (JudgeTimeOut()) {
			DWZ.loadLogin();
			return false;
		}
		
		// form 校验 若校验失败不允许提交
		var $form = $(form);
		/*if (!$form.valid()) {
			return false;
		}*/
		
		// ********添加自定义判断程序 add LiAnDong 
		var myOption = $form.attr("myOption");
		if (null != myOption && myOption != '') {
			var myFlag = eval(myOption);
			if (!myFlag)
				return myFlag;
		}
		
		var result = {data:$form.serializeArray()};
		var parent = $.pdialog.getCurrent() || navTab.getCurrentPanel();
		// *******交易管理监控 Add HYP
		if(dealSwitch){
			result = addHiddenAttrs(result, parent);
		}
		// 按钮添加审计功能
		if (btnSwitch) {
			result = addBtnHiddenAttrs(result, parent);
		}
		
		/*start  在请求参数中获取页面token ADD BY tanzl*/
		var $page = $(form).parents(".dialog");
		if($page.length == 0){
			$page = $(form).parents(".unitBox");
		}
		var tokenval=$page.find("input[name='token']").val();
		var tokenKey=$page.find("input[name=jspname]").val();
		//表单提交获取token和校验token标志
		result.data.push({'name':'token','value':tokenval});
		result.data.push({'name':'tokenKey','value':tokenKey});
		result.data.push({'name':'checkTokenFlag','value':'1'});
		/*token end*/
		
		var _submitFn = function() {
			$form.find(':focus').blur();
			
			$.ajax({
				type : form.method || 'POST',
				url : $form.attr("action"),
				data : result.data,
				dataType : "json",
				cache : false,
				async : false, 
				success : function(data){//对返回301用户session过期进行处理，使其返回到登陆页
					if (data.statusCode==DWZ.statusCode.timeout){
						DWZ.loadLogin(data.flag);
					} else {
						callback(data) || DWZ.ajaxDone(data);
					}
				},
				error : function(obj){
					 
				}
			});
		}
		if (confirmMsg) {
			alertMsg.confirm(confirmMsg, {
				okCall : _submitFn
			});
		} else {
			_submitFn();
		}
		return false;
	}

/*	var alertMsg = {
			_boxId: "#alertMsgBox",
			_bgId: "#alertBackground",
			_closeTimer: null,

			_types: {error:"error", info:"info", warn:"warn", correct:"correct", confirm:"confirm"},

			_getTitle: function(key){
				return $.regional.alertMsg.title[key];
			},

			_keydownOk: function(event){
				if (event.keyCode == DWZ.keyCode.ENTER) event.data.target.trigger("click");
				return false;
			},
			_keydownEsc: function(event){
				if (event.keyCode == DWZ.keyCode.ESC) event.data.target.trigger("click");
			},
			*//**
			 * 
			 * @param {Object} type
			 * @param {Object} msg
			 * @param {Object} buttons [button1, button2]
			 *//*
			_open: function(type, msg, buttons){
				$(this._boxId).remove();
				var butsHtml = "";
				if (buttons) {
					for (var i = 0; i < buttons.length; i++) {
						var sRel = buttons[i].call ? "callback" : "";
						butsHtml += DWZ.frag["alertButFrag"].replace("#butMsg#", buttons[i].name).replace("#callback#", sRel);
					}
				}
				var boxHtml = DWZ.frag["alertBoxFrag"].replace("#type#", type).replace("#title#", this._getTitle(type)).replace("#message#", msg).replace("#butFragment#", butsHtml);
				$(boxHtml).appendTo("body").css({top:-$(this._boxId).height()+"px",'z-index':5000}).animate({top:"0px"}, 500);
						
				if (this._closeTimer) {
					clearTimeout(this._closeTimer);
					this._closeTimer = null;
				}
				if (this._types.info == type || this._types.correct == type){
					this._closeTimer = setTimeout(function(){alertMsg.close()}, buttons[0].clearTime || 3500);
				} else {
					$(this._bgId).show();
				}
				
				var jButs = $(this._boxId).find("a.button");
				var jCallButs = jButs.filter("[rel=callback]");
				var jDoc = $(document);
				
				for (var i = 0; i < buttons.length; i++) {
					if (buttons[i].call) jCallButs.eq(i).click(buttons[i].call);
					if (buttons[i].keyCode == DWZ.keyCode.ENTER) {
						jDoc.bind("keydown",{target:jButs.eq(i)}, this._keydownOk);
					}
					if (buttons[i].keyCode == DWZ.keyCode.ESC) {
						jDoc.bind("keydown",{target:jButs.eq(i)}, this._keydownEsc);
					}
				}
				setTimeout(function(){
					function createIframe(target,zindex){
						var iframe = document.createElement('iframe');
						iframe.src = "javascript:false";
						iframe.frameBorder="0";
						iframe.className = 'shoudClose';
						iframe.style.position = "absolute";
						iframe.style.top = "0px";
						iframe.style.left = target.offsetLeft + "px";
						iframe.style.width = target.offsetWidth + "px";
						iframe.style.height = target.offsetHeight + "px";
						iframe.style.zIndex = zindex;
						target.parentNode.appendChild(iframe);
					}
					createIframe(document.getElementById('alertMsgBox'),document.getElementById('alertMsgBox').style.zIndex - 1);
				},480);
			},
			close: function(){
				if($(".shoudClose", navTab.getCurrentPanel()).length){
					$(".shoudClose", navTab.getCurrentPanel()).remove();
				}
				$(document).unbind("keydown", this._keydownOk).unbind("keydown", this._keydownEsc);
				$(this._boxId).animate({top:-$(this._boxId).height()}, 500, function(){
					$(this).remove();
				});
				$(this._bgId).hide();
			},
			error: function(msg, options) {
				this._alert(this._types.error, msg, options);
			},
			info: function(msg, options) {
				this._alert(this._types.info, msg, options);
			},
			warn: function(msg, options) {
				this._alert(this._types.warn, msg, options);
			},
			correct: function(msg, options) {
				this._alert(this._types.correct, msg, options);
			},
			_alert: function(type, msg, options) {
				 // 添加clearTime
				var op = {okName:$.regional.alertMsg.butMsg.ok, okCall:function(){
					if(options){
						options.find("input[class*=required]").each(function(){
							if($(this).val()==""){
								$(this).focus();
								return false;
							}
						});
					}
				},clearTime:null};
				$.extend(op, options);
				var buttons = [
				    // 添加clearTime
					{name:op.okName, call: op.okCall, keyCode:DWZ.keyCode.ENTER,clearTime:op.clearTime}
				];
			   //############################Add LiAnDong Begin########################
				if(msg.indexOf("Http status: 500")>0 || msg.indexOf("Http status: 404")>0){
					msg = "系统出现异常！请联系运维人员。";
				}else if(msg.indexOf("Http status: 12029")>0){
					msg = "您的网络已中断或系统服务器已停止。";
				}else if(msg.indexOf("ajaxOptions: error")>0){
					msg = "服务器运行异常！请联系运维人员。";
				}
			   //############################Add LiAnDong Begin########################
				this._open(type, msg, buttons);
			},
			*//**
			 * 
			 * @param {Object} msg
			 * @param {Object} options {okName, okCal, cancelName, cancelCall}
			 *//*
			confirm: function(msg, options) {
				var op = {okName:$.regional.alertMsg.butMsg.ok, okCall:null, cancelName:$.regional.alertMsg.butMsg.cancel, cancelCall:null};
				$.extend(op, options);
				var buttons = [
					{name:op.okName, call: op.okCall, keyCode:DWZ.keyCode.ENTER},
					{name:op.cancelName, call: op.cancelCall, keyCode:DWZ.keyCode.ESC}
				];
				this._open(this._types.confirm, msg, buttons);
			}
		};*/

	/*function  exit(){ 
		alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
		 	okCall:function(){
		      navTab.closeCurrentTab();
		 	 }
		 });  
	}*/
	//账单号输入验证
	
	 function checkCountNo(obj){ 
			if( typeof($(obj).val())=='undefined'){
				return true;
			} 
			var name = $(obj).val(); 
			var pattern = new RegExp("[《》`~!@#$^&*()=|{}':;',\\[\\]<>/?~！@#￥……&*（）;—|{}【】‘；：”“'。，、？·（+ \ / () * < % ? ;）]");
			
			 
			var checkName=true;
			var len = 0; 
			 
			var justName=true;
			
			for(var i = 0 ; i < name.length; i++){
				var a = name.charAt(i); 
				//判断是否包含不能包含除数字、字母、汉字、-、_和.之外的其他字符
				 if(pattern.test(a)){
					 checkName=false;
				 }
				//录入的内容不能仅为 - _ .的其中一种或几种字符                  当存在 - _ .之外的字符就不提示 
				 if(a!="."&&a!="-"&&a!="_"){
					 justName=false;
				 }
			}
			 
			
			if(!checkName){
				$(obj).attr("style","border:#ff0000 1px solid");
				  alertMsg.error("不能包含除数字、字母、汉字、-、_和.之外的其他字符");
				  $(obj).val("");
				  return false;
			}else if($(obj).val()!=""&&justName){
				  $(obj).attr("style","border:#ff0000 1px solid");
				  alertMsg.error("录入的内容不能仅为 - _ .的其中一种或几种字符");
				  $(obj).val("");
				  return false;
			}else{
				$(obj).removeAttr("style");
			}
		 
			return true;
		
		}
	 
