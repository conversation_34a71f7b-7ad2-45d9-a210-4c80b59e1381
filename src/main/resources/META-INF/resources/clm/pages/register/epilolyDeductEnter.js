/*function exit(){
	alertMsg.confirm("是否确定退出？",{
		okCall:function(){
			navTab.closeCurrentTab();
		}
	});
}*/

/**
 * 序号排序
 */
function numberaging() {
	var length = $("#outsourceAgingDeductTbody tr", navTab.getCurrentPanel()).length;
	for(var i=0;i<length;i++) {
        $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").text(i+1); 
	}
}
/**
 * 序号排序
 */
function numbererror() {
	var length = $("#outsourceErrorDeductTbody tr", navTab.getCurrentPanel()).length;
	for(var i=0;i<length;i++) {
        $("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq(0)").text(i+1); 
	}
}
function addOutsourceErrorDeduct(obj){
	var tr=$(obj).parent().parent().find("table tr").length;
	$(obj).parent().parent().find("table").append("<tr><td style='text-align: center;'>"+tr+"</td><td><input style='width: 99%;' onblur='checkxs(this)' value='' class='num' maxlength='6'/></td><td><input style='width: 99%;' onblur='checkxs(this)' value='' class='num' maxlength='6'></td><td><input style='width: 99%;' onblur='checkzs(this)' value='' class='num' maxlength='8'></td><td><a title='删除' class='btnDel' id='delButton' style='display:block;  margin:0 auto;  float:none;' href='javascript:void(0);'onclick='deleteErrorDeduct(this);'>删除</a></td></tr>");
}
function addOutsourceAgingDeduct(obj){
	var tr=$(obj).parent().parent().find("table tr").length;
	$(obj).parent().parent().find("table").append("<tr><td style='text-align: center;'>"+tr+"</td><td><input style='width: 99%;' onblur='checkzs(this)' value='' class='num' maxlength='8'></td><td><input style='width: 99%;' onblur='checkzs(this)' value='' class='num' maxlength='8'></td><td><input style='width: 99%;' onblur='checkxs(this)' value='' class='num' maxlength='6'></td><td><a title='删除' class='btnDel' id='delButton' style='display:block;  margin:0 auto;  float:none;' href='javascript:void(0);'onclick='deleteAgingDeduct(this);'>删除</a></td></tr>");
}
function deleteErrorDeduct(obj){
	if($(obj).parent().parent().find("td:eq(4)").find("input").length>0){
		var id=$(obj).parent().parent().find("td:eq(4)").find("input").val();
		var deleteId=$("#deleteOutsourceErrorDeduct", navTab.getCurrentPanel()).val();
		$("#deleteOutsourceErrorDeduct", navTab.getCurrentPanel()).val(deleteId+"-"+id);
	}
	$(obj).parent().parent().remove();
	numbererror();
}
function deleteAgingDeduct(obj,index){
	if($(obj).parent().parent().find("td:eq(4)").find("input").length>0){
		var id=$(obj).parent().parent().find("td:eq(4)").find("input").val();
		var deleteId=$("#deleteOutsourceAgingDeduct", navTab.getCurrentPanel()).val();
		$("#deleteOutsourceAgingDeduct", navTab.getCurrentPanel()).val(deleteId+"-"+id);
	}
	$(obj).parent().parent().remove();
	numberaging();
}
function update(){
	$("input", navTab.getCurrentPanel()).attr("disabled", false);
	$("#deductEnd_epilolyDeductEnter", navTab.getCurrentPanel()).attr("disabled", true);
}

function checkxs(obj){
	var val=obj.value;
	var n=/^[0-9](\.[0-9]{0,4})?$/;
	if(val!=""){
		if(!n.test(val)&&val!=0){
			alertMsg.warn("请输入一个最大为9.9999的值");
			$(obj).val("");
			return flase;
		}
	}
}
function checkzs(obj){
	var val=obj.value;
	var r =/^[0-9]*[1-9][0-9]*$/;
	if(val!=""){
		if(!r.test(val)&&val!=0){
			alertMsg.warn("请输入一个最大为99999999的值");
			$(obj).val("");
			return flase;
		}
	}
}

function checkDeductStart(obj){
	var r =/^((0?[1-9])|((1|2)[0-9])|30|31)$/;
	if($(obj).val()!=""){
		if(!r.test($(obj).val())){
			alertMsg.warn("请输入一个最大为1-31的值");
			$(obj).val("");
			$("#deductEnd_epilolyDeductEnter", navTab.getCurrentPanel()).val("");
			return flase;
		} else {
			if ($(obj).val() == 1 ) {
				$("#deductEnd_epilolyDeductEnter", navTab.getCurrentPanel()).val(31);
			} else {
				$("#deductEnd_epilolyDeductEnter", navTab.getCurrentPanel()).val($(obj).val()-1);
			}
		}
	}
}
/**
 * 校验大小区间是否合法
 */
function isCorrect() {
 var error = $("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr").length - 1;
 for (var j = error; j >= 0; j--) {
    for (var i = 0; i < j; i++) {
	var j1 = $("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + j + ")").find(
			"td:eq(1)").find("input").val();
	var nj1 = parseFloat(j1);
	var j2 = $("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + j + ")").find(
			"td:eq(2)").find("input").val();
	var nj2 = parseFloat(j2);
	var i1 = $("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + i + ")").find(
			"td:eq(1)").find("input").val();
	var ni1 = parseFloat(i1);
	var i2 = $("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + i + ")").find(
			"td:eq(2)").find("input").val();
	var ni2 = parseFloat(i2);
	if(nj1>=nj2){
		alertMsg.warn("不符合数字格式，请修改！");
		return flase;
	}
	if (nj1 >= ni2||nj2 <= ni1) {
	}  else {
		alertMsg.warn("不符合数字格式，请修改！");
		return flase;
	   }
     }
   }
 var aging = $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr").length - 1;
 for (var j = aging; j >= 0; j--) {
    for (var i = 0; i < j; i++) {
	var j1 = $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + j + ")").find(
			"td:eq(1)").find("input").val();
	var nj1 = parseInt(j1);
	var j2 = $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + j + ")").find(
			"td:eq(2)").find("input").val();
	var nj2 = parseInt(j2);
	var i1 = $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + i + ")").find(
			"td:eq(1)").find("input").val();
	var ni1 = parseInt(i1);
	var i2 = $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq(" + i + ")").find(
			"td:eq(2)").find("input").val();
	var ni2 = parseInt(i2);
	if(nj1>=nj2){
		alertMsg.warn("不符合数字格式，请修改！");
		return flase;
	}
	if (nj1 >= ni2||nj2 <= ni1) {
	}  else {
		alertMsg.warn("不符合数字格式，请修改！");
		return flase;
	   }
     }
   }
 }

function saveEpilolyDeduct(){
	isCorrect();
	if(trim($("#deductBytes", navTab.getCurrentPanel()).val())==""||trim($("#agingControlRate").val())==""||$("#deductStart_epilolyDeductEnter", navTab.getCurrentPanel()).val()==""){
		alertMsg.warn("请录入必录项。");
		return false;
	}
	var jsonErrorDeduct=new Array();
	var jsonAgingDeduct=new Array();
	jsonErrorDeduct="[";
	jsonAgingDeduct="[";
	var outsourceErrorDeductVO=["minCorrect","maxCorrect","errorDeductMultiple","errorDeductId"];
	var outsourceAgingDeductVO=["minAging","maxAging","agingDeductRate","agingDeductId"];
	$("#outsourceErrorDeductTbody tr", navTab.getCurrentPanel()).each(function(i) {
		var tr="{";
		$("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td").each(function(j) {
			if($("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq("+j+")").find("input").val()==""){
				alertMsg.warn("请录入必录项。");
				return false;
			}
			var n=j+1;
			tr= tr + "'"+outsourceErrorDeductVO[j]+"':'"+$("#outsourceErrorDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq("+n+")").find("input").val()+"',";
		});
		tr=tr+"},";
		jsonErrorDeduct=jsonErrorDeduct+tr;
	});
	jsonErrorDeduct=jsonErrorDeduct+"]";
	var aging = $("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr").length - 1;
	$("#outsourceAgingDeductTbody tr", navTab.getCurrentPanel()).each(function(i) {
		var tr="{";
		$("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td").each(function(j) {
			if(i<aging||j!=2){
			if($("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq("+j+")").find("input").val()==""){
				alertMsg.warn("请录入必录项。");
				return false;
			}
			}
			var n=j+1;
			tr= tr + "'"+outsourceAgingDeductVO[j]+"':'"+$("#outsourceAgingDeductTbody", navTab.getCurrentPanel()).find("tr:eq("+i+")").find("td:eq("+n+")").find("input").val()+"',";
		});
		tr=tr+"},";
		jsonAgingDeduct=jsonAgingDeduct+tr;
	});
	jsonAgingDeduct=jsonAgingDeduct+"]";
	$.ajax({
		url : 'clm/register/saveAllEpilolyDeduct_CLM_epilolyDeductEnterAction.action',
		type : 'post',
		data : {'outsourceErrorDeductVO.jsonTable':jsonErrorDeduct,
			'outsourceAgingDeductVO.jsonTable':jsonAgingDeduct,
			'outsourceDeductVO.deductId':$("#deductId", navTab.getCurrentPanel()).val(),
			'outsourceDeductVO.deductBytes':$("#deductBytes", navTab.getCurrentPanel()).val(),
			'outsourceDeductVO.agingControlRate':$("#agingControlRate", navTab.getCurrentPanel()).val(),
			'outsourceDeductVO.deductStart':$("#deductStart_epilolyDeductEnter", navTab.getCurrentPanel()).val(),
			'outsourceDeductVO.deductEnd':$("#deductEnd_epilolyDeductEnter", navTab.getCurrentPanel()).val(),
			'deleteErrorDeduct':$("#deleteOutsourceErrorDeduct", navTab.getCurrentPanel()).val(),
			'deleteAgingDeduct':$("#deleteOutsourceAgingDeduct", navTab.getCurrentPanel()).val()},
		global : true,
		dataType : 'json',
		success : function(json){
			if (json.statusCode == DWZ.statusCode.ok) {
				$("#saveButton", navTab.getCurrentPanel()).attr("disabled","disabled");
				alertMsg.info(json.message);
				$("#queryOutsourceIfnoIdEnter", navTab.getCurrentPanel()).submit();
				return false;
			}
		}
	});
}
