var bs = 0;
function saveClick(flag){ 
	  if(bs == 1){
		  tonext('16',$("input[name='claimCaseVO.caseId']", navTab.getCurrentPanel()).val(),$("input[name='claimCaseVO.caseNo']", navTab.getCurrentPanel()).val(),$("input[name='claimAccidentVO.accidentNo']", navTab.getCurrentPanel()).val()); 
		  return;
	  }
	  //下一步需要缴费申请书按钮是否点击过
	  /*if(flag==1){
		  if($("#createDocumentButtion", navTab.getCurrentPanel()).size()!=0&&createDocumentFlag=="0"){
			  alertMsg.error("点击生成缴费申请书后，才可进行下一步");
			  return null;
		  }
	  }*/
/*	  if($("#isDeductPrem", navTab.getCurrentPanel()).size()!=0&&$("#isDeductPrem", navTab.getCurrentPanel()).val()==""){
		  alertMsg.error("在理赔金中扣除保费必选");
		  return null;
	  }*/
	  if(flag == 2){
		  presave = 1; //上一步的标识
	  }
	  var acceptDecisionId=$("#acceptDecisionId", navTab.getCurrentPanel()).val();
	  var num=0;
	  if($.trim(acceptDecisionId)==""){
	  	alertMsg.info("立案结论必选");
	  	return;
	  }
	  //选择不予立案校验
	  if(acceptDecisionId == '2'){
		  if($("#rejectReason", navTab.getCurrentPanel()).val() == ""){
			  alertMsg.error("当立案结论为不予立案时，请选择不予立案原因！");
			  	return;
		  }
	  }
	  //当赔案欺诈风险为“有风险”时，一级标签的数据必选其一
	  if($("#isRisk", navTab.getCurrentPanel()).val() == "1"){
		  //判断是否一级标签的数据选择
		  if($("#riskLabelNine", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelTwo", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelFour", navTab.getCurrentPanel()).attr("checked") != "checked"
			  && $("#riskLabelSix", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#riskLabelTen", navTab.getCurrentPanel()).attr("checked") != "checked"  && $("#riskLabelOne", navTab.getCurrentPanel()).attr("checked") != "checked"){
			  	alertMsg.error("请在一级标签中至少勾选一项欺诈风险选项。");
			  	return;
		  }
		 //当赔案欺诈风险为“有风险”时，欺诈案件信息都是必填项
		  //作案性质校验
		  if($("#committingNatureOne", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#committingNatureTwo", navTab.getCurrentPanel()).attr("checked") != "checked"){
			  alertMsg.error("当赔案欺诈风险为“有风险”时，作案性质必选！");
			  return;
		  }
		  //公检法立案校验
		  if($("#inspectionRegisterFlagOne", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#inspectionRegisterFlagTwo", navTab.getCurrentPanel()).attr("checked") != "checked"){
			  alertMsg.error("当赔案欺诈风险为“有风险”时，公检法立案必选！");
			  return;
		  }
		//公检法立案校验为是时，公检法立案日期必填。
		  if($("#inspectionRegisterFlagOne", navTab.getCurrentPanel()).attr("checked") == "checked"){
			  //公检法立案日期校验
			  if($("#inspectionRegisterTimeId", navTab.getCurrentPanel()).val().trim() == "" || $("#inspectionRegisterTimeId", navTab.getCurrentPanel()).val().trim() == null ){
				  alertMsg.error("当赔案欺诈风险为“有风险”时，公检法立案日期必填！");
				  return;
			  }
		  }
		  //欺诈识别途径校验
		  if($("#cheatDistinguishChannelId", navTab.getCurrentPanel()).val().trim() == "" || $("#cheatDistinguishChannelId", navTab.getCurrentPanel()).val().trim() == null ){
			  alertMsg.error("当赔案欺诈风险为“有风险”时，欺诈识别途径必选！");
			  return;
		  }
		  //欺诈实施人员校验
		  if($("#cheatImplementationPersonnelOne", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelTwo", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelThree", navTab.getCurrentPanel()).attr("checked") != "checked" 
			  && $("#cheatImplementationPersonnelFour", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelFive", navTab.getCurrentPanel()).attr("checked") != "checked" && $("#cheatImplementationPersonnelSix", navTab.getCurrentPanel()).attr("checked") != "checked" 
				  && $("#cheatImplementationPersonnelSeven", navTab.getCurrentPanel()).attr("checked") != "checked"){
			  alertMsg.error("当赔案欺诈风险为“有风险”时，欺诈实施人员必选！");
			  return;
		  }
	  }
	  
	  //选择故意欺诈风险选项为其他时文本框必填
	  if($("#riskLabelEight", navTab.getCurrentPanel()).attr("checked") == "checked"){
		  if($.trim($("#riskOtherReason", navTab.getCurrentPanel()).val())==""){
			  alertMsg.error("当赔案欺诈风险选项为其他时,文本框必填!");
			  return;
		  }
	  }
	  //选择欺诈识别途径为其他时，文本框必填
	  if($("#cheatDistinguishChannelId", navTab.getCurrentPanel()).val() == "09"){
		  if($.trim($("#otherCheatOptionId", navTab.getCurrentPanel()).val())==""){
			  alertMsg.error("欺诈识别途径为其他时,文本框必填!");
			  return;
		  }
	  }
	  //选择欺诈实施人员为其他人员时，文本框必填
	  if($("#cheatImplementationPersonnelSeven", navTab.getCurrentPanel()).attr("checked") == "checked"){
		  if($.trim($("#otherImplementationPersonnel", navTab.getCurrentPanel()).val())==""){
			  alertMsg.error("欺诈实施人员为其他人员时,文本框必填!");
			  return;
		  }
	  }
	  $("#claimBusiProdTbody", navTab.getCurrentPanel()).find("tr").each(function(){
		  num++;
	  });
	  $("#registerCalc", navTab.getCurrentPanel()).find("tr").each(function(){
		  num++;
	  });
	  if(num==0){
		  if($.trim(acceptDecisionId)=="1"){
			  alertMsg.info("赔案下没有保单计算信息，立案结论不能是立案通过！");
			  return;
		  }
	  }
	  
	  if($.trim(acceptDecisionId)=="1"){
		//查询列表选中的保单是否有在续保核保批处理生成核保任务且在人工核保中的保单
				$.ajax({
					url:"clm/register/queryLockUw_CLM_claimMatchCalcAction.action",
					type:"POST",
					data:$("#pagerForms", navTab.getCurrentPanel()).serialize(),
					success:function(data){
						var data = eval("("+data+")");
						if(data.flag == "Y"){
							alertMsg.error(data.message);
							return false;
						}else{
							//保存操作
							var caseId = $("input[name='claimCaseVO.caseId']", navTab.getCurrentPanel()).val();
						      $("form#pagerForms", navTab.getCurrentPanel()).attr("action","clm/register/saveConclusion_CLM_claimMatchCalcAction.action?flag="+flag);
						      $("form#pagerForms", navTab.getCurrentPanel()).submit();
						}
					} 
				});
	  }else{
		  var caseId = $("input[name='claimCaseVO.caseId']", navTab.getCurrentPanel()).val();
	      $("form#pagerForms", navTab.getCurrentPanel()).attr("action","clm/register/saveConclusion_CLM_claimMatchCalcAction.action?flag="+flag);
	      $("form#pagerForms", navTab.getCurrentPanel()).submit();
	  }
	  
	  
  }
  
  function autoMatchClacLogClick(){
      /*$("#pagerForm", navTab.getCurrentPanel()).attr("action","clm/register/matchCalc_CLM_claimMatchCalcAction.action");
      $("#pagerForm", navTab.getCurrentPanel()).submit();*/
  }
 /* function exit(){
	 	alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 		okCall:function(){
				navTab.closeCurrentTab();
	 		}
	 	});
	}*/
//直接退出
  function exit1(){
  	navTab.closeCurrentTab();
  }
//赔付结论事件 控制拒付原因
	function clickAcceptDecisionId(value){
		if(value=="2"){
			 $("#rejectReason", navTab.getCurrentPanel()).setMyComboxDisabled(false);
			 $("#isRiskDiv", navTab.getCurrentPanel()).show();
			 $("#IsnotRegistDocFlag", navTab.getCurrentPanel()).show();
			 if($("#isRisk",navTab.getCurrentPanel()).val() == 1){
				 $("#riskDiv", navTab.getCurrentPanel()).show();
			 }
		}else{
			$("#isRiskDiv", navTab.getCurrentPanel()).hide();
			$("#IsnotRegistDocFlag", navTab.getCurrentPanel()).hide();
			$("#unDocReasonFlag", navTab.getCurrentPanel()).hide();
			$("#riskDiv", navTab.getCurrentPanel()).hide();
			 $("#rejectReason", navTab.getCurrentPanel()).selectMyComBox("");
			 $("#notRegistDocFlag", navTab.getCurrentPanel()).selectMyComBox("");
			 $("#unDocReason", navTab.getCurrentPanel()).val("");
			 $("#rejectReason", navTab.getCurrentPanel()).setMyComboxDisabled(true);
		}
//		if(value=="2"){
//			alertMsg.info("该赔案为 自助理赔，请予以立案通过。");
//		}
	}
	
	//控制不生成通知书原因
	function isUnDocReason(value){
		if(value=="1"){
			 $("#unDocReasonFlag", navTab.getCurrentPanel()).show();
		}else{
			$("#unDocReasonFlag", navTab.getCurrentPanel()).hide();
			$("#unDocReason", navTab.getCurrentPanel()).val("");
		}	
	 }

	//控制标签页输入项为只读
	function onlyReadClaimMatchCalcInit(){
		bs = 1;
		//------  所有的-------
		var obj=$("div#claimMatchCalcInitDiv",navTab.getCurrentPanel());
		//按钮
		obj.find("button").each(function(){
			//if($(this).text()!="上一步" && $(this).text()!="下一步"){
				$(this).attr("disabled",true);
			//}
			/*//if($(this).text()=="下一步"){
				$(this).attr("disabled",false);
			}*/
		});
		//a标签
		obj.find("a").each(function(){
			$(this).attr("disabled",true);
		});
		obj.find("input").each(function(){
			$(this).attr("disabled",true);
		});
		obj.find("select").each(function(){
			$(this).attr("disabled",true);
		});
	}
	//回调函数，下一步是否禁与不禁。
	function claimMathDialogAjaxDone(json){
		DWZ.ajaxDone(json);
		if(json.data.saveConclusionFlag != "1"){
			$("#claimMatchNext", navTab.getCurrentPanel()).attr("disabled",false);
		} else {
			$("#claimMatchNext", navTab.getCurrentPanel()).attr("disabled","disabled");
		}
	}
	//判断赔案欺诈二级标签其他是否勾选 
	function riskLabelEightOther(obj){
		if($(obj).attr("checked")!="checked"){
			$("#riskOtherReason",navTab.getCurrentPanel()).hide();
			$("#riskOtherReason",navTab.getCurrentPanel()).val("");
		} else {
			$("#riskOtherReason",navTab.getCurrentPanel()).show();
		}
	}
	//判断赔案欺诈二级标签其他是否勾选 
	function cheatImplementationPersonnelSevenRegister(obj){
		if($(obj).attr("checked")!="checked"){
			$("#otherImplementationPersonnel",navTab.getCurrentPanel()).hide();
			$("#otherImplementationPersonnel",navTab.getCurrentPanel()).val("");
		} else {
			$("#otherImplementationPersonnel",navTab.getCurrentPanel()).show();
		}
	}
	//控制欺诈识别途径其他框
	function checkOtherCheatOption(value){
		if(value !="09"){
			$("#otherCheatOptionId", navTab.getCurrentPanel()).hide();
			$("#otherCheatOptionId",navTab.getCurrentPanel()).val("");
		}else{
			$("#otherCheatOptionId", navTab.getCurrentPanel()).show();
		}
	}
	//公检法立案日期控制
	function inspectionRegisterFlagClick(value){
		if(value !="1"){
			$("#inspectionRegisterTimeId", navTab.getCurrentPanel()).attr("disabled","disabled");
		}else{
			$("#inspectionRegisterTimeId", navTab.getCurrentPanel()).removeAttr("disabled", "disabled");
		}
	}
	/**
	 * @param obj 生成缴费申请书
	 * @returns
	 */
	function createDocument(obj){
		var caseId=$("#caseId", navTab.getCurrentPanel()).val();
		 $.ajax({
			'url' :'clm/register/createDocument_CLM_claimMatchCalcAction.action?caseId='+caseId,
			'type' :'post',
			'datatype' : 'json' ,
			'success':function(data){
				 var data = eval("(" + data + ")");
				 if(data.statusCode!=null&&data.statusCode==DWZ.statusCode.ok){
					 alertMsg.info(data.message);
					 $(obj).attr("disabled","disabled");
					 $("#createDocumentFlag", navTab.getCurrentPanel()).val("1");
				 }else{
					 alertMsg.error(data.message);
				 }
			},
	  	    'error':function(){
	  				alert("出错了！");
	  		} 
	    });	
	}