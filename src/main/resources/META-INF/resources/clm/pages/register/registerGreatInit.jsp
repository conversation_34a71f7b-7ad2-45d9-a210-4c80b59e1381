<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>

<script type="text/javascript">
	  $("#jointContentCodeDL", navTab.getCurrentPanel()).css("display", "none");
	$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#jointPayRate").parent().parent()
			.css("display", "none");
 
	if ($("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#specialCode").size(0)) {
		
		//findFT($("#specialCode", navTab.getCurrentPanel())); 
		 setTimeout( 'findFTInit()',500);
		 
	}   
	function findFTInit(){
		findFT($("#specialCode", navTab.getCurrentPanel()));
	}
	function specialTypeChangeGr(obj) {
		var relaCode = $(obj).attr("value"); 

		var surgeryCode = $("#specialCode", navTab.getCurrentPanel());

		$.ajax({
					'type' : 'post',
					'url' : 'clm/register/querySpecialDiseaseItem_CLM_tClaimDutyRegisterAction.action?SpecialTypeCode='
							+ relaCode,
					'datatype' : 'json',
					'success' : function(data) {
						var data = eval("(" + data + ")");
						surgeryCode.empty();
//	 					surgeryCode.loadMyComboxOptions("<option value='-1' class = ''></option>");
						/* $(
								"<option value='-1' class = ''></option>")
								.loadMyComboxOptions(surgeryCode); */
						var option1 = "<option value='' class = ''>请选择</option>";
						$(option1).appendTo(surgeryCode);
						for (var i = 0; i < data.length; i++) {
							option2 = "<option value='"+data[i].code+"'   class='"+data[i].name+"' >"
									+ data[i].name
									+ "</option>";
							//$(option2).loadMyComboxOptions(surgeryCode);
						$(option2).appendTo(surgeryCode);
						}
						claimFireEvent($("#specialCode",navTab.getCurrentPanel()));
// 						surgeryCode.loadMyComboxOptions(option2);
					},
					'error' : function() {
						alert("出错了！");
					}
				});
	}
</script>
<!--账单及费用明细  div start -->

<div class="panelPageFormContent"> 
	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">特定手术/疾病/给付信息录入
					</h1>
				</div>
	<div class="panelPageFormContent" id="dutyHospitalCostInitDetails">

		<form class="pageForm required-validate" id="${pageFlag}FormId"
			action="clm/register/saveClaimDutyRegister_CLM_tClaimDutyRegisterAction.action"
			method="post"
			onsubmit="return validateCallback(this,dutyAjaxDone)">
			<input type="hidden" name="claimSurgeryVOlist[0].caseId"
				value="${caseId}"> <input type="hidden"
				name="claimSurgeryVOlist[0].surgeryId"
				value="${claimSurgeryVOlist[0].surgeryId}"> <input
				type="hidden" name="claimTypecode[0]" value="3"> <input
				type="hidden" name="pageFlag"  value="${pageFlag}">
              <input type="hidden"   name="caseId"  value="${caseId}">
              <input type="hidden"   id="isNineFive" name="isNineFive"  value="${isNineFive}">
			<dl sizset="0">
				<dt >
					<font class="point" color="red">* </font>特定费用类型
				</dt>
				<dd   sizset="0"> 
			      
					<Field:codeTable  onChange="specialTypeChangeGr(this)"  id="specialDiseaseType"
						name="claimSurgeryVOlist[0].surgeryType"
						value="${fn:trim(claimSurgeryVOlist[0].surgeryType)}"  
						tableName="APP___CLM__DBUSER.T_SPECIAL_DISEASE_TYPE"
						nullOption="true"  
						cssClass="notuseflagDelete combox title surgeryType" />
						
						 
				</dd>
			</dl>
			<dl sizset="1">
				<dt >
					<font class="point" color="red">* </font>特定费用代码
				</dt>
				<dd sizset="1">
					<Field:codeTable cssClass="selectToInput" id="specialCode"
						value="${fn:trim(claimSurgeryVOlist[0].surgeryCode)}"
						onChange="findFT(this)" name="claimSurgeryVOlist[0].surgeryCode"
						tableName="APP___CLM__DBUSER.T_SPECIAL_DISEASE_ITEM"
						nullOption="true"  whereClause="RELA_CODE='${claimSurgeryVOlist[0].surgeryType}'  AND  CODE !='FT021'"  orderBy="code" />
				</dd>
			</dl>
			
			<dl id="jointContentCodeDL" >
			      <input  type="hidden"    value="${claimSurgeryVOlist[0].jointContentCode }"  id="jointContentCodeHidden">
				<dt >
					<font class="point" color="red">* </font>骨关节代码
				</dt>
				<dd >
				<!--cssClass="combox"  --> 
				
				
				
				   <Field:codeTable  cssClass="combox title"   id="jointContentCode"
						onChange="changeJointRate(this)"  
						value="${claimSurgeryVOlist[0].jointContentCode }"
						name="claimSurgeryVOlist[0].jointContentCode"
						tableName="APP___CLM__DBUSER.T_JOINT" nullOption="true" />  
				</dd>
			</dl>
			<dl >
				<dt > 
					<font class="point" color="red">* </font>骨关节赔付比例
				</dt>
				<dd >
					<input class="textInput" type="text" id="jointPayRate"
						name="claimSurgeryVOlist[0].jointPayRate"
						value="${claimSurgeryVOlist[0].jointPayRate }"
						  readonly="readonly" />
				</dd>
			</dl>
			<dl sizset="2">
				<dt >金额</dt>
				<dd sizset="2">
					<input name="claimSurgeryVOlist[0].sumAmount"
						value="${claimSurgeryVOlist[0].sumAmount}" class="textInput"
						type="text" size="12" onkeyup="this.value=this.value.replace('-','')"/>
				</dd>
			</dl>
			<dl sizset="3">
				<dt >
					<font class="point" color="red">* </font>医疗机构名称
				</dt>
				<dd sizset="3">
					<input name="claimSurgeryVOlist[0].medOrgName"
						value="${claimSurgeryVOlist[0].medOrgName}" class="textInput"
						type="text" size="12" />
				</dd>
			</dl>
			<dl >
				<dt >
					<font class="point" color="red">* </font>确诊日期
				</dt>
				<dd   >
					<input name="claimSurgeryVOlist[0].diagnoseDate"
						value="<s:date name='claimSurgeryVOlist[0].diagnoseDate' format='yyyy-MM-dd' />"
						class="date textInput" type="expandDateYMD" maxLength="10"
						datefmt="yyyy-MM-dd" /><a class="inputDateButton"
						href="javascript:;">选择</a>
				</dd>
			</dl> 
		</form> 
	</div>
	<!--医疗费用明细  div end  -->
</div>
<script type="text/javascript">
  
/*    setTimeout(function() {  
	
if($("#jointContentCodeHidden", navTab.getCurrentPanel()).val() != ""){
	// $("#jointContentCode", navTab.getCurrentPanel()).unwrap();
	//$("#jointContentCode", navTab.getCurrentPanel()).val($("#jointContentCodeHidden", navTab.getCurrentPanel()).val());
	 $("#jointContentCode", navTab.getCurrentPanel()).find("option").each(function(){
		 if($(this).val()==$("#jointContentCodeHidden", navTab.getCurrentPanel()).val()){
				$(this).attr("selected",true);
		}
	});   
	claimFireEvent($("#jointContentCode", navTab.getCurrentPanel()));
  }  
  
}, 300);   */
  

</script>


