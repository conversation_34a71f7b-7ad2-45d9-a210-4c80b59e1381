<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/pages/register/epilolyDeductEnter.js"></script>
<form id="queryOutsourceIfnoIdEnter"
	action="clm/register/epilolyDeductEnterInit_CLM_epilolyDeductEnterAction.action"
	method="post" class="pageForm required-validate"
	onsubmit="return  navTabSearch(this)">
	<div  layoutH="10">
			<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">外包费用参数设置
					</h1>
				</div>
			<div class="panelPageFormContent">
				<dl >
					<dt>
						<font>* </font>扣费字节数
					</dt>
					<dd>
						<input type="hidden" value="${outsourceDeductVO.deductId }"
							id="deductId"> <input type="text" ${outsourceDeductVO.deductId!=null?'disabled="disabled"':'' } 
							onblur="checkzs(this)" id="deductBytes" class="num" maxlength="8"
							value="${outsourceDeductVO.deductBytes }" />
					</dd>
				</dl>
				<dl >
					<dt>
						<font>* </font>时效达标率标准(≥)
					</dt>
					<dd>
						<input type="text" name="agingControlRate" id="agingControlRate"
							${outsourceDeductVO.deductId!=null?'disabled="disabled"':'' }  onblur="checkxs(this)"
							value="${outsourceDeductVO.agingControlRate }" class="num"
							maxlength="6" />
					</dd>
				</dl>
				<dl >
					<dt>
						<font>* </font>外包费用计算起止期
					</dt>

					<dd>
						<input style="width: 40px;" type="text" name="outsourceDeductVO.deductStart" id="deductStart_epilolyDeductEnter"
							${outsourceDeductVO.deductId!=null?'disabled="disabled"':'' }  onblur="checkDeductStart(this)"
							value="${outsourceDeductVO.deductStart }" class="num" size="2" maxlength="2" /> 
							<span>&nbsp; -</span>          
						<input style="width: 40px;" type="text" name="outsourceDeductVO.deductEnd" id="deductEnd_epilolyDeductEnter"
						disabled="disabled" value="${outsourceDeductVO.deductEnd }" class="num" size="2" maxlength="2" />
					</dd>
				</dl>
			</div>
			<div>
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">差错扣费标准
					</h1>
				</div>
				<div class="tabdivclassbr">
					<table class="list " width="100%">
						<thead>
							<tr >
								<th nowrap>序号</th>
								<th nowrap>正确率小值(≥)</th>
								<th nowrap>正确率大值(<)</th>
								<th nowrap>扣除差错案件字节数的倍数</th>
								<th nowrap>操作</th>
							</tr>
						</thead>
						<tbody id="outsourceErrorDeductTbody">
							<s:iterator value="outsourceErrorDeductVOs" status="st">
								<tr>
									<td style="text-align: center;">${st.index+1}</td>
									<td><input style="width: 99%;" type="text" class="num"
										maxlength="6" onblur="checkxs(this)" disabled="disabled"
										value="${minCorrect}"></td>
									<td><input style="width: 99%;" type="text" class="num"
										maxlength="6" onblur="checkxs(this)" disabled="disabled"
										value="${maxCorrect}"></td>
									<td><input style="width: 99%;" type="text" class="num"
										maxlength="8" disabled="disabled"
										value="${errorDeductMultiple}" onblur="checkzs(this)"></td>
									<td><input type="hidden" value="${errorDeductId }"><a
										title='删除' class='btnDel' id='delButton'
										style='display: block; margin: 0 auto; float: none;'
										href='javascript:void(0);'
										onclick='deleteErrorDeduct(this,${st.index});'>删除</a></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<div >
						<a style='display: block; margin: 0 auto; float: none;' title='添加'
							class='btnAdd' id='addButton' href='javascript:void(0);'
							onclick='addOutsourceErrorDeduct(this);'>添加</a>
					</div>
				</div>
			</div>
			<div class="panelPageFormContent" id="">
					<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">超时效扣费标准
					</h1>
				</div>
				<div class="tabdivclassbr" id="">
					<table class="list" width="100%">
						<thead>
							<tr align="center">
								<th nowrap>序号</th>
								<th nowrap>时效小值(>)(m)</th>
								<th nowrap>时效大值(≤)(m)</th>
								<th nowrap>扣除超时案件字节数比例</th>
								<th nowrap>操作</th>
							</tr>
						</thead>
						<tbody id="outsourceAgingDeductTbody">
							<s:iterator value="outsourceAgingDeductVOs" status="st">
								<tr>
									<td style="text-align: center;">${st.index+1}</td>
									<td><input style="width: 99%;" type="text" class="num"
										maxlength="8" onblur="checkzs(this)" disabled="disabled"
										value="${minAging}"></td>
									<td><input style="width: 99%;" type="text" class="num"
										maxlength="8" onblur="checkzs(this)" disabled="disabled"
										value="${maxAging}"></td>
									<td><input style="width: 99%;" type="text" class="num"
										maxlength="6" disabled="disabled" value="${agingDeductRate}"
										onblur="checkxs(this)"></td>
									<td><input type="hidden" value="${agingDeductId }"><a
										title='删除' class='btnDel' id='delButton'
										style='display: block; margin: 0 auto; float: none;'
										href='javascript:void(0);'
										onclick='deleteAgingDeduct(this,${st.index});'>删除</a></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					<div >
						<a title='添加' class='btnAdd' id='addButton'
							style='display: block; margin: 0 auto; float: none;'
							href='javascript:void(0);'
							onclick='addOutsourceAgingDeduct(this);'>添加</a>
					</div>
				</div>
			</div>
		<div class="formBarButton">
			<input type="hidden" id="deleteOutsourceErrorDeduct"> <input
				type="hidden" id="deleteOutsourceAgingDeduct">
			<ul>
				<li>
					<button class="but_blue" type="button" onclick="update()">修改</button>
				</li>
				<li>
					<button class="but_blue" type="button" id="saveButton" onclick="saveEpilolyDeduct();">保存</button>
				</li>
				<li>
					<button class="but_gray" type="button" onclick="exit()">退出</button>
				</li>
			</ul>	
		</div>
	</div>
</form>