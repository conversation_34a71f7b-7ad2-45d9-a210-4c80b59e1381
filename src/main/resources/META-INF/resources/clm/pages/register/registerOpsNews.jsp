<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript">
 
function specialTypeChange(obj){ 
	 var relaCode=$(obj).attr("value");
	 var surgeryCode =  $("#specialCode", navTab.getCurrentPanel());
	if(relaCode!=null&&relaCode!=""){
	   	var  thisPage=$(obj).parent().parent().parent().attr("id");
	 
	  	var fatherTag ='';
		if(thisPage=='dutyHospitalCostInitDetails'){//是详细
			fatherTag=$(obj).parent().parent().parent();
		}else{
			fatherTag=$(obj).parent().parent();
		}
		 $.ajax({
			'type':'post',
			'url':'clm/register/querySpecialDiseaseItem_CLM_tClaimDutyRegisterAction.action?SpecialDiseaseTypeCode='+relaCode,
			'datatype':'json',
			'success':function(data){
				var data = eval("(" + data + ")");
				$(surgeryCode).empty(); 
				var options="";
		        options="<option value=''>请选择</option>";
				for(var i = 0; i < data.length; i++){
					options+="<option value='"+data[i].code+"'>"
	                + data[i].name
	                + "</option>";
				}  
				$(options).appendTo(surgeryCode);
				$(surgeryCode).val('');
				$(surgeryCode).prev().val('-请选择');
	// 			$("#specialCode", navTab.getCurrentPanel()).loadMyComboxOptions(options,'1');
			},
			'error':function(){
				alert("出错了！");
			}
		});
	}else{
		$(surgeryCode).val('');
		$(surgeryCode).prev().val('-请选择');
		$(surgeryCode).empty(); 
	}
}
 
 
  
 
 function  findFT(obj){ 
 
	if($(obj).val().trim()=="FT022"){
		
		 
		    var  jointCode=$("#jointContentCode", navTab.getCurrentPanel());
		    var  jointPayRate=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#jointPayRate");
		  var  jointCodeValue=$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#jointPayRate").find("option:selected").val();  
		  $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#jointContentCodeDL").css("display","");
		   $("#jointPayRate", navTab.getCurrentPanel()).parent().parent().css("display","");
		 $.ajax({
	 			'type':'post',
	 			'url':'clm/register/findAllJointCode_CLM_tClaimDutyRegisterAction.action',
	 			'datatype':'json',
	 			'success':function(data){
	 				var data = eval("(" + data + ")");
	 				var value=$(jointCode).val();
	 			 	$("#jointContentCode", navTab.getCurrentPanel()).empty();
	 		     	var options = "<option value='' class = ''>请选择 </option>";
	 				  for(var i = 0; i < data.length; i++){ 
	 					options += "<option hiddenValue='"+data[i].jointPayRate+"' value='"+data[i].jointContentCode+"'    >"+data[i].jointContentDesc+"</option>";
	 					   
	 				}  
	 			      

	 				 
	 				$(jointCode).loadMyComboxOptions(options,value);  
	 				claimFireEvent($("#jointContentCode", navTab.getCurrentPanel()));
	 				//$("a[name='claimSurgeryVOlist[0].jointContentCode']").val("");
	 			},
	 			'error':function(){
	 				alert("出错了！");
	 			}
	 	});    
		
            		
	}else{
		
		  $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#jointContentCodeDL").css("display","none");
		  $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("#jointPayRate").parent().parent().css("display","none");
		
	}
	 
	 
 }
 
 
 
function  changeJointRate(obj){ 
	
	var jointPayRate=$(obj).find("option:selected").attr("hiddenValue");
	
	  $("#jointPayRate", navTab.getCurrentPanel()).val(jointPayRate);
	
	
}
 

//单选按钮

function radiockeacdOps(obj){  
	//定位数据 
	var innerPage = $(obj).parent().parent().parent().parent()
			.parent().parent();

	var pageFlag = $(innerPage).parent().attr("titles");
	var billType = $(innerPage).parent().attr("billType");

	var surgeryId = $(obj).parent().find("#surgeryId").val();
	
	 
	var caseId = $("#caseId", navTab.getCurrentPanel()).val();
	 

  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
			+ pageFlag 
			+ '&caseId='
			+ caseId 
		 	+ '&claimSurgeryVO.surgeryId=' + surgeryId
		 	+'&claimSurgeryVO.caseId=' + caseId;
	$(innerPage).next().next().loadUrl(url);

	$(innerPage).next().next().show("30000"); 

	return;
	 
}


function  deleteValue(obj){ 
	
	var surgeryId = $(obj).parent().find("#surgeryId").val();
	 
	if(surgeryId!=""&&surgeryId!="0"){  
	$.ajax({ 'type':'post',
			'url':'clm/register/deleteClaimDutyInfo_CLM_tClaimDutyRegisterAction.action'+ '?claimSurgeryVO.surgeryId=' + surgeryId+'&claimTypecode[0]=3',
			'datatype':'json',
			'success':function(data){
				 if(data){ 
					 
					 $(obj).parent().parent().remove();
					 alertMsg.info("删除成功");
				 }else{
					 alertMsg.info("删除失败");
				 }
			    
			},
 			'error':function(){
 				alert("出错了！");
 			}
	 });
	}
	 
}

//用于伸缩按钮的伸缩功能
//$(document).ready(function(){
	  
$(".main_heading", navTab.getCurrentPanel()).off().toggle(function(){  
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
		  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
	
},function(){
  $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
 	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
});

//});

//多级标签的处理
$(function(){    
	$("li>h5","[id^=main_]").bind("click",function(){
//		var id=$(this).attr("id").split("main_")[1];
	    var li=$(this).parent();
		var hasborder=$(this).attr("hasborder");
		
		if(li.hasClass("fold")){
			if (hasborder=="true"){
				$(this).parent().parent().parent().addClass("main_borderbg");
			}
			li.removeClass("fold");
			$(this).find("#two").removeClass("main_plus").addClass("main_minus");
			$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
			
			li.find(".main_foldContent").slideDown();
			
		}else{
			li.addClass("fold");
			if (hasborder=="true"){
				$(this).parent().parent().parent().removeClass("main_borderbg");
			}
			$(this).find("#two").removeClass("main_minus").addClass("main_plus");
			$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
			li.find(".main_foldContent").slideUp();
			
		}
	});
	
})

</script>
			<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">特定手术/疾病/给付录入
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
										<div class="main_bqtitle">
					                         <h1><img src="clm/images/three.png">特定手术/疾病/给付信息录入</h1>
					                     </div>
					                     
					                     	<div id="opsNewsInt" titles="registerGreat" billType="8">
													<div class="panelPageFormContent">
														<div class="tabdivclassbr">		
														<table class="list nowrap itemDetail" addButton="添  加"
															hiddenAdd="hiddenAdd" style="border: 2px solid #CCC;width: 100%;">
															<thead>
																<tr>
																	<th nowrap>选择</th>
																	<th nowrap>序号</th>
												
																	<th nowrap backValueFlag="surgeryType"><font class="point"
																		color="red">* </font>特定费用类型</th>
												
																	<th nowrap backValueFlag="surgeryCode"><font class="point"
																		color="red">* </font>特定费用代码</th>
												
																	<th nowrap backValueFlag="sumAmount">金额</th>
												
																	<th nowrap backValueFlag="medOrgName"><font class="point"
																		color="red">* </font>医疗机构名称</th>
												
																	<th nowrap backValueFlag="diagnoseDate" dateFlag="dateFlag"><font
																		class="point" color="red">* </font>确诊日期</th>
												
																	<th nowrap type="del" >操作</th>
																</tr>
															</thead>
															<tbody class="list" id="claimSurgeryTBody">
																<s:iterator value="claimSurgeryVOlist" status="st">
																	<tr>
																		<td><input type='radio' name="radio" id='radio${st.index}'
																			value='0' onclick="radiockeacdOps(this)" class="MedRadio" />
																			<input
																			type='hidden' id='surgeryId' value='${surgeryId}' /></td>
																		<td><input class="digits textInput" type="text" size="5"
																			value="${st.index+1}" readonly="readonly" />  </td>
																		<td><Field:codeValue
																				tableName="APP___CLM__DBUSER.T_SPECIAL_DISEASE_TYPE"
																				value="${surgeryType}" /></td>
																		<td><Field:codeValue
																				tableName="APP___CLM__DBUSER.T_SPECIAL_DISEASE_ITEM"
																				value="${surgeryCode}"  whereClause="RELA_CODE=${surgeryType}" /></td>
																		<td>${sumAmount}</td>
																		<td>${medOrgName}</td>
																		<td><s:date name='diagnoseDate' format='yyyy-MM-dd' /></td>
																		<td><a class="btnDel" href="javascript:void(0)" onclick="deleteValue(this)"></a> <input
																			type='hidden' id='surgeryId' value='${surgeryId}' /></td>
																	</tr>
																</s:iterator>
															</tbody>
														</table>
														</div>
													</div>
													<s:include value="addBarForDuty.jsp" />
												</div>
					                     
									</div>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>
