<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="scheme">${pageContext.request.scheme}</s:set>
<s:set var="serverName">${pageContext.request.serverName}</s:set>
<s:set var="serverPort">${pageContext.request.serverPort}</s:set>
<%
	String accType = request.getParameter("accType");
	String id = request.getParameter("id");
%>
<%-- <input type="text" size="5" name="claimAccidentResultVO.accResult1"  lookupGroup="claimAccidentResultVO"/><input name="claimAccidentResultVO.accResultDesc1" type="text" lookupGroup="claimAccidentResultVO"/><a class="btnLook" href="clm/report/accResultQueryInit_accResultQueryAction.action?accType=<%=accType%>"  lookupGroup="claimAccidentResultVO">出现结果查询</a>
 --%>
 
 <script type="text/javascript">
 /**
 *此页面为责任明细点击共同的编辑页面, 数据是从点击编辑的行中带来的，赋值dom到编辑区，其中一些在行中字段需要隐藏，已经做了隐藏处理。
 *行中数据->（点击带到编辑区）编辑区->修改保存成功后数据数据复制回行中
 */
          
   
      
	  ///放在页面加载最上部
	  //编辑区 div
        $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html("");
       //选中的行
		var medRadio = $(".MedRadio:checked", navTab.getCurrentPanel())
				.parent().parent().parent().parent();
          
		var medRadios = $(".MedRadio:checked", navTab.getCurrentPanel())
				.parent().parent();
		$(medRadio).find("th").each(
				function(i, num) {
					if (i > 1 && i < $(medRadio).find("th").length - 1) {
						  //拼写dom
						var html = "<dl style='width: 50%'>"
								+ "<dt style='width: 37%'>" + $(this).html()
								+ "</dt>" + "<dd style='width: 60%' title='"+i+"'>"
								+ $(medRadios).find('td:eq(' + i + ')').html()
								+ "</dd></dl>";
						 	
						$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).append(html);
						
						//disable 去掉
					}
					 
				});
		 
		/* $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("a,select,input").each(function(){
			  
			//$(this).attr("readolny","disabled");
			$(this).removeAttr("readonly");
			 
		}) ; */
		
		  
		//【加上不会出错   查找带回问题
		$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find(".btnLook").each(function(){
		  
		//$(this).attr("readolny","disabled");
		var number =$(this).parent().attr("title");
		 
		 $(this).html("");
		 var obj=$(medRadios).find('td:eq(' + number + ')').children();
		
		 $(this).append(obj); 
		 $(medRadios).find('td:eq(' + number + ')').append(obj);
		
    	}) ;  
		
		
		
		//
		//如果按下enter建 新建一行
	    bindEnter();
		function bindEnter(){
		$("#feeItemId", navTab.getCurrentPanel()).find("input,select,a").each(function(i,obj){
            $(this).die().live("keyup",function(){
           	if(event.keyCode=="13"){
           		 $("#dutyHospitalCostInit", navTab.getCurrentPanel()).find("button[type='button']").click();  
           		 $("#feeItemId tr:last td:first", navTab.getCurrentPanel()).find("input").focus();
            	}
            }); 
	     });
		}
		
		$("#dutyHospitalCostInit", navTab.getCurrentPanel()).find("button[type='button']").die().live("click",function(){
			 bindEnter();	
		});
		
		
	    

	////如果存在  hidden说明加载时加入的参数
	      var titleFlag=$(".MedRadio:checked", navTab.getCurrentPanel()).attr("title");
		 if(titleFlag=="hidden"){
			 $(".MedRadio:checked", navTab.getCurrentPanel()).attr("title","");//恢复
			 $("#saveButton", navTab.getCurrentPanel()).nextAll().each(//删除之后所有标签
			function(){
				 $(this).remove(); 
			} ); 
		  }
  
 </script>
 

 
 
<script type="text/javascript">
	if (readOnly == 1 || isApprove == "approve") {
		//隐藏dwz封装的添加中的input
		function buttonRowNum() {
			$("div#dutyHospitalCostInit", navTab.getCurrentPanel()).find(
					"div.buttonContent").find("button").attr("disabled", true);
			$("[name='dwz_rowNum']", navTab.getCurrentPanel()).css("display",
					"none");
		}
		//延迟dwz
		setTimeout('buttonRowNum()', 100);
	}
	//回退页面禁掉费用明细标签
	if (readOnly == 1) {
		ReadOnly();
	}
	//审批禁用
	if (isApprove == "approve") {
		readOnly("hospitalcost");
	}

	///该配按下产品定义的所有费用业务类型项目
  	var medicalFeeTypeList = "${medicalFeeTypeList}";
	var medicalFeeTypeMap = medicalFeeTypeList.substring(1,
			medicalFeeTypeList.length - 1)
			+ "";
	var feeItemList = medicalFeeTypeMap.split(","); 

	function calcAmount(obj) {
		var medFeeAmount = $(obj).val();
		var deductAmount = $(obj).parent().parent().find("td").eq(4).find(
				"input").val();
		var dutyFeeItem = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").val();
		var dutyFeeItemName = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").html();
		var exitValue = false;

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		for (var i = 0; i < feeItemList.length; i++) {

			if (trim(dutyFeeItem) == trim(feeItemList[i])) {
				//	alert(dutyFeeItem+"---"+feeItemList[i]);
				exitValue = true;
			}
		}
		//如果未定义 扣除金额自动调整为等于费用金额。  扣除原因自动记录为其他。扣除备注自动记录为：扣除【费用类型名称】【费用金额】元。
		if (!exitValue) {
			deductAmount = medFeeAmount;
			$(obj).parent().parent().find("td").eq(4).find("input").val(
					medFeeAmount);
			$(obj).parent().parent().find("td").eq(6).find("select").val("2");
			$(obj).parent().parent().find("td").eq(7).find("input").val(
					"扣除【" + dutyFeeItemName + "】【" + deductAmount + "】元");
		}

		$(obj).parent().parent().find("td").eq(5).find("input").val(
				medFeeAmount - deductAmount);
	}
	function calcAmount2(obj) {
		var deductAmount = $(obj).val();
		var medFeeAmount = $(obj).parent().parent().find("td").eq(3).find(
				"input").val();
		var dutyFeeItem = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").val();
		var dutyFeeItemName = $(obj).parent().parent().find("td").eq(1).find(
				"option:selected").html();

		var exitValue = false;

		//调用产品工厂的费用类型计算参数，如果该费用类型在产品工厂有定义，则为费用金额-扣除金额，如果未定义，则为0，扣除金额自动调整为等于费用金额。
		for (var i = 0; i < feeItemList.length; i++) {
			if (trim(dutyFeeItem) == trim(feeItemList[i])) {
				exitValue = true;
			}
		}

		//如果未定义 扣除金额自动调整为等于费用金额。 扣除原因自动记录为其他。扣除备注自动记录为：扣除【费用类型名称】【费用金额】元。
		/* if (!exitValue) {
			deductAmount = medFeeAmount;
			$(obj).val(medFeeAmount);
			$(obj).parent().parent().find("td").eq(6).find("select").val("2");
			$(obj).parent().parent().find("td").eq(7).find("input").val(
					"扣除【" + dutyFeeItemName + "】【" + deductAmount + "】元");

		} */

		$(obj).parent().parent().find("td").eq(5).find("input").val(
				medFeeAmount - deductAmount);
	}
	//保存费用明细
	function saveFeeItem() {
		
		 
		
		/* backValue(); */
		//所有的费用明细项目
		var rows = $("#feeItemId", navTab.getCurrentPanel()).find("tr");
		//所有的费用明细项目行数
		var len = rows.length;
		//总费用
		var sumFee = parseFloat(0.00);
		//扣除费用
		var sumDeduct = parseFloat(0.00);
		//理算金额
		var calcFee = parseFloat(0.00);
		//--------保存费用明细验证金额输入是否正确-------
		$("#feeItemId", navTab.getCurrentPanel()).find("tr").each(function() {
			var obj1 = $(this).find("input:eq(3)");
			var obj2 = $(this).find("input:eq(4)");
			//金额为空赋值为0
			if (obj1.val() == "") {
				obj1.val("0");
			}
			if (obj2.val() == "") {
				obj2.val("0");
			}
		});
		for (var i = 0; i < len; i++) {
			if (rows[i].cells[1].childNodes[0].value == "") {
				alertMsg.error("费用明细项目必填！");
				errorReturn();
				return;
			}
			if (rows[i].cells[4].childNodes[1].value > 0) {
				if (rows[i].cells[6].childNodes[0].value == "") {
					alertMsg.error("扣除原因必填！");
					errorReturn();
					return;
				} else {
					if (rows[i].cells[7].childNodes[0].value == "") {
						alertMsg.error("扣除备注必填！");
						errorReturn();
						return;
					}
				}
			}
			sumFee = sumFee + parseFloat(rows[i].cells[3].childNodes[1].value);
			sumDeduct = sumDeduct
					+ parseFloat(rows[i].cells[4].childNodes[1].value);
			calcFee = calcFee
					+ parseFloat(rows[i].cells[5].childNodes[1].value);
		}
		//当前选中的医疗单证
		var medRadio = $(".MedRadio:checked", navTab.getCurrentPanel())
				.parent().parent();
		var caseId = $("#caseId", navTab.getCurrentPanel()).attr("value");
		//医疗单证子表数据
		var claimBillItemJson = new Array();
		var installment = [ "medFeeItem","operationCode","feeAmount", "deductAmount",
				"calcAmount", "deductReason", "deductRemark" ];
		claimBillItemJson += "[";
		$("#feeItemId tr", navTab.getCurrentPanel()).each(function() {
					 claimBillItemJson += "{";
							$(this).children("td").each(function(i) {
								if (i == 1 || i == 6) {
								var medFeeItem = $(this).find("select").val();
							    claimBillItemJson = claimBillItemJson+ "'"+ installment[i - 1]+ "':'"
									                + medFeeItem + "',";
								} else if (i != 0 && i != 8) {
								 var feeAmount = $(this).find("input").val();
									claimBillItemJson = claimBillItemJson+ "'"+ installment[i - 1]
									+ "':'"+ feeAmount+ "',";
								}
							});
							claimBillItemJson = claimBillItemJson.substring(0,
									claimBillItemJson.length - 1);
							claimBillItemJson += "},";
						});

		if (claimBillItemJson != '[') {
			claimBillItemJson = claimBillItemJson.substring(0,
					claimBillItemJson.length - 1);
			claimBillItemJson += ']';
		}
		claimBillItemJson.toString();
		 
		//医疗单证主表数据
		var medRadioTd = $(medRadio).find("td");
		var billType = $(medRadio).parent().find("input").val();
		var billNo = "";
		var hospitalCode = "";
		var treatType = "";
		var treatStart = "";
		var treatEnd = "";
		var accDetail = "";
		var icdCode = "";
		var sumAmount = "";
		var deductAmount = "";
		var calcAmount = "";
		var billId = "";
		if (billType == 1) {
			$(medRadio).find("td").eq(14).find("input").val(sumFee);
			$(medRadio).find("td").eq(15).find("input").val(sumDeduct);
			$(medRadio).find("td").eq(16).find("input").val(calcFee);
			//获取被选中行的数据
			billNo = $(medRadioTd).eq(2).find("input").val();
			hospitalCode = $(medRadioTd).eq(3).find("#hospitalId").val();
			treatType = $(medRadioTd).eq(5).find("select").val();
			treatStart = $(medRadioTd).eq(6).find("input").val();
			treatEnd = $(medRadioTd).eq(7).find("input").val();
			accDetail = $(medRadioTd).eq(12).find("select").val();
			icdCode = $(medRadioTd).eq(13).find("select").val();
			sumAmount = $(medRadioTd).eq(14).find("input").val();
			deductAmount = $(medRadioTd).eq(15).find("input").val();
			calcAmount = $(medRadioTd).eq(16).find("input").val();
			billId = $(medRadioTd).eq(17).find("input").val();
		} else if (billType == 2) {
			$(medRadioTd).eq(12).find("input").val(sumFee);
			$(medRadioTd).eq(13).find("input").val(sumDeduct);
			$(medRadioTd).eq(14).find("input").val(calcFee);
			//获取被选中行的数据
			billNo = $(medRadioTd).eq(2).find("input").val();
			hospitalCode = $(medRadioTd).eq(3).find("#hospitalId").val();
			treatType = $(medRadioTd).eq(6).find("select").val();
			treatStart = $(medRadioTd).eq(7).find("input").val();
			treatEnd = $(medRadioTd).eq(8).find("input").val();
			//-------------缺个社保/公费/第三方支付
			sumAmount = $(medRadioTd).eq(12).find("input").val();
			deductAmount = $(medRadioTd).eq(13).find("input").val();
			calcAmount = $(medRadioTd).eq(14).find("input").val();
			billId = $(medRadioTd).eq(15).find("input").val();
		} else if (billType == 3) {
			$(medRadioTd).eq(12).find("input").val(sumFee);
			$(medRadioTd).eq(13).find("input").val(sumDeduct);
			$(medRadioTd).eq(14).find("input").val(calcFee);
			//获取被选中行的数据
			billNo = $(medRadioTd).eq(2).find("input").val();
			hospitalCode = $(medRadioTd).eq(3).find("#hospitalId").val();
			treatType = $(medRadioTd).eq(6).find("select").val();
			treatStart = $(medRadioTd).eq(7).find("input").val();
			treatEnd = $(medRadioTd).eq(8).find("input").val();
			//-------------缺个社保/公费/第三方支付
			sumAmount = $(medRadioTd).eq(12).find("input").val();
			deductAmount = $(medRadioTd).eq(13).find("input").val();
			calcAmount = $(medRadioTd).eq(14).find("input").val();
			billId = $(medRadioTd).eq(15).find("input").val();
		}
		
		 
		$.ajax({
			'type' : 'post',
			'url' : 'clm/register/saveFeeItem_CLM_tClaimDutyRegisterAction.action',
			'data' : {
				'claimBillVO.billNo' : billNo,
				'claimBillVO.hospitalCode' : hospitalCode,
				'claimBillVO.treatType' : treatType,
				'claimBillVO.treatStart' : treatStart,
				'claimBillVO.treatEnd' : treatEnd,
				'claimBillVO.accDetail' : accDetail,
				'claimBillVO.icdCode' : icdCode,
				'claimBillVO.sumAmount' : sumAmount,
				'claimBillVO.deductAmount' : deductAmount,
				'claimBillVO.calcAmount' : calcAmount,
				'claimBillVO.caseId' : caseId,
				'claimBillVO.billType' : billType,
				'claimBillVO.billId' : billId,
				'claimBillVO.claimBillItemJson' : claimBillItemJson
			},
			'datatype' : 'json',
			'success' : function(data) {
				var data = eval("(" + data + ")");
				if (data.statusCode == '200') {
					$("#hospitalcost", navTab.getCurrentPanel()).hide("3000",
							function() {
								$(this).empty();
							});
					//对于新增的医疗单证，编辑明细费用保存时要返回billId
					if (billId == "") {
						var billIdSubmitName = "";
						if (billType == 1) {
							billId = $(medRadioTd).eq(17).find("input").attr(
									"value", data.message);
							billIdSubmitName = $(medRadioTd).eq(17).find(
									"input").attr("name");
							$(medRadioTd).eq(17).find("input").attr(
									"name",
									"claimBillVOlist" + billIdSubmitName
											+ ".billId");
						} else if (billType == 2) {
							billId = $(medRadioTd).eq(15).find("input").attr(
									"value", data.message);
							billIdSubmitName = $(medRadioTd).eq(15).find(
									"input").attr("name");
							$(medRadioTd).eq(15).find("input").attr(
									"name",
									"claimHighBillVOlist" + billIdSubmitName
											+ ".billId");
						} else if (billType == 3) {
							billId = $(medRadioTd).eq(15).find("input").attr(
									"value", data.message);
							billIdSubmitName = $(medRadioTd).eq(15).find(
									"input").attr("name");
							$(medRadioTd).eq(15).find("input").attr(
									"name",
									"claimCancerBillVOlist" + billIdSubmitName
											+ ".billId");
						}
					}
					alertMsg.correct("保存成功");
				} else {
					alertMsg.error("保存失败");
					errorReturn();
				}
			},
		});
	}

	 //读取一行数据  放入公用区域

	//创建一个新的一行
	function viewAddNew(obj) {
		$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html("");
		//alert($("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("DD:eq(0)").children().eq(0).val());

		$(obj).parent().prev().find("BUTTON[type='button']").click();
		setTimeout('addNewcheckbox()', 250);
	}

	function addNewcheckbox() {

		var length = $("#AddNewButton", navTab.getCurrentPanel()).parent().prev().find('.MedRadio').length-1;
 
		$("#AddNewButton", navTab.getCurrentPanel()).parent().prev().find(".MedRadio:eq("+length+")").click();
		// 绑定事件 录入enter建增加一行 
	}
  
	function backValue2() {
		
		if($("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).size()==0){
			return; 
		}
        //获取选中的行
		var medRadios = $(".MedRadio:checked", navTab.getCurrentPanel())
				.parent().parent();
        //存放现行数据的区域  并写入  逆向赋值
		$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("dd").each(
				function(i, num) {
					var  obj=$(this).children();
					var numer = i + 2;
					$(medRadios).find('td:eq(' + numer + ')').html("");
					$(medRadios).find('td:eq(' + numer + ')').append(
							obj);
					 
					//置灰不可操作
					/* $(medRadios).find('td:eq(' + numer + ')').find("a,select,input").each(function(){
						 $(this).attr("readonly",true);
						  
					}) ; */

				});
		//alert("sss");
		
		//清空编辑区
		$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html("");

		//执行保存
		
		var  flag="${flag}";///医疗或者高端医疗标识
		
		if(flag=="1"||flag=="2"||flag=="3"){
		saveFeeItem();
		}else{
			//如果不是 则更改相应的title
		   $("#hospitalcost", navTab.getCurrentPanel()).hide("3000",
				function() {
					$(this).empty();
		   });
		 }
		
		
		
	}
	 
	//
	function errorReturn(){
		
		 $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).html("");
			var medRadio = $(".MedRadio:checked", navTab.getCurrentPanel())
					.parent().parent().parent().parent();

			var medRadios = $(".MedRadio:checked", navTab.getCurrentPanel())
					.parent().parent();
			$(medRadio).find("th").each(
					function(i, num) {
						if (i > 1 && i < $(medRadio).find("th").length - 1) {
							  
							var html = "<dl style='width: 50%'>"
									+ "<dt style='width: 37%'>" + $(this).html()
									+ "</dt>" + "<dd style='width: 60%' title='"+i+"'>"
									+ $(medRadios).find('td:eq(' + i + ')').html()
									+ "</dd></dl>";
							 	
							$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).append(html);
							
							//disable 去掉
						}
						 
					});
			 
			/* $("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find("a,select,input").each(function(){
				  
				//$(this).attr("readolny","disabled");
				$(this).removeAttr("readonly");
				 
			}) ; */
			
			  
			//【加上不会出错   查找带回问题
			$("#dutyHospitalCostInitDetails", navTab.getCurrentPanel()).find(".btnLook").each(function(){
			  
			//$(this).attr("readolny","disabled");
			var number =$(this).parent().attr("title");
			 
			  
			 var obj=$(medRadios).find('td:eq(' + number + ')').children();
			  var obj2= $(obj).clone();
			  
			 $(this).parent().html(obj2); 
			 
		 
			
	    	}) ;  
		
		
	}
	 //添加录入字样
	 if("${flag}"!="1"&&"${flag}"!="2"&&"${flag}"!="3"){
	   $("#panelHeaderInt", navTab.getCurrentPanel()).html( $("#hospitalcost", navTab.getCurrentPanel()).prev().find(".panelHeaderContent h1").html());
	  //社保第三方
	   if($("#hospitalcost", navTab.getCurrentPanel()).prev().find(".panelHeaderContent h1:eq(1)").html()!=null){
		   $("#panelHeaderInt", navTab.getCurrentPanel()).html( $("#hospitalcost", navTab.getCurrentPanel()).prev().find(".panelHeaderContent h1:eq(1)").html());  
	   }
	   $("#hospitalcost", navTab.getCurrentPanel()).prev().find(".panelHeader").css("display","none");
	 }
	 
	 /* function  backValue3(){ 
		 $("#registerAddhas", navTab.getCurrentPanel()).itemDetail(); 
	 }; */
</script>
<!--账单及费用明细  div start -->


<a class="button" onclick="viewAddNew(this)"  id="AddNewButton"   ><span>添加</span></a>
<a class="button" onclick="backValue2()" id="saveButton"><span>保存</span></a>

<div class="panelPageFormContent" >
<s:if test="flag==1 or flag==2 or flag==3">
		<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">账单及费用明细
					</h1>
				</div>
</s:if>
<s:else  >
	<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">账单及费用明细
					</h1>
				</div>
</s:else>
   
	<div class="pageFormContent" id="dutyHospitalCostInitDetails"></div>
	<!--医疗费用明细  div end  -->
</div>

<!--医疗费用明细  div end-->
<s:if test="flag==1 or flag==2 or flag==3">
	<!--医疗费用明细  div start -->
	<div id="dutyHospitalCostInit"   >
		<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">费用明细
					</h1>
				</div>
		<div class="panelPageFormContent">
			<div class="tabdivclassbr">
			<table class="list nowrap itemDetail" addButton="添  加" id="addcost" autoAdd="autoAdd"
				style="border: 2px solid #CCC;width: 100%;">
				<thead>
					<tr> 
					<th nowrap type="text" name="items[#index#].itemInt" readonly
							defaultVal="#index#" size="5">序号</th>

						<s:if test="flag == 1">
							<th type="enum" name=""
								enumUrl="clm/pages/html/dutyMedFeeItem1.jsp" size="12">费用明细项目</th>
						</s:if>
						<s:elseif test="flag == 2">
							<th nowrap type="enum" name=""
								enumUrl="clm/pages/html/dutyMedFeeItem2.jsp" size="12">费用明细项目</th>
						</s:elseif>
						<s:elseif test="flag == 3">
							<th nowrap type="enum" name=""
								enumUrl="clm/pages/html/dutyMedFeeItem.jsp" size="12">费用明细项目</th>
						</s:elseif>

						<th nowrap type="enum" name=""
							enumUrl="clm/pages/html/operationPage.jsp" size="10" >手术代码</th>

						<th nowrap type="enum" name=""
							enumUrl="clm/pages/html/dutyMedFeeAmount.jsp" size="10" >费用金额</th>

						<th nowrap type="enum" name=""
							enumUrl="clm/pages/html/dutyDeductAmount.jsp" size="10" >扣除费用</th>

						<th nowrap type="enum" name=""
							enumUrl="clm/pages/html/dutyCalcAmount.jsp" size="12">理算金额</th>

						<th nowrap type="enum" name=""
							enumUrl="clm/pages/html/dutyDeductReason.jsp" size="12">扣除原因</th>

						<!-- <th type="text" name="claimBillItemVOlist[#index#].deductRemark" size="12">扣除备注</th> -->
						<th nowrap type="enum" name=""
							enumUrl="clm/pages/html/dutyDeductRemark.jsp" size="12">扣除备注</th>
						<th nowrap type="del" width="60">操作</th>
					</tr>
				</thead>
				<tbody class="list" id="feeItemId">
					<s:if
						test="claimBillItemVOlist != null||claimBillItemVOlist.size()>0">
						<s:iterator value="claimBillItemVOlist" status="st">
							<tr>
								<td><input class="digits textInput" type="text" size="5"
									value="${st.index+1}" /></td>

								<td name="费用明细项目"><s:if test="flag == 1">
										<Field:codeTable cssClass="combox title"
											name="claimBillItemVOlist[${st.index}].medFeeItem"
											tableName="APP___CLM__DBUSER.T_INVOICE_TYPE"
											nullOption="true" value="${medFeeItem}"
											whereClause="CODE like 'C%'" orderBy="code"/>
									</s:if> <s:elseif test="flag == 2">
										<Field:codeTable cssClass="combox title"
											name="claimBillItemVOlist[${st.index}].medFeeItem"
											tableName="APP___CLM__DBUSER.T_INVOICE_TYPE"
											nullOption="true" value="${medFeeItem}"
											whereClause="CODE like 'Q%'" orderBy="code"/>
									</s:elseif> <s:elseif test="flag == 3">
										<Field:codeTable cssClass="combox title"
											name="claimBillItemVOlist[${st.index}].medFeeItem"
											tableName="APP___CLM__DBUSER.T_INVOICE_TYPE"
											nullOption="true" value="${medFeeItem}" orderBy="code"/>
									</s:elseif></td>

								<td name="手术代码"><input id="operationCodeId"
									name="claimBillItemVOlist[${st.index}].operationCode"
									value="${operationCode}" size="10" type="hidden" /> <input
									id="operationNameId"
									name="claimBillItemVOlist[${st.index}].operationDesc"
									value="<Field:codeValue tableName="APP___CLM__DBUSER.T_OPERATION" value='${operationCode}'/>"
									type="text" /> <a class="btnLook"
									href="clm/report/queryOperationCode_CLM_hospitalInfoQueryAction.action"
									lookupGroup="claimBillItemVOlist[${st.index}]">查询手术代码</a></td>

								<td name="费用金额">
									<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
									<input type="text"
									name="claimBillItemVOlist[${st.index}].feeAmount"
									class="number" maxlength="18" onchange="calcAmount(this);"
									value="${feeAmount}" />
								</td>

								<td name="扣除费用">
									<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
									<input type="text" onchange="calcAmount2(this);"
									name="claimBillItemVOlist[${st.index}].deductAmount"
									class="number" maxlength="18"  
									value="${deductAmount}" />
								</td>

								<td name="理算金额">
									<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
									<input type="text"
									name="claimBillItemVOlist[#index#].calcAmount" maxlength="18"
									readonly="readonly" value="${calcAmount}" />
								</td>

								<td name="扣除原因"><Field:codeTable cssClass="combox title"
										name="claimBillItemVOlist[${st.index}].deductReason"
										tableName="APP___CLM__DBUSER.T_MEDICAL_FEE_DEDUCTION"
										nullOption="true" value="${deductReason}" /></td>

								<td name="扣除备注"><input type="text"
									name="claimBillItemVOlist[${st.index}].deductRemark"
									value="${deductRemark}" maxlength="1000" /></td>
								<td><a class="btnDel"></a></td>
							</tr>
						</s:iterator>
					</s:if>
				</tbody>
			</table>
			 </div>
		</div>
		<!--医疗费用明细  div end  -->
	</div>
</s:if>



 