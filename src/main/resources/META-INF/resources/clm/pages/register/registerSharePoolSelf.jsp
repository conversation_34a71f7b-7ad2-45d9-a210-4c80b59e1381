<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8" import="java.util.*"%>
<%@include file="/udmpCommon.jsp"%>
<%@taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
 <script type="text/javascript">
 	function registerTaskForWard(id,obj){
 		$(obj).remove();
		navTab.openTab("20283","clm/register/toDataCollectInit_CLM_claimRegisterAction.action",{title:"立案登记",data:{caseId:id}});
	}
</script>
<div class="pageContent" id="registerSharePoolJsp">
	<!-- 个人池显示数据列表区域 -->
	<!-- 个人池分页查询访问路径 -->
	<form id="pagerForm" method="post"
		action="clm/register/findregistSharePoolSelf_CLM_registerSharePoolAction.action?leftFlag=0&menuId=${menuId }">
		<input type="hidden" name="pageNum" vaule="${registSharePoolResClientVOPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${registSharePoolResClientVOPage.pageSize}"/>
	</form>
	<form id="registerSharePoolJspFormSelf"
		action="clm/register/findregistSharePoolSelf_CLM_registerSharePoolAction.action?leftFlag=0&menuId=${menuId }"
		method="post" onsubmit="return navTabSearch(this)"
		class="pagerForm required-validate" rel="pagerForm">
	<div >
				<div class="divfclass">
					<h1>
						<img src="clm/images/tubiao.png">个人工作池
					</h1>
				</div>
				
			<div class="tabdivclassbr">
				<table class="list sortable main_dbottom" width="100%">
					<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>赔案号</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>证件号码</th>
						<th nowrap>赔案状态</th>
						<th nowrap>签收机构</th>
						<th nowrap>绩优等级</th>
						<th nowrap>签收人</th>
						<th nowrap>签收时间</th>
						<th nowrap>申请渠道</th>
					
					</tr>
				</thead>
				<tbody id="" align="center">
					<!-- 循环显示数据 -->
					<s:if test="imageFlagSelfSign != null">
						<tr>
							<td colspan="10">
								<div class="noRueryResult">没有符合条件的查询结果！</div>
							</td>
						</tr>
					</s:if>
						
					<s:iterator value="registSharePoolResClientVOPage.PageItems" status="st">
						<tr ondblclick="registerTaskForWard(${caseId},this)" style="height: 25px;" >
							<td>
								<div align="center" class="index">${st.index+1}</div>
							<td>
								<div align="center">${caseNo}</div>
							</td>
							<td>
								<div align="center">${customerName}</div>
							</td>
							<td>
								<div align="center">${customerCertiCode}</div>
							</td>
							<td>
								<div align="center"><Field:codeValue tableName="APP___CLM__DBUSER.T_CASE_STATUS" value="${caseStatus}"/></div>
							</td>
							<td>
								<div align="center" style="width: 220px;"><Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_ORG" value="${organCode}"/></div>
							</td>
							<td>
								<s:if test="greenFlag==0 || greenFlag==null">否</s:if>
	                            <s:else>
	                                <Field:codeValue value="${greenFlag}" tableName="APP___CLM__DBUSER.T_CLAIM_GREEN_FLAG"/>
	                            </s:else>
							</td>
							<td>
								<div align="center"><Field:codeValue tableName="APP___CLM__DBUSER.T_UDMP_USER" value="${signerId}"/></div>
							</td>
							<td>
								<div align="center"><s:date name="acceptTime" format="yyyy-MM-dd"/></div>
							</td>
							<td><Field:codeValue value="${channelCode}" tableName="APP___CLM__DBUSER.T_ACCEPT_CHANNEL" /></td>
							
						</tr>
					</s:iterator>
				</tbody>
				</table>
				<!-- 分页查询区域 -->
				<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
							name="select" onchange="navTabPageBreak({numPerPage:this.value},'registerSharePoolJsp')"
							value="registSharePoolResClientVOPage.pageSize">
						</s:select>
						<span>条，共${registSharePoolResClientVOPage.total}条</span>
					</div>
					<div class="pagination" targetType="navTab" rel="registerSharePoolJsp"
						totalCount="${registSharePoolResClientVOPage.total}"
						numPerPage="${registSharePoolResClientVOPage.pageSize}" pageNumShown="10"
						currentPage="${registSharePoolResClientVOPage.pageNo}"></div>
				</div>
			</div>
		</div>
		</form>
</div>