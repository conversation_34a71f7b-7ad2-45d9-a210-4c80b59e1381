<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<%-- <Field:codeTable cssClass="selectToInput"  name="claimBillVOlist[#index#].hospitalCode" tableName="T_HOSPITAL" nullOption="true" defaultValue=" " onChange="queryHospitalMsg(this);"/>
 --%><%-- <Field:codeTable  name="claimBillVOlist[#index#].hospitalCode" tableName="T_HOSPITAL" nullOption="true" cssClass="notuseflagDelete selectToInput comboxDD" defaultValue=" " showInput="true" showInputId="showInput" />
<input id="showInput" type="text"> --%>
<script type="text/javascript">

/*  alert("ss");
   $.find("#hospitalId").each(function(){
	   alert("ss");
	   $(this).preant().attr("hidden","hiddenFlag");
	   $(this).preant().css("display","none");
	   
   }) ; */

  
</script>


<input id="hospitalId" name="claimBillVOlist[#index#].hospitalCode" value="${hospitalCode}" size="10" type="hidden"   /> 
<input id="cureHospital" name="claimBillVOlist[#index#].hospitalName" value="<Field:codeValue tableName="T_HOSPITAL" value='${hospitalCode}'/>"    type="text"   /> 
<a class="btnLook" href="clm/report/showHospital_CLM_hospitalInfoQueryAction.action?leftFlag=0&menuId=${menuId}" 
lookupGroup="claimBillVOlist[#index#]">查找带回</a></td>