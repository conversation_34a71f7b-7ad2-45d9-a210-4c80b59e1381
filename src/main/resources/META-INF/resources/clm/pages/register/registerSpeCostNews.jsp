<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<script type="text/javascript" language="javascript">
	function specialTypeChange(obj) {
		var relaCode = $(obj).attr("value");

		var thisPage = $(obj).parent().parent().parent().attr("id");

		var fatherTag = '';
		if (thisPage == 'dutyHospitalCostInitDetails') {//是详细
			fatherTag = $(obj).parent().parent().parent();
		} else {
			fatherTag = $(obj).parent().parent();
		}

		var surgeryCode = $(fatherTag).find("#specialCode");

		$.ajax({
					'type' : 'post',
					'url' : 'clm/register/querySpecialCode_CLM_tClaimDutyRegisterAction.action?SpecialTypeCode='
							+ relaCode,
					'datatype' : 'json',
					'success' : function(data) {
						var data = eval("(" + data + ")");
						surgeryCode.empty();
						$(
								"<option value='-1' class = ''> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</option>")
								.appendTo(surgeryCode);
						for (var i = 0; i < data.length; i++) {
							var option2 = "<option value='"+data[i].code+"'   class='"+data[i].name+"' >"
									+ data[i].code
									+ "-"
									+ data[i].name
									+ "</option>";
							$(option2).appendTo(surgeryCode);
						}

					},
					'error' : function() {
						alert("出错了！");
					}
				});
	}
	
 	function queryCalimDateSpe(obj) {
		 
		var treatStartCursor = document.activeElement.id;
		//是否是详细的列表
		//var thisPage = $(obj).parent().parent().parent().attr("id");
		if(treatStartCursor==$(obj).attr("id")){
			return false;
		}
		
		//校验开始结束时间
		var spcStartTime=$(obj).parent().parent().parent().find("#spcStartTime").val() ;
		var spcEndTime=$(obj).parent().parent().parent().find("#spcEndTime").val() ;

		if(!checkDate(spcStartTime,spcEndTime)){
			$(obj).val("");
			return false;
		}

		var startTime = $(obj).val();
		if (startTime.length != 0) {
			var billType = $(obj).parent().parent().parent().find("input")
					.val();
			var td = $(obj).parent().parent().find("td");
			//获取事故日期
			var accidentTime = $("#accDate", $.pdialog.getCurrent()).val()
					.replace(/-/g, '/');
			var accidentTimes = accidentTime.split("/");
			var dt1 = new Date();
			dt1.setFullYear(accidentTimes[0]);
			dt1.setMonth(accidentTimes[1] - 1);
			dt1.setDate(accidentTimes[2]);
			//获取出险日期
			var claimTime = $("#claimDate", $.pdialog.getCurrent()).val()
					.replace(/-/g, '/');
			var claimTimes = claimTime.split("/");
			var dt2 = new Date();
			dt2.setFullYear(claimTimes[0]);
			dt2.setMonth(claimTimes[1] - 1);
			dt2.setDate(claimTimes[2]);
			//获取开始日期
			var startTime = $(obj).val().replace(/-/g, '/');
			var startTimes = startTime.split("/");
			var dt3 = new Date();
			dt3.setFullYear(startTimes[0]);
			dt3.setMonth(startTimes[1] - 1);
			dt3.setDate(startTimes[2]);
			//距离意外事故发生的天数
			if (accidentTime != "") {
				var dif = dt3.getTime() - dt1.getTime();
				var days = Math.round(dif / (24 * 60 * 60 * 1000));
				if (days >= 0 && startTime != null) {
					if (billType == 1) {
						$(td).eq(9).find("input").attr("value", days);
					}
				} else {
					$(obj).val("");
					alertMsg.error("开始日期必填且不小于事故日期！");
					return;
				}
			}
			//距离医疗理赔类型出险日发生的天数
			if (claimTime != "") {
				var dif2 = dt3.getTime() - dt2.getTime();
				var times = Math.round(dif2 / (24 * 60 * 60 * 1000));
				if (times >= 0 && startTime != null) {
					if (billType == 1) {
						$(td).eq(10).find("input").attr("value", times);
					}
				} else {
					$(obj).val("");
					alertMsg.error("开始日期必填且不小于出险日期！");
					return;
				}
			}
		}
	}

	//单选按钮

	function radiockeacdSpe(obj) {

		//定位数据 
		var innerPage = $(obj).parent().parent().parent().parent()
				.parent();

		var pageFlag = $(innerPage).parent().attr("titles");
		var billType = $(innerPage).parent().attr("billType");

		var specialId = $(obj).parent().find("#specialId").val();
		
		 
		var caseId = $("#caseId", navTab.getCurrentPanel()).val();
		 

	  	var url = 'clm/register/queryClaimBillItemMsg_CLM_tClaimDutyRegisterAction.action?pageFlag='
				+ pageFlag 
				+ '&caseId='
				+ caseId 
			 	+ '&claimSpecialVO.specialId=' + specialId
			 	+'&claimSpecialVO.caseId=' + caseId;
		$(innerPage).next().next().loadUrl(url);

		$(innerPage).next().next().show("30000"); 

		return;
	}
	

	function  deleteValueSpe(obj){
	 
		
		var specialId = $(obj).parent().find("#specialId").val();
		 
		if(specialId!=""&&specialId!="0"){  
		$.ajax({ 'type':'post',
				'url':'clm/register/deleteClaimDutyInfo_CLM_tClaimDutyRegisterAction.action'+ '?claimSpecialVO.specialId=' + specialId+'&claimTypecode[0]=4',
				'datatype':'json',
				'success':function(data){
					 if(data){ 
						 
						 $(obj).parent().parent().remove();
						 alertMsg.info("删除成功");
					 }else{
						 alertMsg.info("删除失败");
					 }
				    
				},
	 			'error':function(){
	 				alert("出错了！");
	 			}
		 });
		}
		 
	}
	
	//用于伸缩按钮的伸缩功能
	// $(document).ready(function(){
		  
	   $(".main_heading", $.pdialog.getCurrent()).off().toggle(function(){  
	     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'}, "slow");
			  $(this).find("b").removeClass("maim_lpask").addClass("maim_lpbubble");
		
	   },function(){
	     $(this).next(".main_lptwo").animate({height: 'toggle', opacity: 'toggle'});
	    	 $(this).find("b").removeClass("maim_lpbubble").addClass("maim_lpask");
	   });

	// });

	//多级标签的处理
	$(function(){    
		$("li>h5","[id^=main_]").bind("click",function(){
//	 		var id=$(this).attr("id").split("main_")[1];
		    var li=$(this).parent();
			var hasborder=$(this).attr("hasborder");
			
			if(li.hasClass("fold")){
				if (hasborder=="true"){
					$(this).parent().parent().parent().addClass("main_borderbg");
				}
				li.removeClass("fold");
				$(this).find("#two").removeClass("main_plus").addClass("main_minus");
				$(this).find("#three").removeClass("main_pluss").addClass("main_minuss");
				
				li.find(".main_foldContent").slideDown();
				
			}else{
				li.addClass("fold");
				if (hasborder=="true"){
					$(this).parent().parent().parent().removeClass("main_borderbg");
				}
				$(this).find("#two").removeClass("main_minus").addClass("main_plus");
				$(this).find("#three").removeClass("main_minuss").addClass("main_pluss");
				li.find(".main_foldContent").slideUp();
				
			}
		});
		
	})	
	
	
</script>
		<div class="main_box">
								<div class="main_heading"><h1><img src="clm/images/tubiao.png">特种费用录入
									<b class="maim_lpask"></b></h1></div>
									<div class="main_lptwo">
									<div id="main_1" class="main_borderbg">
				                                    <ul class="main_ul">
				                                       <li class="clearfix">
									<div class="main_foldContent">
									<div class="main_bqtabdivbr" style="margin-left: 10px;">
									<div class="main_bqtitle">
				                         <h1><img src="clm/images/three.png">特种费用信息录入</h1>
				                     </div>
				                     	<div class="panelPageFormContent">
												<div id="speCostNewsInt" class="panelPageFormContent" titles="registerSpeCost" billType="4">
													<div class="tabdivclassbr" >
														<table class="list nowrap itemDetail" addButton="添  加"
															hiddenAdd="hiddenAdd" style="border: 2px solid #CCC;width: 100%">
															<thead>
																<tr>
																	<th nowrap>选择</th>
																	<th nowrap>序号</th>
											
																	<th nowrap backValueFlag="specialType" ><font class="point"
																		color="red">* </font>特种费用类型</th>
											
																	<th nowrap backValueFlag="specialCode" ><font class="point"
																		color="red">* </font>特种费用代码</th>
											
																	<th nowrap backValueFlag="sumAmount" >金额</th>
											
																	<th nowrap backValueFlag="serviceOrgName" ><font class="point"
																		color="red">* </font>服务机构名称</th>
											
																	<th nowrap backValueFlag="feeStart"  dateFlag="dateFlag" ><font class="point" color="red">* </font>开始日期</th>
											
																	<th nowrap backValueFlag="feeEnd" dateFlag="dateFlag" ><font class="point" color="red">* </font>结束日期</th>
																	<th nowrap>操作</th>
																</tr>
															</thead>
															<tbody class="list" id="SpecialTBody">
																<s:iterator value="claimSpecialVOlist" status="st">
																	<tr>
																		<td><input type='radio' name="radio" id='radio${st.index}'
																			value='0' onclick="radiockeacdSpe(this)" class="MedRadio" />
																			<input id="specialId" type="hidden" value="${specialId }">
																			</td>
											
																		<td><input class="digits textInput" type="text" size="5"
																			value="${st.index+1}" readonly="readonly" /></td>
																		<td><Field:codeValue   	tableName="APP___CLM__DBUSER.T_CLAIM_SPECIAL_TYPE"  value="${specialType}" /></td>
																		<td><Field:codeValue  tableName="APP___CLM__DBUSER.T_CLAIM_SPECIAL_CODE"
																				 value="${specialCode}" />
																		</td>
																		<td> ${sumAmount} </td>
																		<td> ${serviceOrgName} </td>
																		<td> <s:date name='feeStart' format='yyyy-MM-dd' /> </td>
																		<td> <s:date name='feeEnd' format='yyyy-MM-dd' /> </td>
																		<td><a class="btnDel" href="javascript:void(0)" onclick="deleteValueSpe(this)"></a><input id="specialId" type="hidden" value="${specialId }"></td>
																	</tr>
																</s:iterator>
															</tbody>
														</table>
													</div>
												    <s:include value="addBarForDuty.jsp" />
												</div>
											</div>
				                     
				                   </div>
				                </div>
				             </li>
				          </ul>
				       </div>
				    </div>
				 </div>
