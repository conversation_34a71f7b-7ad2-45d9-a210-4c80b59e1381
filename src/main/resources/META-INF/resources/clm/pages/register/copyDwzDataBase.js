
$.extend($.fn,{
	itemDetail : function() {
					return this.each(function() {
								var $table = $(this).css("clear", "both"), 
								$tbody = $table.find("tbody");
								var fields = [];
								//遍历table中第一行中的所有th属性,得到其属性并将其放入到fields中
								$table.find("tr:first th[type]").each(
									function(i) {
										var $th = $(this);
										var field = {
											type : $th.attr("type")|| "text",
											patternDate : $th.attr("dateFmt")|| "yyyy-MM-dd",
											name : $th.attr("name")|| "",
											defaultVal : $th.attr("defaultVal")|| "",
											size : $th.attr("size")|| "12",
											enumUrl : $th.attr("enumUrl")|| "",
											lookupGroup : $th.attr("lookupGroup")|| "",
											lookupUrl : $th.attr("lookupUrl")|| "",
											lookupPk : $th.attr("lookupPk")|| "id",
											suggestUrl : $th.attr("suggestUrl"),
											suggestFields : $th.attr("suggestFields"),
											postField : $th.attr("postField")|| "",
											fieldClass : $th.attr("fieldClass")|| "",
											fieldAttrs : $th.attr("fieldAttrs")|| "",
											hiddenAttr : $th.attr("hiddenAttr")|| "",
											myOption: $th.attr("myOption") ||"",
											styleTH:$th.attr("style")||""
										};
										fields.push(field);
								});
								//监听 a.btnDel 的点击事件
								$tbody.find("a.btnDel").click(
									function() {
										var $btnDel = $(this);
										//若$btnDel的href属性是以javascript开头
										if ($btnDel.is("[href^=javascript:]")) {
											//删除该行
											$btnDel.parents("tr:first").remove();
											//重新初始化tbody，对其中index进行处理
											initSuffix($tbody);
											return false;
										}
										//调用ajax处理
										function delDbData() {
											$.ajax({
												type : 'POST',
												dataType : "json",
												url : $btnDel.attr('href'),
												cache : false,
												success : function() {
													//删除该行
													$btnDel.parents("tr:first").remove();
													//重新初始化下标
													initSuffix($tbody);
												},
												error : DWZ.ajaxError
											});
										}
										//若href属性不是javascript开头的，并且a标签的title属性不为空
										if ($btnDel.attr("title")) {
											//弹出提示框
											alertMsg.confirm($btnDel.attr("title"),
											{
												okCall : delDbData
											});
										} else {
											//否则直接执行delDbData()方法
											delDbData();
										}

										return false;
									});
								//取得addButton的值，若为设置，默认将其设置为Add New
								var addButTxt = $table.attr('addButton')
										|| "Add New";
								if (addButTxt) {
									//创建新增一行按钮 将其放到table的前面
									var $addBut = $(
											'<div class="button"><div class="buttonContent"><button type="button">'
													+ addButTxt + '</button></div></div>').insertBefore($table).find("button");
									//创建新增行数的输入框 将其放到table的前面
									var $rowNum = $(
											'<input type="text" name="dwz_rowNum" class="textInput" style="margin:2px;" value="1" size="2"/>')
											.insertBefore($table);

									var trTm = "";
									$addBut.click(function() {
										if (!trTm)
											trTm = trHtml(fields);
										var rowNum = 1;
										try {
											rowNum = parseInt($rowNum.val())
										} catch (e) {
										}

										for (var i = 0; i < rowNum; i++) {
											var $tr = $(trTm);
											$tr.appendTo($tbody).initUI().find("a.btnDel").click(
												function() {
													$(this).parents("tr:first").remove();
													initSuffix($tbody);
													return false;
												});
										}
										initSuffix($tbody);
									});
									//触发事件  
									
									if($table.attr('autoAdd')=="autoAdd"){
									      $addBut.parent().parent().css("display","none");
									      $rowNum.css("display","none");
									     /* //初始化绑定事件
									      bindEnter();*/
									      if($table.find("tbody tr").length==0){
									    	  $addBut.click();
									      }
									}
									//隐藏添加按钮
									if($table.attr('hiddenAdd')=="hiddenAdd"){
										$addBut.parent().parent().css("display","none");
									    $rowNum.css("display","none");
										 
									}
									 ///回車鍵自動增加一行
									if($table.attr('enterAdd')=="enterAdd"){
										$table.prev().prev().find("button").die().bind("click",function(){
									    	$table.find("input,select,a").each(function(i,obj){
										            $(this).die().live("keyup",function(){
										           	if(event.keyCode=="13"){
										           		//判断费用明细为手术类  手术代码必填 start
										           		var medFeeItem=$(obj).parent().parent().find("#medFeeItem").val();
										           		var operationCodeId=$(obj).parent().parent().find("#operationNameId").val();
										           		var drugDetailCodeId=$(obj).parent().parent().find("#drugDetailCodeId").val();
										           		
										           		var feeFlag = false;
										           		var feaFlag = false;   
														if(medFeeItem=='CC007'&&operationCodeId==""){
															feeFlag = true;
														}
														if(medFeeItem=='CC013'&&operationCodeId==""){
															feeFlag = true;
														}
														if(medFeeItem=='CC014'&&operationCodeId==""){
															feeFlag = true;
														}
														if(medFeeItem=='CC015'&&operationCodeId==""){
															feeFlag = true;
														}
														if(medFeeItem=='CC016'&&operationCodeId==""){
															feeFlag = true;
														}        
														if(medFeeItem=='CC017'&&operationCodeId==""){
															feeFlag = true;
														}        
														if(medFeeItem=='CC018'&&operationCodeId==""){
															feeFlag = true;
														} 
														if (feeFlag) {
															alertMsg.error("费用明细为手术类，手术代码必填！");
															return false;
														} 
										           		 
										           		//end
										           		event.keyCode=null;
										           		$addBut.click();
										           		 $table.find("tr:last td:first", navTab.getCurrentPanel()).find("input").focus();
										           		 return false;
										            	}
										            }); 
											  });
										}); 
										  
									    $table.find("input,select,a").each(function(i,obj){
								            $(this).die().live("keyup",function(){
								           	if(event.keyCode=="13"){
								           		
								           		
								           	//判断费用明细为手术类  手术代码必填 start
								           		var medFeeItem=$(obj).parent().parent().find("#medFeeItem").val();
								           		var operationCodeId=$(obj).parent().parent().find("#operationNameId").val();
								           		var drugDetailCodeId=$(obj).parent().parent().find("#drugDetailCodeId").val(); 
								           		var feeFlag = false;
								           		var feaFlag = false  
												if(medFeeItem=='CC007'&&operationCodeId==""){
													feeFlag = true;
												}
												if(medFeeItem=='CC013'&&operationCodeId==""){
													feeFlag = true;
												}
												if(medFeeItem=='CC014'&&operationCodeId==""){
													feeFlag = true;
												}
												if(medFeeItem=='CC015'&&operationCodeId==""){
													feeFlag = true;
												}
												if(medFeeItem=='CC016'&&operationCodeId==""){
													feeFlag = true;
												}        
												if(medFeeItem=='CC017'&&operationCodeId==""){
													feeFlag = true;
												}        
												if(medFeeItem=='CC018'&&operationCodeId==""){
													feeFlag = true;
												} 
												if (feeFlag) {
													alertMsg.error("费用明细为手术类，手术代码必填！");
													return false;
												} 
 
								           		event.keyCode=null;
								           		$addBut.click();   
								           		 $table.find("tr:last td:first", navTab.getCurrentPanel()).find("input").focus();
								           		return false;	
								             	}
								            }); 
									     });
									    
									    
										 
									}
									
									 
									
								}
							});

					/**
					 * 删除时重新初始化下标
					 */
					function initSuffix($tbody) {
						//遍历tbody中所有的tr元素
						$tbody.find('>tr').each(function(i) {
							//找到tbody中所有input,textarea,select,button,a.btnLook,a.btnAttach 元素
							$(':input, a.btnLook, a.btnAttach,',this).each(function() {
								var $this = $(this), 
								id = $this.attr("id"),name = $this.attr('name'), val = $this.val();
								//H 替换id中的#index#
								if(id && id.indexOf("#index#") >= 0){
									$this.attr("id",id.replaceSuffix(i));
								}
								if (name) {
									$this.attr('name',name.replaceSuffix(i));
								}
								var lookupGroup = $this.attr('lookupGroup');
								if (lookupGroup) {
									$this.attr('lookupGroup',lookupGroup.replaceSuffix(i));
								}
								var suffix = $this.attr("suffix");
								if (suffix) { 
									$this.attr('suffix',suffix.replaceSuffix(i));
								}
								if (val&& val.indexOf("#index#") >= 0)
									$this.val(val.replace('#index#',i + 1));
								});
						});
						
					}
					//H 创建td的方法
					function tdHtml(field) {
						var html = '', suffix = '';
						//H 若field.name是以[#index#]结尾 后缀设置为[#index#] 
						if (field.name.endsWith("[#index#]")){
							suffix = "[#index#]";
						}
						//H 若field.name是以[]结尾 后缀设置为[] 
						else if (field.name.endsWith("[]")){
							suffix = "[]";
						}
						//H 后缀标志
						var suffixFrag = suffix ? ' suffix="' + suffix + '" ': '';
						//H 属性标志 用来定义特殊的属性
						var attrFrag = '';
						//H 若th设置了fieldAttrs 属性
						if (field.fieldAttrs) {
							//H 字符串转换成对象
							var attrs = DWZ.jsonEval(field.fieldAttrs);
							//H 将attrs中的属性值以 key=value 的形式拼接
							for ( var key in attrs) {
								attrFrag += key + '="' + attrs[key] + '"';
							}
						}
						//H 保存field.type 
						var expand_type = field.type;
						//H 判断是否是自定义的拓展组件
						if (field.type.indexOf("expand") != -1) {
							field.type = "expand";
						}
						switch (field.type) {
							//H 增加删除按钮
							case 'del':
								html = '<a href="javascript:void(0)" class="btnDel '
										+ field.fieldClass + '">删除</a>';
								break;
							//H 查找带回
							case 'lookup':
								var suggestFrag = '';
								if (field.suggestFields) {
									suggestFrag = 'autocomplete="off" lookupGroup="'
											+ field.lookupGroup
											+ '"'
											+ suffixFrag
											+ ' suggestUrl="'
											+ field.suggestUrl
											+ '" suggestFields="'
											+ field.suggestFields
											+ '"'
											+ ' postField="'
											+ field.postField
											+ '"';
								}
	
								html = '<input type="hidden" name="'
										+ field.lookupGroup + '.' + field.lookupPk
										+ suffix + ' "/>'
										+ '<input type="text" name="' + field.name
										+ '"' + suggestFrag + ' lookupPk="'
										+ field.lookupPk + '" size="' + field.size
										+ '" class="' + field.fieldClass + '"/>'
										+ '<a class="btnLook" href="'
										+ field.lookupUrl + '" lookupGroup="'
										+ field.lookupGroup + '" ' + suggestFrag
										+ ' lookupPk="' + field.lookupPk
										+ '" title="查找带回" myOption="' + field.myOption + '">查找带回</a>';
								break;
							//H 附件
							case 'attach':
								html = '<input type="hidden" name="'
										+ field.lookupGroup
										+ '.'
										+ field.lookupPk
										+ suffix
										+ '"/>'
										+ '<input type="text" name="'
										+ field.name
										+ '" size="'
										+ field.size
										+ '" readonly="readonly" class="'
										+ field.fieldClass
										+ '"/>'
										+ '<a class="btnAttach" href="'
										+ field.lookupUrl
										+ '" lookupGroup="'
										+ field.lookupGroup
										+ '" '
										+ suggestFrag
										+ ' lookupPk="'
										+ field.lookupPk
										+ '" width="560" height="300" title="查找带回">查找带回</a>';
								break;
							//H 枚举
							case 'enum':
								$.ajax({
									type : "POST",
									dataType : "html",
									async : false,
									url : field.enumUrl,
									data : {
										inputName : field.name
									},
									success : function(response) {
										html = response;
									}
								});
								break;
							//H 日期
							case 'date':
								html = '<input type="text" name="' + field.name + '" value="' + field.defaultVal 
										+ '" class="date ' + field.fieldClass + '" dateFmt="' + field.patternDate
										+ '" size="' + field.size + '"' + attrFrag + '/>'
										+ '<a class="inputDateButton" href="javascript:void(0)">选择</a>';
								break;
							//H 新增支持拓展组件
							case 'expand':
								html = '<input type="' + expand_type + '" name="'
										+ field.name + '" value="'
										+ field.defaultVal + '" size="'
										+ field.size + '" class="'
										+ field.fieldClass + '" ' + attrFrag + '/>';
								break;
							//H 查找带回并增加隐藏属性
							case 'lookup_hidden':
								var suggestFrag = '';
								if (field.suggestFields) {
									suggestFrag = 'autocomplete="off" lookupGroup="'
											+ field.lookupGroup + '"' + suffixFrag
											+ ' suggestUrl="' + field.suggestUrl
											+ '" suggestFields="' + field.suggestFields
											+ '"' + ' postField="' + field.postField + '"';
								}
								html = '<input type="hidden" name="'
									+ field.hiddenAttr
									+ '" id="hdn_'
									+ field.hiddenAttr
									+ '"/>';
								html += '<input type="text" name="' + field.name
										+ '"' + suggestFrag + ' lookupPk="'
										+ field.lookupPk + '" size="' + field.size
										+ '" class="' + field.fieldClass + '"/>'
										+ '<a class="btnLook" href="'
										+ field.lookupUrl + '" lookupGroup="'
										+ field.lookupGroup + '" ' + suggestFrag
										+ ' lookupPk="' + field.lookupPk
										+ '" title="查找带回" myOption="' + field.myOption + '">查找带回</a>';
								break;
							//H 默认值为text
							default:
								html = '<input type="text" name="' + field.name
										+ '" value="' + field.defaultVal
										+ '" size="' + field.size + '" class="'
										+ field.fieldClass + '" ' + attrFrag + '/>';
								break;
							}
						
						    var myStyle=field.styleTH;
						//H 将生成input放入td中
						return '<td style="'+myStyle+'">' + html + '</td>';
					}
					function trHtml(fields) {
						var html = '';
						$(fields).each(function() {
							//H 拼接所有的td
							html += tdHtml(this);
						});
						//H 将生成的tds 放入 tr.unitBox 中
						return '<tr class="unitBox">' + html + '</tr>';
					}
				
			}
});
