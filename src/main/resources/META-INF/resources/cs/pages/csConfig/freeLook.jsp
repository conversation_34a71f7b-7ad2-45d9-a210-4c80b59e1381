<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*" %>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<script  type = "text/javascript">
$(document).ready(function(){
	_cs_initMultipleBox();
});
</script>
<div class="pageContent" layoutH="36px">
	<form action="cs/csConfig/queryFreeLookPeriodCfg_PA_freeLookPeriodCfgAction.action" id="freeLookPeriodCfgForm" onsubmit="return divSearch(this,'queryFreeLookResultDiv')">
		<div class="panel">
		<h1>犹豫期天数配置</h1>
			<div class="pageFormContent" id="">
					<div class="multipleBox" style="background-color: white;">
						<div class="codeTable">
							<Field:codeTable cssClass="combox" name="freeLookPeriodCfgVO.businessProdId" tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value=""/>
						</div>
						<div class="lMultipleBox" style="float:left">
							<div class="lTop">险种代码/名称</div>
							<select  size="10" multiple="multiple" optionType="codeAndValue" >
							</select>
						</div>
						<div class="multipleButton " >
							<div class="button lToR" >
								<div class="buttonContent">
									<button type="button" >
											增加》
									</button>
								</div>
							</div>
							<div class="button rToL" >
								<div class="buttonContent">
									<button type="button" >
												《删除
									</button>
								</div>
							</div>	
						</div>
						<div class="rMultipleBox">
							<div class="rTop"></div>
							<select size="10" multiple="multiple">
							</select>
						</div>
					</div>
			</div>
			
			<!-- button area layoutH="20px" -->
			
			<div class="pageFormContent">
				<dl style="width: 32%">
					<dt>机构</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true" name="freeLookPeriodCfgVO.organCode" tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="" id="organCode"/>
<%-- 						<Field:codeTable cssClass="combox" name="" tableName="T_UDMP_ORG" value="" id="organCode"/> --%>
					</dd>
				</dl>
				<dl style="width: 32%;">
					<dt>销售渠道</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true" name="freeLookPeriodCfgVO.channelType" tableName="APP___PAS__DBUSER.T_SALES_CHANNEL" value=""/>
<%-- 						<Field:codeTable cssClass="combox" name="" tableName="T_SALES_CHANNEL" value=""/> --%>
					</dd>
				</dl>
				<dl>
					<dt>银代新政</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true" name="freeLookPeriodCfgVO.bankNewdealFlag" tableName="APP___PAS__DBUSER.T_YES_NO" value=""/>
<%-- 						<Field:codeTable cssClass="combox" name="" tableName="T_YES_NO" value=""/> --%>
					</dd>
				</dl>
				
				<dl style="width:32%">
					<dt>新契约回访是否成功</dt>
					<dd>
						<Field:codeTable cssClass="combox"  nullOption="true"  name="freeLookPeriodCfgVO.visitFlag" tableName="APP___PAS__DBUSER.T_YES_NO" value=""/>
<%-- 						<Field:codeTable cssClass="combox"  name="" tableName="T_YES_NO" value=""/>  --%>
					</dd>
				</dl>
				
				<dl  style="width: 32%;">
					<dt>投保人性别</dt>
					<dd>
<%-- 						<Field:codeTable cssClass="combox" name="freeLookPeriodCfgVO.policyHolderGender" tableName="T_GENDER" value=""/> --%>
						<Field:codeTable cssClass="combox"  nullOption="true" name="" tableName="APP___PAS__DBUSER.T_GENDER" value=""/>
					</dd>
				</dl>
				<dl  style="width: 32%;">
					<dt>投保人年龄</dt>
					<dd>
						<input type="text" name="freeLookPeriodCfgVO.policyHolderAge"/>
					</dd>
				</dl>
				<dl style="width:32%">
					<dt>被保人年龄</dt>
					<dd>
						<input type="text" name="freeLookPeriodCfgVO.insuredAge"/>
					</dd>
				</dl>
				
				<dl style="width:32%">
					<dt>被保人性别</dt>
					<dd>
<%-- 						<Field:codeTable cssClass="combox" name="freeLookPeriodCfgVO.insuredGender" tableName="T_GENDER" value=""/> --%>
						<Field:codeTable cssClass="combox"  nullOption="true" name="" tableName="APP___PAS__DBUSER.T_GENDER" value=""/>
					</dd>
				</dl>
				
				<dl style="width:32%">
					<dt>是否包含非工作日</dt>
					<dd>
						<Field:codeTable cssClass="combox"  nullOption="true"  name="freeLookPeriodCfgVO.includeNonworkdaysFlag" tableName="APP___PAS__DBUSER.T_YES_NO" value=""/>
<%-- 						<Field:codeTable cssClass="combox" name="" tableName="T_YES_NO" value=""/> --%>
					</dd>
				</dl>
				
				<dl style="width:32%">
					<dt>犹豫期天数</dt>
					<dd>
						<input type="text" name="freeLookPeriodCfgVO.freeLookPerid" maxlength="3"/>
<!-- 						<input type="text" name=""/> -->
					</dd>
				</dl>
				
				<dl style="width:32%">
					<dt>生效日期</dt>
					<dd>
						<input type="expandDateYMD" name="freeLookPeriodCfgVO.cfgValidateTime"/>
<!-- 						<input type="expandDateYMD" name="freeLookPeriodCfgVO.cfgValidateTime"/> -->
					</dd>
				</dl>
				
				<dl style="width:32%">
					<dt></dt>
					<dd>
<!-- 						<input type="expandDateYMD"/> -->
					</dd>
				</dl>
				
				<div class="buttonActive" style="margin-right: 17px">
					<div class="buttonContent">
<!-- 					<a><button onclick="query()">查询</button></a> -->
						<button type="submit">查询</button>
					</div>
				</div>
				<div class="buttonActive" style="margin-left: 17px">
					<div class="buttonContent">
						<button type="button" onclick="addFreeLookCfg()">新增</button>
					</div>
				</div>
			</div>
			
			<!-- button area -->
			<div class="pageFormContent">
				<s:include value="freeLookPeriod_queryResult.jsp"></s:include>
				<h1>查询结果</h1>
				<%-- 分页效果 --%>
				<div class="panelBar">
					<div class="pages">
						<span>每页</span>
						<s:select list="#{5:'5',10:'10',20:'20',50:'50'}" name="select"
							onchange="navTabPageBreak({numPerPage:this.value})"
							value="currentPage.pageSize">
						</s:select>
						<span>条，共${currentPage.total }条</span>
					</div>
					<div class="pagination" targetType="navTab" 
						totalCount="${currentPage.total }" 
						numPerPage="${currentPage.pageSize }" pageNumShown="10"
						currentPage="${currentPage.pageNo }"></div>
				</div>
				<div class="panelBar">
					<div class="buttonContent">
						<button type="button">取消</button>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>