<!-- 保全配置——应备资料配置 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="pageFormContent">
	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png" >历史查询结果
		</h1>
	</div>
		<div class="pageFormContent">
			<form id="pagerForm" onsubmit="return divSearch(this, 'queryAppDocCfgLogDiv');" action="${ctx}/cs/csConfig/queryAppDocCfgLogInfo_PA_csAppDocAction.action" method="post">
				<input type="hidden" name="pageNum" value="${pageNum}" />
				<input type="hidden" name="numPerPage" value="${currentPageLog.pageSize}" />
			</form>
			<form id="messageForm" action="" onsubmit="return divSearch(this, 'queryAppDocCfgLogDiv');" method="post">
				<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid">
					<table class="list" id="appDocLogtable" style="width:100%" table_saveStatus="1" >
						<thead>
							<tr>
								<th colName="docTypeId" inputType="select">应备资料类型</th>
								<th colName="docOwner" inputType="select">资料所有人</th>
								<th colName="originalFlag" inputType="select">原件/复印件</th>
								<th colName="docProperty" inputType="input">应备资料属性</th>
								<th colName="docRemark" inputType="input">备注</th>
								<th colName="operaterId" inputType="input">操作员代码</th>
								<th colName="operaterOrganCode" inputType="input">操作机构</th>
								<th colName="insertTime" inputType="input">操作日期</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="currentPageLog.pageItems" status="st" id="qr">
							<tr tr_saveStatus="1" align="center">
								<td>
								<Field:codeTable disabled="true" name="docTypeId" tableName="APP___PAS__DBUSER.T_CS_APP_DOC_TYPE" value="${docTypeId}" />
								</td>
								<td><Field:codeTable disabled="true" cssClass="combox" name="docOwner" tableName="APP___PAS__DBUSER.T_PARTY_TYPE" value="${docOwner}" /></td>
								<td><select name="originalFlag" disabled="disabled" >
									<option <s:if test="originalFlag==0">selected</s:if> value="0">原件</option>
									<option <s:if test="originalFlag==1">selected</s:if> value="1">复印件</option>
								</select></td>
								<td><input name="docProperty" value="${docProperty}" maxlength="3" disabled="disabled"/></td>
								<td><input name="docRemark" value="${docRemark}" maxlength="200"disabled="disabled"/></td>
								<td ><input name="operaterId" value="${operaterId}" disabled="disabled"/></td>
								<td ><input name="operaterOrganCode" value="${operaterOrganCode}" disabled="disabled"/></td>
								<td ><input name="insertTime" value="<s:date name="insertTime" format="yyyy-MM-dd" />" class="date" readonly="readonly" disabled="disabled"/></td>
							</tr>
							</s:iterator>
						</tbody>
					</table>
					<div class="panelBar">
						<div class="pages">
							<span>显示</span>
							<s:select cssClass="combox" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
								name="currentPageLog.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'queryAppDocCfgLogDiv')">
							</s:select>
							<span>条，共${currentPageLog.total}条</span>
						</div>
						<div class="pagination" rel="queryAppDocCfgLogDiv" totalCount="${currentPageLog.total}"currentPage="${currentPageLog.pageNo}"
					     numPerPage="${currentPageLog.pageSize}" pageNumShown="10"></div>
					</div>
				</div>
			</form>
		</div>
	</div>
<script type="text/javascript">
	/* $(document).ready(function(){
		$('select').attr.('disable',true);
		$('input').attr.('disable',true);
	});	
 */
</script>