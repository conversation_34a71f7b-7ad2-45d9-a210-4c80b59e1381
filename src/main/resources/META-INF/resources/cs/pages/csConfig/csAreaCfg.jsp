<!-- 保全-保全任务池分配及任务分配-区域配置 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="pageContent" layoutH="1px">
	<div id="areaCfgPanel">
		<div class="pageFormContent">
			<div id="areaPage">
				<s:include value="csAreaPage.jsp"></s:include>
			</div>
			<div id="orgPage">
				<s:include value="csAreaOrgPage.jsp"></s:include>
			</div>
			<div id="userPage">
				<s:include value="csAreaPersonPage.jsp"></s:include>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	
	
	/**
	 * 选定某条区域信息,更新区域机构、区域人员;flag:判断是新增数据还是原有数据加载
	 */
	function indexLoadArea(obj, flag) {
		//获取区域Id
		//var areaId = $(obj).parents("tr").children().eq(-2).text();
		var areaId = $("input[type='radio']:checked",navTab.getCurrentPanel()).parent().parent().find("#areaId").val();
		//var areaId = $("#areaId").val();
		//获取区域名称
		var areaOrganName =$("input[type='radio']:checked",navTab.getCurrentPanel()).parent().parent().find("#areaName").val();
		if (flag == 'iterator') {
			$.ajax({
				type : "post",
				dataType : "text",
				url : "${ctx}/cs/csConfig/loadInfoByArea_PA_areaCfgAction.action",
				data : "csAreaCfgVO.areaId=" + areaId,
				success : function(data) {
					  $("#personPanel").remove();
					$("#personPanel").html(data).initUI();
					$("#organPanel").html(data).initUI();
					$("#areaId_org").attr("value", areaId);
					$("#areaId_person").attr("value", areaId); 
					var $areaOrganName = $("#areaOrganName");
					$areaOrganName.text("区域机构" + "-" + areaOrganName);
					var $areaPerson = $("#areaPerson");
					$areaPerson.text("区域人员" + "-" + areaOrganName); 
				}
			});
		} else if (flag == 'add') {
			alertMsg.info("该条区域信息为新增信息，请先保存！");
			return false;
		}
		$(obj).parents("tr").attr("tr_saveStatus", "1");
		$(obj).parents("tr").siblings("tr").attr("tr_saveStatus", "0");
	}
	//表格新增行
	function appendElementS(group) {
		var parent = navTab.getCurrentPanel();
		var element;
		var appendTo;
		var sizset;
		var size = $(parent).find("[addTableGroup='" + group + "']").size();
		element = $("[addTableGroup=" + group + "][addRole ='element']");
		appendTo = $("[addTableGroup=" + group + "][addRole ='appendTo']");
		if (size != 0) {
			sizset = $(element).attr("sizset");
			$element = $(element).clone();
			$element.attr('sizset', sizset - 0 + 1).appendTo($(appendTo));
			$(element).removeAttr("addTableGroup").removeAttr("addRole")
					.removeClass().show();
			$(element).parents("table").parent().initUI();
			//将tr_saveStatus table的行状态位置为“修改（1）”，用来判断哪一行数据需要保存。
			if (typeof ($(element).attr("tr_saveStatus")) != "undefined") {
				$(element).attr("tr_saveStatus", "1");//列表信息状态位'保存':'0','修改':'1'
			}
			//			列表信息状态位'以保存':'0','修改':'1'
			$(element).find("input[name='_status']").val(1);
		}
		var _table = $(appendTo, parent).parents("table");
		if (typeof ($(_table).attr("table_saveStatus")) != "undefined") {
			$(_table).attr("table_saveStatus", "1");//列表信息状态位'保存':'0','修改':'1'
		}
	}
	
	 
</script>
