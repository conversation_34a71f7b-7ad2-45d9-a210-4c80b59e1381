<!-- 保全配置——应备资料配置-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="pageContent" layoutH="10px">
	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png" >查询条件
		</h1>
	</div>
			<div class="pageFormContent">
			<form id="queryAppDocCfg" onsubmit="return divSearch(this, 'queryAppDocCfgDiv');" action="${ctx}/cs/csConfig/queryAppDocCfgInfo_PA_csAppDocAction.action" method="post" class="required-validate">
				<div class="pageFormContent">
					<dl>
						<dt>保全项目代码/名称</dt>
						<dd>
						   <!--  <input  size="1" name="code" id="code" value="" readonly="readonly"/> -->
							<Field:codeTable cssClass="combox"  nullOption="true" name="caAppDocCfgVO.serviceCode" 
							tableName="APP___PAS__DBUSER.T_SERVICE" id="SERVICE" value="" onChange="showCode(this)"/>
						</dd>
					</dl>	
					<dl  >
						<dt>机构</dt>
						<dd>
							<Field:codeTable cssClass="combox" nullOption="true" name="caAppDocCfgVO.organCode" 
							tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL"  id= "organCode" value=""/>
						</dd> 
					</dl>
					<dl  >
						<dt>是否已有投保书影像</dt>
						<dd>
							<Field:codeTable cssClass="combox"  nullOption="true" name="caAppDocCfgVO.proposalImageFlag" tableName="APP___PAS__DBUSER.T_YES_NO" id="ImageFlag" defaultValue="1"/>
						</dd>
					</dl>	
					<dl  >
						<dt>是否犹豫期内</dt>
						<dd>
							<Field:codeTable cssClass="combox" nullOption="true" name="caAppDocCfgVO.freeFlag" tableName="APP___PAS__DBUSER.T_YES_NO" id="freeFlag" defaultValue="1"/>
						</dd> 
					</dl>
			
					<dl  >
						<dt>是否属于非常规业务<dt>
						<dd>
							<Field:codeTable cssClass="combox"  nullOption="true" name="caAppDocCfgVO.unconvenFlag" tableName="APP___PAS__DBUSER.T_YES_NO" id="unconvenFlag" defaultValue="1"/>
						</dd>
					</dl>	
					
					<dl  >
						<dt>是否属于预受理业务<dt>
						<dd>
							<Field:codeTable cssClass="combox"  nullOption="true" name="caAppDocCfgVO.preRegFlag" tableName="APP___PAS__DBUSER.T_YES_NO" id="preRegFlag" defaultValue="1"/>
						</dd>
					</dl>	
					<dl  >
						<dt>业务员星级</dt>
						<dd>
							<Field:codeTable cssClass="combox"  nullOption="true" name="caAppDocCfgVO.agentStarFlag" tableName="APP___PAS__DBUSER.T_AGENT_TYPE" id="agentStarFlag" value=""/>
						</dd>
					</dl>	
					<dl  >
						<dt>付费形式</dt>
						<dd>
							<Field:codeTable cssClass="combox" nullOption="true" name="caAppDocCfgVO.payMode" tableName="APP___PAS__DBUSER.T_PAY_MODE" id="payMode" value=""/>
						</dd> 
					</dl>
					<dl>
						<dt>保单已满年度</dt>
							<dd>
							<input type="expandNumber" class="textInput" name="caAppDocCfgVO.policyYear" value=""  id="policyYear"/></dd>
						</dl>
					<dl >
						<dt>付费金额上限</dt>
						<dd><input type="expandNumber" class="textInput" name="caAppDocCfgVO.maxPayamount"   maxlength="16" id="maxPayamount"/></dd>
					</dl>
					<dl >
						<dt>付费金额下限</dt>
						<dd ><input type="expandNumber" class="textInput" name="caAppDocCfgVO.minPayamount"   maxlength="16" id="minPayamount"/></dd>
					</dl>
					<!-- <dl >
					<dt>付费金额上限</dt>
						<dd><input type="text" min="500" max="10000" name="caAppDocCfgVO.maxPayamount" value="" class="number" maxlength="10" id="maxPayamount"/></dd>
					</dl>
						<dl >
					<dt>付费金额下限</dt>
					<dd><input type="text" min="10" max="500" name="caAppDocCfgVO.minPayamount" value="" class="number" maxlength="10" id="minPayamount"/></dd> -->
					<dl>
						<dt>生效日期</dt>
						<dd>
							<input name="caAppDocCfgVO.validDate" style="width:120px" value="<s:date name="caAppDocCfgVO.validDate" format="yyyy-MM-dd" id="validDate"/>" class="date" readonly="readonly"/>
							<a class="inputDateButton" href="javascript:;">选择</a>
						</dd>
					</dl>
				</div>	
			</form>
			</div>
				<table style="width:100%">
					<tr>
						<td></td>
						<td style="width:200px">
									<button class="but_blue" onclick="queryBusiProdCfg('queryAppDocCfg','queryAppDocCfgDiv');">查询</button>
									<button class="but_gray 	close" type="button">退出</button>
						</td>
						<td></td>
					</tr>
				</table>
		<!-- 查询结果 -->
		
		<div id="queryAppDocCfgDiv"  class="unitBox">
		</div>
		
<!-- 	</div> -->
<!-- </div> -->
</div>

<script type="text/javascript">
$(document).ready(function(){
	_cs_initMultipleBox();
	$("#queryAppDocCfgDiv").hide();
});
function queryBusiProdCfg(formId,boxId){
	var $form = $("#"+formId,navTab.getCurrentPanel());
	if($("#SERVICE").val()==''&&$("#organCode").val()=='' &&$("#ImageFlag").val()=='' &&$("#freeFlag").val()=='' &&$("#unconvenFlag").val()=='' &&$("#agentStarFlag").val()==''
			&&$("#payMode").val()=='' &&$("#policyYear").val()=='' &&$("#maxPayamount").val()=='' &&$("#minPayamount").val()=='' ){
		alertMsg.info("请输入查询条件！");	
		return false;
	}
	var $obj = $("#queryAppDocCfg", navTab.getCurrentPanel());
	//验证
	if (!$obj.valid()) {
		alertMsg.error("提交数据不完整，请改正后再提交!");
		return false;
	}
	$("#"+boxId,navTab.getCurrentPanel()).show();
	$form.submit();
};
function showCode(obj){
	$("#code").val(obj.value);
}
</script>
