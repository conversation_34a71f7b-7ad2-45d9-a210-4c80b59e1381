<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">	
<form id="pagerForm" method="post" style="display:none;"
	action="${ctx }/cs/csQuality_qpm/queryDownLoadPage_PA_qualityPlanManageAction.action"
	onsubmit="return navTabSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<form action="${ctx }/cs/csQuality_qpm/queryDownLoadPage_PA_qualityPlanManageAction.action" id="queryDownLoadPage"
		onsubmit="return navTabSearch(this);" method="post" rel="pagerForm" class="pageForm required-validate">
		<input type="hidden" value="${qualityPlanVO.qualityPlanId }" name="qualityPlanVO.qualityPlanId" id="qualityPlanId">
		<input type="hidden" value="${total1 }" name="total1">
		<input type="hidden" value="${total2 }" name="total2"> 
</form>	
<div class="pageContent" layoutH="36px">	
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">计划基本信息
		</h1>
	 </div>
	<div class="pageFormInfoContent">
				<dl>
					<dt>计划名称：</dt>
					<dd>${qualityPlanVO.planName}</dd>    
				</dl>
				<dl>
					<dt>质检类型：</dt>
					<dd><Field:codeValue tableName="APP___PAS__DBUSER.T_CS_QUALITY_TYPE" value="${qualityPlanVO.qualityType}"/></dd>    
				</dl>
				<dl>
					<dt>质检截止日期：</dt>
					<dd><s:date name="qualityPlanVO.qualityEndDate" format="yyyy-MM-dd"/></dd>    
				</dl>
				<dl>
					<dt>数据来源：</dt>
					<dd><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_DATA_SOURCE" value="${qualityPlanVO.qualityDataSource}"/></dd>    
				</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">质检范围明细
		</h1>
    </div>
	<div class="pageContent"  style="width:100%;">
		<div class="panelPageFormContent">
				<dl>
					<dt>导入数据总量：</dt>
					<dd>${total1}</dd>    
				</dl>
				<dl>
					<dt>质检范围数据总量：</dt>
					<dd>${total2}</dd>    
				</dl>
	  </div>
	  <div class="tabdivclass">
			<table class="list" style="width: 100%">
				<thead>
					<tr>
						<th>保全受理号</th>
						<th>申请方式</th>
						<th>保全项目</th>
						<th>保单号</th>
						<th>保单管理机构</th>
						<th>受理机构</th>
						<th>受理人员</th>
						<th>复核人员</th>
						<th>保单服务人员</th>
						<th>当前处理用户</th>
						<th>申请提交日期</th>
						<th>补退费金额</th>
						<th>移动电话核验结果</th>
						<th>质检状态</th>
					</tr>
				</thead>
				<tbody id="planTBody">
					<s:iterator value="currentPage.pageItems" id="" status="st">
						<tr>
							<td>${acceptCode}</td>
							<td>${serviceType }</td>
							<td>${serviceCode }</td>
							<td>${policyCode }</td>
							<td>${policyOrganCode }</td>
							<td>${organCode }</td>
							<td>${acceptByName }</td>
							<td>${reviewName }</td>
							<td>${agentCodeName }</td>
							<td>${qualityByName }</td>
							<td><s:date name="applyTime" format="yyyy-MM-dd"/></td>
							<td>${feeAmount }</td>
							<td>${phoneCheckResult }</td>
							<td>${qualityStatusName }</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			 <div class="panelBar" >
		        <div class="pages">
		            <span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
							onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
						</s:select> <span>条，共${currentPage.total}条</span>        
				</div>
		        <div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
		        	pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
			</div>
			</div>
	</div>
	
    <div id ="qualityPlanDownLoadExportDetailDiv">
    <s:include value="/cs/pages/quality/qualityPlanDownLoadExportDetail.jsp"></s:include>
    </div>
	<div class="pageContent"  style="width:100%;">
			<div class="pageFormdiv">
			    <button onclick="navTab.closeCurrentTab();" type="button" class="but_blue">关闭</button>
			</div>
	</div>
	</div>
<script type="text/javascript">
</script>