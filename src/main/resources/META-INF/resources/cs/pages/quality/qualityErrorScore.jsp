<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<form id="saveRevieweErrCfg" onsubmit="return divSearch(this, 'queryShareTaskDiv');" method="post">
	 <input type="hidden" id="cfgId" name="reviewErrCfgVO.cfgId"  /> 
	 <input type="hidden" id="errType" name="reviewErrCfgVO.errType"  /> 
	 <input type="hidden" id="errorReasonName" name="reviewErrCfgVO.errorReasonName"/> 
	 <input type="hidden" id="errDetail" name="reviewErrCfgVO.errDetail"/> 
	 <input type="hidden" id="acceptRate" name="reviewErrCfgVO.accpectRate"/> 
	 <input type="hidden" id="inputRate" name="reviewErrCfgVO.inputRate"/> 
	 <input type="hidden" id="reviewRate" name="reviewErrCfgVO.reviewRate"/> 
	 <input type="hidden" id="cfgValidateTime" name="reviewErrCfgVO.cfgValidateTime"/> 
	 <input type="hidden" id="organ" name="reviewErrCfgVO.organ"/> 
	 <input type="hidden" id="effectDate" name="effectDate" value = '${effectDate }'/> 
</form>
<div layoutH="36px" style="margin:5px;">
	<div>
	<div class="divfclass">
				<h1>
					<img src="images/tubiao.png" >差错评分配置列表
				</h1>
	</div>
			<div class="pageFormContent">
			 	<form id="messageForm" onsubmit="return navTabSearch(this)"  action="${ctx }/cs/csQuality/initErrorScore_PA_csQualityErrorScoreAction.action?" method="post"> 
					<input type="hidden" name="pageNum" value="${pageNum}" /> 
					<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> 
					<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
						<table class="list" id="appDoctable" style="width:100%" table_saveStatus="1" >
							<thead>
								<tr>
									<th>选择</th>
									<th>序号</th>
									<th>差错代码</th>
									<th>差错名称</th>
									<th>差错明细</th>
									<th>受理折标系数</th>
									<th>录入折标系数</th>
									<th>复核折标系数</th>
									<th>生效日期</th>
									<th>操作员</th>
									<th>操作机构</th>
									<th>操作日期</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody align="center">
								  <s:iterator value="currentPage.pageItems" status="st" id="qr">
									<tr>
										<td><input type="radio" onclick="changeText(this)" name="reviewErrCfgVO.cfgId" value='${cfgId}'/></td>
										<td><s:property value="#st.index+1" /></td>
										<td><s:property value="errType" /></td>
										<td><s:property value="errorReasonName" /></td>
										<td><s:property value="errDetail" /></td>
										<td><s:property value="acceptRate" /></td>
										<td><s:property value="inputRate" /></td>
										<td><s:property value="reviewRate" /></td>
										<td><s:date name="cfgValidateTime" format="yyyy-MM-dd"/></td>
										<td><s:property value="insertByName" /></td>
										<td><s:property value="organCode" /></td>
										<%-- <td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="organCode" /></td> --%>
										<td><s:date name="updateTime" format="yyyy-MM-dd"/></td>
										<td><a href="javascript:void(0)" class="BtnDel" onclick="delPriPointInfo(this,${cfgId})">删除</a></td>
									</tr>
								</s:iterator> 
							</tbody>
						</table>
					</div> 
					<div class="panelBar">
						<div class="pages">
							<span>显示</span>
							<s:select cssClass="combox" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
								name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'messageForm')">
							</s:select>
							<span>条，共${currentPage.total}条</span>
						</div>
						<div class="pagination" rel="getCsTaskDIV" totalCount="${currentPage.total}"currentPage="${currentPage.pageNo}"
					     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
					</div> 
		  		
						<table style="width:100%">
							<tr>
								<td></td>
								<td style="width:220px">
											<button  type="button" class="but_blue" onclick="saveReviewErrcfsInf()">保存</button>
											<button  type="button" class="but_blue" onclick="cannel()">取消</button>
											<button  type="button" class="but_blue" onclick="queryHistory(this)">历史查询</button>
								</td>
								<td></td>
							</tr>
						</table>
				</form> 
		  	</div>
	</div>
	<div class="pageFormContent" style="margin-top:10px;padding:0px;">
		<div id="showHistoryInfo"></div>
	</div>
</div>
<script type="text/javascript">

//保存
function saveReviewErrcfsInf(){
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	
	var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	var tdNodes = $(radio).parent().parent().children();
	//2代码 3差错名称  4明细  5受理-6录入
	//7复效  8-生效日期  9操作员 10-操作机构 11操作日期
	if(radio.length == 0){
		 alertMsg.info("请选择保存项");
		return false;
	}
	var cfgId = radio.val();
	var errType = tdNodes.eq(2).text();
	var errorReasonName = tdNodes.eq(3).text();
	var errDetail = tdNodes.eq(4).text();
	var acceptRate = tdNodes.eq(5).find("input[type='text']").val();
	var inputRate = tdNodes.eq(6).find("input[type='text']").val();
	var reviewRate = tdNodes.eq(7).find("input[type='text']").val();
	var cfgValidateTime = tdNodes.eq(8).find("input[type='text']").val();
	var effectdate = $('#effectDate').val();
	console.log("--代码="+errType+" --名称= "+errorReasonName+" --明细= "+errDetail
			+" --受理折标="+acceptRate+" --录入= "+inputRate+" --复核= "+reviewRate+" --生效= "+reviewRate
			+" --生效日期= " +cfgValidateTime);
	
	 $.ajax({
		 url : "${ctx }/cs/csQuality/updateErrorScoreByErr_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data : {
			"reviewErrCfgVO.cfgId" : cfgId,
			"reviewErrCfgVO.errType" : errType,
			"reviewErrCfgVO.errorReasonName" : errorReasonName,
			"reviewErrCfgVO.errDetail" : errDetail,
			"reviewErrCfgVO.acceptRate" : acceptRate,
			"reviewErrCfgVO.inputRate" : inputRate,
			"reviewErrCfgVO.reviewRate" : reviewRate,
			"reviewErrCfgVO.cfgValidateTime": cfgValidateTime
		 },
		 success : function(data){
			 alertMsg.correct("保存成功");
			 var json = DWZ.jsonEval(data);
			 if(json.statusCode!=null && json.statusCode == 300){
				 alertMsg.error(json.message);
				 return false;
			 }else{
					var $obj1 = $("#messageForm", navTab.getCurrentPanel());
					var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
						var tdNodes = $(radio).parent().parent().children();
							$(radio).attr("checked",false);
							var t5 = tdNodes.eq(5).find("input[type='text']").val();
							var t6 = tdNodes.eq(6).find("input[type='text']").val();
							var t7 = tdNodes.eq(7).find("input[type='text']").val();
							var t8 = tdNodes.eq(8).find("input[type='text']").val();
							var t11 = tdNodes.eq(11).find("input[type='text']").val();
							
							tdNodes.eq(5).text("");
							tdNodes.eq(6).text("");
							tdNodes.eq(7).text("");
							tdNodes.eq(8).text("");
							tdNodes.eq(11).text("");
							
							tdNodes.eq(5).append(t5); 
							tdNodes.eq(6).append(t6); 
							tdNodes.eq(7).append(t7); 
							tdNodes.eq(8).append(t8);
							tdNodes.eq(11).append(t11); 
			 }
			
		 }
	 });
	
	
}

/**
 * 历史查询 
 *
 */
function queryHistory(obj){
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	var url = "${ctx }/cs/csQuality/showHistoryInfo_PA_csQualityErrorScoreAction.action";
	$("#queryFlag").val("history");
	$obj1.attr("action",url);
	$obj1.attr("onsubmit","return divSearch(this, 'showHistoryInfo')");
	$obj1.submit();
	
}

/**
 * 删除
 */
function delPriPointInfo(obj, cfgId){
	
	 if (confirm("确定要删除改记录吗？")) {
		 
	 	 $.ajax({
			 url : "${ctx }/cs/csQuality/deleteScoreInfo_PA_csQualityErrorScoreAction.action",
			 type : "post",
			 dataType : "json",
			 data : {
				 "reviewErrCfgVO.cfgId" : cfgId
			 },
			 success : function(data){
				 if(data.statusCode == 300){
					 alertMsg.info("删除失败");
					 return false;
				 }else{
					alertMsg.correct("删除成功");
					$("#deleteInfo").val("ok"); 
					searchAction();
					/* var $obj1 = $("#messageForm", navTab.getCurrentPanel());
					var url = "${ctx }/cs/csQuality/initErrorScore_PA_csQualityErrorScoreAction.action?reviewErrCfgVO.cfgId="+cfgId;
					$obj1.attr("action", url);
					$obj1.attr("onsubmit","return divSearch(this, 'messageForm')");
					$obj1.submit(); */
				 }
			 }
		 });
	 } 
}

/**
 * 改变输入状态
 *
 */
function changeText(obj){
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	//5受理-6录入-7复效  8-生效 11操作
	$($obj1).find("input:radio",navTab.getCurrentPanel()).each(function(){
		
		var chek = $(this).attr("checked");
		if(chek == 'checked'){
			var valiteTime;
			var tdNodes = $(obj).parent().parent().children();
			var t5 = tdNodes.eq(5).text();
			var t6 = tdNodes.eq(6).text();
			var t7 = tdNodes.eq(7).text();
			var t8 = tdNodes.eq(8).text();
			var t11 = tdNodes.eq(11).text();
			/**
			* 1.生效日期与操作日期都存在，并且生效日期大于操作日期不处理
			* 2.操作日期不存在不处理
			* 3.操作日期存在时，如果不符合第一条，生效日期默认为操作日期后一天
			*/
			//valiteTime = dealTime(t8); //生效时间：操作时间加+1
			
			var effectDate = $('#effectDate').val();
			tdNodes.eq(5).text("");
			tdNodes.eq(6).text("");
			tdNodes.eq(7).text("");
			tdNodes.eq(8).text("");
			//tdNodes.eq(11).text("");
			tdNodes.eq(5).append("<input type='text' name='text' value="+t5+"></input>"); 
			tdNodes.eq(6).append("<input type='text' name='text' value="+t6+"></input>"); 
			tdNodes.eq(7).append("<input type='text' name='text' value="+t7+"></input>"); 
			tdNodes.eq(8).html("<input class='date' readonly='readonly' value='"+effectDate+"'/>").initUI();
			//tdNodes.eq(11).append("<input type='text' name='text' value="+t11+"></input>");
		}else{
			var tdNodes = $(this).parent().parent().children();
			var t5 = tdNodes.eq(5).html();
			if(t5=="undefined" || t5 == undefined){
				t5 = tdNodes.eq(5).outerHTML();
			}
			if(t5.indexOf("input") > 0){
				var t5 = tdNodes.eq(5).find("input[type='text']").val();
				var t6 = tdNodes.eq(6).find("input[type='text']").val();
				var t7 = tdNodes.eq(7).find("input[type='text']").val();
				var t8 = tdNodes.eq(8).find("input[class='date']").val();
				var t11 = tdNodes.eq(11).find("input[class='date']").val();
				
				tdNodes.eq(5).text("");
				tdNodes.eq(6).text("");
				tdNodes.eq(7).text("");
				tdNodes.eq(8).text("");
				tdNodes.eq(11).text("");
				
				tdNodes.eq(5).append(t5); 
				tdNodes.eq(6).append(t6); 
				tdNodes.eq(7).append(t7); 
				tdNodes.eq(8).append(t8);
				tdNodes.eq(11).append(t11); 
			}
			
		}
	});
}
/**
 * 处理时间 
 */
var ma = [['1','3','5','7','8','10'],['4','6','9','11']]; 

function dealTime(time){
	//$('date').value = o.join('-'); 
	if(time == ""){
		dd = new Date();
		time = dd.toLocaleString(); 
	}
	var o = time.replace(/\//g, '-');
	var t = o.split(" "); 
	var d = t[0].split('-'); 
	var l = isLeap(d[0]); 
	if((check(d[1],ma[0]) && (d[2] == '31')) || (check(d[1],ma[1]) && (d[2] == '30')) || 
	(d[1] == '2' && d[2] == '28' && !l) || (d[1] == '2' && d[2] == '29' && l)) { 
		return d[0] + '-' + (d[1] * 1 + 1) + '-' + 1; 
	} else if(d[1] == '12' && d[2] == '31') { 
		return (d[0] * 1 + 1) + '-' + '1-1'; 
	} else { 
		return d[0] + '-' + d[1] + '-' + (d[2] * 1 + 1); 
	}
	
	
}

//判断数组a是否存在在元素n 
function check(n,a) { 
	for(var i = 0,len = a.length;i < len;i++) { 
		if(a[i] == n) { 
			return true; 
		} 
	}
}
//转化日期函数 
function pasDate(da) { 
	var yp = da.indexOf('年'), 
	mp = da.indexOf('月'), 
	dp = da.indexOf('日'); 
	var y = da.substr(0,yp), 
	m = da.substr(yp + 1,mp - yp - 1), 
	d = da.substr(mp + 1,dp - mp - 1); 
	return [y,m,d]; 
} 	
//闰年 
function isLeap(y) { 
	return ((y % 4 == 0 && y % 100 != 0) || y % 400 == 0) ? true : false; 
} 	
/**
 * 取消 
 *
 */
function cannel(){	
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	//$($obj1).find("input:radio",navTab.getCurrentPanel()).each(function(){
		var tdNodes = $(radio).parent().parent().children();
		//var t5 = tdNodes.eq(5).html();
		//if(t5.indexOf("input") > 0){
			$(radio).attr("checked",false);
			var t5 = tdNodes.eq(5).find("input[type='text']").val();
			var t6 = tdNodes.eq(6).find("input[type='text']").val();
			var t7 = tdNodes.eq(7).find("input[type='text']").val();
			var t8 = tdNodes.eq(8).find("input[type='text']").val();
			var t11 = tdNodes.eq(11).find("input[type='text']").val();
			
			tdNodes.eq(5).text("");
			tdNodes.eq(6).text("");
			tdNodes.eq(7).text("");
			tdNodes.eq(8).text("");
			tdNodes.eq(11).text("");
			
			tdNodes.eq(5).append(t5); 
			tdNodes.eq(6).append(t6); 
			tdNodes.eq(7).append(t7); 
			tdNodes.eq(8).append(t8);
			tdNodes.eq(11).append(t11); 
		//}
	
	//});
}


function searchAction(){
	$("#messageForm", navTab.getCurrentPanel()).submit();
}

</script>