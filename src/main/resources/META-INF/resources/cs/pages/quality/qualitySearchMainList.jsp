<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">

<input type="hidden" id="qualityId" name="qualityInspectionInfoVO.qualityId" value="${qualityInspectionInfoVO.qualityId }"/>
<input type="hidden" id="policyCode1" value="${policyCode }"/>
<input type="hidden" id="applyCode1" value="${qualityInspectionInfoVO.applyCode }"/>
<input type="hidden" id="acceptCode1" value="${qualityInspectionInfoVO.acceptCode}"/>
<input type="hidden" id="serviceCode1" value="${qualityInspectionInfoVO.serviceCode }"/>
<input type="hidden" id="changeId1" value="${csAcceptChangeVO.changeId }"/>
<input type="hidden" id="acceptId1" value="${csAcceptChangeVO.acceptId }"/>
<input type="hidden" id="customerId1" value="${csApplicationVO.customerId }"/>
<input type="hidden" id="serviceCode1" value="${qualityInspectionInfoVO.serviceCode }"/>
	 <form id="messageForm" onsubmit="return divSearch(this, 'messageForm');" method="post"> 
		<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
		<div class="pageFormContent">
  		<div class="panel">
		<h1>质检计划统计</h1>
			<table class="list" id="appDoctable1" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>计划名称</th>
						<th>计划已抽档件数</th>
						<th>计划已执行件数</th>
						<th>计划版本号</th>
						<th>计划执行人</th>
					</tr>
				</thead>
				<tbody align="center">
					<%-- <s:iterator value="qualityInspectionInfoVO" status="st" id="qr1">
						<tr>
							<td><s:property value="acceptCode" /></td>
							<td><s:property value="serviceCode" /></td>
							<td><s:property value="feeAmount" /></td>
							<td><s:property value="delayDays" /></td>
							<td><s:property value="serviceType" /></td>
							<td><s:property value="acceptBy" /></td>
							<td><s:property value="insertOperatorId" /></td>
							<td><s:property value="reviewId" /></td>
							<td><s:date name="acceptTime" format="yyyy-MM-dd"/></td>
							<td><s:date name="validateTime" format="yyyy-MM-dd"/></td>
						</tr>
					</s:iterator> --%>
				</tbody>
			</table>
		</div> 
</div></div>
	
	<div class="pageFormContent">
  	<div class="panel">
  		<h1>抽检轨迹</h1>
  		<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
			<table class="list" id="appDoctable2" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>抽检日期</th>
						<th>抽检人员</th>
						<th>抽检结论</th>
						<th>系数值</th>
						<th>不合格原因</th>
						<th>抽检备注</th>
						<th>抽检修改日期</th>
						<th>抽检修改人员</th>
					</tr>
				</thead>
				<tbody align="center">
					<%-- <s:iterator value="qualityInspectionLogListVO" status="st" id="qr1">
						<tr>
							<td><s:date name="qualityDate" format="yyyy-MM-dd"/></td>
							<td><s:property value="qualityByName" /></td>
							<td><s:property value="qualityResult" /></td>
							<td>系数值-待确定</td>
							<td>不合格原因-待确定</td>
							<td><s:property value="qualityRemark" /></td>
							<td><s:property value="qualityModifyDate" /></td>
							<td><s:property value="qualityModifyBy" /></td>
						</tr>
					</s:iterator> --%>
				</tbody>
			</table>
		</div> 
  	</div>
  	</div>
  	
			
	<div class="button">
		<div class="buttonContent">
			<button  type="button" onclick="back()">返回</button>
		</div>
	</div>
</form> 
<script type="text/javascript">


$(function(){

})
/**
 * 复核查询 
 */
function reviewSearch(){
	//alert(" 复核查询");
	
	$.ajax({
		url : "${ctx}/cs/csTask/queryCsCheckTask_PA_csQualityErrorScoreAction.action",
		type : "post",
		dataType : 'json',
		data : {
			"csFocusCheckVO.acceptCode" : acceptCode,
			"csFocusCheckVO.applyCode" : applyCode,
			"numPerPage" : 10
		},
		success : function(result){
			if(result.statusCode == 200){
				var taskId = result.message;
				var title = "复核查询";	
				var fresh = eval("true");
				var external = eval("false"); 
				var tabid="复核查询";
				var url ="${ctx}/cs/csCheck/loadCsCheck_PA_csCheckAction.action?acceptId="+
					acceptId+"&acceptCode="+acceptCode+"&taskId="+taskId+"&checkType="+5;
				
				navTab.openTab(tabid, url, {
					title : title,
					fresh : fresh,
					external : external
				});	
			}else{
				 alertMsg.info("无核保信息");
			}
		}
	});
}




/**
 * 返回
 */
function back(){
	var url = "/cs/csQuality/loadqualityModifiy_PA_csQualityErrorScoreAction.action";
	var title = "事后质检处理";	
	var fresh = eval("true");
	var external = eval("false"); 
	var tabid="事后质检处理";
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	 
}



</script>