$(function(){
	//alert("111");
	
});

//保全快速查询 查询所有保全项的code和pinguying方放到一个数组里面 
function queryCsItem(obj){
	var codeAndPingYing= $("#codeAndPingYing",navTab.getCurrentPanel()).val();
	if(typeof(obj)=="undefined"){
		 obj =$("#itemName",navTab.getCurrentPanel()).val();
	}
	/**********************************************/
	var _value = obj;
	var itemID = null;
	var strPingYing = codeAndPingYing.split(",");
	for(var i =0;i<strPingYing.length;i++){
		//前2位是code从第三位开始截取得到pingying 的字符串
		var strService = strPingYing[i].substr(2);
		//_value是输入的内容 如果pingying里面包含屏幕输入的 取到pingying对应的code
		if(strService.toLowerCase().indexOf(_value.toLowerCase()) == 0){
			//把匹配到的code都筛选出来
			if(itemID == null){
				itemID = strPingYing[i].substr(0,2);
			}else{
				itemID = itemID + "," + strPingYing[i].substr(0,2);
			}
		}
	}
	/**********************************************/		
 if(_value!=null&&_value!=""){
  $("#lMultipleBox", navTab.getCurrentPanel()).find("option").remove();
 }
	if(typeof($navTab)=="undefined"){
		$navTab = navTab.getCurrentPanel();
	}
	var _multipleBoxs = $("#csServiceCode",$navTab);
	$(_multipleBoxs).each(function(){
		
		var _multipleBox = $(this);
		var _options = $(_multipleBox).find(".codeTable").find("option").clone();
		var _codeTable = $(_multipleBox).find(".codeTable");
		var _lMultipleBox_select = $(_multipleBox).find(".lMultipleBox select");
		if(typeof($(_lMultipleBox_select).attr("optionType"))!="undefined"){
			var _optionType = $(_lMultipleBox_select).attr("optionType");
			if(_optionType=="codeAndValue"){//option显示样式是（名称+代码）
				$(_options).each(function(){
/* 					AC(CC)	投保人资料变更	FB	密码重置	CD	客户层地址信息变更
					PL	保单挂失	FK	密码修改	BB	被保险人基本信息变更
					PR	保单迁移	LR	保单补发	MC	基本信息备注项变更
					MR 	主险续保	PC	续期缴费信息变更	FM	交费方式及期限变更
					GB	领取年龄变更	GM	领取方式变更	IO	被保险人职业类别变更
					RF	保单贷款清偿	XD	新增可选责任	EN	附加险满期不续保
					二期
					TA	转增养老金	DT	附加特约终止	ER	附加险满期降低保额续保
					AM	追加保费	CR	(RR)续保险种转换	CB	万能险基本保额减少
					JD	可选责任加保	LC	领取日期变更	DC	万能险基本保额约定变更
					TR	保费自垫清偿	AP	保费自垫申请终止	NS	新增附加险
					RE	保单复效	BC	受益人变更	AA	附加险加保
					RA	保单复缴	DA	新增附加特约	CA	万能险基本保额增加*/
					var _option_val = $(this).val();  
					var _option_txt = $(this).text();
					if(		'CC'==_option_val || 'PL'==_option_val ||'PR'==_option_val||'MR'==_option_val
							||'GB'==_option_val||'RF'==_option_val||'FK'==_option_val||'LR'==_option_val
							||'PC'==_option_val	||'GM'==_option_val	||'XD'==_option_val	||'CD'==_option_val	
							||'FM'==_option_val	||'MC'==_option_val	||'IO'==_option_val	||'EN'==_option_val
							
							||'TA'==_option_val	||'AM'==_option_val	||'PA'==_option_val	||'TR'==_option_val	
							||'RE'==_option_val	||'RA'==_option_val	||'DT'==_option_val	||'LC'==_option_val	
							||'AP'==_option_val	||'BC'==_option_val	||'DA'==_option_val	||'ER'==_option_val	
							||'CB'==_option_val	||'DC'==_option_val	||'NS'==_option_val	||'CA'==_option_val	
							||'RR'==_option_val	
					){
						$(this).text(_option_txt+"*("+_option_val+")");
					}else{
						$(this).text(_option_txt+"("+_option_val+")");
					}
					var _option_txt = $(this).text();
					if (_option_txt.toLowerCase().indexOf(_value.toLowerCase())!=-1||_value==null||_value=="" ||(itemID != null && itemID.toLowerCase().indexOf(_option_val.toLowerCase())!=-1)) {
						$(this).text(_option_txt);
						$(this).val(_option_val);
					document.getElementById("multiple").options.add(new Option(_option_txt,_option_val));
					}
				});
			}
		}
		$(_codeTable).hide();
		//多选下拉选的点击事件初始化
		_cs_multipleBoxClick(_multipleBox);
	});
	
}


//险种

function queryCsItemBusi(){
	
	
}


/*function showMenu() {
var cityObj = $("#" + inputId);
var $treeSearchDiv = $("#treeSearchDiv");
var treeSerrchDivPosition = $treeSearchDiv.position();
var value = $("#" + inputId).val() == null ? "" : $("#" + inputId)
		.val().trim();
var cityPosition = $("#" + inputId).position();
if(typeof($navTab)=="undefined"){
	$navTab = navTab.getCurrentPanel();
}
var _multipleBoxs = $("div .multipleBox",$navTab);
$(_multipleBoxs).each(function(){
	
	var _multipleBox = $(this);
	var _options = $(_multipleBox).find(".codeTable").find("option").clone();
	var _codeTable = $(_multipleBox).find(".codeTable");
	var _menuContent_select = $(_multipleBox).find(".menuContent select");
	
	
			$(_options).each(function(){
				var _option_val = $(this).val();
				var _option_txt = $(this).text();
				$(this).text(_option_val+"/"+_option_txt);
				var _option_txt = $(this).text();
				if (_option_txt.indexOf(value)!=-1||value==null||value=="") {
				document.getElementById("hiddmenuselect").options.add(new Option(_option_txt,_option_val));
				}
			});	 
			  $(_menuContent_select).remove("option");
			$(_menuContent_select).remove("option").append($(_lMultipleBox_select));  
	

	$(_codeTable).hide();
	//多选下拉选的点击事件初始化
	_cs_multipleBoxClick(_multipleBox);
});

$("#" + treeDivId).css(
		{
			left : cityPosition.left + treeSerrchDivPosition.left
					+ 5+ "px",
			top :  cityPosition.top + cityObj.outerHeight()+cityPosition.top+cityPosition.top
			+ treeSerrchDivPosition.top +2+ "px"
		}).slideDown("fast");
//	$("body").bind("mousedown", onBodyDown);
$("#hiddmenuselect").focus();
}

function hiddmenu(){		 
var bankName = $("#hiddmenuselect option:selected").text();  
$("#itemName").val(bankName);
$(".menuContent").css("display",'none');
queryCsItem(bankName);
}
function hiddenmenu(){
$(".menuContent").css("display",'none'); 
}
*/
