//删除
function delPriPointInfo(obj, projectPointId){
	alert("要删除要点的Id=："+projectPointId);
	
     if (obj != null) {
         currentStep = $(obj).parent().parent().find("td:first-child").html();
     }

     if (currentStep == 0) {
         alert('请选择一项!');
         return false;
     }

     if (confirm("确定要删除改记录吗？")) {
         $("#contentDetails tr").each(function () {
             var seq = parseInt($(this).children("td").html());
             if (seq == currentStep) { 
            	 $(this).remove(); 
            }
             if (seq > currentStep) {
	           	 $(this).children("td").each(function (i) {
	           		 if (i == 0) {
	           			 $(this).html(seq - 1); 
	           		 }
	           	}); 
	          }
         });
     }
     if(projectPointId == 0){
   		return false;
   	}
	
	$.ajax({
		url : "${ctx }/cs/csQuality/deleteQualityPoint_PA_csQualityErrorControlAction.action",
		type : post,
		data : {
			"qualityPrjPointVO.projectPointId" : projectPointId
		},
		error : function(){
			alertMsg.info("出错了");
		},
		success : function(response){
			var json = DWZ.jsonEval(response);
			alertMsg.info(json);
		}
	});
}

//新增要点
var currentStep = 0;
var max_line_num = 0;
//publicEntryForm','getCsTaskDIV
function addProPointInfo(){
	var projNo = $("#projNo").val();
	var projName = $("#projName").val();
	var projAccountScore = $("#projAccountScore").val();
	var pointDesc = $("#pointDesc").val();
	if(pointDesc == "" || pointDesc == null){
		alertMsg.info("要点描述为空");
		return false;
	}
  	//添加新记录
    max_line_num = $("#contentDetails tr:last-child").children("td").html();
    if (max_line_num == null) {
        max_line_num = 1;
    }else{
        max_line_num = parseInt(max_line_num);
        max_line_num += 1;
    }
  	
    $('#contentDetails').append(
	    "<tr id='line" + max_line_num + "'>" +
	        "<td class='td_Num'>" + max_line_num + "</td>" +
	        "<td class='td_Item'>"+projNo+"</td>" +
	        "<td class='td_Item'>"+projName+"</td>" +
	        "<td class='td_Item'>"+pointDesc+"</td>" +
	        "<td class='td_Item'>"+projAccountScore+"</td>" +
	        "<td class='td_Item'>" /*+
	        	"<Field:codeTable cssClass='combox' nullOption='true' name='qualityProjectVO.validFlag' tableName='APP___PAS__DBUSER.T_YES_NO'/>" +
	     */  
	        +"</td>" +
	        "<td class='td_Oper'><a href='#' class='BtnDel' onclick='delPriPointInfo(this,0)'>删除</a>"+
	        "</td>" +
	    "</tr>"
	  );
}

//保存数据
function saveQualityRojectInfo() {
	
	
	alert("保存");
	
    var data = "";
    $('#contentDetails tr').each(function () {
        var stepName = $(this).find("td:eq(1)").find("input").val();
        var stepDescription = $(this).find("td:eq(2)").find("input").val();
        data +=  stepName ;
        data +=  stepDescription ;
    });
    alert(data);
}