<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">	
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">导出明细下载
		</h1>
    </div>
	<div class="pageContent" style="width:100%;">
			<div class="pageFormdiv" style="padding-right: 85%;">
			    <button onclick="queryDetailList()" type="button" class="but_blue">刷新</button>
			    <button onclick="createDetailList()" type="button" class="but_blue">生成最新数据明细</button>
			    <br><br>
			</div>
	  <div class="tabdivclass">
			<table class="list" style="width: 30%"  id="qualityDownloadInfoTable">
				<a target="ajax" id ="queryDetailList"
				href="${ctx }/cs/csQuality_qpm/queryDetailList_PA_qualityPlanManageAction.action"
				rel="qualityPlanDownLoadExportDetailDiv" style="display: none"></a>
				<thead>
					<tr>
						<th>数据下载申请时间</th>
						<th>状态</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody >
					<s:iterator value="qualityDownloadInfoVOList" status="st">
						<tr>
							<td><s:date name="applyTime" format="yyyy-MM-dd HH:mm:ss"/></td>
							<td>
							 <s:if test="taskStatus==1">
                              <a title="" href="cs/csQuality_qpm/exportDetailList_PA_qualityPlanManageAction.action?qualityDownloadInfoVO.listId=${listId}"
							  target="dwzExport"  style="text-decoration: underline;"><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_DOWNL_STATUS" value="${taskStatus }"/></a>
                            </s:if>
                            <s:else>
                            <Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_DOWNL_STATUS" value="${taskStatus }"/>
                            </s:else>
							</td>
							<td><a href="#" class="btnDel"  onclick="delFileList(${listId})">删除</a></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			</div>
	</div>
<script type="text/javascript">
function delFileList(listId){
	var qualityPlanId = $("#qualityPlanId",navTab.getCurrentPanel()).val();
	$("#queryDetailList",navTab.getCurrentPanel()).attr("href","${ctx }/cs/csQuality_qpm/delDetailList_PA_qualityPlanManageAction.action?qualityDownloadInfoVO.listId=" + listId+"&qualityDownloadInfoVO.qualityPlanId=" + qualityPlanId);
	$("#queryDetailList",navTab.getCurrentPanel()).click();
	alertMsg.correct("删除成功！");
}

function queryDetailList(){
	var qualityPlanId = $("#qualityPlanId",navTab.getCurrentPanel()).val();
	$("#queryDetailList",navTab.getCurrentPanel()).attr("href","${ctx }/cs/csQuality_qpm/queryDetailList_PA_qualityPlanManageAction.action?qualityDownloadInfoVO.qualityPlanId=" + qualityPlanId);
	$("#queryDetailList",navTab.getCurrentPanel()).click();
}

	function createDetailList(){
		var qualityPlanId = $("#qualityPlanId",navTab.getCurrentPanel()).val();
		$.ajax({
			type : 'POST',
			url : "${ctx }/cs/csQuality_qpm/checkDetailList_PA_qualityPlanManageAction.action",
			data : "qualityDownloadInfoVO.qualityPlanId=" + qualityPlanId,
			cache : false,
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == 300) {
					alertMsg.error(json.message);
				}else{
					$("#queryDetailList",navTab.getCurrentPanel()).attr("href","${ctx }/cs/csQuality_qpm/createDetailList_PA_qualityPlanManageAction.action?qualityDownloadInfoVO.qualityPlanId=" + qualityPlanId);
					$("#queryDetailList",navTab.getCurrentPanel()).click();
					alertMsg.correct("生成成功！");
				}
			},
			error : function() {
				alertMsg.err("失败");
			}
	});
	}
</script>