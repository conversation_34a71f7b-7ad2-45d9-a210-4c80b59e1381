<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">	
<div class="pageContent" layoutH="36px">
<form id="pagerForm" method="post" style="display:none;"
	action="${ctx }/cs/csQuality_qpm/queryUser_PA_qualityPlanManageAction.action"
	onsubmit="return dialogSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<form action="${ctx }/cs/csQuality_qpm/queryUser_PA_qualityPlanManageAction.action" id="queryUser"
		onsubmit="return dialogSearch(this);" method="post" rel="pagerForm" class="pageForm required-validate">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">查询条件
		</h1>
	 </div>
	<div class="pageFormInfoContent">
	<input type="hidden" value="${userBackId}" id="userBackIdHidden" name="userBackId">
				<dl>
					<dt>管理机构</dt>
					<dd >
						<input id="userOrganCode" name="userVo.organCodeLike"
							value="${userVo.organCodeLike}" type="text" size="3"
							class="organ" clickId="userMenuBtn" showOrgName="userOrganNameId" style="width:30px;border-right:0"/>
					    <input id="userOrganNameId"  type="text" size="13" class="public_textInput" style="width:110px"
							readOnly value="<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${userVo.organCodeLike}"/>"/> <a id="userMenuBtn" class="btnLook" href="#"></a>
					</dd>
				</dl>
				<dl>
					<dt>用户代码</dt>
					<dd><input type="text" name="userVo.userNameLike" value="${userVo.userNameLike}"></dd>    
				</dl>
				<dl>
					<dt>用户名称</dt>
					<dd><input type="text" name="userVo.realNameLike" value="${userVo.realNameLike}"></dd>    
				</dl>
			  <div class="pageFormdiv"> 
				<button  type="submit"  class="but_blue">查询</button>
			  </div> 
	</div>
</form>


	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">查询结果
		</h1>
    </div>
	<div class="pageContent" style="width:100%;">
	  <div class="tabdivclass" >
			<table class="list" style="width:100%;" targetType="dialog">
				<thead>
					<tr>
						<th>选择</th>
						<th>用户代码</th>
						<th>用户名称</th>
						<th>权限名称</th>
					</tr>
				</thead>
				<tbody id="">
				<s:iterator value="currentPage.pageItems"  status="st">
						<tr>
							<td><input type="radio" name="userId"  value='${userId}'/></td>
							<td>${userName }</td>
							<td>${realName }</td>
							<td>${userRoleName}</td>
						</tr>
				</s:iterator>		
				</tbody>
			</table>
			</div>
			<div class="panelBar" >
		        <div class="pages">
		            <span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
							onchange="dialogPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
						</s:select> <span>条，共${currentPage.total}条</span>        
				</div>
		        <div class="pagination"  targetType="dialog"  totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
		        	pageNumShown="10"  currentPage="${currentPage.pageNo}"></div>
			</div>
			<div class="pageFormdiv">
			    <button onclick="confirmPage()" type="button" class="but_blue">确定</button>
			    <button onclick="closePage()" type="button" class="but_blue">取消</button>
			</div>
	</div>


</div>
<script type="text/javascript">
$(document).ready(function(){
	$("#userBackIdHidden").click();
	var organNameId = $("#userOrganNameId").val();
	var specificChar = "-";
	var position = organNameId.indexOf(specificChar);
	var result = organNameId.substring(position + 1);
	$("#userOrganNameId").val(result);
})

function closePage(){
	 $.pdialog.closeCurrent();   
}

function confirmPage(){
	var userId = $('input[type=radio][name=userId]:checked').val();
	if(userId==null || userId==""){
		alertMsg.info("请选择用户。");
		return false;
	}
	var userName = $('input[type=radio][name=userId]:checked').parent().next().text();
	var userBackId = $("#userBackIdHidden").val();
 	$("#"+userBackId).val(userName);
	 $.pdialog.closeCurrent();   
}


</script>