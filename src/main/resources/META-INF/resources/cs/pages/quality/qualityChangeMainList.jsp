<!-- 质检个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">共享池查询结果
	</h1>
 </div>
<div class="pageContent">
<form id="pagerForm" name="ChangeComm" action="${ctx }/cs/csQuality/queryQualityModifyPageList_PA_csQualityErrorScoreAction.action"
		 onsubmit="return divSearch(this, 'showQuqlityChangeList')"  method="post"> 
	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
	 <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> 
	 <input type="hidden" id="taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="1" /> <!-- 共享池 -->
	 <input type="hidden" id="taskType" name="qualityInspectionInfoVO.taskType" value="2" /> <!-- 整改任务 -->
	 <input type="hidden" id="organCode" name="qualityInspectionInfoVO.organCode"  value="${qualityInspectionInfoVO.organCode }" /> 
	 <input type="hidden" id="policyOrganCode" name="qualityInspectionInfoVO.policyOrganCode"  value="${qualityInspectionInfoVO.policyOrganCode }" /> 
	 <input type="hidden" id="applyCode" name="qualityInspectionInfoVO.applyCode"  value="${qualityInspectionInfoVO.applyCode }" /> 
	 <input type="hidden" id="policyCode" name="qualityInspectionInfoVO.policyCode"  value="${qualityInspectionInfoVO.policyCode }"/> 
	 <input type="hidden" id="serviceCode" name="qualityInspectionInfoVO.serviceCode"  value="${qualityInspectionInfoVO.serviceCode }"/> 
	 <input type="hidden" id="qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName"  value="${qualityInspectionInfoVO.qualityPlanName }"/> 
	 <input type="hidden" id="qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode"  value="${qualityInspectionInfoVO.qualityPlanCode }"/> 
	</form>
	 	<div class="tabdivclass"> 
	 		<input type="hidden" id="currentUserId" name="currentUserId" value="${currentUserId }" /> 
			<table class="list" id="appDoctable" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>选择</th>
						<th>保全受理机构</th>
						<th>保全受理号</th>
						<th>保单号</th>
						<th>保单管理机构</th>
						<th>保全项目</th>
						<th>申请方式</th>
						<th>补退费金额</th>
						<th>申请提交日期</th>
						<th>受理人员</th>
						<th>复核日期</th>
						<th>复核人</th>
						<th>质检人</th>
						<th>进入任务池时间</th>
					</tr>
				</thead>
				<tbody align="center" id="tbodyChangeComm">
					  <s:iterator value="currentPage.pageItems" status="st" id="qr">
						<tr>
							<td><input type="checkbox" name="qualityIdCheckbox" id="qualityIdCheckbox" value='${qualityId}'/></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${organCode}"/></td>
							<td><s:property value="acceptCode" /></td>
							<td id="policyCode"><s:property value="policyCode" /></td>
							<td id="policyOrganCode"><s:property value="policyOrganCode" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE"  value="${serviceType }" /></td>
							<td>${feeAmount }</td>
							<td><s:date name="applyTime" format="yyyy-MM-dd"/></td>
							<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${acceptUser.userName}(${acceptUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${acceptUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${acceptUser.phone},办公电话:${acceptUser.telPhone},邮箱:${acceptUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${acceptUser.userId}" />
								</dt></a></td>
							<td><s:date name="reviewTime" format="yyyy-MM-dd"/></td>
							<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${reviewUser.userName}(${reviewUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${reviewUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${reviewUser.phone},办公电话:${reviewUser.telPhone},邮箱:${reviewUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${reviewUser.userId}" />
								</dt></a></td>
							<td><s:property value="qualityByName" /></td>
							<td><s:date name="qualityDate" format="yyyy-MM-dd HH:mm:ss"/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			  <div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select cssClass="" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'showQuqlityChangeList')">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" rel="showQuqlityChangeList" totalCount="${currentPage.total}"currentPage="${currentPage.pageNo}"
			     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
			 </div> 
		</div> 
	  	 <div class="pageFormdiv">
			<button  type="button" class="but_blue" onclick="applyChangeTask()">申请任务</button>
		</div>
</div>
<script type="text/javascript">
$(function(){
	$("#tbodyChangeComm", navTab.getCurrentPanel()).find("tr").each(function(){			
		var reg = new RegExp(",","g");//替换
		var _policyCode=$(this).find("td#policyCode").text();
		_policyCode=_policyCode.replace(reg,"<br/>");
		$(this).find("td").eq(3).html(_policyCode);		
		var reg2 = new RegExp("、","g");//替换
		var policyOrganCode=$(this).find("td#policyOrganCode").text();
		policyOrganCode=policyOrganCode.replace(reg2,"<br/>");
		$(this).find("td#policyOrganCode").html(policyOrganCode);
	});	
});
function applyChangeTask(){
	var qualityinput = $("#tbodyChangeComm").find("input[name='qualityIdCheckbox']:checked");
	var currentUserId = $("#currentUserId").val();
	var qualityIdStr = "";
	qualityinput.each(function (){
		qualityIdStr += $(this).val()+",";
	});
	qualityIdStr = qualityIdStr.substring(0, qualityIdStr.length-1)
	if(qualityIdStr == null || qualityIdStr == ''){
		alertMsg.error("请选择申请任务！");
		return;
	}
	
	$.ajax({
        type:'POST',
        url:'${ctx}/cs/csAccept/applyChangeCommTask_PA_csQualityErrorScoreAction.action',
        data : {
        	 "qualityIdStr" : qualityIdStr,
        	 "currentUserId": currentUserId
        },
        dataType:"json",
        async : false,
		cache : false,
        success:function(data){
        	if(data.statusCode == 300){
				alertMsg.error(data.message);
				return false;
			}else{
				alertMsg.info("处理成功");
				var $obj = $("#pagerForm[name='ChangeComm']", navTab.getCurrentPanel());
	        	$obj.submit();
	        	var $obj2 = $("#pagerForm[name='ChangePerson']", navTab.getCurrentPanel());
	        	$obj2.submit();
			}
        }
    });
}


</script>