<!-- 质检个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">个人任务池查询结果
	</h1>
 </div>
<div class="tabdivclass" > 
	<form id="pagerForm" name="person" onsubmit="return divSearch(this, 'showQuqlityModifyPersonList');" method="post" 
		 action="${ctx }/cs/csQuality/queryQualityInspectionPageList_PA_csQualityErrorScoreAction.action" >
	 	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
		 <input type="hidden" name="numPerPage" value="${numPerPage}" /> 
		 <input type="hidden" id="p_acceptCode" name="qualityInspectionInfoVO.acceptCode" value="${qualityInspectionInfoVO.acceptCode }" /> 
		 <input type="hidden" id="p_policyCode" name="qualityInspectionInfoVO.policyCode" value="${qualityInspectionInfoVO.policyCode }" /> 
		 <input type="hidden" id="p_organCode" name="qualityInspectionInfoVO.policyOrganCode" value="${qualityInspectionInfoVO.policyOrganCode }" /> 
		 <input type="hidden" id="p_qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode" value="${qualityInspectionInfoVO.qualityPlanCode }" /> 
		 <input type="hidden" id="p_qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName" value="${qualityInspectionInfoVO.qualityPlanName }" /> 
		 <input type="hidden" id="p_makerName" name="qualityInspectionInfoVO.makerName" value="${qualityInspectionInfoVO.makerName }" /> 
		 <input type="hidden" id="p_serviceCode" name="qualityInspectionInfoVO.serviceCode" value="${qualityInspectionInfoVO.serviceCode }" /> 
		 <input type="hidden" id="p_modifyFlag" name="qualityInspectionInfoVO.modifyFlag" value="${qualityInspectionInfoVO.modifyFlag }" /> 
		 <input type="hidden" id="p_taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="0" /> 
	</form>
		<table class="list" id="personList" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>					
					<th>选择</th>
					<th>质检机构</th>
					<th>保全受理号</th>
					<th>保单号</th>
					<th>保单管理机构</th>
					<th>保全项目</th>
					<th>申请方式</th>
					<th>补退费金额</th>
					<th>申请提交日期</th>
					<th>受理人员</th>
					<th>复核日期</th>
					<th>复核人员</th>
					<th>复核用户片区</th>
					<th>质检计划代码</th>
					<th>质检计划名称</th>
					<th>创建人</th>
					<th>截止日期</th>
					<th style="display: none">录入人id</th>
					<th style="display: none">复核人id</th>
				</tr>
			</thead>
			<tbody align="center" id="tbodyPerson">
				  <s:iterator value="currentPage.pageItems" status="st" id="qr6">
				  <input type="hidden" value="" />
					<tr>
						<td><input type="radio" name="qualityInspectionInfoVO.qualityId" id="qualityId" value='${qualityId}'/></td>
						<td><s:property value="qualityByOrg" /></td>
						<td><s:if  test="modifyMode!=null"><font color="green"></s:if><s:property value="acceptCode" /></td>
						<td id="policyCode"><s:property value="policyCode" /></td>
						<%-- <td id="organCode"><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${organCode}"/></td> --%>
						<td id="policyOrganCode"><s:property value="policyOrganCode" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE"  value="${serviceType }" /></td>
						<td>${feeAmount }</td>
						<td><s:date name="applyTime" format="yyyy-MM-dd"/></td>
						<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${acceptUser.userName}(${acceptUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${acceptUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${acceptUser.phone},办公电话:${acceptUser.telPhone},邮箱:${acceptUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${acceptUser.userId}" />
								</dt></a></td>
						<td><s:date name="reviewTime" format="yyyy-MM-dd"/></td>
						<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${reviewUser.userName}(${reviewUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${reviewUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${reviewUser.phone},办公电话:${reviewUser.telPhone},邮箱:${reviewUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${reviewUser.userId}" />
								</dt></a></td>
						<td>${areaName }</td>
						<td id="qualityPlanCode"><s:property value="qualityPlanCode" /></td>
						<td id="qualityPlanName"><s:property value="qualityPlanName" /></td>
						<td><s:property value="makerName"/></td>
						<td><s:if  test="exceedPlanEndDate==1"><font color="red"></s:if><s:date name="qualityEndDate" format="yyyy-MM-dd"/></td>
						<td style="display: none"><input type="hidden" id="insertOperatorId" name="insertOperatorId" value="${insertOperatorId }" /> </td>
						<td style="display: none"><input type="hidden" id="reviewUserId" name="reviewUserId" value="${reviewUser.userId }" /> </td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	 <div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select cssClass="" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
				name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'showQuqlityModifyPersonList')">
			</s:select>
			<span>条，共${currentPage.total}条</span>
		</div>
		<div class="pagination" rel="showQuqlityModifyPersonList" totalCount="${currentPage.total}" currentPage="${currentPage.pageNo}"
	     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
	</div> 
	 <div class="pageFormdiv">
		<button  type="button" class="but_blue" onclick="qualityModifyPerson()">抽检处理</button>
		<button  type="button" class="but_blue" onclick="shareInpsectTask()">退回共享池</button>
     </div>  
</div>

<script type="text/javascript" charset="utf-8" language="javascript" >
$(function(){
	$("#tbodyPerson", navTab.getCurrentPanel()).find("tr").each(function(){			
		var reg = new RegExp(",","g");//替换
		var _policyCode=$(this).find("td#policyCode").text();
		//console.info(_policyCode);
		_policyCode=_policyCode.replace(reg,"<br/>");
		//console.info(_policyCode);
		$(this).find("td#policyCode").html(_policyCode);	
		
		var reg2 = new RegExp("、","g");//替换
		var policyOrganCode=$(this).find("td#policyOrganCode").text();
		policyOrganCode=policyOrganCode.replace(reg2,"<br/>");
		$(this).find("td#policyOrganCode").html(policyOrganCode);
	});	
});
function shareInpsectTask(){
	var qualityId = $("#personList").find("input#qualityId:checked").val();
	var currentUserId = $("#currentUserId").val();
	if(qualityId == null || qualityId == ''){
		alertMsg.error("请选择质检任务！");
		return;
	}
	$.ajax({
        type:'POST',
        url:'${ctx}/cs/csAccept/checkInpsectPersonTask_PA_csQualityErrorScoreAction.action',
        data : {
        	"qualityIdStr" : qualityId,
       	 	"currentUserId": currentUserId
        },
        dataType:"json",
        async : false,
		cache : false,
        success:function(data){
        	if(data.statusCode == 300){
				alertMsg.error("此任务已不在个人任务池内，请刷新任务池！");
				return false;
			}else{
				$.ajax({
			        type:'POST',
			        url:'${ctx}/cs/csAccept/shareInpsectTask_PA_csQualityErrorScoreAction.action',
			        data : {
			        	 "qualityId" : qualityId,
			        	 "currentUserId": currentUserId
			        },
			        dataType:"json",
			        async : false,
					cache : false,
			        success:function(data){
			        	if(data.statusCode == 300){
							alertMsg.error(data.message);
							return false;
						}else{
							alertMsg.info("处理成功");
							var $obj = $("##pagerForm[name='comm']", navTab.getCurrentPanel());
				        	$obj.submit();
				        	var $obj2 = $("#pagerForm[name='person']", navTab.getCurrentPanel());
				        	$obj2.submit();
						}
			        }
			    });
			}
        }
    });
}
function searchPersonPageListModify(){
	var $obj2 = $("#pagerForm[name='person']", navTab.getCurrentPanel());
	$obj2.submit();
}
function qualityModifyPerson(){
	var qualityId = $("#personList").find("input#qualityId:checked").val();
	//parent().parent().children()
	var tr = $("#personList").find("input#qualityId:checked").closest("tr");
	var policyCode = tr.find("td#policyCode").text();//保单号
	//var organCode = tr.find("td#organCode").text();//机构
	var qualityPlanCode =tr.find("td#qualityPlanCode").text();//质检计划代码
	var currentUserId = $("#currentUserId").val();
	if(qualityId == null || qualityId == ''){
		 alertMsg.error("请选择抽检处理任务！");
		 return false;
	} 
	$.ajax({
        type:'POST',
        url:'${ctx}/cs/csAccept/checkInpsectPersonTask_PA_csQualityErrorScoreAction.action',
        data : {
        	"qualityIdStr" : qualityId,
       	 	"currentUserId": currentUserId
        },
        dataType:"json",
        async : false,
		cache : false,
        success:function(data){
        	if(data.statusCode == 300){
				alertMsg.error("此任务已不在个人任务池内，请刷新任务池！");
				return false;
			}else{
				var title = "抽检处理";	
				var fresh = eval("true");
				var external = eval("false"); 
			    var tabid="抽检处理";
			   	var url ="${ctx }/cs/csQuality/qualityModifyPage_PA_csQualityErrorScoreAction.action?qualityInspectionInfoVO.qualityId="
			   			+qualityId+"&qualityInspectionInfoVO.qualityPlanCode="+qualityPlanCode+"&callBackFunctionModify=searchPersonPageListModify";
			   	
			   	//navTab.closeCurrentTab();//关闭当前页签
			   	navTab.openTab(tabid, url, {
					title : title,
					fresh : fresh,
					external : external
				});	 
			}
        }
    });
	
	
}

</script>