<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
	<table class="list" id="appDoctable2" style="width:100%" table_saveStatus="1" >
	<thead>
		<tr>
			<th>抽检日期</th>
			<th>抽检人员</th>
			<th>抽检结论</th>
			<th>抽检意见</th>
			<th>质检问题类型</th>
			<th>抽检备注</th>
			<th>是否整改</th>
			<th>整改日期</th>
			<th>整改人员</th>
		</tr>
	</thead>
		<tbody align="center">
				<s:iterator value="qualityInspectionLogListVO" status="st" id="qr1">
					<tr id="<s:property value='inspectionId' />">
						<td><s:date name="qualityDate" format="yyyy-MM-dd"/></td>
						<td><s:property value="qualityCheckName" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT"  value="${qualityResult }" /></td>
						<td><s:property value="qualitySurgest" /></td>
						<td><s:property value="questionType" /></td>
						<td><s:property value="qualityRemark" /></td>
						<td>
							<s:if test="modifyMode != null">是</s:if>
							<s:else>否</s:else>
						</td>
						<td><s:date name="qualityModifyDate" format="yyyy-MM-dd"/></td>
						<td><s:property value="qualityModifyByName" /></td>
					</tr>
				</s:iterator>
				</tbody>
			</div>
		</table>
</div> 
