<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">
<form id="pagerForm" method="post" style="display:none;"
	action="${ctx }/cs/csQuality_qpm/queryQualityPlan_PA_qualityPlanManageAction.action"
	onsubmit="return navTabSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<div layoutH="36px">
<form action="${ctx }/cs/csQuality_qpm/queryQualityPlan_PA_qualityPlanManageAction.action" id="queryQualityPlanPage"
		onsubmit="return navTabSearch(this);" method="post" rel="pagerForm" class="pageForm required-validate">
	<div class="pageContent">
		<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">查询条件
				</h1>
			 </div>
			<div class="pageFormInfoContent">
				<dl>
					<dt>计划代码</dt>
					<dd><input type="text" id="planNo" name="qualityQuaryVO.planNo" maxlength="20" value="${qualityQuaryVO.planNo }"/></dd>
				</dl>
				<dl>
					<dt>计划名称</dt>
					<dd><input type="text" id="planName" name="qualityQuaryVO.planName" maxlength="20" value="${qualityQuaryVO.planName }" /></dd>
				</dl>
				<dl>
					<dt>执行起始日期</dt>
					<dd><input type="expandDateYMDRO" id="planDateStart" name="qualityQuaryVO.planDateStart" maxlength="20" class="required" 
						value='<s:date name="qualityQuaryVO.planDateStart" format="yyyy-MM-dd"/>'/></dd>
				</dl>
				<dl>
					<dt>执行终止日期</dt>
					<dd><input type="expandDateYMDRO" id="planDateEnd" name="qualityQuaryVO.planDateEnd" maxlength="20" class="required" 
						value='<s:date name="qualityQuaryVO.planDateEnd" format="yyyy-MM-dd"/>'/></dd>    
				</dl>
				<div class="pageFormdiv">
					<button onclick="querySubmit()" class="but_blue" type="button" >查询</button>
				</div>
			</div>
	</div>
</form>
<form>
	<div class="pageContent">
	    <div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">质检计划统计
				</h1>
		</div>
	 <div class="tabdivclass">
			<table class="list" style="width: 100%">
				<thead>
					<tr>
						<th>计划名称</th>
						<th>计划已抽档件数</th>
						<th>计划已执行件数</th>
						<th>计划版本号</th>
						<th>计划制定人</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="currentPage.pageItems" id="" status="st">
						<tr>
							<td>${planName }</td>
							<td>${planTotal }</td>
							<td>${qualityTotal }</td>
							<td>${planVersionNum }</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${planUser }"/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			 <div class="panelBar" >
		        <div class="pages">
		            <span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
							onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
						</s:select> <span>条，共${currentPage.total}条</span>        
				</div>
		        <div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
		        	pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
			</div>
			</div>
	</div>
</form>
</div>
<script type="text/javascript">
	function querySubmit() {
		var $form = $("#queryQualityPlanPage",navTab.getCurrentPanel()); 
		$form.submit();
	}
	
</script>