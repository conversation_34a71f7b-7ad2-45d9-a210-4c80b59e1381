<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">	
<style>
.combox .select a{ 
	
	width:200px;}/* 下拉框固定长度 */
</style>
<div class="pageContent" layoutH="36px">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">待发布质检计划
		</h1>
	 </div>
	<div class="pageContent"  style="width:100%;">
	  <div class="tabdivclass">
			<table class="list" style="width: 100%">
				<thead>
					<tr>
						<th>质检计划号</th>
						<th>质检计划名称</th>
						<th>创建人</th>
						<th>计划制定机构</th>
						<th>上传任务总数据量</th>
						<th>抽检质检任务总数据量</th>
						<th>质检计划状态</th>
						<th>质检计划制定时间</th>
						<th>质检截止日期</th>
					</tr>
				</thead>
				<tbody id="planTBody">
						<tr>
						    <input type="hidden" id ="qualityPlanIdHidden" value="${qualityPlanVO.qualityPlanId}">
							<td>${qualityPlanVO.planNo }</td>
							<td>${qualityPlanVO.planName }</td>
							<td>${qualityPlanVO.makerName }</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${qualityPlanVO.insertedOrgCode}"/></td>
							<td>${qualityPlanVO.uploadTotal }</td>
							<td>${qualityPlanVO.selectedTotal }</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_PLAN_STATUS" value="${qualityPlanVO.qualityPlanStatus}"/></td>
							<td><s:date name="qualityPlanVO.makeTime" format="yyyy-MM-dd HH:mm:ss"/></td>
							<td><s:date name="qualityPlanVO.qualityEndDate" format="yyyy-MM-dd"/></td>
						</tr>
				</tbody>
			</table>
			</div>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">分配作业人员列表
		</h1>
	 </div>
	<div class="pageFormInfoContent">
				<dl>
					<dt></dt>
					<dd><input type="radio" name="allocateType" onclick="showDiv()" value="1">按机构分发</dd>
					<dd><input type="radio" name="allocateType" onclick="showDiv()" value="2">按数量分发至机构</dd>
					<dd><input type="radio" name="allocateType" onclick="showDiv()" value="3">按人员分发</dd>
				</dl>
	</div>
<div id="allocateOrgDiv" style="display: none">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">分发机构 剩余未分发质检任务总量：<span id="selectedTotalSpan">${qualityPlanVO.selectedTotal }</span>   <input id="selectedTotalHidden" type="hidden" value="${qualityPlanVO.selectedTotal }"/>
		</h1>
    </div>
	<div class="panelPageFormContent"  style="width:100%;">
	  <div class="tabdivclass">
			<table class="list" style="width: 40%" id ="allocateOrgTable">
				<thead>
					<tr>
						<th style="width: 50%">质检机构</th>
						<th>分发质检数量</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="allocateOrgTbody">
						<tr>
							<td>
							<s:if test="userVo.userRoleType==1">
							<Field:codeTable id="allocateOrgId1"  cssClass="combox"  nullOption="true"  tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL" name="allocateOrg1"    whereClause="organ_grade=02 or organ_code=86"/>
							</s:if>
							<s:else>
							<Field:codeTable id="allocateOrgId1"   cssClass="combox"  nullOption="true"  tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL" name="allocateOrg1"    whereClause="organ_grade=02"/>
							</s:else>
                            </td>
							<td><input type="number" onchange="changeAllocateOrg(this)" onkeydown="changeAllocateOrg(this)" onkeyup="changeAllocateOrg(this)" ></td>
							<td><a href="#" class="btnDel" onclick="delAllocateOrg(this)">删除</a></td>
						</tr>
				</tbody>
			</table>
			</div>
			<div class="pageFormdiv" style="padding-right: 1040px">
			    <button onclick="addAllocateOrg()" type="button" class="but_blue">增加质检机构</button>
			</div>
	</div>
</div>

<div id="allocateUserDiv" style="display: none">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">分发人员
		</h1>
    </div>
	<div class="panelPageFormContent"  style="width:100%;">
	  <div class="tabdivclass">
			<table class="list" style="width: 20%" id ="allocateUserTable">
				<thead>
					<tr>
						<th>质检用户</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="allocateUserTbody" >
				<a id="loadQualityPlanUser" target="dialog" width="1300" height="800" title="质检用户"  href="${ctx }/cs/csQuality_qpm/loadQualityPlanUser_PA_qualityPlanManageAction.action" style="display: none"></a>
						<tr>
							<td><input type="text" id="qualityPlanUserId1" onchange="checkUser(this)"><a href="#" class="btnLook"  onclick="loadQualityPlanUser(this)"></td>
							<td><a href="#" class="btnDel" onclick="delAllocateUser(this)">删除</a></td>
						</tr>
				</tbody>
			</table>
			</div>
			<div class="pageFormdiv"  style="padding-right: 1040px">
			    <button onclick="addAllocateUser()" type="button" class="but_blue">增加质检用户</button>
			</div>
	</div>
</div>	
	<div class="panelPageFormContent"  style="width:100%;">
			<div class="pageFormdiv" style="padding-right: 500px">
			    <button onclick="allocateTask()" type="button" class="but_blue">确定分发</button>
			</div>
	</div>
</div>
<script type="text/javascript">
function showDiv(){
	var allocateType = $('input[type=radio][name=allocateType]:checked').val();
	if(allocateType =="1"){
		$("#allocateOrgDiv").attr("style","display:none;");
		$("#allocateUserDiv").attr("style","display:none;");
	}else if(allocateType == "2"){
		$("#allocateOrgDiv").show();
		$("#allocateUserDiv").attr("style","display:none;");
	}else{
		$("#allocateOrgDiv").attr("style","display:none;");
		$("#allocateUserDiv").show();
	}
}

 function addAllocateOrg(){
	var index = $("#allocateOrgTable tr").length;
	var newRow = '<tr><td><s:if test="userVo.userRoleType==1"><Field:codeTable id="allocateOrgId'+index+'"  cssClass="combox"  nullOption="true"  tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL" name="allocateOrg'+index+'" whereClause="organ_grade=02 or organ_code=86"/></s:if>';
	newRow = newRow +'<s:else><Field:codeTable id="allocateOrgId'+index+'"  cssClass="combox"  nullOption="true"  tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL" name="allocateOrg'+index+'"    whereClause="organ_grade=02"/></s:else>';
	newRow = newRow + ' </td><td><input type="number" onchange="changeAllocateOrg(this)" onkeydown="changeAllocateOrg(this)" onkeyup="changeAllocateOrg(this)"></td><td><a href="#" class="btnDel" onclick="delAllocateOrg(this)" >删除</a></td></tr>';
	$("#allocateOrgTable").append(newRow).initUI();
} 

function addAllocateUser(){
	var index = $("#allocateUserTable tr").length;
 	var newRow = '<tr><td><input type="text" id="qualityPlanUserId'+index+'" onchange="checkUser(this)"><a href="#" class="btnLook"  onclick="loadQualityPlanUser(this)"></a></td><td><a href="#" class="btnDel" onclick="delAllocateUser(this)">删除</a></td></tr>';
	$("#allocateUserTable").append(newRow); 
}

function loadQualityPlanUser(obg){
	var userBackId = $(obg).prev('input').attr('id');
	$("#loadQualityPlanUser").attr("href","${ctx }/cs/csQuality_qpm/loadQualityPlanUser_PA_qualityPlanManageAction.action?userBackId="+userBackId);
	$("#loadQualityPlanUser").attr("target","dialog");
	$("#loadQualityPlanUser").click();
}

function delAllocateOrg(obg){
	$(obg).closest('tr').remove();
	var obgTotal = $(obg).closest('tr').find('td:eq(1) input').val();
	var selectedTotalRow = $("#selectedTotalSpan").text();
	$("#selectedTotalSpan").text(Number(selectedTotalRow) + Number(obgTotal));
}

function delAllocateUser(obg){
	$(obg).closest('tr').remove();
}


function changeAllocateOrg(obg){
	var value = $(obg).val();
	value=(value.replace(/\D|^0/g,'')==''?'':parseInt(value));
	$(obg).val(value);
	if(value<1){
		$(obg).val("");
	}
	var selectedTotal = $("#selectedTotalHidden").val();
	var selected = 0;
	var obgTotal = $(obg).val();
	$('#allocateOrgTbody tr').each(function() {
		var selectedTotalRow = $(this).find('td:eq(1) input').val();
		selected = Number(selected) +  Number(selectedTotalRow);
		});
	
	var count = Number(selectedTotal) -Number(selected);
	if(count<0){
		$(obg).val(Number(selectedTotal)+Number(obgTotal)-Number(selected));
		$("#selectedTotalSpan").text(0);
	}else{
		$("#selectedTotalSpan").text(count);
	}
	if($(obg).val()<=0){
		$(obg).val("");
	}
}

function allocateTask(){
	var allocateFlag = "1";
	var allocateType = $('input[type=radio][name=allocateType]:checked').val();
	if(allocateType==null || allocateType==""){
		alertMsg.info("请选择任务分发方式。");
		return false;
	}
	if(allocateType=="2"){
		var values = [];
		var flag = true;
  		$('#allocateOrgTbody tr').each(function() {
			var selectedTotalRow = $(this).find('td:eq(1) input').val();
			var organCode = $(this).find('td:eq(0) select').val();
	 		if(organCode==null || organCode==""){
				alertMsg.info("请录入质检机构。");
				allocateFlag = "2";
				flag = false;
				return false;
			}
	 		if(selectedTotalRow==null || selectedTotalRow==""){
				alertMsg.info("请录入分发质检数量。");
				allocateFlag = "2";
				flag = false;
				return false;
			} 
	        if ($.inArray(organCode, values) != -1) {
				alertMsg.info("质检机构录入重复，请确认！");
				allocateFlag = "2";
				flag = false;
				return false;
	        } else {
	            values.push(organCode);
	        }
			});
  		if(flag){
		    var selectedTotalSpan  = $("#selectedTotalSpan").text();
		    if(selectedTotalSpan>0){
				alertMsg.info("质检任务未全部分配，请录入分配机构数量！");
				allocateFlag = "2";
				return false;
		    }  
  		}
	}
	if(allocateType=="3"){
		var length = $("#allocateUserTable tr").length;
		if(length==1){
			alertMsg.info("请录入质检用户。");
			allocateFlag = "2";
			return false;
		}
		var values = [];
  		$('#allocateUserTbody tr').each(function() {
			var userCode = $(this).find('td:eq(0) input:eq(0)').val();
	 		if(userCode==null || userCode==""){
				alertMsg.info("请录入质检用户。");
				allocateFlag = "2";
				return false;
			} 
	        if ($.inArray(userCode, values) != -1) {
				alertMsg.info("质检用户录入重复，请确认！");
				allocateFlag = "2";
				return false;
	        } else {
	            values.push(userCode);
	        }
			});
	}
	if(allocateFlag=="1"){
		var qualityPlanId = $("#qualityPlanIdHidden").val();
		var allocateOrganList = [];
		var allocateOrganCountList = [];
			$('#allocateOrgTbody tr').each(function() {
				var selectedTotalRow = $(this).find('td:eq(1) input').val();
				var organCode = $(this).find('td:eq(0) select').val();
				allocateOrganList.push(organCode);
				allocateOrganCountList.push(selectedTotalRow);
				});
		var allocateUserList = [];
	 		$('#allocateUserTbody tr').each(function() {
				var userCode = $(this).find('td:eq(0) input:eq(0)').val();
				allocateUserList.push(userCode);
				}); 
	  	$.ajax({
			type : 'POST',
			url : "${ctx }/cs/csQuality_qpm/allocateTask_PA_qualityPlanManageAction.action",
			dataType : "json",
			traditional: true,
			data : {
				 "qualityPlanVO.qualityPlanId" : qualityPlanId,
				 "allocateType" : allocateType,
				 "allocateOrganList" : JSON.stringify(allocateOrganList),
				 "allocateOrganCountList" : JSON.stringify(allocateOrganCountList),
				 "allocateUserList" : JSON.stringify(allocateUserList),
			},
			cache : false,
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == 300) {
					alertMsg.error(json.message);
				}else{
					 alertMsg.correct("分发质检计划成功！"); 
					 $.pdialog.closeCurrent(); 
					 $("#paginationDiv",navTab.getCurrentPanel()).find(".goto").click();
				}
			},
			error : function() {
				alertMsg.err("失败");
			}
	}); 
	}
}
function checkUser(obg){
	var userName = $(obg).val();
	$.ajax({
		type : 'POST',
		url : "${ctx }/cs/csQuality_qpm/checkUser_PA_qualityPlanManageAction.action",
		data : "userVo.userName="+userName ,
		cache : false,
		success : function(data) {
			var json = DWZ.jsonEval(data);
			if (json.statusCode == 300) {
				$(obg).val("");
				alertMsg.error(json.message);
			}
		},
		error : function() {
			alertMsg.err("失败");
		}
});
}

</script>