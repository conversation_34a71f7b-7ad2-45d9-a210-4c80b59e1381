<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<div style="display:none;">
<input type="hidden" id="delteSuccess" value="${delteSuccess }"/>
</div>
<script type="text/javascript">
	$(function(){
		var delteSuccess = $("#delteSuccess").val();
		if(delteSuccess == "success"){
			alertMsg.correct("删除成功");
			var $obj1 = $("#messageForm", navTab.getCurrentPanel());
			var url = "${ctx }/cs/csQuality/initErrorScore_PA_csQualityErrorScoreAction.action";
			$obj1.attr("action", url);
			//$obj1.attr("onsubmit","return divSearch(this, '')");
			$obj1.submit();
		}else{
			 alertMsg.info("删除失败");
		}
	});
</script>