<!-- 差错基本-要点信息-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">

<div class="pageContent" layoutH="36px" style="display:block;">
	<form id="showErrorInfo" onsubmit="return divSearch(this,'getCsTaskDIV')" method="post">
		<input type="hidden" name="numPerPage" value="10" />
		<div class="panel">
			<h1>差错基本信息</h1>				
			<div class="pageFormContent">
				<dl>
					<dt>差错代码</dt>
					<dd><input type="text" name="qualityProjectVO.projectNo"    maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>差错名称</dt>
					<dd><input type="text" name="qualityProjectVO.projectName"  maxlength="20"/></dd>
				</dl>		
				<dl>
					<dt>版本号</dt>
					<dd><input type="text" name="qualityProjectVO.version" value="0.0.1" maxlength="20" /></dd>
				</dl>
				<dl>
					<dt>扣分标准</dt>
					<dd><input type="text" name="qualityProjectVO.accountScore" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>是否有效</dt>
					<dd><Field:codeTable  cssClass="combox" nullOption="true" name="qualityProjectVO.validFlag" tableName="APP___PAS__DBUSER.T_YES_NO"/></dd>    
				</dl>
				
			</div>	
		</div>
		
		<div class="panel">	
	<!-- 差错要点信息 -->
		<h1>差错要点信息</h1>				
		<div class="pageFormContent">
				<dl>
					<dt>差错代码</dt>
					<dd><input type="text" name="csTaskMsgVO.applyCode"  maxlength="20"/></dd>
				</dl>
		</div>	
		<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
			<table class="list" id="appDoctable" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>选择</th>
						<th>差错代码</th>
						<th>差错名称</th>
						<th>扣分标准</th>
						<th>要点描述</th>
						<th>是否有效</th>
						<th>删除</th>
					</tr>
				</thead>
				<tbody align="center">
					<%-- <s:iterator value="qualityPrjPointListVO" status="st" id="qr">
						<tr>
							<td><input type="radio" name="radio" name="qualityPrjPointVO.projectPointId" value='${projectPointId}'/></td>
							<td><s:property value="projectNo" /></td>
							<td><s:property value="projectName" /></td>
							<td ><s:property value="pointScore" /></td>
							<td><s:property value="pointDesc" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.t_accept_status" value="${appStatus}"/></td>
							<td><a href="javascript:void(0)" class="BtnDel" onclik="delPriPointInfo(${projectPointId})">删除</a></td>
						</tr>
					</s:iterator>  --%>
				</tbody>
			</table>
		</div> 
			<div class="panelBar">
				<table style="width:100%">
					<tr>
						<td></td>
						<td style="width:200px">
							<div class="button">
								<div class="buttonContent">
									<button type="button" onclick="queryPublicEntryForm('publicEntryForm','getCsTaskDIV');">提交</button>
								</div>
							</div>
						</td>
						<td></td>
						<td style="width:200px">
							<div class="button">
								<div class="buttonContent">
									<button type="button" onclick="queryPublicEntryForm('publicEntryForm','getCsTaskDIV');">返回</button>
								</div>
							</div>
						</td>
					</tr>
				</table>
			</div>	
		</div>
	</form>

</div>
<script type="text/javascript">
function delPriPointInfo(projectPointId){
	alert("删除要点："+projectPointId);
	$.ajax({
		url : "${ctx }/cs/csQuality/deleteQualityPoint_PA_csQualityErrorControlAction.action",
		type : post,
		data : {
			"qualityPrjPointVO.projectPointId" : projectPointId
		},
		error : function(){
			alertMsg.info("出错了");
		},
		success : function(result){
			alertMsg.info("删除成功");
		}
	});
}


//变更差错信息
function changeErrorInfo(obj){
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	if(radio.length == 0){
		alertMsg.info("请选择要申请的任务");
		return false;
	}else{
		var url ="${ctx}/cs/csTask/ApplyShareTask_PA_csEntryTaskPoolAction.action?csTaskMsgVO.applyCode="+radio[0].value;
		$obj1.attr('action', url);
		$obj1.attr("onsubmit","return validateCallback(this)");
		$obj1.submit();
		
		
	}
}

//复制差错信息
function copyErrorInfo(obj){
	
}






function applyShareTaskToPrivate(obj){	
	
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	if(radio.length!=0){
		var tr=radio.closest("tr");
		var v=confirm("确定要申请该任务？");
		if(v){
			var url ="${ctx}/cs/csTask/ApplyShareTask_PA_csEntryTaskPoolAction.action?csTaskMsgVO.applyCode="+radio[0].value;
			$obj1.attr('action', url);
			$obj1.attr("onsubmit","return validateCallback(this)");
			$obj1.submit();
			//tr.remove();
			// 重新查询公共池
			setTimeout("queryPublicEntryForm('publicEntryForm','getCsTaskDIV');",1000);
			//重新查询个人池
			setTimeout('$("#privateEntryForm",navTab.getCurrentPanel()).submit();',2000);
		}
	}else{
		alertMsg.info("请选择要申请的任务");
		return false;
	}
}
</script>