<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx }/cs/pages/quality/qualityPlanManage_check.js"></script>
<style type="text/css">
	#_qualityProperty dt{ width:15px}
	#_qualityProperty dd{ width:215px}
</style>
<form method="post" id="savePlanManage" onsubmit="return navTabSearch('savePlanManage');">
	<input type="hidden" id="mPlanId" value="${qualityPlanManageVO.qualityPlanId }"/>
	<div class="pageContent">
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">计划基本信息
			</h1>
		 </div>
			<div class="panelPageFormContent">
				<dl>
					<dt>计划代码</dt>
					<dd><input onkeyup="value=value.replace(/[^\w\.\/]/ig,'')" 
						onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\w\.\/]/ig,''))"
						type="text" id="mPlanNo" name="qualityPlanManageVO.planNo" maxlength="20" value="${qualityPlanManageVO.planNo }" /></dd>
				</dl>
				<dl>
					<dt>计划名称</dt>
					<dd><input type="text" id="mPlanName" name="qualityPlanManageVO.planName" maxlength="20" value="${qualityPlanManageVO.planName }" /></dd>
				</dl>
				<dl>
					<dt>是否有效</dt>
					<dd><Field:codeTable id="mValidFlag" cssClass="combox" name="qualityPlanManageVO.validFlag" defaultValue="1" tableName="APP___PAS__DBUSER.T_YES_NO" value="${qualityPlanManageVO.validFlag }" /></dd>    
				</dl>
				<dl>
					<dt>版本号</dt>
					<dd><input type="text" id="mPlanVersionNum" name="qualityPlanManageVO.planVersionNum" maxlength="20" readonly="readonly" value="${qualityPlanManageVO.planVersionNum }" /></dd>
				</dl>
			</div>
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">计划抽档相关性
			</h1>
		 </div>	
		 
		<div class="panelPageFormContent" id="_qualityProperty" style="margin-left: 30px">
				<dl>
					<dt><input type="checkbox" name="serviceCode" id="_serviceCode" <s:if test='qualityPlanManageVO.serviceCodeList != null && qualityPlanManageVO.serviceCodeList.size > 0'> checked ='true'</s:if> /></dt>
					<dd>保全项目</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="orgCode" id="_orgCode" <s:if test='qualityPlanManageVO.orgCodeList != null && qualityPlanManageVO.orgCodeList.size > 0'> checked ='true'</s:if> /></dt>
					<dd>保单管理机构</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="organCode" id="_organCode" <s:if test='qualityPlanManageVO.organCodeList != null && qualityPlanManageVO.organCodeList.size > 0'> checked ='true'</s:if>/></dt>
					<dd>用户归属机构及人员</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="busiProdCode" id="_busiProdCode" <s:if test='qualityPlanManageVO.busiProdCodeList != null && qualityPlanManageVO.busiProdCodeList.size > 0'> checked ='true'</s:if>/></dt>
					<dd>险种</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="acceptFlag" id="_acceptFlag" <s:if test='qualityPlanManageVO.acceptFlag != null'> checked ='true'</s:if>/></dt>
					<dd>是否异地业务</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="feeAmount" id="_feeAmount" <s:if test='qualityPlanManageVO.arapFlag != null'> checked ='true'</s:if>/></dt>
					<dd>保全补退费金额</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="reviewType" id="_reviewType" <s:if test='qualityPlanManageVO.reviewTypeList != null && qualityPlanManageVO.reviewTypeList.size > 0 '> checked ='true'</s:if>/></dt>
					<dd>保全复核类型</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="bizSignFlag" id="_bizSignFlag" <s:if test='qualityPlanManageVO.bizSignFlag != null'> checked ='true'</s:if>/></dt>
					<dd>签报</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="deferTime" id="_deferTime" <s:if test='qualityPlanManageVO.deferTimeLeast != null'> checked ='true'</s:if>/></dt>
					<dd>延期天数</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="ruleType" id="_ruleType" <s:if test='qualityPlanManageVO.ruleType != null'> checked ='true'</s:if>/></dt>
					<dd>是否常规类型</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="nofillFlag" id="_nofillFlag" <s:if test='qualityPlanManageVO.nofillFlag != null'> checked ='true'</s:if>/></dt>
					<dd>是否免填单</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="createDate" id="_createDate" <s:if test='qualityPlanManageVO.createDateStart != null'> checked ='true'</s:if>/></dt>
					<dd>人员上岗时间</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="pasRank" id="_pasRank" <s:if test='qualityPlanManageVO.permissionTypeIdList != null && qualityPlanManageVO.permissionTypeIdList.size > 0 '> checked ='true'</s:if>/></dt>
					<dd>保全权限级别</dd>
				</dl>
				<dl>
					
					<dt><input type="checkbox" name="validateTime" id="_validateTime" <s:if test='qualityPlanManageVO.validateTimeStart != null'> checked ='true'</s:if>/></dt>
					<dd>保全生效日期</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="serviceType" id="_serviceType" <s:if test='qualityPlanManageVO.serviceTypeList != null && qualityPlanManageVO.serviceTypeList.size > 0 '> checked ='true'</s:if>/></dt>
					<dd>保全申请类型</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="isAlreadyFlag" id="_isAlreadyFlag" <s:if test='qualityPlanManageVO.isAlreadyFlag != null'> checked ='true'</s:if>/></dt>
					<dd>是否已制盘</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="isArapFlag" id="_isArapFlag" <s:if test='qualityPlanManageVO.isArapFlag != null '> checked ='true'</s:if>/></dt>
					<dd>是否收付费方式变更</dd>
					
				</dl>
				<dl>
					<dt><input type="checkbox" name="preFlag" id="_preFlag" <s:if test='qualityPlanManageVO.preFlag != null'> checked ='true'</s:if>/></dt>
					<dd>是否预受理</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="bankCode" id="_bankCode" <s:if test='qualityPlanManageVO.bankCodeList != null && qualityPlanManageVO.bankCodeList.size>0 '> checked ='true'</s:if>/></dt>
					<dd>银行网点</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="payToApplicant" id="_payToApplicant" <s:if test='qualityPlanManageVO.payToApplicant != null'> checked ='true'</s:if>/></dt>
					<dd>付费账户户名为申请资格人</dd>
				</dl>
				<dl>
					<dt><input type="checkbox" name="lrFlag" id="_lrFlag" <s:if test='qualityPlanManageVO.lrFlag != null'> checked ='true'</s:if>/></dt>
					<dd>补发保单</dd>
				</dl>				
			</div>
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">相关性参数定义
			</h1>
		 </div>		
		<div class="pageContent" style="padding:5px; overflow:hidden">
			<!-- 保全项目 -->
			<div class="panelW">
			<div class="multipleBox" id="csServiceCode" <s:if test='qualityPlanManageVO.serviceCodeList == null || qualityPlanManageVO.serviceCodeList.size == 0'> style="display:none;"</s:if>>
			<div class="pageFormContent" id="treeSearchDiv">
				<dl>
					<dt>保全项目快速查询</dt>
					<dd>
						<input type="hidden" name="codeAndPingYing" value="${codeAndPingYing}" id="codeAndPingYing"/>
						<input name="itemName" value="" id="itemName" onkeyup="queryCsItem();" /> <a class="btnLook"
							>查找带回</a>
					</dd>
				</dl>
			</div>
				<div class="codeTable">
					<Field:codeTable name="" tableName="APP___PAS__DBUSER.T_SERVICE" />
				</div>
				<div class="lMultipleBox" style="float: inherit;" id="lMultipleBox">
					<div class="lTop">保全项目</div>
					<select size="10" multiple="multiple" id="multiple" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class=" lToR">
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class=" lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class=" rToL">
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class=" rToLAll">
						<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的保全项目</div>
					<select size="10" multiple="multiple">
						<s:if test='qualityPlanManageVO.serviceCodeList != null && qualityPlanManageVO.serviceCodeList.size > 0'> 
							<s:iterator value="qualityPlanManageVO.serviceCodeList " var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
													value="${var}" />
								</option>
							</s:iterator>
						</s:if>
					</select>
				</div>
					<%-- <div id="menuContent" class="menuContent"
						style="display: none; position: absolute; z-index: 1002;">
						<select size="10" id="hiddmenuselect"  multiple="multiple" style="margin-top: 0; width: 134px; "onclick="hiddmenu();" onblur="hiddenmenu();">
						</select>
					</div> --%>
			</div>
			</div>
			<!-- 保全权限 -->
			<div class="panelW">
			
			<div class="multipleBox" id="csPermissionTypeId" <s:if test='qualityPlanManageVO.permissionTypeIdList == null || qualityPlanManageVO.permissionTypeIdList.size == 0 '> style="display:none;"</s:if>>
				<div class="codeTable">
					<Field:codeTable cssClass="combox" name=""
						tableName="APP___PAS__DBUSER.T_UDMP_PERMISSION_INFO" value="" />
				</div>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">保全权限级别</div>
					<select size="10" multiple="multiple" id="" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR" >
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL" >
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll">
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的保全权限级别</div>
					<select id="permissionTypeId" size="10" multiple="multiple">
						<s:iterator value="qualityPlanManageVO.permissionTypeIdList " var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_PERMISSION_INFO"
													value="${var}" />
								</option>
							</s:iterator>	
					</select>
				</div>
			</div>
			</div>
			<!-- 保单管理机构 -->
			<div class="panelW">
			<div class="multipleBox" id="csOrgCode" <s:if test='qualityPlanManageVO.orgCodeList == null || qualityPlanManageVO.orgCodeList.size == 0'> style="display:none;"</s:if>>
				<div class="codeTable">
					<Field:codeTable cssClass="combox" name=""
						tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="" whereClause="ORGAN_CODE like '${operatorOrganCode}%'"/>
				</div>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">保单管理机构</div>
					<select size="10" multiple="multiple" id="" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR" >
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL">
						 <button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll" >
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的保单管理机构</div>
					<select id="orgCode" size="10" multiple="multiple">
						<s:if test='qualityPlanManageVO.orgCodeList != null && qualityPlanManageVO.orgCodeList.size > 0'> 
							<s:iterator value="qualityPlanManageVO.orgCodeList" var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG"
													value="${var}" />
								</option>
							</s:iterator>
						</s:if>
					</select>
				</div>
			</div>
			<div class="lMultipleBox public1_lMultipleBox" id="csIsCluOrgCode" style="display:none;">
				<dl style=" width:370px;">
					<dt style="width:190px;float:left;height:20px;line-height:20px">保单管理机构是否包含下属机构</dt>
					<dd style="width:113px;float:left">
						<Field:codeTable id="isCluOrgCode" cssClass="combox" name="qualityPlanManageVO.isCluOrgCode" nullOption="true" 
				 				tableName="APP___PAS__DBUSER.T_YES_NO" value="${qualityPlanManageVO.isCluOrgCode}"/> 
					</dd>
				</dl>
			</div>
			</div>
			
			<!-- 用户归属机构  -->
			<div class="panelW">
			<div class="multipleBox" id="csOrganCode" <s:if test='qualityPlanManageVO.organCodeList == null || qualityPlanManageVO.organCodeList.size == 0'> style="display:none;"</s:if>>
				<div class="codeTable">
					<Field:codeTable cssClass="combox" name=""
						tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="" whereClause="ORGAN_CODE like '${operatorOrganCode}%'"/>
				</div>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">用户归属机构</div>
					<select size="10" multiple="multiple" id="" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR">
						<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll" >
						<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL">
						<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll">
						<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的用户归属机构</div>
					<select id="organCode" size="10" multiple="multiple">
							<s:iterator value="qualityPlanManageVO.organCodeList " var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG"
													value="${var}" />
								</option>
							</s:iterator>
					</select>
				</div>
			</div>
			<div class="pageFormContent" id="csIsCluOrganCode" <s:if test='qualityPlanManageVO.organCodeList == null || qualityPlanManageVO.organCodeList.size == 0'> style="display:none;"</s:if>>
				<dl>
					<dt style="width:170px;float:left;height:20px;line-height:20px">用户归属机构包含下属机构</dt>
					<dd style="width:50px;float:left;">
				 		<input type="checkbox" name="isCluOrganCodes" id="isCluOrganCode"/>
					</dd>
				</dl>
			</div>
			</div>
		
			<!-- 用户  -->
			<div class="panelW">
			<div class="multipleBox" id="csUserId" style="display:none;">
				
				<div class="lMultipleBox"   style="float: inherit;">
					<div class="lTop">用户</div>
				<select size="10" multiple="multiple" id="csUsercs" optionType="codeAndValue">
				</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR">
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL">
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll">
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的用户归属机构</div>
					<select id="userId" size="10" multiple="multiple">
					</select>
				</div>
			</div>
			</div>
			<!-- 保全申请类型 -->
			<div class="panelW">
			
			<div class="multipleBox" id="csServiceType" <s:if test='qualityPlanManageVO.serviceTypeList == null || qualityPlanManageVO.serviceTypeList.size == 0 '> style="display:none;"</s:if>>
				<div class="codeTable">
					<Field:codeTable cssClass="combox" name=""
						tableName="APP___PAS__DBUSER.T_SERVICE_TYPE" value="" />
				</div>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">保全申请类型</div>
					<select size="10" multiple="multiple" id="" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR">
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL">
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll">
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的保全申请类型</div>
					<select id="serviceType" size="10" multiple="multiple">
						<s:iterator value="qualityPlanManageVO.serviceTypeList " var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE"
													value="${var}" />
								</option>
							</s:iterator>
					</select>
				</div>
			</div>
			</div>
			<!-- 保全复核类型 -->
			<div class="panelW">
			
			<div class="multipleBox" id="csReviewType" <s:if test='qualityPlanManageVO.reviewTypeList == null || qualityPlanManageVO.reviewTypeList.size == 0 '> style="display:none;"</s:if>>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">保全复核类型</div>
					<s:if test='qualityPlanManageVO.reviewTypeList == null || qualityPlanManageVO.reviewTypeList.size == 0 '>
						<s:select list="#{'1':'自动复核', '3':'集中复核', '2':'保全复核'}"
								size="10" multiple="multiple" listKey="key"
							listValue="value" >
						</s:select>
					</s:if><s:else>
						<select size="10" multiple="multiple">
							<s:if test='!(qualityPlanManageVO.reviewTypeList.contains("1"))'><option value="1">自动复核</option></s:if>
							<s:if test='!(qualityPlanManageVO.reviewTypeList.contains("3"))'><option value="3">集中复核</option></s:if>
							<s:if test='!(qualityPlanManageVO.reviewTypeList.contains("2"))'><option value="2">保全复核</option></s:if>
						</select>
					</s:else>
				</div>
				<div class="multipleButton ">
					<div class="lToR" >
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll" >
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL">
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll" >
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的保全复核类型</div>
					<select id="reviewType" size="10" multiple="multiple">
						<s:iterator value="qualityPlanManageVO.reviewTypeList " var="var">
								<option value="${var}">
									<s:if test='#var == 1'>自动复核</s:if>
									<s:if test='#var == 3'>集中复核</s:if>
									<s:if test='#var == 2'>保全复核</s:if>
								</option>
							</s:iterator>
					</select>
				</div>
			</div>
			</div>
			<!-- 险种 -->
			<div class="panelW">
			
			<div class="multipleBox" id="csBusiProdCode" <s:if test='qualityPlanManageVO.busiProdCodeList == null || qualityPlanManageVO.busiProdCodeList.size== 0'> style="display:none;"</s:if>>
				<%-- <div class="pageFormContent" id="treeSearchDiv">
					<dl>
						<dt>险种快速查询</dt>
						<dd>
							<input type="hidden" name="busiPingYing" value="${busiPingYing}" id="busiPingYing"/>
							<input name="busitemName" value="" id="busitemName" onkeyup="queryCsItemBusi();" /> 
							<a class="btnLook"	>查找带回</a>
						</dd>
					</dl>
				</div> --%>
				<div class="codeTable">
					<Field:codeTable cssClass="combox" name=""
						tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="" />
				</div>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">险种</div>
					<select size="10" multiple="multiple" id="Bussmultiple" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR" >
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL">
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll">
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的险种</div>
					<select id="busiProdCode" size="10" multiple="multiple">
						<s:iterator value="qualityPlanManageVO.busiProdCodeList " var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
													value="${var}" />
								</option>
							</s:iterator>
					</select>
				</div>
			</div>
			</div>
			<!-- 银行网点 -->
			<div class="panelW">
			
			<div class="multipleBox" id="csBankCode" <s:if test='qualityPlanManageVO.bankCodeList == null || qualityPlanManageVO.bankCodeList.size==0 '> style="display:none;"</s:if>>
				<div class="codeTable">
					<Field:codeTable cssClass="combox" name=""
						tableName="APP___PAS__DBUSER.T_BANK_BRANCH" value=""  whereClause="BANK_BRANCH_TYPE='00'"/>
				</div>
				<div class="lMultipleBox" id=""  style="float: inherit;">
					<div class="lTop">银行 </div>
					<select size="10" multiple="multiple" id="Bankmultiple" optionType="codeAndValue">
					</select>
				</div>
				<div class="multipleButton ">
					<div class="lToR">
							<button type="button" class="but_blue">增加》</button>
					</div>
					<div class="lToRAll">
							<button type="button" class="but_blue">增加全部》</button>
					</div>
					<div class="rToL" >
							<button type="button" class="but_blue">《删除</button>
					</div>
					<div class="rToLAll">
							<button type="button" class="but_blue">《删除全部</button>
					</div>
				</div>
				<div class="rMultipleBox">
					<div class="rTop">参与质检的银行 </div>
					<select id="bankCode" size="10" multiple="multiple">
						<s:iterator value="qualityPlanManageVO.bankCodeList " var="var">
								<option value="${var}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_BANK_BRANCH"
													value="${var}" />
								</option>
							</s:iterator>
					</select>
				</div>
			</div>
			</div>
			<!-- 保全生效日期 -->
			<div class="panelW " id="csValidateTime" <s:if test='qualityPlanManageVO.validateTimeStart == null '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>保全生效日期起期</dt>
					<dd>
						<input type="expandDateYMDRO" id="validateTimeStart" name="qualityPlanManageVO.validateTimeStart" maxlength="20" class="required" 
						value='<s:date name="qualityPlanManageVO.validateTimeStart" format="yyyy-MM-dd"/>'/>
					</dd>
				</dl>
				<dl>
					<dt>保全生效日期止期</dt>
					<dd>
						<input type="expandDateYMDRO" id="validateTimeEnd" name="qualityPlanManageVO.validateTimeEnd" maxlength="20" class="required" 
						value='<s:date name="qualityPlanManageVO.validateTimeEnd" format="yyyy-MM-dd"/>'/>
					</dd>
				</dl>
			</div>
			</div>
			<!-- 保全补退费金额 -->
			<div class="panelW" id="csFeeAmount"  <s:if test='qualityPlanManageVO.arapFlag == null '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>保全费用类型</dt>
					<dd>
					 <s:if test='qualityPlanManageVO.arapFlag == null '>
					 	<s:select list="#{0:'全部', 1:'补费', 2:'退费'}"
							cssClass="combox"  
							name="qualityPlanManageVO.arapFlag" theme="simple" listKey="key" id="arapFlag"
							listValue="value" value="#{qualityPlanManageVO.arapFlag}">
						</s:select>
					 </s:if><s:else>
					 	<select class="combox" 
							name="qualityPlanManageVO.arapFlag" id="arapFlag">				 		
					 	<option value='0'<s:if test='qualityPlanManageVO.arapFlag == 0'> selected='selected'</s:if>>全部</option>
					 	<option value='1'<s:if test='qualityPlanManageVO.arapFlag == 1'> selected='selected'</s:if>>补费</option>
					 	<option value='2'<s:if test='qualityPlanManageVO.arapFlag == 2'> selected='selected'</s:if>>退费</option>
					 	</select>
					 </s:else>						
					</dd>
				</dl>
				<dl>
					<dt>保全补退费金额最小值</dt>
					<dd>
						<input onkeyup="value=value.replace(/[^-\d]/g,'')"
							onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))"
							type="text" id="feeAmountLeast" name="qualityPlanManageVO.feeAmountLeast" value="${qualityPlanManageVO.feeAmountLeast }" maxlength="20" />
					</dd>
				</dl>
				<dl>
					<dt>保全补退费金额最大值</dt>
					<dd>
						<input onkeyup="value=value.replace(/[^-\d]/g,'')"
							onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))" 
							type="text" id="feeAmountMost" name="qualityPlanManageVO.feeAmountMost" value="${qualityPlanManageVO.feeAmountMost }"maxlength="20" />
					</dd>
				</dl>
			</div>
			</div>
			<!-- 延期天数 -->
			<div class="panelW" id="csDeferTime" <s:if test='qualityPlanManageVO.deferTimeLeast == null'>style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>延期天数最小值</dt>
					<dd>
						<input onkeyup="value=value.replace(/[^\d]/g,'')"
							onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))" 
							type="text" id="deferTimeLeast" name="qualityPlanManageVO.deferTimeLeast" value="${qualityPlanManageVO.deferTimeLeast }" maxlength="20" />
					</dd>
				</dl>
				<dl>
					<dt>延期天数最大值</dt>
					<dd>
						<input onkeyup="value=value.replace(/[^\d]/g,'')"
							onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))" 
							type="text" id="deferTimeMost" name="qualityPlanManageVO.deferTimeMost" value="${qualityPlanManageVO.deferTimeMost }" maxlength="20" />
					</dd>
				</dl>
			</div>
			</div>
			<!-- 签报 -->
			<div class="panelW" id="csBizSignFlag" <s:if test='qualityPlanManageVO.bizSignFlag == null'> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>是否签报</dt>
					<dd>
						<Field:codeTable id="bizSignFlag" cssClass="combox" name="qualityPlanManageVO.bizSignFlag"  
				 				tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="true" value="${qualityPlanManageVO.bizSignFlag }"/> 
					</dd>
				</dl>
			</div>
			</div>
			<!-- 常规类型 -->
			<div class="panelW"  id="csRuleType" <s:if test='qualityPlanManageVO.ruleType == null'> style="display:none;"</s:if>>
			<div class="pageFormContent">
				<dl>
					<dt>常规类型</dt>
					<dd>
						<s:select list="#{'':'请选择', 0:'非常规', 1:'常规'}"
							cssClass="combox" 
							name="qualityPlanManageVO.ruleType" theme="simple" listKey="key" id="ruleType"
							listValue="value" value="#{qualityPlanManageVO.ruleType}">
						</s:select>
					</dd>
				</dl>
			</div>
			</div>
			<!-- 免填单类型 -->
			<div class="panelW"  id="csNofillFlag" <s:if test='qualityPlanManageVO.nofillFlag == null'> style="display:none;"</s:if>>
			<div class="pageFormContent">
				<dl>
					<dt>免填单类型</dt>
					<dd>
						<Field:codeTable id="nofillFlag" cssClass="combox" name="qualityPlanManageVO.nofillFlag"  
				 				tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="true" value="${qualityPlanManageVO.nofillFlag }"/> 
					</dd>
				</dl>
			</div>
			</div>
			<!-- 是否为异地业务 -->
			<div class="panelW" id="csAcceptFlag" <s:if test='qualityPlanManageVO.acceptFlag == null '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>是否异地业务</dt>
					<dd>
						<Field:codeTable id="acceptFlag" cssClass="combox" name="qualityPlanManageVO.acceptFlag"  
				 				tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="true" value="${qualityPlanManageVO.acceptFlag }"/> 
					</dd>
				</dl>
			</div>
			</div>
			<!-- 人员上岗时间 -->
			<div class="panelW" id="csCreateDate" <s:if test='qualityPlanManageVO.createDateStart == null '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>人员上岗时间起期</dt>
					<dd>
						<input type="expandDateYMDRO" id="createDateStart" name="qualityPlanManageVO.createDateStart" maxlength="20" class="required" 
						value='<s:date name="qualityPlanManageVO.createDateStart" format="yyyy-MM-dd"/>'/>
					</dd>
				</dl>
				<dl>
					<dt>人员上岗时间止期</dt>
					<dd>
						<input type="expandDateYMDRO" id="createDateEnd" name="qualityPlanManageVO.createDateEnd" maxlength="20" class="required" 
						value='<s:date name="qualityPlanManageVO.createDateEnd" format="yyyy-MM-dd"/>'/>
					</dd>
				</dl>
			</div>
			</div>
			<!-- 是否已制盘 -->
			<div class="panelW" id="csIsAlreadyFlag" <s:if test='qualityPlanManageVO.isAlreadyFlag == null  '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>是否已制盘</dt>
					<dd>
						<Field:codeTable id="isAlreadyFlag" cssClass="combox" name="qualityPlanManageVO.isAlreadyFlag"  
				 				tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="true" value="${qualityPlanManageVO.isAlreadyFlag }"/> 
					</dd>
				</dl>
			</div>
			</div>
			<!-- 是否做过收付费方式变更 -->
			<div class="panelW" id="csIsArapFlag" <s:if test='qualityPlanManageVO.isArapFlag == null'> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>是否收付费方式变更</dt>
					<dd>
						<Field:codeTable id="isArapFlag" cssClass="combox" name="qualityPlanManageVO.isArapFlag"  
				 				tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="true" value="${qualityPlanManageVO.isArapFlag }"/> 
					</dd>
				</dl>
			</div>
			</div>
			<!-- 是否预受理 -->
			<div class="panelW"  id="csPreFlag" <s:if test='qualityPlanManageVO.preFlag == null'> style="display:none;"</s:if>>
			<div class="pageFormContent ">
				<dl>
					<dt>是否预受理</dt>
					<dd>
						<Field:codeTable id="preFlag" cssClass="combox" name="qualityPlanManageVO.preFlag"  
				 				tableName="APP___PAS__DBUSER.T_YES_NO" nullOption="true" value="${qualityPlanManageVO.preFlag }"/> 
					</dd>
				</dl>
			</div>
			</div>
			<!-- 付费账户户名为申请资格人 -->
			<div class="panelW" id="csPayToApplicant" <s:if test='qualityPlanManageVO.payToApplicant == null '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;付费账户户名为申请资格人</dt>
					<dd>
					 <s:if test='qualityPlanManageVO.payToApplicant == null '>
					 	<s:select list="#{0:'否', 1:'是', 2:'全部'}" cssClass="combox" 
							name="qualityPlanManageVO.payToApplicant" theme="simple" listKey="key" id="payToApplicant"
							listValue="value" value="#{qualityPlanManageVO.payToApplicant}">
						</s:select>
					 </s:if><s:else>
					 	<select class="combox" 
							name="qualityPlanManageVO.payToApplicant" id="payToApplicant">				 		
					 	<option value='0'<s:if test='qualityPlanManageVO.payToApplicant == 0'> selected='selected'</s:if>>否</option>
					 	<option value='1'<s:if test='qualityPlanManageVO.payToApplicant == 1'> selected='selected'</s:if>>是</option>
					 	<option value='2'<s:if test='qualityPlanManageVO.payToApplicant == 2'> selected='selected'</s:if>>全部</option>
					 	</select>
					 </s:else>					
					</dd>
				</dl>
			</div>
			</div>
			<!--补发保单 -->
			<div class="panelW" id="csLrFlag" <s:if test='qualityPlanManageVO.lrFlag == null '> style="display:none;"</s:if>>
			<div class="pageFormContent" >
				<dl>
					<dt>补发保单</dt>
					<dd>
					 <s:if test='qualityPlanManageVO.lrFlag == null '>
					 	<s:select list="#{0:'否', 1:'是', 2:'全部'}" cssClass="combox" 
							name="qualityPlanManageVO.lrFlag" theme="simple" listKey="key" id="lrFlag"
							listValue="value" value="#{qualityPlanManageVO.lrFlag}">
						</s:select>
					 </s:if><s:else>
					 	<select class="combox" 
							name="qualityPlanManageVO.lrFlag" id="lrFlag">				 		
					 	<option value='0'<s:if test='qualityPlanManageVO.lrFlag == 0'> selected='selected'</s:if>>否</option>
					 	<option value='1'<s:if test='qualityPlanManageVO.lrFlag == 1'> selected='selected'</s:if>>是</option>
					 	<option value='2'<s:if test='qualityPlanManageVO.lrFlag == 2'> selected='selected'</s:if>>全部</option>
					 	</select>
					 </s:else>						
					</dd>
				</dl>
			</div>
			</div>
		</div>
		<div class="pageFormdiv">
			<button onclick="savePlanManage()" type="button" class="but_blue">提交</button>
			<button class="close but_gray" type="button" onclick="exit()" >返回</button>
		</div>
	</div>
</form>
<script type="text/javascript">
function exit(){
	navTab.closeCurrentTab();
}
$(document).ready(function() {
	_cs_initMultipleBox();
	/* //保全项目
	var serviceCodeList = '${qualityPlanManageVO.serviceCodeList }';
	if(serviceCodeList != null && serviceCodeList != ''){
		$("#_serviceCode").attr('checked',true);
	} */
	//保单管理机构
	/* var orgCodeList = '${qualityPlanManageVO.orgCodeList }';
	if(orgCodeList != null && orgCodeList != ''){
		$("#_orgCode").attr('checked',true);
	}
	//用户所属机构
	var organCodeList = '${qualityPlanManageVO.organCodeList }';
	if(organCodeList != null && organCodeList != ''){
		$("#_organCode").attr('checked',true);
	}
	//保全权限
	var pasRankList = '${qualityPlanManageVO.pasRankList }';
	if(pasRankList != null && pasRankList != ''){
		$("#_pasRank").attr('checked',true);
	}
	//保全申请类型
	var serviceTypeList = '${qualityPlanManageVO.serviceTypeList }';
	if(serviceTypeList != null && serviceTypeList != ''){
		$("#_serviceType").attr('checked',true);
	}
	//保全复核类型
	var reviewTypeList = '${qualityPlanManageVO.reviewTypeList }';
	if(reviewTypeList != null && reviewTypeList != ''){
		$("#_reviewType").attr('checked',true);
	}
	//险种
	var busiProdCodeList = '${qualityPlanManageVO.busiProdCodeList }';
	if(busiProdCodeList != null && busiProdCodeList != ''){
		$("#_busiProdCode").attr('checked',true);
	}
	//银行网点
	var bankCodeList = '${qualityPlanManageVO.bankCodeList}';
	if(bankCodeList != null && bankCodeList != ''){
		$("#_bankCode").attr('checked',true);
	}
	//保全生效日期ValidateTime
	var validateTimeStart = '${qualityPlanManageVO.validateTimeStart}';
	if(validateTimeStart != null && validateTimeStart != ''){
		$("#_validateTime").attr('checked',true);
	}
	//保全费用类型
	var feeAmountLeast = '${qualityPlanManageVO.feeAmountLeast}';
	if(feeAmountLeast != null && feeAmountLeast != ''){
		$("#_feeAmount").attr('checked',true);
	}
	//是否签报
	var bizSignFlag = '${qualityPlanManageVO.bizSignFlag}';
	if(bizSignFlag != null && bizSignFlag != ''){
		$("#_bizSignFlag").attr('checked',true);
	}
	//延期天数
	var deferTimeLeast = '${qualityPlanManageVO.deferTimeLeast}';
	if(deferTimeLeast != null && deferTimeLeast != ''){
		$("#_deferTime").attr('checked',true);
	}
	//免填单类型
	var nofillFlag = '${qualityPlanManageVO.nofillFlag}';
	if(nofillFlag != null && nofillFlag != ''){
		$("#_nofillFlag").attr('checked',true);
	}
	//是否异地业务
	var acceptFlag = '${qualityPlanManageVO.acceptFlag}';
	if(acceptFlag != null && acceptFlag != ''){
		$("#_acceptFlag").attr('checked',true);
	}
	//人员上岗时间
	var createDateStart = '${qualityPlanManageVO.createDateStart}';
	if(createDateStart != null && createDateStart != ''){
		$("#_createDate").attr('checked',true);
	}
	//是否制盘
	var isAlreadyFlag = '${qualityPlanManageVO.isAlreadyFlag}';
	if(isAlreadyFlag != null && isAlreadyFlag != ''){
		$("#_isAlreadyFlag").attr('checked',true);
	}
	//是否做过收付费形式变更
	var isArapFlag = '${qualityPlanManageVO.isArapFlag}';
	if(isArapFlag != null && isArapFlag != ''){
		$("#_isArapFlag").attr('checked',true);
	}
	//是否预受理
	var preFlag = '${qualityPlanManageVO.preFlag}';
	if(preFlag != null && preFlag != ''){
		$("#_preFlag").attr('checked',true);
	}
	//是否常规类型
	var ruleType = '${qualityPlanManageVO.ruleType}';
	if(ruleType != null && ruleType != ''){
		$("#_ruleType").attr('checked',true);
	} */
});
//判断是否选择了相关
$("#_serviceCode").change(
	function(){
		if($("#_serviceCode").is(":checked")){
			$("#csServiceCode").show();
		}else{
			$("#csServiceCode").css("display","none");
		}
	}
);
$("#_orgCode").change(
	function(){
		if($("#_orgCode").is(":checked")){
			$("#csOrgCode").css("display","block");
			$("#csIsCluOrgCode").css("display","block");
		}else{
			$("#csOrgCode").css("display","none");
			$("#csIsCluOrgCode").css("display","none");
		}
	}
);
$("#_organCode").change(
	function(){
		if($("#_organCode").is(":checked")){
			$("#csOrganCode").css("display","block");
			$("#csIsCluOrganCode").css("display","block");
		
		}else{
			$("#csOrganCode").css("display","none");
			$("#csIsCluOrganCode").css("display","none");
			$("#csUserId").css("display","none");
		}
	}
);
$("#_busiProdCode").change(
	function(){
		if($("#_busiProdCode").is(":checked")){
			$("#csBusiProdCode").css("display","block");
		}else{
			$("#csBusiProdCode").css("display","none");
		}
	}
);
$("#_acceptFlag").change(
	function(){
		if($("#_acceptFlag").is(":checked")){
			$("#csAcceptFlag").css("display","block");
		}else{
			$("#csAcceptFlag").css("display","none");
		}
	}
);
$("#_feeAmount").change(
	function(){
		if($("#_feeAmount").is(":checked")){
			$("#csFeeAmount").css("display","block");
		}else{
			$("#csFeeAmount").css("display","none");
		}
	}
);
$("#_reviewType").change(
	function(){
		if($("#_reviewType").is(":checked")){
			$("#csReviewType").css("display","block");
		}else{
			$("#csReviewType").css("display","none");
		}
	}
);
$("#_bizSignFlag").change(
	function(){
		if($("#_bizSignFlag").is(":checked")){
			$("#csBizSignFlag").css("display","block");
		}else{
			$("#csBizSignFlag").css("display","none");
		}
	}
);
$("#_lrFlag").change(
		function(){
			if($("#_lrFlag").is(":checked")){
				$("#csLrFlag").css("display","block");
			}else{
				$("#csLrFlag").css("display","none");
			}
		}
	);
$("#_payToApplicant").change(
		function(){
			if($("#_payToApplicant").is(":checked")){
				$("#csPayToApplicant").css("display","block");
			}else{
				$("#csPayToApplicant").css("display","none");
			}
		}
	);
$("#_deferTime").change(
	function(){
		if($("#_deferTime").is(":checked")){
			$("#csDeferTime").css("display","block");
		}else{
			$("#csDeferTime").css("display","none");
		}
	}
);
$("#_ruleType").change(
	function(){
		if($("#_ruleType").is(":checked")){
			$("#csRuleType").css("display","block");
		}else{
			$("#csRuleType").css("display","none");
		}
	}
);
$("#_nofillFlag").change(
	function(){
		if($("#_nofillFlag").is(":checked")){
			$("#csNofillFlag").css("display","block");
		}else{
			$("#csNofillFlag").css("display","none");
		}
	}
);
$("#_createDate").change(
	function(){
		if($("#_createDate").is(":checked")){
			$("#csCreateDate").css("display","block");
		}else{
			$("#csCreateDate").css("display","none");
		}
	}
);
$("#_pasRank").change(
	function(){
		if($("#_pasRank").is(":checked")){
			$("#csPermissionTypeId").css("display","block");
		}else{
			$("#csPermissionTypeId").css("display","none");
		}
	}
);
$("#_validateTime").change(
	function(){
		if($("#_validateTime").is(":checked")){
			$("#csValidateTime").css("display","block");
		}else{
			$("#csValidateTime").css("display","none");
		}
	}
);
$("#_serviceType").change(
	function(){
		if($("#_serviceType").is(":checked")){
			$("#csServiceType").css("display","block");
		}else{
			$("#csServiceType").css("display","none");
		}
	}
);
$("#_isAlreadyFlag").change(
	function(){
		if($("#_isAlreadyFlag").is(":checked")){
			$("#csIsAlreadyFlag").css("display","block");
		}else{
			$("#csIsAlreadyFlag").css("display","none");
		}
	}
);
$("#_isArapFlag").change(
	function(){
		if($("#_isArapFlag").is(":checked")){
			$("#csIsArapFlag").css("display","block");
		}else{
			$("#csIsArapFlag").css("display","none");
		}
	}
);
$("#_preFlag").change(
	function(){
		if($("#_preFlag").is(":checked")){
			$("#csPreFlag").css("display","block");
		}else{
			$("#csPreFlag").css("display","none");
		}
	}
);
$("#_bankCode").change(
	function(){
		if($("#_bankCode").is(":checked")){
			$("#csBankCode").css("display","block");
		}else{
			$("#csBankCode").css("display","none");
		}
	}
);

//当选择用户管理机构时，显示用户
$("#isCluOrganCode").change(
		function(){
		var isOrganCodes = '';
		var $form = $("#savePlanManage",navTab.getCurrentPanel());
		var $isOrganCodesOptions = $form.find("#csOrganCode .rMultipleBox select option");
		$isOrganCodesOptions.each(function(){
			isOrganCodes = isOrganCodes + $(this).val()+",";
		});
		isOrganCodes = isOrganCodes.substring(0,isOrganCodes.length - 1);
		
		if(isOrganCodes == null || isOrganCodes == ''){
			alertMsg.info("必填项不完整，请输入");
			return false;
		}
		$("#csUsercs").empty();
		$form.find("input[name='qualityPlanManageVO.organCodes']").val(isOrganCodes);
		if($("#isCluOrganCode").is(":checked")){
			$.ajax({
				url : "${ctx }/cs/csQuality_qpm/findUserByOrganId_PA_qualityPlanManageAction.action",
				type : "post",
				dataType : 'json',
				data : {
					"qualityPlanManageVO.organCodes" : isOrganCodes,
					"qualityPlanManageVO.isCluOrganCode" : 1,
				},
				success : function(result){
					if(result.statusCode == 200){
						if(result.message != ""){
							$("#csUserId").css("display","block");
							var userIdl = '';
							var userIdJson = eval("("+result.message+")");
							for(var i = 0; i < userIdJson.length; i++ ){
								userIdl = userIdl +"<option value='"+userIdJson[i].userId+"'>"+ userIdJson[i].userName +"</option>";
							}
							$("#csUsercs").append(userIdl);
						}
					}
				}
			});
		}else{
			$.ajax({
				url : "${ctx }/cs/csQuality_qpm/findUserByOrganId_PA_qualityPlanManageAction.action",
				type : "post",
				dataType : 'json',
				data : {
					"qualityPlanManageVO.organCodes" : isOrganCodes,
					"qualityPlanManageVO.isCluOrganCode" : 0,
				},
				success : function(result){
					if(result.statusCode == 200){
						if(result.message != ""){
							$("#csUserId").css("display","block");
							var userIdl = '';
							var userIdJson = eval("("+result.message+")");
							for(var i = 0; i < userIdJson.length; i++ ){
								userIdl = userIdl +"<option title='"+ userIdJson[i].userName +"' value='"+userIdJson[i].userId+"'>"+ userIdJson[i].userName +"</option>";
							}							
							$("#csUsercs").append(userIdl);
						}
					}
				}
			});
		}
	}
);

function savePlanManage(){
	alertMsg.confirm("确认要保存记录吗？",{
		okCall:function(){
			var _planNo = $("#mPlanNo").val();
			if(_planNo == null || _planNo == ''){
				alertMsg.info("计划代码不能为空!");
				return false;
			}
			var _planName = $("#mPlanName").val();
			if(_planName == null || _planName == ''){
				alertMsg.info("计划名称不能为空!");
				return false; 
			}
			var mPlanId = $("#mPlanId").val();
			var _validFlag = $("#mValidFlag").val();
			if(_validFlag == 0){
				alertMsg.info("该质检计划无效，不能添加");
				return false;
			}
			var _planVersionNum = $("#mPlanVersionNum").val();
			var $form = $("#savePlanManage",navTab.getCurrentPanel());
			//var action = "${ctx }/cs/csQuality_qpm/saveQualityPlanManage_PA_qualityPlanManageAction.action";
			//$form.attr("action",action);
			//$form.attr("onsubmit","return divSearch(this, 'savePlanManage')");
			//保全项目
			var serviceCodes = '';
			if($("#_serviceCode").attr('checked')){
				var $serviceCodeOptions = $form.find("#csServiceCode .rMultipleBox select option");
				$serviceCodeOptions.each(function(){
					serviceCodes = serviceCodes + $(this).val()+",";
				});
				serviceCodes = serviceCodes.substring(0,serviceCodes.length - 1);
				if(serviceCodes == null || serviceCodes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.serviceCodes']").val(serviceCodes);
			}
			//保单管理机构
			var orgCodes = '';
			var isCluOrgCode = '';
			if($("#_orgCode").attr('checked')){
				var $orgCodeOptions = $form.find("#csOrgCode .rMultipleBox select option");
				$orgCodeOptions.each(function(){
					orgCodes = orgCodes + $(this).val()+",";
				});
				orgCodes = orgCodes.substring(0,orgCodes.length - 1);
				
				if(orgCodes == null || orgCodes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.orgCodes']").val(orgCodes);
				isCluOrgCode = $("#isCluOrgCode").val();
			}
			//用户所属机构
			var organCodes = '';
			var userIds = '';
			var isCluOrganCode = '';
			if($("#_organCode").attr('checked')){
				var $organCodeOptions = $form.find("#csOrganCode .rMultipleBox select option");
				$organCodeOptions.each(function(){
					organCodes = organCodes + $(this).val()+",";
				});
				organCodes = organCodes.substring(0,organCodes.length - 1);
				
				if(organCodes == null || organCodes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.organCodes']").val(organCodes);
				if($("#isCluOrganCode").is(":checked")){
					isCluOrganCode = 1;
				}else{
					isCluOrganCode = 0;
				}
				//用户
				var $userIdOptions = $form.find("#csUserId .rMultipleBox select option");
				$userIdOptions.each(function(){
					userIds = userIds + $(this).val()+",";
				});
				userIds = userIds.substring(0,userIds.length - 1);
				if(userIds != null && userIds != ''){
					$form.find("input[name='qualityPlanManageVO.userIds']").val(userIds);
				}
			}
			//保全权限
			var permissionTypeIds = '';
			if($("#_pasRank").attr('checked')){
				var $pasRankOptions = $form.find("#csPermissionTypeId .rMultipleBox select option");
				$pasRankOptions.each(function(){
					permissionTypeIds = permissionTypeIds + $(this).val()+",";
				});
				permissionTypeIds = permissionTypeIds.substring(0,permissionTypeIds.length - 1);
				
				if(permissionTypeIds == null || permissionTypeIds == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.permissionTypeIds']").val(permissionTypeIds);
			}
			//保全申请类型
			var serviceTypes = '';
			if($("#_serviceType").attr('checked')){
				var $serviceTypeOptions = $form.find("#csServiceType .rMultipleBox select option");
				$serviceTypeOptions.each(function(){
					serviceTypes = serviceTypes + $(this).val()+",";
				});
				serviceTypes = serviceTypes.substring(0,serviceTypes.length - 1);
				
				if(serviceTypes == null || serviceTypes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.serviceTypes']").val(serviceTypes);
			}
			//保全复核类型
			var reviewTypes = '';
			if($("#_reviewType").attr('checked')){
				var $reviewTypeOptions = $form.find("#csReviewType .rMultipleBox select option");
				$reviewTypeOptions.each(function(){
					reviewTypes = reviewTypes + $(this).val()+",";
				});
				reviewTypes = reviewTypes.substring(0,reviewTypes.length - 1);
				
				if(reviewTypes == null || reviewTypes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.reviewTypes']").val(reviewTypes);
			}
			//险种
			var busiProdCodes = '';
			if($("#_busiProdCode").attr('checked')){
				var $busiProdCodeOptions = $form.find("#csBusiProdCode .rMultipleBox select option");
				$busiProdCodeOptions.each(function(){
					busiProdCodes = busiProdCodes + $(this).val()+",";
				});
				busiProdCodes = busiProdCodes.substring(0,busiProdCodes.length - 1);
				
				if(busiProdCodes == null || busiProdCodes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.busiProdCodes']").val(busiProdCodes);
			}
			//银行网点
			var bankCodes = '';
			if($("#_bankCode").attr('checked')){
				var $bankCodeOptions = $form.find("#csBankCode .rMultipleBox select option");
				$bankCodeOptions.each(function(){
					bankCodes = bankCodes + $(this).val()+",";
				});
				bankCodes = bankCodes.substring(0,bankCodes.length - 1);
				
				if(bankCodes == null || bankCodes == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				$form.find("input[name='qualityPlanManageVO.bankCodes']").val(bankCodes);
			} 
			//保全生效日期ValidateTime
			var validateTimeStart = '';
			var validateTimeEnd = '';
			if($("#_validateTime").attr('checked')){
				validateTimeStart = $("#validateTimeStart").val();
				validateTimeEnd = $("#validateTimeEnd").val();
				if(validateTimeStart == null || validateTimeStart == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(validateTimeEnd == null || validateTimeEnd == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(validateTimeStart > validateTimeEnd){
					alertMsg.info("保全生效日期起期不能晚于保全生效日期止期");
					return false;
				}
			}
			//保全费用类型
			var feeAmountLeast = '';
			var feeAmountMost = '';
			var arapFlag = '';
			if($("#_feeAmount").attr('checked')){
				arapFlag = $("#arapFlag").val();
				feeAmountLeast = $("#feeAmountLeast").val();
				feeAmountMost = $("#feeAmountMost").val();
				if(feeAmountLeast == null || feeAmountLeast == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(feeAmountMost == null || feeAmountMost == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				
				if(arapFlag ==  "1"){
					if(feeAmountLeast<0 || feeAmountMost<0){
						alertMsg.info("收付费类型为收费，收付费金额请填写正数");
						return false;
					}				
				}
				if(arapFlag ==  "2"){
					if(feeAmountLeast>0 || feeAmountMost>0){
						alertMsg.info("收付费类型为付费，收付费金额请填写负数");
						return false;
					}				
				}
				if(Number(feeAmountLeast) > Number(feeAmountMost)){
					alertMsg.info("收付费最大金额不能小于收付费最小金额，请重新输入");
					return false;
				}
			}
			//是否签报
			var bizSignFlag = '';
			if($("#_bizSignFlag").attr('checked')){
				bizSignFlag = $("#bizSignFlag").val();
				if(bizSignFlag == null || bizSignFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//延期天数
			var deferTimeLeast = '';
			var deferTimeMost = '';
			if($("#_deferTime").attr('checked')){
				deferTimeLeast = $("#deferTimeLeast").val();
				deferTimeMost = $("#deferTimeMost").val();
				if(deferTimeLeast == null || deferTimeLeast == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(deferTimeMost == null || deferTimeMost == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(Number(deferTimeLeast) > Number(deferTimeMost)){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//免填单类型
			var nofillFlag = '';
			if($("#_nofillFlag").attr('checked')){
				nofillFlag = $("#nofillFlag").val();
				if(nofillFlag == null || nofillFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//是否异地业务
			var acceptFlag = '';
			if($("#_acceptFlag").attr('checked')){
				acceptFlag = $("#acceptFlag").val();
				if(acceptFlag == null || acceptFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//人员上岗时间
			var createDateStart = '';
			var createDateEnd = '';
			if($("#_createDate").attr('checked')){
				createDateStart = $("#createDateStart").val();
				createDateEnd = $("#createDateEnd").val();
				if(createDateStart == null || createDateStart == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(createDateEnd == null || createDateEnd == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
				if(createDateStart > createDateEnd){
					alertMsg.info("人员上岗时间起期不能晚于人员上岗时间止期");
					return false;
				}
			}
			//是否制盘
			var isAlreadyFlag = '';
			if($("#_isAlreadyFlag").attr('checked')){
				isAlreadyFlag = $("#isAlreadyFlag").val();
				if(isAlreadyFlag == null || isAlreadyFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//是否做过收付费形式变更
			var isArapFlag = '';
			if($("#_isArapFlag").attr('checked')){
				isArapFlag = $("#isArapFlag").val();
				if(isArapFlag == null || isArapFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//是否预受理
			var preFlag = '';
			if($("#_preFlag").attr('checked')){
				preFlag = $("#preFlag").val();
				if(preFlag == null || preFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//是否常规类型
			var ruleType = '';
			if($("#_ruleType").attr('checked')){
				ruleType = $("#ruleType").val();
				if(ruleType == null || ruleType == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//补发保单
			var lrFlag = '';
			if($("#_lrFlag").attr('checked')){
				lrFlag = $("#lrFlag").val();
				if(lrFlag == null || lrFlag == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			//付费账户户名为申请资格人
			var payToApplicant = '';
			if($("#_payToApplicant").attr('checked')){
				payToApplicant = $("#payToApplicant").val();
				if(payToApplicant == null || payToApplicant == ''){
					alertMsg.info("必填项不完整，请输入");
					return false;
				}
			}
			$.ajax({
				url : "${ctx }/cs/csQuality_qpm/saveQualityPlanManage_PA_qualityPlanManageAction.action",
				type : "post",
				dataType : 'json',
				data : {
					"qualityPlanManageVO.qualityPlanId":mPlanId,
					"qualityPlanManageVO.planNo" : _planNo,
					"qualityPlanManageVO.planName" : _planName,
					"qualityPlanManageVO.validFlag" : _validFlag,
					"qualityPlanManageVO.planVersionNum" : _planVersionNum,
					"qualityPlanManageVO.serviceCodes" : serviceCodes,
					"qualityPlanManageVO.orgCodes" : orgCodes,
					"qualityPlanManageVO.isCluOrgCode" : isCluOrgCode,
					"qualityPlanManageVO.organCodes" : organCodes,
					"qualityPlanManageVO.isCluOrganCode" : isCluOrganCode,
					"qualityPlanManageVO.userIds" : userIds,
					"qualityPlanManageVO.permissionTypeIds" : permissionTypeIds,
					"qualityPlanManageVO.serviceTypes" : serviceTypes,
					"qualityPlanManageVO.reviewTypes" : reviewTypes,
					"qualityPlanManageVO.busiProdCodes" : busiProdCodes,
					"qualityPlanManageVO.bankCodes" : bankCodes,
					"qualityPlanManageVO.validateTimeStart" : validateTimeStart,
					"qualityPlanManageVO.validateTimeEnd" : validateTimeEnd,
					"qualityPlanManageVO.arapFlag" : arapFlag,
					"qualityPlanManageVO.feeAmountLeast" : feeAmountLeast,
					"qualityPlanManageVO.feeAmountMost" : feeAmountMost,
					"qualityPlanManageVO.bizSignFlag" : bizSignFlag,
					"qualityPlanManageVO.deferTimeLeast" : deferTimeLeast,
					"qualityPlanManageVO.deferTimeMost" : deferTimeMost,
					"qualityPlanManageVO.nofillFlag" : nofillFlag,
					"qualityPlanManageVO.acceptFlag" : acceptFlag,
					"qualityPlanManageVO.createDateStart" : createDateStart,
					"qualityPlanManageVO.createDateEnd" : createDateEnd,
					"qualityPlanManageVO.isAlreadyFlag" : isAlreadyFlag,
					"qualityPlanManageVO.isArapFlag" : isArapFlag,
					"qualityPlanManageVO.preFlag" : preFlag,
					"qualityPlanManageVO.ruleType":ruleType,
					"qualityPlanManageVO.lrFlag" : lrFlag,
					"qualityPlanManageVO.payToApplicant":payToApplicant,
				},
				success : function(result){
					if(result.statusCode == 200){
						alertMsg.correct("保存成功！");
					}else{
						alertMsg.info(result.message);
					}
				}
			});
		}
	});
}


</script>