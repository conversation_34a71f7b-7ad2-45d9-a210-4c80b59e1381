<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">	
<form id="pagerForm" method="post" style="display:none;"
	action="${ctx }/cs/csQuality_qpm/queryQueryQualityInspection_PA_qualityPlanManageAction.action"
	onsubmit="return navTabSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	<input type="hidden" name="qualityQuaryVO.acceptCode" value="${qualityQuaryVO.acceptCode}" />
	<input type="hidden" name="qualityQuaryVO.qualityResult" value="${qualityQuaryVO.qualityResult}" />
	<input type="hidden" name="qualityQuaryVO.qualityCheckByCode" value="${qualityQuaryVO.qualityCheckByCode}" />
</form>
<div class="pageContent" layoutH="10">
<form action="${ctx }/cs/csQuality_qpm/queryQueryQualityInspection_PA_qualityPlanManageAction.action" id="queryQualityInspectionPage"
		onsubmit="return navTabSearch(this);" method="post" rel="pagerForm" class="pageForm required-validate">
	<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">查询条件
				</h1>
	 </div>
	<div class="pageContent">
			<div class="pageFormInfoContent">
				<dl>
					<dt>保全受理号</dt>
					<dd><input type="text" id="planNo" name="qualityQuaryVO.acceptCode" maxlength="30" value="${qualityQuaryVO.acceptCode }"/></dd>
				</dl>
				<dl>
					<dt>抽检结论</dt>
					<dd><Field:codeTable id="qualityResult" nullOption="true" cssClass="combox" name="qualityQuaryVO.qualityResult" tableName="APP___PAS__DBUSER.T_QUALITY_RESULT" value="${qualityQuaryVO.qualityResult }" /></dd>
				</dl>
				<dl>
					<dt>抽检人员</dt>
					<dd><input type="text" id="qualityCheckName" name="qualityQuaryVO.qualityCheckByCode" maxlength="10" value="${qualityQuaryVO.qualityCheckByCode }" /></dd>
				</dl>
				<div class="pageFormdiv">
					<button onclick="querySubmit()" class="but_blue" type="button" >查询</button>
				</div>
			</div>
	</div>
</form>
<form action="${ctx }/cs/csQuality_qpm/queryQueryQualityInspection_PA_qualityPlanManageAction.action">
	<div class="pageContent">
		<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">抽档轨迹
				</h1>
		</div>
		<div class="tabdivclass">
			<table class="list" style="width: 100%;">
				<thead>
					<tr>
						<!-- <th>序号</th> -->
						<th>质检计划号</th>
						<th>质检计划名称</th>
						<th>保单受理号</th>
						<!-- <th>质检计划建立人</th> -->
						<th>抽检日期</th>
						<th>抽检人员</th>
						<th>抽检结论</th>
						<!-- <th>系数值</th> -->
						<!-- <th>不合格原因</th> -->
						<th>抽检备注</th>
						<th>质检问题类型</th>
						<th>二次质检日期</th>
						<th>二次质检人员</th>
						<th>二次质检结论</th>
						<th>二次质检备注</th>
						<th>整改日期</th>
						<th>整改人员</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="currentPage.pageItems" id="" status="st">
						<tr>
							<%-- <td><input type="hidden" name="qualityQuaryVO.qualityPlanId" value='${qualityPlanId}'/>
							<s:property value="#st.index+1" /></td> --%>
							<td>${qualityPlanCode }</td>
							<td>${qualityPlanName }</td>
							<td>${acceptCode }</td>
							<%-- <td>${makerName }</td> --%>
							<td><s:date name="qualityCheckDate" format="yyyy-MM-dd"/></td>
							<td>${qualityCheckName }</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT" value="${qualityResult }"/></td>
							<%-- <td>${qualityRate }</td>
							<td>${errorReasonName }</td> --%>
							<td>${qualityRemark }</td>
							<td>${questionType }</td>
							<td><s:date name="recheckDate" format="yyyy-MM-dd"/></td>
							<td><s:property value="recheckByName" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT" value="${recheckResult }"/></td>
							<td>${recheckRemark }</td>
							<td><s:date name="qualityModifyDate" format="yyyy-MM-dd"/></td>
							<td><s:property value="qualityModifyByName" /></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			 <div class="panelBar" >
		        <div class="pages">
		            <span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
							onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
						</s:select> <span>条，共${currentPage.total}条</span>        
				</div>
		        <div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
		        	pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
			</div>
		</div>
	</div>
</form>
</div>
<script type="text/javascript">
	function querySubmit() {
		var $form = $("#queryQualityInspectionPage",navTab.getCurrentPanel()); 
		$form.submit();
	}
</script>