<!-- 质检公共任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<form id="pagerForm" onsubmit="return divSearch(this, 'showQuqlityModifyList');" method="post" 
	 action="${ctx }/cs/csQuality/queryQualityInspectionPageList_PA_csQualityErrorScoreAction.action" >
 	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
	 <input type="hidden" name="numPerPage" value="${numPerPage}" /> 
	 <input type="hidden" id="applyCode" name="qualityInspectionInfoVO.applyCode" value="${qualityInspectionInfoVO.applyCode }" /> 
	 <input type="hidden" id="policyCode" name="qualityInspectionInfoVO.policyCode" value="${qualityInspectionInfoVO.policyCode }" /> 
	 <input type="hidden" id="orangeName" name="qualityInspectionInfoVO.orangeName" value="${qualityInspectionInfoVO.orangeName }" /> 
	 <input type="hidden" id="serviceCode" name="qualityInspectionInfoVO.serviceCode" value="${qualityInspectionInfoVO.serviceCode }" /> 
	 <input type="hidden" id="qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName" value="${qualityInspectionInfoVO.qualityPlanName }" /> 
	 <input type="hidden" id="qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode" value="${qualityInspectionInfoVO.qualityPlanCode }" /> 
	 <input type="hidden" id="qualityOrigin" name="qualityInspectionInfoVO.qualityOrigin" value="${qualityInspectionInfoVO.qualityOrigin }" /> 
	 <input type="hidden" id="makerName" name="qualityInspectionInfoVO.makerName" value="${qualityInspectionInfoVO.makerName }" /> 
</form>
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">共享任务池
	</h1>
 </div>
<div class="tabdivclass" > 
		<table class="list" id="appDoctable11" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>					
					<th>选择</th>
					<th>序号</th>
					<th>保全受理号</th>
					<th>保单号</th>
					<th>保单管理机构</th>
					<th>保全项目</th>
					<th>质检计划代码</th>
					<th>质检计划名称</th>
					<th>抽检来源</th>
					<th>质检计划建立人</th>
					<th>质检件状态</th>
					<th style="display: none">taskId</th>
				</tr>
			</thead>
			<tbody align="center" id="tbody">
				  <s:iterator value="currentPage.pageItems" status="st" id="qr6">
				  <input type="hidden" value="" />
					<tr>
						<td><input type="radio" name="qualityInspectionInfoVO.qualityId" value='${qualityId}'/></td>
						<td><s:property value="#st.index+1" /></td>
						<td><s:property value="acceptCode" /></td>
						<td><s:property value="policyCode" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${organCode}"/></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
						<td><s:property value="qualityPlanCode" /></td>
						<td><s:property value="qualityPlanName" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_ORIGIN"  value="${qualityOrigin }" />
						<td><s:property value="makerName"/></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_STATUS"  value="${qualityStatus }" />
						<td style="display: none">${taskId}</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	 <div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select cssClass="" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
				name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'showQuqlityModifyList')">
			</s:select>
			<span>条，共${currentPage.total}条</span>
		</div>
		<div class="pagination" rel="showQuqlityModifyList" totalCount="${currentPage.total}" currentPage="${currentPage.pageNo}"
	     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
	</div> 
	 <div class="pageFormdiv">
		<button  type="button" class="but_blue" onclick="qualityModify()">质检处理</button>
     </div>  
	</div> 
<script type="text/javascript" charset="utf-8" language="javascript" >
$(function(){
	$("#tbody", navTab.getCurrentPanel()).find("tr").each(function(){			
		var reg = new RegExp(",","g");//替换
		var _policyCode=$(this).find("td").eq(3).text();
		//console.info(_policyCode);
		_policyCode=_policyCode.replace(reg,"<br/>");
		//console.info(_policyCode);
		$(this).find("td").eq(3).html(_policyCode);		
	});	
});
/**
 * 质检修改
 *
 */
function qualityModify(){
	var $obj1 = $("#tbody", navTab.getCurrentPanel());
	var radio = $($obj1).find("input:radio:checked");
	//parent().parent().children()
	var tdNodes = $(radio).parent().parent().children();
	var policyCode = tdNodes.eq(3).text();//保单号
	var organCode = tdNodes.eq(4).text();//机构
	var qualityPlanCode =tdNodes.eq(6).text();//质检计划代码
	var taskId =tdNodes.eq(11).text();
	//zhulh 拼接参开始
	var qualityPlanCodes=$("#step1_qualityPlanCode",navTab.getCurrentPanel()).val();
	var policyCodes=$("#step1_policyCode",navTab.getCurrentPanel()).val();
	var organCodes=$("#step1_organCode",navTab.getCurrentPanel()).val();
	var serviceCodes=$("#step1_serviceCode",navTab.getCurrentPanel()).val();
	var qualityPlanCodes=$("#step1_qualityPlanCode",navTab.getCurrentPanel()).val();
	var qualityPlanNames=$("#step1_qualityPlanName",navTab.getCurrentPanel()).val();
	var qualityOrigins=$("#step1_qualityOrigin",navTab.getCurrentPanel()).val();
	var acceptCodes=$("#step1_acceptCode",navTab.getCurrentPanel()).val();
	
	if(radio.length == 0){s
		 alertMsg.info("请选择抽检项目");
		 return false;
	} 
	
	
	var title = "质检事后抽检处理";	
	var fresh = eval("true");
	var external = eval("false"); 
    var tabid="质检事后抽检处理";
   	var url ="${ctx }/cs/csQuality/qualityModifyPage_PA_csQualityErrorScoreAction.action?qualityInspectionInfoVO.qualityId="
   			+radio[0].value+"&qualityInspectionInfoVO.qualityPlanCode="+qualityPlanCode+"&taskId="+taskId+"&policyCode="+policyCode+"&organCode="+encodeURI(organCode)
		+"&serviceCodes="+serviceCodes+"&qualityOrigins="+qualityOrigins+"&policyCodes="+policyCodes
		+"&qualityPlanCodes="+qualityPlanCodes+"&organCodes="+organCodes+"&qualityPlanNames="+encodeURI(encodeURI(qualityPlanNames))+"&acceptCodes="+acceptCodes;
   		;
   	navTab.closeCurrentTab();//关闭当前页签
   	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	 
}

</script>