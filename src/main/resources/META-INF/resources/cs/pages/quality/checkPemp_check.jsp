<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<form action="${ctx }/cs/csCheckPemp/saveCheckPemp_PA_checkPempAction.action" method="post" id="saveCheckSub">
	<input type="hidden" id="_planId" name="checkPempVO.qualityPlanId" value="${checkPempVO.qualityPlanId }"  />
	<input type="hidden" id="_planCode" name="checkPempVO.qualityPlanCode" value="${checkPempVO.qualityPlanCode }"  />
	<input type="hidden" id="_planName" name="checkPempVO.qualityPlanName" value="${checkPempVO.qualityPlanName }"  />
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">抽档结果
		</h1>
   </div>
	<div class="panelPageFormContent">
			<table>
				<tr>
					<td>
						<dl>
							<dt>抽档件数</dt>
							<dd><input type="text" id="checkTotal" name="checkPempVO.checkTotal" maxlength="10" readonly="readonly" 
							           value="${checkPempVO.checkTotal }" />
							</dd>
						</dl>
					</td>
				</tr>
				<tr>
					<td>
						<dl>
							<dt><input type="radio" name="checkThis" id="_checkScale" style="position:relative;top:2px;"/>&nbsp;抽档比例</dt>
							<dd><input onkeyup="value=value.replace(/[^\d]/g,'')" 
									   onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))"
									   type="text" id="checkScale" name="checkPempVO.checkScale" maxlength="10" readonly="readonly" 
									   value="${checkPempVO.checkScale }" />&nbsp;%
							</dd>
						</dl>
					</td>
				</tr>
				<tr>
					<td>
						<dl>
							<dt><input type="radio" name="checkThis" id="_checkCount" style="position:relative;top:2px;"/>&nbsp;抽档数量</dt>
							<dd><input onkeyup="value=value.replace(/[^\d]/g,'')"
									   onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))"
									   type="text" id="checkCount" name="checkPempVO.checkCount" maxlength="10" readonly="readonly" 
									   value="${checkPempVO.checkCount }" />
							</dd>
						</dl>
					</td>
				</tr>
			</table>
		</div>
	<div class="pageFormdiv">
			<button onclick="saveSubmit()" class="but_blue" type="button">提交</button>
			<button onclick="backCheck()" class="but_blue" type="button">返回抽档</button>
			<button onclick="backCheck()" class="but_gray" type="button">退出</button>
	</div>
</form>

<script type="text/javascript">
	//点击返回抽档
	function backCheck(){
		$("#saveCheckSub").remove();
	}
	//点击提交
	function saveSubmit(){
		var checkTotal = $("#checkTotal").val();
		var checkScale = $("#checkScale").val();
		var checkCount = $("#checkCount").val();
		if(checkTotal == 0){
			alertMsg.info("没有可以抽档的信息!");
			return false;
		}
		if((checkScale == null || checkScale == '') && (checkCount == null || checkCount=='')){
			alertMsg.info("抽档比例和抽档数量，请至少选择一项!");
			return false;
		}
		var _planId = $("#_planId").val();
		var _planCode = $("#_planCode").val();
		var _planName = $("#_planName").val();
		$.ajax({
			url : "${ctx }/cs/csCheckPemp/saveCheckPemp_PA_checkPempAction.action",
			type : "post",
			dataType : 'json',
			data : {
				"checkPempVO.checkTotal" : checkTotal,
				"checkPempVO.checkScale" : checkScale,
				"checkPempVO.checkCount" : checkCount,
				"checkPempVO.qualityPlanId" : _planId,
				"checkPempVO.qualityPlanCode" : _planCode,
				"checkPempVO.qualityPlanName" : _planName,
			},
			success : function(result){
				if(result.statusCode == 200){
					alertMsg.correct("保存成功！");
				}else{
					alertMsg.info(result.message);
				}
			}
		});
	}
	//判断单选框
	$("#_checkScale").change(function(){
		if($("#_checkScale").is(":checked")){
			$("#checkScale").attr("readonly",false);
			$("#checkCount").attr("readonly",true);
			$("#checkCount").val("");
		}
	});
	$("#_checkCount").change(function(){
		if($("#_checkCount").is(":checked")){
			$("#checkScale").attr("readonly",true);
			$("#checkCount").attr("readonly",false);
			$("#checkScale").val("");
		}
	});
	function getId(obj){
		var id = $(obj).attr("value");
		ids = id;
	}
	
</script>