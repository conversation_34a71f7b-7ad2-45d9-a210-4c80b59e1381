<!-- 质检公共任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>

<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">共享任务池
	</h1>
 </div>
<div class="tabdivclass" > 
	<form id="pagerForm" name="comm" onsubmit="return divSearch(this, 'showQuqlityModifyList');" method="post" 
		 action="${ctx }/cs/csQuality/queryQualityInspectionPageList_PA_csQualityErrorScoreAction.action" >
	 	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
		 <input type="hidden" name="numPerPage" value="${numPerPage}" /> 
		 <input type="hidden" id="p_qualityByOrg" name="qualityInspectionInfoVO.qualityByOrg" value="${qualityInspectionInfoVO.qualityByOrg }" /> 
		 <input type="hidden" id="p_organFlag" name="qualityInspectionInfoVO.organFlag" value="${qualityInspectionInfoVO.organFlag }" /> 
		 <input type="hidden" id="p_acceptCode" name="qualityInspectionInfoVO.acceptCode" value="${qualityInspectionInfoVO.acceptCode }" /> 
		 <input type="hidden" id="p_policyCode" name="qualityInspectionInfoVO.policyCode" value="${qualityInspectionInfoVO.policyCode }" /> 
		 <input type="hidden" id="p_organCode" name="qualityInspectionInfoVO.policyOrganCode" value="${qualityInspectionInfoVO.policyOrganCode }" /> 
		 <input type="hidden" id="p_qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode" value="${qualityInspectionInfoVO.qualityPlanCode }" /> 
		 <input type="hidden" id="p_qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName" value="${qualityInspectionInfoVO.qualityPlanName }" /> 
		 <input type="hidden" id="p_makerName" name="qualityInspectionInfoVO.makerName" value="${qualityInspectionInfoVO.makerName }" /> 
		 <input type="hidden" id="p_serviceCode" name="qualityInspectionInfoVO.serviceCode" value="${qualityInspectionInfoVO.serviceCode }" /> 
		 <input type="hidden" id="p_modifyFlag" name="qualityInspectionInfoVO.modifyFlag" value="${qualityInspectionInfoVO.modifyFlag }" /> 
		 <input type="hidden" id="p_taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="1" /> 
	</form>
		<input type="hidden" id="currentUserId" name="currentUserId" value="${currentUserId }" /> 
		<table class="list" id="commList" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>					
					<th><input type="checkbox" style="position:relative;top:2px;right:5px;" class="checkboxCtrl" group="qualityIds"/>选择</th>
					<th>质检机构</th>
					<th>保全受理号</th>
					<th>保单号</th>
					<th>保单管理机构</th>
					<th>保全项目</th>
					<th>申请方式</th>
					<th>补退费金额</th>
					<th>申请提交日期</th>
					<th>受理人员</th>
					<th>复核日期</th>
					<th>复核人员</th>
					<th>复核用户片区</th>
					<th>质检计划代码</th>
					<th>质检计划名称</th>
					<th>创建人</th>
					<th>创建时间</th>
					<th>截止日期</th>
					<th>质检件状态</th>
					<th style="display: none">录入人Code</th>
					<th style="display: none">复核人Code</th>
					<th style="display: none">录入人id</th>
					<th style="display: none">复核人id</th>
				</tr>
			</thead>
			<tbody align="center" id="tbodyComm">
				  <s:iterator value="currentPage.pageItems" status="st" id="qr6">
				  <input type="hidden" value="" />
					<tr>
						<td><input type="checkbox" name="qualityIds" class="qualityIds" value='${qualityId}'/></td>
						<td><s:property value="qualityByOrg" /></td>
						<td id="commtr_acceptCode"><s:property value="acceptCode" /></td>
						<td><s:property value="policyCode" /></td>
						<%-- <td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${organCode}"/></td> --%>
						<td id="policyOrganCode"><s:property value="policyOrganCode" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE"  value="${serviceType }" /></td>
						<td>${feeAmount }</td>
						<td><s:date name="applyTime" format="yyyy-MM-dd"/></td>
						<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${acceptUser.userName}(${acceptUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${acceptUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${acceptUser.phone},办公电话:${acceptUser.telPhone},邮箱:${acceptUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${acceptUser.userId}" />
								</dt></a></td>
						<td><s:date name="reviewTime" format="yyyy-MM-dd"/></td>
						<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${reviewUser.userName}(${reviewUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${reviewUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${reviewUser.phone},办公电话:${reviewUser.telPhone},邮箱:${reviewUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${reviewUser.userId}" />
								</dt></a></td>
						<td>${areaName }</td>
						<td><s:property value="qualityPlanCode" /></td>
						<td><s:property value="qualityPlanName" /></td>
						<td><s:property value="makerName"/></td>
						<td><s:date name="makeTime" format="yyyy-MM-dd"/></td>
						<td><s:if  test="exceedPlanEndDate==1"><font color="red"></s:if><s:date name="qualityEndDate" format="yyyy-MM-dd"/></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_STATUS"  value="${qualityStatus }" />
						<td style="display: none"><input type="hidden" id="insertOperatorCode" name="insertOperatorCode" value="${insertOperatorCode }" /> </td>
						<td style="display: none"><input type="hidden" id="reviewUserCode" name="reviewUserCode" value="${reviewUser.userName }" /> </td>
						<td style="display: none"><input type="hidden" id="insertOperatorId" name="insertOperatorId" value="${insertOperatorId }" /> </td>
						<td style="display: none"><input type="hidden" id="reviewUserId" name="reviewUserId" value="${reviewUser.userId }" /> </td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	 <div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<s:select cssClass="" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
				name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'showQuqlityModifyList')">
			</s:select>
			<span>条，共${currentPage.total}条</span>
		</div>
		<div class="pagination" rel="showQuqlityModifyList" totalCount="${currentPage.total}" currentPage="${currentPage.pageNo}"
	     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
	</div> 
	 <div class="pageFormdiv">
		<button  type="button" class="but_blue" onclick="applyInspectTask()">申请任务</button>
     </div>  
</div>

<script type="text/javascript" charset="utf-8" language="javascript" >
$(function(){
	$("#tbodyComm", navTab.getCurrentPanel()).find("tr").each(function(){			
		var reg = new RegExp(",","g");//替换
		var _policyCode=$(this).find("td").eq(3).text();
		//console.info(_policyCode);
		_policyCode=_policyCode.replace(reg,"<br/>");
		//console.info(_policyCode);
		$(this).find("td").eq(3).html(_policyCode);		
		var reg2 = new RegExp("、","g");//替换
		var policyOrganCode=$(this).find("td#policyOrganCode").text();
		policyOrganCode=policyOrganCode.replace(reg2,"<br/>");
		$(this).find("td#policyOrganCode").html(policyOrganCode);
	});	
});
function applyInspectTask(){
	var qualityinput = $("#commList").find("input[name='qualityIds']:checked");
	var qualityIdStr = "";
	var currentUserId = $("#currentUserId").val();
	var isSame = false;
	qualityinput.each(function (){
		qualityIdStr += $(this).val()+",";
		var insertOperatorId = $(this).closest("tr").find("input#insertOperatorId").val();
		var reviewUserId = $(this).closest("tr").find("input#reviewUserId").val();
		//alert(currentUserId +'--'+insertOperatorID+'--'+reviewUserId);
		if(currentUserId == insertOperatorId || currentUserId == reviewUserId){
			isSame = true;
		}
	});
	qualityIdStr = qualityIdStr.substring(0, qualityIdStr.length-1)
	if(qualityIdStr == null || qualityIdStr == ''){
		alertMsg.error("请选择质检任务！");
		return;
	}
	
	if(isSame){
		alertMsg.error("质检人与录入人、质检人与复核人不可为同一人。");
		return;
	}
	
	$.ajax({
        type:'POST',
        url:'${ctx}/cs/csAccept/applyInspectTask_PA_csQualityErrorScoreAction.action',
        data : {
        	 "qualityIdStr" : qualityIdStr,
        	 "currentUserId": currentUserId
        },
        dataType:"json",
        async : false,
		cache : false,
        success:function(data){
        	alertMsg.info("处理成功");
        	var $obj = $("#pagerForm[name='comm']", navTab.getCurrentPanel());
        	$obj.submit();
        	var $obj2 = $("#pagerForm[name='person']", navTab.getCurrentPanel());
        	$obj2.submit();
        }
    });
}

</script>