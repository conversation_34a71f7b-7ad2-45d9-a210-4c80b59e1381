<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<div class="panel">
	<h1>查询结果</h1>
	<div class="pageFormContent">
		<%-- <form id="pagerForm" onsubmit="return divSearch(this, 'getCsTaskDIV');" action="${ctx}/cs/csTask/queryCsAllResult_PA_csEntryTaskPoolAction.action" method="post">
			<input type="hidden" name="pageNum" value="${pageNum}" /> 
			<input type="hidden" name="numPerPage" value="${numPerPage}" /> 
			<input type="hidden" name="csTaskMsgVO.applyCode" value="${csTaskMsgVO.applyCode}"/>
			<input type="hidden" name="csTaskMsgVO.acceptCode" value="${csTaskMsgVO.acceptCode}" />
			<input type="hidden" name="csTaskMsgVO.policyCode" value="${csTaskMsgVO.policyCode}" />
			<input type="hidden" name="csTaskMsgVO.serviceCode" value="${csTaskMsgVO.serviceCode}" />
			<input type="hidden" name="csTaskMsgVO.customerID" value="${csTaskMsgVO.customerID}" />
			<input type="hidden" name="csTaskMsgVO.appStatus" value="${csTaskMsgVO.appStatus}" />
			<input type="hidden" name="csTaskMsgVO.rollbackPoint" value="${csTaskMsgVO.rollbackPoint}" />
			<input type="hidden" name="csTaskMsgVO.applyTime"  value="${csTaskMsgVO.applyTime}" />
		</form> --%> 
	 <form id="messageForm" onsubmit="return divSearch(this, 'queryShareTaskDiv');" method="post"> 
	 <input type="hidden" name="qualityProjectVO" id="qualityProjectVO"/>
		<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
			<table class="list" id="appDoctable" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
					
						<th>选择</th>
						<th>差错代码</th>
						<th>差错名称</th>
						<th>扣分标准</th>
						<th>创建人</th>
						<th>创建时间</th>
						<th>是否有效</th>
						<th>版本号</th>
					</tr>
				</thead>
				<tbody align="center">
					 <s:iterator value="currentPage.pageItems" status="st" id="qr">
						<tr>
							<td><input type="radio" name="qualityProjectVO.qualityPrjId" value='${qualityPrjId}'/></td>
							<td><s:property value="projectNo" /></td>
							<td><s:property value="projectName" /></td>
							<td ><s:property value="accountScore" /></td>
							<td><s:property value="userName" /></td>
							<td><s:date name="insertTime" format="yyyy-MM-dd"/></td>
							<td>${validFlag eq 1 ?'有效':'无效' }</td> 
							<td><s:property value="version" /></td>
						</tr>
					</s:iterator> 
				</tbody>
			</table>
		</div> 
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select cssClass="combox" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
					name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'getCsTaskDIV')">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			
			<div class="pagination" rel="getCsTaskDIV" totalCount="${currentPage.total}"currentPage="${currentPage.pageNo}"
		     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
		</div>
  		
		</form> 
	</div>
	<div class="panelBar">
		<table style="width:100%">
			<tr>
				<td></td>
				<td style="width:200px">
					<div class="button">
						<div class="buttonContent">
							<button  type="button" onclick="changeErrorInfo(this)">变更差错信息</button>
						</div>
					</div>
				</td>
				
				<td style="width:200px">
					<div class="button">
						<div class="buttonContent">
							<button  type="button" onclick="copyErrorInfo(this)">复制差错信息</button>
						</div>
					</div>
				</td><td></td>
			</tr>
		</table>
	</div>
</div>

<div id="showMainBaseInfo">
</div>
<script type="text/javascript">

//变更差错信息
function changeErrorInfo(obj){
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	if(radio.length == 0){
		alertMsg.info("请选择计划");
		return false;
	}else{
		//alert("radio="+radio[0].value);
		console.log("radio="+radio+" changdu"+radio.length);
		var url ="${ctx }/cs/csQuality/changMianBaseInfo_PA_csQualityErrorControlAction.action?qualityPrjId="+radio[0].value;
		$obj1.attr('action', url);
		$obj1.attr("onsubmit","return divSearch(this, 'showMainBaseInfo')");
		$obj1.submit();
	}
}

//复制差错信息
function copyErrorInfo(obj){
	alert("复制差错");
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	if(radio.length == 0){
		alertMsg.info("请选择计划");
		return false;
	}else{
		console.log("radio="+radio+" changdu"+radio.length);
		var url ="${ctx }/cs/csQuality/cpoyProjectInfo_PA_csQualityErrorControlAction.action?qualityPrjId="+radio[0].value;
		$obj1.attr('action', url);
		$obj1.attr("onsubmit","return divSearch(this, 'showMainBaseInfo')");
		$obj1.submit();
	}
}

</script>