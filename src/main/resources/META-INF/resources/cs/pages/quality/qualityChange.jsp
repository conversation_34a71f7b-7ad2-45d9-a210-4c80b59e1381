<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<form id="showQualityLogChange" onsubmit="return divSearch(this, 'showQualityLogLogsDetailChange');" method="post"> 
<input type="hidden" id="qualityId" name="qualityInspectionInfoVO.qualityId" value="${qualityInspectionInfoVO.qualityId }"/>
</form>
<input type="hidden" id="taskId2" name="taskId" value="${taskId }"/>
<input type="hidden" id="qualityId2" name="qualityInspectionInfoVO.qualityId" value="${qualityInspectionInfoVO.qualityId }"/>
<input type="hidden" id="policyCode2" value="${policyCode }"/>
<input type="hidden" id="applyCode2" value="${qualityInspectionInfoVO.applyCode }"/>
<input type="hidden" id="acceptCode2" value="${qualityInspectionInfoVO.acceptCode}"/>
<input type="hidden" id="serviceCode2" value="${qualityInspectionInfoVO.serviceCode }"/>
<input type="hidden" id="changeId2" value="${csAcceptChangeVO.changeId }"/>
<input type="hidden" id="acceptId2" value="${csAcceptChangeVO.acceptId }"/>
<input type="hidden" id="customerId2" value="${csApplicationVO.customerId }"/>
<input type="hidden" id="qualityPlanCode2" value="${qualityInspectionInfoVO.qualityPlanCode }"/>
<input type="hidden" id="callBackFunctionChange" value="${callBackFunctionChange }"/>
<div class="pageContent" layoutH="10">
	 <form id="messageForm1" onsubmit="return divSearch(this, 'messageForm1');" method="post"> 
	 	<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">保全件信息
			</h1>
		 </div>
	 	<div class="panelPageFormContent">
			<dl>
				<dt>保全受理号</dt>
				<dd><input id="mian_acceptCode" type="text" maxlength="20"  value="${qualityInspectionInfoVO.acceptCode }" disabled="disabled"/></dd>
			</dl>
			<dl>
				<dt>保单号</dt>
				<dd><input type="text" title="${policyCode }" value="${policyCode }" maxlength="20" disabled="disabled"/></dd>
			</dl>
			<dl>
				<dt>保单管理机构</dt>
				<dd><input type="text" title="${organCode }" value="${organCode }" disabled="disabled">
				</dd>
			</dl>	
		</div>
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">保全具体信息
			</h1>
		 </div>
		<div class="tabdivclass"> 
			<table class="list" id="appDoctable1" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>保全受理号</th>
						<th>保全项目</th>
						<th>补退费金额</th>
						<th>延期天数</th>
						<th>申请方式</th>
						<th>受理人员</th>
						<th>录入人员</th>
						<th>复核人员</th>
						<th>申请确认日期</th>
						<th>保全生效日期</th>
					</tr>
				</thead>
				<tbody align="center">
					<s:iterator value="qualityInspectionInfoVO" status="st" id="qr1">
						<tr>
							<td><s:property value="acceptCode" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
							<td><s:property value="feeAmount" /></td>
							<td><s:property value="delayDays" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE" value="${serviceType }"/></td>
							<td><s:property value="acceptByName" /></td>
							<td><s:property value="insertOperatorName" /></td>
							<td><s:property value="reviewName" /></td>
							<td><s:date name="acceptTime" format="yyyy-MM-dd"/></td>
							<td><s:date name="validateTime" format="yyyy-MM-dd"/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div> 
   <div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />代办人信息
		</h1>
	</div>
	<div class="tabdivclass">
		<table width="100%" align="center" class="list">
			<thead align="center">
				<tr>
					<th>代办人姓名</th>
					<th>代办人证件类型</th>
					<th>代办人证件号码</th>
					<th>证件有效期起期</th>
					<th>证件有效期止期</th>
					<th>代办人联系电话</th>
					<th>业务员代码</th>
					<th>新业务员代码</th>
					<th>绩优等级</th>
				</tr>
			</thead>
			<tbody align="center" id="tbodys">
				<tr>
					<td><s:property value="csApplicationVO.agentName" /></td>
					<td><Field:codeValue
							tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
							value="${csApplicationVO.agentCertiType}" /></td>
					<td><s:property value="csApplicationVO.agentCertiCode" />
					<td><input nullOption="true" style="border-style: none;" readonly="readonly" value="<s:date format="yyyy-MM-dd" name="csApplicationVO.agentCertStarDate"/>" /></td>
					<td><input id="agentCertEndDate" nullOption="true" style="border-style: none;" readonly="readonly" value="<s:date format="yyyy-MM-dd" name="csApplicationVO.agentCertEndDate"/>" /></td>
					<td><s:property value="csApplicationVO.agentTel" /></td>
					<td><s:property value="csApplicationVO.agentCode" /></td>
					<td><s:property value="csApplicationVO.agentCodeNew" /></td>
					<td><Field:codeValue tableName="APP___PAS__DBUSER.T_AGENT_LEVEL"
						value="${csApplicationVO.agentLevel}" />
					</td>
				</tr>
			</tbody>
		</table>
	</div> 
	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png">标识信息
		</h1>
	</div>
	<div class="pageContent">
		<s:include value="/cs/pages/csCheck/rewiew_reviewFlag.jsp"></s:include>
	</div>
	<!-- 补退费信息加载 add by panmd_wb -->
	<s:if test="csArapVOs.size!=0">
		<div id="checkPremInfo">
			<s:include value="/cs/pages/csCheck/check_policyPayMode.jsp"></s:include>
		</div>
	</s:if>
	<!-- 保全信息录入 回访电话录入  start-->
	<div id="returnVisitTelCheckDiv">
		<s:include value="/cs/pages/csCheck/check_returnVisitTel.jsp"></s:include>
	</div>
	<!-- 保全信息录入 回访电话录入  end-->
 	
	<div class="divfclass">
		<h1>
			<a href="#" id="showButtonId" value='+'
				onclick="showButtonQualityLogDiv(this,'showQualityLogLogsDetailChange')">
				<img id="imgPlusQualityLog"  src="${ctx}/cs/img/icon/three_plus.png" />
				<img id="imgMinusQualityLog" style="display: none;" src="${ctx}/cs/img/icon/three_minus.png" />抽检轨迹</a>
		</h1>
	</div>
  	<div class="tabdivclass" id="showQualityLogLogsDetailChange" style="display: none;"> 
		<table class="list" id="appDoctable2" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>
					<th>抽检日期</th>
					<th>抽检人员</th>
					<th>抽检结论</th>
					<th>抽检意见</th>
					<th>抽检备注</th>
					<th>质检问题类型</th>
					<th>二次质检日期</th>
					<th>二次质检人员</th>
					<th>二次质检结论</th>
					<th>二次质检备注</th>
					<th>整改日期</th>
					<th>整改人员</th>
				</tr>
			</thead>
				<tbody align="center">
						<s:iterator value="qualityInspectionLogListVO" status="st" id="qr1">
							<tr id="<s:property value='inspectionId' />">
								<td><s:date name="qualityCheckDate" format="yyyy-MM-dd"/></td>
								<td><s:property value="qualityCheckName" /></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT"  value="${qualityResult }" /></td>
								<td><s:property value="qualitySurgest" /></td>
								<td><s:property value="qualityRemark" /></td>
								<td><s:property value="questionType" /></td>
								<td><s:date name="recheckDate" format="yyyy-MM-dd"/></td>
								<td><s:property value="recheckByName" /></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT" value="${recheckResult }"/></td>
								<td>${recheckRemark }</td>
								<td><s:date name="qualityModifyDate" format="yyyy-MM-dd"/></td>
								<td><s:property value="qualityModifyByName" /></td>
							</tr>
						</s:iterator>
				</tbody>
			</div>
		</table>
	</div> 
	<div class="pageFormdiv">
		<form id="saveQualityChangeForm" method="post" >
			<div class="panelPageFormContent">
				<dl>
					<dt>整改方案：</dt>
					<dd>
						<Field:codeTable  cssClass="combox" nullOption="true" id="modifyMode" name="qualityInspectionInfoVO.modifyMode" 
							tableName="APP___PAS__DBUSER.T_CS_MODIFY_MODE" onChange="changeModifyMode(this)" value="${qualityInspectionInfoVO.modifyMode }" ></Field:codeTable>
					</dd>
				</dl>
			</div>
			<div class="pageFormdiv" id="uploadImageDiv" style="text-align: left;display: none;">
			 	<dl >
					<dt>
	              		<button style="margin-left:166px;" type="button" class="but_blue" onclick="qualityChange_scansion()" >本地影像上传</button>
	              	</dt>
	            </dl>
			</div>
			<br/>
			<div class="panelPageFormContent" id="rollbackAcceptCodeDiv" style="display: none;">
				<dl>
					<dt>保全回退受理号：</dt>
					<dd>
						<input id="rollbackAcceptCode" type="text" maxlength="20" name="qualityInspectionInfoVO.rollbackAcceptCode" value="${qualityInspectionInfoVO.rollbackAcceptCode }" />
					</dd>
				</dl>
			</div>
			<div class="panelPageFormContent" id="modifyRemarkDiv" style="height: 70px;display: none;">
				<dl>
					<dt >整改说明</dt>
					<dd >
						<textarea style="width: 200px" name="qualityInspectionInfoVO.modifyRemark"
							rows="4" cols="80" id="modifyRemark">${qualityInspectionInfoVO.modifyRemark }</textarea>
					</dd>
				</dl>
			</div>
			<div class="pageFormdiv" id="saveMesId" style="text-align: left;">
			 	<dl >
					<dt>
	              		<button style="margin-left:166px;" type="button" class="but_blue" onclick="saveQualityChangeForm()" >提交</button>
	              	</dt>
	            </dl>
			</div>
		</form>
	</div>
  </div>
<script type="text/javascript">
var accpectRate = new Array();
var inputRate = new Array();
var reviewRate = new Array();
var reviewErrInfoList = new Array();
var applyCode = $("#applyCode2").val();
var acceptCode = $("#acceptCode2").val();
var serviceCode = $("#serviceCode2").val();
var policyCode = $("#policyCode2").val();
var customerId = $("#customerId2").val();
var changeId = $("#changeId2").val();
var acceptId = $("#acceptId2").val();
var callBackFunctionChange = $("#callBackFunctionChange").val();
/* var changeId = 12;
var acceptId = 3; */
$(function(){
	var agentCertEndDate = $("#agentCertEndDate", navTab.getCurrentPanel()).val();//长期是否
 	if(agentCertEndDate == '9999-12-31'){
 		$("#agentCertEndDate", navTab.getCurrentPanel()).val("长期");
 	}
	var modifyModeObj = $("#saveQualityChangeForm").find("#modifyMode");
	changeModifyMode(modifyModeObj);
});
function saveQualityChangeForm(){
	var qualityId = $("#qualityId2").val();
	var modifyMode = $("#saveQualityChangeForm").find("#modifyMode").val();
	var rollbackAcceptCode = $("#saveQualityChangeForm").find("#rollbackAcceptCode").val();
	var modifyRemark = $("#saveQualityChangeForm").find("#modifyRemark").text();
	if(modifyMode==null || modifyMode==''){
		alertMsg.error("请录入整改方案！");
		return;
	}
	if(modifyRemark!=null && modifyRemark!='' && modifyRemark.length > 500){
		alertMsg.error("整改说明仅支持输入500字（含）及以内的文字！");
		return;
	}
	$.ajax({
		 url : "${ctx }/cs/csQuality/saveReviewErrChangeInf_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data : {
			 "qualityInspectionInfoVO.qualityId" : qualityId,
			 "qualityInspectionInfoVO.modifyMode" : modifyMode,
			 "qualityInspectionInfoVO.rollbackAcceptCode" : rollbackAcceptCode,
			 "qualityInspectionInfoVO.modifyRemark" : modifyRemark
		 },
		 success : function(data){
			 if (data.statusCode==DWZ.statusCode.timeout){
					DWZ.loadLogin(data.flag);
			 }else if(data.statusCode == 300){
				 alertMsg.error(data.message);
			 }else{
				 //showQualityLogChange(qualityId);
				 alertMsg.correct(data.message);
			            // 获取隐藏域中的回调函数名
			            
						debugger;
			            // 检查是否存在该函数，且是可调用的
			            if (callBackFunctionChange && typeof window.parent[callBackFunctionChange] === "function") {
			                // 延迟执行，确保页面关闭完成
			                setTimeout(function () {
			                	// 切换回 A 页面
			                	$(".navTab-tab li").each(function() {
			                		if ($(this).find("a span").text().trim() === "整改任务池") {
			                			$(this).trigger('click');
			                		}
			                	});
			                	window.parent[callBackFunctionChange]();  // 执行 A 页面的函数
			                }, 300);  // 300ms 通常足够
			            }

			            // 然后关闭当前 tab
				 navTab.closeCurrentTab();
			 }
		 }
	 });
}
//整改方案选择
function changeModifyMode(obj){
	var modifyMode = $(obj, navTab.getCurrentPanel()).attr("value");
	if(modifyMode == 1){
		$("#saveQualityChangeForm").find("#uploadImageDiv").show();
		$("#saveQualityChangeForm").find("#rollbackAcceptCodeDiv").hide();
		$("#saveQualityChangeForm").find("#modifyRemarkDiv").hide();
	}else if(modifyMode == 2){
		$("#saveQualityChangeForm").find("#uploadImageDiv").hide();
		$("#saveQualityChangeForm").find("#rollbackAcceptCodeDiv").show();
		$("#saveQualityChangeForm").find("#modifyRemarkDiv").hide();
	}else if(modifyMode == 3){
		$("#saveQualityChangeForm").find("#uploadImageDiv").hide();
		$("#saveQualityChangeForm").find("#rollbackAcceptCodeDiv").hide();
		$("#saveQualityChangeForm").find("#modifyRemarkDiv").show();
	}else{
		$("#saveQualityChangeForm").find("#uploadImageDiv").hide();
		$("#saveQualityChangeForm").find("#rollbackAcceptCodeDiv").hide();
		$("#saveQualityChangeForm").find("#modifyRemarkDiv").hide();
	}
}
//本地影像上传
function qualityChange_scansion() {
	var qualityPlanCode2 = $("#qualityPlanCode2").val();
	var title = "保全业务影像上传 ";	
	var fresh = eval("true");
	var external = eval("false"); 
    var tabid="保全业务影像上传 ";
   	var url ="${ctx }/cs/csAccept/showCsImageScanPage_PA_acceptOtherAction.action?uploadDetailReason="+encodeURI(encodeURI('质检计划号'+qualityPlanCode2));
   	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	
}
//抽检轨迹收起放开
function showButtonQualityLogDiv(obj,str){
	var  objs= $(obj, navTab.getCurrentPanel()).attr("value");
	if("+"==objs){
		$(obj, navTab.getCurrentPanel()).val("\-");
		$("#"+str , navTab.getCurrentPanel()).show();
		$("#imgPlusQualityLog" , navTab.getCurrentPanel()).hide();
		$("#imgMinusQualityLog" , navTab.getCurrentPanel()).show();
	}else if("\-"==objs){
		$(obj, navTab.getCurrentPanel()).val("+");
		$("#"+str , navTab.getCurrentPanel()).hide();
		$("#imgPlusQualityLog" , navTab.getCurrentPanel()).show();
		$("#imgMinusQualityLog" , navTab.getCurrentPanel()).hide();
	}
}


/**
 * 暂存 
 * 
 */
function tempSaveErrCfgInfo(obj,param){
	var qualityOrigin = "";
	var qualityStatus = "";
	var qualityId = $("#qualityId2").val();
	var taskId = $("#taskId2").val();
	var qualityRemark = $("#qualityRemark1").val(); //备注
	 //qualityOrigin-抽检来源  1 抽检修改 2 非抽检修改
	 //qualityStatus 状态   1 待质检 2 质检中 3 质检修改 4 质检完成 5 质检取消 
	 if(param.indexOf('save') > -1){ //保存
		qualityStatus = 1;
		qualityOrigin = 1;
	 }else{ //暂存
		 qualityStatus = 3; //质检中（抽检修改中）
	 }
	
	 $.ajax({
		 url : "${ctx }/cs/csQuality/saveReviewErrChangeInf_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data : {
			 "saveFlag" : param,
			 "taskId" : taskId,
			 "qualityInspectionInfoVO.qualityId" : qualityId,
			 "qualityInspectionInfoVO.qualityStatus" : qualityStatus,
			 "qualityInspectionInfoVO.qualityOrigin" : qualityOrigin,
			 "qualityInspectionLogVO.qualityRemark" : qualityRemark
		 },
		 error : function(){
			 alertMsg.info("出错了！");
		 },
		 success : function(data){
			 if(data.statusCode == 300){
				 alertMsg.info("保存失败");
			 }else{
				alertMsg.correct("保存成功");
				if(param.indexOf('save') > -1){
					 $(obj).attr("disabled", true);
					 showQualityLogChange(qualityId);
				 }else{
					 //暂存
				 }
			 }
		 }
	 });
	
}

/**
 * 保存成功显示轨迹细信息
 *
 */
function showQualityLogChange(qualityId){
	
	var $obj1 = $("#showQualityLogChange", navTab.getCurrentPanel());
	var url = "${ctx }/cs/csQuality/showQualityLog_PA_csQualityErrorScoreAction.action";
	$obj1.attr("action",url);
	$obj1.attr("onsubmit","return divSearch(this, 'showQualityLogLogsDetailChange')");
	$obj1.submit();
}


/**
 * 返回
 */
/* function back(){
	var url = "/cs/csQuality/loadqualityModifiy_PA_csQualityErrorScoreAction.action";
	var title = "事后质检处理";	
	var fresh = eval("true");
	var external = eval("false"); 
	var tabid="事后质检处理";
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	 
} */




</script>