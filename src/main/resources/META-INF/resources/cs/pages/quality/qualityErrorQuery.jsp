<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">

<div class="pageContent" layoutH="36px">
	<form action="${ctx }/cs/csQuality/queryResult_PA_csQualityErrorControlAction.action" id="publicEntryForm"  
		onsubmit="return divSearch(this,'getCsTaskDIV')"
		method="post">
		<input type="hidden" name="numPerPage" value="10" />
		<div class="panel">
			<h1>查询条件</h1>				
			<div class="pageFormContent">
				<dl>
					<dt>差错代码</dt>
					<dd><input type="text" name="qualityProjectVO.projectNo"  maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>差错名称</dt>
					<dd><input type="text" name="qualityProjectVO.projectName" maxlength="20"/></dd>
				</dl>		
				<dl>
					<dt>版本号</dt>
					<dd><input type="text" name="qualityProjectVO.version"  maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>是否有效</dt>
					<dd><Field:codeTable  cssClass="combox" nullOption="true" name="qualityProjectVO.validFlag" tableName="APP___PAS__DBUSER.T_YES_NO"/></dd>    
				</dl>

			</div>	
			<div class="panelBar">
				<table style="width:100%">
					<tr>
						<td></td>
						<td style="width:200px">
							<div class="button">
								<div class="buttonContent">
									<button type="button" onclick="queryProjectInfo('publicEntryForm','getCsTaskDIV');">查询</button>
								</div>
							</div>
						</td>
						<td style="width:200px">
							<div class="button">
								<div class="buttonContent">
									<button type="button" onclick="addProjectInfo('publicEntryForm','getCsTaskDIV');">新建</button>
								</div>
							</div>
						</td>
						<td></td>
					</tr>
				</table>
			</div>	
		</div>
	</form>

	<div id="getCsTaskDIV">
	
	</div>	

	
</div>

<script type="text/javascript">
	//查询
	function queryProjectInfo(formId,boxId) {
		var $form = $("#"+formId,navTab.getCurrentPanel());
		$("#"+boxId,navTab.getCurrentPanel()).show();
		$form.submit();
	}
	
	//新增
	function addProjectInfo(formId,boxId){
		var $form = $("#"+formId,navTab.getCurrentPanel());
		$("#"+boxId,navTab.getCurrentPanel()).show();
		var url = "${ctx }/cs/csQuality/addProjectInfo_PA_csQualityErrorControlAction.action";
		$form.attr('action', url);
		$form.attr("onsubmit","return divSearch(this, 'showMainBaseInfo')");
		$form.submit();
	}
	
	
</script>




