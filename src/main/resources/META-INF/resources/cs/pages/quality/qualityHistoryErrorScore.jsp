<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div>
	<div class="divfclass">
				<h1>
					<img src="images/tubiao.png" >差错评分配置历史列表
				</h1>
	</div>
		<div class="pageFormContent">
		 <form id="historyMessageForm" action="${ctx}/cs/csQuality/showHistoryInfo_PA_csQualityErrorScoreAction.action" 
		 	onsubmit="return divSearch(this, 'queryShareTaskDiv');"   method="post"> 
		 <input type="hidden" name="pageNum" value="${pageNum}" /> 
		 <input type="hidden" name="numPerPage" value="${numPerPage}" /> 
			<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid">
				<table class="list" id="appDoctable" style="width:100%" table_saveStatus="1" >
					<thead>
						<tr>
							<!-- <th>选择</th> -->
							<th>序号</th>
							<th>差错类型</th>
							<th>差错名称</th>
							<th>差错明细</th>
							<th>受理折标系数</th>
							<th>录入折标系数</th>
							<th>复核折标系数</th>
							<th>生效日期</th>
							<th>操作员</th>
							<th>操作机构</th>
							<th>操作类型</th>
							<th>操作日期</th>
						</tr>
					</thead>
					<tbody align="center">
						<s:iterator value="currentPage.pageItems" status="st" id="qr">
							<tr>
								<%-- <td><input type="radio" name="reviewErrCfgVO.cfgId" value='${cfgId}'/></td> --%>
								<td><s:property value="#st.index+1" /></td>
								<td><s:property value="errType" /></td>
								<td><s:property value="errorReasonName" /></td>
								<td title='<s:property value="errDetail" />' ><s:property value="showErrDetail" /></td>
								<td><s:property value="acceptRate" /></td>
								<td><s:property value="inputRate" /></td>
								<td><s:property value="reviewRate" /></td>
								<td><s:date name="cfgValidateTime" format="yyyy-MM-dd"/></td>
								<td><s:property value="insertByName" /></td>
								<td><s:property value="organCode" /></td>
								<td><s:property value="orperateType" /></td>
								<td><s:date name="updateTime" format="yyyy-MM-dd"/></td>
							</tr>
						 </s:iterator> 
					</tbody>
				</table>
			</div> 
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select cssClass="combox" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'getCsTaskDIV')">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				
				<div class="pagination" rel="getCsTaskDIV" totalCount="${currentPage.total}"currentPage="${currentPage.pageNo}"
			     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
			</div> 
		</form> 
		</div>
</div>
