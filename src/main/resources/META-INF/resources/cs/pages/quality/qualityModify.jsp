<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.core.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.widget.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/assets/prettify.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.js"></script>

<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/jquery-ui.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/assets/prettify.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.css" />

<input type="hidden" id="policyCode1" value="${policyCode }"/>
<input type="hidden" id="applyCode1" value="${qualityInspectionInfoVO.applyCode }"/>
<input type="hidden" id="acceptCode1" value="${qualityInspectionInfoVO.acceptCode}"/>
<input type="hidden" id="serviceCode1" value="${qualityInspectionInfoVO.serviceCode }"/>
<input type="hidden" id="changeId1" value="${csAcceptChangeVO.changeId }"/>
<input type="hidden" id="acceptId1" value="${csAcceptChangeVO.acceptId }"/>
<input type="hidden" id="customerId1" value="${csApplicationVO.customerId }"/>
<input type="hidden" id="map1" value="${map }"/>
<input type="hidden" name="abnormalPayFlag" id="abnormalPayFlag" value="${csAcceptChangeVO.abnormalPayFlag }"/>
<input type="hidden" id="callBackFunctionModify" value="${callBackFunctionModify }"/>
<form id="showQualityLog" onsubmit="return divSearch(this, 'showQualityLogLogsDetail');" method="post"> 
<input type="hidden" id="qualityId" name="qualityInspectionInfoVO.qualityId" value="${qualityInspectionInfoVO.qualityId }"/>
</form>
<div id="qualityModifyDiv" class="pageContent" layoutH="10">
 <form id="messageForm" onsubmit="return divSearch(this, 'messageForm');" method="post"> 
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">保全件信息
		</h1>
	</div>
	<div class="panelPageFormContent">
	
		<dl>
			<dt>保全受理号</dt>
			<dd><input id="mian_acceptCode" type="text" maxlength="20"  value="${qualityInspectionInfoVO.acceptCode }" disabled="disabled"/></dd>
		</dl>
		<dl>
			<dt>保单号</dt>
			<dd><input type="text" title="${policyCode }" value="${policyCode }" maxlength="20" disabled="disabled"/></dd>
		</dl>
		<dl>
			<dt>保单管理机构</dt>
			<dd><input type="text" title="${organCode }" value="${organCode }" disabled="disabled">
			</dd>
		</dl>
	</div>	
	<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">保全具体信息
			</h1>
	</div>	
	<div class="tabdivclass"> 
		<table class="list" id="appDoctable1" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>
					<th>保全受理号</th>
					<th>保全项目</th>
					<th>补退费金额</th>
					<th>延期天数</th>
					<th>申请方式</th>
					<th>受理人员</th>
					<th>录入人员</th>
					<th>复核人员</th>
					<th>申请确认日期</th>
					<th>保全生效日期</th>
				</tr>
			</thead>
			<tbody align="center">
				<s:iterator value="qualityInspectionInfoVO" status="st" id="qr1">
					<tr>
						<td><s:property value="acceptCode" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
						<td><s:property value="feeAmount" /></td>
						<td><s:property value="delayDays" /></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE" value="${serviceType }"/></td>
						<td><s:property value="acceptByName" /></td>
						<td><s:property value="insertOperatorName" /></td>
						<td><s:property value="reviewName" /></td>
						<td><s:date name="acceptTime" format="yyyy-MM-dd"/></td>
						<td><s:date name="validateTime" format="yyyy-MM-dd"/></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	</div> 
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />代办人信息
		</h1>
	</div>
	<div class="tabdivclass">
		<table width="100%" align="center" class="list">
			<thead align="center">
				<tr>
					<th>代办人姓名</th>
					<th>代办人证件类型</th>
					<th>代办人证件号码</th>
					<th>证件有效期起期</th>
					<th>证件有效期止期</th>
					<th>代办人联系电话</th>
					<th>业务员代码</th>
					<th>新业务员代码</th>
					<th>绩优等级</th>
				</tr>
			</thead>
			<tbody align="center" id="tbodys">
				<tr>
					<td><s:property value="csApplicationVO.agentName" /></td>
					<td><Field:codeValue
							tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
							value="${csApplicationVO.agentCertiType}" /></td>
					<td><s:property value="csApplicationVO.agentCertiCode" />
					<td><input nullOption="true" style="border-style: none;" readonly="readonly" value="<s:date format="yyyy-MM-dd" name="csApplicationVO.agentCertStarDate"/>" /></td>
					<td><input id="agentCertEndDate" nullOption="true" style="border-style: none;" readonly="readonly" value="<s:date format="yyyy-MM-dd" name="csApplicationVO.agentCertEndDate"/>" /></td>
					<td><s:property value="csApplicationVO.agentTel" /></td>
					<td><s:property value="csApplicationVO.agentCode" /></td>
					<td><s:property value="csApplicationVO.agentCodeNew" /></td>
					<td><Field:codeValue tableName="APP___PAS__DBUSER.T_AGENT_LEVEL"
						value="${csApplicationVO.agentLevel}" />
					</td>
				</tr>
			</tbody>
		</table>
	</div> 
	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png">标识信息
		</h1>
	</div>
	<div class="pageContent">
		<s:include value="/cs/pages/csCheck/rewiew_reviewFlag.jsp"></s:include>
	</div>
	<!-- 补退费信息加载 add by panmd_wb -->
	<s:if test="csArapVOs.size!=0">
		<div id="checkPremInfo">
			<s:include value="/cs/pages/csCheck/check_policyPayMode.jsp"></s:include>
		</div>
	</s:if>
	<!-- 保全信息录入 回访电话录入  start-->
	<div id="returnVisitTelCheckDiv">
		<s:include value="/cs/pages/csCheck/check_returnVisitTel.jsp"></s:include>
	</div>
	<!-- 保全信息录入 回访电话录入  end-->
 	
	<div class="divfclass">
		<h1>
			<a href="#" id="showButtonId" value='+'
				onclick="showButtonQualityLogDiv(this,'showQualityLogLogsDetail')">
				<img id="imgPlusQualityLog"  src="${ctx}/cs/img/icon/three_plus.png" />
				<img id="imgMinusQualityLog" style="display: none;" src="${ctx}/cs/img/icon/three_minus.png" />抽检轨迹</a>
		</h1>
	</div>
  	<div class="tabdivclass" id="showQualityLogLogsDetail" style="display: none;"> 
		<table class="list" id="appDoctable2" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>
					<th>抽检日期</th>
					<th>抽检人员</th>
					<th>抽检结论</th>
					<th>抽检意见</th>
					<th>抽检备注</th>
					<th>质检问题类型</th>
					<th>二次质检日期</th>
					<th>二次质检人员</th>
					<th>二次质检结论</th>
					<th>二次质检备注</th>
					<th>整改日期</th>
					<th>整改人员</th>
				</tr>
			</thead>
				<tbody align="center">
						<s:iterator value="qualityInspectionLogListVO" status="st" id="qr1">
							<tr id="<s:property value='inspectionId' />">
								<td><s:date name="qualityCheckDate" format="yyyy-MM-dd"/></td>
								<td><s:property value="qualityCheckName" /></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT"  value="${qualityResult }" /></td>
								<td><s:property value="qualitySurgest" /></td>
								<td><s:property value="qualityRemark" /></td>
								<td><s:property value="questionType" /></td>
								<td><s:date name="recheckDate" format="yyyy-MM-dd"/></td>
								<td><s:property value="recheckByName" /></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_RESULT" value="${recheckResult }"/></td>
								<td>${recheckRemark }</td>
								<td><s:date name="qualityModifyDate" format="yyyy-MM-dd"/></td>
								<td><s:property value="qualityModifyByName" /></td>
							</tr>
						</s:iterator>
				</tbody>
			</div>
		</table>
	</div> 
	<div class="divfclass" <s:if test="existsModify!=1">style="display: none" </s:if>>
		<h1>
			<img src="cs/img/icon/tubiao.png">整改情况
		</h1>
	</div>
  	<div class="tabdivclass" id="showQualityModifyDetail" <s:if test="existsModify!=1">style="display: none" </s:if>> 
		<table class="list" id="appDoctable2" style="width:100%" table_saveStatus="1" >
			<thead>
				<tr>
					<th>整改时间</th>
					<th>整改方案</th>
				</tr>
			</thead>
				<tbody align="center">
						<s:iterator value="qualityInspectionLogListVO" status="st" id="qr1">
							<s:if test="modifyMode != null">
							<tr id="<s:property value='inspectionId' />">
								<td><s:date name="qualityModifyDate" format="yyyy-MM-dd"/></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CS_MODIFY_MODE"  value="${modifyMode }" /></td>
							</tr>
							</s:if>
						</s:iterator>
				</tbody>
			</div>
		</table>
	</div> 
  	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">本次抽检信息
		</h1>
	</div>
  	<form id="saveQualityErrCfg" method="post" >
  		<input type="hidden" id="_existsModify" name=""  maxlength="20" value="${existsModify }"/>
  		<div class="panelPageFormContent">
			<dl>
				<dt>抽检结论</dt>
				<!-- <dd><input type="hidden" id="qualityResult" name="qualityInspectionInfoVO.qualityResult"  maxlength="20" readonly="readonly"/></dd> -->
				<dd>
					<Field:codeTable  cssClass="combox" nullOption="true" id="qualityResult" name="qualityInspectionInfoVO.qualityResult" 
						tableName="APP___PAS__DBUSER.T_QUALITY_RESULT" whereClause="QUALITY_RESULT_TYPE != 3"  onChange="changeStats(this)" value="${qualityInspectionInfoVO.qualityResult }" ></Field:codeTable>
				</dd>
				<dd id="ddIsModify" <s:if test="existsModify!=1 || qualityInspectionInfoVO.qualityResult!=1">style="display: none" </s:if>><input type="checkbox" id="isModify" name="qualityInspectionInfoVO.isModify" <s:if test="qualityInspectionInfoVO.isModify==1">checked</s:if> value="${qualityInspectionInfoVO.isModify }"  />无需整改</dd>
			</dl>
		</div>	
		<%-- <div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">不合格原因
			</h1>
		</div>
		<!-- 不合格原因 -->
		<div class="showOrhiddenErorrCfg">
		<div id="showCurrentErrReason"></div>
		<div class="tabdivclass"> 
				<table class="list" id="appDoctable3" style="width:100%" table_saveStatus="1" >
					<thead>
						<tr>
							<th>差错类型代码</th>
							<th>差错类型名称</th>
							<th>差错具体原因</th>
							<th>受理折标系数</th>
							<th>录入折标系数</th>
							<th>复核折标系数</th>
							<th>删除</th>
						</tr>
					</thead>
					<tbody align="center" id="contentDetails">
						<s:iterator value="reviewErrInfListVO" status="st" id="qr2">
							<tr value="${qualityErrId }">
								<td><s:property value="errorReason" /></td>
								<td>
									<select onchange='changeValue(this)' <s:if test="queryFlag==1"> disabled="disabled"</s:if>>
								       	<option value=''>请选择</option>
								       	<option value='a01' <s:if test="errorReason=='a01'">selected='selected'</s:if>>关键信息未填</option>
								        <option value='a02'  <s:if test="errorReason=='a02'">selected='selected'</s:if>>关键信息填错</option>
								        <option value='a03'  <s:if test="errorReason=='a03'">selected='selected'</s:if>>一般信息未填或填错</option>
								        <option value='a04'  <s:if test="errorReason=='a04'">selected='selected'</s:if>>重要信息录错</option>
								        <option value='b01'  <s:if test="errorReason=='b01'">selected='selected'</s:if>>关键材料缺失</option>
								        <option value='b02' <s:if test="errorReason=='b02'">selected='selected'</s:if>>一般材料缺失</option>
								        <option value='b03' <s:if test="errorReason=='b03'">selected='selected'</s:if>>申请材料不规范</option>
								        <option value='b04' <s:if test="errorReason=='b04'">selected='selected'</s:if>>申请材料无签字</option>
								        <option value='b05' <s:if test="errorReason=='b05'">selected='selected'</s:if>>签字不合规范</option>
								        <option value='c01' <s:if test="errorReason=='c01'">selected='selected'</s:if>>严重不规范操作行为</option>
								        <option value='c02' <s:if test="errorReason=='c02'">selected='selected'</s:if>>一般不规范操作行为</option>
								        <option value='d01' <s:if test="errorReason=='d01'">selected='selected'</s:if>>复核结论有误</option>
								        <option value='e01' <s:if test="errorReason=='e01'">selected='selected'</s:if>>其他</option>
							        </select>
								</td>
								<td title="<s:property value='showErrDetail'/>"><s:property value="errDetail" /></td>
								<td><s:property value="acceptRate" /></td>
								<td><s:property value="reviewRate" /></td>
								<td><s:property value="inputRate" /></td>										
								<td><a href="javascript:void(0)" class="BtnDel" value="${qualityId}" onclick="delPriPointInfo(this,'${qualityErrId}')" <s:if test="queryFlag==1"> disabled="disabled"</s:if>>删除</a></td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</div> 
			<div class="pageFormdiv">
			    <button  type="button" class="but_blue" onclick="addReviewErrInf(this)" <s:if test="queryFlag==1"> disabled="disabled"</s:if>>差错记录</button>
		    </div>  
		</div>
		<div class="showOrhiddenErorrCfg">
		   <div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">抽检折标系数汇总
				</h1>
		  </div>
		 <div class="panelPageFormContent"> 	
					<dl>
						<dt>受理折标系数汇总</dt>
						<dd><input id="acceptRateCollect" type="text" name="qualityInspectionInfoVO.acceptRateCollect" maxlength="20" <s:if test="queryFlag==1"> disabled="disabled"</s:if>/></dd>
					</dl>
					<dl>
						<dt>录入折标系数汇总</dt>
						<dd><input id="insertRateCollect" type="text" name="qualityInspectionInfoVO.insertRateCollect" maxlength="20" <s:if test="queryFlag==1"> disabled="disabled"</s:if>/></dd>
					</dl>
					<dl>
						<dt>复核折标系数汇总</dt>
						<dd><input id="reviewRateCollect" type="text" name="qualityInspectionInfoVO.reviewRateCollect" maxlength="20" <s:if test="queryFlag==1"> disabled="disabled"</s:if>/></dd>
					</dl>
		  </div> 
	    </div>
	  	<div class="panelPageFormContent">
			<dt>抽检备注</dt>
				<dd><textarea id="qualityRemark" name="qualityInspectionLogVO.qualityRemark" maxlength="20" <s:if test="queryFlag==1"> disabled="disabled"</s:if>/></dd>
			</dl>
 		</div> --%>
 		<div class="panelPageFormContent" style="height: 70px">
 			<dl>
				<dt >抽检意见</dt>
				<dd >
					<textarea style="width: 200px" name="qualityInspectionInfoVO.qualitySurgest"
						rows="4" cols="80" id="qualitySurgest">${qualityInspectionInfoVO.qualitySurgest }</textarea>
				</dd>
			</dl>
		</div>
		<div class="pageFormContent" style="height: 50px;margin-left:31px;">
			<dl>
				<input type="hidden"  id="questionTypeHidden" value="${qualityInspectionInfoVO.questionType }" />
				<dt >质检问题类型</dt>
				<dd >
					<%-- <Field:codeTable  cssClass="combox" nullOption="true" id="questionType" name="qualityInspectionInfoVO.questionType" 
						tableName="APP___PAS__DBUSER.T_CS_QUESQUESTION_TYPE"  onChange="changeStats(this)" value="${qualityInspectionInfoVO.questionType }" ></Field:codeTable> --%>
					<select id="questionType" multiple="multiple" name="qualityInspectionInfoVO.questionType"
						selectType="selectMulti" displayname="质检问题类型" isRequired="false" size="5" >
						<s:iterator value="csQuesquestionTypeVOList">
							<option value="${typeCode}">${typeName}</option>
						</s:iterator>
					</select>
				</dd>
			</dl>
		</div>
		<div class="panelPageFormContent" style="height: 70px">
			<dl>
				<dt >抽检备注</dt>
				<dd >
					<textarea style="width: 200px" name="qualityInspectionInfoVO.qualityRemark"
						rows="4" cols="80" id="qualityRemark">${qualityInspectionInfoVO.qualityRemark }</textarea>
				</dd>
			</dl>
			<dl <s:if test="showPersonResponsible!=1 ">style="display: none" </s:if>>
				<dt >指定整改人员</dt>
				<dd >
					<input id ="personResponsibleCode"  type="text" name="qualityInspectionInfoVO.personResponsibleCode" value="${qualityInspectionInfoVO.personResponsibleCode}" maxlength="20"/>
				</dd>
			</dl>
		</div>
	  	<div class="pageFormdiv">
			<button  type="button" class="but_blue" id="checkLive">发起生调</button>
			<button id="tempSave" type="button" class="but_blue" onclick="tempSaveErrCfgInfo(this, 'tmp')" <s:if test="qualityInspectionInfoVO.recheckBy!=null ">style="display: none" </s:if>>暂存</button>
			<button id="SubmSave" type="button" class="but_blue" onclick="tempSaveErrCfgInfo(this,'save')" <s:if test="queryFlag==1"> disabled="disabled"</s:if>>提交</button>
			<button class="but_gray" onclick="back()"   type="button" <s:if test="queryFlag==1"> disabled="disabled"</s:if>>返回</button>
		</div>
	</form>
	<div id="toReCheckDiv" style="display: none">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">质检任务调配
		</h1>
	</div>
  	<form id="toReCheckForm" method="post" >
  		<div class="panelPageFormContent" >
  			<dl>
				<dt>二次质检人员：</dt>
				<dd>
					<input id="recheckByCode" name="qualityInspectionInfoVO.recheckByCode" type="text" value="${qualityInspectionInfoVO.recheckByCode }" />
					<a id="loadQualityPlanUser" target="dialog" width="1300" height="800" title="质检用户" class="btnLook"  href="${ctx }/cs/csQuality_qpm/loadQualityPlanUser_PA_qualityPlanManageAction.action?userBackId=recheckByCode"></a>
				</dd>
			</dl>
		</div>
		<div class="pageFormdiv" style="text-align: left;">
			<button type="button" style="margin-left:166px;"  class="but_blue" onclick="toReCheck()" >调配</button>
		</div>
  	</form>
  	</div>
	<div class="pageFormdiv">
		<button  type="button" class="but_blue" id="flimSearch" >影像查询</button>
		<button  type="button" class="but_blue" onclick="applyDetail()">保全申请明细查询</button>
	    <button  type="button" class="but_blue" onclick="policyDetail()">保全受理明细查询</button>
		<button  type="button" class="but_blue" id="revicePolicySearch" >核保查询</button>
	    <button  type="button" class="but_blue" onclick="reviewSearch()">复核查询</button>
		<button  type="button" class="but_blue" id="backBeeAmount" >补退费明细查询</button>
		<button  type="button" class="but_blue" id="queryCustomerInfo" >一键查询</button>
	</div>
	<div id="checkImagePage" layoutH="160" style="display: none">
	</div>
	<input type="hidden" id="highFlagCustomerId" value="${csReviewVO.highFlagCustomerId}" />
	<div class="pageFormdiv">
		<s:if test="serviceTypeOrgCfg ==1">
			<a	title="投保影像"><button  class="but_blue" onclick="queryRelateImageUrl()">投保书影像</button></a>
		</s:if>	
		<s:else>
			<a target="dialog" rel="loadFileNCQuery" height='242' width='525'
				href="${ctx}/cs/csAccept/queryCustomerMasterInfos_PA_acceptOtherAction.action?acceptCode=${csCheckInitVO.csAcceptChangeVO.acceptCode}&customerId=${customerVO.customerId}&applyCode=${csApplicationVO.applyCode}&changeId=${csCheckInitVO.csAcceptChangeVO.changeId}"
			 title="保单信息">
				<button  class="but_blue">投保书影像</button>
			</a>
		</s:else>
		<button type="button" id="scanning" onclick="queryHistoryScanning();" <s:if test="csReviewVO.highFlag eq false">disabled="disabled"</s:if> class="but_blue">风险测评问卷查阅</button>
		<a target="navTab" rel="policyStatusQuery"
				href="${ctx}/cs/common/loadCustomerListInfo_PA_customerIdentityCheckAction.action?checkProject=3&checkSource=1&acceptId=${csAcceptChangeVO.acceptId}"
				title="客户身份验真"><button type="submit" class="but_blue" >客户身份验真复核</button></a>
		<a target="navTab" rel="policyStatusQuery"
				href="${ctx}/cs/csCheck/getCSInfo_PA_csCheckAction.action?acceptCode=${csCheckInitVO.csAcceptChangeVO.acceptCode}&customerId=${customerVO.customerId}&policyCodeList=${policyCode}&policyCode=${policyCode}"
				title="历史保全批改查询"><button class="but_blue" type="submit" id="uwSignButton">历史保全批改查询</button></a>
		<button type="button" class="but_blue" onclick="showResultShengDiao() ">查看生调结果</button>
		<button type="button" class="but_blue" onclick="searchOprationHistory() ">查看操作记录</button>
	</div>
</div>
<script type="text/javascript" charset="utf-8" language="javascript">
var accpectRate = new Array();
var inputRate = new Array();
var reviewRate = new Array();
var reviewErrInfoList = new Array();
var applyCode = $("#applyCode1").val();
var acceptCode = $("#acceptCode1").val();
var serviceCode = $("#serviceCode1").val();
var policyCode = $("#policyCode1").val();
var customerId = $("#customerId1").val();
var changeId = $("#changeId1").val();
var acceptId = $("#acceptId1").val();
var callBackFunctionModify = $("#callBackFunctionModify").val();
/* var changeId = 12;
var acceptId = 3; */
var reviewErrCfgVOList = "";
$(function(){
	$("select[selectType='selectMulti']").multiselect({
		noneSelectedText : "==请选择==",
		checkAllText : "全选",
		uncheckAllText : '全不选',
		selectedList : 1
	});
	var questionTypeHidden = $("#questionTypeHidden",navTab.getCurrentPanel()).val();
	 if(questionTypeHidden!=null && questionTypeHidden!=''){
		 var questionType = questionTypeHidden.split(",");
		 for (var i = 0; i < questionType.length; i++) {
			 questionType[i] = questionType[i].trim();
		 }
		 $("#saveQualityErrCfg").find("#questionType").val(questionType).multiselect("refresh");
	 }
	 var qualityResult = $("#saveQualityErrCfg").find("#qualityResult").val();//抽检结论
	 if(qualityResult == 1){
		 $("#saveQualityErrCfg").find("#tempSave").hide();
	 }
	 var agentCertEndDate = $("#agentCertEndDate", navTab.getCurrentPanel()).val();//长期是否
 	if(agentCertEndDate == '9999-12-31'){
 		$("#agentCertEndDate", navTab.getCurrentPanel()).val("长期");
 	}
});

//抽检轨迹收起放开
function showButtonQualityLogDiv(obj,str){
	var  objs= $(obj, navTab.getCurrentPanel()).attr("value");
	if("+"==objs){
		$(obj, navTab.getCurrentPanel()).val("\-");
		$("#"+str , navTab.getCurrentPanel()).show();
		$("#imgPlusQualityLog" , navTab.getCurrentPanel()).hide();
		$("#imgMinusQualityLog" , navTab.getCurrentPanel()).show();
	}else if("\-"==objs){
		$(obj, navTab.getCurrentPanel()).val("+");
		$("#"+str , navTab.getCurrentPanel()).hide();
		$("#imgPlusQualityLog" , navTab.getCurrentPanel()).show();
		$("#imgMinusQualityLog" , navTab.getCurrentPanel()).hide();
	}
}
function searchOprationHistory() {
    var acceptCode = $("#acceptCode1", navTab.getCurrentPanel()).val();
    var acceptId = $("#acceptId1", navTab.getCurrentPanel()).val();
    var title = "查询操作记录";
    var fresh = eval("true");
    var external = eval("false");
    var tabid = "searchOprationHistory";
    var url = "${ctx}/cs/information/searchOperationHistory_PA_csInformationQueryAction.action?acceptId="
        + acceptId;
    navTab.openTab(tabid, url, {
      title : title,
      fresh : fresh,
      external : external
    });
  }
function showResultShengDiao() {
	var applyCode=$("#applyCode1",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId1",navTab.getCurrentPanel()).val();
	var title = "保全生调查询结果";
	var fresh = eval("true");
	var external = eval("false");
	var tabid = "showResultShengDiao";
	var url = "${ctx}/cs/manualitem/querySurveyApplyOrAccept_PA_csSurvivalSurveyQueryAction.action?changeId="+ changeId ;
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});
}
function queryRelateImageUrl() {
	var acceptCode = $("#acceptCode1", navTab.getCurrentPanel()).val();
	var _changeId = $("#changeId1", navTab.getCurrentPanel()).val();
	var applyCode = $("#applyCode1").val();
	$.ajax({
		type : 'POST',
		url : '${ctx}/cs/csAccept/queryCoverImage_PA_acceptOtherAction.action?acceptCode=' + acceptCode + '&imageFlag=4',
		success : function(data) {
					window.open(
							data,
							'',
							'width='
									+ (window.screen.availWidth - 10)
									+ ',height='
									+ (window.screen.availHeight - 30)
									+ ',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');
				
		},
		error : DWZ.ajaxError
	});
}
/** 查询问卷**/
function queryHistoryScanning(){
	var highFlagCustomerId = $("#qualityModifyDiv", navTab.getCurrentPanel()).find("#highFlagCustomerId").val();
	if (typeof(highFlagCustomerId) != "undefined" && null != highFlagCustomerId && "" != highFlagCustomerId) {
		var highFlagCustIdArray = highFlagCustomerId.split(",");
		for (var i = 0;i < highFlagCustIdArray.length;i++) {
			var customerId = highFlagCustIdArray[i];
			$.ajax({
				'type':'post',
				'url':'${ctx}/common/highCustomerSignHigh/queryHistoryScanning_PA_highCustomerSignHighAction.action?highCustomerSignVO.customerId='+customerId,
				'datatype':'json',
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode==300){
						alertMsg.error(data.message);
						return;
					} else {
						window.open(data.message);
					}
				},
				'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
				}
			}); 
		}
	}
}
$(function(){
	//差错评分配置
	console.log("applyCode="+applyCode+" acceptCode="+acceptCode+" serviceCode="+serviceCode+" policyCode="+policyCode+
			" changeId="+changeId+" customerId="+customerId+" acceptId="+acceptId);
	/* showTotleRate(); */
	//生存调查
	$("#checkLive").click(function(){
	   /*  var url = "${ctx}/cs/manualitem/csSurvivalSurveyManage_PA_csSurvivalSurveyAction.action?applyCode="+
	    applyCode+"&acceptCode="+acceptCode	+"&serviceCode="+serviceCode+"&policyCode="+policyCode; */
	    
	     var url = "${ctx}/cs/manualitem/survivalSurveyEnter_PA_csSurvivalSurveyAction.action?applyCode="
	     		+applyCode+"&acceptCode="+acceptCode+"&serviceCode="+serviceCode+"&policyCode="+policyCode;
	     var title = "生存调查";	
		 var fresh = eval("true");
		 var external = eval("false"); 
		 var tabid="生存调查";
		 navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});	 
	});
	
	//影像查询
	$("#flimSearch").click(function(){
		/* var title = "影像查询";	
		var fresh = eval("true");
		var external = eval("false"); 
		var tabid="影像查询";
		var url ="${ctx}/cs/csAccept/relateImage_PA_acceptAction.action?applyCode="+
				acceptCode+"&changeId="+changeId+"&customerId="+customerId;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});	 */ 
		 //var  rootPath= getRootPath();
	     //var _changeId = $("#changeId",navTab.getCurrentPanel()).val();
	     //var applyCode = $("#applyCode").val();
         /* $.ajax({
            type:'POST',
            url:'${ctx}/cs/csAccept/queryDataUpdate_PA_csQualityErrorScoreAction.action',
            data : {
            	 "qualityInspectionInfoVO.acceptCode" : acceptCode,
            	 "qualityInspectionInfoVO.applyCode" : applyCode
            },
            dataType:"json",
            async : false,
			cache : false,
            success:function(data){
            	if(!data.statusCode == DWZ.statusCode.error){
            		 alertMsg.error("该保单没有对应影像信息");
            	}else{
		            window.open(data.message,'','width='+(window.screen.availWidth-10)+',height='+
		               (window.screen.availHeight-30)+',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');    
            	}
            }
        }); */
		var url="${ctx}/cs/csCheck/imagePage_PA_acceptOtherAction.action";
			$.ajax({
		        type:'POST',
		        url:url,
		        dataType:"text",
				data : {
					'applyCode' : applyCode,
					'acceptCode' : acceptCode
				},
				async : false,
		        success:function(data)
		        {   
					$("#checkImagePage").html(data); 
					var imageUrl = $("#checkIframe").attr('src');
					window.open(imageUrl,'','width='
							+ (window.screen.availWidth - 10)
							+ ',height='
							+ (window.screen.availHeight - 30)
							+ ',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');
		        },
		        error:DWZ.ajaxError
		    });
			
	});
	
	
	//核保查询
	$("#revicePolicySearch").click(function(){
		var title = "核保查询";	
		var fresh = eval("true");
		var external = eval("false"); 
		var tabid="核保查询";
		var url ="${ctx}/cs/csCheck/showUWDetial_PA_csCheckAction.action?acceptId="+acceptId;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});
	});	
	
	
	//补退费查询
	$("#backBeeAmount").click(function(){
		var title = "补退费明细";	
		var fresh = eval("true");
		var external = eval("false"); 
		var tabid="补退费明细";
		/* var url ="${ctx}/cs/common/parmArapInfo_PA_csPayPremArapAction.action?acceptId="+
			acceptId+"&changeId="+changeId+"&customerId="+customerId+"&massage="+1+"&acceptCode="+acceptCode; */
			//http://10.8.29.7:8080/ls/cs/common/parmArapInfo_PA_csPayPremArapAction.action?&
					//menuId=&acceptId=346128&changeId=447490&customerId=2000005623479&massage=1&acceptCode=z612017012244749129
				
			/* var url ="${ctx}/cs/common/showPayPremArap_PA_csPayPremArapAction.action?changeId="+
				changeId+"&acceptId="+acceptId+"&customerId="+customerId;	 */
		var url = "${ctx}/cs/common/parmArapInfo_PA_csPayPremArapAction.action?&changeId="+
		changeId+"&acceptId="+acceptId+"&customerId="+customerId+"&massage=1&acceptCode="+acceptCode;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});	
	});	
	
	//一键查询
	$("#queryCustomerInfo").click(function(){
		var title = "一键查询";	
		var fresh = eval("true");
		var external = eval("false"); 
		var tabid="一键查询";
		var url = "${ctx}/cs/information/loadPage_PA_csQueryCustomerInfoAction.action?customerId="+customerId+"&acceptId="+acceptId;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});	
	});	
	//复核查询
	/* $("#reviewSearch").click(
		reviewSearch()
	); */
});
/**
 * 复核查询 
 */
function reviewSearch(){
	
	var title = "复核查询";	
	var fresh = eval("true");
	var external = eval("false"); 
	var tabid="复核查询";
			//${ctx}/cs/csCheck/loadCsCheck_PA_csCheckAction.action
	var url ="${ctx}/cs/csCheck/loadCsCheck_PA_csCheckAction.action?acceptId="+
		acceptId+"&acceptCode="+acceptCode+"&reviewFlag=1";
	
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	
	
	/* $.ajax({
		url : "${ctx}/cs/csTask/queryCsCheckTask_PA_csQualityErrorScoreAction.action",
		type : "post",
		dataType : 'json',
		data : {
			"csFocusCheckVO.acceptCode" : acceptCode,
			"csFocusCheckVO.applyCode" : applyCode,
			"numPerPage" : 10
		},
		success : function(result){
			if(result.statusCode == 200){
				var taskId = result.message;
				var title = "复核查询";	
				var fresh = eval("true");
				var external = eval("false"); 
				var tabid="复核查询";
						//${ctx}/cs/csCheck/loadCsCheck_PA_csCheckAction.action
				var url ="${ctx}/cs/csCheck/loadCsCheck_PA_csCheckAction.action?acceptId="+
					acceptId+"&acceptCode="+acceptCode+"&taskId="+taskId+"&checkType="+5;
				
				navTab.openTab(tabid, url, {
					title : title,
					fresh : fresh,
					external : external
				});	
			}else{
				 alertMsg.info(result.message);
			}
		}
	}); */
}




//更改抽检结论
function changeStats(obj){
	var qualityResult = $(obj).attr("value");
	var _existsModify = $("#saveQualityErrCfg").find("#_existsModify").val();
	
	if(qualityResult == 1){
		/* $(".showOrhiddenErorrCfg").css("display","none"); */
		$("#saveQualityErrCfg").find("#tempSave").hide();
		if(_existsModify == '1'){
			$("#saveQualityErrCfg").find("#ddIsModify").show();
		}
	}else{
		/* $(".showOrhiddenErorrCfg").css("display","block"); */
		$("#saveQualityErrCfg").find("#tempSave").show();
		if(_existsModify == '1'){
			$("#saveQualityErrCfg").find("#ddIsModify").hide();
		}
	}
	/* $("#qualityRemark").css("display","block"); */
	
}



/**
 * 暂存 
 * 
 */
function tempSaveErrCfgInfo(obj,param){
	 var qualityStatus = "";
	 var qualityId = $("#qualityId",navTab.getCurrentPanel()).val();
	var qualityResult = $("#saveQualityErrCfg").find("#qualityResult").val();//抽检结论
	var isModify = $("#saveQualityErrCfg").find("#isModify").is(":checked");//无需整改
	var qualitySurgest = $("#saveQualityErrCfg").find("#qualitySurgest").text();//抽检意见
	var questionType = $("#saveQualityErrCfg").find("#questionType").val();//质检问题类型
	var qualityRemark = $("#saveQualityErrCfg").find("#qualityRemark").text();//抽检备注
	var personResponsibleCode = $("#saveQualityErrCfg").find("#personResponsibleCode").val();//指定整改人员
	// var acceptCode = $("#acceptCode1").val();
	 /* var reviewErrInfoList = getTbaleContent();
	 if(qualityResult == 2 || qualityResult == 3){
	     if(reviewErrInfoList == ""){
		    return false;
	     }
	 } */
		 
	 //qualityResult-结论 1 合格 2 不合格 3 复核结论有误
	 //qualityStatus 状态   1 待质检 2 质检中 3 质检修改 4 质检完成 5 质检取消 6整改中
	 if(param.indexOf('save') > -1){
		 if(qualityResult == null || qualityResult == ''){
			 alertMsg.error("请录入抽检结论！");
			 return;
		 }
		 if(qualityResult == 1 ){
			 qualityStatus = 4;
			 if(questionType == null){
				 questionType = '';
			 }
		}else{
			if(qualitySurgest == null || qualitySurgest == ''){
				alertMsg.error("请录入抽检意见！");
				return;
			}else if(qualitySurgest != null && qualitySurgest != '' && qualitySurgest.length > 500){
				alertMsg.error("抽检意见仅支持输入500字（含）及以内的文字！");
				return;
			}
			if(questionType == null || questionType == ''){
				alertMsg.error("请录入质检问题类型！");
				return;
			}
		}
	 }else{
		 qualityStatus = 7; //暂存
	 }
	 var modify = "0";
	 if(isModify){
		 modify = "1";
	 }
	 $.ajax({
		 url : "${ctx }/cs/csQuality/tempSaveReviewErrInf_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data : {
			 "saveFlag" : param,
			 "qualityInspectionInfoVO.qualityId" : qualityId,
			 "qualityInspectionInfoVO.qualityResult" : qualityResult,
			 "qualityInspectionInfoVO.isModify" : modify,
			 "qualityInspectionInfoVO.qualitySurgest" : qualitySurgest,
			 "qualityInspectionInfoVO.questionType" : questionType.toString(),
			 "qualityInspectionInfoVO.qualityRemark" : qualityRemark,
			 "qualityInspectionInfoVO.personResponsibleCode" : personResponsibleCode,
			 "qualityInspectionInfoVO.qualityStatus" : qualityStatus,
			 "qualityInspectionInfoVO.acceptCode" : acceptCode,
			 "qualityInspectionInfoVO.applyCode" : applyCode
		 },
		 success : function(data){
			 if (data.statusCode==DWZ.statusCode.timeout){
					DWZ.loadLogin(data.flag);
				} 
			 
			 else if(data.statusCode == 300){
				 alertMsg.error(data.message);
			 }else{
				 alertMsg.correct(data.message);
				 //提交状态并且没有未处理的生存调查置灰提交按钮
				 if(param.indexOf('save') > -1 && data.message.indexOf("生存调查") == -1){
					 $(obj).attr("disabled", true);
					 showQualityLog(qualityId);
						// 检查是否存在该函数，且是可调用的
			            if (callBackFunctionModify && typeof window.parent[callBackFunctionModify] === "function") {
			                // 延迟执行，确保页面关闭完成
			                setTimeout(function () {
			                	// 切换回 A 页面
			                	$(".navTab-tab li").each(function() {
			                		if ($(this).find("a span").text().trim() === "保全抽检任务池") {
			                			$(this).trigger('click');
			                		}
			                	});
			                	window.parent[callBackFunctionModify]();  // 执行 A 页面的函数
			                }, 300);  // 300ms 通常足够
			            }
					 navTab.closeCurrentTab();
				 }else if(param.indexOf('tmp') > -1){
					 $("#toReCheckDiv").show();
				 }
			 }
		 }
	 });
	
}
/**
 * 调配
 */
function toReCheck(){
	 var qualityId = $("#qualityId",navTab.getCurrentPanel()).val();
	 var recheckByCode = $("#toReCheckForm").find("#recheckByCode").val();//二次质检人员
	 if(recheckByCode == null || recheckByCode == ''){
		 alertMsg.error("请录入二次质检人员！");
		 return;
	 }
	 var personResponsibleCode = $("#saveQualityErrCfg").find("#personResponsibleCode").val();//指定整改人员
	 
	 $.ajax({
		 url : "${ctx }/cs/csQuality/toReCheck_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data : {
			 "qualityInspectionInfoVO.qualityId" : qualityId,
			 "qualityInspectionInfoVO.recheckByCode" : recheckByCode,
			 "qualityInspectionInfoVO.personResponsibleCode" : personResponsibleCode
		 },
		 success : function(data){
			 if (data.statusCode==DWZ.statusCode.timeout){
					DWZ.loadLogin(data.flag);
				} 
			 
			 else if(data.statusCode == 300){
				 alertMsg.error(data.message);
			 }else{
				 alertMsg.correct(data.message);
					// 检查是否存在该函数，且是可调用的
		            if (callBackFunctionModify && typeof window.parent[callBackFunctionModify] === "function") {
		                // 延迟执行，确保页面关闭完成
		                setTimeout(function () {
		                	// 切换回 A 页面
		                	$(".navTab-tab li").each(function() {
		                		if ($(this).find("a span").text().trim() === "保全抽检任务池") {
		                			$(this).trigger('click');
		                		}
		                	});
		                	window.parent[callBackFunctionModify]();  // 执行 A 页面的函数
		                }, 300);  // 300ms 通常足够
		            }
				 navTab.closeCurrentTab();
			 }
		 }
	 });
}

/**
 * 保存成功显示轨迹细信息
 *
 */
function showQualityLog(qualityId){
	
	var $obj1 = $("#showQualityLog", navTab.getCurrentPanel());
	var url = "${ctx }/cs/csQuality/showQualityLog_PA_csQualityErrorScoreAction.action";
	$obj1.attr("action",url);
	$obj1.attr("onsubmit","return divSearch(this, 'showQualityLogLogsDetail')");
	$obj1.submit();
}

/**
 * 返回
 */
function back(){
	/* //获取受理号
	var acceptCodes=$("#mian_acceptCode",navTab.getCurrentPanel()).val();
	//保全项目
	var serviceCodes=$("#mian_serviceCode",navTab.getCurrentPanel()).val();
	//抽检来源
	var qualityOrigins=$("#mian_qualityOrigin",navTab.getCurrentPanel()).val();
	//保单号
	var policyCodes=$("#mian_policyCode",navTab.getCurrentPanel()).val();
	//质检计划代码
	var qualityPlanCodes=$("#mian_qualityPlanCode",navTab.getCurrentPanel()).val();
	//保单管理机构
	var organCodes=$("#mian_organCode",navTab.getCurrentPanel()).val();
	//质检计划名称
	var qualityPlanNames=$("#mian_qualityPlanName",navTab.getCurrentPanel()).val(); */
	navTab.closeCurrentTab();
	/* var title = "抽检处理";
	var tabid = "_accpetPool_M";
	var fresh = eval("true");
	var external = eval("false");
	var url = "${ctx}/cs/csQuality/loadqualityModifiy_PA_csQualityErrorScoreAction.action?acceptCodes="+acceptCodes
			+"&serviceCodes="+serviceCodes+"&qualityOrigins="+qualityOrigins+"&policyCodes="+policyCodes
			+"&qualityPlanCodes="+qualityPlanCodes+"&organCodes="+organCodes+"&qualityPlanNames="+encodeURI(encodeURI(qualityPlanNames));
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	 */ 
}

/**
 * 保全申请明细
 */
function applyDetail(){
	var url ="${ctx}/cs/information/geCsPolicyApplyInfo_PA_csInformationQueryAction.action?"
			+"csPolicyInfoQueryVO.checkedRadio=applyCode&csPolicyInfoQueryVO.queryApplyCode="+changeId;
	var title = "保全申请明细";	
	var fresh = eval("true");
	var external = eval("false"); 
	var tabid="保全申请明细";
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});
}

/**
 * 保全受理明细
 */

function policyDetail(){
	var url = "${ctx}/cs/csAccept/toService_PA_uwParameterAction.action?acceptId="+acceptId+"&nciHeadVo.buttonCode=path()__id(undefined)__name(undefined)__class(undefined)&pageId=path()&btnId=id(undefined)__name(undefined)__class(undefined)";
	console.log("url="+url);
	if(serviceCode == 'AG'){
		var abnormalPayFlag = $("#abnormalPayFlag").val();
		url += "&abnormalPayFlag=" + abnormalPayFlag;
	}
	var title = "保全受理明细";	
	var fresh = eval("true");
	var external = eval("false"); 
	var tabid="保全受理明细";
	navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});
}
//新增一行
/* var currentStep = 0;
var max_line_num = 0;
var _num = 0;
function addReviewErrInf(){
	
  	//添加新记录
	max_line_num = $("#contentDetails tr").length;
    if (max_line_num == null) {
        max_line_num = 1;
      
    }else{
        max_line_num = parseInt(max_line_num);
        max_line_num += 1;
    }
    
    console.log("hangshu="+max_line_num);
    $('#contentDetails').append(
	    "<tr id='line" + max_line_num + "' value="+max_line_num+">" +
	        "<td class='td_Num'></td>" +
	        "<td class='td_Item'>" +
	        	"<select onchange='changeValue(this, max_line_num)'>" +
		        	 "<option value=''>请选择</option>"+
		        	"<option value='a01'>关键信息未填</option>"+
			        "<option value='a02'>关键信息填错</option>"+
			        "<option value='a03'>一般信息未填或填错</option>"+
			        "<option value='a04'>重要信息录错</option>"+
			        "<option value='b01'>关键材料缺失</option>"+
			        "<option value='b02'>一般材料缺失</option>"+
			        "<option value='b03'>申请材料不规范</option>"+
			        "<option value='b04'>申请材料无签字</option>"+
			        "<option value='b05'>签字不合规范</option>"+
			        "<option value='c01'>严重不规范操作行为</option>"+
			        "<option value='c02'>一般不规范操作行为</option>"+
			        "<option value='d01'>复核结论有误</option>"+
			        "<option value='e01'>其他</option>"+ 
		        "</select>" +
	        "</td>" +
	        "<td class='td_Item'></td>" +
	        "<td class='td_Item'></td>" +
	        "<td class='td_Item'></td>" +
	        "<td class='td_Item'></td>" +
	        "<td class='td_Oper'><a href='javascript:void(0)' value='0' class='BtnDel' onclick='delPriPointInfo(this,0)'>删除</a>"+
	        "</td>" +
	    "</tr>"
	  );
} */
//根据名称改变差错值
/* function changeValue(obj, num){
	var errorReason = $(obj).attr("value");
	var $tr =  $(obj).parents("tr");
	$.ajax({
		url : "${ctx}/cs/csQuality/changeReviewErrInfo_PA_csQualityErrorScoreAction.action",
		type : "post",
		dataType : "json",
		data : {
			"reviewErrCfgVO.errorReason" : errorReason
		},
		success : function(data){
			if(data.statusCode == '200'){
				var tdNodes = $tr.find("td");
				tdNodes.eq(0).html("");
				tdNodes.eq(2).html("");
				tdNodes.eq(2).html(""); 
				tdNodes.eq(3).html("");
				tdNodes.eq(4).html("");
				tdNodes.eq(5).html("");
				var json = DWZ.jsonEval(data);
				json = eval("("+data.message+")");
				console.log("json="+json);
				//0差错类型代码	1差错类型名称	2差错具体原因	3受理折标系数	4录入折标系数	5复核折标系数	6折标系数	
				tdNodes.eq(0).append(json.errorReason);
				tdNodes.eq(2).attr("title",json.errDetail);
				tdNodes.eq(2).append(json.showErrDetail); //原因
				tdNodes.eq(3).append(json.acceptRate);
				tdNodes.eq(4).append(json.inputRate);
				tdNodes.eq(5).append(json.reviewRate);
				showTotleRate();
			}else{
				 alertMsg.error(data.message);
			}
		}
	});
	
	
} */
/**
 *  获取表格内容
 *
 */
/* function getTbaleContent(){
	
	var result = $("#qualityResult").val();
	var trNodes = $("#contentDetails tr");
	//alert("结论："+result+" 节点长度："+trNodes.length);
	
	if((result == 2 || result == 3) && trNodes.length == 0){
		 alertMsg.error("必填项不完整，请检查");
		 return false;
	}
	
	$("#contentDetails tr").each(function(){
		var reviewErrInfVO = {};
		var type = $(this).find("td:eq(0)").text();
		if($(this).css("display")!="none"){
			if(type == "" || $(this).find("td:eq(1) option:selected").text().indexOf("请选择")>-1){
				 alertMsg.error("必填项不完整，请检查");
				 return false;
			}			  
			reviewErrInfVO.qualityErrId = $(this).attr("value");
			reviewErrInfVO.errorReason = $(this).find("td:eq(0)").text();
			reviewErrInfVO.errorReasonName = $(this).find("td:eq(1) option:selected").text();
			reviewErrInfVO.errDetail = $(this).find("td:eq(2)").text();
			reviewErrInfVO.acceptRate = $(this).find("td:eq(3)").text(); 
			reviewErrInfVO.inputRate = $(this).find("td:eq(4)").text();
			reviewErrInfVO.reviewRate = $(this).find("td:eq(5)").text(); 
			reviewErrInfVO.qualityId = $(this).find("td:eq(6) a").attr("value");
			reviewErrInfoList.push(reviewErrInfVO);
			
		}		
		//alert("-leixing="+$(this).find("td:eq(0)").text()+" mingcheng"+ $(this).find("td:eq(1) option:selected").attr("value"));
	});
	return reviewErrInfoList;
	
} */
/**
 * 删除抽检信息
 */
/* function delPriPointInfo(obj, qualityErrId){
	
	 if (confirm("确定要删除改记录吗？")) {
		 if(qualityErrId ==0){
			 $(obj).parent().parent().css("display","none");
			 return false;
		 }
	 	 $.ajax({
			 url : "${ctx }/cs/csQuality/deleteReviewErrInf_PA_csQualityErrorScoreAction.action",
			 type : "post",
			 dataType : "json",
			 data : {
				 "reviewErrInfVO.qualityErrId" : qualityErrId
			 },
			 success : function(data){
				 if(data.statusCode == 300){
					 alertMsg.info("删除失败");
					 return false;
				 }else{
					alertMsg.correct("删除成功");
					 $(obj).parent().parent().css("display","none");
				 }
			 }
		 });
	 } 
} */

/**
 * 改变折标系数
 */
/* function showTotleRate(){
	var accpectRate = new Array();
	var inputRate = new Array();
	var reviewRate = new Array();
	var trNodes = $("#contentDetails tr");
	if(trNodes.length > 0){
		$("#contentDetails tr").each(function(){
			if($(this).css("display")!="none"){
				//alert("受理 "+$(this).find("td:eq(3)").text()+" 录入："+$(this).find("td:eq(4)").text()+" 复核："+$(this).find("td:eq(5)").text() );
				accpectRate.push(parseFloat($(this).find("td:eq(3)").text()));
				inputRate.push(parseFloat($(this).find("td:eq(4)").text()));
				reviewRate.push(parseFloat($(this).find("td:eq(5)").text()));
			}
		});
		var amax = getMaxRate(accpectRate);
		var imax = getMaxRate(inputRate);
		var rmax = getMaxRate(reviewRate);
		$("#acceptRateCollect").val(amax);
		$("#insertRateCollect").val(imax);
		$("#reviewRateCollect").val(rmax);
	}
} */
/**
 * 获取url
 *
 */
/* function getPolicyDetailUrl(){
	var url = "";
	var serviceCode = $("#serviceCode1").val();
	//alert("serviceCode="+serviceCode);
	switch(serviceCode){
	
		case "AE": //投保人变更
		  //拼接url
		  url = divUrl("AE");
		  break;
		case "SH": //指定第二投保人
		  //拼接url
		  url = divUrl("SH");
		  break;
		case "AP": // 保费自垫申请和终止
		  url = "${ctx}/cs/serviceitem_ap/loadAPPage_PA_csEndorseAPAction.action";
		  break;
		case "BC": //受益人变更
		  url = divUrl("BC");
		  break;
		case "CC": // 客户基本变更
		  url = "${ctx}/cs/serviceitem_cc/loadCCPage_PA_csEndorseCCAction.action";
		  break;
		case "CD": //关联银行卡
		  url = divUrl("CD");
		  break;
		case "CF": //保单冻结  
		  url = divUrl("AE");
		  break;
		case "CT": //退保
		 // url = divUrl("CT");
		  url = "${ctx}/cs/serviceitem_ct/loadCTPage_PA_csEndorseCTAction.action";
		  break;
		case "CM" : //客户重要资料变更
		  url = divUrl("CM");
		  break;
		case "CP": //保单质押第三方解付
		  url = divUrl("CP");
		  break;
		case "CS": //第三方止付
		  url = divUrl("CS");
		  break;
		case "XT": //协议退保
		  url = divUrl("XT");
		  break;
		case "EA": //公司解约
		  url = divUrl("EA");
		  break;
		case "CW": //保单解冻
		  url = divUrl("CW");
		  break;
		case "DA": //新增附加特约
		  url = divUrl("DA");
		  break;
		case "EN": //附加险满期不续保
		  url = divUrl("EN");
		  break;
		case "FK": //客户层交易密码密码设置、修改
		  url = divUrl("FK");
		  break;
		case "GM": //领取方式变更
		  url = divUrl("GM");
		  break;
		case "HI": // 增补告知 
		  url = divUrl("HI");
		  break;
		case "IO": //客户职业类别变更
		  url = divUrl("IO");
		  break;
		case "MC": //基本信息备注项变更
		  url = divUrl("MC");
		  break;
		case "PF": //保单解挂
		  url = divUrl("PF");
		  break;
		case "MR": //主险续保
		  url = "${ctx}/cs/serviceitem_mr/loadMRPage_PA_csEndorseMRAction.action";
		  break;
		case "PL": //保单挂失
		  url = divUrl("PL");
		  break;
		case "XD": //新增可选责任
		  url = divUrl("XD");
		  break;
		case "NS": //新增附加险
		  url = divUrl("NS");
		  break;
		case "LR": //保单补发
		  url = divUrl("LR");
		  break;
		case "YS": //保险起期变更（个人）
		  url = divUrl("YS");
		  break;
		case "RG": // 生存保险金追回
		  url = divUrl("RG");
		  break;
		case "PA": //加保
		  url = divUrl("PA");
		  break;
		case "PT": //减保
			//cs/serviceitem_pt/loadPTPage_PA_csEndorsePTAction.action
			url = "${ctx}/cs/serviceitem_pt/loadPTPage_PA_csEndorsePTAction.action";
		  //url = divUrl("PT");
		  break;
		case "LN": //保单贷款
		  url = divUrl("LN");
		  break;
		case "FM": //交费方式及期限变更
		  url = divUrl("FM");
		  break;
		case "RF": //保单贷款清偿
		  url = divUrl("RF");
		  break;
		case "MD": //变更目的地国家
		  url = divUrl("MD");
		  break;
		case "RE": //保单复效
		  url = divUrl("RE");
		  break;
		case "AI": //累积生息账户领取与注销
		  url = divUrl("AI");
		  break;
		case "PR": //保单迁移无录入标识
		  url = divUrl("PR");
		  break;
		case "ER": //附加险满期降低保额续保
		  url = divUrl("ER");
		  break;
		case "GB": //领取年龄变更
		  url = divUrl("GB");
		  break;
		case "AG": //年金满期金给付
		  url = divUrl("AG");
		  break;
		case "DT": //附加特约终止
		  url = divUrl("DT");
		  break;
		case "PC": //
		  url = divUrl("PC");
		  break;
		case "LC": //领取日期变更
		  url = divUrl("LC");
		  break;
		case "GC": //领取形式变更
		  url = divUrl("GC");
		  break;
		case "XX": //保障计划约定变更
		  url = divUrl("XX");
		  break;
		case "PU": //减额交清/险种转换
		  url = divUrl("PU");
		  break;
		case "SP": //TPA直付卡补发
		  url = divUrl("SP");
		  break;
		case "AZ": //新增领取责任
		  url = divUrl("AZ");
		  break;
		case "TR": //自垫清偿-
		  url = divUrl("TR");
		  break;
		case "RR": //续保险种转换
		  url = divUrl("RR");
		  break;
		case "TA": //转赠养老金
		  url = divUrl("TA");
		  break;
		case "PG": //万能险/投连险账户部分领取
		  url = divUrl("PG");
		  break;
		case "AM": //追加保费-
		  url = divUrl("AM");
		  break;
		case "DC": //万能险基本保额约定变更
		  url = divUrl("DC");
		  break;
		case "RB": //保全回退
		  url = "${ctx}/cs/serviceitem_rb/queryRBcsHistory_PA_csEndorseRBAction.action";
		  break;
		case "CB": //万能险基本保额减少
		  url = divUrl("CB");
		  break;
		case "CA": //万能险基本保额增加
		  url = divUrl("CA");
		  break;
		case "IT": //投连险退保
		  url = divUrl("IT");
		  break;
		case "TI": //投连险投资账户转换
		  url = divUrl("TI");
		  break;
		case "RA": //保单复缴 
		  url = "${ctx}/cs/serviceitem_ra/loadRAPage_PA_csEndorseRAAction.action";
		  break;
		case "RZ": //柜面认证
		  url = divUrl("RZ");
		  break;
		case "UC": //险种变更
			  url = "${ctx }/cs/serviceitem_uc/loadUCDetail_PA_csEndorseUCAction.action"
			  break;
		case "RL": //保单贷款续贷
			  url = "${ctx}/cs/serviceitem_rl/loadRLPage_PA_csEndorseRLAction.action"
			  break;	  
		case "HC": //红利领取形式变更
			  url = "${ctx}/cs/serviceitem_hc/loadHCPage_PA_csEndorseHCAction.action"
			  break;	  
		case "SO": //社保状态变更
			  url = "${ctx}/cs/serviceitem_so/loadSOPage_PA_csEndorseSOAction.action"
			  break;
		default:
		  url = "${ctx}/cs/csQuality/loadqualityModifiy_PA_csQualityErrorScoreAction.action";

	}
	return url;
		
} */
/**
 * 组装Url 
 */
/* function divUrl(serviceCode){
		   //cs/serviceitem_pt/loadPTPage_PA_csEndorsePTAction.action
	//${ctx}/cs/serviceitem_ae/loadAEPage_PA_csEndorseAEAction.action
	var divUrl = "${ctx}/cs/serviceitem_"+ serviceCode.toLowerCase()+
	"/load"+serviceCode.toUpperCase()+"Page_PA_csEndorse"+serviceCode.toUpperCase()+"Action.action";
	return divUrl;
	
} */
//比较最大值
/* function getMaxRate(tmp){
	console.log("---"+tmp);
	var max = tmp[0];
	for(var i = 1; i < tmp.length; i++){
	  if(max<tmp[i]){
		  max=tmp[i];
	  }
	}
	return max;
} */


</script>