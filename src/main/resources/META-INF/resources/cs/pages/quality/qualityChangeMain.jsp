<!-- 保全录入个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<%--   <form id="searchRevieweErrCfg" onsubmit="return divSearch(this, 'queryShareTaskDiv1');" method="post">
 	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
	 <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> 
	 <input type="hidden" id="applyCode" name="qualityInspectionInfoVO.applyCode"  /> 
	 <input type="hidden" id="policyCode" name="qualityInspectionInfoVO.policyCode"/> 
	 <input type="hidden" id="orangeName" name="qualityInspectionInfoVO.orangeName"/> 
	 <input type="hidden" id="serviceCode" name="qualityInspectionInfoVO.serviceCode"/> 
	 <input type="hidden" id="qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName"/> 
	 <input type="hidden" id="qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode"/> 
	 <input type="hidden" id="cfgValidateTime" name="reviewErrCfgVO.cfgValidateTime"/> 
	 <input type="hidden" id="qualityOrigin" name="qualityInspectionInfoVO.qualityOrigin"/> 
</form>  --%>
<div class="pageContent" layoutH="10">
<div style="display:none">
<form id="assignForm" action="${ctx }/cs/csQuality/manualAssign_PA_csQualityErrorScoreAction.action" method="post"  onsubmit="return divSearch(this, 'showQuqlityChangeList')">  
			<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">申请任务
				</h1>
			 </div>
			<div class="pageFormInfoContent">
				<dl >
					<dt >保单管理机构</dt>
					<dd >
						<input id="organCodeId" name="qualityInspectionInfoVO.organCode"
							value="${qualityInspectionInfoVO.organCode}" type="text" size="3"
							class="organ" clickId="menuBtn" showOrgName="organNameId" style="width:30px;border-right:0"/>
					    <input id="organNameId"  type="text" size="13" class="public_textInput" style="width:110px"
							readOnly /> <a id="menuBtn" class="btnLook" href="#"></a>
					</dd>
				</dl>
				<dl>
					<dt>所属片区</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true" 
						            name="qualityInspectionInfoVO.areaCode" tableName="APP___PAS__DBUSER.T_CS_AREA"/>
					</dd>
				</dl>
				<div class="pageFormdiv">
					<button  type="button" class="but_blue" id="openBtn" onclick="openAutoAssign();">打开自动分配</button>
					<button  type="button" class="but_gray" id="closeBtn" onclick="closeAutoAssign();">关闭自动分配</button>
					<button  type="button" class="but_blue" id="manualBtn" onclick="manualAssign();">手工申请任务</button>
				</div>
			</div>
	</form>	
	</div>
	 <form id="changeCommMessageForm" action="${ctx }/cs/csQuality/queryQualityModifyPageList_PA_csQualityErrorScoreAction.action"
		 onsubmit="return divSearch(this, 'showQuqlityChangeList')" method="post"> 
			<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">共享池查询条件
				</h1>
			 </div>
			<div class="pageFormInfoContent">
			<input type="hidden" id="taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="1" /> <!-- 共享池 -->
			<input type="hidden" id="taskType" name="qualityInspectionInfoVO.taskType" value="2" /> <!-- 整改任务 -->
		 	<input type="hidden" name="numPerPage" value="10" /> 
				<dl >
					<dt >保全受理机构</dt>
					<dd >
						<input id="step1_organCode" name="qualityInspectionInfoVO.organCode"
							value="${qualityInspectionInfoVO.organCode}" type="text" size="3"
							class="organ" clickId="menuBtn1" showOrgName="organNameId1" style="width:30px;border-right:0"/>
					    <input id="organNameId1"  type="text" size="13" class="public_textInput" style="width:110px"
							readOnly /> <a id="menuBtn1" class="btnLook" href="#"></a>
					</dd>
				</dl>
				<dl >
					<dt >保单管理机构</dt>
					<dd >
						<input id="step1_policyOrganCode" name="qualityInspectionInfoVO.policyOrganCode"
							value="${qualityInspectionInfoVO.policyOrganCode}" type="text" size="3"
							class="organ" clickId="menuBtn2" showOrgName="policyOrganNameId" style="width:30px;border-right:0"/>
					    <input id="policyOrganNameId"  type="text" size="13" class="public_textInput" style="width:110px"
							readOnly /> <a id="menuBtn2" class="btnLook" href="#"></a>
					</dd>
				</dl>
				<dl>
					<dt>保全受理号</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.acceptCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>保单号</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.policyCode" maxlength="20"/></dd>
				</dl>
				<dl >
					<dt>保全项目</dt>
					<dd>
						<Field:codeTable  cssClass="combox" nullOption="true" name="qualityInspectionInfoVO.serviceCode" 
						tableName="APP___PAS__DBUSER.T_SERVICE" />
					</dd>
				</dl>
				<dl>
					<dt>质检计划代码</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.qualityPlanCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>质检计划名称</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.qualityPlanName" maxlength="100"/></dd>
				</dl>	
				<div class="pageFormdiv">
					<button  type="button" class="but_blue" onclick="searchPageList()">查询</button>
				</div>
			</div>	
	  </form> 
<div class="pageContent"> 
   <div id="showQuqlityChangeList" >
   	<s:include value ='qualityChangeMainList.jsp'></s:include>
   </div>
</div>
	 <form id="changePersonMessageForm" action="${ctx }/cs/csQuality/queryQualityModifyPageList_PA_csQualityErrorScoreAction.action"
		 onsubmit="return divSearch(this, 'showQuqlityChangePersonList')" method="post"> 
			<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">个人池查询条件
				</h1>
			 </div>
			<div class="pageFormInfoContent">
				<input type="hidden" id="taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="0" /> <!-- 个人池 -->
				<input type="hidden" id="taskType" name="qualityInspectionInfoVO.taskType" value="2" /> <!-- 整改任务 -->
			 	<input type="hidden" name="numPerPage" value="10" /> 
				<dl>
					<dt>保全受理号</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.acceptCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>保单号</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.policyCode" maxlength="20"/></dd>
				</dl>
				<dl >
					<dt>保全项目</dt>
					<dd>
						<Field:codeTable  cssClass="combox" nullOption="true" name="qualityInspectionInfoVO.serviceCode" 
						tableName="APP___PAS__DBUSER.T_SERVICE" />
					</dd>
				</dl>
				<dl>
					<dt>质检计划代码</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.qualityPlanCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>质检计划名称</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.qualityPlanName" maxlength="100"/></dd>
				</dl>	
				<div class="pageFormdiv">
					<button  type="button" class="but_blue" onclick="searchPagePersonList()">查询</button>
				</div>
			</div>	
	  </form> 
<div class="pageContent"> 
   <div id="showQuqlityChangePersonList" >
   	<s:include value ='qualityChangePersonList.jsp'></s:include>
   </div>
</div>
</div>
<script type="text/javascript">
//初始化的时候
$(document).ready(function(){
	 // 获取当前页面容器
    //var $panel = navTab.getCurrentPanel();
    // 设置 tabId（如 bPageTab）
    //$panel.attr("navTabId", "qualityChangeMain");
	searchPageList();
	searchPagePersonList();
});
/**
 * 查询
 */
function searchPageList(){
	var $obj = $("#changeCommMessageForm", navTab.getCurrentPanel());
	$obj.submit();
}
function searchPagePersonList(){
	var $obj = $("#changePersonMessageForm", navTab.getCurrentPanel());
	$obj.submit();
}
function openAutoAssign(){
	$("#openBtn",navTab.getCurrentPanel()).attr("class","but_gray");
	$("#closeBtn",navTab.getCurrentPanel()).attr("class","but_blue");
	var $form=$("assignForm",navTab.getCurrentPanel());
	$.ajax({
		 url : "${ctx }/cs/csQuality/autoAssign_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data :$form.serializeArray(),
		 success : function(data){
			 if(data.statusCode == 300){
				 alertMsg.error(data.message);
			 }else{
				 $("#showQuqlityChangeList".navTab.getCurrentPanel()).html(data).initUI();
			 }
		 }
	 });
}
function closeAutoAssign(){
	$("#openBtn",navTab.getCurrentPanel()).attr("class","but_blue");
	$("#closeBtn",navTab.getCurrentPanel()).attr("class","but_gray");
}
function manualAssign(){
	//assignForm
	var $form=$("assignForm",navTab.getCurrentPanel());
	$.ajax({
		 url : "${ctx }/cs/csQuality/manualAssign_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data :$form.serializeArray(),
		 success : function(data){
			 if(data.statusCode == 300){
				 alertMsg.error(data.message);
			 }else{
				 $("#showQuqlityChangeList".navTab.getCurrentPanel()).html(data).initUI();				 
			 }
		 }
	 });
}
</script>