<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">	
<script type="text/javascript" src="cs/js/jquery-2.0.3.js"></script>
<script type="text/javascript" src="cs/js/jquery.form.js"></script>
<div class="pageContent" layoutH="36px">
<form action="${ctx }/cs/csQuality_qpm/showQualityPlanManagePage_PA_qualityPlanManageAction.action" id="showQualityPlanAddOrUpdatePage"
		onsubmit="return navTabSearch(this);" method="post">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">计划基本信息
		</h1>
	 </div>
	<div class="pageFormInfoContent">
	            <input type="hidden" id="qualityPlanId" name="qualityPlanVO.qualityPlanId" value="${qualityPlanVO.qualityPlanId }">
	            <input type="hidden" id="validFlag" name="qualityPlanVO.validFlag" value="${qualityPlanVO.validFlag }">
	            <input type="hidden" id="qualityPlanStatus" name="qualityPlanVO.qualityPlanStatus" value="${qualityPlanVO.qualityPlanStatus }">
	            <input type="hidden" id="fileId" name="qualityFilesVO.fileId" value="">
				<dl>
					<dt>质检计划号：</dt>
					<dd>${qualityPlanVO.qualityPlanId }</dd>
				</dl>
				<dl>
					<dt>计划名称<font color="red">*</font>：</dt>
				    <dd><input type="text" id="planName" name="qualityPlanVO.planName" value="${qualityPlanVO.planName }"></dd>
				</dl>
				<dl>
					<dt>质检类型<font color="red">*</font>：</dt>
					<dd><Field:codeTable id="qualityType"  cssClass="combox" nullOption="true" name="qualityPlanVO.qualityType" tableName="APP___PAS__DBUSER.T_CS_QUALITY_TYPE" value="${qualityPlanVO.qualityType }"/></dd>  
				</dl>
				<dl>
					<dt>质检截止日期<font color="red">*</font>：</dt>
					<dd><input id="qualityEndDate" type="text" class="date" name="qualityPlanVO.qualityEndDate" value="<s:date name="qualityPlanVO.qualityEndDate" format="yyyy-MM-dd" />" /></dd>    
				</dl>
				<dl>
					<dt>数据来源<font color="red">*</font>：</dt>
					<dd><Field:codeTable id="qualityDataSource"  cssClass="combox" nullOption="true" name="qualityPlanVO.qualityDataSource" tableName="APP___PAS__DBUSER.T_QUALITY_DATA_SOURCE" value="${qualityPlanVO.qualityDataSource }" onChange="isShowDataImportDiv()"/></dd>    
				</dl>
			  <div class="pageFormdiv"> 
			  <s:if test="addFlag==1">
			    <button onclick="createQualityPlan()" type="button" class="but_blue">创建质检计划</button>
			  </s:if>
			  <s:else>
			    <button onclick="updateQualityPlan()" type="button" class="but_blue">保存修改信息</button>
			  </s:else>
			  </div> 
	</div>
</form>

<div id="dataImportDiv"  style="display:none;">
<form id="uploadFile" action="cs/csConfig/cs/csQuality_qpm/uploadFile_PA_qualityPlanManageAction.action" method="post" enctype="multipart/form-data" >
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">数据导入
		</h1>
	 </div>
	<div class="pageFormInfoContent">
				<dl>
					<dt>质检比例（%）<font color="red">*</font>：</dt>
					<dd><input id="qualityRate"  type="number" value="100" max ="100" onchange="checkRate()" onkeydown="value=(value.replace(/\D|^0/g,'')==''?'':parseInt(value))" onkeyup="value=(value.replace(/\D|^0/g,'')==''?'':parseInt(value))"></dd>
				</dl>
				<dl>
					<dt>上传文件<font color="red">*</font>：</dt>
				    <dd><input type="file" name="fileName" id="fileNameAdd" value ="fileName" style="height: 27px;width: 155%">
				    </dd>
				</dl>
				<dl>
					<dt style="width: 65%"><button onclick="uploadFile()" type="button" class="but_blue">上传/导入</button></dt>
				    <dd></dd>
				</dl>
			  <div class="pageFormdiv" style="padding-right: 25%;padding-top: 15px"> 
			  	<a title="" href="cs/csQuality_qpm/downLoadFileTemplate_PA_qualityPlanManageAction.action"
							target="dwzExport" id="downLoadFileTemplate" style="display: none;"></a>
				<button onclick="downLoadFileTemplate()" type="button" class="but_blue">下载导入模板</button>
			  </div> 
	</div>
</form>

<form id="showQualityPlanManage" method="post">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">导入文件列表
		</h1>
    </div>
	<div class="pageContent"  style="width:100%;">
	  <div class="tabdivclass">
			<table class="list" style="width: 100%">
				<thead>
					<tr>
						<th>导入的文件名称</th>
						<th>导入时间</th>
						<th>质检比例（%）</th>
						<th>上传数据量</th>
						<th>导入数据量</th>
						<th>未上传成功数据量</th>
						<th>状态</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody>
				 <s:iterator value="qualityFilesVOList" id="" status="st">
						<tr>
							<td>${fileName}</td>
                            <td><s:date name="uploadTime" format="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>${qualityRate}</td>
                            <td>${totalCount}</td>
                            <td>${successTotal}</td>
                            <td>
                            <s:if test="failTotal==0">
                              ${failTotal}
                            </s:if>
                            <s:else>
                               <a title="" href="cs/csQuality_qpm/downLoadCheckResult_PA_qualityPlanManageAction.action?qualityFilesVO.fileId=${fileId}"
							  target="dwzExport" id="downLoadCheckResult" style="text-decoration: underline;">${failTotal}</a>
                            </s:else>
                            </td>
                            <td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_FILES_STATUS" value="${fileStatus}"/></td>
							<td><a href="#" class="btnDel" id="" onclick="delFile(${fileId})">删除</a></td>
						</tr>
			    </s:iterator>
				</tbody>
			</table>
			</div>
			<div class="pageFormContent">
				<dl>
					<dt>导入数据总量：</dt>
					<dd>
                         ${total1}
					</dd>
				</dl>
				<dl>
					<dt>质检抽取数据总量：</dt>
					<dd>
                         ${total2}
					</dd>
				</dl>
			</div>
			<div class="pageFormdiv" style="padding-right: 85%">
			    <button onclick="queryFileInfoByPlanID()" type="button" class="but_blue">刷新数据检查/抽取结果</button>
			</div>
	</div>
</form>
</div>
</div>
<script type="text/javascript">
$(document).ready(function(){
	var qualityDataSource = $("#qualityDataSource",navTab.getCurrentPanel()).val();
	if(qualityDataSource == '01'){
		$("#dataImportDiv",navTab.getCurrentPanel()).show();
	}else{
		$("#dataImportDiv",navTab.getCurrentPanel()).attr("style", "display:none;");
	}
})
	function checkQualityPlan() {
		var planName = $("#planName",navTab.getCurrentPanel()).val();
		var qualityType = $("#qualityType",navTab.getCurrentPanel()).val();
		var qualityEndDate = $("#qualityEndDate",navTab.getCurrentPanel()).val();
		var qualityDataSource = $("#qualityDataSource",navTab.getCurrentPanel()).val();
		if(planName == "" || planName == null || qualityType == "" || qualityType == null  ||qualityEndDate == "" || qualityEndDate == null ||qualityDataSource == "" || qualityDataSource == null ){
			alertMsg.info("必录项未录入，请确认！");
			return false;
		}
		if(qualityDataSource != '01'){
			alertMsg.info("目前数据来源仅支持【自定义上传】！");
			return false;
		}
		var pattern = /^[0-9a-zA-Z\u4e00-\u9fa5]+$/;
		if(!pattern.test(planName)){
			alertMsg.info("计划名称仅支持录入“数字、大小写字母、汉字”。");
			return false;
		}
		if(planName.length >15){
			alertMsg.info("计划名称最多不超过15字符长度。");
			return false;
		}
		var myDate =udmpUtil.getWorkDate();
		var nowDate = myDate.getYear()+"-"+addZero(parseInt(myDate.getMonth())+1)+"-"+addZero(parseInt(myDate.getDate()));
		var qualityEndDate1 = qualityEndDate.replaceAll("-","");
		nowDate = nowDate.replaceAll("-","");
		if(qualityEndDate1<nowDate){
			alertMsg.info("质检截止日期不可早于当前系统日期");
			return false;
		}
		return true;
	}
	
	function createQualityPlan() {
		var flag = checkQualityPlan();
		if(flag){
			var $Obj1 = $("#showQualityPlanAddOrUpdatePage",navTab.getCurrentPanel()); 
			var action="${ctx }/cs/csQuality_qpm/createQualityPlan_PA_qualityPlanManageAction.action";
			$Obj1.attr('action',action);
			$Obj1.submit();
			alertMsg.correct("创建质检计划成功！");
		}
	}
	
	function updateQualityPlan() {
		var flag = checkQualityPlan();
		if(flag){
			var qualityPlanId = $("#qualityPlanId",navTab.getCurrentPanel()).val();
			$.ajax({
				type : 'POST',
				url : "${ctx }/cs/csQuality_qpm/checkQualityPlanStatus_PA_qualityPlanManageAction.action",
				data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
				cache : false,
				success : function(data) {
					var json = DWZ.jsonEval(data);
					if (json.statusCode == 300) {
						alertMsg.error(json.message);
					}else{
						var $Obj1 = $("#showQualityPlanAddOrUpdatePage",navTab.getCurrentPanel()); 
						var action="${ctx }/cs/csQuality_qpm/modifyQualityPlanByID_PA_qualityPlanManageAction.action";
						$Obj1.attr('action',action);
						$Obj1.submit();
						alertMsg.correct("保存修改信息成功！");
					}
				},
				error : function() {
					alertMsg.err("失败");
				}
		});
		}
	}
	
	function isShowDataImportDiv() {
		var qualityDataSource = $("#qualityDataSource",navTab.getCurrentPanel()).val();
		if(qualityDataSource == '01'){
			$("#dataImportDiv",navTab.getCurrentPanel()).show();
		}else{
			$("#dataImportDiv",navTab.getCurrentPanel()).attr("style", "display:none;");
		}
	}
	
	/*清单导出按钮*/
	function downLoadFileTemplate(){
		/*调用*/
		$("#downLoadFileTemplate", navTab.getCurrentPanel()).click();
	}
	
	/*清单导入*/
	function uploadFile(){
		var qualityPlanId = $("#qualityPlanId",navTab.getCurrentPanel()).val();
		if(qualityPlanId==null || qualityPlanId==""){
			alertMsg.info("仅支持待发布状态质检计划进行修改，请确认！");
			return false;
		}
		$.ajax({
			type : 'POST',
			url : "${ctx }/cs/csQuality_qpm/checkQualityPlanStatus_PA_qualityPlanManageAction.action",
			data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
			cache : false,
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == 300) {
					alertMsg.error(json.message);
				}else{
					var fileName = $("#fileNameAdd",navTab.getCurrentPanel()).val();
					if(fileName == null || '' == fileName){
						alertMsg.info("请上传数据文件。");
						return false;
					}
					var qualityRate = $("#qualityRate",navTab.getCurrentPanel()).val();
					if(qualityRate == null || '' == qualityRate){
						alertMsg.info("请录入质检比例。");
						return false;
					}
					if(qualityRate > 100){
						alertMsg.info("质检比例不能大于100。");
						return false;
					}
					$("#uploadFile",navTab.getCurrentPanel()).ajaxSubmit({
						　     url : "cs/csQuality_qpm/uploadFile_PA_qualityPlanManageAction.action?qualityFilesVO.qualityPlanId="+qualityPlanId+"&qualityFilesVO.qualityRate="+qualityRate , 
						　　type : "post",
						　　data : $('#uploadFile',navTab.getCurrentPanel()).serialize(),
						　　processData : false,
						　　contentType : false,
						    dataType: "json",
						　　error : function(request) {
								alertMsg.err("保存失败！");
						　　},
						　　success : function(response) {
								var json = DWZ.jsonEval(response);
								if (json.statusCode == DWZ.statusCode.timeout) {
									DWZ.loadLogin();
								} else if (json.statusCode != "200") {
									if (json.message && alertMsg){
										alertMsg.info(json.message);
									}
								} else {
									queryFileInfoByPlanID();
									alertMsg.correct("质检数据导入成功。");
								}
						　　}
						
					})
				}
			},
			error : function() {
				alertMsg.err("失败");
			}
	});
	}
	function queryFileInfoByPlanID(){
		var $Obj1 = $("#showQualityPlanAddOrUpdatePage",navTab.getCurrentPanel()); 
		var action="${ctx }/cs/csQuality_qpm/queryFileInfoByPlanID_PA_qualityPlanManageAction.action";
		$Obj1.attr('action',action);
		$Obj1.submit();
	}
	
	function delFile(fileId){
		$.ajax({
			type : 'POST',
			url : "${ctx }/cs/csQuality_qpm/checkQualityFileStatus_PA_qualityPlanManageAction.action",
			data : "qualityFilesVO.fileId=" + fileId,
			cache : false,
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == 300) {
					alertMsg.error(json.message);
				}else{
					$("#fileId",navTab.getCurrentPanel()).val(fileId);
					var $Obj1 = $("#showQualityPlanAddOrUpdatePage",navTab.getCurrentPanel()); 
					var action="${ctx }/cs/csQuality_qpm/delFile_PA_qualityPlanManageAction.action";
					$Obj1.attr('action',action);
					$Obj1.submit();
					alertMsg.correct("删除成功！");
				}
			},
			error : function() {
				alertMsg.err("失败");
			}
	});
	}
	
	function checkRate(){
		var qualityRate = $("#qualityRate",navTab.getCurrentPanel()).val(); 
		if(qualityRate<1){
			$("#qualityRate",navTab.getCurrentPanel()).val("");
		}
	}
	
	
</script>