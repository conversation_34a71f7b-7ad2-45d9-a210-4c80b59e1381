<!-- 质检个人任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">个人池查询结果
	</h1>
 </div>
<div class="pageContent">
<form id="pagerForm" name="ChangePerson" action="${ctx }/cs/csQuality/queryQualityModifyPageList_PA_csQualityErrorScoreAction.action"
		 onsubmit="return divSearch(this, 'showQuqlityChangePersonList')"  method="post"> 
	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
	 <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> 
	 <input type="hidden" id="taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="0" /> <!-- 个人池 -->
	 <input type="hidden" id="taskType" name="qualityInspectionInfoVO.taskType" value="2" /> <!-- 整改任务 -->
	 <input type="hidden" id="applyCode" name="qualityInspectionInfoVO.applyCode"  value="${qualityInspectionInfoVO.applyCode }" /> 
	 <input type="hidden" id="policyCode" name="qualityInspectionInfoVO.policyCode"  value="${qualityInspectionInfoVO.policyCode }"/> 
	 <input type="hidden" id="serviceCode" name="qualityInspectionInfoVO.serviceCode"  value="${qualityInspectionInfoVO.serviceCode }"/> 
	 <input type="hidden" id="qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName"  value="${qualityInspectionInfoVO.qualityPlanName }"/> 
	 <input type="hidden" id="qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode"  value="${qualityInspectionInfoVO.qualityPlanCode }"/> 
	</form>
	 	<div class="tabdivclass"> 
			<table class="list" id="appDoctablePerson" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>选择</th>
						<th>保全受理机构</th>
						<th>保全受理号</th>
						<th>保单号</th>
						<th>保单管理机构</th>
						<th>保全项目</th>
						<th>申请方式</th>
						<th>补退费金额</th>
						<th>申请提交日期</th>
						<th>受理人员</th>
						<th>复核日期</th>
						<th>复核人</th>
						<th>质检人</th>
						<th>进入任务池时间</th>
					</tr>
				</thead>
				<tbody align="center" id="tbodyChangePerson">
					  <s:iterator value="currentPage.pageItems" status="st" id="qr">
						<tr <s:if test="modifyColour!=null ">style="color: ${modifyColour}" </s:if>>
							<td><input type="radio" name="qualityInspectionInfoVO.qualityId" id="qualityIdradio" value='${qualityId}'/></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${organCode}"/></td>
							<td><s:property value="acceptCode" /></td>
							<td id="policyCode"><s:property value="policyCode" /></td>
							<td id="policyOrganCode"><s:property value="policyOrganCode" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"  value="${serviceCode }" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE_TYPE"  value="${serviceType }" /></td>
							<td>${feeAmount }</td>
							<td><s:date name="applyTime" format="yyyy-MM-dd"/></td>
							<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${acceptUser.userName}(${acceptUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${acceptUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${acceptUser.phone},办公电话:${acceptUser.telPhone},邮箱:${acceptUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${acceptUser.userId}" />
								</dt></a></td>
							<td><s:date name="reviewTime" format="yyyy-MM-dd"/></td>
							<td><a maxable="true" minable="true"
							resizable="true" id="userInfo" mask="true"
							width="800" height="300"
							title="${reviewUser.userName}(${reviewUser.realName})"
							href="${ctx}/cs/csUser/queryUserInfo_PA_csUserAction.action?user.userId=${reviewUser.userId}"
							target="dialog">
							<dt style="color: blue" title="手机号:${reviewUser.phone},办公电话:${reviewUser.telPhone},邮箱:${reviewUser.email}">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${reviewUser.userId}" />
								</dt></a></td>
							<td><s:property value="qualityByName" /></td>
							<td><s:date name="qualityDate" format="yyyy-MM-dd HH:mm:ss"/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			  <div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select cssClass="" list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="currentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'showQuqlityChangePersonList')">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" rel="showQuqlityChangePersonList" totalCount="${currentPage.total}"currentPage="${currentPage.pageNo}"
			     numPerPage="${currentPage.pageSize}" pageNumShown="10"></div>
			 </div> 
		</div> 
	  	 <div class="pageFormdiv">
			<button  type="button" class="but_blue" onclick="qualityModify()">抽检修改</button>
		</div>
</div>
<script type="text/javascript">
$(function(){
	$("#tbodyChangePerson", navTab.getCurrentPanel()).find("tr").each(function(){			
		var reg = new RegExp(",","g");//替换
		var _policyCode=$(this).find("td#policyCode").text();
		_policyCode=_policyCode.replace(reg,"<br/>");
		$(this).find("td").eq(3).html(_policyCode);		
		var reg2 = new RegExp("、","g");//替换
		var policyOrganCode=$(this).find("td#policyOrganCode").text();
		policyOrganCode=policyOrganCode.replace(reg2,"<br/>");
		$(this).find("td#policyOrganCode").html(policyOrganCode);
	});	
});

function searchPagePersonList2(){
	debugger;
	var $obj2 = $("#pagerForm[name='ChangePerson']", navTab.getCurrentPanel());
	$obj2.submit();
}
/**
 * 质检修改
 *
 */
function qualityModify(){
	var $obj1 = $("#tbodyChangePerson", navTab.getCurrentPanel());
	var qualityId = $($obj1).find("input#qualityIdradio:checked").val();
	if(qualityId == null || qualityId == ''){
		 alertMsg.error("请选择质检任务。");
		 return false;
	} 
	var title = "抽检修改";	
	var fresh = eval("true");
	var external = eval("false"); 
    var tabid="抽检修改";
   	var url ="${ctx }/cs/csQuality/qualityChange_PA_csQualityErrorScoreAction.action?qualityInspectionInfoVO.qualityId=" +qualityId+"&callBackFunctionChange=searchPagePersonList2";
    navTab.openTab(tabid, url, {
		title : title,
		fresh : fresh,
		external : external
	});	 
}

</script>