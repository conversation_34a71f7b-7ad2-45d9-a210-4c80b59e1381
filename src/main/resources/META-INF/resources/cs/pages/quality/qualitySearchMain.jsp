<!-- 质检抽检查询-查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<%--  <form id="saveRevieweErrCfg" onsubmit="return divSearch(this, 'queryShareTaskDiv');" method="post">
 	 <input type="hidden" name="pageNum" value="${pageNum}" /> 
	 <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> 
	 <input type="hidden" id="applyCode" name="qualityInspectionInfoVO.applyCode"  /> 
	 <input type="hidden" id="policyCode" name="qualityInspectionInfoVO.policyCode"/> 
	 <input type="hidden" id="orangeName" name="qualityInspectionInfoVO.orangeName"/> 
	 <input type="hidden" id="serviceCode" name="qualityInspectionInfoVO.serviceCode"/> 
	 <input type="hidden" id="qualityPlanName" name="qualityInspectionInfoVO.qualityPlanName"/> 
	 <input type="hidden" id="qualityPlanCode" name="qualityInspectionInfoVO.qualityPlanCode"/> 
	 <input type="hidden" id="cfgValidateTime" name="reviewErrCfgVO.cfgValidateTime"/> 
	 <input type="hidden" id="qualityOrigin" name="qualityInspectionInfoVO.qualityOrigin"/> 
</form> --%>
<div class="panel">
<div class="pageFormContent">
<h1>抽检查询</h1>
	 <form id="messageForm" action="${ctx }/cs/csQuality_qpm/qualitySearch_PA_qualityPlanManageAction.action"
		 onsubmit="return divSearch(this, 'showQuqlitySerach')" method="post"> 
	 	<div class="panel">
	 	<h1>查询条件</h1>
			<div class="pageFormContent">
				<dl>
					<dt>计划代码</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.applyCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>计划名称</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.policyCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>执行起始日期</dt>
					<dd><input id="startTime" type="text" class="date" name="qualityInspectionInfoVO.applyCode" maxlength="20" readonly="readonly"/></dd>
				</dl>
				<dl>
					<dt>执行终止日期</dt>
					<dd><input id="endTime" type="text" class="date"  name="qualityInspectionInfoVO.policyCode" maxlength="20" readonly="readonly"/></dd>
				</dl>
				<dl>
					<dt>保全受理号</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.applyCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>抽检人员</dt>
					<dd><input type="text" name="qualityInspectionInfoVO.applyCode" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>抽检结论</dt>
					<dd><select style="background-position: 100% -50px; color: #183152;" name="qualityInspectionInfoVO.qualityOrigin">
							<option value="">请选择</option>
							<option value="1">合格</option>
							<option value="2">不合格</option>
							<option value="3">复核有误</option>
						</select>
					</dd>
				</dl>
				<dl>
					<dd><button  type="button" onclick="searchPageList()">查询</button></dd>
				</dl>		
			</div>	
		</div>
	  </form> 
  </div>
</div>
<div class="pageFormContent">
<div id="showQuqlitySerach"></div>
</div>

<script type="text/javascript">
/**
 * 查询
 */
function searchPageList(){
	var $obj = $("#messageForm", navTab.getCurrentPanel());
	var startTime = $("#startTime").val();
	var endTime = $("#endTime").val();
	if("" != t1 && "" != t2){
		var t1 = (new Date(startTime)).getTime();
		var t2 = (new Date(endTime)).getTime();
		if(t1 > t2){
			alertMsg.info("执行开始日期大于执行结束日期！");
			return false;
		}
		
	}
	$obj.submit();
}




</script>