<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.core.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.widget.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/assets/prettify.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.js"></script>

<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/jquery-ui.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/assets/prettify.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.css" />

<form id="pagerForm" method="post" style="display:none;"
	action="${ctx }/cs/csQuality_qpm/queryQulityPlanTask_PA_qualityPlanManageAction.action"
	onsubmit="return navTabSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<div class="pageContent" layoutH="36px">
<form action="${ctx }/cs/csQuality_qpm/queryQulityPlanTask_PA_qualityPlanManageAction.action" id="queryQulityPlanTask"
		onsubmit="return navTabSearch(this);" method="post" rel="pagerForm" class="pageForm required-validate">
		<input id="qualityPlanStatusHidden" type="hidden" value="${qualityPlanVO.qualityPlanStatus}"/>
		<input type="hidden" name="numPerPage" value="10" />
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">质检计划查询条件
		</h1>
	 </div>
	<div class="pageFormInfoContent" >
				<dl>
					<dt>计划制定机构<font color="red">*</font>：</dt>
					<dd >
						<input id="insertedOrgCode" name="qualityPlanVO.insertedOrgCode" 
							value="${qualityPlanVO.insertedOrgCode}" type="text" size="3"
							class="organ" clickId="menuBtn" showOrgName="organNameId" style="width:16%;border-right:0"/>
					    <input id="organNameId"  type="text" size="13" class="public_textInput" style="width:55%"
							readOnly value="<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${qualityPlanVO.insertedOrgCode}"/>"/> <a id="menuBtn" class="btnLook" href="#"></a>
					</dd>
				</dl>
				<dl style="width: 25%">
					<dt>质检计划状态：</dt>
					<dd>
					<select id="qualityPlanStatus" multiple="multiple" 
						name="qualityPlanVO.qualityPlanStatus" selectType="selectMulti" 
						displayname="质检计划状态" isRequired="false" style="padding-left:20%">
						<option value="01" >待发布</option>
						<option value="02" >质检中</option>
						<option value="03" >质检完成</option>
					</select>
					</dd>
				</dl>
				<dl>
					<dt  style="width: 36%">质检计划制定日期起止期<font color="red">*</font>：</dt>
					<dd  style="width: 58%"><input id="makeTimeStart" type="text" class="date" name="qualityPlanVO.makeTimeStart" value="<s:date name="qualityPlanVO.makeTimeStart" format="yyyy-MM-dd" />" style="width: 35%" /><span>&nbsp;&nbsp;至</span><input id="makeTimeEnd" type="text" class="date" name="qualityPlanVO.makeTimeEnd" value="<s:date name="qualityPlanVO.makeTimeEnd" format="yyyy-MM-dd" />" style="width: 35%" /></dd>
				</dl><br>
				<dl>
					<dt>质检计划号：</dt>
					<dd><input type="text" id="planNo" name="qualityPlanVO.planNo" value="${qualityPlanVO.planNo}"></dd>    
				</dl>
				<dl  style="width: 25%;padding-top: 5px">
					<dt>质检计划名称：</dt>
					<dd><input type="text" id="planName" name="qualityPlanVO.planName" value="${qualityPlanVO.planName}"></dd>    
				</dl>
				<dl>
					<dt   style="width: 39%">质检截止日期起止期：</dt>
					<dd   style="width: 58%"><input id="qualityEndDateStart" type="text" class="date" name="qualityPlanVO.qualityEndDateStart" value="<s:date name="qualityPlanVO.qualityEndDateStart" format="yyyy-MM-dd" />"  style="width: 37%" /><span>&nbsp;&nbsp;至</span><input id="qualityEndDateEnd" type="text" class="date" name="qualityPlanVO.qualityEndDateEnd" value="<s:date name="qualityPlanVO.qualityEndDateEnd" format="yyyy-MM-dd" />" style="width: 37%" /></dd>
				</dl>
				<br>
				<dl>
					<dt>创建人姓名：</dt>
					<dd><input type="text" id="makerName" name="qualityPlanVO.makerName" value="${qualityPlanVO.makerName}"></dd>    
				</dl>
				<dl  style="width: 30%;padding-top: 5px">
					<dt>质检类型：</dt>
					<dd><Field:codeTable id="qualityType"  cssClass="combox" nullOption="true" name="qualityPlanVO.qualityType" tableName="APP___PAS__DBUSER.T_CS_QUALITY_TYPE" value="${qualityPlanVO.qualityType }"/></dd>    
				</dl>
 				<dl>
					<dt></dt>
					<dd>
					<s:if test="qualityPlanVO.isOnlyOwnQuery==1">
					 <input type="checkBox" checked="checked" id="isOnlyOwnQuery"  name="qualityPlanVO.isOnlyOwnQuery" value="1"/>
					</s:if>
					<s:else>
					<input type="checkBox" id="isOnlyOwnQuery"  name="qualityPlanVO.isOnlyOwnQuery" value="1"/>
					</s:else>
					仅查询本人创建任务
					</dd>    
				</dl> 
			  <div class="pageFormdiv"> 
				<button type="button" class="but_blue" onclick="queryQulityPlanTask()" id="queryQulityPlanTaskButton">质检计划查询</button>
			  </div> 
	</div>
</form>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">质检计划查询结果
		</h1>
    </div>
	<div class="pageContent"  style="width:100%;">
	  <div class="tabdivclass">
			<table class="list" style="width: 100%;" >
				<thead>
					<tr>
						<th>选择</th>
						<th>质检计划号</th>
						<th>质检计划名称</th>
						<th>创建人</th>
						<th>计划制定机构</th>
						<th>上传任务总数据量</th>
						<th>抽检质检任务总数据量</th>
						<th>分发至本机构的质检任务总量</th>
						<th>质检分发用户</th>
						<th>质检计划状态</th>
						<th>质检计划制定时间</th>
						<th>质检截止日期</th>
						<th>查看/下载明细</th>
					</tr>
				</thead>
				<tbody id="planTBody">
				<s:iterator value="currentPage.pageItems"  status="st">
						<tr>
							<td>
							<s:if test="userId == makerId">
							<input type="radio" name="qualityPlanId"  value='${qualityPlanId }'/>
							</s:if>
							</td>
							<td>${planNo }</td>
							<td>${planName }</td>
							<td>${makerName }</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${insertedOrgCode}"/></td>
							<td>${uploadTotal }</td>
							<td>${selectedTotal }</td>
							<td>${allocateTotal }</td>
							<td>
							 <s:if test="allocateBy!=null">
							 ${allocateByName}(<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${allocateBy}"/>)
							 </s:if>
							</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_QUALITY_PLAN_STATUS" value="${qualityPlanStatus}"/></td>
							<td><s:date name="makeTime" format="yyyy-MM-dd HH:mm:ss"/></td>
							<td><s:date name="qualityEndDate" format="yyyy-MM-dd"/></td>
							<td><a target="navTab" href="${ctx }/cs/csQuality_qpm/initDownLoadPage_PA_qualityPlanManageAction.action?qualityPlanVO.qualityPlanId=${qualityPlanId}"
				title="查看/下载明细"><button class="but_blue" type="button" >查看/下载明细</button></a></td>
						</tr>
				</s:iterator>
				</tbody>
			</table>
			 <div class="panelBar" >
		        <div class="pages">
		            <span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
							onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
						</s:select> <span>条，共${currentPage.total}条</span>        
				</div>
		        <div id="paginationDiv" class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
		        	pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
			</div>
			</div>
			<div class="pageFormdiv" style="text-align: left;margin-left: 10px">
				<a target="navTab" 
				href="${ctx }/cs/csQuality_qpm/loadQualityPlanAdd_PA_qualityPlanManageAction.action"
				title="新建质检计划"><button class="but_blue" type="button" style="margin-top: 10px;">新建质检计划</button></a>
				<a target="dialog" id="loadQualityPlanAllocate" rel="loadQualityPlanAllocate" width="1400" height="900"
				href="${ctx }/cs/csQuality_qpm/loadQualityPlanAllocate_PA_qualityPlanManageAction.action"
				title="分发质检计划"  style="display: none"></a><button class="but_blue" onclick="checkQualityPlanAllocate()" type="button" style="margin-top: 10px;">分发质检计划</button>
                <button class="but_blue" type="button" onclick="withDrawQulityPlan()" style="margin-top: 10px;">撤回质检计划</button>
				<a target="navTab" id ="loadQualityPlanByID"
				href="${ctx }/cs/csQuality_qpm/loadQualityPlanByID_PA_qualityPlanManageAction.action"
				title="修改质检计划" style="display: none"></a><button class="but_blue" type="button" onclick="checkQualityPlanByID()" style="margin-top: 10px;">修改质检计划</button>
				<button onclick="cancelQualityPlan()" type="button" class="but_blue">作废质检计划</button>
			</div>
	</div>
</div>
<script type="text/javascript">
$(document).ready(function(){
	var organNameId = $("#organNameId",navTab.getCurrentPanel()).val();
	var specificChar = "-";
	var position = organNameId.indexOf(specificChar);
	var result = organNameId.substring(position + 1);
	$("#organNameId",navTab.getCurrentPanel()).val(result);
	// 初始化多选下拉框
	init('qualityPlanStatus', 'qualityPlanStatusHidden');
})
  // 初始化多选下拉框  参数说明：selectId：多选下拉框的ID hiddenSelectId：与多选下拉框字段对应的隐藏域ID
function init(selectId, hiddenSelectId) {
	$("select[selectType='selectMulti']").multiselect({
		noneSelectedText : "==请选择==",
		checkAllText : "全选",
		uncheckAllText : '全不选',
		selectedList : 1
	});
	$("select[selectType='selectSingle']").multiselect({
		multiple : false,
		header : "请选择",
		noneSelectedText : "==请选择==",
		selectedList : 1
	});
	//创建数组
	var csServices = new Array();
	//获取页面核保决定查询条件的值
	var csServiceQueryContidion = $("#" + hiddenSelectId, navTab.getCurrentPanel()).val();
	if (csServiceQueryContidion != null && csServiceQueryContidion != "" && csServiceQueryContidion.indexOf(",") != -1) {
		var csServiceArr = csServiceQueryContidion.split(",");
		for (var i = 0; i < csServiceArr.length; i++) {
			csServices[i] = csServiceArr[i];
		}
		//给页面核保决定查询条件附上上一次选择的值
		$("#" + selectId, navTab.getCurrentPanel()).val(csServices);
		$("#" + selectId, navTab.getCurrentPanel()).multiselect("refresh");
	} else if (csServiceQueryContidion != null && csServiceQueryContidion != "" && csServiceQueryContidion.indexOf(",") == -1) {
		csServices[0] = csServiceQueryContidion;
		//给页面核保决定查询条件附上上一次选择的值
		$("#" + selectId, navTab.getCurrentPanel()).val(csServices);
		$("#" + selectId, navTab.getCurrentPanel()).multiselect("refresh");
	}
}

  function queryQulityPlanTask(){
	    var insertedOrgCode =  $("#insertedOrgCode",navTab.getCurrentPanel()).val(); 
	    var organNameId =  $("#organNameId",navTab.getCurrentPanel()).val(); 
	    if(insertedOrgCode==null || insertedOrgCode==""){
			alertMsg.info("计划制定机构必填！");
			return false;
	    }else if(organNameId==null || organNameId==""){
			alertMsg.info("请通过机构树控件选择计划制定机构！");
			return false;
	    }
	    var makeTimeStart =  $("#makeTimeStart",navTab.getCurrentPanel()).val(); 
	    if(makeTimeStart==null || makeTimeStart==""){
			alertMsg.info("质检计划制定日期起期必填！");
			return false;
	    }
	    var makeTimeEnd =  $("#makeTimeEnd",navTab.getCurrentPanel()).val(); 
	    if(makeTimeEnd==null || makeTimeEnd==""){
			alertMsg.info("质检计划制定日期止期必填！");
			return false;
	    }
		var $Obj1 = $("#queryQulityPlanTask",navTab.getCurrentPanel()); 
		var action="${ctx }/cs/csQuality_qpm/queryQulityPlanTask_PA_qualityPlanManageAction.action";
		$Obj1.attr('action',action);
		$Obj1.submit();
  }
function checkQualityPlanAllocate(){
	var qualityPlanId = $('input[type=radio][name=qualityPlanId]:checked',navTab.getCurrentPanel()).val();
	if(qualityPlanId==null || qualityPlanId==""){
		alertMsg.info("请选择质检计划！");
		return false;
	}
	$.ajax({
		type : 'POST',
		url : "${ctx }/cs/csQuality_qpm/checkQualityPlanStatus_PA_qualityPlanManageAction.action",
		data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
		cache : false,
		success : function(data) {
			var json = DWZ.jsonEval(data);
			if (json.statusCode == 300) {
				alertMsg.error("仅支持待发布状态质检计划进行分配，请确认！");
			}else{
				$("#loadQualityPlanAllocate",navTab.getCurrentPanel()).attr("href","${ctx }/cs/csQuality_qpm/loadQualityPlanAllocate_PA_qualityPlanManageAction.action?qualityPlanVO.qualityPlanId="+qualityPlanId);
				$("#loadQualityPlanAllocate",navTab.getCurrentPanel()).click();
			}
		},
		error : function() {
			alertMsg.err("失败");
		}
});
}

function checkQualityPlanByID(){
	var qualityPlanId = $('input[type=radio][name=qualityPlanId]:checked',navTab.getCurrentPanel()).val();
	if(qualityPlanId==null || qualityPlanId==""){
		alertMsg.info("请选择质检计划！");
		return false;
	}
	$.ajax({
		type : 'POST',
		url : "${ctx }/cs/csQuality_qpm/checkQualityPlanStatus_PA_qualityPlanManageAction.action",
		data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
		cache : false,
		success : function(data) {
			var json = DWZ.jsonEval(data);
			if (json.statusCode == 300) {
				alertMsg.error("仅支持【待发布】状态质检计划进行修改，请确认！");
			}else{
				$("#loadQualityPlanByID",navTab.getCurrentPanel()).attr("href","${ctx }/cs/csQuality_qpm/loadQualityPlanByID_PA_qualityPlanManageAction.action?qualityPlanVO.qualityPlanId="+qualityPlanId);
				$("#loadQualityPlanByID",navTab.getCurrentPanel()).click();
			}
		},
		error : function() {
			alertMsg.err("失败");
		}
});
}
function cancelQualityPlan(){
	var qualityPlanId = $('input[type=radio][name=qualityPlanId]:checked',navTab.getCurrentPanel()).val();
	if(qualityPlanId==null || qualityPlanId==""){
		alertMsg.info("请选择质检计划！");
		return false;
	}
	$.ajax({
		type : 'POST',
		url : "${ctx }/cs/csQuality_qpm/checkQualityPlanStatus_PA_qualityPlanManageAction.action",
		data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
		cache : false,
		success : function(data) {
			var json = DWZ.jsonEval(data);
			if (json.statusCode == 300) {
				alertMsg.error("仅支持【待发布】状态质检计划进行作废，请确认！");
			}else{
				$.ajax({
					type : 'POST',
					url : "${ctx }/cs/csQuality_qpm/cancelQualityPlan_PA_qualityPlanManageAction.action",
					data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
					cache : false,
					success : function(data) {
						var json = DWZ.jsonEval(data);
						if (json.statusCode == 300) {
							alertMsg.error(json.message);
						}else{
							$("#paginationDiv",navTab.getCurrentPanel()).find(".goto").click();
							alertMsg.correct("作废质检计划成功！");
						}
					},
					error : function() {
						alertMsg.err("失败");
					}
			});
			}
		},
		error : function() {
			alertMsg.err("失败");
		}
});
}

function withDrawQulityPlan(){
	var qualityPlanId = $('input[type=radio][name=qualityPlanId]:checked',navTab.getCurrentPanel()).val();
	if(qualityPlanId==null || qualityPlanId==""){
		alertMsg.info("请选择质检计划！");
		return false;
	}
				$.ajax({
					type : 'POST',
					url : "${ctx }/cs/csQuality_qpm/withDrawQulityPlan_PA_qualityPlanManageAction.action",
					data : "qualityPlanVO.qualityPlanId=" + qualityPlanId,
					cache : false,
					success : function(data) {
						var json = DWZ.jsonEval(data);
						if (json.statusCode == 300) {
							alertMsg.error(json.message);
						}else{
							$("#paginationDiv",navTab.getCurrentPanel()).find(".goto").click();
							alertMsg.correct("撤回质检计划成功！");
						}
					},
					error : function() {
						alertMsg.err("失败");
					}
			});
		
}


</script>