<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<form id="pagerForm" method="post" style="display:none;"
	action="${ctx }/cs/csCheckPemp/showQualityPlan_PA_checkPempAction.action"
	onsubmit="return navTabSearch(this);">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>
<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png">查询条件
	</h1>
 </div>
<div class="pageContent" layoutH="10">
<form action="${ctx }/cs/csCheckPemp/showQualityPlan_PA_checkPempAction.action" id="showCheckPempPage" 
		onsubmit="return navTabSearch(this);" method="post" rel="pagerForm" class="pageForm required-validate">
	<div class="pageFormInfoContent">
				<dl>
					<dt>计划代码</dt>
					<dd><input type="text" id="planNo" name="qualityPlanVO.planNo" maxlength="20" value="${qualityPlanVO.planNo }" /></dd>
				</dl>
				<dl>
					<dt>计划名称</dt>
					<dd><input type="text" id="planName" name="qualityPlanVO.planName" maxlength="20" value="${qualityPlanVO.planName }" /></dd>
				</dl>
				<dl>
					<dt>创建人</dt>
					<dd><input type="text" id="makerName" name="qualityPlanVO.makerName" maxlength="100" value="${qualityPlanVO.makerName }" /></dd>
				</dl>  
			<div class="pageFormdiv">
			   <button onclick="checkSubmit()" class="but_blue" type="button" >查询</button>
			</div>
		</div>
</form>
<form action="${ctx }/cs/csCheckPemp/checkCheckPemp_PA_checkPempAction.action" method="post" id="checkCheckPemp">
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">查询结果
		</h1>
   </div>
	<div class="tabdivclass">
			<table class="list" style="width: 100%">
				<thead>
					<tr>
						<th>选择</th>
						<th>计划代码</th>
						<th>计划名称</th>
						<th>是否有效</th>
						<th>创建时间</th>
						<th>创建人</th>
						<th>创建机构</th>
					</tr>
				</thead>
				<tbody id="planTBody">
					<s:iterator value="currentPage.pageItems" id="" status="st">
						<tr>
							<td><input type="radio" name="qualityPlanId" onclick="getId(this)" value="${qualityPlanId }"/></td>
							<td>${planNo }</td>
							<td>${planName }</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${validFlag}"/></td>
							<td><s:date name="insertTime" format="yyyy-MM-dd"/></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${insertBy}"/></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${insertedOrgCode}"/></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
				 <div class="panelBar" >
			       <div class="pages">
			           <span>显示</span>
						<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
								onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
						</s:select> <span>条，共${currentPage.total}条</span>        
					</div>
			        <div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
			        	pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
				</div> 
			</div>
		 	<div class="pageFormdiv"> 
				<button onclick="checkThis()" class="but_blue" type="button" >抽档</button>
				<button class="close but_gray" type="button" >返回</button>
			</div> 
	        <div id="checkPempJspDIV" style="display:none;">
				<s:include value="checkPemp_check.jsp"></s:include>
			</div>   
</form>
</div>
<script type="text/javascript">
	//点击查询
	function checkSubmit(){
		var $form = $("#showCheckPempPage",navTab.getCurrentPanel());
		$form.submit();
	}
	
	//点击抽档
	function checkThis(){
		
		var $form = $("#checkCheckPemp",navTab.getCurrentPanel());
		var tBody = $("#planTBody",navTab.getCurrentPanel());
		var radio = $(tBody).find("input:radio:checked");

		if(radio.length == 0){
			alertMsg.info("请选择质检计划");
			return false;
		}else{
			$("#saveCheckSub").remove();
			$("#checkPempJspDIV").css("display","block");
			var action = "${ctx }/cs/csCheckPemp/checkCheckPemp_PA_checkPempAction.action";
			$form.attr('action', action);
			$form.attr("onsubmit","return divSearch(this, 'checkPempJspDIV')");
			$form.submit();
		}
	}
	function getId(obj){
		var id = $(obj).attr("value");
		ids = id;
	}
	
</script>