<!-- 差错基本-要点信息-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type = "text/javascript" src="${ctx}/cs/pages/quality/qualityCommon.js"></script>

<div class="pageContent" layoutH="36px" style="display:block;">
	<form id="showErrorInfo" onsubmit="return divSearch(this,'getCsTaskDIV')" method="post">
		<input type="hidden" name="numPerPage" value="10" />
		<input type="hidden" id="showProjectVO" value = "${qualityProjectVO }"/>
		<div class="panel">
			<h1>差错基本信息</h1>				
			<div class="pageFormContent">
				<dl>
					<dt>差错代码</dt>
					<dd><input id="projNo" type="text" name="qualityProjectVO.projectNo"  value="${qualityProjectVO.projectNo}"  maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>差错名称</dt>
					<dd><input id="projName" type="text" name="qualityProjectVO.projectName" value="${qualityProjectVO.projectName }" maxlength="20"/></dd>
				</dl>		
				<dl>
					<dt>版本号</dt>
					<dd><input type="text" name="qualityProjectVO.version" value="0.0.1" maxlength="20" readonly="readonly" disabled="disabled"/></dd>
				</dl>
				<dl>
					<dt>扣分标准</dt>
					<dd><input id="projAccountScore" type="text" name="qualityProjectVO.accountScore" value="${qualityProjectVO.accountScore }" maxlength="20"/></dd>
				</dl>
				<dl>
					<dt>是否有效</dt>
					<dd><Field:codeTable cssClass="combox" nullOption="true" name="qualityProjectVO.validFlag" tableName="APP___PAS__DBUSER.T_YES_NO" value="${qualityProjectVO.validFlag}" /></dd>    
				</dl>
				
			</div>	
		</div>
		
		<div class="panel">	
	<!-- 差错要点信息 -->
		<h1>差错要点信息</h1>				
		<div class="pageFormContent">
				<dl>
					<dt>要点描述</dt>
					<dd><input id="pointDesc" type="text" name="qualityPrjPointVO.pointDesc"  maxlength="20"/></dd>
				</dl>
				<div class="button">
					<div class="buttonContent">
						<button type="button" onclick="addProPointInfo();">新增</button>
					</div>
				</div>
		</div>	
		<div id="mainPrjPoint" class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid"> 
			<table class="list" id="appDoctable" style="width:100%" table_saveStatus="1" >
				<thead>
					<tr>
						<th>序号</th>
						<th>差错代码</th>
						<th>差错名称</th>
						<th>扣分标准</th>
						<th>要点描述</th>
						<th>是否有效</th>
						<th>删除</th>
					</tr>
				</thead>
				<tbody align="center" id="contentDetails">
					<s:iterator value="qualityPrjPointListVO" status="st" id="qr">
						<tr>
							<td><s:property value="#st.index+1" /><input type="hidden" id="main_"${projectPointId} name="qualityPrjPointVO.projectPointId" value='${projectPointId}'/></td>
							<td><s:property value="projectNo" /></td>
							<td><s:property value="projectName" /></td>
							<td ><s:property value="pointScore" /></td>
							<td><s:property value="pointDesc" /></td>
							<td><Field:codeTable value="${validFlag }" cssClass="combox" name="qualityProjectVO.validFlag" tableName="APP___PAS__DBUSER.T_YES_NO"/></td>
							<td><a href="#" class="BtnDel" onclick="delPriPointInfo(this,${projectPointId})">删除</a></td>
						</tr>
					</s:iterator> 
				</tbody>
			</table>
		</div> 
			<div class="panelBar">
				<table style="width:100%">
					<tr>
						<td></td>
						<td style="width:200px">
							<div class="button">
								<div class="buttonContent">
									<button type="button" onclick="saveQualityRojectInfo('publicEntryForm','getCsTaskDIV');">提交</button>
								</div>
							</div>
						</td>
						<td></td>
						<td style="width:200px">
							<div class="button">
								<div class="buttonContent">
									<button type="button" onclick="queryPublicEntryForm('publicEntryForm','getCsTaskDIV');">返回</button>
								</div>
							</div>
						</td>
					</tr>
				</table>
			</div>	
		</div>
	</form>

</div>
