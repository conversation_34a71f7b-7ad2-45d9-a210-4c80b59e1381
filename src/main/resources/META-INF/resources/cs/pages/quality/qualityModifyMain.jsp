<!-- 质检公共任务池 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<input type="hidden" id="cfgValidateTime" name="reviewErrCfgVO.cfgValidateTime"/> 
<div class="pageContent" layoutH="10" id="zhu">
	 <form id="messageForm" action="${ctx }/cs/csQuality/queryQualityInspectionPageList_PA_csQualityErrorScoreAction.action" 
		 onsubmit="return divSearch(this, 'showQuqlityModifyList')" method="post"> 
		 <input type="hidden" id="taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="1" /> 
		 <input type="hidden" name="numPerPage" value="10" /> 
		 <div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">共享任务池查询条件
				</h1>
	      </div>
		<div class="pageFormInfoContent">
					<dl >
						<dt >质检机构<font color="red">*</font></dt>
						<dd >
							<input id="qualityByOrg" name="qualityInspectionInfoVO.qualityByOrg"
								value="${qualityInspectionInfoVO.qualityByOrg}" type="text" size="3"
								class="organ" clickId="menuBtnorg" showOrgName="checkOrganNameId" style="width:30px;border-right:0"/>
						    <input id="checkOrganNameId"  type="text" size="13" class="public_textInput" style="width:110px"
						    	value="<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${qualityInspectionInfoVO.qualityByOrg}"/>"
								readOnly /> <a id="menuBtnorg" class="btnLook" href="#"></a>
						</dd>
					</dl>
					<dl>
						<dt ><P style="WORD-WRAP: break-word">是否查询下级机构任务<font color="red">*</font></P></dt>
						<select name="qualityInspectionInfoVO.organFlag" id="organFlag">
							<option value="no"
								<s:if test="qualityInspectionInfoVO.organFlag eq 'no' || qualityInspectionInfoVO.organFlag == null"> selected </s:if>>否
							</option>
							<option value="yes"
								<s:if test="qualityInspectionInfoVO.organFlag eq 'yes'"> selected </s:if>>是
							</option>
						</select>
					</dl>
					<dl>
						<dt>保全受理号</dt>
						<dd><input id ="step1_acceptCode"  type="text" name="qualityInspectionInfoVO.acceptCode" value="${qualityInspectionInfoVO.acceptCode}" maxlength="20"/></dd>
					</dl>
					<dl>
						<dt>保单号</dt>
						<dd><input id ="step1_policyCode"  type="text" name="qualityInspectionInfoVO.policyCode"  value="${qualityInspectionInfoVO.policyCode}"maxlength="20"/></dd>
					</dl>
					<dl >
						<dt >保单管理机构</dt>
						<dd >
							<input id="step1_organCode" name="qualityInspectionInfoVO.policyOrganCode"
								value="${qualityInspectionInfoVO.policyOrganCode}" type="text" size="3"
								class="organ" clickId="menuBtn" showOrgName="organNameId" style="width:30px;border-right:0"/>
						    <input id="organNameId"  type="text" size="13" class="public_textInput" style="width:110px"
								readOnly /> <a id="menuBtn" class="btnLook" href="#"></a>
						</dd>
					</dl>
					<dl>
						<dt>质检计划代码</dt>
						<dd><input id ="step1_qualityPlanCode"  type="text" name="qualityInspectionInfoVO.qualityPlanCode" value="${qualityInspectionInfoVO.qualityPlanCode}" maxlength="20"/></dd>
					</dl>
					<dl>
						<dt>质检计划名称</dt>
						<dd><input id ="step1_qualityPlanName"  type="text" name="qualityInspectionInfoVO.qualityPlanName" value="${qualityInspectionInfoVO.qualityPlanName}" maxlength="100"/></dd>
					</dl>	
					<dl>
						<dt>质检计划建立人</dt>
						<dd><input id ="makerName"  type="text" name="qualityInspectionInfoVO.makerName" value="${qualityInspectionInfoVO.makerName}" maxlength="100"/></dd>
					</dl>
					<dl >
						<dt>保全项目</dt>
						<dd>
							<Field:codeTable id ="step1_serviceCode"   cssClass="combox" nullOption="true" name="qualityInspectionInfoVO.serviceCode" 
							tableName="APP___PAS__DBUSER.T_SERVICE" value="${qualityInspectionInfoVO.serviceCode}"  disabled="disabled" />
						</dd>
					</dl>
					<dl>
						<dt >是否为整改件</dt>
						<select name="qualityInspectionInfoVO.modifyFlag" id="modifyFlag">
							<option value=""> </option>
							<option value="no"
								<s:if test="qualityInspectionInfoVO.modifyFlag == 'no'"> selected </s:if>>否
							</option>
							<option value="yes"
								<s:if test="qualityInspectionInfoVO.modifyFlag == 'yes'"> selected </s:if>>是
							</option>
						</select>
					</dl>
				<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="searchPageList()" >查询</button>
				</div>
				
		</div>
	</form>
	<div class="pageContent">
	   <div id="showQuqlityModifyList" ></div>
	</div> 
	<div class="divfclass" <s:if test="showAllocateInspect!=1">style="display: none"</s:if>>
		<h1>
			<img src="cs/img/icon/tubiao.png">任务分配
		</h1>
	</div>
	<div class="pageFormInfoContent" <s:if test="showAllocateInspect!=1">style="display: none"</s:if>>
	   <div id="allocateInspectTaskDiv" >
	   		<input type="hidden" id="currentUserOrg" value="${qualityInspectionInfoVO.qualityByOrg}" /> 
	   		<dl id="allocateQualityByOrg_dl">
				<dt >质检机构</dt>
				<dd >
					<input id="allocateQualityByOrg" name="allocateQualityByOrg"
						value="" type="text" size="3"
						class="organ" clickId="menuBtnAllocate" showOrgName="allocateOrganNameId" style="width:30px;border-right:0"/>
				    <input id="allocateOrganNameId"  type="text" size="13" class="public_textInput" style="width:110px"
				    	value="<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value=""/>"
						readOnly /> <a id="menuBtnAllocate" class="btnLook" href="#"></a>
				</dd>
			</dl>
			<dl id="newAgentCodeDl" >
				<dt>质检用户</dt>
				<dd>
					<input id="a_qualityBy"
						name="qualityInspectionInfoVO.recheckByCode" class=""
						type="text" onblur="checkUser(this)"
						value="${qualityInspectionInfoVO.recheckByCode }" />
					<a id="loadQualityPlanUser" target="dialog" width="1300" height="800" title="质检用户" class="btnLook"  href="${ctx }/cs/csQuality_qpm/loadQualityPlanUser_PA_qualityPlanManageAction.action?userBackId=a_qualityBy"></a>
				</dd>
			</dl>
			<div class="pageFormdiv">
				<button type="button" class="but_blue" onclick="allocateInspectTask()" >任务分配</button>
			</div>
	   </div>
	</div> 
	<form id="personForm" action="${ctx }/cs/csQuality/queryQualityInspectionPageList_PA_csQualityErrorScoreAction.action"
		 onsubmit="return divSearch(this, 'showQuqlityModifyPersonList')" method="post"> 
	 	<input type="hidden" id="taskPoolType" name="qualityInspectionInfoVO.taskPoolType" value="0" /> 
	 	<input type="hidden" name="numPerPage" value="10" /> 
	 	<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">个人任务池查询条件
			</h1>
      	</div>
		<div class="pageFormInfoContent">
			<dl>
				<dt>保全受理号</dt>
				<dd><input id ="step1_acceptCode"  type="text" name="qualityInspectionInfoVO.acceptCode" value="${qualityInspectionInfoVO.acceptCode}" maxlength="20"/></dd>
			</dl>
			<dl>
				<dt>保单号</dt>
				<dd><input id ="step1_policyCode"  type="text" name="qualityInspectionInfoVO.policyCode"  value="${qualityInspectionInfoVO.policyCode}"maxlength="20"/></dd>
			</dl>
			<dl >
				<dt >保单管理机构</dt>
				<dd >
					<input id="step1_organCodeperson" name="qualityInspectionInfoVO.policyOrganCode"
						value="${qualityInspectionInfoVO.policyOrganCode}" type="text" size="3"
						class="organ" clickId="menuBtn1" showOrgName="organNameId1" style="width:30px;border-right:0"/>
				    <input id="organNameId1"  type="text" size="13" class="public_textInput" style="width:110px"
						readOnly /> <a id="menuBtn1" class="btnLook" href="#"></a>
				</dd>
			</dl>
			<dl>
				<dt>质检计划代码</dt>
				<dd><input id ="step1_qualityPlanCode"  type="text" name="qualityInspectionInfoVO.qualityPlanCode" value="${qualityInspectionInfoVO.qualityPlanCode}" maxlength="20"/></dd>
			</dl>
			<dl>
				<dt>质检计划名称</dt>
				<dd><input id ="step1_qualityPlanName"  type="text" name="qualityInspectionInfoVO.qualityPlanName" value="${qualityInspectionInfoVO.qualityPlanName}" maxlength="100"/></dd>
			</dl>	
			<dl>
				<dt>质检计划建立人</dt>
				<dd><input id ="makerName"  type="text" name="qualityInspectionInfoVO.makerName" value="${qualityInspectionInfoVO.makerName}" maxlength="100"/></dd>
			</dl>
			<dl >
				<dt>保全项目</dt>
				<dd>
					<Field:codeTable id ="step1_serviceCode"   cssClass="combox" nullOption="true" name="qualityInspectionInfoVO.serviceCode" 
					tableName="APP___PAS__DBUSER.T_SERVICE" value="${qualityInspectionInfoVO.serviceCode}"  disabled="disabled" />
				</dd>
			</dl>
			<dl>
				<dt >是否为整改件</dt>
				<select name="qualityInspectionInfoVO.modifyFlag" id="modifyFlag">
					<option value=""> </option>
					<option value="no"
						<s:if test="qualityInspectionInfoVO.modifyFlag == 'no'"> selected </s:if>>否
					</option>
					<option value="yes"
						<s:if test="qualityInspectionInfoVO.modifyFlag == 'yes'"> selected </s:if>>是
					</option>
				</select>
			</dl>
			<div class="pageFormdiv">
				<button type="button" class="but_blue" onclick="searchPersonPageList()" >查询</button>
			</div>
			
		</div>
	</form>
	<div class="pageContent">
	   <div id="showQuqlityModifyPersonList" ></div>
	</div> 
</div>
<script type="text/javascript">

	//初始化的时候
	$(document).ready(function(){
		 // 获取当前页面容器
	    //var $panel = navTab.getCurrentPanel();
	    // 设置 tabId（如 bPageTab）
	    //$panel.attr("navTabId", "qualityModifyMain");
			searchPageList();
			searchPersonPageList();
			var currentUserOrg = $("#currentUserOrg").val();
			if(currentUserOrg.length>=8){
				$("#allocateQualityByOrg_dl").hide();
				$("#allocateQualityByOrg").val("");
			}
	});
/**
 * 共享池查询
 */
function searchPageList(){
	var qualityByOrg = $("#messageForm").find("input#qualityByOrg").val();
	var organFlag = $("#messageForm").find("input#organFlag").val();
	if(qualityByOrg == null || qualityByOrg == ''){
		alertMsg.error("请选择质检机构！");
		return;
	}
	var $obj = $("#messageForm", navTab.getCurrentPanel());
	$obj.submit();
}
//个人池查询
function searchPersonPageList(){
	//var currentUserId = $("#currentUserId").val();
	var $obj = $("#personForm", navTab.getCurrentPanel());
	$obj.submit();
}

//任务分配
function allocateInspectTask(){
	var qualityinput = $("#commList").find("input[name='qualityIds']:checked");
	var qualityIdStr = "";
	var currentUserId = $("#currentUserId").val();
	var currentUserOrg = $("#currentUserOrg").val();
	var allocateQualityByOrg = $("#allocateQualityByOrg").val();
	if(allocateQualityByOrg.indexOf(currentUserOrg)!=0){
		/* alertMsg.error("质检机构范围为登录用户下辖机构");
		return; */
	}
	var qualityByCode = $("#a_qualityBy").val();
	var isSame = false;
	var acceptCode = "";
	qualityinput.each(function (){
		qualityIdStr += $(this).val()+",";
		var insertOperatorCode = $(this).closest("tr").find("input#insertOperatorCode").val();
		var reviewUserCode = $(this).closest("tr").find("input#reviewUserCode").val();
		if(qualityByCode == insertOperatorCode || qualityByCode == reviewUserCode){
			isSame = true;
			acceptCode = $(this).closest("tr").find("td#commtr_acceptCode").html();
		}
	});
	qualityIdStr = qualityIdStr.substring(0, qualityIdStr.length-1)
	if(qualityIdStr == null || qualityIdStr == ''){
		alertMsg.error("请选择质检任务！");
		return;
	}
	if((allocateQualityByOrg == null || allocateQualityByOrg == '') 
			&&(qualityByCode == null || qualityByCode == '')){
		alertMsg.error("请录入质检机构或质检用户");
		return;
	}
	if(isSame){
		alertMsg.error("质检人与"+acceptCode+"录入人、质检人与复核人不可为同一人。");
		return;
	}
	$.ajax({
        type:'POST',
        url:'${ctx}/cs/csAccept/allocateInspectTask_PA_csQualityErrorScoreAction.action',
        data : {
        	 "qualityIdStr" : qualityIdStr,
        	 "allocateQualityByOrg" : allocateQualityByOrg,
        	 "qualityByCode": qualityByCode
        },
        dataType:"json",
        async : false,
		cache : false,
        success:function(data){
        	alertMsg.info("处理成功");
        	var $obj = $("#pagerForm[name='comm']", navTab.getCurrentPanel());
        	$obj.submit();
        }
    });
}
function checkUser(obg) {
	userName = $(obg).val();
	$.ajax({
		type: 'POST',
		url: "${ctx }/cs/csQuality_qpm/checkUser_PA_qualityPlanManageAction.action",
		data: "userVo.userName=" + userName,
		cache: false,
		success: function (data) {
			json = DWZ.jsonEval(data);
			if (json.statusCode == 300) {
				$(obg).val("");
				alertMsg.error(json.message);
			}
		},
		error: function () {
			alertMsg.err("失败");
		}
	});
}

//////////////////////////////////////////////////////////////////////////
//保存
function saveReviewErrcfsInf(){
	var $obj1 = $("#messageForm", navTab.getCurrentPanel());
	
	var radio = $($obj1).find("input:radio:checked", navTab.getCurrentPanel());
	var tdNodes = $(radio).parent().parent().children();
	//2代码 3差错名称  4明细  5受理-6录入
	//7复效  8-生效 9操作员 10-操作机构11操作日期
	if(radio.length == 0){
		 alertMsg.info("请选择保存项");
		return false;
	}
	 $.ajax({
		 url : "${ctx }/cs/csQuality/saveReviewErrCfgInf_PA_csQualityErrorScoreAction.action",
		 type : "post",
		 dataType : "json",
		 data : {
			"reviewErrCfgVO.cfgId" : cfgId,
			"reviewErrCfgVO.errType" : errType,
			"reviewErrCfgVO.errTypeName" : errTypeName,
			"reviewErrCfgVO.errDetail" : errDetail,
			"reviewErrCfgVO.acceptRate" : acceptRate,
			"reviewErrCfgVO.inputRate" : inputRate,
			"reviewErrCfgVO.reviewRate" : reviewRate,
			"reviewErrCfgVO.cfgValidateTime": cfgValidateTime
		 },
		 success : function(data){
			 if(data.statusCode == 300){
				 alertMsg.info("保存失败");
				 return false;
			 }else{
				 alertMsg.correct("保存成功");
				 var $obj1 = $("#messageForm", navTab.getCurrentPanel());
				 var url = "${ctx }/cs/csQuality/initErrorScore_PA_csQualityErrorScoreAction.action";
				 $obj1.attr("action", url);
				 $obj1.attr("onsubmit","return divSearch(this, 'messageForm')");
				 $obj1.submit();
			 }
		 }
	 });
	
}


/**
 * 删除
 */
function delPriPointInfo(obj, cfgId){
	
	 if (confirm("确定要删除改记录吗？")) {
		 
	 	 $.ajax({
			 url : "${ctx }/cs/csQuality/deleteScoreInfo_PA_csQualityErrorScoreAction.action",
			 type : "post",
			 dataType : "json",
			 data : {
				 "reviewErrCfgVO.cfgId" : cfgId
			 },
			 success : function(data){
				 if(data.statusCode == 300){
					 alertMsg.info("删除失败");
					 return false;
				 }else{
					alertMsg.correct("删除成功");
					$("#deleteInfo").val("ok"); var $obj1 = $("#messageForm", navTab.getCurrentPanel());
					var url = "${ctx }/cs/csQuality/initErrorScore_PA_csQualityErrorScoreAction.action?reviewErrCfgVO.cfgId="+cfgId;
					$obj1.attr("action", url);
					$obj1.attr("onsubmit","return divSearch(this, 'messageForm')");
					$obj1.submit();
				 }
			 }
		 });
	 } 
}



</script>