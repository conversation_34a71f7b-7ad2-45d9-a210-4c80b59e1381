<!-- 分红险红利通知书清单 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
﻿<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript">
	$(function() {
		var organCode = $("#organCode", navTab.getCurrentPanel()).val();
		if (organCode.length > 4) {
			$("#organCode", navTab.getCurrentPanel()).val("");
			$("#organName", navTab.getCurrentPanel()).val("");
		}

		var startYearMonth = $("#startDate", navTab.getCurrentPanel()).val().trim();
		var pageItemsSize = $("#pageItemsSize", navTab.getCurrentPanel()).val()
				.trim();
		if (startYearMonth != null && startYearMonth != ''
				&& (pageItemsSize == null || pageItemsSize == 0)) {
			alertMsg.warn('此时间范围无数据，请重新选择统计起期、止期。');
		}
	});

	function searchAction() {
		//判断必填项是否为空
		var organCode = $("#organCode", navTab.getCurrentPanel()).val().trim();
		var startDateStr = $("#startDate", navTab.getCurrentPanel()).val().trim();
		var endDateStr = $("#endDate", navTab.getCurrentPanel()).val().trim();
		if (organCode == null || organCode == '' || startDateStr == null
				|| startDateStr == '' || endDateStr == null || endDateStr == '') {
			alertMsg.warn('管理机构、统计起期、统计止期不能为空。');
			return false;
		}
		if (startDateStr > endDateStr) {
			alertMsg.warn('日期起期不能大于止期！');
			return false;
		}
		var sTime = new Date(startDateStr.replace(/\-/g, "/")); 
		var eTime = new Date(endDateStr.replace(/\-/g, "/")); 
		var days = (eTime.getTime()-sTime.getTime())/(24*3600*1000);
		if(days > 365){
			alertMsg.error("日期查询范围不能超过1年，请重新输入。");
			return false;
		} 

		var $form = $("#univerRepForm", navTab.getCurrentPanel());
		var action = "${ctx }/cs/bonus/queryList_PA_bonusDocumentListAction.action";
		$form.attr('action', action);
		$form.attr('onsubmit', "return navTabSearch(this)");
		$form.submit();
	}

	//导出清单数据
	function downloadFile() {
		var listId = $('input:radio[name="listId"]:checked').val();
		if (listId == '' || listId == null) {
			alertMsg.info("请选择要下载的文件。");
			return false;
		}

		$("#univerRepForm", navTab.getCurrentPanel()).attr("onsubmit", null);
		$("#univerRepForm", navTab.getCurrentPanel()).attr(
				"action",
				"${ctx }/cs/bonus/exportExcel_PA_bonusDocumentListAction.action?listId="
						+ listId);
		$("#univerRepForm", navTab.getCurrentPanel()).submit();
	}
</script>
<input
			type="hidden" id="pageItemsSize" name="currentPage.pageItems"
			value="${currentPage.pageItems.size()}">

<div id="pageHeaderDiv" class="pageContent" >
	<form id="pagerForm" method="post"
		action="cs/bonus/queryList_PA_bonusDocumentListAction.action">
		<input id="pageNumId" type="hidden" name="pageNum"
			value="${currentPage.pageNo}" /> <input id="numPerPageId"
			type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<form id="univerRepForm"
		action="cs/bonus/queryList_PA_bonusDocumentListAction.action"
		class="pageForm required-validate" method="post"
		onsubmit="return navTabSearch(this)" rel="pagerForm" width="150%">
		<div class="divfclass">
			<h1>
				<img src="cs/img/icon/tubiao.png">查询条件
			</h1>
		</div>
		<div id="searchBarDiv" class="pageFormInfoContent">
			<dl>
				<dt>
					管理机构<font style="color: red">*</font>
				</dt>
				<dd>
					<input id="organCode" name="univRepFileVO.organCode"
						value="${univRepFileVO.organCode}" type="text"
						style="float: left; width: 30px; border-right: 0px;"
						class="organ" clickId="organMenuBtn"
						showOrgName="organName" data-grade-in="01,02" />
					<input id="organName" name="univRepFileVO.organName"
						value="${univRepFileVO.organName}"
						style="float: left; width: 110px;" type="text" readOnly />
					<s:if test="univRepFileVO.organCode != null && univRepFileVO.organCode.length() < 5">
						<a id="organMenuBtn" class="btnLook" href="#" style="width: 20px;" />
					</s:if>
					<s:else>
						<a id="organMenuBtn1" class="btnLook" href="#" style="width: 20px;" />
					</s:else>
				</dd>
			</dl>
			<dl>
				<dt>
					统计起期<font style="color: red">*</font>
				</dt>
				<dd>
					<input type="expandDateYMD"
						value="${univRepFileVO.startYearMonth}"
						name="univRepFileVO.startYearMonth" id="startDate"
						class="required"> <a class="inputDateButton"
						href="javascript:;" id="">选择</a>
				</dd>
			</dl>
			<dl>
				<dt>
					统计止期<font style="color: red">*</font>
				</dt>
				<dd>
					<input id="endDate" type="expandDateYMD"
						name="univRepFileVO.endYearMonth"
						value="${univRepFileVO.endYearMonth}"
						class="required"> <a class="inputDateButton"
						href="javascript:;" id="">选择</a>
				</dd>
			</dl>
			<div>
				<div class="pageFormdiv">
					<button type="button" class="but_blue" onclick="searchAction();">查询</button>
					<s:if test="st.listId==null || st.listId==0">
						<button class="but_blue" disabled="disabled">下载</button>
					</s:if>
					<s:else>
						<button type="button" class="but_blue" onclick="downloadFile()">下载</button>
					</s:else>
				</div>
			</div>
		</div>
	</form>
</div>

<div class="divfclass">
	<h1>
		<img src="cs/img/icon/tubiao.png"">查询结果
	</h1>
</div>
<div id="pageContentDiv" class="tabdivclass">
	<table class="list" width="100%" id="checkedMesg">
		<thead>
			<tr>
				<th colName="listId" inputType="input">选择</th>
				<th colName="fileName" inputType="input">文件名称</th>
				<th colName="organCode" inputType="input">分公司代码</th>
				<th colName="organName" inputType="input">分公司名称</th>
			</tr>
		</thead>
		<tbody>
			<s:iterator value="csUnivRepFileVOList" id="st">
				<tr id="trrr" align="center" height="25" target="listId"
					rel="${st.listId}">
					<td><input type="radio" name="listId" value="${st.listId}" /></td>
					<td><input type="hidden" name="fileName"
						value="${st.fileName}" />${st.fileName}</td>
					<td><input type="hidden" name="organCode"
						value="${st.organCode}" />${st.organCode}</td>
					<td><input type="hidden" name="organName"
						value="${st.organName}" />${st.organName}</td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
	<div id="panelBarDiv" class="panelBar">
		<div id="curruntPage" class="pages">
			<span>显示</span>
			<s:select id="selectPageBtn"
				list="#{20:'20',50:'50',100:'100',200:'200'}" name="select"
				onchange="navTabPageBreak({numPerPage:this.value})"
				value="currentPage.pageSize">
			</s:select>
			<span>条，共${currentPage.total}条</span>
		</div>
		<div id="pageMsg" class="pagination" targetType="navTab"
			totalCount="${currentPage.total}"
			numPerPage="${currentPage.pageSize}" pageNumShown="10"
			currentPage="${currentPage.pageNo}"></div>
	</div>
</div>