<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx }/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 

<div class="pageFormContent">
	<div class="pageContent" style="border-left: 1px #B8D0D6 solid; border-right: 1px #B8D0D6 solid">
		<form action="${ctx }/cs/csCancel/nextStep_PA_csEndorseManualCancelApplyAction.action"
			id="nextStep" method="post" onsubmit="return navTabSearch(this);">
			<table width="100%" class="list">
				<thead>
					<tr>
						<th>保全申请号</th>
						<th>保全受理号</th>
						<th>保全项目</th>
						<th>保单号</th>
						<th>撤销原因</th>
						<th>撤销时间</th>
						<th>操作人员</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="manualCancelApplyVOList" status="st" var="var">
						<tr>
							<td align="center">${applyCode }</td>
							<td align="center">${acceptCode }</td>
							<td align="center"><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE" value="${serviceCode }" /></td>
							<td align="center">${policyCode }</td>
							<td align="center"><Field:codeValue tableName="APP___PAS__DBUSER.t_cs_cancel_cause" value="${cancelCause }" /></td>
							<td align="center"><s:date format="yyyy-MM-dd" name="cancelTime"></s:date></td>
							<td align="center">${userName }</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</form>
	</div>
</div>