<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 

<div class="pageContent" layoutH="50px">
	<form action="${ctx }/cs/csCancel/queryCsEndorseManualCancelTrail_PA_csEndorseManualCancelApplyAction.action"
		  id="manualCancelTrailQueryForm" onsubmit="return divSearch(this,'queryManualCancelTrailResultDIV')" method="post">
		<div class="panel" >
			<!-- <h1 style="font-size: 20px;">手工撤销申请轨迹</h1> -->
			<h1>查询条件</h1>
			<div class="pageFormContent" id="">
				<dl>
					<dt>保全申请号</dt>
					<dd>
						<input type="text" name="manualCancelApplyVO.applyCode"
							maxlength="20" value="${manualCancelApplyVO.applyCode}" />
					</dd>
				</dl>
				<dl>
					<dt>保全受理号</dt>
					<dd>
						<input type="text" name="manualCancelApplyVO.acceptCode"
							maxlength="30" value="${manualCancelApplyVO.acceptCode}" />
					</dd>
				</dl>
				<dl>
					<dt>保单号</dt>
					<dd>
						<input type="text" name="manualCancelApplyVO.policyCode"
							maxlength="20" value="${manualCancelApplyVO.policyCode}" />
					</dd>
				</dl>
				<dl>
					<dt>客户姓名</dt>
					<dd>
						<input type="text" name="manualCancelApplyVO.userName"
							maxlength="20" value="${manualCancelApplyVO.userName}" />
					</dd>
				</dl>
				<dl>
					<dt>客户证件类型</dt>
					<dd>
						<Field:codeTable cssClass="combox" id="certiType"
							nullOption="true" name="manualCancelApplyVO.certiType"
							tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
							value="${manualCancelApplyVO.certiType}" />
					</dd>
				</dl>
				<dl>
					<dt>证件号码</dt>
					<dd>
						<input type="expandChar" name="manualCancelApplyVO.certiCode"
							onchange="dateValidation(this)" maxlength="20"
							value="${manualCancelApplyVO.certiCode}" />
					</dd>
				</dl>
				<dl>
					<dt>撤销申请时间</dt>
					<dd>
						<input type="expandDateYMD" name="manualCancelApplyVO.insertTime"
							value="<s:date format="yyyy-MM-dd" name="manualCancelApplyVO.insertTime"></s:date>" />
					</dd>
				</dl>
			    <table style="width:100%">
			        <tbody>
				        <tr>
				            <td></td>
				            <td style="width:150px">
					            <div class="button">
									<div class="buttonContent" align="center">
										<button id="queryButton" type="button" >查询</button>
									</div>
								</div>
				            </td>
				            <td></td>
				        </tr>
			    	 </tbody>
			    </table>
			</div>
			<input type="hidden" name="manualCancelApplyVO.cancelCause"
				value="${manualCancelApplyVO.cancelCause}">
			<div style="display: none;">
				<textarea name="manualCancelApplyVO.cancelNote" tpye="hidden"
					id="cancelNote" value="${manualCancelApplyVO.cancelNote}"></textarea>
			</div>
		</div>
	</form>

	<div class="panel">
		<h1>查询结果</h1>
		<div class="pageFormContent">
			<div id="queryManualCancelTrailResultDIV"></div>
		</div>
	</div>
	<div class="panelContent ">
	    <table style="width:100%">
	        <tbody>
		        <tr>
		            <td></td>
		            <td style="width:150px">
		                <div class="button">
							<div class="buttonContent">
								<button type="button" class="close" >退出</button>
							</div>
						</div>
		            </td>
		            <td></td>
		        </tr>
	    	 </tbody>
	    </table>
	</div>
</div>

<script type="text/javascript">
	$('#queryButton').click(function() {
		var trailForm = $("#manualCancelTrailQueryForm");
		trailForm.submit();
	});
	/**
	 * 验证证件号码里面是否有日期格式。客户重要资料变更。
	 */
	function dateValidation(obj){
		//证件类型
		var carTypeValue = $("#certiType").val();
		var carTypeTitle = $("#certiType").find("option:selected").text();
		//用户写的证件号码 
		var carNumber = obj.value;
		//非空
		if(carNumber != null && carNumber != ""){
			if(carTypeValue === '0' ){//身份证
				//身份证是否有校
				var a = /^(\d{6})(18|19|20)?(\d{2})([01]\d)([0123]\d)(\d{3})(\d|X|x)?$/; 
				if(!a.exec(carNumber)){
					alertMsg.error("身份证号格式错误");
					return null;
				}
				//日期验证
				var regular = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
				var birthday = carNumber.substr(6,4)+"-"+carNumber.substr(10,2)+"-"+carNumber.substr(12,2);
				/**
				 * 因为身份证的号码是有一定的规则的所以根据规则来取得日期。
				 * */
				 if(regular.exec(birthday)){
				 }else{
					 alertMsg.error("身份证号格式错误");
				 }
			}
		}
	}
</script>
