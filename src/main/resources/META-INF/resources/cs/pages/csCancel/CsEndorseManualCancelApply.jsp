<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type = "text/javascript" src="${ctx}/cs/js/cs_ifSamePerson.js"></script>
<div class="pageContent" layoutH="10">
	<form action="${ctx }/cs/csCancel/queryPolicyCode_PA_csEndorseManualCancelApplyAction.action" id="manualCancelApplyQueryForm"
		  onsubmit="return divSearch(this,'queryManualCancelApplyResultDIV')" method="post">
		<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >查询条件
			</h1>
		</div>
		<div class="pageFormInfoContent" id="">
						<dl >
							<dt>保全申请号</dt>
							<dd>
								<input type="text" id="applyCode"  name="manualCancelApplyVO.applyCode"   value="${manualCancelApplyVO.applyCode}"/>
							</dd>
						</dl>
						<dl >
							<dt>保全受理号</dt>
							<dd>
								<input type="text" id="acceptCode"  name="manualCancelApplyVO.acceptCode"  value="${manualCancelApplyVO.acceptCode}"/>
							</dd>
						</dl>
						<dl >
							<dt>保单号</dt>
							<dd>
								<input type="text" id="policyCode" name="manualCancelApplyVO.policyCode"   value="${manualCancelApplyVO.policyCode}"/>
							</dd>
						</dl>
						
						
						<dl >
							<dt>客户姓名</dt>
							<dd>
								<input type="text"  id="userName"  name="manualCancelApplyVO.userName"  value="${manualCancelApplyVO.userName}"/>
							</dd>
						</dl>
						<dl>
							<dt>客户证件类型</dt>
							<dd>
								<Field:codeTable cssClass="combox" id="certiType" tableName="APP___PAS__DBUSER.T_CERTI_TYPE" name="manualCancelApplyVO.certiType" value="${manualCancelApplyVO.certiType}" nullOption="true" />
							</dd>
						</dl>
						<dl >
							<dt>证件号码</dt>
							<dd>
								<input type="expandChar" id="certiCode" name="manualCancelApplyVO.certiCode" onchange="dateValidation(this)"  value="${manualCancelApplyVO.certiCode}"/>
							</dd>
						</dl>
						<dl >
							<dt>录入日期</dt>
							<dd>
								<input type="expandDateYMD" id="insertTime" name="manualCancelApplyVO.insertTime" value="<s:date format="yyyy-MM-dd" name="manualCancelApplyVO.insertTime"></s:date>"/>
							</dd>
						</dl>
					<div class="pageFormdiv">
						<button type="button" class="but_blue" onclick="queryPolicyCode();">查询</button>
					</div>
			</div>
			<input type="hidden" name="manualCancelApplyVO.cancelCause" value="${manualCancelApplyVO.cancelCause}">
			<div style="display:none;">
				<textarea name="manualCancelApplyVO.cancelNote" tpye="hidden"  id="cancelNote" value="${manualCancelApplyVO.cancelNote}"></textarea>
			</div>
	</form>
	<div class="divfclass">
			<h1>
				<img src="images/tubiao.png" >查询结果
			</h1>
	</div>
	
		<div id="queryManualCancelApplyResultDIV">
		
		</div>
		<div class="pageFormdiv">
			<button type="button" class="but_blue" onclick="nextCancelStep()" >下一步</button>
			<button type="button" class="close but_gray" onclick="navTab.closeCurrentTab()">退出</button>
		</div>
	
</div>

<script type="text/javascript">
	
	function queryPolicyCode(){
		//"certiType",
		var controlsList = ["applyCode", "acceptCode", "policyCode", "userName", "certiCode", "insertTime"];
		isAllEmpty = true;
		for (var i in controlsList) {
			if ($("#" + controlsList[i],navTab.getCurrentPanel()).val() != "") {
				isAllEmpty = false;
				break;
			}
		}
		if (isAllEmpty) {
			alertMsg.error("请输入查询条件!");
			return;
		}
		/* if ($("#certiType").val() == "" && $("#certiCode").val() != "") {
			alertMsg.error("证件类型和证件号码需要同时输入");
			return;
		} else */
		if ($("#certiType",navTab.getCurrentPanel()).val() != "" && $("#certiCode",navTab.getCurrentPanel()).val() == "") {
			alertMsg.error("证件类型和证件号码需要同时输入");
			return;
		}

		$("#manualCancelApplyQueryForm",navTab.getCurrentPanel()).submit();
	};
	function nextCancelStep(){
		var $form = $("#nextStep",navTab.getCurrentPanel());
		var $acceptIds=$(".acceptIds",navTab.getCurrentPanel());
		var acceptIds="";		
		
		var policyCodesTMP="";
		var policyCodes="";
		for(var i=0;i<$acceptIds.length;i++){
			if($acceptIds[i].checked==true){
				acceptIds=acceptIds+$acceptIds[i].value.trim()+',';
				
				//正则取保单号
				policyCodesTMP = $($acceptIds[i]).parents("tr").find("td:eq(5)").html().replace(/\"/g, "");
				policyCodesTMP = policyCodesTMP.replace(/<br>/g, ",");
				policyCodes = policyCodes + policyCodesTMP + ",";
			}
		}
		acceptIds = acceptIds.substring(0, acceptIds.length - 1);
		if(acceptIds.length==0){
			alertMsg.info("请选择受理条目！");	
			return false;
		}else{
			//保全判断保单销售人员和操作人员是否一致
	 		if(ifSamePerson(policyCodes)){
	 			return;
			}
		}
		$form.find("input[name='manualCancelApplyVO.acceptIds']").val(
				acceptIds);
		$form.submit();
	}
	
	/**
	 * 验证证件号码里面是否有日期格式。客户重要资料变更。
	 */
	function dateValidation(obj){
		//证件类型
		var carTypeValue = $("#certiType",navTab.getCurrentPanel()).val();
		var carTypeTitle = $("#certiType",navTab.getCurrentPanel()).find("option:selected").text();
		//用户写的证件号码 
		var carNumber = obj.value;
		//非空
		if(carNumber != null && carNumber != ""){
			if(carTypeValue == '0' ){//身份证
				//身份证是否有校
				var a = /^(\d{6})(18|19|20)?(\d{2})([01]\d)([0123]\d)(\d{3})(\d|X|x)?$/; 
				if(!a.exec(carNumber)){
					alertMsg.error("身份证号错误，请重新输入！");
					return null;
				}
				//日期验证
				var regular = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
				var birthday = carNumber.substr(6,4)+"-"+carNumber.substr(10,2)+"-"+carNumber.substr(12,2);
				/**
				 * 因为身份证的号码是有一定的规则的所以根据规则来取得日期。
				 * */
				 if(regular.exec(birthday)){
				 }else{
					 alertMsg.error("身份证号里面的日期错误，请重输入");
				 }
			}
		}
	}
</script>
