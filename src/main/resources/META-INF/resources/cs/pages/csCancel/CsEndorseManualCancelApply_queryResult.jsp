<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<div class="tabdivclass">
		<form id="pagerForm"
			onsubmit="return divSearch(this,'queryManualCancelApplyResultDIV');" 
			action="${ctx}/cs/csCancel/queryPolicyCode_PA_csEndorseManualCancelApplyAction.action"
			method="post">
			<input type="hidden" name="pageNum" value="${pageNum}" /> <input
				type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
				<input type="hidden" name="checkFlag" value="${checkFlag}" /> 
				<input type="hidden" name="unitNumber" value="${unitNumber}" />	
				<input type="hidden" name="manualCancelApplyVO.applyCode" 	value="${manualCancelApplyVO.applyCode}" /> 
				<input type="hidden" name="manualCancelApplyVO.acceptCode" value="${manualCancelApplyVO.acceptCode}" /> 
				<input type="hidden" name="manualCancelApplyVO.policyCode" value="${manualCancelApplyVO.policyCode}" />
				<input type="hidden" name="manualCancelApplyVO.userName" value="${manualCancelApplyVO.userName}" />
				<input type="hidden" name="manualCancelApplyVO.certiType" value="${manualCancelApplyVO.certiType}" />
				<input type="hidden" name="manualCancelApplyVO.certiCode" value="${manualCancelApplyVO.certiCode}" />
				<input type="hidden" name="manualCancelApplyVO.insertTime" value="${manualCancelApplyVO.insertTime}" />
		</form>
		<form action="${ctx }/cs/csCancel/nextStep_PA_csEndorseManualCancelApplyAction.action"
			rel="pagerForm" class="pageForm required-validate"
			id="nextStep" method="post" onsubmit="return navTabSearch(this);">
			<table width="100%" class="list">
				<thead>
					<tr>
						<th><input type="checkbox" style="position:relative;top:2px;right:5px;" class="checkboxCtrl" group="acceptIds"/>选择</th>
						<th>序号</th>
						<th>保全申请号</th>
						<th>保全受理号</th>
						<th>保全项目</th>
						<th>保单号</th>
						<th>涉及金额</th>
						<th>申请提交日期</th>
						<th>经办人</th>
					</tr>
					<input type="hidden" name="manualCancelApplyVO.acceptIds" value="">
					<input type="hidden" name="manualCancelApplyVO.queryCondition" value="${manualCancelApplyVO.queryCondition}">
				</thead>
				<tbody>
						<s:iterator value="currentPage.pageItems" status="st" var="var" id="qr">
							<tr>
								<td><input type="checkbox" name="acceptIds" value="${acceptId}" class="acceptIds"></td>
								<td><s:property value="#st.count" /></td>
								<td>${applyCode}</td>
								<td>${acceptCode }</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
										value="${serviceCode}" /></td>
								<td style="width:20%;word-wrap:break-word;word-break:break-all;">${policyCode}</td>
								<td>${feeAmount}</td>
								<td><s:date format="yyyy-MM-dd" name="applyTime"></s:date></td>
								<td>${principalUserName}</td>
							</tr>
						</s:iterator>
					</div>	
				</tbody>
			</table>
			<input type="hidden" name="manualCancelApplyVO.cancelCause" value="${manualCancelApplyVO.cancelCause}">
			<div style="display:none;">
				<textarea name="manualCancelApplyVO.cancelNote" >${manualCancelApplyVO.cancelNote}</textarea>
			</div>
		</form>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select  list="#{5:'5',10:'10',20:'20'}"
					name="currentPage.pageSize"
					onchange="navTabPageBreak({numPerPage:this.value}, 'queryManualCancelApplyResultDIV')">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" rel="queryManualCancelApplyResultDIV"
				totalCount="${currentPage.total}"
				currentPage="${currentPage.pageNo}"
				numPerPage="${currentPage.pageSize}" pageNumShown="5"></div>
		</div>
</div>