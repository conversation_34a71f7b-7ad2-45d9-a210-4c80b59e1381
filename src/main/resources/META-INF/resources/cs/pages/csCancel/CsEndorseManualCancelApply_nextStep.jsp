<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- <div class="pageContent" > -->
	<div class="pageContent">
		<div class="pageContent">
		     <div class="divfclass">
				<h1>
					<img src="images/tubiao.png" >客户信息
				</h1>
			</div>
			<div class="panelPageFormContent">
							<dl>
								<dt>姓名</dt>
								<dd>
									<input type="text" value="${manualCancelApplyVO.userName}" readonly="true" maxlength="20" />
								</dd>
							</dl>
							<dl>
								<dt>性别</dt>
								<dd>
									<input type="text" value="<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
									value="${manualCancelApplyVO.sex}" />" readonly="true" maxlength="20" />
								</dd>
							</dl>
							<dl >
								<dt>出生日期</dt>
								<dd>
									<input type="text" value="<s:date format="yyyy-MM-dd" name="manualCancelApplyVO.birth"/>" readonly="true" maxlength="20" />
								</dd>
							</dl>
							
							<dl >
								<dt>证件类型</dt>
								<dd>
								<input type="text"
									value="<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
									value="${manualCancelApplyVO.certiType}" />"
									readonly="true" maxlength="20" />
							</dd>
							</dl>
							<dl >
								<dt>证件号码</dt>
								<dd>
									<input type="text" value="${manualCancelApplyVO.certiCode}" readonly="true" maxlength="20" />
								</dd>
							</dl>
						</div>
				
					<div class="pageContent">
						<div class="divfclass">
							<h1>
								<img src="images/tubiao.png" >保全项目信息
							</h1>
						</div>
						<div class="tabdivclass">
							<table width="100%" class="list" id="resultCancel">
								<thead>
									<tr>
									    <th>选择</th>
										<th>序号</th>
										<th>保全申请号</th>
										<th>保全受理号</th>
										<th>保全项目</th>
										<th>保单号</th>
										<th>投保人</th>
										<th>被保人</th>												
										<th>涉及金额</th>
										<th>申请提交日期</th>
										<th>受理状态</th>
									</tr>
									<input type="hidden" name="manualCancelApplyVO.acceptIds" value="${manualCancelApplyVO.acceptIds}">
								</thead>
								<tbody>
									<s:iterator value="manualCancelApplyVOList" status="st" var="var">
										<tr>
										    <td>
										    <s:if test="#st.count==1">
										     <input type="radio" checked="true" name="radio"  value='${acceptId}'/>
										    </s:if>							
								            <s:else>
                                             <input type="radio"  name="radio"  value='${acceptId}'/>
								            </s:else>
								            </td>
											<td><s:property value="#st.count" /></td>
											<td>${applyCode}</td>
											<td>${acceptCode}</td>
											<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
													value="${serviceCode}" /></td>
											<td style="width:20%;word-wrap:break-word;word-break:break-all;">${policyCode}</td>
											<td>${customer}</td>													
											<td>${protectrdCustomer}</td>													
											<td>${feeAmount}</td>
											<td><s:date format="yyyy-MM-dd" name="applyTime"/></td>
											<td><Field:codeValue tableName="APP___PAS__DBUSER.T_ACCEPT_STATUS"
													value="${acceptStatus}" />
													</td>
											<td hidden >${customerId}</td>
											<td hidden >${changeId}</td>
											<input type="hidden" id="hiddenPayMode" value="${payMode}">
											<input type="hidden" id="hiddenFeeStatus" value="${feeStatus}">
										</tr>
									</s:iterator>
								</tbody>
							</table>
						</div>
					</div>
</div>
			<form action="" id="queryform" method="post" onsubmit="return navTabSearch(this);">
				<input type="hidden" name="manualCancelApplyVO.queryCondition" value="${manualCancelApplyVO.queryCondition}">
				<input type="hidden" name="manualCancelApplyVO.acceptIds" value="${manualCancelApplyVO.acceptIds}">
				<input type="hidden" name="manualCancelApplyVO.acceptStatus" value="${manualCancelApplyVO.acceptStatus}">
				<div class="pageContent">
				    <div class="divfclass">
						<h1>
							<img src="images/tubiao.png" >信息录入
						</h1>
					</div>
					<div class="panelPageFormContent">
					<dl>
						<dt>原因</dt>
						<dd>
						<Field:codeTable cssClass="combox"  nullOption="true"
						name="manualCancelApplyVO.cancelCause" tableName="APP___PAS__DBUSER.T_CS_CANCEL_CAUSE"
						value="${manualCancelApplyVO.cancelCause}"  id="cancelCause"/>
						</dd>
					</dl>
					<dl style="height:auto;">
					<dt>详细情况</dt>
						<dd>
						<textarea name="manualCancelApplyVO.cancelNote"  id="cancelNote"
						cols="80" rows="5">${manualCancelApplyVO.cancelNote}</textarea>
						</dd>
					</dl>
					</div>
				</div>
			</form>
	 <div class="pageFormdiv">
			<button type="button" onclick="flimSearch()" class="but_blue" >影像查询</button>
			<button type="button" onclick="applyDetail()" class="but_blue" >保全申请明细查询</button>
			<button type="button" onclick="policyDetail()" class="but_blue" >保全受理明细查询</button>
			<button type="button" onclick="cancelApply()" class="but_blue" >撤消申请</button>
			<button type="button" onclick="beforeNextStep()" class="but_blue">上一步</button>
			<button type="button" class="close but_gray">退出</button>
	</div>
</div>
<script type="text/javascript">
    //影像查询
	function flimSearch(){
	   var $obj1 = $("#resultCancel", navTab.getCurrentPanel());
	   var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
	   var tr=radio.closest("tr");
	   var applyCode = tr.find("td:eq(2)").text();
	   var acceptCode = tr.find("td:eq(3)").text();
       $.ajax({
        type:'POST',
        url:'${ctx}/cs/csAccept/queryDataUpdate_PA_csQualityErrorScoreAction.action',
        data : {
        	 "qualityInspectionInfoVO.acceptCode" : acceptCode,
        	 "qualityInspectionInfoVO.applyCode" : applyCode
        },
        dataType:"json",
        async : false,
		cache : false,
        success:function(data){
        	if(!data.statusCode == DWZ.statusCode.error){
        		 alertMsg.error("该保单没有对应影像信息");
        	}else{
	            window.open(data.message,'','width='+(window.screen.availWidth-10)+',height='+
	               (window.screen.availHeight-30)+',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');    
        	}
        }
     });
    };
    /**
     * 保全申请明细
     */
    function applyDetail(){
       var $obj1 = $("#resultCancel", navTab.getCurrentPanel());
 	   var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
 	   var tr=radio.closest("tr");
 	   var changeId = tr.find("td:eq(12)").text();
    	var url ="${ctx}/cs/information/geCsPolicyApplyInfo_PA_csInformationQueryAction.action?"
    			+"csPolicyInfoQueryVO.checkedRadio=applyCode&csPolicyInfoQueryVO.queryApplyCode="+changeId;
    	var title = "保全申请明细";	
    	var fresh = eval("true");
    	var external = eval("false"); 
    	var tabid="保全申请明细";
    	navTab.openTab(tabid, url, {
    		title : title,
    		fresh : fresh,
    		external : external
    	});
    }
    /**
     * 保全受理明细 cs/csAccept/toService_PA_uwParameterAction.action?acceptId=
     */
    function policyDetail(){
    	var $obj1 = $("#resultCancel", navTab.getCurrentPanel());
  	    var radio = $($obj1).find("input:radio:checked",navTab.getCurrentPanel());
  	    var tr=radio.closest("tr");
  	    var customerId = tr.find("td:eq(11)").text();
  	    var changeId = tr.find("td:eq(12)").text();
  	    var acceptId = $("input[name='radio']:checked",navTab.getCurrentPanel()).val()
  	    url = "${ctx}/cs/csAccept/toService_PA_uwParameterAction.action?changeId=" + changeId
		+ "&customerId=" + customerId + "&acceptId=" + acceptId;
    	var title = "保全受理明细";	
    	var fresh = eval("true");
    	var external = eval("false"); 
    	var tabid="保全受理明细";
    	navTab.openTab(tabid, url, {
    		title : title,
    		fresh : fresh,
    		external : external
    	});
    }
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById){
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false){
				eval("obj.style." + theProp + "=" + theValue);
			}else{
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};
	//上一步
	function beforeNextStep(){
		alertMsg.confirm("请确认是否需要保存录入的信息.", {
			okCall : function() {
				var json = "[";
				$("#resultCancel").find("tbody tr").each(function(obj){
					var applyCode = $(this).find("td:eq(2)").text();
					var acceptCode = $(this).find("td:eq(3)").text();
					json = json+"{'applyCode':'"+applyCode+"','acceptCode':'"+acceptCode+"'},";
				});
				json = json.substring(0, json.length-1)+"]";
				var cancelNote = $("#cancelNote").val();
				var cancelCause = $("#cancelCause").val();
				$.ajax({
					url:'${ctx}/cs/csCancel/saveHistorCancel_PA_csEndorseManualCancelApplyAction.action',
					data:'manualCancelApplyVO.json='+json+'&manualCancelApplyVO.cancelCause='+cancelCause+'&manualCancelApplyVO.cancelNote='+cancelNote,
					cache:false,
					async:false,
					type:'post',
					success:function(response){
						var json = DWZ.jsonEval(response);
						if(json.statusCode==DWZ.statusCode.error){
							alertMsg.error(json.message);
						}else{
							alertMsg.info(json.message);
							var $form=$("#queryform");
							var action="${ctx}/cs/csCancel/beforeNextStep_PA_csEndorseManualCancelApplyAction.action";
							var onsubmit="return navTabSearch(this);";
							$form.attr("action",action);
							$form.attr('onsubmit', onsubmit);
							$form.submit();
						}
					}
				});
			},
			cancelCall : function() {
				var $form=$("#queryform");
				var action="${ctx}/cs/csCancel/beforeNextStep_PA_csEndorseManualCancelApplyAction.action";
				var onsubmit="return navTabSearch(this);";
				$form.attr("action",action);
				$form.attr('onsubmit', onsubmit);
				$form.submit();
			}
		});
	}
	//撤消申请
	function cancelApply(){
		if($("#cancelCause",navTab.getCurrentPanel()).val()==''){
			alertMsg.info("请选择撤消原因！");	
			return false;
		}
		if($("#cancelNote",navTab.getCurrentPanel()).val().length>500){
			alertMsg.info("详细情况长度不能超过500！");	
			return false;
		}
		
		//变更前“收付费方式”为“43-移动支付”，且费用状态不为“03-转账失败”，系统阻断提示
		var flag = false;
		$("#resultCancel").find("tbody tr").each(function(index) {
			var payMode = $("#hiddenPayMode").val();
			var feeStatus = $("#hiddenFeeStatus").val();
			if (payMode == '43' && feeStatus != '03') {
				flag = true;
			}
		});
		if (flag) {
			alertMsg.info("该业务处于移动支付中/支付成功，不能撤销！");
			return false;
		}
		var acceptIds = $("input[name='manualCancelApplyVO.acceptIds']", navTab.getCurrentPanel()).val();
		$.ajax({
	    	    type : 'POST',
				dataType : "text",
				url : '${ctx}/cs/csCancel/checkCancelApply_PA_csEndorseManualCancelApplyAction.action',
				data:'manualCancelApplyVO.acceptIds='+acceptIds,
				cache : false,
				async : false,
				success:function(response){
					var json = DWZ.jsonEval(response);
					if(json.statusCode == "302"){
						alertMsg.confirm(json.message, {
							okCall : function() {
								_cancelApply();
							},
							cancleCall : function() {
								return false;
							}
						});
					}else if(json.statusCode == "200"){
						_cancelApply();
					}else if(json.statusCode == "300"){
						alertMsg.error(json.message);
					}
				}
	       })		
	}
	function _cancelApply() {		
		var $form = $("#queryform", navTab.getCurrentPanel());
		var action = "${ctx}/cs/csCancel/cancelApply_PA_csEndorseManualCancelApplyAction.action";
		var onsubmit = "return validateCallback(this,cancleCallBack);";
		$form.attr("action", action);
		$form.attr('onsubmit', onsubmit);
		$form.submit();
	}
	
	function cancleCallBack(data) {
		DWZ.ajaxDone(data)
		if (data.statusCode == DWZ.statusCode.ok) {
			alertMsg.correct(data.message)
			setTimeout(function() {
				navTab.closeCurrentTab(data.navTabId);
			}, 100);
		} else {
			alertMsg.error(data.message);
		}
		var json = DWZ.jsonEval(data);
	}
</script>
<script type="text/javascript">
	$(document).ready(function(){
		csHelpMenu();
	});
</script>