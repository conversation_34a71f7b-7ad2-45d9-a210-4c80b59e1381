<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="pageContent" layoutH="10">
	<form action="${ctx }/cs/csCancel/queryPolicyCode_PA_csEndorseManualCancelApplyAction.action" id="manualCancelApplyQueryForm"
		  onsubmit="return divSearch(this,'queryManualCancelApplyResultDIV')" method="post">
			<div class="pageContent" id="">
			        <div class="divfclass">
							<h1>
								<img src="images/tubiao.png" >查询条件
							</h1>
					</div>
					<div class="pageFormInfoContent" id="">	
						<dl>
							<dt>保全申请号</dt>
							<dd>
								<input type="text" name="manualCancelApplyVO.applyCode"  maxlength="20" value="${manualCancelApplyVO.applyCode}"/>
							</dd>
						</dl>
						<dl>
							<dt>保全受理号</dt>
							<dd>
								<input type="number" style="width:135px"  name="manualCancelApplyVO.acceptCode" maxlength="30" value="${manualCancelApplyVO.acceptCode}"/>
							</dd>
						</dl>
						<dl>
							<dt>保单号</dt>
							<dd>
								<input type="text" name="manualCancelApplyVO.policyCode"  maxlength="20" value="${manualCancelApplyVO.policyCode}"/>
							</dd>
						</dl>
						<dl>
							<dt>客户姓名</dt>
							<dd>
								<input type="text"  name="manualCancelApplyVO.userName" maxlength="20" value="${manualCancelApplyVO.userName}"/>
							</dd>
						</dl>
						<dl>
							<dt>客户证件类型</dt>
							<dd>
								<Field:codeTable cssClass="combox" nullOption="true" name="manualCancelApplyVO.certiType"
									tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${manualCancelApplyVO.certiType}" />
							</dd>
						</dl>
						<dl>
							<dt>证件号码</dt>
							<dd>
								<input type="expandChar" name="manualCancelApplyVO.certiCode"  maxlength="20" value="${manualCancelApplyVO.certiCode}"/>
							</dd>
						</dl>
						<dl>
							<dt>录入日期</dt>
							<dd>
								<input type="expandDateYMD" name="manualCancelApplyVO.insertTime" value="<s:date format="yyyy-MM-dd" name="manualCancelApplyVO.insertTime"></s:date>"/>
							</dd>
						</dl>
					</div>
					<div class="pageFormdiv">
					   <button type="button" class="but_blue" onclick="queryPolicyCode();">查询</button>
					</div>
			</div>
			<input type="hidden" name="manualCancelApplyVO.cancelCause" value="${manualCancelApplyVO.cancelCause}">
			<div style="display:none;">
				<textarea name="manualCancelApplyVO.cancelNote" >${manualCancelApplyVO.cancelNote}</textarea>
			</div>
	</form>
	<div class="pageContent">
		<div class="divfclass">
				<h1>
					<img src="images/tubiao.png" >查询结果
				</h1>
		</div>
		<div class="tabdivclass" id="queryManualCancelApplyResultDIV">
						<form action="${ctx }/cs/csCancel/nextStep_PA_csEndorseManualCancelApplyAction.action"
							  id="nextStep" method="post" onsubmit="return navTabSearch(this);">
							<table width="100%" class="list">
								<thead>
									<tr>
										<th><input type="checkbox" style="position:relative;top:2px;right:5px;" class="checkboxCtrl" group="acceptIds"/>选择</th>
										<th>序号</th>
										<th>保全申请号</th>
										<th>保全受理号</th>
										<th>保全项目</th>
										<th>保单号</th>
										<th>涉及金额</th>
										<th>申请提交日期</th>
										<th>经办人</th>
									</tr>
									<input type="hidden" name="manualCancelApplyVO.acceptIds" value="">
									<input type="hidden" name="manualCancelApplyVO.queryCondition" value="${manualCancelApplyVO.queryCondition}">
								</thead>
								<tbody>
									<s:iterator value="manualCancelApplyVOList" status="st" var="var">
										<tr>
											<td><input type="checkbox" name="acceptIds" value="${acceptId}" class="acceptIds"></td>
											<td><s:property value="#st.count" /></td>
											<td>${applyCode}</td>
											<td>${acceptCode }</td>
											<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
													value="${serviceCode}" /></td>
											<td>${policyCode}</td>
											<td>${feeAmount}</td>
											<td><s:date format="yyyy-MM-dd" name="applyTime"></s:date></td>
											<td>${principalUserName} </td>
										</tr>
									</s:iterator>
								</tbody>
							</table>
							<input type="hidden" name="manualCancelApplyVO.cancelCause" value="${manualCancelApplyVO.cancelCause}">
							<div style="display:none;">
								<textarea name="manualCancelApplyVO.cancelNote" >${manualCancelApplyVO.cancelNote}</textarea>
							</div>
						</form>
					</div>
		    <div class="pageFormdiv">
		       <button type="button" class="but_blue" onclick="nextCancelStep()" >下一步</button>
			    <button type="button" class="close but_gray">退出</button>
		    </div>
	</div>
</div>

<script type="text/javascript">
	
	function queryPolicyCode(){
		var $form = $("#manualCancelApplyQueryForm");
		$form.submit();
	};
	function nextCancelStep(){
		var $form = $("#nextStep");
		var $acceptIds=$(".acceptIds");
		var acceptIds="";
		for(var i=0;i<$acceptIds.length;i++){
			if($acceptIds[i].checked==true){
				acceptIds=acceptIds+$acceptIds[i].value.trim()+',';
			}
		}
		acceptIds = acceptIds.substring(0, acceptIds.length - 1);
		if(acceptIds.length==0){
			alertMsg.info("请选择受理条目！");	
			return false;
		}
		$form.find("input[name='manualCancelApplyVO.acceptIds']").val(
				acceptIds);
		$form.submit();
	}
</script>
