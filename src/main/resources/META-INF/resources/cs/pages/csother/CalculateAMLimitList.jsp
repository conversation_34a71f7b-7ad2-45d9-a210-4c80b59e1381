<!-- 保全双录机构配置-机构类型配置结果表  -->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<div class="panel">
	<p>&nbsp;&nbsp;提示：若关联保单正处于犹豫期，可追加限额将不包含处于该犹豫期内的关联保单。您可在犹豫期结束后，查看最新的可追加限额。</p>
	<div class="pageFormContent">
		<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid">
			<table class="list" id="csDrinfoTable" style="width:60%">
                 <thead>
                     <tr>
                         <th style="width:15%">万能险代码</th>
                         <th style="width:31%">万能险名称</th>
                         <th style="width:18%">可追加保费上限</th>
                         <th style="width:18%">已追加保费</th>
                         <th style="width:18%">剩余可追加保费限额</th>
                     </tr>
                 </thead>
                 <tbody align="center" id="tbody">
                     <s:iterator value="calculateAMLimitVOList" status="st" id="qr6">
                         <tr>
                             <td><s:property value="productCode" /></td>
                             <td><s:property value="productName" /></td>
                             <td><s:property value="appendPremMax" /></td>
                             <td><s:property value="appendPrem" /></td>
                             <td><s:property value="appendPremRemaining" /></td>
                         </tr>
                     </s:iterator>
                 </tbody>
             </table>
		</div>
	</div>
</div>

