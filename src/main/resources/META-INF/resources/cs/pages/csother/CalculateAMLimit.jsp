<!-- 追加保费限额计算页面 -->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script src="${ctx}/udmp/plugins/ribbon/jquery.orgtree.js" type="text/javascript"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>

<div class="pageContent" layoutH="10"  >
	<form action="${ctx}/cs/other/calculateAMLimit_PA_calculateAMLimitAction.action" id="calculateAMLimitForm"  onsubmit="return navTabSearch(this,'queryDiv')" method="post">
		<input type="hidden" name="numPerPage" value="10" />
		<!-- <div class="divfclass">
            <h1>
                <img src="images/tubiao.png" >查询条件
            </h1>
		</div> -->
		<br/>
		<div class="pageFormInfoContent">
            <dl >
                <dt >保单号<font color="red">*</font>:</dt>
                <dd >
                    <input type="text" id="policyCode" name="policyCode" title="${policyCode }" value="${policyCode }" maxlength="20" <s:if test="queryFlag == 'readonly'">readonly style="background-color: #f5f5f5;color: #999;border-color: #ccc;"</s:if> />
                </dd>
            </dl>

            <dl >
                <dd>
					<button  type="button" class="but_blue"  onclick="calculateAMLimit();">计算</button>
                </dd>
            </dl>
        </div>
	</form>

	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png">万能险信息
		</h1>
	</div>
	<div class="tabdivclass" id="queryDiv">
		<!-- 查询结果页面-->
		<s:include value="/cs/pages/csother/CalculateAMLimitList.jsp"></s:include>
	</div>

</div>
<script type="text/javascript">
	$(function(){
		var policyCode = $("#policyCode").val();
		if(policyCode != null && policyCode != ''){
			calculateAMLimit();
		}
	})
	function calculateAMLimit(){
		var policyCode = $("#policyCode").val();
		if(policyCode == null || policyCode == ''){
			alertMsg.error("请输入保单号！");
			return;
		}
		var projectPath = getRootPath();
		$.ajax({
			type : "post",
			url : projectPath+'/cs/other/checkPolicy_PA_calculateAMLimitAction.action',
			data : {policyCode : policyCode},
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if(json.errMsg != '' && json.errMsg != null){
					alertMsg.error(json.errMsg);
					return false;
				} else {
					$("#calculateAMLimitForm").submit();
				}
			}
		});
	}
</script>