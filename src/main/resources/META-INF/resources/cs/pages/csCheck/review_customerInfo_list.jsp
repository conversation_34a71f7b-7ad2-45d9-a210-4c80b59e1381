<%@ page language="java" pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>

	<form id="pagerForm" method="post" style="display: none;"
		action="${ctx}/cs/csCheck/getCsCustomerListInformation_PA_csCheckAction.action"
		onsubmit="return divSearch(this,'reviewCustomerInfoList');">
		<input type="hidden" name="pageNum" value="${cusInfoListCurrentPage.pageNo}" /> 
		<input type="hidden" name="numPerPage" value="${cusInfoListCurrentPage.pageSize}" />
		<input type="hidden" name="acceptId" value="${acceptId}" id="acceptId" />
	</form>
	
		<div id="" class="pageFormContent">
			<form action="" id="BackMsgCheckInfoForm2" method="post" class="pageForm required-validate" rel="pagerForm" onsubmit="return navTabSearch(this,'reviewCustomerInfoList')">
				<div class="tabdivclass">
				<table class="list" width="100%" id="">
					<thead>
						<tr align="center">
							<th>保单号</th>
							<th>多主险保单</th>
							<th>角色</th>
							<th>姓名</th>
							<th>性别</th>
							<th>出生日期</th>
							<th>证件类型</th>
							<th>证件号码</th>
							<th>与投保人关系</th>
							<th>客户风险等级</th>
							<th>保单状态</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="cusInfoListCurrentPage.pageItems" status="st" id="qr" var="cust">
							<tr align="center">
								<!-- 保单号 -->
								<td>${cust.policyCode}</td>
								<!-- 多主险保单标识 -->
								<td>${multiMainriskFlag}</td>
								<!-- 角色 -->
								<td>${cusRoleForPolicy}</td>
								<!-- 姓名 -->
								<td>${customerName}</td>
								<!-- 性别 -->
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value="${customerGender}" /></td>
								<!-- 出生日期 -->
								<td><s:date name="customerBirthday" format="yyyy-MM-dd" /></td>
								<!-- 证件类型 -->
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${customerCertType}" /></td>
								<!-- 证件号码 -->
								<td>${customerCertiCode}</td>
								<!-- 与投保人关系 -->
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LA_PH_RELA" value="${relationToPh}" /></td>
								<!-- 客户风险等级 -->
								<s:if test='customerRiskLevel == "A"'>
							<td><font style="color: red;">${customerRiskLevel}</font></td>
						</s:if>
						<s:else>
							<td>${customerRiskLevel}</td>
						</s:else>
						<s:if test='statusName=="有效"'>
						<td>${statusName}</td>
						</s:if>
						<s:elseif test='statusName=="失效"'><br>
							<td>
								<font style="color: red; font-weight: bold;">
									<!-- modify by shaoocongwang 20200814 RM61590_bug8519 -->
									<Field:codeValue value="${lapseCause}"
									tableName="APP___PAS__DBUSER.T_LAPSE_CAUSE" />
								</font>
							</td>
						</s:elseif>
						<s:elseif test='statusName=="终止"'>
						<td>
							<font style="color: red; font-weight: bold;">
							<!-- modify by shaoocongwang 20200814 RM61581_bug8510 -->
								<Field:codeValue value="${endCause}"
									tableName="APP___PAS__DBUSER.T_END_CAUSE" />
							</font>
						</td>
						</s:elseif>
							</tr>
						</s:iterator>
					</tbody>
				</table>
			</form>
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{5:'5',10:'10',20:'20'}" name="cusInfoListCurrentPage.pageSize" onchange="navTabPageBreak({numPerPage:this.value},'reviewCustomerInfoList')">
					</s:select>
					<span>条，共${cusInfoListCurrentPage.total}条</span>
				</div>
				<div class="pagination" rel="reviewCustomerInfoList" targetType="navTab" totalCount="${cusInfoListCurrentPage.total}" cusInfoListCurrentPage="${cusInfoListCurrentPage.pageNo}" numPerPage="${cusInfoListCurrentPage.pageSize}"
					pageNumShown="5">
				</div>
			</div>

		</div>
	</div>

