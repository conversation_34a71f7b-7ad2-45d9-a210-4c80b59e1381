<!-- 保全配置——保单层贷款要素配置 -查询结果-->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<form id="pagerForm" method="post"
	action="cs/csCheck/showByPagesReviewPointCfg_PA_csCheckAction.action"
	onsubmit="return divSearch(this, 'queryReviewPointCfgDiv');">
	<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> <input
		type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> <input
		type="hidden" name="acceptId" value="${acceptId}" />
</form>
	<div class="panel" style="width: 99%">
		<h1>保全复核要点</h1>
		<div>
			<table class="list" style="width: 100%">
				<thead>
					<tr>
						<th>序号</th>
						<th>复核要点</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="currentPage.pageItems" status="st"
						var="revPoint">
						<tr>
							<s:if test="currentPage.pageItems.size()==0">
								<td><font color="red">没有查到相关记录</font></td>
							</s:if>
							<s:else>
								<td><s:property value="#st.count" /></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_REVIEW_POINT"
										value="${pointId}" /></td>
							</s:else>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</div>
		<div class="panelBar">
			<div class="pages">
				<span>每页</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50'}" name="select"
					onchange="navTabPageBreak({numPerPage:this.value},'queryReviewPointCfgDiv')"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total }条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${currentPage.total }" rel="queryReviewPointCfgDiv"
				numPerPage="${currentPage.pageSize }" pageNumShown="10"
				currentPage="${currentPage.pageNo }"></div>
		</div>
	</div>
