<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_accept.js"></script>
<script type="text/javascript"
	src="${ctx}/cs/pages/csCheck/appletLib/deployJava.js"></script>
<script type="text/javascript">
	$(document).ready(function(){ 
		var _top=($(window).height())/2+80;
		var _left=($(window).width())/2+185;
		$("#loadFikeNC").parent().parent().css({top:_top,left:_left});
		$(document).find("div.shadow").css({top:_top,left:_left});
	});
</script>
<form>
	<input type="hidden" id="customer_Name_ann" value=${customerVO.customerName }>
	<input type="hidden" id="holderCertiType_ann" value=${customerCertiType }>
	<input type="hidden" id="customerCertiCode_ann" value=${customerCertiCode }>
	<input type="hidden" id="holderGender_ann" value=${holderGender }>
	<input type="hidden" id="birthDate_ann" value=${birthDate }>
	<input type="hidden" id="acceptId_ann" value=${acceptId }>
</form>
<div id="policyHolderDialogTable" style="height: 90%">
<span >法定受益人中存在我司在职销售人员，其五要素信息及上一自然年度收入信息如下：</span>
			<div style="text-align: center;margin-top: 60px;">
				<div class="tabdivclass" >
					<table class="list" id="table_year" border="2" table_saveStatus='0'>
						<thead>
							<tr>
								<th>姓名</th>
								<th>性别</th>
								<th>证件类型</th>
								<th>证件号码</th>
								<th>出生日期</th>
								<th>年收入（万元）</th>
							</tr>
						</thead>
						<tbody id="tbody_tr">
							<tr >
								<td ><input id="table_customerName" name="name"></td>
								<td>
									<Field:codeTable id="agentGender" name="agentGender" nullOption="true" cssClass="combox" tableName="APP___PAS__DBUSER.T_GENDER" whereClause="gender_code in (1,2)" value="" onChange="agentGenderChange(this)"></Field:codeTable>
									<input id='table_Gender' name="agentGender1" type="hidden">
								</td>
								<td>
									<Field:codeTable id="certType" name="certType" nullOption="true" cssClass="combox"  
								tableName="APP___PAS__DBUSER.T_CERTI_TYPE1"  value="" onChange="certTypeChange(this)"/>
								<input id='table_CertiType' name="certType1" type="hidden">
								</td>
								<td><input id="table_CertiCode" name="certiCode"></td>
								<td><input id="table_birthDate" name="birthday" type="expandDateYMD" class="date"></td>
								<td><input id="table_income" name="income"></td>
								<td style="display:none"><input id="table_acceptId" type="hidden" name="acceptId" value="${acceptId }"></td>
								<td ><a class="btnDel" onclick="deleteAccountLine(this)"></a></td>
							</tr>
						</tbody>
					</table>
				</div>
				<span style="float:left;"><input type="button" class="but_blue" value="+" onclick="addContext()" /></span>
			</div>
			
</div>
<div style="text-align: center;margin-margin-top 60px;">
					<button onclick="saveYearIncome()">提交</button>
					<button onclick="colsedialog()">返回</button>
				</div>
				
<script>
var genderstr='';
var certiTypestr='';
	function saveYearIncome(){
		var table_customerName = $("#table_customerName").val();
		var table_Gender = $("#table_Gender").val();
		var table_CertiType = $("#table_CertiType").val();
		var table_CertiCode = $("#table_CertiCode").val();
		var table_birthDate = $("#table_birthDate").val();
		var table_income = $("#table_income").val();
		var acceptId_ann = $("#acceptId_ann").val();
		
		if (table_customerName == '') {
			alertMsg.info("姓名不能为空");
			return false;
		}
		if(table_Gender ==''){
			alertMsg.info("请选择性别");
			return false;
		}
		if (table_CertiType == '') {
			alertMsg.info("请选择证件类型");
			return false;
		}
		if (table_CertiCode == '') {
			alertMsg.info("证件号不能为空");
			return false;
		}
		if (table_birthDate == '') {
			alertMsg.info("出生日期不能为空");
			return false;
		}
		if (table_income == '') {
			alertMsg.info("年收入不能为空");
			return false;
		}
		
		var namestr='';
		
		var certiCodestr='';
		var birthDatestr='';
		var incomestr='';
		var acceptIdstr='';
	$("#tbody_tr td").each(function(index, item) {
			$(item).children().each(function(i, it) {

				var name = $(it).attr("name");
				if (name == 'name') {
					console.log($(it).val());
					namestr +='"'+$(it).val()+'"'+",";
				}
				if(name == 'certiCode'){
					console.log($(it).val());
					certiCodestr +=$(it).val()+",";
				}
				if(name == 'birthday'){
					console.log($(it).val());
					birthDatestr +=$(it).val()+",";
				}
				if(name == 'income'){
					console.log($(it).val());
					incomestr +=$(it).val()+",";
				}
				if(name == 'acceptId'){
					console.log($(it).val());
					acceptIdstr +=$(it).val()+",";
				}

			})
		})
		console.log(namestr+"|"+genderstr+"|"+certiTypestr+"|"+certiCodestr+"|"+birthDatestr+"|"+incomestr+"|"+acceptIdstr);
		var saveYearIncomeJson = '{"name":['+namestr+'],"certiCode":['+certiCodestr+'],"certType":['+certiTypestr+'],"agentGender":['+genderstr+'],"birthday":['+birthDatestr+'],"acceptId":['+acceptIdstr+'],"income":['+incomestr+']}';
		console.log(JSON.stringify(saveYearIncomeJson));
		$
				.ajax({
					type : "post",
					url : "${ctx }/cs/csEntry/previousNaturalYearIncomeSave_PA_csEntryAction.action",
					data : {
						"saveYearIncomeJson" : saveYearIncomeJson.toString()
					},
					dataType : "json",
					success : function(data) {
						var json = DWZ.jsonEval(data);
						if (json != null && json != ''
								&& json.statusCode == "200") {
							$.pdialog.close("policyHolderDialogTable");
							alertMsg.info("保存成功");
						} else if (json != null && json != ''
								&& json.statusCode == "300") {
							alertMsg.error(json.message);
						}
					}
				})
	}

	var accountIndexId = 1;
	function addContext() {
		accountIndexId=accountIndexId-0+1;
		var addMsg = '<tr>'
				+ '<td ><input id="table_customerName" name="name"></td>'
				+ '<td ><Field:codeTable id="agentGender" name="agentGender" nullOption="true" cssClass="combox" tableName="APP___PAS__DBUSER.T_GENDER" whereClause="gender_code in (1,2)" value="" onChange="agentGenderChange(this)"></Field:codeTable>'
				+ '<input id="table_Gender" name="agentGender" type="hidden"></td>'
				+ '<td ><Field:codeTable id="certType" name="certType" nullOption="true" cssClass="combox" 	tableName="APP___PAS__DBUSER.T_CERTI_TYPE1"  value="" onChange="certTypeChange(this)"/>'
				+ '	<input id="table_CertiType" name="certType" type="hidden"></td>'
				+ '<td ><input id="table_CertiCode" name="certiCode"></td>'
				+ '<td ><input id="table_birthDate" name="birthday" type="expandDateYMD" class="date"></td>'
				+ '<td ><input id="table_income" name="income"></td>'
				+ '<td style="display:none"><input id="table_acceptId" type="hidden" name="acceptId" value="${acceptId }"></td>'
				+ '<td ><a class="btnDel" onclick="deleteAccountLine(this)"></a>'
				+ '</td>' + '</tr>';
		$(addMsg).appendTo("#tbody_tr", navTab.getCurrentPanel()).initUI();
	}
	function deleteAccountLine(line) {
		$(line).parents("tr").first().remove();
	}
	function agentGenderChange(val) {
		genderstr +=$(val).val()+",";
		$("#table_Gender").val($(val).val());
	}
	function certTypeChange(val) {
		certiTypestr +=$(val).val()+",";
		$("#table_CertiType").val($(val).val());
	}

	function colsedialog() {
		$.pdialog.close("policyHolderDialogTable");
		$("#yearFlag", navTab.getCurrentPanel()).val("0");
		$("#yearFlag", navTab.getCurrentPanel()).attr("checked", false);
	}
</script>