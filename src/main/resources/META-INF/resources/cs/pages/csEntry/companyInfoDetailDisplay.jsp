<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/ChinesePostcode.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<%-- <script type="text/javascript" src="${ctx}/cs/js/check_name.js"></script>
 --%>

<script type="text/javascript" src="${ctx}/cs/pages/common/js/tax.js"></script>

<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">


<div class="backgroundCollor" style="background: #f5f6f8" layoutH="8">

	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />企业客户详情信息
		</h1>
	</div>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />单位信息
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>
				客户名称<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.companyName" class=""
					value="${csPayCompanyVO.companyName}" id="companyName" />
			</dd>
		</dl>
		<dl>
			<dt>
				注册地址<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.companyAddress" class=""
					value="${csPayCompanyVO.companyAddress}" id="companyAddress" />
			</dd>
		</dl>
		<dl>
			<dt>
				所属行业<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.companyIndustry" class=""
					value="${csPayCompanyVO.companyIndustry}" id="companyIndustry" />
			</dd>
		</dl>
		<dl>
			<dt>
				注册资本<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.regCapital" class=""
					value="${csPayCompanyVO.regCapital}" id="regCapital" />
			</dd>
		</dl>
		<dl>
			<dt>
				经营范围<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.businessCope" class=""
					value="${csPayCompanyVO.businessCope}" id="businessCope" />
			</dd>
		</dl>
		<dl>
			<dt>
				注册资本金币种<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.regCapitalCurrency" class=""
					readonly="readonly" value="RMB" id="regCapitalCurrency" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />组织机构信息
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>社会统一信用代码/组织机构代码</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.companyOrgCode" class=""
					value="${csPayCompanyVO.companyOrgCode}" id="companyOrgCode" />
			</dd>
		</dl>
		<dl>
			<dt>证件有效起期</dt>
			<dd>
				<input name="csPayCompanyVO.orgCodeStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.orgCodeStartDate" format="yyyy-MM-dd"/>"
					id="orgCodeStartDate" />
			</dd>
		</dl>


		<dl>
			<dt>证件有效止期</dt>
			<dd>
				<input name="csPayCompanyVO.orgCodeEndDate" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.orgCodeEndDate" format="yyyy-MM-dd"/>"
					id="orgCodeEndDate" />
			</dd>
		</dl>

		<dl>
			<dt>营业执照号码</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.busiLicenceCode" class=""
					value="${csPayCompanyVO.busiLicenceCode}" id="busiLicenceCode" />
			</dd>
		</dl>

		<dl>
			<dt>证件有效起期</dt>
			<dd>
				<input name="csPayCompanyVO.licenceStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.licenceStartDate" format="yyyy-MM-dd"/>"
					id="licenceStartDate" />
			</dd>
		</dl>
		<dl>
			<dt>证件有效止期</dt>
			<dd>
				<input name="csPayCompanyVO.licenceEndDate" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.licenceEndDate" format="yyyy-MM-dd"/>"
					id="licenceEndDate" />
			</dd>
		</dl>
		<dl>
			<dt>社会保障号</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.socialSecurityCode" class=""
					value="${csPayCompanyVO.socialSecurityCode}"
					id="socialSecurityCode" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />税务登记信息
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>税务登记证</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.taxCode" class=""
					value="${csPayCompanyVO.taxCode}" id="taxCode" />
			</dd>
		</dl>

		<dl>
			<dt>证件有效起期</dt>
			<dd>
				<input name="csPayCompanyVO.taxCodeStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.taxCodeStartDate" format="yyyy-MM-dd"/>"
					id="taxCodeStartDate" />
			</dd>
		</dl>


		<dl>
			<dt>证件有效止期</dt>
			<dd>
				<input name="csPayCompanyVO.taxCodeEndDate" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.taxCodeEndDate" format="yyyy-MM-dd"/>"
					id="taxCodeEndDate" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">受益所有人信息</img>
		</h1>
	</div>
	<div class="panelPageFormContent">
		<table class="list" id="csPayCompanyBeneListTable" width="90%"
			style="margin-left: 57px;">
			<thead>
				<tr>
					<th colName='beneName' inputType="input">受益所有人姓名</th>
					<th colName='beneCertiType' inputType="input">受益所有人证件类型</th>
					<th colName='beneCertiCode' inputType="input">受益所有人证件号码</th>
					<th colName='beneCertiStartDate' inputType="input">证件有效期起期</th>
					<th colName='beneCertiEndDate' inputType="input">证件有效期止期</th>
				</tr>
			</thead>
			<tbody id="csPayCompanyBeneListBody" class="t_body">
				<s:iterator value="csPayCompanyBeneList" status="st"
					var="csPayCompanyBeneVO">
					<tr tr_saveStatus="1" align="center" id="${logId}">
						<td><input type="text" name="csPayCompanyBeneVO.beneName"
							class="" value="${beneName}" id="beneName" /></td>
						<td><Field:codeTable name="csPayCompanyBeneVO.beneCertiType"
								value="${beneCertiType}"
								tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" cssClass="combox"
								nullOption="true" id="beneCertiType" /> <input
							id="beneCertiTypeInput" type="hidden" value="${beneCertiType}" /></td>
						<td><input type="text"
							name="csPayCompanyBeneVO.beneCertiCode" class=""
							value="${beneCertiCode}" id="beneCertiCode" /></td>
						<td><input name="csPayCompanyBeneVO.beneCertiStartDate"
							style="width: 146px;"
							value="<s:date name="beneCertiStartDate" format="yyyy-MM-dd"/>"
							id="beneCertiStartDate" /></td>
						<td><input name="csPayCompanyBeneVO.beneCertiEndDate"
							style="width: 146px;"
							value="<s:date name="beneCertiEndDate" format="yyyy-MM-dd"/>"
							id="beneCertiEndDate" /></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>

	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">控股股东或实际控股人信息</img>
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt style="width: 150px;">
				控股股东或实际控制人姓名<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.holdingPersonName" class=""
					value="${csPayCompanyVO.holdingPersonName}" id="holdingPersonName" />
			</dd>
		</dl>
		<dl>
			<dt style="width: 170px;">
				控股股东或实际控制人证件类型<font color="red">*</font>
			</dt>
			<dd>
				<Field:codeTable name="csPayCompanyVO.companyHolderCertiType"
					value="${csPayCompanyVO.companyHolderCertiType}"
					tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" nullOption="true"
					id="companyHolderCertiType" />
			</dd>
		</dl>
		<dl>
			<dt style="width: 150px;">
				控股股东或控制人证件号码<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.companyHolderCertiCode"
					class="" value="${csPayCompanyVO.companyHolderCertiCode}"
					id="companyHolderCertiCode" />
			</dd>
		</dl>

		<dl>
			<dt>
				证件有效起期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.holderCertiStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.holderCertiStartDate" format="yyyy-MM-dd"/>"
					id="holderCertiStartDate" />
			</dd>
		</dl>


		<dl>
			<dt>
				证件有效止期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.holderCertiEndDate"
					myOption="endDateComputer(this)"
					value="<s:date name="csPayCompanyVO.holderCertiEndDate" format="yyyy-MM-dd"/>"
					id="holderCertiEndDate" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">法定代表人信息</img>
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>
				法定代表人姓名<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.legalPersonName" class=""
					value="${csPayCompanyVO.legalPersonName}" id="legalPersonName" />
			</dd>
		</dl>
		<dl>
			<dt>
				法定代表人证件类型<font color="red">*</font>
			</dt>
			<dd>
				<Field:codeTable name="csPayCompanyVO.legalPersonCertiType"
					value="${csPayCompanyVO.legalPersonCertiType}"
					tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" nullOption="true"
					id="legalPersonCertiType" />
			</dd>
		</dl>

		<dl>
			<dt>
				法定代表人证件号码<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.legalPersonCertiCode"
					class="" value="${csPayCompanyVO.legalPersonCertiCode}"
					id="legalPersonCertiCode" />
			</dd>
		</dl>


		<dl>
			<dt>
				证件有效起期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.legalPerCertiStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.legalPerCertiStartDate" format="yyyy-MM-dd"/>"
					id="legalPerCertiStartDate" />
			</dd>
		</dl>

		<dl>
			<dt>
				证件有效止期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.legalPerCertiEndDate"
					style="width: 146px;"
					value="<s:date name="csPayCompanyVO.legalPerCertiEndDate" format="yyyy-MM-dd"/>"
					id="legalPerCertiEndDate" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">负责人信息</img>
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>
				负责人姓名<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.managerName" class=""
					value="${csPayCompanyVO.managerName}" id="managerName" />
			</dd>
		</dl>
		<dl>
			<dt>
				负责人证件类型<font color="red">*</font>
			</dt>
			<dd>
				<Field:codeTable name="csPayCompanyVO.managerCertiType"
					value="${csPayCompanyVO.managerCertiType}"
					tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" nullOption="true"
					id="managerCertiType" />
			</dd>
		</dl>
		<dl>
			<dt>
				负责人证件号码<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.managerCertiCode" class=""
					value="${csPayCompanyVO.managerCertiCode}" id="managerCertiCode" />
			</dd>
		</dl>
		<dl>
			<dt>
				证件有效起期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.managerCertiStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.managerCertiStartDate" format="yyyy-MM-dd"/>"
					id="managerCertiStartDate" />
			</dd>
		</dl>
		<dl>
			<dt>
				证件有效止期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.managerCertiEndDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.managerCertiEndDate" format="yyyy-MM-dd"/>"
					id="managerCertiEndDate" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">授权办理业务员信息</img>
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>
				授权办理业务人员姓名<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.operatorName" class=""
					value="${csPayCompanyVO.operatorName}" id="operatorName" />
			</dd>
		</dl>
		<dl>
			<dt>
				授权办理业务人员证件类型<font color="red">*</font>
			</dt>
			<dd>
				<Field:codeTable name="csPayCompanyVO.operatorCertiType"
					value="${csPayCompanyVO.operatorCertiType}"
					tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" nullOption="true"
					id="operatorCertiType" />
			</dd>
		</dl>
		<dl>
			<dt>
				授权办理业务人员证件号码<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.operatorCertiCode" class=""
					value="${csPayCompanyVO.operatorCertiCode}" id="operatorCertiCode" />
			</dd>
		</dl>
		<dl>
			<dt>
				证件有效起期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.operatorCertiStartDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.operatorCertiStartDate" format="yyyy-MM-dd"/>"
					id="operatorCertiStartDate" />
			</dd>
		</dl>
		<dl>
			<dt>
				证件有效止期<font color="red">*</font>
			</dt>
			<dd>
				<input name="csPayCompanyVO.operatorCertiEndDate"
					myOption="endDateComputer(this)" style="width: 146px;"
					value="<s:date name="csPayCompanyVO.operatorCertiEndDate" format="yyyy-MM-dd"/>"
					id="operatorCertiEndDate" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">判定受益人信息</img>
		</h1>
	</div>
	<div class="panelPageFormContent">
		<dl>
			<dt>
				判定受益人所有人方式<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.beneJudgeWay" class=""
					value="${csPayCompanyVO.beneJudgeWay}" id="beneJudgeWay" />
			</dd>
		</dl>
		<dl>
			<dt>
				持股比例或表决权占比<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.holdeRatio" class=""
					value="${csPayCompanyVO.holdeRatio}" id="holdeRatio" />
			</dd>
		</dl>
	</div>
	<div class="divfclass">
		<h1>
			<img src="cs/img/icon/tubiao.png">通讯单位地址</img>
		</h1>
	</div>
	<div class="panelPageFormContent" style="height:150px">
		<dl>
			<dt>
				国家<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.country" class="" value="中国"
					id="country" readonly="readonly" />
			</dd>
		</dl>
		<dl>
			<dt>
				省/直辖市<font color="red">*</font>
			</dt>
			<dd>
				<s:select list="districtMap" listKey="key" listValue="value"
					headerKey="all" headerValur="全部" cssClass="combox"
					id="province_panelInfoAdd" name="csPayCompanyVO.state"
					ref="city_panelInfoAdd" initval=" "
					refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}">
				</s:select>
			</dd>
		</dl>
		<dl>
			<dt>
				市<font color="red">*</font>
			</dt>
			<dd>
				<select id="city_panelInfoAdd" name="csPayCompanyVO.city"
					ref="district_panelInfoAdd" initval="${csPayCompanyVO.district }"
					refUrl="${ctx }/cs/common/getDistrict_PA_distictUtilAction.action?cityCode={value}"
					class="combox">
					<s:if
						test="csPayCompanyVO.city != null && csPayCompanyVO.city != '' ">
						<option value="${csPayCompanyVO.city }">
							<Field:codeValue tableName="APP___PAS__DBUSER.T_DISTRICT"
								value="${csPayCompanyVO.city }"></Field:codeValue>
						</option>
					</s:if>
					<s:else>
						<option value="">全部</option>
					</s:else>
				</select>
			</dd>
		</dl>
		<dl>
			<dt>
				区/县<font color="red">*</font>
			</dt>
			<dd>
				<select id="district_panelInfoAdd" name="csPayCompanyVO.district"
					class="combox">
					<s:if
						test="csPayCompanyVO.district != null && csPayCompanyVO.district != '' ">
						<option value="${csPayCompanyVO.district }" dis>
							<Field:codeValue tableName="APP___PAS__DBUSER.T_DISTRICT"
								value="${csPayCompanyVO.district }"></Field:codeValue>
						</option>
					</s:if>
					<s:else>
						<option value="">全部</option>
					</s:else>
				</select>
			</dd>
		</dl>
		<dl>
			<dt>
				详细地址<font color="red">*</font>
			</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.address" class=""
					value="${csPayCompanyVO.address}" id="address" />
			</dd>
		</dl>
		<dl>
			<dt>手机号</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.mobile" class=""
					value="${csPayCompanyVO.mobile}" id="mobile" />
			</dd>
		</dl>
		<dl>
			<dt>邮政编码</dt>
			<dd>
				<input type="text" name="csPayCompanyVO.postCode" class=""
					value="${csPayCompanyVO.postCode}" id="postCode" />
			</dd>
		</dl>
		<dl>
			<dt>备注</dt>
			<dd>
				<textarea rows="3" name="csPayCompanyVO.remark" class="" value=""
					id="remark">${csPayCompanyVO.remark}</textarea>
			</dd>
		</dl>
	</div>

</div>

<script type="text/javascript">
	$(function (){
		 $('input').prop('readonly', true);
		 $('select').attr('disabled', 'disabled');

	});   

</script>
