<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_accept.js"></script>
<script type="text/javascript"
	src="${ctx}/cs/pages/csCheck/appletLib/deployJava.js"></script>
<script type="text/javascript">
	$(document).ready(function(){ 
		var _top=($(window).height())/2+80;
		var _left=($(window).width())/2+185;
		$("#loadFikeNC").parent().parent().css({top:_top,left:_left});
		$(document).find("div.shadow").css({top:_top,left:_left});
	});
</script>
<form>
	<input type="hidden" id="customer_Name_ann" value=${customerVO.customerName }>
	<input type="hidden" id="holderCertiType_ann" value=${customerCertiType }>
	<input type="hidden" id="customerCertiCode_ann" value=${customerCertiCode }>
	<input type="hidden" id="holderGender_ann" value=${holderGender }>
	<input type="hidden" id="birthDate_ann" value=${birthDate }>
	<input type="hidden" id="acceptId_ann" value=${acceptId }>
</form>
<div id="policyHolderDialogSubmit" style="height: 90%">
			<div style="text-align: center;margin-top: 60px;">
				<div class="tabdivclass" >
					根据北京银保监局文件要求，如您的法定继承人包含北京分公司在职销售人员，需采集该名销售人员的五要素及年收入信息
				</div>
				<div>
					<button onclick="saveNaturalYearIncome()">确定</button>
					<button onclick="colsedialog()">返回</button>
				</div>
			</div>
			
</div>

<script>
	function saveNaturalYearIncome(){
		var projectPath = getRootPath();
		var acceptId  = $("#acceptId_ann").val();
		$.pdialog.open(projectPath+"/cs/csEntry/entryPageTable_PA_csEntryAction.action?acceptId="+acceptId,"policyHolderDialogTable","",{width:1050,height:300});
		$.pdialog.close("policyHolderDialogSubmit");
	}
	
	function colsedialog(){
		$.pdialog.close("policyHolderDialogSubmit");
		$("#yearFlag",navTab.getCurrentPanel()).val("0");
		$("#yearFlag",navTab.getCurrentPanel()).attr("checked",false);
	}
	
</script>