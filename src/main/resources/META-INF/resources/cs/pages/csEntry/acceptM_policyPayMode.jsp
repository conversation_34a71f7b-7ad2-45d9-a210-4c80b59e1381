<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-补退费信息-保单补退费div页面 -->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%> 
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">

<div id="premInfoDiv">
	<!-- 存储页面id -->
	<div class="main_clearfix" class="pageFormInfoContent">
		<div class="divfclass">
			<img src="${ctx}/cs/img/icon/tubiao.png" />保单补退费形式
		</div>
		<div id="policyPayModeChangeDiv">
			<a href="${ctx}/cs/csEntry/showAcceptAndArapInfo_PA_csEntryAction.action?&menuId=${menuId}&changeId=${changeId}&acceptId="
				target="navTab" title="保全录入" rel="${menuId}" style="display: none"
				id="showPremArapByacceptId">下一步</a>
				<input type="hidden" id="premArapAcceptId" value="${acceptId}">
			<form id="CspremArapFormId">
				<input type="hidden" id="qy_customerId"  name="csPremArapVO.qy_customerId" value="${qy_customerId}"></input>
				<input type="hidden" id="isSaveThird" name="isSaveThird" value="${isSaveThird}"></input>
                <input type="hidden" id="isShowThird" name="isShowThird" value="${isShowThird}"></input>
                <input type="hidden" id="isCrsFlag"   name="csPremArapVO.isCrsFlag" value="${isCrsFlag}"></input>
				<input type="hidden" id="qy_customerName" name="csPremArapVO.qy_customerName" value="${qy_customerName}"></input>
				<input type="hidden" id="changQiFlag"  name="changQiFlag" value="${changQiFlag }">
				<table class="list" style="width: 100%" id="policyTableId">
					<thead>
						<tr>
							<!-- <th>选择</th> -->
							<th colName='serviceCode'>保全项目</th>
							<th colName='payeeType' inputType="td">交费人类型</th>
							<th colName='policyCode' inputType="td">保单号</th>
							<th colName='amountMany' inputType="">金额</th>
							<th colName='arapAndPrem'>补退费标识</th>
							<th colName='payMode' inputType="select">补退费形式</th>
							<th style="width: 120px" colName='medicalNo' inputType="td">医保卡号</th>
							<s:if test="queryFlag != 1&&chooseFlag != 1">
								<th colName='policyPremArapBank' inputType="select">账户信息</th>
							</s:if>
							<s:if test="queryFlag != 1&&chooseFlag == 1">
								<th colName='policyPremArapBank' inputType="td">账户信息</th>
							</s:if>
							<th colName= "openingBankName" style="display: none">开户行名称</th>
							<th colName= "publicIdentification" style="display: none">对公标识</th>
							<s:if test="queryFlag == 1">
								<th colName='bankCode' inputType="input">银行代码</th>
								<th colName='code' inputType="input">账号</th>
								<th colName='bankName' inputType="input" id="bankName">户名</th>
							</s:if>
<%-- 							<s:if test="csPolicyPremArapVO.arapAndPrem =='应付'"> --%>
<!-- 								<th colName='getMoneyName' inputType='input'>领款人姓名</th> -->
<!-- 								<th colName='customerCertType' inputType="select">领款人证件类型</th> -->
<!-- 								<th colName='customerCertiCode' inputType='input'>领款人证件号码</th> -->
<%-- 							</s:if> --%>
<%-- 							<s:if test="csPolicyPremArapVO.arapAndPrem =='应收'"> --%>
<!-- 								<th colName='getMoneyName' inputType='input'>付款人姓名</th> -->
<!-- 								<th colName='customerCertType' inputType="select">付款人证件类型</th> -->
<!-- 								<th colName='customerCertiCode' inputType='input'>付款人证件号码</th> -->
<%-- 							</s:if> --%>
<%-- 							<s:if test="csPolicyPremArapVO.arapAndPrem =='非应付/应付'"> --%>
								<th colName='getMoneyName' inputType='input'>户名/收款人姓名</th>
								<th colName='customerCertType' inputType="select" >领款人证件类型</th>
								<th colName='customerCertiCode' inputType='input'  >领款人证件号码</th>
								<th colName='custCertStarDate' inputType='input'  >证件有效期起期</th>
								<th colName='custCertEndDate' inputType='input'  >证件有效期止期</th>
								<th colName='payStakeReasion' inputType='input'  >支付至非权益人账户原因</th>
<%-- 							</s:if> --%>
							<th colName="businessCode" style="display: none">受理号</th>
							<th colName="qy_customerId" style="display: none">权益人Id</th>
							<th colName="qy_customerName" style="display: none">权益人姓名</th>
							<th colName='customerGender' inputType="select" >领款人性别</th>
							<th colName='customerBirthday' inputType='input' >领款人出生日期</th>
							<th colName='taxResidentType' inputType='input'>税收居民身份</th>
							<th colName='taxCountry' inputType='input'>税收居民国(地区)</th>
							<th colName='taxsqzh' style="display: none" inputType='input'>首期账户</th>
							<th colName='taxxqzh' style="display: none" inputType='input'>续期账户</th>
							<th colName='redonlyFlag' style="display: none" inputType='input'>是否只读</th>
							<th colName='countryCode' style="display: none" inputType='input'>领款人国籍</th>
							<th colName='payAccountUsed' style="display: none" inputType='input'>使用过的账户</th>
							
							
							
							
						</tr>
					</thead>
					<tbody id="policypayMoel">
						<s:iterator value="#csPolicyPremArap.csPremArapVOs" var='csArapVo'>

							<tr align="center" tr_saveStatus="1" id="holloshit">
								<%-- <td><input type="radio" name='showAcceptId' onchange="showPremArapByacceptId(${var1.acceptId})"  
						<s:if test="acceptId== showAcceptId">checked</s:if>></td> --%>
								<td name="csPremArapVO.serviceCode" id="serviceCode"><Field:codeValue
										tableName="APP___PAS__DBUSER.T_SERVICE" value="${serviceCode}" /><input
									type="hidden" id="hiddenServiceCode" value="${serviceCode}" />
									<input type="hidden" id="payMode" value="${payMode}" />
									<input type="hidden" id="oldPayMode" value="${payMode}" /></td>
								<s:if test='flag928 == "1" && serviceCode == "AM"'>
									<td>
									   <Field:codeTable name="csPremArapVO.payeeType" cssClass="combox" tableName="APP___PAS__DBUSER.T_PAYEE_TYPE"
                                               value="1" disabled="false"  id="payeeType" onChange="payeeTypeChange(this)"/>
									</td>
								</s:if>
								<s:else>
									<td>
									   <Field:codeTable name="csPremArapVO.payeeType" cssClass="combox" tableName="APP___PAS__DBUSER.T_PAYEE_TYPE"
                                               value="1" disabled="true"  id="payeeType" onChange="payeeTypeChange(this)"/>
									</td>
								</s:else>
								<td name="csPremArapVO.policyCode" style="width: 190px" id="policyCode">${policyCode}</td>
								<td id='amountMany' align="right" name="csPremArapVO.amountMany" style="color: red;font-weight: bolder;width: 190px;	">${amountMany}</td>
								<td name="csPremArapVO.arapAndPrem" id="arapAndPrem">${arapAndPrem}</td>
								<input type="hidden" id="arapAndPremss" value="${arapAndPrem}" />
								<td id = "payModeBankZRR"><s:if test="queryFlag == 1">
										<Field:codeValue tableName="APP___PAS__DBUSER.T_PAY_MODE"
											value="${payMode}" />
									</s:if>
									<s:else>
										<!-- 1:不能选 -->
										<s:if test="chooseFlag == 1 ">
											<Field:codeTable name="csPremArapVO.payMode"
												cssClass="combox" tableName="APP___PAS__DBUSER.T_PAY_MODE"
												value="${payMode}" disabled="true"
												id="payModeBank${qy_customerId}${businessCode}" />
										</s:if>
										<s:elseif test="chooseFlag == 2">
										    <Field:codeTable name="csPremArapVO.payMode"
                                                cssClass="combox" tableName="APP___PAS__DBUSER.T_PAY_MODE"
                                                value="${payMode}" disabled="true"
                                                id="payModeBank${qy_customerId}${businessCode}" />
										</s:elseif>
										
										<input id="ylaccountflags" type = "hidden"  name="csspecialaccountinfovo.ylaccountflag" value="${csSpecialAccountInfoVO.ylaccountflag}" />
										
										<s:elseif test="csSpecialAccountInfoVO.ylaccountflag== 1">
											<label style="width: 160px">
											<Field:codeTable name="csPremArapVO.payMode" cssClass="combox" disabled="true"
												tableName="${'应付'==arapAndPrem?'APP___PAS__DBUSER.T_POLICY_PAY_MODE':'APP___PAS__DBUSER.T_POLICY_PAY_MODE_COLL'}"
												value="32" id="payModeBank${qy_customerId}${businessCode}"
												defaultValue="32" onChange="payChanBank(this)" />
											</label>
										</s:elseif>
										<s:else>
											<label style="width: 160px">
											<Field:codeTable name="csPremArapVO.payMode" cssClass="combox"
												tableName="${'应付'==arapAndPrem?'APP___PAS__DBUSER.T_POLICY_PAY_MODE':'APP___PAS__DBUSER.T_POLICY_PAY_MODE_COLL'}"
												value="${payMode}" id="payModeBank${qy_customerId}${businessCode}"
												defaultValue="32" onChange="payChanBank(this)" />
											</label>
										</s:else>
									</s:else>
								</td>
								<td id = "payModeBankQY" style = "display:none" >
									<label style="width: 160px">
									<Field:codeTable name="csPremArapVO.payMode" cssClass="combox"
										tableName="${'应付'==arapAndPrem?'APP___PAS__DBUSER.T_POLICY_PAY_MODE':'APP___PAS__DBUSER.T_POLICY_PAY_MODE_QY'}"
										value="${payMode}" id="payModeBank${qy_customerId}${businessCode}"
										defaultValue="32" onChange="payChanBank(this)" />
									</label>
								</td>
								<td name="csPremArapVO.medicalNo" id="medicalNo">${medicalNo}</td>
								<s:if test="queryFlag != 1&&chooseFlag != 1">
									<td>
									<label style="width: 210px" id="bankAccountInfoSelectBlock"> 
									<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<select name="csPremArapVO.policyPremStr" id="policyPremStr">
											<option
													value="${csSpecialAccountInfoVO.customerId},${csSpecialAccountInfoVO.accountBank},${csSpecialAccountInfoVO.account},${csSpecialAccountInfoVO.accountName}" 
													id="bankAccountInfo"  >${csSpecialAccountInfoVO.account} / ${csSpecialAccountInfoVO.accountBank}</option>
										</select>				
									</s:if>
									<s:else>
										<select name="csPremArapVO.policyPremStr" id="policyPremStr"> 
												<s:iterator value="csBankAccountVOs" status="st" >
													<s:if test="accountId+','+bankCode+','+bankAccount+','+accoName==policyPremArapBank">
														<option
															value="${accountId},${bankCode},${bankAccount},${accoName}"
															id="bankAccountInfo" selected>${bankAccount} / ${bankCode}</option>
													</s:if>
													<s:else>
														<option
															value="${accountId},${bankCode},${bankAccount},${accoName}"
															id="bankAccountInfo">${bankAccount} / ${bankCode}</option>
													</s:else>
												</s:iterator>
										</select>
									</s:else>
										
								 	  <s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<input id="ylaccount" type = "hidden"  name="csspecialaccountinfovo.account" value="${csSpecialAccountInfoVO.account}" />
									  </s:if>
										
										<input id="policyPremStrInput" value="" onchange="checkbankStr(this)" style="display: none"/>
										<font style="color: red; font-size: 11px; width: 150px;"
													id="strDIV"></font> <input type="hidden" id="checkname" value="" />
									</label>
									</td>
								</s:if>	
								
								<s:elseif test="queryFlag != 1&& chooseFlag == 2">
								    <td>
                                        <label style="width: 210px" id="bankAccountInfoSelectBlock"> 
                                            <select name="csPremArapVO.policyPremStr" id="policyPremStr">
                                                    <s:iterator value="csBankAccountVOs" status="st">
                                                        <s:if test="accountId+','+bankCode+','+bankAccount+','+accoName==policyPremArapBank">
                                                            <option
                                                                value="${accountId},${bankCode},${bankAccount},${accoName}"
                                                                id="bankAccountInfo" selected>${bankAccount} / ${bankCode}</option>
                                                        </s:if>
                                                        <s:elseif test="csSpecialAccountInfoVO.ylaccountflag== 1">
														<option
															value="${accountId},${bankCode},${bankAccount},${accoName}"  
															id="bankAccountInfo" selected  disabled="true">${csSpecialAccountInfoVO.account} / ${bankCode}</option>
														</s:elseif>
                                                        <s:else>
                                                            <option
                                                                value="${accountId},${bankCode},${bankAccount},${accoName}"
                                                                id="bankAccountInfo">${bankAccount} / ${bankCode}</option>
                                                        </s:else>
                                                    </s:iterator>
                                            </select>
                                            <input id="policyPremStrInput" value="" onchange="checkbankStr(this)" style="display: none" />
                                        	<font style="color: red; font-size: 11px; width: 150px;"
													id="strDIV"></font> <input type="hidden" id="checkname" value="" />
                                        </label>
                                    </td>
								</s:elseif>
								
									<td colName= "openingBankNameCon" style="display: none">
									<input id="inputOrg1" name="org1.orgNum" value="" type="hidden"/>
									<input class="required" name="org1.orgName" type="text" postField="keyword" readonly="readonly"
									suggestFields="orgNum,orgName" suggestUrl="cs/pages/csEntry/districtBankCodeQuery.jsp" lookupGroup="org1"/>
									<a class="btnLook" href="${ctx}/cs/csEntry/selectReturnNumber_PA_csEntryAction.action" lookupGroup="org1" style="float: right;">查找带回</a>
									<dl class="nowrap" style="display: none">
										<dt>部门编号A1：</dt>
											<dd>
												<input class="readonly" name="org1.orgNum" readonly="readonly" type="text"/>
											</dd>
									</dl>
									
									<!-- <a id="menuBtn" class="btnLook" href="#" onfocus="checkSearch('queryApplicant')" style="float: right;"></a> -->
									</td>
									<!-- 对公标识 -->
									<td colName= "publicIdentificationCon" style="display: none">
										<Field:codeTable name="publicIdentification" cssClass="combox"
										value="" tableName="APP___PAS__DBUSER.T_YES_NO"
										defaultValue="0" onChange="identificationCor(this)" />
									</td>
								<s:if test="queryFlag != 1&&chooseFlag == 1">
									<td>
										<label style="width: 190px" id="bankAccountInfoSelectBlock">
									    	<select name="csPremArapVO.policyPremStr" id="policyPremStr">
												<option value="${policyPremArapBank}" selected>${showPolicyPremArapBank}</option>
											</select>
										</label>
									</td>
								</s:if>								
								<s:if test="queryFlag == 1">
									<%-- <s:iterator value="csBankAccountVOs"> --%>
									<td name="csBankAccountVOs.bankCode" id="bankCode">${csBankAccountVOs[0].bankCode}</td>
									<td name="csBankAccountVOs.bankAccount" id="bankAccount">${csBankAccountVOs[0].bankAccount}</td>
									<td name="csBankAccountVOs.accoName" id="accoName">${csBankAccountVOs[0].accoName}</td>
									<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<td id="getMoneyName" disabled="true">${csSpecialAccountInfoVO.customerName}</td>
									</s:if>	
									<s:else>
										<td id="getMoneyName">${payeeName}</td>
									</s:else>
									<td><Field:codeValue value="${certiType }" tableName="APP___PAS__DBUSER.T_CERTI_TYPE"  /></td>
									<td id="customerCertiCode">${certiCode}</td>
									<td><input id="custCertStarDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertStarDate" nullOption="true"
									  myOption="getEndDate(this);" value="<s:date format="yyyy-MM-dd" name="payeeCertStarDate"/>" /></td>
									<td><input   <s:if test="certiType == 5">readonly</s:if><s:else>type="expandDateYMD"</s:else> id="custCertEndDate"  name="payeeCertEndDate" nullOption="true"
									 onclick="onblurs()" value="<s:date format="yyyy-MM-dd" name="payeeCertEndDate"/>" />
									<input <s:if test="certiType == 5">disabled="disabled"</s:if> type="checkbox" onclick="endDateClear()" id="longDate" value="" />长期</td>
									<td><Field:codeTable name="payStakeReasion"
												cssClass="combox" tableName="APP___PAS__DBUSER.T_PAY_STAKE_REASION"
												value="${payStakeReasion}" disabled="false" nullOption="true" defaultValue="请选择"
												id="payStakeReasion" /></td>
								</s:if>
								<s:else>
									<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
									<td  ><input id="getMoneyName" name="csPremArapVO.getMoneyName" value="${csSpecialAccountInfoVO.customerName}" 	onchange="checkThirdPerson(this);checkCustomerCertiCodeAndName(this);" onkeyup="moneyNameAcceptKeyUp(this)" 
										onblur="checkLength(this);" disabled="disabled" <s:if test="chooseFlag == 1" > readonly="readonly"</s:if> />
									</td>	
									</s:if>	
									<s:else>
										<td id="getMoneyNameZRR">
											<s:if test= "payeeType != 2">
												<input id="getMoneyName"  name="csPremArapVO.getMoneyNameZRR" value="${payeeName}" 	onchange="checkThirdPerson(this);checkCustomerCertiCodeAndName(this);" onkeyup="moneyNameAcceptKeyUp(this)" 
												  onblur="checkNameLength(this);"  <s:if test="chooseFlag == 1"> readonly="readonly"</s:if> />
											</s:if>
											<s:else>
												<input id="getMoneyName"  name="csPremArapVO.getMoneyNameZRR" value="" 	onchange="checkThirdPerson(this);checkCustomerCertiCodeAndName(this);" onkeyup="moneyNameAcceptKeyUp(this)" 
												  onblur="checkNameLength(this);" <s:if test="chooseFlag == 1"> readonly="readonly"</s:if> />
											</s:else>
										</td>	
										<td id="getMoneyNameQY"  style = "display:none">
											<s:if test= "payeeType == 2">
												<input id="getMoneyName"  name="csPremArapVO.getMoneyName" value="${payeeName}" onblur="checkLength(this);"  readonly="readonly" /> 		
												<a class="btnLook" id="companyInfo" rel="companyInfoID"  href="${ctx}/cs/csPayCompany/loadCompanyInfo_PA_csPayCompanyAction.action?acceptCode=${businessCode}" 
												lookupGroup="csPremArapVO"
												maxable="false" minable="false" z-index ="1" resizable="false" title="" width="1200" height="500"  style="float: right;">企业客户信息录入
												</a>
												<input type="hidden" id="payCompanyId"  name="csPremArapVO.payCompanyId" value="" />
											</s:if>
											<s:else>
												<input id="getMoneyName"  name="csPremArapVO.getMoneyName" value="" onblur="checkLength(this);"  readonly="readonly" /> 		
												<a class="btnLook" id="companyInfo" rel="companyInfoID"  href="${ctx}/cs/csPayCompany/loadCompanyInfo_PA_csPayCompanyAction.action?acceptCode=${businessCode}" 
												lookupGroup="csPremArapVO"
												maxable="false" minable="false" z-index ="1" resizable="false" title="" width="1200" height="500"  style="float: right;">企业客户信息录入
												</a>
												<input type="hidden" id="payCompanyId"  name="csPremArapVO.payCompanyId" value="" />
											</s:else>
										
											 
										</td>	
									</s:else>
									<s:if test="redonlyFlag != 0 && redonlyFlag != null">
									<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<td ><select name="csPremArapVO.customerCertiType" id="customerCertiType${qy_customerId}${businessCode}"
												 cssClass="combox" style="width: 190px" onchange="checkMoneyName(this);">
												<option <s:if test="specialFlag==1">selected</s:if> 
												value="${csSpecialAccountInfoVO.customerCertType }" ><Field:codeValue value="${csSpecialAccountInfoVO.customerCertType  }" tableName="APP___PAS__DBUSER.T_CERTI_TYPE" /></option>
										</select> 
										<input type = "hidden"  name="csPremArapVO.customerCertiType" value="${csSpecialAccountInfoVO.customerCertType  }" />
											 </td>
									</s:if>	
									<s:else>
										<td ><select name="csPremArapVO.customerCertiType" id="customerCertiType${qy_customerId}${businessCode}"
												 cssClass="combox" style="width: 190px" onchange="checkMoneyName(this);">
												<option <s:if test="specialFlag==1">selected</s:if> 
												value="${certiType }" ><Field:codeValue value="${certiType }" tableName="APP___PAS__DBUSER.T_CERTI_TYPE"  /></option>
										</select> 
										<input type = "hidden"  name="csPremArapVO.customerCertiType" value="${certiType }" />
											 </td>
									</s:else>
									</s:if>
									<s:else>
									<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<td ><Field:codeTable name="csPremArapVO.customerCertiType" onChange="checkMoneyName(this);"
											value="${csSpecialAccountInfoVO.customerCertType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE" nullOption="true"
											id="customerCertiType${qy_customerId}${businessCode}"
											  disabled="true"/>
											<input type = "hidden"  name = "hiddenCertiType" value="${csSpecialAccountInfoVO.customerCertType  }"/>
									</td>
									</s:if>	
									<s:else>
										<td id="customerCertiTypeZRR" ><Field:codeTable name="csPremArapVO.customerCertiType" onChange="checkMoneyName(this);"
											value="${certiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE" nullOption="true"
											id="customerCertiType${qy_customerId}${businessCode}" 
											/>
											<input type = "hidden"  name = "hiddenCertiType" value="${certiType }"/>
										</td>
										<td id="customerCertiTypeQY" style = "display:none;">
											<textarea rows="3" disabled="true" >营业执照号、社会统一信用代码证/组织机构代码证、税务登记证</textarea>
										</td>
									</s:else>
									</s:else>
									
									<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<td  ><input id="customerCertiCode" 
										name="csPremArapVO.customerCertiCode"  
										onchange="checkThirdPerson(this);checkCustomerCertiCodeAndName(this);"
										value="${csSpecialAccountInfoVO.customerCertiCode }" maxlength="50"  disabled="disabled"<s:if test="redonlyFlag != 0 && redonlyFlag != null"> readonly="readonly"</s:if> /> </td>
									</s:if>	
									<s:else>
										<td id="customerCertiCodeZRR">
											<s:if test= "payeeType != 2">
												<input id="customerCertiCode" 
												name="csPremArapVO.customerCertiCodeZRR"  
												onchange="checkThirdPerson(this);checkCustomerCertiCodeAndName(this);"
												value="${certiCode }" maxlength="50" <s:if test="redonlyFlag != 0 && redonlyFlag != null"> readonly="readonly"</s:if> /> 
											</s:if>
											<s:else>
												<input id="customerCertiCode" 
												name="csPremArapVO.customerCertiCode"  
												onchange="checkThirdPerson(this);checkCustomerCertiCodeAndName(this);"
												value="" maxlength="50" <s:if test="redonlyFlag != 0 && redonlyFlag != null"> readonly="readonly"</s:if> /> 
											</s:else>
										</td>
										<td id="customerCertiCodeQY"  style = "display:none">
											<s:if test= "payeeType == 2">
												<textarea rows="3" disabled="true" id="customerCertiCode" name="csPremArapVO.customerCertiCode" >${certiCode }</textarea>
											</s:if>
											<s:else>
												<textarea rows="3" disabled="true" id="customerCertiCode" name="csPremArapVO.customerCertiCode" ></textarea>
											</s:else>
										</td>
									</s:else>
									
									<td id="custCertStarDateZRR"><input id="custCertStarDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertStarDate" nullOption="true"
									  myOption="getEndDate(this);" value="<s:date format="yyyy-MM-dd" name="payeeCertStarDate"/>" /></td>
									<td id="custCertStarDateQY" style = "display:none"><input id="custCertStarDate" type="hidden" name="custBaseInfoUpdateVO.customerVO.custCertStarDate"  /></td>
									<td id="custCertEndDateZRR"><input style="margin-top: 20px;"  <s:if test="certiType == 5">readonly</s:if><s:else>type="expandDateYMD"</s:else> id="custCertEndDate"  name="payeeCertEndDate" nullOption="true"
									 onclick="onblurs()" value="<s:date format="yyyy-MM-dd" name="payeeCertEndDate"/>" /><input <s:if test="certiType == 5">disabled="disabled"</s:if> type="checkbox" onclick="endDateClear()" id="longDate" value="" />长期</td>
									<td id="custCertEndDateQY" style = "display:none"><input type="hidden" id="custCertEndDate"  name="payeeCertEndDate" nullOption="true" /></td>
									<td id="payStakeReasionZRR">
										<Field:codeTable name="payStakeReasion"
												cssClass="combox" tableName="APP___PAS__DBUSER.T_PAY_STAKE_REASION"
												value="${payStakeReasion}" disabled="false" nullOption="true" defaultValue="请选择"
												id="payStakeReasion" />
									</td>
									<td id="payStakeReasionQY" style = "display:none">
										<input type="hidden" name="payStakeReasion" value="" id="payStakeReasion" />
									</td>
									
								</s:else>
								
								
								
								<td style="display: none" id="businessCode">${businessCode}</td>
								<td style="display: none" id="acceptId">${acceptId}</td>
								<td style="display: none" id="qy_customerId">${qy_customerId}</td>
								<td style="display: none" id="qy_customerName">${qy_customerName}</td>
								<s:if test="csSpecialAccountInfoVO.ylaccountflag== 1">
										<td ><Field:codeTable name="csPremArapVO.customerGender" tableName="APP___PAS__DBUSER.T_GENDER" nullOption="true"
								id="customerGender${qy_customerId}${businessCode}" value="${csSpecialAccountInfoVO.customerGender}" disabled="true"/></td>
								<td ><input disabled="disabled" id="customerBirthday" value="<s:date name="csSpecialAccountInfoVO.customerBirthday"  format="yyyy-MM-dd" />"/></td>
									</s:if>	
								<s:else>
									<td id="customerGenderZRR">
										<Field:codeTable name="csPremArapVO.customerGender" tableName="APP___PAS__DBUSER.T_GENDER" nullOption="true"
										id="customerGender${qy_customerId}${businessCode}" value="${customerGender}"/>
									</td>
									<td id="customerGenderQY"  style = "display:none">
										<input type="hidden" name="csPremArapVO.customerGender" tableName="APP___PAS__DBUSER.T_GENDER" nullOption="true"
										id="customerGender${qy_customerId}${businessCode}" value=""/>
									</td>
									<td id="customerBirthdayZRR"><input id="customerBirthday" value="<s:date name="customerBirthday"  format="yyyy-MM-dd" />"/></td>
									<td id="customerBirthdayQY"  style = "display:none"><input type="hidden" id="customerBirthday" name="customerBirthday" /></td>
								</s:else>
								
								
								<td id="taxResidentTypeZRR"><input id="taxResidentType" name="csPremArapVO.taxResidentType" value="${taxResidentType }" disabled="true" /></td>
								<td id="taxResidentTypeQY"  style = "display:none"><input type="hidden" id="taxResidentType" name="csPremArapVO.taxResidentType" value="" disabled="true" /></td>
								<td id="taxCountryZRR"><input id="taxCountry" name="csPremArapVO.taxCountry" value="${taxCountry }" disabled="true"/></td>
								<td id="taxCountryQY"  style = "display:none"><input type="hidden" id="taxCountry" name="csPremArapVO.taxCountry" value="" disabled="true"/></td>
								
								<td style="display: none" ><input id="downPayAccount" name="" value="${downPayAccount }" /></td>
								<td style="display: none" ><input id="nextPayAccount" name="" value="${nextPayAccount }" /></td>
								<td style="display: none" ><input id="csServiceCode" name="csServiceCode" value="${serviceCode }" /></td>
								<td style="display: none" ><input id="redonlyFlag" name="redonlyFlag" value="${redonlyFlag }" /></td>
								<td style="display: none" ><input id="cusCountryCode" name="countryCode" value="${countryCode }" /></td>
								<td style="display: none" ><input id="payAccountUsed" name="payAccountUsed" value="${payAccountUsed }" /></td>
								
								
							</tr>
						</s:iterator>
					</tbody>
				</table>
				
				<div class="pageFormbut">
					<s:if test="queryFlag != 1&&premSave = 1">
						<dl id="buttonDiv" class="nowrap" style="width: 100%">
							<dd style="width: 100%" id="saveMes">
								<table style="width: 100%">
									<tbody>
										<tr>
											<td></td>
											<s:if test="isShowThird == 1">
												<td style="width: 100px">
												<!-- RM101239 修改自动勾选--><!-- 101239 ALM18127 disabled -->
												<input onclick="isChecked(this);" id="payToThirdPerson"   name="checkbox"type="checkbox" <s:if test="isSaveThird != 1">disabled</s:if> <s:if test="isSaveThird == 1">checked='checked'</s:if>/>付款至第三人
											    </td>
												<td style="width: 60px">
													<button type="button" class="but_blue"
													onclick="queryCrsCustomer(this)">查询</button>
												</td>
											</s:if>
											<s:if test="isShowThird == 0">
												<td style="width: 100px;display: none" >
												<!-- RM101239 修改自动勾选 --><!-- 101239 ALM18127 disabled -->
												<input  onclick="isChecked(this);" id="payToThirdPerson" name="checkbox"type="checkbox" <s:if test="isSaveThird != 1">disabled</s:if> <s:if test="isSaveThird == 1">checked='checked'</s:if>/>付款至第三人
											    </td>
											</s:if>
											
											<td style="width: 60px">
												<button type="button" class="but_blue" 
													onclick="saveCsPolicyPayMode(this)">保存</button>
											</td>
											<s:if test="isShowThird == 1">
												
											<td style="width: 140px" >
												<button type="button" id="showtax" class="but_gray" onclick="showTax()"
												disabled="true">居民税收身份采集</button>
											</td>
											
											
											</s:if>
											
											<td></td>
										</tr>
									</tbody>
								</table>
							</dd>
						</dl>
					</s:if>
					<s:if test="queryFlag == 1">
						<dl id="buttonDiv1" style="width: 100%">
							<dd style="width: 100%" id="searchImage">
								<table style="width: 100%">
									<tbody>
										<tr>
											<td></td>
											<td style="width: 80px">
												<button type="button" class="but_blue"
													onclick="searchImage(this)">查询影像</button>
											</td>
											<td></td>
										</tr>
									</tbody>
								</table>
							</dd>
						</dl>
					</s:if>
				</div>
			</form>
		</div>
	</div>
	<div id="csSurveyCrsDiv" class="divfclass">
	</div>
	<!-- 银行账户 -->
	<s:if
		test="queryFlag != 1&&premArapShowFlag!=1&&csPremArapVOList != null && csPremArapVOList.size()>0">
		<div id="bankAccountInfoDiv">
			<s:include value="acceptM_BankAccount.jsp"></s:include>
		</div>
	</s:if>
<script type="text/javascript">





	function showTax(){
		var title = "居民税收身份采集";
		var fresh = eval("true");
		var external = eval("false");
		var tabid = "showResultShengDiao";
		if(customerIdNew==null){
			var customerId = "${qy_customerId}";
		}else{
			var customerId = customerIdNew;	
		}
		var url = "${ctx }/common/demand/taxInfoLoad_PA_CustomerTaxInfoAction.action?customerTacInfVO.customerId="+customerId;
		$.ajax({
			type : 'POST',
			url : url,
			success : function(data) {
				var dataStr = data.toString();
				if(dataStr.indexOf("\"statusCode\":\"300\"") < 0){
					navTab.openTab(tabid, url, {
						title : title,
						fresh : fresh,
						external : external
					});
				}else{
					var json = DWZ.jsonEval(data);
					if(json.statusCode == "300"){
						alertMsg.error(json.message);
						return false;
					}
				}
			},
			error : DWZ.ajaxError
		});
	}
	
$(function() {
	
	var certiEndDate = $("#custCertEndDate", navTab.getCurrentPanel()).val();//长期是否
	if(certiEndDate == '9999-12-31'){
		$("#changQiFlag").val("1");
		$("#custCertEndDate").val("");
		$("#custCertEndDate").attr( 'disabled', true);
		$("#longDate").attr("checked","checked");
	}
	var isCrsFlag = $("#isCrsFlag", navTab.getCurrentPanel()).val();
	var isSaveThird = $("#isSaveThird", navTab.getCurrentPanel()).val();
	if(null != isSaveThird  && isSaveThird==1){
		$("#payToThirdPerson", navTab.getCurrentPanel()).attr("checked","checked");
	
	}
	if(isCrsFlag==1){
		document.getElementById("showtax").disabled=false;
		document.getElementById("showtax").className="but_blue";
	}
	var policyCodes = "";
	$("#policypayMoel",navTab.getCurrentPanel()).find("td:eq(2)").each(function(){
		policyCodes = policyCodes +"'"+ $(this).text()+"',";
	});
	var customerId = "${qy_customerId}";	
	var parentDiv=$("input[value="+customerId+"]").parents("#policyPayModeInfoDiv");
	var rel = $("#bankAccountInfoDiv",parentDiv);
	var changeId = $("#changeId",navTab.getCurrentPanel()).val();
	var acceptId = $("#acceptId",navTab.getCurrentPanel()).val();
	var customerName = "${qy_customerName}";
	bank_index++;
	debugger;
	rel.loadUrl("${ctx}/cs/common/loadBankAccountInfoPremArap_PA_bankAccountInfoAction.action","customerId="
					+ customerId
					+ "&policyCodes="
					+ policyCodes
					+ "&changeId="
					+ changeId
					+ "&customerName=" 
					+ encodeURI(customerName)
					+"&acceptId="+acceptId);
	var parentTrs=$("#policypayMoel tr",parentDiv);
	for(var i=0;i<parentTrs.size();i++){ 
		parentTr=parentTrs.get(i);
		var payeeType =  $("#payeeType",parentTr).val();
		var companyInfo =  $("#companyInfo",parentTr).val();
		debugger;
		if(payeeType!=null && payeeType!="" && payeeType =="2"){
			$("#companyInfo",parentTrs).show();
		}else{
			$("#companyInfo",parentTrs).hide();
		}
	}
});

	$(document).ready(function() {
		_cs_applyColVal();//初始化table的th选择checkbox，其列中的值都付给th的checkbox的值
		var payModeBank=$("#payModeBank").val();
		if(""==payModeBank){
			$("#payModeBank").attr("value","30");
		}
		var parentDiv=$("#payModeBank"+"${qy_customerId}"+"${businessCode}").parents("#policyPayModeInfoDiv").get(0);
		$("select[name='csPremArapVO.payMode']",parentDiv).change(function(){
			var parentTr=$(this,navTab.getCurrentPanel()).parents("tr", navTab.getCurrentPanel()).get(0);
			var paymode = $("select[name='csPremArapVO.payMode']",parentTr).val();
			if(paymode == '31'||paymode == '32'||paymode == '34'|| paymode == '22'){
				$("#bankAccountInfoSelectBlock",parentTr).show();
			}else{
				$("#bankAccountInfoSelectBlock",parentTr).hide();
				$("#policyPremStr",parentTr).val("");
			}
		});	
		
		
		$("select[name='csPremArapVO.payMode']",parentDiv).each(function(){
			//切换退补费形式确定显示账户信息liqd
			var parentTr=$(this).parents("tr").get(0);
			var paymode = $("select[name='csPremArapVO.payMode']",parentTr).val();
			
			var arapAndPrem = $(parentTr, navTab.getCurrentPanel()).find("#arapAndPremss").val();
			if(arapAndPrem == '应付'){
			//	$("th[colName='openingBankName']",navTab.getCurrentPanel()).removeAttr("style");
				$("th[colName='publicIdentification']",navTab.getCurrentPanel()).removeAttr("style");
			//	$("td[colName='openingBankNameCon']",navTab.getCurrentPanel()).removeAttr("style");
				$("td[colName='publicIdentificationCon']",navTab.getCurrentPanel()).removeAttr("style");
			}
			if(${!'1'.equals(queryFlag)} && ${'1'.equals(chooseFlag)}){//不可选择收付方式
				if(paymode == '31'||paymode == '32'||paymode == '34'||paymode == '22'){ 
					$("#bankAccountInfoSelectBlock",parentTr).show();
				}else{
					$("#bankAccountInfoSelectBlock",parentTr).hide();
					$("#policyPremStr",parentTr).val("");
				}
			}else{
				$("select[name='csPremArapVO.payMode']",parentTr).change();	
			}
		});	
		
	});
	
	//校验姓名长度 add by panmd
	function checkLength(obj){
		debugger;
		var name = obj.value;
		if(150<name.length*3){
			alertMsg.info("姓名长度不能超过50!");
			obj.value='';
			return false;
		}
		// 姓名规则校验
		/** RM:157485针对证件类型为“1-护照”或“e-外国人永久居留身份证”的客户，姓名在原有校验基础上，增加允许“,”（半角）和“.”，且“,”、 “.”不能出现在首位或末尾； **/
		var custCertiType = $(obj, navTab.getCurrentPanel()).closest("tr").find("[name='csPremArapVO.customerCertiType']").find("option:selected").val();
		if ("1" == custCertiType || "e" == custCertiType) {
			if(!check_nameForFore(obj,null)){
				return false;
			}
		} else {
			if(!check_name(obj,null)){
				return false;
			}
		}
		var cusName = obj.value;
		if(cusName!=null && cusName!=""){
			var cusNameTrim = cusName.replace(/[ ]/g,"");//中部去空
			//需要包含•字符,汉字校验
			var patternCHN = /[\u4E00-\u9FA5\uF900-\uFA2D•]/;
			//英文、数字校验
			var patternEN = /^[0-9a-zA_Z]+$/;
			//纯数字校验
			var patternNO = /^\d*$/;
			
			if(patternCHN.test(cusNameTrim)){
				if(cusNameTrim.length < 2){
					obj.val("");
					alertMsg.warn("客户姓名不能少于四个字符");
					return false;
				}
			}else if(patternNO.test(cusNameTrim)){//纯数字
					obj.val("");
					alertMsg.warn("客户姓名不能少于四个字符");
					return false;
			}else if(patternEN.test(cusNameTrim)){//英文
				if(cusNameTrim.length < 4){
					obj.val("");
					alertMsg.warn("客户姓名不能少于四个字符");
					return false;
				}
			}else{//其他
				if(cusNameTrim.length < 2){
					obj.val("");
					alertMsg.warn("客户姓名不能少于四个字符");
					return false;
				}
			}
		}
	}
	// 姓名规则校验
	/** RM:157485针对证件类型为“1-护照”或“e-外国人永久居留身份证”的客户，姓名在原有校验基础上，增加允许“,”（半角）和“.”，且“,”、 “.”不能出现在首位或末尾； **/
	function checkMoneyName(obj) {
		debugger;
		var getMoneyName = $(obj, navTab.getCurrentPanel()).closest("tr").find("#getMoneyName").val();
		if (typeof(getMoneyName) == 'undefined' || getMoneyName == null || getMoneyName == "") {
			return true;
		}
		
		var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）;—|{}【】‘；：”“'。，、？]");
		var custCertiType = obj.value;
		if ("1" == custCertiType || "e" == custCertiType) {
			pattern = new RegExp("[`~!@#$^&=|':;'?~！@#￥……&;—|【】‘；：”“'。，、？•+%\"]");
		} else {
			pattern = new RegExp("[`~!@#$^&=|':;',.?~！@#￥……&;—|【】‘；：”“'。，、？•+%\"]");
		}
		for (var i = 0 ; i < getMoneyName.length; i++) {
			var a = getMoneyName.charAt(i);
			if (pattern.test(a)) {
				checkName = false;
				$(obj, navTab.getCurrentPanel()).closest("tr").find("#getMoneyName").val("");
				alertMsg.error("录入姓名含有特殊字符，请重新录入！");
				return false;
			}
		}
		if (getMoneyName.length > 0) {
			if(','==getMoneyName.substring(0,1) || ','==getMoneyName.substring(getMoneyName.length-1,name.length)){
				alertMsg.error("间隔符“,”不能放在开头及结尾，且不少于2个字符中!");
				$(obj, navTab.getCurrentPanel()).closest("tr").find("#getMoneyName").val("");
				return false;
			}
			if('.'==getMoneyName.substring(0,1) || '.'==getMoneyName.substring(getMoneyName.length-1,name.length)){
				alertMsg.error("间隔符“.”不能放在开头及结尾，且不少于2个字符中!");
				$(obj, navTab.getCurrentPanel()).closest("tr").find("#getMoneyName").val("");
				return false;
			}
		}
		checkThirdPerson(obj);
	}

	function timestampToTime(timestamp) {
        var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1):date.getMonth()+1) + '-';
        var D = (date.getDate()< 10 ? '0'+date.getDate():date.getDate());
        return Y+M+D;
    }
	/*对公标识  */
	function identificationCor(obj) {
		var identification = $(obj).parents("#holloshit").find("select[name='publicIdentification']").val();
		var parentTrs=$(obj).parents("#holloshit"); 
		if(identification=='1'){
			$("#CspremArapFormId").find("#getMoneyName",navTab.getCurrentPanel()).val('');
			$("#CspremArapFormId").find("[name='csPremArapVO.customerCertiType']",navTab.getCurrentPanel()).val("");
			$("#CspremArapFormId").find("#customerCertiCode",navTab.getCurrentPanel()).val('');
			$("#CspremArapFormId").find("#custCertStarDate",navTab.getCurrentPanel()).val('');
			$("#CspremArapFormId").find("#custCertEndDate",navTab.getCurrentPanel()).val('');
			$("#CspremArapFormId").find("[name='csPremArapVO.customerGender']",navTab.getCurrentPanel()).val("");
			$("#CspremArapFormId").find("#customerBirthday",navTab.getCurrentPanel()).val('');
			$("#CspremArapFormId").find("#taxResidentType",navTab.getCurrentPanel()).val('');
			$("#CspremArapFormId").find("#taxCountry",navTab.getCurrentPanel()).val('');
	    }
	}
	function payChanBank(targe){
		var payeeType = $(targe).parents("#holloshit").find("select[name='csPremArapVO.payeeType']").val();
		var parentDiv=$(targe, navTab.getCurrentPanel()).parents("#policyPayModeInfoDiv",navTab.getCurrentPanel()).get(0);
		//106_28657 处理补退费信息中当切换交费人类型时，对应的补退费信息、账户信息也做变更
		if(payeeType == '2'){
			var payModeBanks=$("#payModeBankQY select[name='csPremArapVO.payMode']",parentDiv);
		}else{
			var payModeBanks=$("select[name='csPremArapVO.payMode']",parentDiv);
		}
		var check=false;
		for(var i=0;i<payModeBanks.size();i++)
		{
			var payModeBank=payModeBanks.get(i).value;
			
			var arapAndPrem = $(parentDiv, navTab.getCurrentPanel()).find("#arapAndPremss").val();
			if(payeeType == '2'){
				var paymode = $("#payModeBankQY select[name='csPremArapVO.payMode']",parentDiv).val();	
			}else{
				var paymode = $("#payModeBankZRR select[name='csPremArapVO.payMode']",parentDiv).val();	
			}
			if(arapAndPrem == '应付'){
				$("th[colName='publicIdentification']",navTab.getCurrentPanel()).removeAttr("style");
				$("td[colName='publicIdentificationCon']",navTab.getCurrentPanel()).removeAttr("style");
				if(paymode == '34' || paymode == '22'){
					$("th[colName='openingBankName']",navTab.getCurrentPanel()).removeAttr("style");
					$("td[colName='openingBankNameCon']",navTab.getCurrentPanel()).removeAttr("style");
					$("#policyPremStrInput",navTab.getCurrentPanel()).removeAttr("style");
					$("#policyPremStr",navTab.getCurrentPanel()).attr("style","display: none");
				}
				
				if(paymode == '31' || paymode == '32' || paymode == '18' || paymode == '10'){
					$("th[colName='openingBankName']",navTab.getCurrentPanel()).attr("style","display: none");
					$("td[colName='openingBankNameCon']",navTab.getCurrentPanel()).attr("style","display: none");
					$("#policyPremStr",navTab.getCurrentPanel()).removeAttr("style");
					$("#policyPremStrInput",navTab.getCurrentPanel()).attr("style","display: none");
				}
			}else{//应收
				$("th[colName='publicIdentification']",navTab.getCurrentPanel()).attr("style","display: none");
				$("td[colName='publicIdentificationCon']",navTab.getCurrentPanel()).attr("style","display: none");
				if(paymode == '34' || paymode == '22'){
					$("th[colName='openingBankName']",navTab.getCurrentPanel()).removeAttr("style");
					$("td[colName='openingBankNameCon']",navTab.getCurrentPanel()).removeAttr("style");
					$("#policyPremStrInput",navTab.getCurrentPanel()).removeAttr("style");
					$("#policyPremStr",navTab.getCurrentPanel()).attr("style","display: none");
				}
				
				if(paymode == '31' || paymode == '32' || paymode == '18' || paymode == '10'){
					$("th[colName='openingBankName']",navTab.getCurrentPanel()).attr("style","display: none");
					$("td[colName='openingBankNameCon']",navTab.getCurrentPanel()).attr("style","display: none");
					$("#policyPremStr",navTab.getCurrentPanel()).removeAttr("style");
					$("#policyPremStrInput",navTab.getCurrentPanel()).attr("style","display: none");
				}
				
			}
			if(payModeBank==30||payModeBank==32||payModeBank==31 || payModeBank==34){
				check=true;			
			}
		}
		if(check){
			$("#bankAccountInfoDiv",parentDiv).show();
		}else
		{
			$("#bankAccountInfoDiv",parentDiv).hide();
		}
		
		if(payeeType == '2'){
			var str = $("#payModeBankQY select[name='csPremArapVO.payMode']").val();
		}else{
			var str = $("#payModeBankZRR select[name='csPremArapVO.payMode']").val();
		}
		//alert(str)
		$("#payMode").val(str);
		checkXQ(targe,parentDiv);
	}
	
	 //续期处理判断
	function checkXQ(targe,parentDiv){
		 debugger;
		var payeeType = $("select[name='csPremArapVO.payeeType']").val();
		var parentTr=$(targe, navTab.getCurrentPanel()).parents("tr").get(0);
		var serviceCode = $(parentTr, navTab.getCurrentPanel()).find("#hiddenServiceCode").val();
		if(serviceCode=="XQ"){
			var payMode =$("#oldPayMode",navTab.getCurrentPanel()).val();
			var payModeBank=$("select[name='csPremArapVO.payMode']",parentTr).val();
			if(payMode==30||payMode==32||payMode==31||payMode==34||payMode==33){
				if(payModeBank!=30&&payModeBank!=32&&payModeBank!=31&&payModeBank!=34&&payModeBank!=33){
				//当前收付费方式选择时仅可选择4-银行划款，则户名为锁定为投保人姓名，不可修改
					alertMsg.info("被回退的续期收费是银行划款方式收费，不可选择非银行划款的收付费方式!");
					//当选择其他方式时改为31
					/* $("select[name='csPremArapVO.payMode']", navTab.getCurrentPanel()).val("31");
					$("a[name='csPremArapVO.payMode']", navTab.getCurrentPanel()).val("31");
					$("#bankAccountInfoSelectBlock",parentTr).show();
					$("select[name='csPremArapVO.payMode']", navTab.getCurrentPanel()).attr("disabled","disabled"); */
					return;
				}
			}else if(payModeBank==18||payModeBank==20){
				alertMsg.info("该保全项目暂不支持此类收付费方式!");
				return;
			}
			
		}
		if(payeeType == '2'){
			var paymode = $("#payModeBankQY select[name='csPremArapVO.payMode']",parentTr).val();
		}else{
			var paymode = $("#payModeBankZRR select[name='csPremArapVO.payMode']",parentTr).val();
		}
		
		if(paymode == '31'||paymode == '32'||paymode == '34'){
			$("#bankAccountInfoSelectBlock",parentTr).show();
		}else{
			$("#bankAccountInfoSelectBlock",parentTr).hide();
			$("#policyPremStr",parentTr).val("");
		}
	}

	function searchImage(){
		var parentDiv=$(targe).parents("#policyPayModeInfoDiv").get(0);
		var policypayMoel =$("#policypayMoel",parentDiv).text();
		var acceptCode = $("#acceptCode",navTab.getCurrentPanel()).val();
	    var _changeId = $("#changeId",navTab.getCurrentPanel()).val();
	    var applyCode = $("#applyCode",navTab.getCurrentPanel()).val();
		if(policypayMoel==""){
			alertMsg.info("银行卡无对应的影像!");
		}else{
			$.ajax({
				type : 'POST',
				url : '${ctx}/cs/csAccept/checkFileIsBankCode_PA_checkAcceptAction.action?changeId='+_changeId,
				dataType : 'json',
				async : false,
				success : function(rep){
					if(rep.statusCode=='300'){
						alertMsg.info("银行卡无对应的影像!");
						return false;
					}else{
					    $.ajax({
					        type:'POST',
					        url:'${ctx}/cs/csAccept/queryDataUpdate_PA_acceptOtherAction.action?changeId='+_changeId+'&applyCode='+applyCode+'&acceptCode='+acceptCode+'&imageFlag=4',
					        success:function(data)
					        {   
					            window.open(data,'','width='+(window.screen.availWidth-10)+',height='+(window.screen.availHeight-30)+',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');    
					        },
					        error:DWZ.ajaxError
					    });
					}
				}
			});
		}
	}

	
	function checkNameLength(){
		var getMoneyName = $("#getMoneyName",parentTr).val();
		
		var moneyName = getMoneyName.replace(/[ ]/g,"");//中部去空
		
		if(getMoneyName==null || ''==getMoneyName){
			alertMsg.error("姓名不能为空，请重新录入。");
			return ;
		}
		var regex = /^[0-9]*$/;
		if(regex.test(getMoneyName)){
            alertMsg.error("名字不能全为数字,请重新输入。");
            return ;
        }
		
		//需要包含•字符,汉字校验
		var patternCHN = /[\u4E00-\u9FA5\uF900-\uFA2D•]/;
		//英文、数字校验
		var patternEN = /^[0-9a-zA_Z]+$/;
		
		
		if(patternCHN.test(moneyName)){
			if(moneyName.length > 25){
				alertMsg.error("领款人姓名长度不能超过50!");
				return ;
			}
		}else if(patternEN.test(moneyName)){//英文
			if(moneyName.length > 50){
				alertMsg.error("领款人姓名长度不能超过50!");
				return ;
			}
		}else{//其他
			if(moneyName.length > 25){
				alertMsg.error("领款人姓名长度不能超过50!");
				return ;
			}
		}
		
		if(patternCHN.test(moneyName)){
			if(moneyName.length < 1){
				alertMsg.error("领款人姓名长度不能小于两个字符!");
				return ;
			}
		}else if(patternEN.test(moneyName)){//英文
			if(moneyName.length < 2){
				alertMsg.error("领款人姓名长度不能小于两个字符!");
				return ;
			}
		}else{//其他
			if(moneyName.length < 1){
				alertMsg.error("领款人姓名长度不能小于两个字符!");
				return ;
			}
		}
	}
	
	 /* 保存收付费信息 */
	function saveCsPolicyPayMode(targe){
		debugger;
		 
		var parentDiv=$(targe).parents("#policyPayModeInfoDiv",navTab.getCurrentPanel()).get(0);		
		var parentTrs=$("#policypayMoel tr",parentDiv);
		var payMode = $("select[name='csPremArapVO.payMode']").val();
		var payeeType = $("select[name='csPremArapVO.payeeType']").val();
		var serviceCode=$("#hiddenServiceCode").val();
		var identification = $("select[name='publicIdentification']").val();
		
		
		//var _policyCode = $("#policyCode",parentTr).val();		
		var parentTrs=$("#policypayMoel tr",parentDiv);
		
		var _policyCode="";
		
		// 是否有养老金账户不符合要求
		var yljzhFalseFlag ="";
		var $trs=$("#policyTableId",navTab.getCurrentPanel()).find("tbody tr");
	    $trs.each(function(){
	    	var policyCode= $(this).find("#policyCode").text();
	    	var bpCodeylaccountflags=$(this).find("#ylaccountflags").val();
	    	var bpCodeylaccount=$(this).find("#ylaccount").val();
	    	if(bpCodeylaccountflags=='1' && ( bpCodeylaccount=='' || bpCodeylaccount== null || bpCodeylaccount==' ')){
	    		yljzhFalseFlag=policyCode;
	    	}
		});
	    
	    if(yljzhFalseFlag!=""){
	    	alertMsg.warn("保单"+yljzhFalseFlag+"不存在中银保信个人养老金账户信息，请先办理账户维护。");
			return false;
	    }
	    
		//非阻断校验是否变更
		var hiddenpaymode = $("#payMode").val();
		if(serviceCode=="CT"){
			if(hiddenpaymode!=payMode){
				alertMsg.info("退保的补退费形式与承保的不一致!");
			}						
		}			
		//校验必填项
		var checkedFlag=false;
		for(var i=0;i<parentTrs.size();i++){ 
			if(parentTrs.size==i)
			checkedFlag=true;
			parentTr=parentTrs.get(i);
			debugger;
			var payeeType =  $("#payeeType",parentTr).val();
			if(payeeType == '2'){
				 // 获取ID为getMoneyNameQY的元素  
			    var tdElement = document.getElementById("getMoneyNameQY");  
			    // 在该元素内部获取ID为getMoneyName的input元素  
			    var inputElement = tdElement.querySelector("#getMoneyName");  
			    // 获取input元素的值  
			    var inputGetMoneyNameValue = inputElement.value;  
				if(inputGetMoneyNameValue==null || inputGetMoneyNameValue==''){
					alertMsg.error("请录入企业信息。");
					return false;
				}else{
					getAccoName(parentDiv);
				}
				return true;
			}
			var qy_customerId= $("#qy_customerId",parentTr).html();
			var businessCode=$("#businessCode",parentTr).html();
			var customerCertType = $("#customerCertiType"+qy_customerId+businessCode+" option:selected",parentTr).text();
			var arapAndPrem = $("#arapAndPrem",parentTr).text();
			var medicalNo = $("#medicalNo",parentTr).val();
			var accountName = $("#policyPremStr",parentTr).val();
			var _amountMany = $('#amountMany',parentTr).text();
			var accoName =  $("#policyPremStr",parentTr).val();
			var accoNames = '';
			var payModeBank=$("#payModeBank"+qy_customerId+businessCode,parentTr).val();
			var customerGender = $("#customerGender"+qy_customerId+businessCode+" option:selected",parentTr).text();
			var customerBirthday =  $("#customerBirthday",parentTr).val();
			var _redonlyFlag =  $("#redonlyFlag",parentTr).val();
			var cusCountryCode =  $("#cusCountryCode",parentTr).text();
			var getMoneyName = $("#getMoneyName",parentTr).val();
			
			var moneyName = getMoneyName.replace(/[ ]/g,"");//中部去空
			
			// 账户信息
			var bankstr = document.getElementById("policyPremStrInput").value;
			//开户行名称
			var newCipDistrictBankCode =$("#inputOrg1",navTab.getCurrentPanel()).val();
			
			/* 127287 关于优化个险新核心柜台收付费录入无法选择企业方账号及名称-保全录入*/
			if(payModeBank=='34'){
				if(bankstr==null || bankstr=='' || getMoneyName==null || getMoneyName=='' || newCipDistrictBankCode==null || newCipDistrictBankCode==''){
					alertMsg.error("请录入银行账户名、银行编码、银行账号、开户行名称。");
					return false;
				}
			}
			
			if(payModeBank=="30"){
				alertMsg.warn("不能选择银行转账，请选择银行转账（非制返盘）或者银行转账（制返盘）");
				return null;
			}
			
			//只有银行转账才会进行判断账户信息 
			if(payModeBank =="31" || payModeBank == "32"|| payModeBank == "34"){
				if (accoName==null || accoName=="") {
					alertMsg.warn("请先保存银行账户信息！");
					return false;
				}
			}
			//原来的获取customerCertType方法因为拼接id问题存在取不到值问题，在这里进行校验，如果两个方法都拿不到值就是真的没值
			if(customerCertType == null || customerCertType == ""){
				customerCertType = $("#customerCertiType"+businessCode).find("option:selected").val();
			}
			
			// 姓名规则校验 178031 1）客户姓名不能全为数字；
            //             2）客户姓名不能小于2个字符（1个字符=0.5个汉字、1个数字、1个字母），不得大于50个字符（25个汉字）；
            //             3）不能为空；
			
			if(getMoneyName==null || ''==getMoneyName){
				alertMsg.error("姓名不能为空，请重新录入。");
				return ;
			}
			var regex = /^[0-9]*$/;
			if(regex.test(getMoneyName)){
	            alertMsg.error("名字不能全为数字,请重新输入。");
	            return ;
	        }
			
			//需要包含•字符,汉字校验
			var patternCHN = /[\u4E00-\u9FA5\uF900-\uFA2D•]/;
			//英文、数字校验
			var patternEN = /^[0-9a-zA_Z]+$/;
			
			
			if(patternCHN.test(moneyName)){
				if(moneyName.length > 25){
					alertMsg.error("领款人姓名长度不能超过50!");
					return ;
				}
			}else if(patternEN.test(moneyName)){//英文
				if(moneyName.length > 50){
					alertMsg.error("领款人姓名长度不能超过50!");
					return ;
				}
			}else{//其他
				if(moneyName.length > 25){
					alertMsg.error("领款人姓名长度不能超过50!");
					return ;
				}
			}
			
			if(patternCHN.test(moneyName)){
				if(moneyName.length < 1){
					alertMsg.error("领款人姓名长度不能小于两个字符!");
					return ;
				}
			}else if(patternEN.test(moneyName)){//英文
				if(moneyName.length < 2){
					alertMsg.error("领款人姓名长度不能小于两个字符!");
					return ;
				}
			}else{//其他
				if(moneyName.length < 1){
					alertMsg.error("领款人姓名长度不能小于两个字符!");
					return ;
				}
			}
						
			if(identification == '1'){
			}else{
			if(customerCertType==null || ''==customerCertType){
				alertMsg.warn("请选择证件类型");
				return false;
			}
			if(arapAndPrem=='应付'){
				if(getMoneyName==null || getMoneyName.length == 0){
					alertMsg.warn("请填写领款人");
					return false;
				}
			}
			if(arapAndPrem=='应收'){
				if(getMoneyName==null || ''==getMoneyName){
					alertMsg.info("姓名不能为空，请重新录入。");
					return false;
				}
			}
			
			//按照需求校验证件号
			var customerCertiType = $("#customerCertiType"+qy_customerId+businessCode,parentTr).val();
			var customerCertiCode = $("#customerCertiCode",parentTr).val();
			var custCertStarDate = $("#custCertStarDate",parentTr).val();
			var custCertEndDate = $("#custCertEndDate",parentTr).val();
			var payStakeReasion = $("#payStakeReasion",parentTr).val();
			var changQiFlag = $("#changQiFlag",navTab.getCurrentPanel()).val();
			
			//同理，此处因为id拼接问题，需要判空，进行获取值
			if(customerCertiType==null || customerCertiType==""){
				customerCertiType = $("#customerCertiType"+businessCode).find("option:selected").val();

			}
			
			//106_28659:当缴费人类型为1-自然人缴费时才会去校验证件有效期起止日期不能为空等操作
			if(payeeType == '1'){
				
			if((serviceCode == "CT" || serviceCode == "XT" || serviceCode == "IT") && payStakeReasion == "1"){
					
				if(changQiFlag != "1" && (custCertStarDate == "" || custCertEndDate == "")){
					alertMsg.warn("证件有效起止日期不能为空，请重新录入。");
					return false;
				}
			}
			if(changQiFlag != "1" && (custCertStarDate != "" && custCertEndDate == "")){
				alertMsg.warn("证件有效止期不能为空，请重新录入。");
				return false;
			}
			if(!checkEndTime(custCertStarDate,custCertEndDate)){
	            alertMsg.warn('证件有效期起期不得晚于证件有效期止期，请重新录入。');
	            return;
	        }
			if(changQiFlag == "1" && custCertStarDate == ""){
				alertMsg.warn("证件有效起期不能为空，请重新录入。");
				return false;
			}
			
			}
			
			if(customerCertiCode==null || ''==customerCertiCode){

				alertMsg.warn("请填写证件号码");
				return false;
			}
			if(customerCertiCode.length!=18 && customerCertiType=='h'){
				alertMsg.warn("证件号码录入有误，请重新录入。");
				return false;
			}
			if( customerCertiType == 'e'){
			 var flag = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？ ]");
			 if(customerCertiCode.length<10 || flag.test(customerCertiCode) ){
				alertMsg.warn("证件号码仅能包括字母、数字，且不能低于10位，请重新录入。");
				return false;
			 }
			}
			if(customerCertiCode.length>50){
				alertMsg.warn("领款人证件号码长度不能超过50!");
				return false;
			}
			if(customerGender==null|| ''==customerGender){
				alertMsg.info("请填写付款人性别");
				return false;
			}
			if(customerBirthday==null|| ''==customerBirthday){
				alertMsg.info("请填写付款人出生日期");
				return false;
			}
			
			
			
			
			
			/* if((!idCardNoUtil.checkBirthDayCodeNew(customerBirthday))){
				alertMsg.warn("请填写正确的付款人出生日期，正确格式为YYYY-MM-DD");
				return false;
			} */
			var certTypeObj = $("#customerCertiType"+qy_customerId+businessCode,parentTr);
			var certCodeObi = $("#customerCertiCode",parentTr);
			var birthdayObj =  $("#customerBirthday",parentTr);
			var genderObj = $("#customerGender"+qy_customerId+businessCode+" option:selected",parentTr);
			var countryCodeObj =  $("#cusCountryCode",parentTr);
			//证件类型(参数:1证件类型对象2证件号码对象3:生日对象4:国籍对象5:有效止期对象6:是否长期7:性别)
			debugger;
			
			//如果录入的“户名/收款人姓名”与申请下任意一个参与变更保单的投保人或被保人姓名一致，“领款人证件类型”、“领款人证件号码”的录入规则校验同重要资料变更
			if(_redonlyFlag == '1'){
				if(!checkCertiType(certTypeObj, certCodeObi,birthdayObj, countryCodeObj, null,null,genderObj,"CM")){
					return false;
				}
			}
			
			// 判断身份证教研修改dongxf_wb start
			 if(customerCertiType == '0'){
				if(customerCertiCode.length!=18){
					alertMsg.warn("证件号码必须为18位身份证号码，请重新录入。");
					return false;
				}
				
				if((!idCardNoUtil.check18IdCardNo(customerCertiCode))){
					alertMsg.warn("证件号码输入有误，不符合一般编码规则，请重新录入。");
					return false;
				}
				
				//校验生日-性别
				var tempStr="";
				var sexno = "";
				var sex = "";
				if(customerCertiCode.length==18){
					tempStr=customerCertiCode.substring(6,14);
					tempStr=tempStr.substring(0,4)+"-"+tempStr.substring(4,6)+"-"+tempStr.substring(6);
				    sexno=customerCertiCode.substring(16,17)
					
				}
				var tempid=sexno%2;
			    if(tempid==0){
			        sex='女'
			    }else{
			        sex='男'
			    }
				if(tempStr+sex!=customerBirthday+customerGender){
					alertMsg.warn("证件号码与出生日期/性别不匹配，请重新录入。");
					return false;
					
				}
				
			// modify by dongxf_wb end 
			}else if(customerCertiType == '1'){//护照
				if(!CV_checkPassport(customerCertiCode)){
					alertMsg.warn("证件号码应不少于3个字符，且只能含有字母、数字，请重新录入。");
					return false;
				}
			}else if(customerCertiType == '2'){//军官证
				if(!CV_checkOfficerNo(customerCertiCode)){
					alertMsg.warn("证件号码应为10（含）-18（含）个字符，且只能含有汉字、数字、字母、-，请重新录入");
					return false;
				}
			}else if(customerCertiType == '4'){//出生证明
				/* if(GetAge(customerBirthday.replace(/-/g,"/"))>=16){
					alertMsg.warn("客户年龄大于等于16周岁，不允许使用出生证明。");
					return false;
				} */
				var myDate = new Date();
				 var month = myDate.getMonth() + 1;
				 var day = myDate.getDate();
				 var age = myDate.getFullYear() - customerCertiCode.substring(6, 10) - 1;
				 if (customerCertiCode.substring(10, 12) < month || customerCertiCode.substring(10, 12) == month && customerCertiCode.substring(12, 14) <= day) {
				  age++;
				 }
				 //客户年龄大于等于16周岁，不允许使用出生证明。

				 if(age>3){
						alertMsg.warn("客户年龄大于3周岁，不允许使用出生证明。");
						return false;
					}
				 
				 if(customerCertiCode.length != 10){
						alertMsg.error("出生证明证件号码应为10位。");
						return;
					}else{   
						var check = /^[A-Z]{1}.*/.test(customerCertiCode);
						if (!check){
							alertMsg.error("出生证明证件号码首位应为英文字母（大写）。");
							return;
						}else{  
							var check = /.*(\d)(?!\1{8})[\d]{8}$/.test(customerCertiCode);
							if(!check){
								alertMsg.error("数字必须为9位，且9位数字不能完全相同。");
								return;
							}
						}
					}
				
			}else if(customerCertiType == '5'){//户口簿
				if(customerCertiCode.length!=18){
					alertMsg.warn("证件号码必须为18位身份证号码，请重新录入。");
					return false;
				}
				
				if(!idCardNoUtil.check18IdCardNo(customerCertiCode)){
					alertMsg.warn("证件号码输入有误，不符合一般编码规则，请重新录入。");
					return false;
				}
				//校验生日-性别
				var tempStr="";
				var sexno = "";
				var sex = "";
				if(customerCertiCode.length==18){
					tempStr=customerCertiCode.substring(6,14);
					tempStr=tempStr.substring(0,4)+"-"+tempStr.substring(4,6)+"-"+tempStr.substring(6);
				    sexno=customerCertiCode.substring(16,17)
					
				}
				var tempid=sexno%2;
			    if(tempid==0){
			        sex='女'
			    }else{
			        sex='男'
			    }
				if(tempStr+sex!=customerBirthday+customerGender){
					alertMsg.warn("证件号码与出生日期/性别不匹配，请重新录入。");
					return false;
					
				}
				if(GetAge(tempStr.replace(/-/g,"/"))>=16){
					alertMsg.warn("客户年龄大于等于16周岁，不允许使用户口簿。");
					return false;
				}
				
				
			}else if(customerCertiType == 'b'){
				if(!CV_checkGATPassport(customerCertiCode)){
					alertMsg.warn("证件号码不少于8个字符，请重新录入。");
					return false;
				}
			}else if(customerCertiType == 'e'){
		 		var result = CV_checkForeignIdCrad(customerCertiCode,customerBirthday);
		 		if(result!=true){
		 			alertMsg.warn(result);
		 			return false;
		 		}
			}else{
				if(customerCertiCode.length>30){
					alertMsg.warn("证件号码长度不能超过30个字符！");
					return false;
				}
			} 
			if(accoName!=null){
				accoNames = accoName.split(',');	
			}
			if(300<getMoneyName.length*3){
				alertMsg.info("付款人姓名长度不能超过100");
				return false;
			}
			var customerName = $("#customerName",parentTr).val();
			//var payModeBank=$("#payModeBank",parentTr).val();
			if(payModeBank==30){
				if(accountName ==null || '' == accountName){
					alertMsg.info("请选择银行账号或新增银行账户");
					return false;
				}
			if(arapAndPrem=='应收'){
				if(getMoneyName==null || ''==getMoneyName){
					alertMsg.info("请填写付款人");
					return false;
				}
			}
			if(300<getMoneyName.length*3){
				alertMsg.info("付款人姓名长度不能超过100");
				return false;
			}
			
			
			
			if(parseInt(_amountMany) > 0 ||parseInt(_amountMany) < 0 ||parseInt(_amountMany) == 0){
				if(trim(accoNames[3]) !=trim(customerName) ){
					alertMsg.confirm("开户名称和客户名称不一致,是否继续?", {
						okCall : function(){
						},
						cancleCall : function() {
							return false;
						}
					})
				}
			}
			}
		  }
		} 	
		//续期回退时添加校验
		if(!checkXQAccName(parentDiv)){
			alertMsg.info("续期回退银行户名为只能为投保人请修改。");
			return;
		};
		if(!checkLNOrRLAccName(parentDiv)){
			alertMsg.info("客户收款账户户名非投保人姓名。");
			return;
		}
		
		var flag=false;
		var msg_policyCode=checkAccountRLAndLN(parentDiv);

			
		
		 if(!saveBankInfo(parentDiv)) //只有银行转账才会校验户名
		 
		{
			alertMsg.confirm("付费账户户名与申请资格人姓名不一致，请再次确认。若录入无误，请点击“确定”，若录入错误，请点击“取消”重新修改。", {
				okCall : function(){
					if(msg_policyCode!=null&&msg_policyCode!=''){
						alertMsg.confirm(msg_policyCode, {
							okCall : function(){
								checkPremAccount(parentDiv);//123963
							},
							cancleCall : function() {
							}
						});
					}else{
						checkPremAccount(parentDiv);//123963
					}
				},
				cancleCall : function() {
				}
			});
		}else if(msg_policyCode!=null && msg_policyCode!=''){//已经默认空串，此处没有空串判断 ，现在加上 bytanyh
			alertMsg.confirm(msg_policyCode, {
				okCall : function(){
					checkPremAccount(parentDiv);//123963
				},
				cancleCall : function() {
				}
			});
		}else{
			checkPremAccount(parentDiv);//123963
		}
 
      
 		
 		
 		
 		
	}
	 /**
    贷款/续贷，校验录入账户是否与首期和续期账户一致，非阻断提示
 */
	 function checkAccountRLAndLN(parentDiv){
		 var parentTrs=$("#policypayMoel tr",parentDiv);
		 var flag = true;
		 var masg="";
		 var isRLOrLN = false;//是否时贷款或者续贷保全项目
			if(parentTrs.length>0){
				for(var i = 0;i<parentTrs.length;i++){
					parentTr=parentTrs.get(i);
					var payModeBank=$("select[name='csPremArapVO.payMode']",parentTr).val();
					var serviceCode = $("#csServiceCode",parentTr).val();
					if(payModeBank =="31" || payModeBank == "32"|| payModeBank == "34"){
						
						var downPayAccount= $("#downPayAccount",parentTr).val();
						var payAccountUsed= $("#payAccountUsed",parentTr).val();
						if(serviceCode=='LN'||serviceCode=='RL'){
							isRLOrLN=true;
							var nextPayAccount= $("#nextPayAccount",parentTr).val();
							//只校验账号 取0
							downPayAccount = downPayAccount.split("/")[0];
							// 此处校验只是规则 :帐号不相同
							nextPayAccount = nextPayAccount.split("/")[0];
							var bankCode= $("#policyPremStr option:selected",parentTr).html();
							var policyCode= $("#policyCode",parentTr).html();
							var qy_customerName= $("#getMoneyName",parentTr).val();
							bankCode=bankCode.split("/")[0].trim();
							//bankCode=downPayAccount;
	 						//alert(nextPayAccount + "==="+downPayAccount+"--"+bankCode);
	 						//只要成功缴纳过首期或续期保费，则应视为原账户对待；
	 						//如果未成功缴纳过保费，即使曾经或目前作为续期交费账号，也不视为原账户。
	 						if(downPayAccount =="" || typeof downPayAccount == "undefined" ||  downPayAccount== null){
	 							flag=false;
	 						}else if( payAccountUsed.indexOf(bankCode) < 0){
	 							flag=false;
 							}else{
 								flag=true;
 							}
	 						/* if(nextPayAccount!=null){
	 							//alert("续期账号为："+nextPayAccount);
	 							if(bankCode!=nextPayAccount){
	 								flag=false;
	 							}
	 						} */
	 						//if(!flag){
	 							//alert("首期账号为："+downPayAccount);
	 							/* if(bankCode!=downPayAccount){
	 								flag=false;
	 							}else{
	 								flag=true;
	 							} */
	 						//}
	 						
	 						if(!flag){
	 							masg=masg+policyCode+",";
	 						}
	 						
							
						}else{
							// 此处校验只是规则 :帐号+户名 
							downPayAccount = downPayAccount.split("/")[0]+"/"+downPayAccount.split("/")[2];
							var bankCode= $("#policyPremStr option:selected",parentTr).html();
							var policyCode= $("#policyCode",parentTr).html();
							var qy_customerName= $("#getMoneyName",parentTr).val();
							bankCode=bankCode.split("/")[0].trim()+"/"+qy_customerName;
//	 						alert(downPayAccount + "  "+bankCode);
							if(bankCode!=downPayAccount){
								masg=masg+policyCode+",";
								//alert("policyCode="+policyCode);
								flag=false;
							}
						}
						
						
						
					}
				}
			}		
			//alert(masg.substring(0,masg.length-1));
			
			if(isRLOrLN&&!flag){
				return masg.substring(0,masg.length-1)+"客户收款账户不为原投保账户。";
			} else if (!flag){
				return masg.substring(0,masg.length-1)+"本次收付费账户≠首期保费交费账户，请再次确认。若录入无误，请点击“确定”，若录入错误，请点击“取消”重新修改。";
			}else{
				return masg;
			}		 
	 }
	
	 function checkAccount(parentDiv){
		 var parentTrs=$("#policypayMoel tr",parentDiv);
		 var flag = true;
		 var masg="";
			if(parentTrs.length>0){
				for(var i = 0;i<parentTrs.length;i++){
					parentTr=parentTrs.get(i);
					var payModeBank=$("select[name='csPremArapVO.payMode']",parentTr).val();
					if(payModeBank =="31" || payModeBank == "32"|| payModeBank == "34"){
						var downPayAccount= $("#downPayAccount",parentTr).val();
						// 此处校验只是规则 :帐号+户名 
						downPayAccount = downPayAccount.split("/")[0]+"/"+downPayAccount.split("/")[2];
						var bankCode= $("#policyPremStr option:selected",parentTr).html();
						var policyCode= $("#policyCode",parentTr).html();
						var qy_customerName= $("#getMoneyName",parentTr).val();
						bankCode=bankCode.split("/")[0].trim()+"/"+qy_customerName;
// 						alert(downPayAccount + "  "+bankCode);
						if(bankCode!=downPayAccount){
							masg=masg+policyCode+",";
							//alert("policyCode="+policyCode);
							flag=false;
						}
					}
				}
			}		
			//alert(masg.substring(0,masg.length-1));
			return masg.substring(0,masg.length-1);
		 
	 }
	 
	//校验续期回退时银行转账户名是否是投保人
	function checkXQAccName(parentDiv){
		var serviceCode = $("#hiddenServiceCode",parentDiv);
		if(serviceCode.length>0){
			for(var i = 0;i<serviceCode.length;i++){
				var scode = $(serviceCode[i]).val();
				if(scode=='XQ'){
					console.dir(123);
					var parent = $(serviceCode[i]).parents("tr");
					var payModeBank = parent.find("select[name='csPremArapVO.payMode'] option:selected").val();
					var preAccName = parent.find("#policyPremStr option:selected").val();
					var customerName = $("#qy_customerName",parentDiv).val();
					var accountName =$("#getMoneyName", navTab.getCurrentPanel()).val();
					if(payModeBank==30||payModeBank==32||payModeBank==31||payModeBank==34||payModeBank==33){
						if(preAccName.length>0&&accountName!=customerName){
							return false;
						}
					}
				}
			}
		}
		return true;
	}
	
	 //保全项目为RL/LN时，阻断提示客户收款账户户名非投保人姓名
	function checkLNOrRLAccName(parentDiv){
		var parentTrs=$("#policypayMoel tr",parentDiv);
		if(parentTrs.length>0){
			for(var i = 0;i<parentTrs.length;i++){
				parentTr=parentTrs.get(i);
				var qy_customerId= $("#qy_customerId",parentTr).html();
				var qy_customerName= $("#qy_customerName",parentTr).html();
				var getMoneyName = $("#getMoneyName",parentTr).val();
				var businessCode=$("#businessCode",parentTr).html();
				var payModeBank=$("#payModeBank"+qy_customerId+businessCode,parentTr).val();
				var serviceCode = $("#csServiceCode",parentTr).val();
				if(serviceCode=='LN'||serviceCode=='RL'){
					
					if(payModeBank =="31" || payModeBank == "32"|| payModeBank == "34"){
						var selectResult=$("#policyPremStr",parentTr).val();
						
				 		if(selectResult!="" && selectResult != "null"&&selectResult != null){
				 			
							var begin=selectResult.lastIndexOf(",");
				 			var objresult=selectResult.substr(begin+1).trim();
				 			var selectResult=$("#policyPremStr",parentTr).val();
				 			if(selectResult!="" && selectResult != "null"&&selectResult != null){
				 				//alert(objresult+"-"+qy_customerName);
					 			if(objresult!=qy_customerName)
					 			{
					 				return false;
					 			}
					 			//alert(objresult+"-"+getMoneyName);
					 			if(objresult!=getMoneyName)
					 			{
					 				return false;
					 			}
				 			}
						}
					}
				}
				
			}
		}		
		return true;
	}
	
	function saveBankInfo2(moneyOwener,parentTr,checkedFlag,parentDiv){
		//付费类业务银行账户户名与权益人不一致校验
		var bankUserName = moneyOwener;
		//var acceptId = $("#premArapAcceptId",parentTr).val();
		var qy_customerName= $("#qy_customerName",parentTr).val();
		if(bankUserName!=qy_customerName)
		{
			alertMsg.confirm("付费账户户名与权益人姓名不一致，请再次确认。若录入无误，请点击“确定”，若录入错误，请点击“取消”重新修改。", {
				okCall : function(){					
					checkPremAccount(parentDiv);//123963
				},
				cancleCall : function() {
				}
			});
		}else{
			checkPremAccount(parentDiv);//123963
		}
	}
	
	function saveBankInfo(parentDiv){
		var parentTrs=$("#policypayMoel tr",parentDiv);
		if(parentTrs.length>0){
			for(var i = 0;i<parentTrs.length;i++){
				parentTr=parentTrs.get(i);
				var qy_customerId= $("#qy_customerId",parentTr).html();
				var qy_customerName= $("#qy_customerName",parentTr).html();
				var getMoneyName = $("#getMoneyName",parentTr).val();
				var businessCode=$("#businessCode",parentTr).html();
				var payModeBank=$("#payModeBank"+qy_customerId+businessCode,parentTr).val();
				if(payModeBank =="31" || payModeBank == "32"|| payModeBank == "34"){
					var selectResult=$("#policyPremStr",parentTr).val();
			 		if(selectResult!="" && selectResult != "null"&&selectResult != null){
						var begin=selectResult.lastIndexOf(",");
			 			var objresult=selectResult.substr(begin+1).trim();
			 			var selectResult=$("#policyPremStr",parentTr).val();
			 			if(selectResult!="" && selectResult != "null"&&selectResult != null){
				 			if(objresult!=qy_customerName)
				 			{
				 				return false;
				 			}
				 			if(objresult!=getMoneyName)
				 			{
				 				return false;
				 			}
			 			}
					}
				}
			}
		}		
		return true;
	}
	
	var payToThirdPerson ="0";
	function isChecked(obj){
		if($(obj).is(":checked")){
			payToThirdPerson="1";
		}else{
			payToThirdPerson="0";
		}
	}
	//123963 start
	function checkPremAccount(parentDiv){
		debugger;
		var policyTableId=$("#policyTableId",parentDiv);
		var length = policyTableId.find("tr").length-1;
		var thisAcceptCode=null;
		var copyInfoArr = [ "serviceCode", "policyCode","amountMany","arapAndPrem","payMode","medicalNo","policyPremArapBank","openingBankName","publicIdentification",
		                    "getMoneyName","customerCertiType","customerCertiCode","payeeCertStarDateString","payeeCertEndDateString","payStakeReasion",
		                    "businessCode","changeId","acceptId","customerId","customerGender","customerBirthday","customerBirthday1","payToThirdPerson", 
		                    "qy_customerId","changQiFlag","isCorporate","cipDistrictBankCode","payeeType"];
		var json = new Array();
		var changQiFlag = $("#changQiFlag",navTab.getCurrentPanel()).val();
		json += "[";
		for (var i = 1; i <= length; i++){
			var serviceCode = policyTableId.find("tr:eq(" + i + ")").find("#hiddenServiceCode").val(); 
			var policyCode = policyTableId.find("tr:eq(" + i + ")").find("#policyCode").html(); 
			var amountMany = policyTableId.find("tr:eq(" + i + ")").find("#amountMany").html(); 
			var arapAndPrem = policyTableId.find("tr:eq(" + i + ")").find("#arapAndPrem").html(); 
			var payMode = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.payMode']").find('option:selected').val();
			var medicalNo = policyTableId.find("tr:eq(" + i + ")").find("#medicalNo").html();
			if(payMode == '34' || payMode == '22'){
				var policyPremStr = policyTableId.find("tr:eq(" + i + ")").find("#policyPremStrInput").val(); //需要拼接账户信息 ${accountId},${bankCode},${bankAccount},${accoName}
			}else{
				var policyPremStr = policyTableId.find("tr:eq(" + i + ")").find("#policyPremStr").find('option:selected').val(); 
			}
			var openingBankName = policyTableId.find("tr:eq(" + i + ")").find("td[colName='openingBankNameCon']").find('input').val(); 
			var publicIdentification = policyTableId.find("tr:eq(" + i + ")").find("td[colName='publicIdentificationCon']").find('option:selected').val(); 
			//alert(publicIdentification);
			var getMoneyName = policyTableId.find("tr:eq(" + i + ")").find("#getMoneyName").val(); 
			var customerCertType = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.customerCertiType']").find('option:selected').val(); 
			var customerCertiCode = policyTableId.find("tr:eq(" + i + ")").find("#customerCertiCode").val();
			var payeeCertStarDateString = policyTableId.find("tr:eq(" + i + ")").find("#custCertStarDate").val();
			var payeeCertEndDateString = policyTableId.find("tr:eq(" + i + ")").find("#custCertEndDate").val();
			var payStakeReasion = policyTableId.find("tr:eq(" + i + ")").find("#payStakeReasion").val();
			var businessCode = policyTableId.find("tr:eq(" + i + ")").find("#businessCode").html();
			var acceptId = policyTableId.find("tr:eq(" + i + ")").find("#acceptId").html();
			var customerGender = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.customerGender']").find('option:selected').val(); 
			var customerBirthday = policyTableId.find("tr:eq(" + i + ")").find("#customerBirthday").val(); 
			var qy_customerId = policyTableId.find("tr:eq(" + i + ")").find("#qy_customerId").text(); 
			var payeeType = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.payeeType']").find('option:selected').val();
			thisAcceptCode=businessCode;
			json += "{";
			json = json + "'" + copyInfoArr[0] + "'" + ":'" + serviceCode + "',";
			json = json + "'" + copyInfoArr[1] + "'" + ":'" + policyCode + "',";
			json = json + "'" + copyInfoArr[2] + "'" + ":'" + amountMany + "',";
			json = json + "'" + copyInfoArr[3] + "'" + ":'" + arapAndPrem + "',";
			json = json + "'" + copyInfoArr[4] + "'" + ":'" + payMode + "',";
			json = json + "'" + copyInfoArr[5] + "'" + ":'" + medicalNo + "',";
			json = json + "'" + copyInfoArr[6] + "'" + ":'" + policyPremStr + "',";
			json = json + "'" + copyInfoArr[7] + "'" + ":'" + openingBankName + "',";
			json = json + "'" + copyInfoArr[8] + "'" + ":'" + publicIdentification + "',";
			json = json + "'" + copyInfoArr[9] + "'" + ":'" + getMoneyName + "',";
			json = json + "'" + copyInfoArr[10] + "'" + ":'" + customerCertType + "',";
			json = json + "'" + copyInfoArr[11] + "'" + ":'" + customerCertiCode + "',";
			json = json + "'" + copyInfoArr[12] + "'" + ":'" + payeeCertStarDateString + "',"; 
			json = json + "'" + copyInfoArr[13] + "'" + ":'" + payeeCertEndDateString + "',";
			json = json + "'" + copyInfoArr[14] + "'" + ":'" + payStakeReasion + "',";
			json = json + "'" + copyInfoArr[15] + "'" + ":'" + businessCode + "',";
			json = json + "'" + copyInfoArr[16] + "'" + ":'" + $("#changeId",navTab.getCurrentPanel()).val() + "',";
			json = json + "'" + copyInfoArr[17] + "'" + ":'" + acceptId + "',";
			json = json + "'" + copyInfoArr[18] + "'" + ":'" + $("#qy_customerId",parentDiv).val() + "',";
			json = json + "'" + copyInfoArr[19] + "'" + ":'" + customerGender + "',";
			json = json + "'" + copyInfoArr[20] + "'" + ":'" + customerBirthday + "',";
			json = json + "'" + copyInfoArr[21] + "'" + ":'" + customerBirthday + "',";
			json = json + "'" + copyInfoArr[22] + "'" + ":'" + payToThirdPerson + "',";
			json = json + "'" + copyInfoArr[23] + "'" + ":'" + qy_customerId + "',";
			json = json + "'" + copyInfoArr[24] + "'" + ":'" + changQiFlag + "',";
			json = json + "'" + copyInfoArr[25] + "'" + ":'" + publicIdentification + "',";
			json = json + "'" + copyInfoArr[26] + "'" + ":'" + openingBankName + "',";
			json = json + "'" + copyInfoArr[27] + "'" + ":'" + payeeType + "',";
			json += "},";  
		}
		json = json.substring(0, json.length - 1);
		json += "]";
		$.ajax({
			contentType: "application/x-www-form-urlencoded; charset=utf-8",
			type : 'POST',
			url : "${ctx }"+"/cs/csEntry/checkPremAccount_PA_csEntryAction.action?jsonStr="+encodeURI(encodeURI(json)),
			success:function(data){
				var json = jQuery.parseJSON(data);	
				//alert(json);
				if(json.statusCode==200){
					getAccoName(parentDiv);
				}else if(json.statusCode==301){
					alertMsg.confirm(json.message, {
						okCall : function(){				
							getAccoName(parentDiv);
						},
						cancleCall : function() {
						}
					});
				}else{
					alertMsg.error(json.message);
				}
			}
		
		});
		
	}
	//123963 end
	 //**************************************************************
	   function getAccoName(parentDiv){
		 debugger;
		var policyTableId=$("#policyTableId",parentDiv);
		var length = policyTableId.find("tr").length-1;
		var thisAcceptCode=null;
		var copyInfoArr = [ "serviceCode", "policyCode","amountMany","arapAndPrem","payMode","medicalNo","policyPremArapBank","openingBankName","publicIdentification",
		                    "getMoneyName","customerCertiType","customerCertiCode","payeeCertStarDateString","payeeCertEndDateString","payStakeReasion",
		                    "businessCode","changeId","acceptId","customerId","customerGender","customerBirthday","customerBirthday1","payToThirdPerson",
		                    "qy_customerId","changQiFlag","isCorporate","cipDistrictBankCode","payeeType"];
		var json = new Array();
		var changQiFlag = $("#changQiFlag",navTab.getCurrentPanel()).val();
		
		json += "[";
		for (var i = 1; i <= length; i++){
			//106_28659 添加获得交费人类型字段中的值
			var payeeType = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.payeeType']").find('option:selected').val();
			var serviceCode = policyTableId.find("tr:eq(" + i + ")").find("input[id='hiddenServiceCode']").val(); 
			var policyCode = policyTableId.find("tr:eq(" + i + ")").find("#policyCode").html();
			
			var amountMany = policyTableId.find("tr:eq(" + i + ")").find("#amountMany").html(); 
			var arapAndPrem = policyTableId.find("tr:eq(" + i + ")").find("#arapAndPrem").html(); 
			//106_28789 判断当为企业交费时，取值应该取企业交费相关的
			if(payeeType == '2'){
				var payMode = policyTableId.find("tr:eq(" + i + ")").find("#payModeBankQY select[name='csPremArapVO.payMode']").find('option:selected').val();
			}else{
				var payMode = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.payMode']").find('option:selected').val();
			}
			var medicalNo = policyTableId.find("tr:eq(" + i + ")").find("#medicalNo").html();
			if(payMode == '34' || payMode == '22'){
				var policyPremStr = policyTableId.find("tr:eq(" + i + ")").find("#policyPremStrInput").val(); //需要拼接账户信息 ${accountId},${bankCode},${bankAccount},${accoName}
			}else{
				var policyPremStr = policyTableId.find("tr:eq(" + i + ")").find("#policyPremStr").find('option:selected').val(); 
			}
			var openingBankName = policyTableId.find("tr:eq(" + i + ")").find("td[colName='openingBankNameCon']").find('input').val();
			var publicIdentification = policyTableId.find("tr:eq(" + i + ")").find("td[colName='publicIdentificationCon']").find('option:selected').val(); 
			//106_28789 添加判断交费人类型，判断交费人类型是企业缴费还是自然人缴费，企业缴费的话需要拿getMoneyName下的户名
			var ylaccountflags = $("#ylaccountflags").val();
			if(ylaccountflags != 1){
				if(payeeType=='2'){
					var getMoneyName = policyTableId.find("tr:eq(" + i + ")").find("#getMoneyNameQY #getMoneyName").val(); 
				}else{
					var getMoneyName = policyTableId.find("tr:eq(" + i + ")").find("#getMoneyNameZRR #getMoneyName").val(); 
				}
			}else{
				var getMoneyName = policyTableId.find("tr:eq(" + i + ")").find("#getMoneyName").val(); 
			}
			
			var customerCertType = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.customerCertiType']").find('option:selected').val(); 
			var customerCertiCode = policyTableId.find("tr:eq(" + i + ")").find("#customerCertiCode").val();
			/*
				106_28659 添加判断交费人类型，判断交费人类型是企业缴费还是自然人缴费，企业缴费的话不需要判断证件类型的起止时间
				此处加上判断并分别赋值是为了解决，如果在自然人缴费中选择证件起止日期，然后切换到企业缴费中点击保存导致的证件类型起止时间校验不通过阻断提示
			*/
			if(payeeType=='2'){
				var payeeCertStarDateString = "";
				var payeeCertEndDateString = "";
			}else{
				var payeeCertStarDateString = policyTableId.find("tr:eq(" + i + ")").find("#custCertStarDate").val();
				var payeeCertEndDateString = policyTableId.find("tr:eq(" + i + ")").find("#custCertEndDate").val();
			}
			var payStakeReasion = policyTableId.find("tr:eq(" + i + ")").find("#payStakeReasion").find("option:selected").val();
			var businessCode = policyTableId.find("tr:eq(" + i + ")").find("#businessCode").html();
			var acceptId = policyTableId.find("tr:eq(" + i + ")").find("#acceptId").html();
			var customerGender = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.customerGender']").find("option:selected").val(); 
			var customerBirthday = policyTableId.find("tr:eq(" + i + ")").find("#customerBirthday").val(); 
			var qy_customerId = policyTableId.find("tr:eq(" + i + ")").find("#qy_customerId").text(); 
			//var payeeType = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.payeeType']").find('option:selected').val();
			//alert(i);
			thisAcceptCode=businessCode;
			
			json += "{";
			json = json + "'" + copyInfoArr[0] + "'" + ":'" + serviceCode + "',";
			json = json + "'" + copyInfoArr[1] + "'" + ":'" + policyCode + "',";
			json = json + "'" + copyInfoArr[2] + "'" + ":'" + amountMany + "',";
			json = json + "'" + copyInfoArr[3] + "'" + ":'" + arapAndPrem + "',";
			json = json + "'" + copyInfoArr[4] + "'" + ":'" + payMode + "',";
			json = json + "'" + copyInfoArr[5] + "'" + ":'" + medicalNo + "',";
			json = json + "'" + copyInfoArr[6] + "'" + ":'" + policyPremStr + "',";
			json = json + "'" + copyInfoArr[7] + "'" + ":'" + openingBankName + "',";
			json = json + "'" + copyInfoArr[8] + "'" + ":'" + publicIdentification + "',";
			json = json + "'" + copyInfoArr[9] + "'" + ":'" + getMoneyName + "',";
			json = json + "'" + copyInfoArr[10] + "'" + ":'" + customerCertType + "',";
			json = json + "'" + copyInfoArr[11] + "'" + ":'" + customerCertiCode + "',";
			json = json + "'" + copyInfoArr[12] + "'" + ":'" + payeeCertStarDateString + "',"; 
			json = json + "'" + copyInfoArr[13] + "'" + ":'" + payeeCertEndDateString + "',";
			json = json + "'" + copyInfoArr[14] + "'" + ":'" + payStakeReasion + "',";
			json = json + "'" + copyInfoArr[15] + "'" + ":'" + businessCode + "',";
			json = json + "'" + copyInfoArr[16] + "'" + ":'" + $("#changeId",navTab.getCurrentPanel()).val() + "',";
			json = json + "'" + copyInfoArr[17] + "'" + ":'" + acceptId + "',";
			json = json + "'" + copyInfoArr[18] + "'" + ":'" + $("#qy_customerId",parentDiv).val() + "',";
			
			json = json + "'" + copyInfoArr[19] + "'" + ":'" + customerGender + "',";
			json = json + "'" + copyInfoArr[20] + "'" + ":'" + customerBirthday + "',";
			json = json + "'" + copyInfoArr[21] + "'" + ":'" + customerBirthday + "',";
			json = json + "'" + copyInfoArr[22] + "'" + ":'" + payToThirdPerson + "',";
			json = json + "'" + copyInfoArr[23] + "'" + ":'" + qy_customerId + "',";
			json = json + "'" + copyInfoArr[24] + "'" + ":'" + changQiFlag + "',";
			json = json + "'" + copyInfoArr[25] + "'" + ":'" + publicIdentification + "',";
			json = json + "'" + copyInfoArr[26] + "'" + ":'" + openingBankName + "',";
			json = json + "'" + copyInfoArr[27] + "'" + ":'" + payeeType + "',";
			json += "},";   
		}
		  json = json.substring(0, json.length - 1);
		  json += "]";
			  $.ajax({
					contentType: "application/x-www-form-urlencoded; charset=utf-8",
					type : 'POST',
					url : "${ctx }"+"/cs/csEntry/savePolicyPremArap_PA_csEntryAction.action?jsonStr="+encodeURI(encodeURI(json)),
					//data : 'changeId='+changeId+'&acceptId='+acceptId+'&jsonStr='+$jsonStr.val()+'&customerId='+customerId,
					//data :$("#CspremArapFormId").serializeArray(),
					success:function(data){
						var json = jQuery.parseJSON(data);	
						if(json.statusCode==200){
							var acceptStatus=$(".wsyAcceptStatus");
							for(var indexWsy=0;indexWsy<acceptStatus.length;indexWsy++){
								var thatAcceptCode=$(acceptStatus[indexWsy]).parents("tr").first().children("td").eq(1).text();
/* 								alert(thisAcceptCode);
								alert(thisAcceptCode.length);
								alert(thatAcceptCode);
								alert(thatAcceptCode.length); */
								//alert($(acceptStatus[indexWsy]).parents("tr").first().children("td").eq(5).text());
								//alert(thisAcceptCode==thatAcceptCode);
// 								if(thisAcceptCode==thatAcceptCode){
// 									//07为录入完成
// 									//alert(wsy);
// 									$(acceptStatus[indexWsy]).parents("tr").first().children("td").eq(5).text("录入完成");
// 								}
							}
							alertMsg.correct("保存成功！");
							//id = csServiceEntryDiv
							//刷新补退费信息
// 							initCsServiceEntry
                            if($("#csServiceEntryDiv",navTab.getCurrentPanel()).size() > 0 ){
                            var	$box =  $("#csServiceEntryDiv",navTab.getCurrentPanel());
                            $box.ajaxUrl({
									type : "POST",
									url : "${ctx }"+"/cs/csEntry/initCsServiceEntry_PA_csEntryAction.action?changeId="+$("#changeId",navTab.getCurrentPanel()).val(),
									callback : function() {
										$box.find("[layoutH]").layoutH();
									}
								});
                            }
							/*
                            var acceptCodes=json.message.split(",");							
	
							//保存收付费信息成功后，更新受理状态为更新保全项状态
					 		var $updateDiv = $("#updateDiv",navTab.getCurrentPanel());	//刷新目标div
							if($updateDiv.size() > 0){ //存在要刷新的div
								for(var i=0;i<acceptCodes.length;i++)
								{
									var acceptCode_update= acceptCodes[i];
									console.dir($updateDiv.find("tbody").find("tr#"+acceptCode_update));
									$updateDiv.find("tbody").find("tr#"+acceptCode_update+" td:eq(4)").html("录入完成001");
								}
							}*/
						}else if(json.statusCode==301){
							alertMsg.confirm(json.message , {
								okCall: function() {
									var acceptStatus=$(".wsyAcceptStatus");
									for(var indexWsy=0;indexWsy<acceptStatus.length;indexWsy++){
										var thatAcceptCode=$(acceptStatus[indexWsy]).parents("tr").first().children("td").eq(1).text();
									}
									alertMsg.correct("保存成功！");
									//刷新补退费信息
//		 							initCsServiceEntry
		                            if($("#csServiceEntryDiv",navTab.getCurrentPanel()).size() > 0 ){
		                            var	$box =  $("#csServiceEntryDiv",navTab.getCurrentPanel());
		                            $box.ajaxUrl({
											type : "POST",
											url : "${ctx }"+"/cs/csEntry/initCsServiceEntry_PA_csEntryAction.action?changeId="+$("#changeId",navTab.getCurrentPanel()).val(),
											callback : function() {
												$box.find("[layoutH]").layoutH();
											}
										});
		                            }
									
								},
									cancleCall : function() {
										return false;
									}
								});
							
						}else{
							alertMsg.error(json.message);
						}
					},
				});
	 } 
	 
	 	//展示一个受理号下的应收应付记录
	   function showPremArapByacceptId(acceptId){
		   var href = $("#showPremArapByacceptId").attr("href");
		   var acceptId=$("#premArapAcceptId").val();
		   href=href+acceptId;
		   $("#showPremArapByacceptId").attr("href",href);
		   $("#showPremArapByacceptId").click();
	   }

	   var customerIdNew=null;
	   function queryCrsCustomer(targe){
		   debugger;
		   var parentDiv=$(targe).parents("#policyPayModeInfoDiv",navTab.getCurrentPanel()).get(0);		
		   var parentTrs=$("#policypayMoel tr",parentDiv);
		   //校验必填项
			var checkedFlag=false;
			for(var i=0;i<parentTrs.size();i++){
				if(parentTrs.size==i)
				checkedFlag=true;
				parentTr=parentTrs.get(i);
				var qy_customerId= $("#qy_customerId",parentTr).html();
				var businessCode=$("#businessCode",parentTr).html();
				var customerCertType = $("#customerCertiType"+qy_customerId+businessCode+" option:selected",parentTr).text();
				var arapAndPrem = $("#arapAndPrem",parentTr).text();
				var accountName = $("#policyPremStr",parentTr).val();
				var _amountMany = $('#amountMany',parentTr).text();
				var accoName =  $("#policyPremStr",parentTr).val();
				var payModeBank=$("#payModeBank"+qy_customerId+businessCode,parentTr).val();
				var customerGender = $("#customerGender"+qy_customerId+businessCode+" option:selected",parentTr).text();
				var customerBirthday =  $("#customerBirthday",parentTr).val();
				if(customerCertType==null || ''==customerCertType){
					alertMsg.warn("请选择证件类型");
					return false;
				}
				var getMoneyName = $("#getMoneyName",parentTr).val();
				if(arapAndPrem=='应付'){
					if(getMoneyName==null || getMoneyName.length == 0){
						alertMsg.warn("请填写领款人");
						return false;
					}
				}
				if(customerBirthday==null|| ''==customerBirthday){
					alertMsg.info("请填写付款人出生日期");
					return false;
				}
				// 姓名规则校验
				/** RM:157485针对证件类型为“1-护照”或“e-外国人永久居留身份证”的客户，姓名在原有校验基础上，增加允许“,”（半角）和“.”，且“,”、 “.”不能出现在首位或末尾； **/
				var custCertiType = $("#customerCertiType"+qy_customerId+businessCode,parentTr).val();
				if ("1" == custCertiType || "e" == custCertiType) {
					if(!check_nameForFore($("#getMoneyName",parentTr))){
						return false;
					}
				} else {
					if(!check_name($("#getMoneyName",parentTr))){
						return false;
					}
				}
				var customerCertiCode = $("#customerCertiCode",parentTr).val();
				//按照需求校验证件号
				var customerCertiType = $("#customerCertiType"+qy_customerId+businessCode,parentTr).val();
				var customerCertiCode = $("#customerCertiCode",parentTr).val();
				if(customerCertiCode==null || ''==customerCertiCode){
					alertMsg.warn("请填写证件号码");
					return false;
				}
				// 判断身份证教研修改dongxf_wb start
				if(customerCertiType == '0'){
					if((!idCardNoUtil.check15IdCardNo(customerCertiCode)) && (!idCardNoUtil.check18IdCardNo(customerCertiCode))){
						alertMsg.warn("请输入正确身份证号码");
						return false;
					}
				// modify by dongxf_wb end 
				}else if(customerCertiType == '1'){
					if(!CV_checkPassport(customerCertiCode)){
						alertMsg.warn("请输入正确护照号码");
						return false;
					}
				}else if(customerCertiType == '2'){
					if(!CV_checkOfficerNo(customerCertiCode)){
						alertMsg.warn("请输入正确军官证号码");
						return false;
					}
				}else if(customerCertiType == '5'){
					if(!CV_checkHouseholdRegisterNo(customerCertiCode)){
						alertMsg.warn("请输入正确户口簿号码");
						return false;
					}
				}else if(customerCertiType == 'b'){
					if(!CV_checkGATPassport(customerCertiCode)){
						alertMsg.warn("请输入正确港澳台居民内地通行证");
						return false;
					}
				}else{
					if(customerCertiCode.length>30){
						alertMsg.warn("证件号码长度不能超过30个字符！");
						return false;
					}
				}
				if(accoName!=null){
					accoNames = accoName.split(',');	
				}
				if(arapAndPrem=='应收'){
					if(getMoneyName==null || ''==getMoneyName){
						alertMsg.info("请填写付款人");
						return false;
					}
				}
				var customerName = $("#customerName",parentTr).val();
				if(payModeBank==30){
				if(arapAndPrem=='应收'){
					if(getMoneyName==null || ''==getMoneyName){
						alertMsg.info("请填写付款人");
						return false;
					}
				}
				if(customerBirthday==null|| ''==customerBirthday){
					alertMsg.info("请填写付款人出生日期");
					return false;
				}
				}
			}
		    var policyTableId=$("#policyTableId",parentDiv);
			var length = policyTableId.find("tr").length-1;
			var thisAcceptCode=null;
			var copyInfoArr = [ "serviceCode", "policyCode","amountMany","arapAndPrem","payMode","policyPremArapBank",
			                    "getMoneyName","customerCertiType","customerCertiCode","businessCode","changeId","acceptId","customerId","customerGender","customerBirthday1","taxResidentType","taxCountry"];
			var json = new Array();
			json += "[";
			for (var i = 1; i <= length; i++){
				var serviceCode = policyTableId.find("tr:eq(" + i + ")").find("input[id='hiddenServiceCode']").val(); 
				var policyCode = policyTableId.find("tr:eq(" + i + ")").find("#policyCode").html(); 
				var amountMany = policyTableId.find("tr:eq(" + i + ")").find("#amountMany").html(); 
				var arapAndPrem = policyTableId.find("tr:eq(" + i + ")").find("#arapAndPrem").html(); 
				var payMode = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.payMode']").find('option:selected').val();
				var policyPremStr = policyTableId.find("tr:eq(" + i + ")").find("#policyPremStr").find('option:selected').val(); 
				var getMoneyName = policyTableId.find("tr:eq(" + i + ")").find("#getMoneyName").val(); 
				var customerCertType = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.customerCertiType']").find('option:selected').val(); 
				var customerCertiCode = policyTableId.find("tr:eq(" + i + ")").find("#customerCertiCode").val(); 
				var businessCode = policyTableId.find("tr:eq(" + i + ")").find("#businessCode").html();
				var acceptId = policyTableId.find("tr:eq(" + i + ")").find("#acceptId").html();
				var customerGender = policyTableId.find("tr:eq(" + i + ")").find("select[name='csPremArapVO.customerGender']").find("option:selected").val(); 
				var customerBirthday1 = policyTableId.find("tr:eq(" + i + ")").find("#customerBirthday").val(); 
				var taxResidentType = policyTableId.find("tr:eq(" + i + ")").find("#taxResidentType").val(); 
				var taxCountry = policyTableId.find("tr:eq(" + i + ")").find("#taxCountry").val(); 
				
				thisAcceptCode=businessCode;
				
				json += "{";
				json = json + "'" + copyInfoArr[0] + "'" + ":'" + serviceCode + "',";
				json = json + "'" + copyInfoArr[1] + "'" + ":'" + policyCode + "',";
				json = json + "'" + copyInfoArr[2] + "'" + ":'" + amountMany + "',";
				json = json + "'" + copyInfoArr[3] + "'" + ":'" + arapAndPrem + "',";
				json = json + "'" + copyInfoArr[4] + "'" + ":'" + payMode + "',";
				json = json + "'" + copyInfoArr[5] + "'" + ":'" + policyPremStr + "',";
				json = json + "'" + copyInfoArr[6] + "'" + ":'" + getMoneyName + "',";
				json = json + "'" + copyInfoArr[7] + "'" + ":'" + customerCertType + "',";
				json = json + "'" + copyInfoArr[8] + "'" + ":'" + customerCertiCode + "',";
				json = json + "'" + copyInfoArr[9] + "'" + ":'" + businessCode + "',";
				json = json + "'" + copyInfoArr[10] + "'" + ":'" + $("#changeId",navTab.getCurrentPanel()).val() + "',";
				json = json + "'" + copyInfoArr[11] + "'" + ":'" + acceptId + "',";
				json = json + "'" + copyInfoArr[12] + "'" + ":'" + $("#qy_customerId",parentDiv).val() + "',";
				
				json = json + "'" + copyInfoArr[13] + "'" + ":'" + customerGender + "',";
				json = json + "'" + copyInfoArr[14] + "'" + ":'" + customerBirthday1 + "',";
				json = json + "'" + copyInfoArr[15] + "'" + ":'" + taxResidentType + "',";
				json = json + "'" + copyInfoArr[16] + "'" + ":'" + taxCountry + "',";
				json += "},";   
			} 
			  json = json.substring(0, json.length - 1);
			  json += "]";
				  $.ajax({
						contentType: "application/x-www-form-urlencoded; charset=utf-8",
						type : 'POST',
						url : "${ctx }"+"/cs/csEntry/queryCrsCustomer_PA_csEntryAction.action?jsonStr="+encodeURI(encodeURI(json)),
						dataType : "json",
						success:function(customerVOList){
							
							$.each(customerVOList,function(key,val){
								
								var residentName = customerVOList[key].residentName;
								var countryName = customerVOList[key].countryName;
								var isThirdPerson = customerVOList[key].isThirdPerson;
								var customerId = customerVOList[key].customerId;
								if(isThirdPerson=="1"){
									alertMsg.info("请确认客户是否为除投被保人的第三人");
									if(document.getElementById("showtax").className=="but_blue"){
										document.getElementById("showtax").className="but_gray";
									}
								}else{
									key = key+1;
									customerIdNew=customerId;
									if(residentName==null || residentName==""){
										policyTableId.find("tr:eq(" + key + ")").find("#taxResidentType").val(""); 
										if(document.getElementById("showtax").className=="but_gray"){
											document.getElementById("showtax").className="but_blue";
										}
										alertMsg.info("客户既往未声明税收居民身份，需录入税收居民身份。");
									}else{
										policyTableId.find("tr:eq(" + key + ")").find("#taxResidentType").val(residentName); 
									}
									if(customerName==null || customerName==""){
										policyTableId.find("tr:eq(" + key + ")").find("#taxCountry").val(""); 
									}else{
										policyTableId.find("tr:eq(" + key + ")").find("#taxCountry").val(countryName);
										alertMsg.correct("查询成功");
									}
									if(document.getElementById("showtax").className=="but_gray"){
										document.getElementById("showtax").disabled=true;
									}else{
										document.getElementById("showtax").disabled=false;
									}
								}
							});
						},
					});
	   }
	   //比较时间方法
	    function checkEndTime(startTime,endTime){  
	        var start=new Date(startTime.replace("-", "/").replace("-", "/"));  
	        var end=new Date(endTime.replace("-", "/").replace("-", "/"));  
	        if(end<start){  
	            return false;  
	        }  
	        return true;  
	    } 
	   
		//根据证件有效起期获取证件有效止期
		/* certiType ， 证件类型
	     * certiStartDate ，证件有效期始
	     * birthDateTime ， 客户出生日期
	     * calculDate，计算时间，如申请时间，当前时间等
	     */
		function getEndDate(targe) {
			var parentDiv=$(targe).parents("#policyPayModeInfoDiv",navTab.getCurrentPanel()).get(0);
			var parentTrs=$("#policypayMoel tr",parentDiv);
			for(var i=0;i<parentTrs.size();i++){
				parentTr=parentTrs.get(i);
				var qy_customerId= $("#qy_customerId",parentTr).html();
				var businessCode=$("#businessCode",parentTr).html();
				var customerCertiCode = $("#customerCertiCode",parentTr).val();
				var certyType = $("#customerCertiType"+qy_customerId+businessCode+" option:selected",parentTr).text();
				if(certyType == ''){
					certyType = $("#customerCertiType"+businessCode+" option:selected",parentTr).text();
				}
				if(certyType =='身份证' || certyType =='户口簿'){
					var customerBirthday = getBirthdayFromIdCard(customerCertiCode);
				}else{
					var customerBirthday =  $("#customerBirthday",parentTr).val();
				}
				var custCertStarDate = $("#custCertStarDate",parentTr).val();
				var custCertEndDate = $("#custCertEndDate",parentTr).val();
				var longDate = $("#longDate",parentTr).val();
			     if (certyType =='身份证') {
			    	 getCertiEndDate1($("#custCertStarDate",parentTr),$("#customerBirthday",parentTr),
			    			 $("#longDate",parentTr),$("#changQiFlag", navTab.getCurrentPanel()),
			    			 $("#custCertEndDate",parentTr));
			     }else if(certyType == '港澳台居民居住证'){
			    	 /* 1:计算证件有效止期  2:不能勾选长期*/
			    	var newDate, sDate, eDate, iDays
			        //js月份默认是从0开始的所以月份要-1
		            newDate = custCertStarDate.split("-");
		            var date  = new Date(newDate[0], newDate[1]-1, newDate[2]);
		        	date.setFullYear(date.getFullYear() + 5);
		        	var year = date.getFullYear();
		        	var month = date.getMonth()+1, month = month < 10 ? '0' + month : month;
		        	var day = date.getDate(), day =day < 10 ? '0' + day : day;
		        	$("#custCertEndDate", navTab.getCurrentPanel()).val(year + '-' + month + '-' + day);
		        	$("input[id='longDate']").click(function(){ 
		        		this.attr( 'disabled', false);
		            }); 
		        	$("#changQiFlag", navTab.getCurrentPanel()).val("0");
		        	$("#longDate", navTab.getCurrentPanel()).attr( 'disabled', true);
			     }else if(certyType =='户口簿'){
			    	 var customerBirthdayForDate = new Date(customerBirthday.replace(/-/g, "/"));
			    	 customerBirthdayForDate = customerBirthdayForDate.setFullYear(customerBirthdayForDate.getFullYear()+16);
			    	 var date2 = dateChange(-1,customerBirthdayForDate);
			    	 $("#custCertEndDate", navTab.getCurrentPanel()).val(date2);
			    	 $("#custCertEndDate", navTab.getCurrentPanel()).attr( 'disabled', true);
			    	 $("#longDate", navTab.getCurrentPanel()).attr( 'disabled', true);
			     }
			}
		}
		//获取证件止期
		//1:证件起期 2：出生日期  3：长期  4：是否长期 5:证件止期
		function getCertiEndDate1(certiStartDateObj,birthDateObj,longDateObj,changQiFlagObj,certiEndDateObj){
			debugger;
			var certiStartDate = certiStartDateObj.val();//证件的有效期起
			var birthDate = birthDateObj.val();//出生日期
			longDateObj.attr("checked",false);
			if((null != birthDate && "" != birthDate) && (null != certiStartDate && "" != certiStartDate) ){
				var newDate = certiStartDate.split("-");
		        var date  = new Date(newDate[0], newDate[1]-1, newDate[2]);
		        //parseInt(18);
		        var now_age = getAgeForPA1(birthDate,certiStartDate);
				if(now_age > 0 && now_age <= 15){
					date.setFullYear(date.getFullYear() + 5);
				}else if(now_age > 15 && now_age <= 25){
					date.setFullYear(date.getFullYear() + 10);
				}else if(now_age > 25 && now_age <= 45){
					date.setFullYear(date.getFullYear() + 20);
				}else if(now_age > 45){
					longDateObj.attr("checked","checked");
					certiEndDateObj.val("");
					debugger;
					certiEndDateObj.attr('disabled', true);
					changQiFlagObj.val("1");
					return;
				}
				var year = date.getFullYear();
		    	var month = date.getMonth()+1, month = month < 10 ? '0' + month : month;
		    	var day = date.getDate(), day =day < 10 ? '0' + day : day;
		    	var custCertEndDate = year+"-"+month+"-"+day;
		    	certiEndDateObj.val(custCertEndDate);
		    	certiEndDateObj.attr('disabled', false);
		    	changQiFlagObj.val("0");
		    	return;
			}
		}
		function dateChange(num,date) {
		　　if (!date) {
		　　　　date = new Date();//没有传入值时,默认是当前日期
		　　　　date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
		　　}
		　　date = date/1000 - 86400;//修改后的时间戳
		　　var newDate = new Date(parseInt(date) * 1000);//转换为时间
		   var year = newDate.getFullYear();
    	   var month = newDate.getMonth()+1, month = month < 10 ? '0' + month : month;
    	   var day = newDate.getDate(), day =day < 10 ? '0' + day : day;
		　　return year + '-' + month + '-' + day;
		}
		function endDateClear(){
			var certyType = $("#customerCertType", navTab.getCurrentPanel()).val();
			if(certyType  == "h"){//港澳台居民居住证
	    		$('#longDate').removeAttr("checked"); 
	    		return;
	    	}
			if($("#longDate", navTab.getCurrentPanel()).attr("checked") == "checked"){
				$("#custCertEndDate", navTab.getCurrentPanel()).val("");
				$("#custCertEndDate", navTab.getCurrentPanel()).attr('disabled', true);
				$("#changQiFlag", navTab.getCurrentPanel()).val("1");
			}else{
				$("#custCertEndDate", navTab.getCurrentPanel()).attr('disabled', false);
				$("#custCertEndDate", navTab.getCurrentPanel()).val("");
				$("#changQiFlag", navTab.getCurrentPanel()).val("0");
			}
		}
		// 根据身份证号码获取出生日期
		function getBirthdayFromIdCard(idCard) {
	        var birthday = "";  
	        if(idCard != null && idCard != ""){  
	            if(idCard.length == 15){  
	                birthday = "19"+idCard.substr(6,6);  
	            } else if(idCard.length == 18){  
	                birthday = idCard.substr(6,8);  
	            }  
	          
	            birthday = birthday.replace(/(.{4})(.{2})/,"$1-$2-");  
	        }  
	          
	        return birthday;  
	    }
		/* 
		 *  方法说明：RM:157485非半角“，”自动转化为“,”（半角）
		 */
		function moneyNameAcceptKeyUp(obj) {
			debugger;
			if (typeof($(obj).val()) != 'undefined') {
				obj.value = obj.value.replace(/[，]/g,",");
			}
		}
		//101239 start
		function checkThirdPerson(obj){
			var changeId = $("#changeId",navTab.getCurrentPanel()).val();
			var customerName="";
			var customerCertiType="";
			var customerCertiCode="";
			
			customerName=$(obj,navTab.getCurrentPanel()).closest("tr").find("input#getMoneyName").val();
			customerCertiType=$(obj,navTab.getCurrentPanel()).closest("tr").find("[name='csPremArapVO.customerCertiType']").find("option:selected").val();
			customerCertiCode=$(obj,navTab.getCurrentPanel()).closest("tr").find("input#customerCertiCode").val();
			$.ajax({
				type : "post",
				dataType : "json",
				url : "${ctx}/cs/csEntry/checkThirdPerson_PA_csEntryAction.action",
				data : 'changeId='+changeId+'&customerName='+encodeURI(customerName)+'&customerCertiType='+customerCertiType+'&customerCertiCode='+customerCertiCode,
				success : function(adr) {
					if(adr.message == 'no'){
						$("#payToThirdPerson", navTab.getCurrentPanel()).attr("checked","checked");
						//101239 ALM18127
						$("#payToThirdPerson", navTab.getCurrentPanel()).removeAttr("disabled");
						// 判断是否展示了付款至第三人复选框
                        var isShowThird=$("#isShowThird", navTab.getCurrentPanel()).val();
                            if(isShowThird==1){
                        		payToThirdPerson = "1";
                        	}
					}else{
						$("#payToThirdPerson", navTab.getCurrentPanel()).removeAttr("checked");
						//101239 ALM18127
						$("#payToThirdPerson", navTab.getCurrentPanel()).attr("disabled","disabled");
						payToThirdPerson = "0";
					}
				}
			});
			
		}
		
		function checkbankStr(obj) {

			var bankstr = document.getElementById("policyPremStrInput").value;
			if(bankstr!=null && bankstr!=""){
				var bankstrTrim = bankstr.replace(/[ ]/g,"");//中部去空
				//纯数字校验
				var patternNO = /^\d*$/;
				/* if(!patternNO.test(bankstrTrim)){//纯数字
					$("#policyPremStrInput").val("");
					alertMsg.warn("账户信息必须全部为数字");
					return false;
				} */
			}else{
				$("#policyPremStrInput").val("");
				alertMsg.warn("账户信息不能为空。");
				return false;
			}

			var str = obj.value;
			if ($("#checkname").val() == "" || $("#checkname").val() == null) {
				$("#checkname").attr("value", str);
				obj.value = "";
				/*  $("#nameDIV").html('<font style="color:red;font-size:12px">请再次输入</font>') ; */
				$("#strDIV").html('再次输入');
			} else {
				if (obj.value == $("#checkname").val()) {
					$("#checkname").val("");
					$("#strDIV").html("");
				} else {
					$("#checkname").attr("class", "error");
					$("#").val("");
					$("#strDIV").html("");
					alertMsg.warn("两次输入账户信息不一致!");
					$("#checkname").val("");
					$("#policyPremStrInput",navTab.getCurrentPanel()).val("");
					return false;
				}
			}

		}
		
		function checkCustomerCertiCodeAndName(obj){
			debugger;
			var changeId = $("#changeId",navTab.getCurrentPanel()).val();
			var customerName="";
			var customerCertiType="";
			var customerCertiCode="";
			
			customerName=$(obj,navTab.getCurrentPanel()).parent().parent().find("input#getMoneyName").val();
			customerCertiType=$(obj,navTab.getCurrentPanel()).parent().parent().find("[name='csPremArapVO.customerCertiType']").find("option:selected").val();
			customerCertiCode=$(obj,navTab.getCurrentPanel()).parent().parent().find("input#customerCertiCode").val();
			if(customerName != '' && customerName != null && customerCertiCode != '' && customerCertiCode != null){
				$.ajax({
					type : "post",
					dataType : "json",
					url : "${ctx}/cs/csEntry/checkCustomerCertiCodeAndName_PA_csEntryAction.action",
					data : 'changeId='+changeId+'&customerName='+encodeURI(customerName)+'&customerCertiType='+customerCertiType+'&customerCertiCode='+customerCertiCode,
					success : function(premArap) {
						$.each(premArap,function(key,val){
							debugger;
							var custCertStarDateTime=premArap[key].payeeCertStarDate;
							var custCertEndDateTime=premArap[key].payeeCertEndDate;
							if(custCertStarDateTime != null && custCertEndDateTime != null){
								var payeeCertStarDate = timestampToTime(custCertStarDateTime.time);
								var payeeCertEndDate = timestampToTime(custCertEndDateTime.time);
								
								if(payeeCertStarDate != "" && payeeCertStarDate != null){
									$("#custCertStarDate").val(payeeCertStarDate);
								}
								if(payeeCertEndDate == '9999-12-31'){
									$("#changQiFlag").val("1");
									$("#custCertEndDate").val("");
									$("#custCertEndDate").attr( 'disabled', true);
									$("#longDate").attr("checked","checked");
								}else{
									$("#custCertEndDate").val(payeeCertEndDate);
								}
							}
						})
					}
				});
			}
		}
		function payeeTypeChange(targe) {
			debugger;
			var parentDiv=$(targe).parents("#policyPayModeInfoDiv",navTab.getCurrentPanel()).get(0);		
			var parentTrs=$(targe).parents("#holloshit");
			//alert(parentTrs.find("select[name='csPremArapVO.payeeType']").val());
			var payeeType = parentTrs.find("select[name='csPremArapVO.payeeType']").val();
			if(payeeType!=null && payeeType!="" && payeeType =="2"){// 企业
				$("#payModeBankZRR",parentTrs).hide();
				$("#payModeBankZRR", parentTrs).attr("disabled",true);
				$("#payModeBankQY",parentTrs).show();
				$("#companyInfo",parentTrs).show();
				$("#customerCertiTypeQY",parentTrs).show();
				$("#customerCertiTypeZRR",parentTrs).hide();
				$("#customerCertiTypeZRR", parentTrs).attr("disabled",true);
				$("#getMoneyNameZRR",parentTrs).hide();
				$("#getMoneyNameZRR", parentTrs).attr("disabled",true);
				$("#getMoneyNameQY",parentTrs).show();
				$("#customerCertiCodeZRR",parentTrs).hide();
				$("#customerCertiCodeZRR", parentTrs).attr("disabled",true);
				$("#customerCertiCodeQY",parentTrs).show();
				$("#customerGenderZRR",parentTrs).hide();
				$("#customerGenderZRR", parentTrs).attr("disabled",true);
				$("#customerGenderQY",parentTrs).show();
				$("#customerBirthdayZRR",parentTrs).hide();
				$("#customerBirthdayZRR", parentTrs).attr("disabled",true);
				$("#customerBirthdayQY",parentTrs).show();
				$("#taxResidentTypeZRR",parentTrs).hide();
				$("#taxResidentTypeZRR", parentTrs).attr("disabled",true);
				$("#taxResidentTypeQY",parentTrs).show();
				$("#taxCountryZRR",parentTrs).hide();
				$("#taxCountryZRR", parentTrs).attr("disabled",true);
				$("#taxCountryQY",parentTrs).show();
				$("#custCertStarDateZRR",parentTrs).hide();
				$("#custCertStarDateZRR", parentTrs).attr("disabled",true);
				$("#custCertStarDateQY",parentTrs).show();
				$("#custCertEndDateZRR",parentTrs).hide();
				$("#custCertEndDateZRR", parentTrs).attr("disabled",true);
				$("#custCertEndDateQY",parentTrs).show();
				$("#payStakeReasionZRR",parentTrs).hide();
				$("#payStakeReasionZRR", parentTrs).attr("disabled",true);
				$("#payStakeReasionQY",parentTrs).show();
				
				// 企业付费时 将领款人性别,出生日期,税收身份，居民国隐藏 
				$("#customerGenderQY",parentTrs).hide();
				$("#customerBirthdayQY",parentTrs).hide();
				$("#taxResidentTypeQY",parentTrs).hide();
				$("#taxCountryQY",parentTrs).hide();
				
				//多保单场景，如果有非928的保单，那么就是自然人，此时领款人性别、领款人出生日期、税收居民身份、税收居民国（地区）不应该隐藏
				var $trs=$("#policyTableId tbody tr",navTab.getCurrentPanel());	
				var isHide = false;
				$trs.each(function (){
					var payeeType1 = $(this).find("select[name='csPremArapVO.payeeType']").val();
					if(payeeType1 == "1"){
						isHide = true;
					}
				});
				if(!isHide){
					var beferTable = $("#policyTableId",navTab.getCurrentPanel());                                                                                                                
					var beferTableTrs = $(beferTable).find("thead tr");
					beferTableTrs.each(function(){
						// 性别
						$(this).find("th[colname='customerGender']").hide();
						// 出生日期
						$(this).find("th[colname='customerBirthday']").hide();
						// 税收身份
						$(this).find("th[colname='taxResidentType']").hide();
						// 居民国
						$(this).find("th[colname='taxCountry']").hide();
					});
				}
				

			}else{
				$("#payModeBankQY",parentTrs).hide();
				$("#payModeBankQY", parentTrs).attr("disabled",true);
				$("#payModeBankZRR",parentTrs).show();
				$("#payModeBankZRR",parentTrs).removeAttr("disabled");
				$("#companyInfo",parentTrs).hide();
				$("#customerCertiTypeQY",parentTrs).hide();
				$("#customerCertiTypeZRR",parentTrs).show();
				$("#customerCertiTypeZRR",parentTrs).removeAttr("disabled");
				$("#getMoneyNameQY",parentTrs).hide();
				$("#getMoneyNameQY", parentTrs).attr("disabled",true);
				$("#getMoneyNameZRR",parentTrs).show();
				$("#getMoneyNameZRR",parentTrs).removeAttr("disabled");
				$("#customerCertiCodeQY",parentTrs).hide();
				$("#customerCertiCodeQY", parentTrs).attr("disabled",true);
				$("#customerCertiCodeZRR",parentTrs).show();
				$("#customerCertiCodeZRR",parentTrs).removeAttr("disabled");
				$("#customerGenderQY",parentTrs).hide();
				$("#customerGenderQY", parentTrs).attr("disabled",true);
				$("#customerGenderZRR",parentTrs).show();
				$("#customerGenderZRR",parentTrs).removeAttr("disabled");
				$("#customerBirthdayZRR",parentTrs).show();
				$("#customerBirthdayZRR", parentTrs).removeAttr("disabled");
				$("#taxResidentTypeQY",parentTrs).hide();
				$("#taxResidentTypeQY", parentTrs).attr("disabled",true);
				$("#taxResidentTypeZRR",parentTrs).show();
				$("#taxResidentTypeZRR",parentTrs).removeAttr("disabled");
				$("#taxCountryQY",parentTrs).hide();
				$("#taxCountryQY", parentTrs).attr("disabled",true);
				$("#taxCountryZRR",parentTrs).show();
				$("#taxCountryZRR",parentTrs).removeAttr("disabled");
				$("#custCertStarDateQY",parentTrs).hide();
				$("#custCertStarDateQY", parentTrs).attr("disabled",true);
				$("#custCertStarDateZRR",parentTrs).show();
				$("#custCertStarDateZRR",parentTrs).removeAttr("disabled");
				$("#custCertEndDateQY",parentTrs).hide();
				$("#custCertEndDateQY", parentTrs).attr("disabled",true);
				$("#custCertEndDateZRR",parentTrs).show();
				$("#custCertEndDateZRR",parentTrs).removeAttr("disabled");
				$("#payStakeReasionQY",parentTrs).hide();
				$("#payStakeReasionQY", parentTrs).attr("disabled",true);
				$("#payStakeReasionZRR",parentTrs).show();
				$("#payStakeReasionZRR",parentTrs).removeAttr("disabled");

				// 自然人付费时 将领款人性别,出生日期,税收身份，居民国展示 
				var beferTable = $("#policyTableId",navTab.getCurrentPanel());
				var beferTableTrs = $(beferTable).find("thead tr");
				beferTableTrs.each(function(){
					// 性别
					$(this).find("th[colname='customerGender']").show();
					// 出生日期
					$(this).find("th[colname='customerBirthday']").show();
					// 税收身份
					$(this).find("th[colname='taxResidentType']").show();
					// 居民国
					$(this).find("th[colname='taxCountry']").show();
				});
				
			}
			//106_28657:当交费人类型变更切换时，所对应的补退费形式也会变更，所以此时需要将账户信息也做对应的变更
			payChanBank(targe);

		}
		//101239 end
	  /* 设置金额保留两位小数显示 */
  /*    $(function(){
      	 $amountMany = $("#amountMany", navTab.getCurrentPanel())
      	 var amountMany = $amountMany.text();
      	 $amountMany.text(returnFloatTwo(amountMany));
      });
     
      function returnFloatTwo(value){
    	  alert(value);
      	 var xsd=value.toString().split(".");
      	 if(xsd.length==1){
      	 	value=value.toString()+".00";
      	 	return value;
      	 }
      	 if(xsd.length>1){
  	    	 if(xsd[1].length<2){
  	    	   value=value.toString()+"0";
  	    	 }
  	    	 return value;
      	 }
      } */
</script>