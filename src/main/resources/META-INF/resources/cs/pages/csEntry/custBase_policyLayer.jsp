<!--保全变更录入- 客户基本资料变更页面 -保单层级信息变更页面-->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<style type="text/css"></style>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
	<div class="pageContent">
		<form method="post" action="" 
			class="required-validate" onsubmit="return navTabSearch(this);" id="custBaseInfoPolicyUpdateForm">
			<div class="panel">
				<h1>客户基本资料变更</h1>
				<div class="pageFormContent" id="" layoutH="110px" style="background-color: white;">
					<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
					<s:include value="customerInfo_5Items.jsp"/>
					<div class="panel" minH="100">
						<h1>变更前信息</h1>
						<div minH="95" >
							<s:if test="1!=2">
								<dl>
									<dt>国籍</dt> 
									<dd>
	<%-- 										<Field:codeTable id="countryCode" name="custBaseInfoUpdateVO.customerVO.countryCode" --%>
	<%--                              				tableName="APP___PAS__DBUSER.T_COUNTRY" nullOption="true" value="${custBaseInfoUpdateVO.customerVO.countryCode}"/> --%>
									</dd>
								</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>婚姻状况</dt> 
								<dd>
<%-- 										<Field:codeTable id="marriageStatus" name="custBaseInfoUpdateVO.customerVO.marriageStatus" --%>
<%--                              				tableName="APP___PAS__DBUSER.T_MARRIAGE" nullOption="true" value="${custBaseInfoUpdateVO.customerVO.marriageStatus}"/> --%>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>子女状况</dt> 
								<dd>
								<s:select name="custBaseInfoUpdateVO.customerVO.isParent" id="isParent" cssClass="combox"
									list="#{'1':'邮件','2':'短信'}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>证件有效期起期</dt> 
								<dd>
								<input id="custCertStarDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertStarDate" 
									value="${custBaseInfoUpdateVO.customerVO.custCertStarDate}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>证件有效期止期</dt> 
								<dd>
								<input id="custCertEndDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertEndDate" 
									value="${custBaseInfoUpdateVO.customerVO.custCertEndDate}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>驾照类型</dt> 
								<dd>
								<s:select name="custBaseInfoUpdateVO.customerVO.driverLicenseType" id="driverLicenseType" cssClass="combox"
									list="#{'1':'邮件','2':'短信'}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>公司联系客户方式</dt> 
								<dd>
								<s:select name="custBaseInfoUpdateVO.companyNameContactMethod" id="companyNameContactMethod" cssClass="combox"
									list="#{'1':'邮件','2':'短信'}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>移动电话</dt> 
								<dd>
								<input id="mobileTel" type="text" name="custBaseInfoUpdateVO.customerVO.mobileTel" 
									value="${custBaseInfoUpdateVO.customerVO.mobileTel}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>固定电话</dt> 
								<dd>
								<input id="houseTel" type="expandPhone" name="custBaseInfoUpdateVO.customerVO.houseTel" 
									value="${custBaseInfoUpdateVO.customerVO.houseTel}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>电子邮箱</dt> 
								<dd>
								<input id="email" type="text" name="custBaseInfoUpdateVO.customerVO.email" class="email"
									value="${custBaseInfoUpdateVO.customerVO.email}"/>
								</dd>
							</dl>
							</s:if>
							<s:if test="1!=2">
							<dl>
								<dt>工作单位</dt> 
								<dd>
								<input id="companyName" type="text" name="custBaseInfoUpdateVO.customerVO.companyName" 
									value="${custBaseInfoUpdateVO.customerVO.companyName}"/>
								</dd>
							</dl>
							</s:if>
							<table class="list" id="" style="width:100%;">
								<thead>
									<tr id="" align="center">
										<th>地址号码</th>
										<th>省/直辖市</th>
										<th>市</th>
										<th>区县</th>
										<th>地址</th>
										<th>邮政编码</th>
									</tr>
								</thead>
								<tbody>
<%-- 											<s:iterator value="plist" id="plist"> --%>
										<tr align="center">
											<td>110112</td>
											<td>${addressVO.state}</td>
											<td>${addressVO.city}</td>
											<td>${addressVO.district}</td>
											<td>${addressVO.address}</td>
											<td>${addressVO.postCode}</td>
										</tr>
<%-- 											</s:iterator> --%>
								</tbody>
							</table>
						</div>
					</div>
					
					<div class="panel" minH="100">
						<h1>变更后信息</h1>
						<div minH="95" class="pageFormContent">
							<s:if test="1!=2">
					<dl>
						<dt>国籍</dt> 
						<dd>
<%-- 										<Field:codeTable id="countryCode" name="custBaseInfoUpdateVO.customerVO.countryCode" --%>
<%--                              				tableName="APP___PAS__DBUSER.T_COUNTRY" nullOption="true" value="${custBaseInfoUpdateVO.customerVO.countryCode}"/> --%>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>婚姻状况</dt> 
						<dd>
<%-- 										<Field:codeTable id="marriageStatus" name="custBaseInfoUpdateVO.customerVO.marriageStatus" --%>
<%--                              				tableName="APP___PAS__DBUSER.T_MARRIAGE" nullOption="true" value="${custBaseInfoUpdateVO.customerVO.marriageStatus}"/> --%>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>子女状况</dt> 
						<dd>
						<s:select name="custBaseInfoUpdateVO.customerVO.isParent" id="isParent" cssClass="combox"
							list="#{'1':'邮件','2':'短信'}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>证件有效期起期</dt> 
						<dd>
						<input id="custCertStarDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertStarDate" 
							value="${custBaseInfoUpdateVO.customerVO.custCertStarDate}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>证件有效期止期</dt> 
						<dd>
						<input id="custCertEndDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertEndDate" 
							value="${custBaseInfoUpdateVO.customerVO.custCertEndDate}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>驾照类型</dt> 
						<dd>
						<s:select name="custBaseInfoUpdateVO.customerVO.driverLicenseType" id="driverLicenseType" cssClass="combox"
							list="#{'1':'邮件','2':'短信'}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>公司联系客户方式</dt> 
						<dd>
						<s:select name="custBaseInfoUpdateVO.companyNameContactMethod" id="companyNameContactMethod" cssClass="combox"
							list="#{'1':'邮件','2':'短信'}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>移动电话</dt> 
						<dd>
						<input id="mobileTel" type="expandMobile" name="custBaseInfoUpdateVO.customerVO.mobileTel" 
							value="${custBaseInfoUpdateVO.customerVO.mobileTel}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>固定电话</dt> 
						<dd>
						<input id="houseTel" type="expandPhone" name="custBaseInfoUpdateVO.customerVO.houseTel" 
							value="${custBaseInfoUpdateVO.customerVO.houseTel}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>电子邮箱</dt> 
						<dd>
						<input id="email" type="text" name="custBaseInfoUpdateVO.customerVO.email" class="email"
							value="${custBaseInfoUpdateVO.customerVO.email}"/>
						</dd>
					</dl>
					</s:if>
							<s:if test="1!=2">
					<dl>
						<dt>工作单位</dt> 
						<dd>
						<input id="companyName" type="text" name="custBaseInfoUpdateVO.customerVO.companyName" 
							value="${custBaseInfoUpdateVO.customerVO.companyName}"/>
						</dd>
					</dl>
					</s:if>
							<table class="list" id="" style="width:100%;">
								<thead>
									<tr id="" align="center">
										<th>地址号码</th>
										<th>省/直辖市</th>
										<th>市</th>
										<th>区县</th>
										<th>地址</th>
										<th>邮政编码</th>
									</tr>
								</thead>
								<tbody>
<%-- 											<s:iterator value="plist1" id="plist1"> --%>
										<tr align="center">
											<td>110112</td>
											<td>${addressVO.state}</td>
											<td>${addressVO.city}</td>
											<td>${addressVO.district}</td>
											<td>${addressVO.address}</td>
											<td>${addressVO.postCode}</td>
										</tr>
<%-- 											</s:iterator> --%>
								</tbody>
							</table>
						</div>
					</div>
					
					<div>
						<label style="width: auto;">
							<input id="isApplyPolicy" type="checkbox" name="" />
							应用所有涉及的保单
						</label> 
					</div>
					
					<div class="panel" minH="100">
						<h1>涉及保单列表</h1>
						<div minH="95">
							<table class="list" id="relPolicyList" width="100%">
								<thead>
									<tr id="" align="center">
										<th>保单号</th>
										<th>角色</th>
										<th><input type="checkbox" group="c1" value="2" class="checkboxCtrl"/>公司联系客户方式</th>
										<th><input type="checkbox" group="c2" value="1" class="checkboxCtrl"/>移动电话</th>
										<th><input type="checkbox" group="c3" value="2" class="checkboxCtrl"/>固定电话</th>
										<th><input type="checkbox" group="c4" value="1" class="checkboxCtrl"/>电子邮箱</th>
										<th><input type="checkbox" group="c5"  class="checkboxCtrl"/>地址信息</th>
									</tr>
								</thead>
								<tbody>
<%-- 											<s:iterator value="plist2" id="plist2"> --%>
										<tr align="center">
											<td>${policyCode}</td>
											<td>${rules}</td>
											<td>
												<s:select name="" id="companyContactMethod" cssClass="combox"
													list="#{'1':'邮件','2':'短信'}" listKey="key" listValue="value" 
 													headerKey="" headerValue="请选择"/>
<%-- 													<select class="combox" > --%>
<%-- 														<option value="">111</option> --%>
<%-- 														<option value="2" selected="selected">222</option> --%>
<%-- 													</select> --%>
												<input type="checkbox" name="c1"  value="1" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="mobileTel" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c2" value="2" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="houseTel" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c3"  value="1" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="email" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c4"  value="2" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="address" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c5"  value="1" style="display: none"/>
											</td>
										</tr>
										<tr align="center">
											<td>${policyCode}</td>
											<td>${rules}</td>
											<td>
												<s:select name="" id="companyContactMethod" cssClass="combox"
													list="#{'1':'邮件','2':'短信'}" listKey="key" listValue="value" 
 													headerKey="" headerValue="请选择"/>
<%-- 													<select class="combox" > --%>
<%-- 														<option value="">111</option> --%>
<%-- 														<option value="2" selected="selected">222</option> --%>
<%-- 													</select> --%>
												<input type="checkbox" name="c1"  value="2" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="mobileTel" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c2" value="1" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="houseTel" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c3"  value="2" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="email" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c4"  value="1" style="display: none"/>
											</td>
											<td>
												<s:select name="" id="address" cssClass="combox"
													list="#{'1':'费率表','2':'加费表'}" listKey="key" listValue="value"
													headerKey="" headerValue="请选择"/>
													<input type="checkbox" name="c5"  value="2" style="display: none"/>
											</td>
										</tr>
<%-- 											</s:iterator> --%>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>
<!-- </div> -->
<div class="formBar" style="height: 30px">
	<ul>
		<li>
			<div class="button">
				<div class="buttonContent">
					<button type="button" onclick="up()">上一步</button>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<button type="button" onclick="next()">下一步</button>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起签报</button>
					</a>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起生调</button>
					</a>
				</div>
			</div>
		</li>
<!-- 		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起回访</button>
					</a>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起转办</button>
					</a>
				</div>
			</div>
		</li> -->
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">撤销</button>
					</a>
				</div>
			</div>
		</li>
	</ul>
</div>

<script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById){
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false){
				eval("obj.style." + theProp + "=" + theValue);
			}else{
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};
	//下一页
	function next() {
		//保存变更信息---返回保全录入主页面
		var $obj = $("#custBaseInfoPolicyUpdateForm", navTab.getCurrentPanel());
		action = "serviceitem/saveAllCustBaseInfoPolicy_PA_custBaseInfoPolicyUpdateAction.action";
		$obj.attr('action', action);
		$obj.submit();
	};
	//上一页
	function up() {
		//系统提示是否保存变更信息，确认--保存，取消--清除
		alertMsg.confirm("请确认是否需要保存录入的信息？",{
			okCall:function(){
				//保存变更信息---返回客户基本信息录入页面
				var $obj = $("#custBaseInfoPolicyUpdateForm", navTab.getCurrentPanel());
				action = "serviceitem/saveAllCustBaseInfoPolicy_PA_custBaseInfoPolicyUpdateAction.action";
				$obj.attr('action', action);
				$obj.submit();
			},
			cancelCall:function(){
				//清除变更信息---返回客户基本信息录入页面
				var $obj = $("#custBaseInfoPolicyUpdateForm", navTab.getCurrentPanel());
				action = "serviceitem/delCustBaseInfoPolicy_PA_custBaseInfoPolicyUpdateAction.action";
				$obj.attr('action', action);
				$obj.submit();
			}
		});
	};

	
	//table表头的checkBox的绑定单机事件单机事件
	function custBase_thCBoxClick(){
		var $table = $("#relPolicyList",navTab.getCurrentPanel());
		var $thCheckBoxs = $table.find("thead input:checkbox");
		$thCheckBoxs.on('change',function(){
			var $thCheckBox = $(this);
			var _thCheckBoxVal=  $(this).val()||'';
			var _group = $thCheckBox.attr("group");
			var $tdCheckBoxs = $table.find("tbody input[name='"+_group+"']");
			var $selects =$tdCheckBoxs.parent("td").find("select");
			if($thCheckBox.is(":checked")){
				$selects.find("option[value='"+_thCheckBoxVal+"']").attr("selected","selected");
			}else if($thCheckBox.not(":checked")&&_thCheckBoxVal!=''){
				$tdCheckBoxs.each(function(){
					var _val = $(this).val();
					$(this).parent("td").find("select option[value='"+_val+"']").attr("selected","selected");
				});
			}
			$selects.each(function(){
				$(this).appendTo($(this).parents("td"));
				$(this).parents("td").find("div.combox").remove();
				$(this).addClass("combox").combox();
			});
		});
	};
	
	function custBase_relAllButton(){
		$("#isApplyPolicy",navTab.getCurrentPanel()).on('click',function(){
			var $table = $("#relPolicyList",navTab.getCurrentPanel());
			var $thCheckBoxs = $table.find("thead input:checkbox");
			
			if($(this).is(":checked")){
				$thCheckBoxs.each(function(){
					$(this).attr("checked","checked");
// 					$(this).removeAttr("checked");
				});
			}else{
				$thCheckBoxs.each(function(){
					$(this).removeAttr("checked");
				});
			};
			$thCheckBoxs.change();
		});
		
	}
	
	$(document).ready(function(){
		custBase_thCBoxClick();
		custBase_relAllButton();
	});
</script>
<script type="text/javascript">
	$(document).ready(function(){
		csHelpMenu();
	});
</script>