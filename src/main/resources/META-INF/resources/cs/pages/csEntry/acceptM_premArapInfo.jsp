<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-补退费信息 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/check_name.js"></script>
<!-- 保全项目的补退费信息 -->
<!-- <div class="panel collapse"> -->
<%-- <h1>
		<s:property value="" />
	</h1> --%>
<div class="divfclass">
	<h1>
		<img src="${ctx}/cs/img/icon/tubiao.png" />补退费信息
	</h1>
</div>
<div class="tabdivclass" style="background-color: white;">
	<table class="list" style="width: 100%">
		<thead>
			<tr>
				<th>保单号</th>
				<th>险种</th>
				<th>应缴/应退日期</th>
				<th>费用类型</th>
				<th>金额</th>
				<th>补退费标识</th>
				<th>权益人</th>
				<th>权益人角色</th>
			</tr>
		</thead>
		<tbody>
			<s:iterator value="csPremArapVOList">
				<tr>
					<td><s:property value="policyCode" /></td>
					<td>${busiProdCode}<s:property value="busiProdName" /></td>
					<td style="width: 10%"><s:date name="dueTime"
							format="yyyy-MM-dd"></s:date>
					<td><Field:codeValue tableName="APP___PAS__DBUSER.T_FEE_TYPE"
							value="${feeType}" /></td>
					<td align="right"><s:property value="feeAmount" /></td>
					<%-- <td><s:property value="arapFlag" /></td> --%>
					<td><Field:codeValue tableName="APP___PAS__DBUSER.T_AR_AP"
							value="${arapFlag}" /></td>
					<td><s:property value="qy_customerName" /></td>
					<td><s:property value="qy_customerRole" /></td>
				</tr>
			</s:iterator>
		</tbody>
	</table>
</div>
<!-- 补退费形式 -->
<s:if test="csPremArapVOList != null && csPremArapVOList.size()>0">
	<s:iterator value='csArapVOs' var='csPolicyPremArap'>
		<div class="pageFormContent">
			<div class="panel collapse">
				<h1></h1>
				<div id="policyPayModeInfoDiv">
					<s:set name='qy_customerId'
						value="#csPolicyPremArap.csPremArapVOs.get(0).qy_customerId"></s:set>
					<s:set name='qy_customerName'
						value="#csPolicyPremArap.csPremArapVOs.get(0).qy_customerName"></s:set>
					<s:set name='businessCode'
						value="#csPolicyPremArap.csPremArapVOs.get(0).businessCode"></s:set>
					<s:include value="acceptM_policyPayMode.jsp"></s:include>
				</div>
			</div>
		</div>
	</s:iterator>
</s:if>
<!-- </div> -->