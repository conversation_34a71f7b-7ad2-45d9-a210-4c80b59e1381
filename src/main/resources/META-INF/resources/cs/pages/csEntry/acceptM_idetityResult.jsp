<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<div style="width: 100%">
	<div class="divfclass">
		<h1>
			<!-- <input id="showButtonId" class="main_fold"value="+" type="button"> -->
				<a href="#" id="showButtonId" value="+" onclick="showButton(this,'identityCheck')" > 
			<img src="${ctx}/cs/img/icon/three_plus.png" />身份验真信息</a>
		</h1>
	</div>
	<div class="panelPageFormContent" id="identityCheck">
		<dl>
			<dt>验真结果</dt>
			<dd>
				<input readonly="readonly" value="${identityCheckVO.serviceResult }"
					style="color: red; font-weight: bold; position: relative; left: 10px;" />
			</dd>
		</dl>
		<dl>
			<dt>验真时间</dt>
			<dd>
				<input readonly="readonly"
					style="color: red; font-weight: bold; position: relative; left: 10px;"
					value='<s:date name="identityCheckVO.checkDate" format="yyyy-MM-dd"/>' />
			</dd>
		</dl>
		<dl>
			<dt>注销原因</dt>
			<dd>
				<input readonly="readonly"
					style="color: red; font-weight: bold; position: relative; left: 10px;"
					value='${identityCheckVO.logoutReason }' />
			</dd>
		</dl>
		<!-- 		<br /> -->
		<!-- 		<table style="width: 100%; text-align: center;"> -->
		<!-- 			<tr> -->
		<!-- 				<td>验真结果</td> -->
		<!-- 				<td>验真时间</td> -->
		<!-- 				<td>注销原因</td> -->
		<!-- 			</tr> -->
		<!-- 		</table> -->
		<!-- 		<br /> -->
	</div>
</div>
