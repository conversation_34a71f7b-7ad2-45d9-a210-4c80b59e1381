<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-补退费信息 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<!-- 保全项目的补退费信息 -->
<div id="remarkDiv" class="panel collapse">
<h1 id="remarkH1">备注信息录入</h1>
	<div>
		<dl style="width: 500px" class="nowrap">
			<dt style="width: 80px">录入备注</dt>
			<dd style="width: 300px">
				<textarea rows="5" cols="50" id='summeryContent'></textarea>
			</dd>
		</dl>
		<dl style="width: 500px">
			<dt style="width: 80px">录入历史备注</dt>
			<dd style="width: 300px">
				<textarea rows="5" cols="70" readonly="readonly"  id='oldSummeryContent'>${csRemarkVO.entryRemark}</textarea>
			</dd>
		</dl>

	</div>
	<div>
	<div id="saveButton" class="button">
		<div class="buttonContent">
			<button type="button" onclick="saveSummery()">保存</button>
		</div>
	</div>	
	</div>
</div>

<script type="text/javascript">
function saveSummery() {
	var changeId = $("#changeId", navTab.getCurrentPanel()).val();
	//$("#tricalButtom", navTab.getCurrentPanel()).hide();
	var _acceptId = $("#acceptId").val();
	var _content = $("#summeryContent").val();
	if (_content == "") {
		alertMsg.info("请输入备注信息");
		return false;
	}
	if(500<_content.length*3){//数据库中该字段目前是400，需要修改为500后修改此校验
		alertMsg.info("备注信息长度不能超过500！");
		return false;
	} 
	var rootPath = getRootPath();
	alertMsg.confirm("保全项目无变化，请确认。", {
		okCall : function() {
			$.ajax({
				type : 'POST',
				url : rootPath
						+ '/cs/csAccept/saveSummery_PA_acceptOtherAction.action?changeId='
						+ changeId + '&acceptId=' + _acceptId + '&content='
						+encodeURI(encodeURI(_content)),
				success : function(data) {
					var json = jQuery.parseJSON(data);
					if (json.statusCode == 200) {
						$("#summeryContent").val("");
						$("#oldSummeryContent").val(json.message);
					}
					alertMsg.correct("保存成功!");
				},
				error : DWZ.ajaxError
			});
		},
		cancleCall : function() {
			return false;
		}
	});
}
</script>