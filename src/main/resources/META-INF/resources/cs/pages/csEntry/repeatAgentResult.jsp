<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"	type="text/css">

<form id="pagerForm" method="post" action="cs/csEntry/loadTelRepeatAgentTest_PA_csEntryAction.action">
    <input type="hidden" name="pageNum" value="${currentPageAgent.pageNo}" />
	<input type="hidden" name="numPerPage" value="${currentPageAgent.pageSize}" />
</form>

<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">客户与业务员电话重复列表
		</h1>
	</div>
	<form action="cs/csEntry/loadTelRepeatAgentTest_PA_csEntryAction.action" id="repeatAgentResultForm" method="post"
		class="pageForm required-validate" rel="pagerForm"
		onsubmit="return divSearch(this,'repeatAgentResultDiv')">
		
		<input type="hidden" name="customerPolicyAgentCompVO.changeId" value="${customerPolicyAgentCompVO.changeId}" />
		<input type="hidden" name="customerPolicyAgentCompVO.mobileTel" value="${customerPolicyAgentCompVO.mobileTel }"/>
		<input type="hidden" name="customerPolicyAgentCompVO.officeTel"value="${customerPolicyAgentCompVO.officeTel }" />
		<input type="hidden" name="customerPolicyAgentCompVO.callPhone1" id="callPhone1" value="${customerPolicyAgentCompVO.callPhone1 }"/>
		<input type="hidden" name="customerPolicyAgentCompVO.callPhone2" id="callPhone2" value="${customerPolicyAgentCompVO.callPhone2 }" />
		
	<div id="agentRepeat" class="tabdivclassbr">
		<table id="agentRepeatTable" class="list main_dbottom" width="100%">
			<thead>
				<tr>
					<th nowrap>序号</th>
					<th nowrap>电话号码</th>
					<th nowrap>业务员姓名</th>
					<th nowrap>业务员编码</th>
					<th nowrap>业务员所属分公司</th>
					<!-- <th nowrap>营业区</th>
					<th nowrap>营业部</th>
					<th nowrap>营业组</th> -->
				</tr>
			</thead>
			<tbody id="agentRepeatTbody">
				<s:if test="currentPageAgent.PageItems == null || currentPageAgent.PageItems.size()==0">
					<tr>
						<td colspan="20">
							<div class="noRueryResult">无符合条件数据！</div>
						</td>
					</tr>
				</s:if>
				<s:iterator value="currentPageAgent.pageItems" status="st">
					   <tr align="center">
						<td>${st.index+1 }</td>
						<td>${agentMobile }</td>
						<td>${agentName }</td>
						<td>${agentCode }</td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG" value="${agentOrganCode}"/></td>
						<%-- <td>${agentArea }</td>
						<td>${agentDepartment }</td>
						<td>${agentGroup }</td> --%>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value}, 'repeatAgentResultDiv')"
					value="currentPageAgent.pageSize">
				</s:select>
				<span>条，共${currentPageAgent.total}条</span>
			</div>
			<div class="pagination" targetType="navTab" rel="repeatAgentResultDiv"
				totalCount="${currentPageAgent.total}"
				numPerPage="${currentPageAgent.pageSize}" pageNumShown="10"
				currentPage="${currentPageAgent.pageNo}"></div>
		</div>
	</div>
	</form>