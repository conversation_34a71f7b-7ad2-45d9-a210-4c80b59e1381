<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">

		<form action="" class="required-validate"  onsubmit="" id="formBody" method="post">
			<!-- 隐藏域传值 -->
			<input type="hidden" name="acceptId" value="${acceptId}" id="bankAcceptId"></input> <input
				type="hidden" name="changeId" value="${changeId}" id="bankChangeId"></input> <input
				type="hidden" name="customerId" value="${customerId}"></input> <input
				type="hidden" name="customerName" value="${customerName}"></input> <input
				type="hidden" name="url" value="${url}"></input> <input
				type="hidden" name="title" value="${title}"></input> <input
				type="hidden" name="pageId" value="${pageId}"></input> <input
				type="hidden" name="jsMethod" value="${jsMethod}"></input> <input
				type="hidden" name="planId" value="${planId}"></input><input
				type="hidden" id="issueBankNames" name="issueBankNames" value=""></input>
			<input type="hidden" id="bankCodes" name="bankCodes" value=""></input>
			<input type="hidden" id="bankAccounts" name="bankAccounts" value=""></input>
			<input type="hidden" id="accoNames" name="accoNames" value=""></input>
			<input type="hidden" id="deleteAccountIds" name="deleteAccountIds"
				value=""></input><input type="hidden" name="activeId" id="activeId"
				value="${activeId}${customerId}"></input>
			<table class="list" width="100%">
				<thead>
					<tr>
						<th>选择</th>
						<th style="display: none">客户id</th>
						<th>客户姓名</th>
						<th>银行代码/开户银行</th>
						<th>银行账号</th>
						<th>户名</th>
						<th>账号是否经过验证</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="${activeId}${customerId}" class="t_body">
					<s:iterator value="csBankAccountVOList" status="st"
						var="csBankAccountVO">
						<tr>
							<td align="center">
								<s:if test="#st.count==1">
									<input type="radio" name="bankAccountId" checked = "checked"
										class="bankAccountId" value="${accountId}" />
								</s:if>
								<s:else>
									<input type="radio" name="bankAccountId" 
										class="bankAccountId" value="${accountId}" />
								</s:else>
									
								</td>
							<td style="display: none">${customerId}</td>
							<td align="center" id="qy_customerName">${customerName}</td>
							<td align="center">${bankCode}/<Field:codeValue
									tableName="APP___PAS__DBUSER.T_BANK" value="${bankCode}" /></td>
							<td align="center"><span type="expandBankAccount">${bankAccount }</span></td>
							<td align="center">${accoName }</td>
							<td align="center"><Field:codeValue
									tableName="APP___PAS__DBUSER.t_yes_no" value="${verifiedFlag}" /></td>
							<td align="center">
								<s:if test="#csBankAccountVO.operationType==1">
									<a class="btnDel" onclick="deleteAccountLine(this)"></a>
								</s:if></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</form>
