<!-- 保全复核-弹出页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>


<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_accept.js"></script>



<div style="float: left;" width="100%" >
	<%  String acceptId = request.getParameter("acceptId");%>
	<ul>
		<li>
			<!-- *****影像 *********begin*********************-->
			<div class="pageFormbut" width="50%" id="delayCauseDiv"
				align="center">

				<form id = "saveDelayCauseForm" action="${ctx}/cs/csAccept/saveDelayCause_PA_csEntryAction.action">
					<input type="hidden" id="acceptId" name="csAcceptChangeVO.acceptId"
						value="<%=acceptId%>" />
					<dl id='delayCauseDL'>
						<dd></dd>
						<dd>
							<Field:codeTable 
								name="csAcceptChangeVO.delayCause"
								tableName="APP___PAS__DBUSER.T_DELAY_CAUSE" />
						</dd>
						<dd></dd>
					</dl>
				</form>
			</div>
			<div class="formBar" align = "center">
				<button type="button" class="but_blue" onclick ="saveDelayCause()" >保存</button>
				<button type="button" class="but_blue close" value="关闭"" >退出</button>
			</div>
			
		</li>
	</ul>
</div>

<script type="text/javascript">
	function saveDelayCause(){
		$.post("${ctx}/cs/csAccept/saveDelayCause_PA_csEntryAction.action",
				$("#saveDelayCauseForm").serialize(),
				function(data){
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
				if (json.message && alertMsg)
					alertMsg.error(json.message);
				} else{
					//关闭这个窗口
					$.pdialog.closeCurrent();
					alertMsg.correct("保存成功！");	
				}
		})
	}
	
</script>
