<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"	type="text/css">

<form id="pagerForm" method="post" action="cs/csEntry/loadTelRepeatPolicyTest_PA_csEntryAction.action">
    <input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
	<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>

<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">客户电话重复列表
		</h1>
	</div>
	<form action="cs/csEntry/loadTelRepeatPolicyTest_PA_csEntryAction.action" id="repeatPolicyResultForm" method="post"
		class="pageForm required-validate" rel="pagerForm"
		onsubmit="return divSearch(this,'repeatPolicyResultDiv')">
		
		<input type="hidden" name="customerPolicyAgentCompVO.changeId" value="${customerPolicyAgentCompVO.changeId}" />
		<input type="hidden" name="customerPolicyAgentCompVO.mobileTel" id="mobileTel" value="${customerPolicyAgentCompVO.mobileTel }"/>
		<input type="hidden" name="customerPolicyAgentCompVO.offenUseTel" id="officeTel" value="${customerPolicyAgentCompVO.offenUseTel }" />
		<input type="hidden" name="customerPolicyAgentCompVO.callPhone1" id="callPhone1" value="${customerPolicyAgentCompVO.callPhone1 }"/>
		<input type="hidden" name="customerPolicyAgentCompVO.callPhone2" id="callPhone2" value="${customerPolicyAgentCompVO.callPhone2 }" />
	<div id="repeatPolicyTable" class="tabdivclassbr">
		<table id="repeatPolicyDiv" class="list main_dbottom" style="width: 100%;">
			<thead>
				<tr align="center">
					<th nowrap>序号</th>
					<th nowrap>电话号码</th>
					<th style="display:none;" nowrap>客户号</th>
					<th nowrap>客户姓名</th>
					<th nowrap>保单号</th>
					<th nowrap>保单管理机构</th>
					<th nowrap>保全受理号</th>
				</tr>
			</thead>
			<tbody id="">
				<s:if test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="20">
							<div class="noRueryResult">无符合条件数据！</div>
						</td>
					</tr>
				</s:if>
				<s:iterator value="currentPage.pageItems" status="st">
					<tr align="center">
					   <td>${st.index+1}</td>
						<td>${mobileTel }</td>
						<td style="display:none;">${customerId }</td>
						<td>${customerName }</td>
						<td>${policyCode }</td>
						<td>${organCode}</td>
						<td>${acceptCode}</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value}, 'repeatPolicyResultDiv')"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab" rel="repeatPolicyResultDiv"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
	</form>