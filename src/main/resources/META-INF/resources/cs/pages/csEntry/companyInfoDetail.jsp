<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/ChinesePostcode.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<%-- <script type="text/javascript" src="${ctx}/cs/js/check_name.js"></script>
 --%>
 
 <script type="text/javascript" src="${ctx}/cs/pages/common/js/tax.js"></script>
 
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="backgroundCollor"  style="background:#f5f6f8" layoutH="8">
	<form action="${ctx }/cs/csPayCompany/addCsPayCompany_PA_csPayCompanyAction.action" id="companyInfo_add1" onsubmit="return validateCallback(this);" method="post">
		<input id="acceptId1" type="hidden"  name="acceptId" value="${acceptId}" />
		<input id="acceptCodePayeeCompany" type="hidden"  name="acceptCode" value="${acceptCode}" />
		<input id="changeId1"  type="hidden"  name="changeId" value="${changeId}" />
		<input id="busiItemId1" type="hidden" name="busiItemId" value="${busiItemId}" />
		<input id="policyId1" type="hidden" name="policyId" value="${beneCustomerVO.policyId}" />
		<input id="ypolicyChgId" type="hidden" name=policyChgId value="${beneCustomerVO.policyChgId}" /> 
		<input id="companyId1"  type="hidden"  name="companyId" value="${csPayCompanyVO.companyOrgCode}">
		<input id="insuredId1" type="hidden" name="insuredId" value="${csPayCompanyBeneVO.beneName}" />
		<input type="hidden" name="activeId" id="activeId" value="${activeId}${csPayCompanyVO.companyOrgCode}"></input>
		<input type="hidden" id="beneCertiCQFlag"  name="beneCertiCQFlag" value="${beneCertiCQFlag }">
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png" />单位信息
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl>
						<dt >客户名称<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.companyName" class="" 
								value="${csPayCompanyVO.companyName}" id="companyName" />
						</dd>
					</dl>
					<dl>
						<dt>注册地址<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.companyAddress" class=""
								value="${csPayCompanyVO.companyAddress}" id="companyAddress" />
						</dd>
					</dl>
					<dl>
						<dt >所属行业<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.companyIndustry" class=""
								value="${csPayCompanyVO.companyIndustry}" id="companyIndustry" />
						</dd>
					</dl>
					<dl>
						<dt >注册资本<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.regCapital" class=""
								value="${csPayCompanyVO.regCapital}" id="regCapital" />
						</dd>
					</dl>
					<dl>
						<dt >经营范围<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.businessCope" class=""
								value="${csPayCompanyVO.businessCope}" id="businessCope" />
						</dd>
					</dl>
					<dl>
						<dt >注册资本金币种<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.regCapitalCurrency" class="" readonly="readonly"
								value="RMB" id="regCapitalCurrency" />
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png" />组织机构信息
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl>
						<dt >社会统一信用代码/组织机构代码</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.companyOrgCode" class=""
								value="${csPayCompanyVO.companyOrgCode}"
								 id="companyOrgCode" />
						</dd>
					</dl>
					<dl>
						<dt>
							证件有效起期
						</dt>
						<dd >
							<input name="csPayCompanyVO.orgCodeStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.orgCodeStartDate" format="yyyy-MM-dd"/>" id="orgCodeStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>


					<dl>
						<dt>
							证件有效止期
						</dt>
						<dd >
							<input name="csPayCompanyVO.orgCodeEndDate"  style="width: 146px;" value="<s:date name="csPayCompanyVO.orgCodeEndDate" format="yyyy-MM-dd"/>" class="date" type="expandDateYMD" id="orgCodeEndDate" />
						</dd>
					</dl>
					
					<dl>
						<dt  >
							营业执照号码
						</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.busiLicenceCode"  class="" value="${csPayCompanyVO.busiLicenceCode}" id="busiLicenceCode"  />
						</dd>
					</dl>
					
					<dl>
						<dt>
							证件有效起期
						</dt>
						<dd >
							<input name="csPayCompanyVO.licenceStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.licenceStartDate" format="yyyy-MM-dd"/>" id="licenceStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>
					<dl>
						<dt>
							证件有效止期
						</dt>
						<dd >
							<input name="csPayCompanyVO.licenceEndDate"   style="width: 146px;" value="<s:date name="csPayCompanyVO.licenceEndDate" format="yyyy-MM-dd"/>" class="date" type="expandDateYMD" id="licenceEndDate" />
						</dd>
					</dl>
					<dl>
						<dt>
							社会保障号
						</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.socialSecurityCode"  class="" value="${csPayCompanyVO.socialSecurityCode}" id="socialSecurityCode"  />
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="${ctx}/cs/img/icon/tubiao.png" />税务登记信息
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl>
						<dt>
							税务登记证
						</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.taxCode"  class="" value="${csPayCompanyVO.taxCode}" id="taxCode"  />
						</dd>
					</dl>

					<dl>
						<dt>
							证件有效起期
						</dt>
						<dd >
							<input name="csPayCompanyVO.taxCodeStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.taxCodeStartDate" format="yyyy-MM-dd"/>" id="taxCodeStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>


					<dl>
						<dt>
							证件有效止期
						</dt>
						<dd >
							<input name="csPayCompanyVO.taxCodeEndDate"  style="width: 146px;" value="<s:date name="csPayCompanyVO.taxCodeEndDate" format="yyyy-MM-dd"/>" class="date" type="expandDateYMD" id="taxCodeEndDate" />
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">受益所有人信息</img>
					</h1>
				</div>
				<div class="panelPageFormContent">
					<table class="list"  id="csPayCompanyBeneListTable" width="90%" style="margin-left: 57px;">
						<thead>
							<tr >
								<th colName='beneName' inputType="input">受益所有人姓名</th>
								<th colName='beneCertiType' inputType="select">受益所有人证件类型</th>
								<th colName='beneCertiCode' inputType="input">受益所有人证件号码</th>							
								<th colName='beneCertiStartDate' inputType="input">证件有效期起期</th>
								<th colName='beneCertiEndDate' inputType="input">证件有效期止期</th>
								<th >删除</th>
							</tr>
						</thead>
						<tbody id="csPayCompanyBeneListBody" class="t_body">
							<s:iterator value="csPayCompanyBeneList" status="st" var="csPayCompanyBeneVO">
								<tr tr_saveStatus="1" align="center" id="${logId}" >
									<td><input type="text"  name="csPayCompanyBeneVO.beneName"  class="" value="${beneName}" id="beneName"  /></td>
									<td><Field:codeTable name="csPayCompanyBeneVO.beneCertiType" value="${beneCertiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" cssClass="combox"
										nullOption="true" id="beneCertiType"  onChange="_checkShowUrgentDiv(this)" />
										<input id="beneCertiTypeInput" type="hidden" value="${beneCertiType}"/></td>
									<td><input type="text"  name="csPayCompanyBeneVO.beneCertiCode"  class="" value="${beneCertiCode}" id="beneCertiCode"  /></td>
									<td><input name="csPayCompanyBeneVO.beneCertiStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="beneCertiStartDate" format="yyyy-MM-dd"/>" id="beneCertiStartDate" type="expandDateYMD" class="date" /></td>
									<td><input name="csPayCompanyBeneVO.beneCertiEndDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="beneCertiEndDate" format="yyyy-MM-dd"/>" id="beneCertiEndDate" type="expandDateYMD" class="date" />
										<input type="checkbox" onclick="endDateClear(this)" id="beneCerti" value="" />长期</td>
									<td><a class="btnDel" href="#" onclick="delBeneInfo(this)">删除</a></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				<div style="margin-top: 8px; float: left; margin-left: 15px">
					<button type="button" class="" onclick="_addCsPayCompanyBene(this)" >+添加受益人所有信息</button>
				</div>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">控股股东或实际控股人信息</img>
					</h1>
				</div>
				<div class="panelPageFormContent">	
					<dl>
						<dt style="width: 150px;">
							控股股东或实际控制人姓名<font color="red">*</font>
						</dt>
						<dd>
							<input type="text"  name="csPayCompanyVO.holdingPersonName"  class="" value="${csPayCompanyVO.holdingPersonName}" id="holdingPersonName"  />
						</dd>
					</dl>
					<dl>
						<dt style="width: 170px;" >
							控股股东或实际控制人证件类型<font color="red">*</font>
						</dt>
						<dd>
							<Field:codeTable name="csPayCompanyVO.companyHolderCertiType" value="${csPayCompanyVO.companyHolderCertiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" 
											 nullOption="true" 	id="companyHolderCertiType"  />
						</dd>
					</dl>
					<dl>
						<dt style="width: 150px;">
							控股股东或控制人证件号码<font color="red">*</font>
						</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.companyHolderCertiCode"  class="" value="${csPayCompanyVO.companyHolderCertiCode}" id="companyHolderCertiCode" 
							/>
						</dd>
					</dl>

					<dl>
						<dt>
							证件有效起期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.holderCertiStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.holderCertiStartDate" format="yyyy-MM-dd"/>" id="holderCertiStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>


					<dl>
						<dt>
							证件有效止期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.holderCertiEndDate"  myOption="endDateComputer(this)" value="<s:date name="csPayCompanyVO.holderCertiEndDate" format="yyyy-MM-dd"/>" id="holderCertiEndDate" type="expandDateYMD" class="date"/>
							<input type="checkbox" onclick="endDateClear(this)" id="holderCerti" value="" />长期
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">法定代表人信息</img>
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl>
						<dt>
							法定代表人姓名<font color="red">*</font>
						</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.legalPersonName" class="" value="${csPayCompanyVO.legalPersonName}" id="legalPersonName"  />
						</dd>
					</dl>
					<dl  >
						<dt>法定代表人证件类型<font color="red">*</font></dt>
						<dd>
							<Field:codeTable name="csPayCompanyVO.legalPersonCertiType" value="${csPayCompanyVO.legalPersonCertiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY"
											nullOption="true" id="legalPersonCertiType"  />
						</dd>
					</dl>

					<dl>
						<dt>法定代表人证件号码<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.legalPersonCertiCode"  class="" value="${csPayCompanyVO.legalPersonCertiCode}" id="legalPersonCertiCode"  />
						</dd>
					</dl>


					<dl>
						<dt>
							证件有效起期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.legalPerCertiStartDate"  myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.legalPerCertiStartDate" format="yyyy-MM-dd"/>" id="legalPerCertiStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>


					<dl>
						<dt>
							证件有效止期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.legalPerCertiEndDate"   style="width: 146px;" value="<s:date name="csPayCompanyVO.legalPerCertiEndDate" format="yyyy-MM-dd"/>" class="date" type="expandDateYMD" id="legalPerCertiEndDate" />
							<input type="checkbox" onclick="endDateClear(this)" id="legalPerCerti" value="" />长期
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">负责人信息</img>
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl  >
						<dt>负责人姓名<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.managerName" class="" value="${csPayCompanyVO.managerName}" id="managerName"  />
						</dd>
					</dl>
					<dl  >
						<dt>负责人证件类型<font color="red">*</font></dt>
						<dd>
							<Field:codeTable name="csPayCompanyVO.managerCertiType" value="${csPayCompanyVO.managerCertiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY"
											 nullOption="true" id="managerCertiType"  />
						</dd>
					</dl>
					<dl  >
						<dt>负责人证件号码<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.managerCertiCode"  class="" value="${csPayCompanyVO.managerCertiCode}" id="managerCertiCode"  />
						</dd>
					</dl>
					<dl>
						<dt>
							证件有效起期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.managerCertiStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.managerCertiStartDate" format="yyyy-MM-dd"/>" id="managerCertiStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>
					<dl>
						<dt>
							证件有效止期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.managerCertiEndDate" myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.managerCertiEndDate" format="yyyy-MM-dd"/>" class="date" type="expandDateYMD" id="managerCertiEndDate" />
							<input type="checkbox" onclick="endDateClear(this)" id="managerCerti" value="" />长期
							
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">授权办理业务员信息</img>
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl  >
						<dt>授权办理业务人员姓名<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.operatorName"  class="" value="${csPayCompanyVO.operatorName}" id="operatorName"  />
						</dd>
					</dl>
					<dl  >
						<dt>授权办理业务人员证件类型<font color="red">*</font></dt>
						<dd>
							<Field:codeTable name="csPayCompanyVO.operatorCertiType" value="${csPayCompanyVO.operatorCertiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY"
											 nullOption="true" id="operatorCertiType"  />
						</dd>
					</dl>
					<dl  >
						<dt>授权办理业务人员证件号码<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.operatorCertiCode"  class="" value="${csPayCompanyVO.operatorCertiCode}" id="operatorCertiCode" />
						</dd>
					</dl>
					<dl>
						<dt>
							证件有效起期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.operatorCertiStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.operatorCertiStartDate" format="yyyy-MM-dd"/>" id="operatorCertiStartDate" type="expandDateYMD" class="date" />
						</dd>
					</dl>
					<dl>
						<dt>
							证件有效止期<font color="red">*</font>
						</dt>
						<dd >
							<input name="csPayCompanyVO.operatorCertiEndDate" myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="csPayCompanyVO.operatorCertiEndDate" format="yyyy-MM-dd"/>" class="date" type="expandDateYMD" id="operatorCertiEndDate" />
							<input type="checkbox" onclick="endDateClear(this)" id="operatorCerti" value="" />长期
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">判定受益人信息</img>
					</h1>
				</div>
				<div class="panelPageFormContent">
					<dl  >
						<dt>判定受益人所有人方式<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.beneJudgeWay"  class="" value="${csPayCompanyVO.beneJudgeWay}" id="beneJudgeWay"  />
						</dd>
					</dl>
					<dl  >
						<dt>持股比例或表决权占比<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.holdeRatio"  class="" value="${csPayCompanyVO.holdeRatio}" id="holdeRatio"  />
						</dd>
					</dl>
				</div>
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">通讯单位地址</img>
					</h1>
				</div>
				<div class="panelPageFormContent">	
					<dl  >
						<dt>国家<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.country"   class="" value="中国" id="country"  readonly="readonly"/>
						</dd>
					</dl>
					<dl  >
						<dt>省/直辖市<font color="red">*</font></dt>
						<dd>
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="csPayCompanyVO.state"
								ref="city_panelInfoAdd"  initval=" "
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}"
								>
							</s:select>
						</dd>
					</dl>
					<dl  >
						<dt>市<font color="red">*</font></dt>
						<dd>
							<select id="city_panelInfoAdd" name="csPayCompanyVO.city" ref="district_panelInfoAdd" 
							initval="${csPayCompanyVO.district }"
							refUrl="${ctx }/cs/common/getDistrict_PA_distictUtilAction.action?cityCode={value}" class="combox">
							<s:if test="csPayCompanyVO.city != null && csPayCompanyVO.city != '' ">
								<option value="${csPayCompanyVO.city }">
									<Field:codeValue tableName="APP___PAS__DBUSER.T_DISTRICT" value="${csPayCompanyVO.city }" ></Field:codeValue>
								</option>
							</s:if>
							<s:else>
								<option value=""> 全部</option>
							</s:else> 
					</select>
						</dd>
					</dl>
					<dl  >
						<dt>区/县<font color="red">*</font></dt>
						<dd>
							<select id="district_panelInfoAdd"  name="csPayCompanyVO.district" class="combox" >
								<s:if test="csPayCompanyVO.district != null && csPayCompanyVO.district != '' ">
									<option value="${csPayCompanyVO.district }">
										<Field:codeValue tableName="APP___PAS__DBUSER.T_DISTRICT" value="${csPayCompanyVO.district }" ></Field:codeValue>
									</option>
								</s:if>
								<s:else>
									<option value=""> 全部</option>
								</s:else> 
							</select>
						</dd>
					</dl>
					<dl  >
						<dt>详细地址<font color="red">*</font></dt>
						<dd>
							<input type="text" name="csPayCompanyVO.address"  class="" value="${csPayCompanyVO.address}" id="address"  />
						</dd>
					</dl>
					<dl  >
						<dt>手机号</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.mobile"  class="" value="${csPayCompanyVO.mobile}" id="mobile"  />
						</dd>
					</dl>		
					<dl  >
						<dt>邮政编码</dt>
						<dd>
							<input type="text" name="csPayCompanyVO.postCode"  class="" value="${csPayCompanyVO.postCode}" id="postCode"  />
						</dd>
					</dl>	
					<dl  >
						<dt>备注</dt>
						<dd>
							<textarea rows="3" name="csPayCompanyVO.remark" class="" value="" id="remark"  >${csPayCompanyVO.remark}</textarea>
						</dd>
					</dl>						
				</div>
			
			
			 <table style="width: 100%">
				<tbody>
					<tr>
						<td></td>
						<td style="width: 100px">
							<div class="pageFormbut" >
								 <!-- <button type="button" class="but_blue" onclick='csCompanyInfoSubmit("companyInfo_add1");'>保存</button>  -->
								 <!-- href="javascript:$.bringBack({id:'1', orgName:'${ correspondentName}', orgNum:'${ correspondentNo}'})"  -->
								 <button type="button" onclick='csCompanyInfoSubmit(this);' id="csCompanyInfoSubmitID" class="but_blue">保存</button>
							</div>
						</td>
						<td></td>
					</tr>
				</tbody>
			</table> 
	</form>
</div>
<script type="text/javascript">
$(function (){
	/* var type = $("#type1").val();
	if(type=='query'){
		$("#editId",$.pdialog.getCurrent()).hide();
		$("#deleteId",$.pdialog.getCurrent()).hide();
		$("#insertId",$.pdialog.getCurrent()).hide();
		//改input的 type 的类型为 readOnly
		var $checkIds =$("#formDiv",$.pdialog.getCurrent()).find('input','tpye:text');
		var $select =$("#formDiv",$.pdialog.getCurrent()).find('select');
			  for(var i=0;i<$checkIds.length; i++){
				$($checkIds[i],$.pdialog.getCurrent()).attr('readonly','readonly');
		  }
			  for(var i=0;i<$select.length; i++){
				 $($select[i],$.pdialog.getCurrent()).attr('disabled','disabled'); 
			  }
	}else if(type=='add'){
		$("#editId",$.pdialog.getCurrent()).hide();
		$("#deleteId",$.pdialog.getCurrent()).hide();
	}else if(type=='delete'){
		$("#editId",$.pdialog.getCurrent()).hide();
		$("#insertId",$.pdialog.getCurrent()).hide();
	}else if(type=='update'){
		$("#insertId",$.pdialog.getCurrent()).hide();
		$("#deleteId",$.pdialog.getCurrent()).hide();
	}
	$("[id='busiPrdId']").attr("disabled",true);
	var certiEndDate = $("#certiEndDate", $.pdialog.getCurrent()).val();//长期是否
	if(certiEndDate == '9999-12-31'){
		$("#changQiFlag").val("1");
		$("#certiEndDate").val("");
		$("#certiEndDate").attr( 'disabled', true);
		$("#longDate").attr("checked","checked");
	} */
});   


	/** 针对客户名称的校验  **/
	function checkCompanyCustomerNameMethod(companyCustomerName) {
		
		if(companyCustomerName == null || companyCustomerName=='' || companyCustomerName==""){
			alertMsg.warn("必录项未录入，请重新录入");
			return false;
		}
		// 判断"."是否是"Co."或"Ltd."的一部分
		if (companyCustomerName.indexOf(".") > 0 && !(companyCustomerName.indexOf("Co.") > 0 || companyCustomerName.indexOf("Ltd.") > 0)) {  
	    	alertMsg.error("不能单独使用“.”必须是“Co.”,“Ltd.”。");
			return false; 
	    }  
		
		 var pattern1 = /^[\u4e00-\u9fa5a-zA-Z0-9•.★\- \[\]<>（）《》#【】]+$/; // 正则表达式, /^[\u4e00-\u9fa5]+$/
		if (!pattern1.test(companyCustomerName)) {
		    alertMsg.error("仅能含有汉字、字母、数字、•、.、★、-、空格、【】、[] 、<>、（）、《》、#");
			return false;
		 }
		 
		var flag='';
		var flag2=false;
		var flg1=false;
		var pat1 = new RegExp("[《》]");
		for(var i = 0 ; i < companyCustomerName.length; i++){
			var a = companyCustomerName.charAt(i);
			if(pat1.test(a)){
				flg1=true;
				if(flag2==true){
					flag2=false;
				}
				if(a=='《'){
					 flag='5';
				 }
				 if(a=='》' && flag=='5'){
					 flag2=true;
				 }
				 if(flag2==true){
					 flag='00';
				 }
			}
		}
		if(flg1==true&&flag2!=true){
			alertMsg.error("\“《\》”必须成对使用!");
			return false;
		}
		
	}


		function customerNameMethod(cusName) {
			if(cusName == ''){
				alertMsg.warn("必录项未录入，请重新录入");
				return false;
			}
			if(cusName!=null && cusName!=""){
				var cusNameTrim = cusName.replace(/[ ]/g,"");//中部去空
				//需要包含•字符,汉字校验
				var patternCHN = /[\u4E00-\u9FA5\uF900-\uFA2D•]/;
				//英文、数字校验
				var patternEN = /^[0-9a-zA_Z]+$/;
				//纯数字校验
				var patternNO = /^\d*$/;
				if(patternCHN.test(cusNameTrim)){
					if(cusNameTrim.length < 2){
						$("#checkname").val("");
						$("#holderName").val("");
						alertMsg.warn("不小于2个字符");
						return false;
					}
				}else if(patternNO.test(cusNameTrim)){//纯数字
						$("#checkname").val("");
						$("#holderName").val("");
						alertMsg.warn("姓名不能全部为数字");
						return false;
						
				}else if(patternEN.test(cusNameTrim)){//英文
					if(cusNameTrim.length < 3){
						$("#checkname").val("");
						$("#holderName").val("");
						alertMsg.warn("全为字母的，不能低于3个字符。");
						return false;
					}
				}else{//其他
					if(cusNameTrim.length < 2){
						$("#checkname").val("");
						$("#holderName").val("");
						alertMsg.warn("不小于2个字符");
						return false;
					}
				}
			}
			if(!check_name_qy1(cusName)){
				return false;
			}
		}	
	 //Redmine:48607 新增   end
function check_name_qy1(name){
	if(name == "" || name == null){
		return false;
	}
    var pattern1 = /^[\u4e00-\u9fa5a-zA-Z0-9•.★\- \[\]<>（）《》#【】]+$/; // 正则表达式, /^[\u4e00-\u9fa5]+$/
    if (!pattern1.test(name)) {
    	alertMsg.error("仅能含有汉字、字母、数字、•、.、★、-、空格、【】、[] 、<>、（）、《》、#");
		return false;
    }
    var _regex = /^-|-$/; // 定义正则表达式，^为开始位置，$为结束位置
    
    if (_regex.test(name)) {
    	alertMsg.error("“-”不能在字首或字尾。");
		return false; // "-"存在于字符串的开头或结尾时返回false
    }
    var _regex1 =  /^\s|\s$/; // 定义正则表达式，^为开始位置，$为结束位置
    
    if (_regex1.test(name)) {
    	alertMsg.error("空格不能在字首或字尾。");
		return false; // 空格存在于字符串的开头或结尾时返回false
    }
 	// 判断"."是否是"Co."或"Ltd."的一部分  
    if (name.indexOf(".") > 0 && !(name.indexOf("Co.") > 0 || name.indexOf("Ltd.") > 0)) {
    	alertMsg.error("不能单独使用“.”必须是“Co.”,“Ltd.”。");
		return false; // 
    }  
	var regex = /^[0-9]*$/;
	if(regex.test(name)){
		alertMsg.error("名字不能全为数字,请重新输入。");
		return false;
	}
	var pat = new RegExp("[<>\\[\\]【】（）]");
	var pat1 = new RegExp("[《》]");
	var len = 0;
	
	var flag='';
	var flag1=false;
	var flag2=false;
	var flg=false;
	var flg1=false;
	debugger;
	for(var i = 0 ; i < name.length; i++){
		var a = name.charAt(i);
		if(pat.test(a)){
			flg=true;
			if(flag1==true){
				flag1=false;
			}
			 if(a=='（'){
				 flag='1';
			 }
			 if(a=='）' && flag=='1'){
				 flag1=true;
			 }
			 if(a=='<'){
				 flag='2';
			 }
			 if(a=='>' && flag=='2'){
				 flag1=true;
			 }
			 if(a=='['){
				 flag='3';
			 }
			 if(a==']' && flag=='3'){
				 flag1=true;
			 }
			 if(a=='【'){
				 flag='4';
			 }
			 if(a=='】' && flag=='4'){
				 flag1=true;
			 }
			 if(flag1==true){
				 flag='00';
			 }
		 }
		if(pat1.test(a)){
			flg1=true;
			if(flag2==true){
				flag2=false;
			}
			if(a=='《'){
				 flag='5';
			 }
			 if(a=='》' && flag=='5'){
				 flag2=true;
			 }
			 if(flag2==true){
				 flag='00';
			 }
		}
	}

	if(flg==true&&flag1!=true){
		alertMsg.error("\“（\）”、\“<\>”、\“[\]”、\“【\】””必须成对使用。");
		return;
	}
	if(flg1==true&&flag2!=true){
		alertMsg.error("\“《\》”必须成对使用!");
		return;
	}
	
	//成对使用的标签校验通过之后，需要校验成对的标签中必须包含2个以上的字符
	if( (flg==true && flag1==true) || (flg1==true && flag2==true) ){
		//空数组，用来存储所有标签的开始和结束的下标数
		var arrStartParent = [];//()的开始
		var arrEndParent = [];//()的结束

		var arrStartAngle = [];//<>的开始
		var arrEndAngle = [];//<>的结束
		
		var arrStartSquare = [];//[]的开始
		var arrEndSquare = [];//[]的结束
		
		var arrStartSolid = [];//【】的开始
		var arrEndSolid = [];//【】的结束
		//用来判断闭合标签中的字符长度是否小于2
		var labelFlagParent = false;
		var labelFlagAngle = false;
		var labelFlagSquare = false;
		var labelFlagSolid = false;
		
		
		//循环拿到每个标签的开始位置和结束位置的下标
		for(var i=0;i<name.length;i++){
			var a = name.charAt(i);
			if(pat.test(a)){
				 if(a=='（'){
					 arrStartParent.push(i);
				 }else if(a=='）'){
					 arrEndParent.push(i);
				 }else if( a=='<' ){
					 arrStartAngle.push(i);
				 }else if(a=='>'){
					 arrEndAngle.push(i);
				 }else if(a=='['){
					 arrStartSquare.push(i);
				 }else if(a==']'){
					 arrEndSquare.push(i);
				 }else if(a=='【'){
					 arrStartSolid.push(i);
				 }else if(a=='】'){
					 arrEndSolid.push(i);
				 }
			 }
		}
		
		//先判断数组中的长度是否为0，如果都为0直接变为true
		//()的判断
		if(arrStartParent.length == arrEndParent.length){
			for(var i=0;i<arrStartParent.length;i++){
				var differ = arrEndParent[i] - arrStartParent[i]; 
				if(differ < 3){
					labelFlagParent = true;
					break;
				}
			}
		}
		
		//<>的判断
		if(arrStartAngle.length == arrEndAngle.length){
			for(var i=0;i<arrStartAngle.length;i++){
				var differ = arrEndAngle[i] - arrStartAngle[i]; 
				if(differ < 3){
					labelFlagAngle = true;
					break;
				}
			}
		}
		
		//[]的判断
		if(arrStartSquare.length == arrEndSquare.length){
			for(var i =0;i<arrStartSquare.length;i++){
				var differ = arrEndSquare[i] - arrStartSquare[i]; 
				if(differ < 3){
					labelFlagSquare = true;
				}
			}
		}
		
		//【】的判断
		if(arrStartSolid.length == arrEndSolid.length){
			for(var i=0;i<arrStartSolid.length;i++){
				var differ = arrEndSolid[i] - arrStartSolid[i]; 
				if(differ < 3){
					labelFlagSolid = true;
				}
			}
		}
		
		//判断标签的开始位置下标和结束位置的下标差值是否有不大于2的，如果进行阻断提示
		if(labelFlagParent || labelFlagSquare || labelFlagSolid || labelFlagAngle){
			alertMsg.error("\“（\）”、\“<\>”、\“[\]”、\“【\】””中的字符不小于2个。");
			return;
		}
		
	}
	

	
	//校验长度
	var valueLength = 0;
	var result = true;
    for(var i=0;i<name.length;i++){
    	   /*if(!/[a-zA-Z]/.test(name.charAt(0)) && !/[\u4e00-\u9fa5]/.test(name.charAt(0)) ){
    		    alertMsg.error('姓名不符合录入规则，请重新输入！');
				obj.value = "";
				return false;
    	   }*/
    	   //非阻断提示
		   if(name.charAt(i) == "·" || name.charAt(i) == "•"){
			if(name.charCodeAt(i).toString(16) != 'b7'){
				result = false;
			}
		  }
		  if(/[\u4e00-\u9fa5]/.test(name.charAt(i))){
			  valueLength += 2;
		  }else{
			  valueLength += 1;
		  }
	}
	if(valueLength < 2){	
		    alertMsg.error('姓名长度小于2个字符!');
			return false;
	}
	return true;
}
   
    
    function endDateClear(obj){
    	var id = obj.id + "CQFlag";
    	var endDate = obj.id + "EndDate";
    	if(id == 'beneCertiCQFlag'){
    		var parentTr=$(obj).parent().parent()
    		if(obj.checked){
    			$("#" + id).val("1");
    			$("#" + endDate,parentTr).attr( 'disabled', true); 
        		$("#" + endDate,parentTr).val("9999-12-31");
    		}else{
        		$("#" + id).val("0");
        		$("#" + endDate,parentTr).attr( 'disabled', false);
        		$("#" + endDate,parentTr).val("");
        	}
    	}else{
	    	if(obj.checked){
	    		$("#" + id).val("1");
	    		$("#" + endDate).attr( 'disabled', true);
	    		$("#" + endDate).val("9999-12-31");
	    	}else{
	    		$("#" + id).val("0");
	    		$("#" + endDate).attr( 'disabled', false);
	    		$("#" + endDate).val("");
	    	}
    	}
    }
    //计算证件止期
    function endDateComputer(obj){
    	if($("#holderCertiType").val() == "h"){//港澳台居民居住证
    		/* 1:计算证件有效止期  2:不能勾选长期*/
    		var newDate, sDate, eDate, iDays
            //js月份默认是从0开始的所以月份要-1
            newDate = $("#beneCertiStartDt").val().split("-");
            var date  = new Date(newDate[0], newDate[1]-1, newDate[2]);
        	date.setFullYear(date.getFullYear() + 5);
        	var year = date.getFullYear();
        	var month = date.getMonth()+1, month = month < 10 ? '0' + month : month;
        	var day = date.getDate(), day =day < 10 ? '0' + day : day;
        	$("input[name='beneCustomerVO.custCertEndDate']").val(year + '-' + month + '-' + day);
        	$("input[id='longDate']").click(function(){ 
        		this.attr( 'disabled', false);
            }); 
        	/* <input type="checkbox" onclick="endDateClear()" id="longDate" value="" /> */
    	}else if($("#holderCertiType").val() == "0"){
    		var certiEndDateObj = $("#certiEndDate", $.pdialog.getCurrent());//证件的有效期起
    		var certiStartDateObj = $("#beneCertiStartDt", $.pdialog.getCurrent());//证件的有效期起
    		var longDateObj = $("#longDate", $.pdialog.getCurrent());//长期
    		var changQiFlagObj = $("#changQiFlag", $.pdialog.getCurrent());//长期是否
    		var birthDateObj = $("#beneBirthDate", $.pdialog.getCurrent());//出生日期
    		getCertiEndDate(certiStartDateObj,birthDateObj,longDateObj,changQiFlagObj,certiEndDateObj);
    	}
    }
    
    function changeVal(obj){
    	if($(obj).is(":checked")){
    		$(obj).val(1);
    	}else{
    		$(obj).val(0);
    	}
    }
    //是否法定添加
    function changeVal_fading(obj){
    	var val = $(obj).val();
    	if(val == '0'){
    		$(obj).val(1);
    	}else{
    		$(obj).val(0);
    	}
    }
    
    function checkCustomerSexAndBirthday(){
		var holderCertiType=$("#holderCertiType",$.pdialog.getCurrent()).val();
		if(holderCertiType == '0'){
			var policyHoldercertiCode=$("#certiCode",$.pdialog.getCurrent()).val();
			var birthday = "00000000";
			var sex = "0";
			if(policyHoldercertiCode.length == 18){
				birthday = policyHoldercertiCode.substring(6,14);
			    sex = policyHoldercertiCode.substring(16,17);
			}else{
				birthday = policyHoldercertiCode.substring(6,14);
			    sex = policyHoldercertiCode.substring(14,15);
			}
			birthday = birthday.substring(0,4)+"-"+birthday.substring(4,6)+"-"+birthday.substring(6,8);
			if((sex-0)%2==0){
				sex = "2";
			}else{
				sex = "1";
			} 
			//holderGender性别  birthDate 出生日期
			selectMyComBox("holderGender", sex);
			//$("#birthDate",$.pdialog.getCurrent()).val(birthday);
			$("input[name='beneCustomerVO.customerBirthday']").val(birthday);
		}
	}
    /**
	 * 验证证件号码里面是否有日期格式
	 */
	function dateValidation(obj){
		//证件类型
		//var carTypeValue = $("#customerCertType").val();
		var carTypeValue=$("#holderCertiType",$.pdialog.getCurrent()).val();
		//用户写的证件号码 
		var carNumber = obj.value;
		//非空
		if(carNumber != null && carNumber != ""){
			if(carTypeValue === '0' ){//身份证
				//身份证是否有校
				var a = /^(\d{6})(18|19|20)?(\d{2})([01]\d)([0123]\d)(\d{3})(\d|X|x)?$/; 
				if(!a.exec(carNumber)){
					alertMsg.error("身份证号错误，请重新输入！");
					$("input[name='beneCustomerVO.customerBirthday']").val("");
					selectMyComBox("holderGender", "");
					return null;
				}
				//日期验证
				var regular = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
				var birthday = carNumber.substr(6,4)+"-"+carNumber.substr(10,2)+"-"+carNumber.substr(12,2);
				/**
				 * 因为身份证的号码是有一定的规则的所以根据规则来取得日期。
				 * */
				 if(regular.exec(birthday)){
					//给出生日期赋值
						//$("input[name='beneCustomerVO.customerBirthday']").val(birthday);
				 }else{
					 $("input[name='beneCustomerVO.customerBirthday']").val("");
					 selectMyComBox("holderGender", "");
					 alertMsg.error("身份证号里面的日期错误，请重输入"); 
				 }
			}
		}
	}
    
    //保存时校验身份证号的性别和所选性别(需求变更环境,uat注释-2018-04-24)
    function checkSexByCodeForbcsave(){
    	//alert("保存校验开始...");
		var certiType=$("#holderCertiType",$.pdialog.getCurrent()).val();//证件类型
		var gender=$("#holderGender",$.pdialog.getCurrent()).val();//证件类型
		var certiCode=$("#certiCode",$.pdialog.getCurrent()).val();//正件号码
		if(certiType == '0'){
			var sex = "0";
			if(certiCode.length == 18){
			    sex = certiCode.substring(16,17);
			}else{
			    sex = certiCode.substring(14,15);
			}
			if((sex-0)%2==0){
				sex = "2";
			}else{
				sex = "1";
			} 
			//alert("holderGender--"+gender);
			//alert("sex--"+sex);
			if(gender == sex){
				return true;
			}else{
				return false;
			}
		}
		return true;
	}
    
    //保存身份证规则
    function checkIDcertiCode(certiType,certiCode){
    	var regX = /(^\d{17}(\d|X|x)$)/;
    	var reg = /^[0-9]+$/;
    	var pattern = /^[\u4e00-\u9fa5\w-]+$/;
    	var pattern1 = /^[a-zA-Z0-9]+$/;
    	if(certiType == '0'){// 身份证
	 		if(certiCode.length < 18){
				alertMsg.warn("身份证号应为18位，请重新录入。");  
			      	return  false;  
			} 
			if(regX.test(certiCode) === false){
				alertMsg.warn("身份证号仅能含有数字及大写字母X，请重新录入。");  
			      	return  false;  
			}
			 if((!idCardNoUtil.check18IdCardNo(certiCode)) ){
				 alertMsg.error("证件号码不符编码规则，请重新录入。");
				 return false;
			} 
    	}
    	if(certiType == '5'){// 户口簿
    		var regX = /(^\d{17}(\d|X|x)$)/;
     		if(certiCode.length != '8' && certiCode.length != '18'){
    			alertMsg.warn("户口簿号码仅允许8位或18位，请重新录入。");  
   		      	return  false;  
    		}
     		if(certiCode.length == '8'){
     			if(reg.test(certiCode) === false){
     				alertMsg.warn("户口簿号码为8位时，仅允许含有数字，请重新录入。");  
       		      	return  false;
     			}
     		}
     		if(certiCode.length == '18'){
	    		if(regX.test(certiCode) === false){
	    			alertMsg.warn("户口簿号码为18位时，仅允许含有数字和X，请重新录入。");  
	   		      	return  false;  
	    		}
				if((!idCardNoUtil.check18IdCardNo(certiCode))){
					 alertMsg.error("证件号码不符编码规则，请重新录入。");
					 return false;
				} 
     		}
    	}
    	if(certiType == '2'){// 军官证
    		if(certiCode.length < 10 || certiCode.length > 18){
    			 alertMsg.error("军官证应为10-18位，请重新录入。");
				 return false;
    		}
    		if(pattern.test(certiCode) === false){
				alertMsg.warn("军官证仅允许录入汉字、数字、字母、-，请重新录入。");  
			    return  false;  
			}
    	}
    	if(certiType == 'd' || certiType == 'i'){// 台湾居民来往大陆通行证,港澳居民来往内地通行证
    		if(certiCode.length < 8){
   			 	alertMsg.error("港澳台居民内地通行证应不少于8个字符，请重新录入。");
				return false;
   			}
    	}
    	if(certiType == '1'){// 护照
    		if(certiCode.length < 3 || pattern1.test(certiCode) === false){
	   			alertMsg.error("护照号码应不少于3个字符，且只能含有字母、数字，请重新录入。");
				return false;
   			}
    	}
    	if(certiType == 'e'){// 外国人永久居留身份证
    		if(certiCode.length < 10 || pattern1.test(certiCode) === false){
	   			alertMsg.error("外国人永久居留证件仅能包括字母、数字，且不能低于10位，请重新录入。");
				return false;
   			}
    	}
    }
    //保存时,校验当前操作日期和证件有效止期
    function checkEndTime(certiEndDate){
    	//var time = new Date();
    	var seperator = "/";
    	var time = getUserTimeForPA();//获取当前操作时间
    	time = time.substring(0,10);
    	var ndates = time.split("-");
    	var ntime = ndates[0] + seperator + ndates[1] + seperator + ndates[2];
    	var nowTime = new Date(ntime);//当前时间的时间格式
    	
    	var dates = certiEndDate.split("-");
    	var etime = dates[0] + seperator + dates[1] + seperator + dates[2];
    	var endTime = new Date(etime);//证件有效止期的时间格式
    	console.log('nowTime:' + nowTime);
    	console.log('endTime' + endTime);
    	
    	if(endTime <= nowTime){
    		return false;
    	}
    	return true;
    }
    
    //保存时,校验当前操作日期和证件有效止期
    function checkStartTime(certiStartDate){
    	//var time = new Date();+
    	var seperator = "/";
    	var time = getUserTimeForPA();//获取当前操作时间
    	time = time.substring(0,10);
    	var ndates = time.split("-");
    	var ntime = ndates[0] + seperator + ndates[1] + seperator + ndates[2];
    	var nowTime = new Date(ntime);//当前时间的时间格式
    	
    	var dates = certiStartDate.split("-");
    	var stime = dates[0] + seperator + dates[1] + seperator + dates[2];
    	var startTime = new Date(stime);//证件有效止期的时间格式
    	console.log('nowTime:' + nowTime);
    	console.log('startTime' + startTime);
    	if(startTime > nowTime){
    		return false;
    	}
    	return true;
    }

    function csCompanyInfoSubmit(targe){
      	var companyName=$("#companyName", $.pdialog.getCurrent()).val();
      	var companyAddress=$("#companyAddress", $.pdialog.getCurrent()).val();
      	var companyIndustry=$("#companyIndustry", $.pdialog.getCurrent()).val();
      	var regCapital=$("#regCapital", $.pdialog.getCurrent()).val();
      	var businessCope=$("#businessCope", $.pdialog.getCurrent()).val();
      	var beneName=$("#beneName", $.pdialog.getCurrent()).val();
      	var holdingPersonName=$("#holdingPersonName", $.pdialog.getCurrent()).val();
      	var companyHolderCertiType=$("#companyHolderCertiType", $.pdialog.getCurrent()).val();
      	var companyHolderCertiCode=$("#companyHolderCertiCode", $.pdialog.getCurrent()).val();
      	var holderCertiStartDate=$("#holderCertiStartDate", $.pdialog.getCurrent()).val();
      	var holderCertiEndDate=$("#holderCertiEndDate", $.pdialog.getCurrent()).val();
      	var legalPersonName=$("#legalPersonName", $.pdialog.getCurrent()).val();
      	var legalPersonCertiType=$("#legalPersonCertiType", $.pdialog.getCurrent()).val();
      	var legalPersonCertiCode=$("#legalPersonCertiCode", $.pdialog.getCurrent()).val();
      	var legalPerCertiStartDate=$("#legalPerCertiStartDate", $.pdialog.getCurrent()).val();
      	var legalPerCertiEndDate=$("#legalPerCertiEndDate", $.pdialog.getCurrent()).val();
      	var managerName=$("#managerName", $.pdialog.getCurrent()).val();
      	var managerCertiType=$("#managerCertiType", $.pdialog.getCurrent()).val();
      	var managerCertiCode=$("#managerCertiCode", $.pdialog.getCurrent()).val();
      	var managerCertiStartDate=$("#managerCertiStartDate", $.pdialog.getCurrent()).val();
      	var managerCertiEndDate=$("#managerCertiEndDate", $.pdialog.getCurrent()).val();
      	var operatorName=$("#operatorName", $.pdialog.getCurrent()).val();
      	var operatorCertiType=$("#operatorCertiType", $.pdialog.getCurrent()).val();
      	var operatorCertiCode=$("#operatorCertiCode", $.pdialog.getCurrent()).val();
      	var operatorCertiStartDate=$("#operatorCertiStartDate", $.pdialog.getCurrent()).val();
      	var operatorCertiEndDate=$("#operatorCertiEndDate", $.pdialog.getCurrent()).val();
     	var companyOrgCode=$("#companyOrgCode", $.pdialog.getCurrent()).val();// 社会统一信用代码证/组织机构代码证
    	var orgCodeStartDate=$("#orgCodeStartDate", $.pdialog.getCurrent()).val();
    	var orgCodeEndDate=$("#orgCodeEndDate", $.pdialog.getCurrent()).val();
     	var busiLicenceCode=$("#busiLicenceCode", $.pdialog.getCurrent()).val();// 营业执照号
     	var licenceStartDate=$("#licenceStartDate", $.pdialog.getCurrent()).val();
    	var licenceEndDate=$("#licenceEndDate", $.pdialog.getCurrent()).val();
    	var taxCode=$("#taxCode", $.pdialog.getCurrent()).val();// 税务登记证
    	var taxCodeStartDate=$("#taxCodeStartDate", $.pdialog.getCurrent()).val();
    	var taxCodeEndDate=$("#taxCodeEndDate", $.pdialog.getCurrent()).val();
    	var beneCertiType=$("#beneCertiType", $.pdialog.getCurrent()).val();
    	var beneCertiCode=$("#beneCertiCode", $.pdialog.getCurrent()).val();//受益人证件号码
    	var companyHolderCertiType=$("#companyHolderCertiType", $.pdialog.getCurrent()).val();
    	var companyHolderCertiCode=$("#companyHolderCertiCode", $.pdialog.getCurrent()).val();//控股股东或实际控制人证件号
    	var holderCertiStartDate=$("#holderCertiStartDate", $.pdialog.getCurrent()).val();
    	var holderCertiEndDate=$("#holderCertiEndDate", $.pdialog.getCurrent()).val();
    	var legalPersonCertiType=$("#legalPersonCertiType", $.pdialog.getCurrent()).val();
    	var legalPersonCertiCode=$("#legalPersonCertiCode", $.pdialog.getCurrent()).val();//法定代表人证件号码
    	var legalPerCertiStartDate=$("#legalPerCertiStartDate", $.pdialog.getCurrent()).val();
    	var legalPerCertiEndDate=$("#legalPerCertiEndDate", $.pdialog.getCurrent()).val();
    	var managerCertiType=$("#managerCertiType", $.pdialog.getCurrent()).val();
    	var managerCertiCode=$("#managerCertiCode", $.pdialog.getCurrent()).val();//负责人证件号码
    	var managerCertiStartDate=$("#managerCertiStartDate", $.pdialog.getCurrent()).val();
    	var managerCertiEndDate=$("#managerCertiEndDate", $.pdialog.getCurrent()).val();
    	var operatorCertiType=$("#operatorCertiType", $.pdialog.getCurrent()).val();
    	var operatorCertiCode=$("#operatorCertiCode", $.pdialog.getCurrent()).val();//授权办理业务人员证件号
    	var operatorCertiStartDate=$("#operatorCertiStartDate", $.pdialog.getCurrent()).val();
    	var operatorCertiEndDate=$("#operatorCertiEndDate", $.pdialog.getCurrent()).val();
    	var mobile=$("#mobile", $.pdialog.getCurrent()).val();//手机号
    	var country=$("#country", $.pdialog.getCurrent()).val();
    	var districtMap=$("#districtMap", $.pdialog.getCurrent()).val();
    	var city=$("#city_panelInfoAdd", $.pdialog.getCurrent()).val();//获得市的输入值
    	var district=$("#district_panelInfoAdd", $.pdialog.getCurrent()).val();//获得区的输入值
    	var address=$("#address", $.pdialog.getCurrent()).val();//获得详细地址的输入值
    	var province = $("#province_panelInfoAdd",$.pdialog.getCurrent()).val();//获得省的输入值
    	var postCode = $("#postCode",$.pdialog.getCurrent()).val();//获得输入的邮政编码
    	var holdeRatio = $("#holdeRatio",$.pdialog.getCurrent()).val();//获得输入的持股比例
    
    	if(companyName == '' || companyAddress == '' || companyIndustry == '' || regCapital == '' || businessCope == '' || holdingPersonName == '' ||
    	   companyHolderCertiType == '' || companyHolderCertiCode == '' || holderCertiStartDate == '' || holderCertiEndDate == '' || legalPersonName == '' ||
    	   legalPersonCertiType == '' || legalPersonCertiCode == '' || legalPerCertiStartDate == '' || legalPerCertiEndDate == '' || managerName == '' ||
    	   managerCertiType == '' || managerCertiCode == '' || managerCertiStartDate == '' || managerCertiEndDate == '' || operatorName == '' ||
    	   operatorCertiType == '' || operatorCertiCode == '' || operatorCertiStartDate == '' || operatorCertiEndDate == '' || country == '' ||
    	   districtMap == ''){
    		alertMsg.error("必录项未录入，请重新录入。");
    		return ;
    	}
    	
    	//修改省、市、区、详细地址的校验    	
    	if(province==null || province=='' || city==null || city=='' || district==null || district=='' || address==null || address=='' ){
    		alertMsg.error("省/市、区、详细地址不能为空，请重新录入。");
    		return ;
    	}
    	
    	
    	//添加上针对详细地址的校验    	
    	if(address.length < 5 ){
    		alertMsg.error("详细地址最低字符数不少于5位，请重新录入。");
    		return ;
    	}else{
    		//校验字符串中返回值是否包含汉字，如果不包含汉字，阻断性提示
    		var pattern = /[\u4e00-\u9fa5]/;
    		if( !(pattern.test(address)) ){
    			alertMsg.error("详细地址不允许全部录入非汉字字符，请重新录入。");
        		return ;
    		}
    	}
    	
    	//先校验输入的手机号是否为空，不为空的情况下再去校验手机号是否输入的正确
    	if(mobile != null && mobile != ''){
    		var isnum = /^\d+$/.test(mobile);
        	if(!isnum || mobile.match(/^([0-9a-zA-Z])\1*$/) != null || mobile.length != 11 || mobile == '13000000000'){
        		alertMsg.error("手机号码11位数字，数字不能全相同，不能是13000000000，请重新录入。");
        		return ;
        	}
        	var str = mobile.substring(0,2);
        	if (str != '13' && str != '14' && str != '15' && str != '16' 
    			&& str != '17' && str != '18' && str != '19') {
    			alertMsg.error("手机号必须13、14、15、16、17、18、19开头，请重新录入。");
    			return false;
    		}
    	}
    	
    	//先判断邮政编码是否为空，不为空的情况下再去校验邮政编码与省市是否对应的上
    	if(postCode != null && postCode != ''){
    		var postCodePat = /^\d{6}$/;
    		if(!postCodePat.test(postCode)){
    			alertMsg.error("邮政编码必须是六位数字，请重新录入。");
    			return false;
    		}else{
			 	var provinceName = $("#province_panelInfoAdd option:selected").text();
				var cityName = $("#city_panelInfoAdd  option:selected").text();
				var districtName = $("#district_panelInfoAdd  option:selected").text();
    			var getPostCode = findPostCode(provinceName,cityName,districtName);
    			if( getPostCode.substring(0,4)!= postCode.substring(0,4) ){
    				alertMsg.error("邮编与省市不匹配，请重新录入。");
        			return false;
    			}
    		}
    	}
    	
    	//判断持持股比例或表决权占比的长度最大为5，超过阻断提示
    	if(holdeRatio.length > 5){
    		alertMsg.error("持股比例或表决权占比最大长度为5，请重新录入。");
			return false;
    	}
    	
    	if(checkIDcertiCode(beneCertiType,beneCertiCode) == false || checkIDcertiCode(companyHolderCertiType,companyHolderCertiCode) == false
    			|| checkIDcertiCode(legalPersonCertiType,legalPersonCertiCode) == false || checkIDcertiCode(managerCertiType,managerCertiCode) == false
    			|| checkIDcertiCode(operatorCertiType,operatorCertiCode) == false){
    		return false;
    	}
    	/** 针对客户名称的校验  **/
    	if(checkCompanyCustomerNameMethod(companyName) == false){
    		return false;
    	}
    	if(beneName != '' && beneName != undefined){
    		if(customerNameMethod(beneName) == false || customerNameMethod(holdingPersonName) == false 
        			|| customerNameMethod(legalPersonName) == false || customerNameMethod(managerName) == false || customerNameMethod(operatorName) == false){
        		return false;
        	}
    	}
    	if(companyOrgCode == '' && busiLicenceCode == '' && taxCode == ''){
    		alertMsg.error("社会统一信用代码证/组织机构代码证、营业执照号、税务登记证，三者不能全为空，请录入。");
    		return ;
    	}
		if(companyOrgCode != ''){//社会统一信用代码证/组织机构代码证
			var check = /^[A-Z0-9]{15}$|^[A-Z0-9]{18}$/.test(companyOrgCode);
			if(!check){
				alertMsg.error("仅支持录入18位或15位（社会统一信用代码证为18位，组织机构代码证为15位），且仅能为数字或大写字母，请重新录入。");
	    		return ;
			}
			if(orgCodeStartDate == '' || orgCodeEndDate == ''){
				alertMsg.error("社会统一信用代码证/组织机构代码证的有效期不能为空，请重新录入。");
	    		return ;
			}
			/* if(companyOrgCode.length == '15' && (taxCode == '' || taxCodeStartDate == '' || taxCodeEndDate == '')){
				alertMsg.error("社会统一信用代码证/组织机构代码证的有效期不能为空，请重新录入。");
	    		return ;
			} */
			if(companyOrgCode.length == '15' && taxCode == ''){
				alertMsg.error("请录入税务登记证。");
	    		return ;
			}
		}
		if(busiLicenceCode != ''){//营业执照号
			var check = /^[A-Z0-9]+$/.test(busiLicenceCode);
			if(!check){
				alertMsg.error("仅能为数字或大写字母，请重新录入。");
	    		return ;
			}
			if(licenceStartDate == '' || licenceEndDate == ''){
				alertMsg.error("营业执照号的有效期不能为空，请重新录入。");
	    		return ;
			}
		}
		if(taxCode != ''){//税务登记证
			var check = /^[A-Z0-9]+$/.test(taxCode);
			if(taxCode.length != '15' && taxCode.length != '18' || !check){
				alertMsg.error("仅能为18位或15位，请重新录入。");
	    		return ;
			}
			if(taxCode.length == '18' && taxCode != companyOrgCode){
				alertMsg.error("税务登记证为18位时，应与社会统一信用代码证一致，请重新录入。");
	    		return ;
			}
			if(taxCodeStartDate == '' || taxCodeEndDate == ''){
				alertMsg.error("税务登记证的有效期不能为空，请重新录入。");
	    		return ;
			}
		}
		
    	if(companyName==''){
    		alertMsg.error("请录入单位名称");
    		return ;
    	}

    	if( (orgCodeStartDate=='' || orgCodeStartDate==null) && (orgCodeEndDate=='' || orgCodeEndDate==null) && 
    		(licenceStartDate=='' || licenceStartDate==null) && (licenceEndDate=='' || licenceEndDate==null) && 
    		(taxCodeStartDate=='' || taxCodeStartDate==null) && (taxCodeEndDate=='' || taxCodeEndDate==null) ){
    		
    		alertMsg.error("统一社会信用代码/组织机构代码、税务登记号码、营业执照号码及相对应证件有效起止期不能同时为空");
    		return ;
    	}
    	
    	if(companyOrgCode==''){
    		alertMsg.error("请录入统一社会信用代码/组织机构代码");
    		return ;
    	}
    	if(!checkEndTime(orgCodeEndDate) || !checkEndTime(licenceEndDate) || !checkEndTime(taxCodeEndDate) || !checkEndTime(holderCertiEndDate) 
    			|| !checkEndTime(legalPerCertiEndDate) || !checkEndTime(managerCertiEndDate) || !checkEndTime(operatorCertiEndDate) ){
    		alertMsg.error("证件有效止期应大于录入日期，请重新录入。");
    		return false;
    	}
    	if(!checkStartTime(orgCodeStartDate) || !checkStartTime(licenceStartDate) || !checkStartTime(taxCodeStartDate) || !checkStartTime(holderCertiStartDate)
    			|| !checkStartTime(legalPerCertiStartDate) || !checkStartTime(managerCertiStartDate) || !checkStartTime(operatorCertiStartDate)){
    		alertMsg.error("证件有效起期应小于等于录入日期，请重新录入。");
    		return false;
    	}
    	var beneResult = "";
    	$("#csPayCompanyBeneListBody",$.pdialog.getCurrent()).find('tr').each(function (){
    		var beneName = $(this).find("#beneName").val();
    		var beneCertiTypeInput = $(this).find("#beneCertiTypeInput").val();
    		var beneCertiCode = $(this).find("#beneCertiCode").val();
    		var beneCertiStartDate = $(this).find("#beneCertiStartDate").val();
    		var beneCertiEndDate = $(this).find("#beneCertiEndDate").val();
    		var beneCertiType = $(this).find("#beneCertiType").val();
    		if(beneName == ''){
    			beneResult = "受益所有人姓名为空，请重新录入。";
    		}
    		if(beneCertiType == ''){
    			beneResult = "受益所有人证件类型为空，请重新录入。";
    		}
    		if(beneCertiCode == ''){
    			beneResult = "受益所有人证件号码为空，请重新录入。";
    		}
    		if(beneCertiStartDate == ''){
    			beneResult = "受益所有人证件有效起期为空，请重新录入。";
    		}
    		if(beneCertiEndDate == ''){
    			beneResult = "受益所有人证件有效止期为空，请重新录入。";
    		}
    		if(!checkStartTime(beneCertiStartDate)){
    			beneResult = "证件有效起期应小于等于录入日期，请重新录入。";
    		}
    		if(!checkEndTime(beneCertiEndDate)){
    			beneResult = "证件有效止期应大于录入日期，请重新录入。";
    		}
    		
    	});
    	if(beneResult != ""){
    		alertMsg.error(beneResult);
    		return false;
    	}
    	addCsPayCompany();
    	/*$.ajax({
			url:"${ctx}/cs/csPayCompany/checkIdentityDateRange_PA_csPayCompanyAction.action",
			type:"post",
			data:$("#companyInfo_add1").serialize(),
			success:function(data){
				var json = jQuery.parseJSON(data);
				if (json.statusCode == 300) {
					alertMsg.error(json.message);
		    		return false;
				}else{
					addCsPayCompany()；
				}
			}
		});*/
		
		/* var onsubmit = "return validateCallback(this,updateEndAjaxDone);";
		var $form = $("#" + formId, $.pdialog.getCurrent());
		$form.attr("onsubmit", onsubmit);
		$form.submit(); */
	}
  	//保存
	function addCsPayCompany(){
		var $dataTable = $("#csPayCompanyBeneListTable" ,$.pdialog.getCurrent());
		var _jsons = "";
		var $jsonStr = $("#jsonStr",$.pdialog.getCurrent());
		_jsons += _cs_tableToJson($dataTable);
		$jsonStr.val(_jsons);
		var companyName=$("#companyName", $.pdialog.getCurrent()).val();
		var busiLicenceCode=$("#busiLicenceCode", $.pdialog.getCurrent()).val();
		var companyOrgCode=$("#companyOrgCode", $.pdialog.getCurrent()).val();
		var taxCode=$("#taxCode", $.pdialog.getCurrent()).val();
		var customerCertiCode =  busiLicenceCode + "、" + companyOrgCode+ "、" + taxCode;
		
		//在提交数据之前，添加去除disabled属性，否则执行$("#companyInfo_add1").serialize()时拿不到对应的值
		$("#holderCertiEndDate").removeAttr( 'disabled');
		$("#legalPerCertiEndDate").removeAttr( 'disabled');
		$("#managerCertiEndDate").removeAttr( 'disabled');
		$("#operatorCertiEndDate").removeAttr( 'disabled');
		
		//这个操作是为了解决如果字符串中带#号，保存的时候直接进行转码会失败
		var jsonStrContent = encodeURI(encodeURI(_jsons));
		var jsonStrUpdate = jsonStrContent.replace(/#/g,"%23");
		$.ajax({
			type : "post",
			url : "${ctx }/cs/csPayCompany/addCsPayCompany_PA_csPayCompanyAction.action?jsonStr="+jsonStrUpdate,
			data : $("#companyInfo_add1").serialize(),
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					alertMsg.error(json.message);
				} else if (json.statusCode == DWZ.statusCode.timeout) {
					DWZ.loadLogin();
				}else{
					$.bringBack({id:1,getMoneyName:companyName,customerCertiCode:customerCertiCode, payCompanyId:json.message});// 此处代码为将弹出页面的相关字段值带回到补退费页面
					alertMsg.correct("保存成功！");
//					updateEndAjaxDone(json);
					console.log(json.message);
					$.pdialog.closeCurrent(); // 关闭当前弹出框
				}
			},
		});
	}
	function _addCsPayCompanyBene(targe) {
	 	var parentDiv=$(targe).parents("#queryCompanyInfoDIV").get(0);
	 	var beneName= $("#beneName",parentDiv).val();
		var beneCertiType= $("#beneCertiType",parentDiv).val();
		var beneCertiCode= $("#beneCertiCode",parentDiv).val();
		var beneCertiStartDate= $("#beneCertiStartDate",parentDiv).val();
		var beneCertiEndDate= $("#beneCertiEndDate",parentDiv).val(); 
		var addMsg = '<tr tr_saveStatus="1">'
				+ '<td align="center"><input type="text" name="csPayCompanyBeneVO.beneName" id="beneName" value=""></td>'
				+ '<td><Field:codeTable name="csPayCompanyBeneVO.beneCertiType" value="${csPayCompanyBeneVO.beneCertiType}" tableName="APP___PAS__DBUSER.T_CERTI_TYPE_QY" id="beneCertiType"  /></td>'
				+ '<td align="center"><input type="text" name="csPayCompanyBeneVO.beneCertiCode" id="beneCertiCode" value=""></td>'
				+ '<td align="center"><input name="csPayCompanyBeneVO.beneCertiStartDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="" format="yyyy-MM-dd"/>" id="beneCertiStartDate" type="expandDateYMD" class="date" /></td>'
				+ '<td align="center"><input name="csPayCompanyBeneVO.beneCertiEndDate"   myOption="endDateComputer(this)" style="width: 146px;" value="<s:date name="" format="yyyy-MM-dd"/>" id="beneCertiEndDate" type="expandDateYMD" class="date" /><input type="checkbox" onclick="endDateClear(this)" id="beneCerti" value="" />长期</td>'
				+ '<td><a class="btnDel" onclick="deleteLine(this)"></a>' + '</td>'
				+ '</tr>'; 
		$(addMsg).appendTo("#csPayCompanyBeneListBody").initUI(); 
	}
	
	//alm:26288,因js问题，这里对一个input标签动态赋值，使后台能够拿到证件类型
	function _checkShowUrgentDiv(obj) {
		var $this = $(obj).val();
		$("#beneCertiTypeInput").val($this);
	}
	//删除受益人
	function delBeneInfo(obj) {
		console.log('开始删除');
		var radio = $(obj);
		$(radio).parent().parent().remove();
	}
</script>
