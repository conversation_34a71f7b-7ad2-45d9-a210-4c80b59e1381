<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_accept.js"></script>
<script type="text/javascript"
	src="${ctx}/cs/pages/csCheck/appletLib/deployJava.js"></script>
<script type="text/javascript">
	$(document).ready(function(){ 
		var _top=($(window).height())/2+80;
		var _left=($(window).width())/2+185;
		$("#loadFikeNC").parent().parent().css({top:_top,left:_left});
		$(document).find("div.shadow").css({top:_top,left:_left});
	});
</script>
<form>
	<input type="hidden" id="customer_Name_ann" value=${customerVO.customerName }>
	<input type="hidden" id="holderCertiType_ann" value=${customerCertiType }>
	<input type="hidden" id="customerCertiCode_ann" value=${customerCertiCode }>
	<input type="hidden" id="holderGender_ann" value=${holderGender }>
	<input type="hidden" id="birthDate_ann" value=${birthDate }>
	<input type="hidden" id="acceptId_ann" value=${acceptId }>
</form>
<div id="policyHolderDialogAnnual" style="height: 90%">
			<div style="text-align: center;margin-top: 60px;">
			<form id="saveFrom" action="" class="aaa">
			<c:forEach items="${agentPOList }" var="apl">
				<div class="tabdivclass">
						客户&nbsp;&nbsp;${apl.agentName}&nbsp;&nbsp;为我司在职销售人员，其上一自然年度税后年收入为<input id="income" name="income" style="width: 100px;" onblur="incomeBlur()"/>万元；
						<input type="hidden" id="" name="certiCode" value="${apl.certiCode }">
						<input type="hidden" id="" name="name" value="${apl.agentName }">
						<input type="hidden" id="" name="certType" value="${apl.certType }">
						<input type="hidden" id="" name="agentGender" value="${apl.agentGender }">
						<input type="hidden" id="" name="birthday" value="${apl.birthday }">
						<input type="hidden" id="" name="acceptId" value=${acceptId }>
				</div>
			</c:forEach>
			</form>
				<div>
					<button onclick="saveNaturalYearIncome()">提交</button>
					<button onclick="colsedialog()">返回</button>
				</div>
			</div>
			
</div>

<script type="text/javascript">

	function saveNaturalYearIncome(){
		debugger;
		var cusName = $("#customer_Name_ann").val();
		var holderCertiType = $("#holderCertiType_ann").val();
		var customerCertiCode = $("#customerCertiCode_ann").val();
		var holderGender = $("#holderGender_ann").val();
		var birthDate = $("#birthDate_ann").val();
		var acceptId = $("#acceptId_ann").val();
		var income = $("#income").val();
		
		
		if(income==null||income==""){
			alertMsg.error("年收入不可为空");
			return;
		}
		
		//输出以数组形式序列化表单值
		 var obj = [];
		 $('.aaa').each(function () {
		      obj.push(JSON.stringify($(this).serializeObject()));
		 });
		
		console.log(obj.toString());
		$.ajax({
			type:"post",
			url:"${ctx }/cs/csEntry/previousNaturalYearIncomeSave_PA_csEntryAction.action",
			data:{
				"saveYearIncomeJson":obj.toString()
			},
			dataType : "json",
			success: function(data){
				var json = DWZ.jsonEval(data);
				if (json != null && json != ''
					&& json.statusCode == "200"){
					$.pdialog.close("policyHolderDialogAnnual");
					alertMsg.correct("保存成功");
				}
			}
		})
	}
	$.fn.serializeObject = function () {
	    var o = {};
	    var a = this.serializeArray();
	    $.each(a, function () {
	        if (o[this.name]) {
	            if (!o[this.name].push) {
	                o[this.name] = [o[this.name]];
	            }
	            o[this.name].push(this.value || '');
	        } else {
	            o[this.name] = this.value || '';
	        }
	    });
	    return o;
	}
	function incomeBlur(){
		var income = $("#income").val();
		if(!checkAmount(income)){
			$("#income").val("");
			alertMsg.error("金额格式错误，非0的正整数且保留两位小数，请确认！");
			return;
		}
	}
	function colsedialog(){
		$.pdialog.close("policyHolderDialogAnnual");
	}
</script>
