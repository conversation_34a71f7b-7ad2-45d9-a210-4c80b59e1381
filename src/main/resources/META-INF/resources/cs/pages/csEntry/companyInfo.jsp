<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/ChinesePostcode.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<%-- <script type="text/javascript" src="${ctx}/cs/js/check_name.js"></script>
 --%>
 
 <script type="text/javascript" src="${ctx}/cs/pages/common/js/tax.js"></script>
 
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="backgroundCollor"  style="background:#f5f6f8" layoutH="8">
 	<form action="${ctx}/cs/csPayCompany/queryCsPayCompanyById_PA_csPayCompanyAction.action"
					id="queryCompanyInfo" onsubmit="return divSearch(this,'queryCompanyInfoDIV')"
					method="post">
					<input id="acceptCodePayee" type="hidden"  name="acceptCode" value="${acceptCode}" />
					<div class="divfclass">
						<div class="panelPageFormContent" >
							<dl>
								<dt style="width: 200px;">客户名称</dt>
								<dd>
									<input type="text" name="csPayCompanyVO.companyName" class=""
										value="${csPayCompanyVO.companyName}" id="companyNameQRY" />
								</dd>
							</dl>
							<dl>
								<dt style="width: 200px;">社会统一信用代码/组织机构代码</dt>
								<dd>
									<input type="text" name="csPayCompanyVO.companyOrgCode" class=""
										value="${csPayCompanyVO.companyOrgCode}"
										id="companyOrgCodeQRY" />
								</dd>
							</dl>
						</div>
						<table style="width: 100%">
							<tbody>
								<tr>
									<td style="width: 100px" >
										<div class="pageFormbut" >
											<button type="button" class="but_blue"  onclick='queryCompanyInfo("queryCompanyInfo");'>查询</button>
										</div>
									</td>
								</tr>
							</tbody>
						</table> 
					</div>
				</form> 
	<div id="queryCompanyInfoDIV">
		<s:include value="companyInfoDetail.jsp"></s:include>
	</div>
	
</div>
<script type="text/javascript">
$(function (){
	/* var type = $("#type1").val();
	if(type=='query'){
		$("#editId",navTab.getCurrentPanel()).hide();
		$("#deleteId",navTab.getCurrentPanel()).hide();
		$("#insertId",navTab.getCurrentPanel()).hide();
		//改input的 type 的类型为 readOnly
		var $checkIds =$("#formDiv",navTab.getCurrentPanel()).find('input','tpye:text');
		var $select =$("#formDiv",navTab.getCurrentPanel()).find('select');
			  for(var i=0;i<$checkIds.length; i++){
				$($checkIds[i],navTab.getCurrentPanel()).attr('readonly','readonly');
		  }
			  for(var i=0;i<$select.length; i++){
				 $($select[i],navTab.getCurrentPanel()).attr('disabled','disabled'); 
			  }
	}else if(type=='add'){
		$("#editId",navTab.getCurrentPanel()).hide();
		$("#deleteId",navTab.getCurrentPanel()).hide();
	}else if(type=='delete'){
		$("#editId",navTab.getCurrentPanel()).hide();
		$("#insertId",navTab.getCurrentPanel()).hide();
	}else if(type=='update'){
		$("#insertId",navTab.getCurrentPanel()).hide();
		$("#deleteId",navTab.getCurrentPanel()).hide();
	}
	$("[id='busiPrdId']").attr("disabled",true);
	var certiEndDate = $("#certiEndDate", navTab.getCurrentPanel()).val();//长期是否
	if(certiEndDate == '9999-12-31'){
		$("#changQiFlag").val("1");
		$("#certiEndDate").val("");
		$("#certiEndDate").attr( 'disabled', true);
		$("#longDate").attr("checked","checked");
	} */
});   
function queryCompanyInfo(formId){
	var companyName =  $("#companyNameQRY",$.pdialog.getCurrent()).val();
	var companyOrgCode =  $("#companyOrgCodeQRY", $.pdialog.getCurrent()).val();
	var acceptCode = $("#acceptCodePayee", $.pdialog.getCurrent()).val();
	if(companyName == '' && companyOrgCode == ''){
		alertMsg.error("请输入查询条件。");
		return false;
	}
/* 	var $form = $("#"+formId, $.pdialog.getCurrent());
	$form.submit(); */
	  $.ajax({
		type : "post",
		url : "${ctx}/cs/csPayCompany/queryCsPayCompanyById_PA_csPayCompanyAction.action",
		data : 'csPayCompanyVO.companyName='+encodeURI(companyName)+'&csPayCompanyVO.companyOrgCode='+companyOrgCode+'&acceptCode='+acceptCode,
		success : function(data) {
			debugger;
			var json = DWZ.jsonEval(data);
			if (json.statusCode == DWZ.statusCode.error) {
				alertMsg.error(json.message);
			}else {
				$("#companyInfo_add1").html(data).initUI();
			}
		}
	});  
}
</script>
