<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<div class="panel">
	<!-- <h1>查询结果</h1> -->
	<div class="pageFormContent">
		<form id="pagerForm" onsubmit="return divSearch(this, 'uuy');" action="${ctx}/cs/csEntry/selectReturnNumber2_PA_csEntryAction.action" method="post">
			<input type="hidden" name="pageNum" value="${pageNum}" />
			<input type="hidden" name="numPerPage" value="${selectReturnPage.pageSize}" />
			
			<input type = "hidden" name = "bankOfDepositVO.correspondentNo" value = "${bankOfDepositVO.correspondentNo }"/>
			<input type = "hidden" name = "bankOfDepositVO.correspondentName" value = "${bankOfDepositVO.correspondentName }"/>
			<input type = "hidden" name = "bankOfDepositVO.bankCode" value = "${bankOfDepositVO.bankCode }"/>
			<input type = "hidden" name = "bankOfDepositVO.bankName" value = "${bankOfDepositVO.bankName }"/>
		</form>
		
		<div class="pageContent" style="border-left:1px #B8D0D6 solid;border-right:1px #B8D0D6 solid">
				<table class="list" layoutH='160px' style="width: 100%">
				<thead>
					<tr>
						<th orderfield="orgNum">联行号</th>
						<th orderfield="orgName">支行银行名称</th>
						<th orderfield="creator">银行代码</th>
						<th orderfield="leader">银行名称</th>
						<th width="80">查找带回</th>
					</tr>
				</thead>
				<tbody>
					<s:iterator value="selectReturnPage.pageItems" status="st" var="item">
						<tr>
							<td>${correspondentNo }</td>
							<td>${correspondentName }</td>
							<td>${bankCode }</td>
							<td>${bankName }</td>
							<td>
								<a class="btnSelect" href="javascript:$.bringBack({id:'1', orgName:'${ correspondentName}', orgNum:'${ correspondentNo}', creator:'${bankCode }'})" title="查找带回">选择</a>
							</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select  list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="selectReturnPage.pageSize" onchange="dialogPageBreak({numPerPage:this.value}, 'uuy')">
					</s:select>
					<span>条，共${selectReturnPage.total}条</span>
				</div>				
				<div class="pagination" rel="uuy" totalCount="${selectReturnPage.total}" currentPage="${selectReturnPage.pageNo}"
				numPerPage="${selectReturnPage.pageSize}"  targetType ="dialog" pageNumShown="10"></div>
			</div>		
		</div>
	</div>
</div>