<!--guyy_wb 保全录入-受理信息修改页面-保全项目信息录入panel-->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<form id="checkPremInfoForm"
	action="cs/csEntry/checkArapInfo_PA_csEntryAction.action?menuId=${menuId}"
	method="post" onsubmit="return divSearch(this,'csPremArapDiv');">
	<input type="hidden" id="changeId" name="changeId" value="${changeId}" />
	<input type="hidden" id="customerId" name="customerId"
		value="${customerId}" /> <input type="hidden" id="acceptId"
		name="acceptId" value="${acceptId}" /> <input type="hidden"
		id="jsonStr" name="jsonStr" value="" />
</form>
<div id='updateDiv'>
<!-- 录入退回意见 -->
	<s:if test="csCheckRemarkVOs !=null && csCheckRemarkVOs.size != 0">
		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png" />复核修改意见
			</h1>
		</div>
		<div class="tabdivclass">
				<div>
					<table class="list" style="width:100%">
						<thead>
							<tr>
								<th>复核人员</th>
								<th>复核结论</th>
								<th>复核意见</th>
								<th>复核修改原因</th>
								<th>复核完成时间</th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="csCheckRemarkVOs" status="st">
							<tr>
								<td align="center"><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${insertBy}"/></td>
								<td align="center"><Field:codeValue tableName="APP___PAS__DBUSER.T_CS_REVIEW_RESULT" value="${reviewResult}"/></td> 
								<td align="left" style="width:40%">${reviewView}</td>
								<td>${backEntyrCause}</td>
								<td align="center"><s:date name="insertTime" format="yyyy-MM-dd hh:mm:ss"/></td>
							</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
		</div>
	</s:if>

	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />保全项目信息录入
		</h1>
	</div>
	<div class="tabdivclass" style="background-color: white;">
		<input name="" type="hidden" id="accepts" value="${accepts}" />
		<table class="list" style="width: 100%" id="csEntryTable">
			<thead>
				<tr>
					<th>保全受理号</th>
					<th>保全项目名称</th>
					<th>保单号</th>
					<!-- 85366增加多主多附标识 -->
					<th>多主险保单</th>
					<th>保全受理状态</th>
					<th width="90px">项目信息录入</th>
					<th>打印免填单</th>
				</tr>
			</thead>
			<tbody align="center">
				<s:iterator value="csAcceptInfoVOList" var="tt">
					<tr id='${acceptCode}'>
						<td>${acceptCode}<input type="hidden" value="${acceptId}"
							name="thisAcceptId"></td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
								value="${serviceCode }" />${serviceCode }</td>
						<td style="width: 40%; word-wrap: break-word; word-break: break-all;">${policyCodeS}<input type="hidden" value="${accId}"
							name="thisAccId"></td>
						<td style="display: none;">${orderId}<input type="hidden"
							value="${premArap}" id="premArap"></td>
						</td>
						<!-- 85366增加多主多附标识 -->
						<td>${multiMainriskFlag}</td> 
						<td class="wsyAcceptStatus"><Field:codeValue
								tableName="APP___PAS__DBUSER.T_ACCEPT_STATUS"
								value="${tt.acceptStatus}" /><input type="hidden"
							value="${tt.acceptStatus}" id="acceptStatusValue"
							name="acceptStatusValueArray"></td>
						<td>
						<s:if test="csAcceptInfoEntryVO.pageFlag == 2">
							<!-- 投保人变更  --> <s:if test="serviceCode=='AE'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ae/loadAEPage_PA_csEndorseAEAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:if> <!-- 指定第二投保人  --> <s:elseif test="serviceCode=='SH'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_sh/loadSHPage_PA_csEndorseSHAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保费自垫申请和终止  --> <s:elseif test="serviceCode=='AP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ap/loadAPPage_PA_csEndorseAPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 受益人变更  --> <s:elseif test="serviceCode=='BC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_bc/loadBCPage_PA_csEndorseBCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&acceptCode=${acceptCode}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 客户基本变更 --> <s:elseif test="serviceCode=='CC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cc/loadCCPage_PA_csEndorseCCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 关联银行卡  --> <s:elseif test="serviceCode=='CD'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cd/LoadCDPage_PA_csEndorseCDAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单冻结  --> <s:elseif test="serviceCode=='CF'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cf/loadCFPage_PA_csEndorseCFAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 退保  --> <s:elseif test="serviceCode=='CT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ct/loadCTPage_PA_csEndorseCTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 客户重要资料变更  --> <s:elseif test="serviceCode=='CM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cm/loadCMPage_PA_csEndorseCMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单质押第三方解付  --> <s:elseif test="serviceCode=='CP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cp/loadCPPage_PA_csEndorseCPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单质押第三方止付  --> <s:elseif test="serviceCode=='CS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cs/loadCSPage_PA_csEndorseCSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 协议退保  --> <s:elseif test="serviceCode=='XT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xt/loadXTPage_PA_csEndorseXTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 公司解约  --> <s:elseif test="serviceCode=='EA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ea/loadEAPage_PA_csEndorseEAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单解冻  --> <s:elseif test="serviceCode=='CW'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cw/loadCWPage_PA_csEndorseCWAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 新增附加特约  --> <s:elseif test="serviceCode=='DA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_da/loadDAPage_PA_csEndorseDAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 附加险满期不续保  --> <s:elseif test="serviceCode=='EN'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_en/loadENPage_PA_csEndorseENAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> 
							<!-- 续保状态变更 -->
							<s:elseif test="serviceCode=='ES'">
							<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_es/loadESPage_PA_csEndorseESAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
							</div>
							</s:elseif>
							<!-- 客户层交易密码密码设置、修改 --> <s:elseif test="serviceCode=='FK'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_fk/loadFKPage_PA_csEndorseFKAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 领取方式变更  --> <s:elseif test="serviceCode=='GM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gm/loadGMPage_PA_csEndorseGMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 增补告知  --> <s:elseif test="serviceCode=='HI'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_hi/loadHIPage_PA_csEndorseHIAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 客户职业类别变更  --> <s:elseif test="serviceCode=='IO'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_io/loadIOPage_PA_csEndorseIOAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 基本信息备注项变更 --> <s:if test="serviceCode=='MC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_mc/loadMCPage_PA_csEndorseMCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab"></a>
								</div>
							</s:if> <!-- 主险续保  --> <s:elseif test="serviceCode=='MR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_mr/loadMRPage_PA_csEndorseMRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单解挂  --> <s:elseif test="serviceCode=='PF'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pf/loadPFPage_PA_csEndorsePFAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单挂失  --> <s:elseif test="serviceCode=='PL'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pl/loadPLPage_PA_csEndorsePLAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 新增可选责任  --> <s:elseif test="serviceCode=='XD'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xd/loadXDPage_PA_csEndorseXDAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 新增附加险 --> <s:elseif test="serviceCode=='NS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ns/loadNSPage_PA_csEndorseNSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单补发 --> <s:elseif test="serviceCode=='LR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_lr/loadLRPage_PA_csEndorseLRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保险起期变更（个人）--> <s:elseif test="serviceCode=='YS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ys/loadYSPage_PA_csEndorseYSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 生存保险金追回 --> <s:elseif test="serviceCode=='RG'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rg/loadRGPage_PA_csEndorseRGAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--加保--> <s:elseif test="serviceCode=='PA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pa/loadPAPage_PA_csEndorsePAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--减保--> <s:elseif test="serviceCode=='PT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked2(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pt/loadPTPage_PA_csEndorsePTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单贷款--> <s:elseif test="serviceCode=='LN'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ln/loadLNPage_PA_csEndorseLNAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单贷款清偿--> <s:elseif test="serviceCode=='RF'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rf/loadRFPage_PA_csEndorseRFAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单贷款清偿--> <s:elseif test="serviceCode=='RL'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rl/loadRLPage_PA_csEndorseRLAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--交费方式及期限变更--> <s:elseif test="serviceCode=='FM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_fm/loadFMPage_PA_csEndorseFMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--变更目的地国家--> <s:elseif test="serviceCode=='MD'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_md/loadMDPage_PA_csEndorseMDAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&appectCode=${acceptCode}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单复效--> <s:elseif test="serviceCode=='RE'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_re/loadREPage_PA_csEndorseREAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--累积生息账户领取与注销--> <s:elseif test="serviceCode=='AI'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ai/loadAIPage_PA_csEndorseAIAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单迁移无录入标识--> <s:elseif test="serviceCode=='PR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pr/loadPRPage_PA_csEndorsePRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--附加险满期降低保额续保--> <s:elseif test="serviceCode=='ER'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_er/loadERPage_PA_csEndorseERAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取年龄变更--> <s:elseif test="serviceCode=='GB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gb/loadGBPage_PA_csEndorseGBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取年龄变更--> <s:elseif test="serviceCode=='PC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pc/loadPCPage_PA_csEndorsePCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--年金满期金给付--> <s:elseif test="serviceCode=='AG'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ag/loadAGPage_PA_csEndorseAGAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}&abnormalPayFlag=${tt.abnormalPayFlag}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--附加特约终止--> <s:elseif test="serviceCode=='DT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_dt/loadDTPage_PA_csEndorseDTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取年龄变更--> <s:elseif test="serviceCode=='GB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gb/loadGBPage_PA_csEndorseGBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取形式变更--> <s:elseif test="serviceCode=='GC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gc/loadGCPage_PA_csEndorseGCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取日期变更--> <s:elseif test="serviceCode=='LC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_lc/loadLCPage_PA_csEndorseLCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保障计划约定变更--> <s:elseif test="serviceCode=='XX'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xx/loadXXPage_PA_csEndorseXXAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--减额交清/险种转换--> <s:elseif test="serviceCode=='PU'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pu/loadPUPage_PA_csEndorsePUAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--TPA直付卡补发--> <s:elseif test="serviceCode=='SP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_sp/loadSPPage_PA_csEndorseSPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--自垫清偿--> <s:elseif test="serviceCode=='TR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_tr/loadTRPage_PA_csEndorseTRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--新增领取责任--> <s:elseif test="serviceCode=='AZ'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_az/loadAZPage_PA_csEndorseAZAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--续保险种转换--> <s:elseif test="serviceCode=='RR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rr/loadRRPage_PA_csEndorseRRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--转赠养老金--> <s:elseif test="serviceCode=='TA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ta/loadTAPage_PA_csEndorseTAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险/投连险账户部分领取--> <s:elseif test="serviceCode=='PG'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pg/loadPGPage_PA_csEndorsePGAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--追加保费--> <s:elseif test="serviceCode=='AM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_am/loadAMPage_PA_csEndorseAMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险基本保额约定变更--> <s:elseif test="serviceCode=='DC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_dc/loadDCPage_PA_csEndorseDCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保全回退--> <s:elseif test="serviceCode=='RB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rb/queryRBcsHistory_PA_csEndorseRBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险基本保额减少--> <s:elseif test="serviceCode=='CB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cb/loadCBPage_PA_csEndorseCBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险基本保额增加--> <s:elseif test="serviceCode=='CA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_ca/loadCAPage_PA_csEndorseCAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--投连险退保--> <s:elseif test="serviceCode=='IT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_it/loadITPage_PA_csEndorseITAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&policyChgId=${policyChgId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 投连险投资账户转换 --> <s:elseif test="serviceCode=='TI'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_ti/loadTIPage_PA_csEndorseTIAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单复缴 --> <s:elseif test="serviceCode=='RA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_ra/loadRAPage_PA_csEndorseRAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 柜面认证 --> <s:elseif test="serviceCode=='RZ'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_rz/loadRZPage_PA_csEndorseRZAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 续期回退--> <s:elseif test="serviceCode=='XQ'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xq/loadXQPage_PA_csEndorseXQAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 红利领取 --> <s:elseif test="serviceCode=='GR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gr/loadGRPage_PA_csEndorseGRAction.action?&menuId=${menuId}&acceptId=

										${acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 红利领取形式变更 --> <s:elseif test="serviceCode=='HC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_hc/loadHCPage_PA_csEndorseHCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 社保状态变更--> <s:elseif test="serviceCode=='SO'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_so/loadSOPage_PA_csEndorseSOAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 特殊复效--> <s:elseif test="serviceCode=='SR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_sr/loadSRPage_PA_csEndorseSRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 保单关联 -->
							 <s:elseif test="serviceCode=='RS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rs/loadRSPage_PA_csEndorseRSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 重新出单 -->
							 <s:elseif test="serviceCode=='RN'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> 
									<a style='display: none'
										href="${ctx}/cs/serviceitem_rn/loadRNPage_PA_csEndorseRNAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&accectCode=${acceptCode}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 受益人变更（信托）  --> 
							<s:elseif test="serviceCode=='TC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> 
									<a style='display: none'
										href="${ctx}/cs/serviceitem_tc/loadTCPage_PA_csEndorseTCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&acceptCode=${acceptCode}&queryFlag=${csAcceptInfoEntryVO.pageFlag-1}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
						</s:if>
						<s:else>
						<div <s:if test="acceptStatus == '05' ||  acceptStatus == '06' || acceptStatus == '07' || acceptStatus == '01' ">style="display:block"</s:if>
						 <s:else>style="display:none"</s:else>>
							<!-- 投保人变更  --> <s:if test="serviceCode=='AE'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ae/loadAEPage_PA_csEndorseAEAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:if> <!-- 指定第二投保人  --> <s:elseif test="serviceCode=='SH'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_sh/loadSHPage_PA_csEndorseSHAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保费自垫申请和终止  --> <s:elseif test="serviceCode=='AP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ap/loadAPPage_PA_csEndorseAPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 受益人变更  --> <s:elseif test="serviceCode=='BC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_bc/loadBCPage_PA_csEndorseBCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&acceptCode=${acceptCode}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 客户基本变更 --> <s:elseif test="serviceCode=='CC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cc/loadCCPage_PA_csEndorseCCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 关联银行卡  --> <s:elseif test="serviceCode=='CD'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cd/LoadCDPage_PA_csEndorseCDAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单冻结  --> <s:elseif test="serviceCode=='CF'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cf/loadCFPage_PA_csEndorseCFAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 退保  --> <s:elseif test="serviceCode=='CT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ct/loadCTPage_PA_csEndorseCTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 客户重要资料变更  --> <s:elseif test="serviceCode=='CM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cm/loadCMPage_PA_csEndorseCMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单质押第三方解付  --> <s:elseif test="serviceCode=='CP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cp/loadCPPage_PA_csEndorseCPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单质押第三方止付  --> <s:elseif test="serviceCode=='CS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cs/loadCSPage_PA_csEndorseCSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 协议退保  --> <s:elseif test="serviceCode=='XT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xt/loadXTPage_PA_csEndorseXTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 公司解约  --> <s:elseif test="serviceCode=='EA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ea/loadEAPage_PA_csEndorseEAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单解冻  --> <s:elseif test="serviceCode=='CW'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cw/loadCWPage_PA_csEndorseCWAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 新增附加特约  --> <s:elseif test="serviceCode=='DA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_da/loadDAPage_PA_csEndorseDAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 附加险满期不续保  --> <s:elseif test="serviceCode=='EN'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_en/loadENPage_PA_csEndorseENAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> 
							<!-- 续保状态变更 -->
							<s:elseif test="serviceCode=='ES'">
							<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_es/loadESPage_PA_csEndorseESAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
							</div>
							</s:elseif>
							<!-- 客户层交易密码密码设置、修改 --> <s:elseif test="serviceCode=='FK'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_fk/loadFKPage_PA_csEndorseFKAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 领取方式变更  --> <s:elseif test="serviceCode=='GM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gm/loadGMPage_PA_csEndorseGMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 增补告知  --> <s:elseif test="serviceCode=='HI'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_hi/loadHIPage_PA_csEndorseHIAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 客户职业类别变更  --> <s:elseif test="serviceCode=='IO'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_io/loadIOPage_PA_csEndorseIOAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 基本信息备注项变更 --> <s:if test="serviceCode=='MC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_mc/loadMCPage_PA_csEndorseMCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab"></a>
								</div>
							</s:if> <!-- 主险续保  --> <s:elseif test="serviceCode=='MR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_mr/loadMRPage_PA_csEndorseMRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单解挂  --> <s:elseif test="serviceCode=='PF'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pf/loadPFPage_PA_csEndorsePFAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单挂失  --> <s:elseif test="serviceCode=='PL'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pl/loadPLPage_PA_csEndorsePLAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 新增可选责任  --> <s:elseif test="serviceCode=='XD'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xd/loadXDPage_PA_csEndorseXDAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 新增附加险 --> <s:elseif test="serviceCode=='NS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ns/loadNSPage_PA_csEndorseNSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单补发 --> <s:elseif test="serviceCode=='LR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_lr/loadLRPage_PA_csEndorseLRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保险起期变更（个人）--> <s:elseif test="serviceCode=='YS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ys/loadYSPage_PA_csEndorseYSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 生存保险金追回 --> <s:elseif test="serviceCode=='RG'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rg/loadRGPage_PA_csEndorseRGAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--加保--> <s:elseif test="serviceCode=='PA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pa/loadPAPage_PA_csEndorsePAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--减保--> <s:elseif test="serviceCode=='PT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked2(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pt/loadPTPage_PA_csEndorsePTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单贷款--> <s:elseif test="serviceCode=='LN'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ln/loadLNPage_PA_csEndorseLNAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--投保人变信托--> <s:elseif test="serviceCode=='AL'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_al/loadALPage_PA_csEndorseALAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单贷款清偿--> <s:elseif test="serviceCode=='RF'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rf/loadRFPage_PA_csEndorseRFAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单贷款清偿--> <s:elseif test="serviceCode=='RL'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rl/loadRLPage_PA_csEndorseRLAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--交费方式及期限变更--> <s:elseif test="serviceCode=='FM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_fm/loadFMPage_PA_csEndorseFMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--变更目的地国家--> <s:elseif test="serviceCode=='MD'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_md/loadMDPage_PA_csEndorseMDAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&appectCode=${acceptCode}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单复效--> <s:elseif test="serviceCode=='RE'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_re/loadREPage_PA_csEndorseREAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--累积生息账户领取与注销--> <s:elseif test="serviceCode=='AI'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ai/loadAIPage_PA_csEndorseAIAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保单迁移无录入标识--> <s:elseif test="serviceCode=='PR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pr/loadPRPage_PA_csEndorsePRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--附加险满期降低保额续保--> <s:elseif test="serviceCode=='ER'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_er/loadERPage_PA_csEndorseERAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取年龄变更--> <s:elseif test="serviceCode=='GB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gb/loadGBPage_PA_csEndorseGBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取年龄变更--> <s:elseif test="serviceCode=='PC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pc/loadPCPage_PA_csEndorsePCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--年金满期金给付--> <s:elseif test="serviceCode=='AG'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ag/loadAGPage_PA_csEndorseAGAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&abnormalPayFlag=${tt.abnormalPayFlag}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--附加特约终止--> <s:elseif test="serviceCode=='DT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_dt/loadDTPage_PA_csEndorseDTAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取年龄变更--> <s:elseif test="serviceCode=='GB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gb/loadGBPage_PA_csEndorseGBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取形式变更--> <s:elseif test="serviceCode=='GC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gc/loadGCPage_PA_csEndorseGCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--领取日期变更--> <s:elseif test="serviceCode=='LC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_lc/loadLCPage_PA_csEndorseLCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保障计划约定变更--> <s:elseif test="serviceCode=='XX'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xx/loadXXPage_PA_csEndorseXXAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--减额交清/险种转换--> <s:elseif test="serviceCode=='PU'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pu/loadPUPage_PA_csEndorsePUAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--TPA直付卡补发--> <s:elseif test="serviceCode=='SP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_sp/loadSPPage_PA_csEndorseSPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--自垫清偿--> <s:elseif test="serviceCode=='TR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_tr/loadTRPage_PA_csEndorseTRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--新增领取责任--> <s:elseif test="serviceCode=='AZ'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_az/loadAZPage_PA_csEndorseAZAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--续保险种转换--> <s:elseif test="serviceCode=='RR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rr/loadRRPage_PA_csEndorseRRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--转赠养老金--> <s:elseif test="serviceCode=='TA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ta/loadTAPage_PA_csEndorseTAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险/投连险账户部分领取--> <s:elseif test="serviceCode=='PG'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_pg/loadPGPage_PA_csEndorsePGAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--追加保费--> <s:elseif test="serviceCode=='AM'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_am/loadAMPage_PA_csEndorseAMAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险基本保额约定变更--> <s:elseif test="serviceCode=='DC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_dc/loadDCPage_PA_csEndorseDCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--保全回退--> <s:elseif test="serviceCode=='RB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rb/queryRBcsHistory_PA_csEndorseRBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险基本保额减少--> <s:elseif test="serviceCode=='CB'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_cb/loadCBPage_PA_csEndorseCBAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--万能险基本保额增加--> <s:elseif test="serviceCode=='CA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_ca/loadCAPage_PA_csEndorseCAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!--投连险退保--> <s:elseif test="serviceCode=='IT'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_it/loadITPage_PA_csEndorseITAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&policyChgId=${policyChgId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 投连险投资账户转换 --> <s:elseif test="serviceCode=='TI'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_ti/loadTIPage_PA_csEndorseTIAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 保单复缴 --> <s:elseif test="serviceCode=='RA'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_ra/loadRAPage_PA_csEndorseRAAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 柜面认证 --> <s:elseif test="serviceCode=='RZ'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="cs/serviceitem_rz/loadRZPage_PA_csEndorseRZAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 续期回退--> <s:elseif test="serviceCode=='XQ'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_xq/loadXQPage_PA_csEndorseXQAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 红利领取 --> <s:elseif test="serviceCode=='GR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_gr/loadGRPage_PA_csEndorseGRAction.action?&menuId=${menuId}&acceptId=

										${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 红利领取形式变更 --> <s:elseif test="serviceCode=='HC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_hc/loadHCPage_PA_csEndorseHCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> 
							<!-- 延长宽限期 -->
							 <s:elseif test="serviceCode=='EP'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_ep/loadEPPage_PA_csEndorseEPAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 交费标准变更 -->
							 <s:elseif test="serviceCode=='FS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_fs/loadFSPage_PA_csEndorseFSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 社保状态变更--> <s:elseif test="serviceCode=='SO'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_so/loadSOPage_PA_csEndorseSOAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> <!-- 特殊复效--> <s:elseif test="serviceCode=='SR'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_sr/loadSRPage_PA_csEndorseSRAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 保单关联 -->
							 <s:elseif test="serviceCode=='RS'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_rs/loadRSPage_PA_csEndorseRSAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 重新出单 -->
							 <s:elseif test="serviceCode=='RN'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> 
									<a style='display: none'
										href="${ctx}/cs/serviceitem_rn/loadRNPage_PA_csEndorseRNAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&accectCode=${acceptCode}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							<!-- 重新出单 -->
							 <s:elseif test="serviceCode=='UC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> 
									<a style='display: none'
										href="${ctx}/cs/serviceitem_uc/loadUCPage_PA_csEndorseUCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&accectCode=${acceptCode}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif> 
							
							<!-- 受益人变更（信托）  --> <s:elseif test="serviceCode=='TC'">
								<div style="margin: 0 0 0 36px">
									<a href="javascript:void(0);"
										onclick="_check_radioChecked(this)" class="btnEdit"></a> <a
										style='display: none'
										href="${ctx}/cs/serviceitem_tc/loadTCPage_PA_csEndorseTCAction.action?&menuId=${menuId}&acceptId=${tt.acceptId}&changeId=${changeId}&customerId=${customerId}&acceptCode=${acceptCode}"
										title="保全录入" rel="${menuId}" target="navTab" class="btnEdit"></a>
								</div>
							</s:elseif>
							
							
							</div>
						</s:else>
						</td>
						<td>
							<button type="button" class="but_blue" acceptcode='${acceptCode}' acceptStatus='${acceptStatus}'
								onclick="acceptPrint(this, 'view')">预览</button>
								<button type="button" class="but_blue" acceptcode='${acceptCode}' acceptStatus='${acceptStatus}'
								onclick="acceptPrint(this, 'print')">打印</button>
						</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	</div>
	
</div>
<div id="printDataSignDiv" style="visibility: hidden"></div>
<script type="text/javascript">
	$(function() {
		var $dataTable = $("#csEntryTable", navTab.getCurrentPanel());
		var $trs = $dataTable.find("tbody tr");
		$trs.each(function() {
			var $tds = $(this).find("td");
			if ($tds.eq(1).children().val() == $tds.eq(3).children().val()) {
				var acceptStatu = $tds.find("input[type='radio']");
				acceptStatu.attr("checked", "checked");
			} else {
			}
// 			console.info($(this).find("td input[name='acceptStatusValueArray']").val());
			// 保全撤销  按钮不可点击
			if($(this).find("td input[name='acceptStatusValueArray']").val() == '12'){
				$(this).find("td input[name='acceptStatusValueArray']").parent("td").next().find("a").eq(0).attr("disabled","true");
			}
		});
	});
	function showPremArapInfo(obj) {
		var acceptID = $(obj).val();
		//保全受理状态
		var acceptStatu = $(obj).parent().parent().find("td:eq(5)").children()
				.val();
		//该受理下是否存在补退费 1：存在
		var premSaves = $(obj).parent().parent().find("td:eq(4)").children()
				.val();
		if ((acceptStatu == '05' || acceptStatu == '06') && premSaves != '1') {
			alertMsg.info("该受理下尚无补退费信息。");
			return false;
		}
		$("#acceptId", navTab.getCurrentPanel()).val(acceptID);
		var $form = $("#checkPremInfoForm", navTab.getCurrentPanel());
		$form.submit();
	}
	function acceptPrint(obj,viewOrPrint) {
		//获取一个受理号 先获取这个表格
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var accpetCodeString = $(obj).attr("acceptcode");
		var acceptStatus = $(obj).attr("acceptStatus");
		if(acceptStatus!="07"){
			alertMsg.error("必须在录入完成后进行打印或预览");
			return false;
		}
// 		var $dataTable = $("#CsChangeInfoTable", navTab.getCurrentPanel());
		//获取表格中选中的tr
// 		var $radio = $dataTable.find(":radio:checked");
// 		if ($radio.size() == 0) {//如果没选就默认选第一个
// 			var $radioFirst = $dataTable.find(":radio:eq(0)");
// 			accpetCodeString = $radioFirst.val();
// 		} else {
// 			accpetCodeString = $radio.val();
// 		}
		$.ajax({
				type : "POST",
				url : "${ctx}/cs/applyvoucher/printCsApplyVoucher_PA_csEndorseApplyVoucherAction.action",
				data : "changeId=" + changeId + "&acceptCode=" + accpetCodeString + "&viewOrPrint=" + viewOrPrint,
				dataType : 'json',
				async : false,
				success : function(data) {
					if (data.msgVO.successed) {
												$("#printDataSignDiv", navTab.getCurrentPanel()).show().html(

										"<object  type='application/x-java-applet' ><param name=\"code\" value=\"SipRpcltApplet.class\" />"
												+ "<param name=\"archive\" value=\"SipRpclt.jar\" /><param name=\"reqId\" value='"
												+ data.msgVO.reqId
												+ "' /><param name=\"server\" value='"
												+ data.msgVO.server
												+ "'/><param name=\"operation\" value='"
												+ data.msgVO.printType
												+ "' /></object>");
						if (viewOrPrint == 'view') {//如果是预览
							//不提示
						} else {
							alertMsg.correct('打印免填单申请书成功！');
							//打印成功后不可再录入
							$(obj).parent().parent().find("td:eq(5)").find("a").attr("disabled","disabled");
						}
						$("#printDataSignDiv", navTab.getCurrentPanel()).hide();
					} else {
						if (viewOrPrint == 'view') {//如果是预览
							alertMsg.error(data.msgVO.message);
						} else {
							alertMsg.error(data.msgVO.message);
						}
					}
				}
			});

	}
	
</script>