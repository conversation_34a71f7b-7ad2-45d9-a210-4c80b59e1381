<!--guyy_wb 保全录入-受理和补退费信息-补退费信息-客户银行账号div-->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<div class="_cs_divDel" id="customerAccountInfo">
	<div class="panel">
		<h1>客户银行账号</h1>
		<div class="pageFormContent">
			<table class="list" style="width:100%" id="csCustomerBank" table_saveStatus="0">
				<thead>
					<tr>
						<th colName="t1" inputType="input">选择</th>
						<th colName="t2" inputType="input">客户姓名</th>
						<th colName="t3" inputType="input">开户银行</th>
						<th colName="t4" inputType="input">银行账号</th>
						<th colName="t5" inputType="input">户名</th>
						<th colName="t6">账号是否经过验证</th>
						<th colName="t7">删除/修改</th>
					</tr>
				</thead>
				<tbody  addRole="appendTo"  addTableGroup="csCustomerBankInfo">
					<s:iterator>
					<s:if test="1==1"><!-- 不是此次受理增加的银行账户 -->
						<tr align="center" id="showTr" tr_saveStatus="0">
							<td>
								<input type="radio" id="" name="r1" value="1" transmitTo="thBankAccount"/>													
							</td>
							<td>王某某</td>
							<td>中国工商银行</td>
							<td>6222020403027602099</td>
							<td>王某某</td>
							<td>是</td>
							<td>
							</td>
						</tr>
					</s:if>
<%-- 												<s:elseif test="1==1"><!-- 此次受理增加的银行账户 --> --%>
						<tr align="center" id="showTr" tr_saveStatus="0">
							<td>
								<input type="radio" id="" name="r1" value="2"  transmitTo="thBankAccount"/>													
							</td>
							<td>王某某</td>
							<td>
								<input disabled="disabled"  style="width:70px;float: left;margin-left: 5px;" value=""/>
								<input readonly="readonly"  value="中国工商银行" style="float: left;margin-left: 5px"/></td>
							<td><input disabled="disabled" style="width:200px" value='6222020403027602099'/></td>
							<td><input disabled="disabled" value='王某某'/></td>
							<td>是</td>
							<td>
								<s:if test="1==1">
									<div style="margin:0 0 0 25px">
										<a href="#" class="btnEdit" onclick="_cs_trEdit(this)"></a>
									</div>
									<div style="margin:0 0 0 25px">
										<a href="javascript:void(0)" url="cs/csEntry/delCustBankAcc_PA_csEntryAction.action?customerId=123&&accountId=123" class="btnDel" onclick="_cs_divDel(this)"></a>
									</div>
								</s:if>
							</td>
						</tr>
<%-- 												</s:elseif> --%>
					</s:iterator>
						<!-- ///////////////table增加的行///////begin////////// -->
						<tr align="center" addTableGroup="csCustomerBankInfo" style="display: none" addRole="element" tr_saveStatus="1">
							<td>
								<input type="radio" id="" name="r1" value=""/>													
							</td>
							<td>王某某</td>
							<td>
								<input  class="bankCode" style="width:70px;float: left;margin-left: 5px;"/><input readonly="readonly" style="float: left;margin-left: 5px"/>
							</td>
							<td><input /></td>
							<td><input/></td>
							<td>否</td>
							<td>
								<div style="margin:0 0 0 25px">
									<a href="#" class="btnEdit" onclick="editBankCode(this)"></a>
								</div>
								<div style="margin:0 0 0 25px">
									<a href="#" class="btnDel" onclick="delBankCode(this)"></a>
								</div>
							</td>
						</tr>
						<!-- ///////////////table增加的行///////begin////////// -->
				</tbody>
			</table>
		</div>
			<div class="panelBar">
				<div>
					<a addTableGroup="csCustomerBankInfo" href="javascript:appendElement('csCustomerBankInfo');" class="btnAdd">as</a>
				</div>
				<div style="float:left;margin:4px 0 0 4px">
					<span><b>增加银行账号</b></span>
				</div>
				<div class="button" style="margin-left:10px;float:right;margin-right:40px;">
					<div class="buttonContent">
						<button type="button" onclick="_cs_table_save('customerAccountInfo')">保存</button>
					</div>
				</div>
			</div>			
	</div>
</div>
<object classid="CLSID:C08148B7-2779-4924-B6ED-93E51C6A6039"
        codebase="${ctx}/plugins/MxDevice.ocx" id="fpDevObj" height=0 width=0> </object>