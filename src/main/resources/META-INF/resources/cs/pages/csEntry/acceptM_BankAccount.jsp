<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/jquery.doublecheckplugins.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx }/udmp/scripts/common.js" ></script>
<div  id="saveResult">
  <div class="main_clearfix">
    <div class="divfclass">
		<h1>客户银行账号</h1>
	</div>
	<div id="bankListResult">
		<form action="" class="required-validate"  onsubmit="" id="formBody" method="post">
			<!-- 隐藏域传值 -->
			<input type="hidden" name="acceptId" value="${acceptId}" id="bankAcceptId"></input> <input
				type="hidden" name="changeId" value="${changeId}" id="bankChangeId"></input> <input
				type="hidden" name="customerId" value="${customerId}"></input> <input
				type="hidden" name="customerName" value="${customerName}"></input> <input
				type="hidden" name="url" value="${url}"></input> <input
				type="hidden" name="title" value="${title}"></input> <input
				type="hidden" name="pageId" value="${pageId}"></input> <input
				type="hidden" name="jsMethod" value="${jsMethod}"></input> <input
				type="hidden" name="planId" value="${planId}"></input><input
				type="hidden" id="issueBankNames" name="issueBankNames" value=""></input>
			<input type="hidden" id="bankCodes" name="bankCodes" value=""></input>
			<input type="hidden" id="bankAccounts" name="bankAccounts" value=""></input>
			<input type="hidden" id="accoNames" name="accoNames" value=""></input>
			<input type="hidden" id="deleteAccountIds" name="deleteAccountIds"
				value=""></input><input type="hidden" name="activeId" id="activeId"
				value="${activeId}${customerId}"></input>
			<table class="list" width="100%">
				<thead>
					<tr>
						<th>选择</th>
						<th style="display: none">客户id</th>
						<th>客户姓名</th>
						<th>银行代码/开户银行</th>
						<th>银行账户</th>
						<th>户名</th>
						<th>账号是否经过验证</th>
						<th>账户等级</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="${activeId}${customerId}" class="t_body">
					<s:iterator value="csBankAccountVOList" status="st"
						var="csBankAccountVO">
						<tr>
							<td align="center">
								<s:if test="#st.count==1">
									<input type="radio" name="bankAccountId" checked = "checked"
										class="bankAccountId" value="${accountId}" />
								</s:if>
								<s:else>
									<input type="radio" name="bankAccountId" 
										class="bankAccountId" value="${accountId}" />
								</s:else>
									
								</td>
							<td style="display: none">${customerId}</td>
							<td align="center" id="qy_customerName">${customerName}</td>
							<td align="center">${bankCode}/<Field:codeValue
									tableName="APP___PAS__DBUSER.T_BANK" value="${bankCode}" /></td>
							<td align="center"><span >${bankAccount }</span></td>
							<td align="center">${accoName }</td>
							<td align="center"><Field:codeValue
								tableName="APP___PAS__DBUSER.t_yes_no" value="${verifiedFlag==null? '0' : verifiedFlag}" /></td>
							<td align="center">
								<s:if test="safeAccountType==null || safeAccountType=='' ">0</s:if>
								<s:else>${safeAccountType}</s:else>
							</td>
							<td align="center">
							<s:if test="#csBankAccountVO.operationType==1">
								<a class="btnDel" onclick="deleteAccountLine(this)"></a>
							</s:if>
							</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</form>
	</div>
	</div>


		<div style="margin-top: 8px; float: left; margin-left: 20px">
			<font style="size: 10;">开户银行：</font>
		</div>
		<div style="margin-top: 5px; float: left;">
		<%-- <select cssClass="combox" id="bankCode${customerId}">
			<option value="">请选择</option>
			<s:iterator value="bankCodeByOrgans" status="st">
				<option value="${bankCode}">${bankCode}-${bankUserName}</option>
			</s:iterator>
		</select> --%>
		
		<input id = "orgcodeid" type="hidden" value="<s:property value="#session.appUser.organCode"/>" />
		<input id="bankinfocodeid" type="text" class="autocomplete textLeft"
						value="" data-width="250px"
						data-showValue="bankInfo${customerId}" data-mode="2"
						data-disCurrentValFlag="1"
						data-tableName="APP___PAS__DBUSER.T_BANK A"
						data-tableCol="BANK_CODE"
						data-tableDesc="BANK_CODE||' - '||BANK_NAME"
						data-separator=" - " data-like="3" data-orderBy="BANK_CODE"
						data-view="BANK_NAME" data-disUpShowFlag="1"/> 
		<input type="text" id="bankInfo${customerId}" class="textRight" readOnly style="width: 200px;"/>
			
<%-- 			<Field:codeTable cssClass="combox" nullOption="true" name="" --%>
<%-- 				tableName="APP___PAS__DBUSER.T_BANK" value="" id="bankCode${customerId}" /> --%>
		</div>
		<!-- <div style="margin-top: 8px; float: left; margin-left: 20px" > -->
		<div   style="margin-top: 8px; float: left; margin-left: 15px">
			<button type="button" class="but_blue" onclick="_csEnt_addAccount(this)" >+增加银行账号</button>
			<button type="button"  class="but_blue" onclick="_csEnt_newBank_save(this)" >保存修改</button>

		</div>
		
		<%-- <div style="margin-top: 5px; float: right;">
			<input type="button" value="确认并返回" onclick="${jsMethod}" />
		</div> --%>

</div>

<script type="text/javascript">
$(function() {
	
	/**
	
	var payMode = $("#payMode",navTab.getCurrentPanel()).val();
	var sessionorgcode = $("#orgcodeid", navTab.getCurrentPanel()).val();
	if(sessionorgcode.length>=4){
		var org_code = sessionorgcode.substring(0,4);
		$("#bankinfocodeid", navTab.getCurrentPanel()).attr("data-whereClause","IS_CREDIT_CARD='0' "+
				"AND (EXISTS (SELECT 1 FROM DEV_PAS.t_Bank_Orgen_Rel B WHERE A.BANK_CODE=B.BANK_CODE AND "+
				"EXISTS(SELECT ORGAN_CODE FROM (SELECT C.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL C WHERE C.ORGAN_CODE='860000' OR C.ORGAN_CODE LIKE "+"'"+org_code+"%'"+") E "+ 
						"WHERE 1=1 AND B.ORGAN_CODE = E.ORGAN_CODE)) OR PAY_MODE like '%"+payMode+"%')");
		
	}else{
		$("#bankinfocodeid", navTab.getCurrentPanel()).attr("data-whereClause","IS_CREDIT_CARD='0' "+
				"AND (EXISTS (SELECT 1 FROM DEV_PAS.t_Bank_Orgen_Rel B WHERE A.BANK_CODE=B.BANK_CODE AND "+
						"EXISTS(SELECT ORGAN_CODE FROM (SELECT C.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL C WHERE C.ORGAN_CODE='860000' ) E "+ 
								"WHERE 1=1 AND B.ORGAN_CODE = E.ORGAN_CODE)) OR PAY_MODE like '%"+payMode+"%')");

	}
	*/
	var payMode = $("#payMode",navTab.getCurrentPanel()).val();
	//alert(payMode+"=========");
	bankChange(payMode);
	
});
	
	function bankChange(obj){
		debugger;
		//alert("tttttt"+obj);
		var sessionorgcode = $("#orgcodeid", navTab.getCurrentPanel()).val();
		if(sessionorgcode.length>=4){
			var org_code = sessionorgcode.substring(0,4);
			$("#bankinfocodeid", navTab.getCurrentPanel()).attr("data-whereClause","IS_CREDIT_CARD='0' "+
					"AND (EXISTS (SELECT 1 FROM DEV_PAS.t_Bank_Orgen_Rel B WHERE A.BANK_CODE=B.BANK_CODE AND "+
					"EXISTS(SELECT ORGAN_CODE FROM (SELECT C.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL C WHERE C.ORGAN_CODE='860000' OR C.ORGAN_CODE LIKE "+"'"+org_code+"%'"+") E "+ 
							"WHERE 1=1 AND B.ORGAN_CODE = E.ORGAN_CODE)) OR PAY_MODE like '%"+obj+"%')");
			
		}else{
			//alert("ttttnnn"+obj);
			$("#bankinfocodeid", navTab.getCurrentPanel()).attr("data-whereClause","IS_CREDIT_CARD='0' "+
					"AND (EXISTS (SELECT 1 FROM DEV_PAS.t_Bank_Orgen_Rel B WHERE A.BANK_CODE=B.BANK_CODE AND "+
							"EXISTS(SELECT ORGAN_CODE FROM (SELECT C.ORGAN_CODE FROM DEV_PAS.T_UDMP_ORG_REL C WHERE C.ORGAN_CODE='860000' ) E "+ 
									"WHERE 1=1 AND B.ORGAN_CODE = E.ORGAN_CODE)) OR PAY_MODE like '%"+obj+"%')");

		}
		var $this = $("#bankinfocodeid", navTab.getCurrentPanel());
	 	udmpUtil.reAutocomplete($this);
	 	
	}

	var accountRN = 1;
	function deleteLine(line){
		$(line).parent().parent().remove();
	}
	
	function deleteAccountLine(line) {
		var parentDiv=$(line).parents("#policyPayModeInfoDiv").get(0);
		var deleteAccountIds = $("#deleteAccountIds",parentDiv).val();
		if ($(line).parents("tr").first().children("td").first().children(
				"input").first().val() != "") {
			deleteAccountIds = deleteAccountIds
					+ $(line).parents("tr").first().children("td").first()
							.children("input").first().val() + ",";
			$("#deleteAccountIds",parentDiv).val(deleteAccountIds);
			
		}
		var $form = $("#formBody",parentDiv);
		$(line).parents("tr").first().remove();
		$.ajax({			
			type:"POST",
			url:"${ctx}/cs/common/delBankAccountChangePremArap_PA_bankAccountInfoAction.action",
			data : $form.serialize(),
			success:function(date){
				
				if(date.indexOf("message")!=-1)
				{		
					var obj=JSON.parse(date);
					alertMsg.error(obj.message);
				}
				else{
					navTab.reload();
				}
			},
			error:function(date){
				
			}
		});
	}
	function _csEnt_addAccount(targe) {
		var parentDiv=$(targe).parents("#policyPayModeInfoDiv").get(0);
		var qy_customerId= $("#qy_customerId",parentDiv).val();
		var qy_customerName= $("#qy_customerName",parentDiv).val();
		accountRN = accountRN - 0 + 1;
		var tid = $("#activeId",parentDiv).val();
		var bankCode = $("#bankinfocodeid",parentDiv).val();
		var bankName = $("#bankInfo"+qy_customerId,parentDiv).val();
		if (bankCode == "" || bankName == "") {
			alertMsg.info("请先选择开户银行！");
			return false;
		}
		/* var addMsg = '<tr>'
				+ '<td align="center"><input type="radio" name="bankAccountId" value="" class="wrong"></td>'
				+ '<td align="center"><input type="text"  value="' + qyCustomerName + '"  size="8" disabled="true" /></td>'
				+ '<td align="center"><font class="bankCodes">'
				+ bankCode
				+ '</font>/'
				+ bankName
				+ '</td>'
				+ '<td align="center"><input type="expandBankAccount"  id="accountCode" class="doublecheck" size="15" /></td>'
				+ '<td align="center"><input type="text" id="accountName"  class="accoNames"  size="8" /></td>'
				+'<td align="center">否</td>'
				+ '<td align="center">'
				+ '<a class="btnDel" onclick="deleteLine(this)"></a>' + '</td>'
				+ '</tr>'; */
		var addMsg = '<tr>'
				+ '<td align="center"><input type="radio" name="bankAccountId" value="" class="wrong"></td>'
				+ '<td align="center"><input type="text"  value="' + qy_customerName + '"  size="8" disabled="true" /></td>'
				+ '<td align="center"><font class="bankCodes">'
				+ bankCode
				+ '</font>/'
				+ bankName
				+ '</td>'
				+ '<td  ><div style="margin-left:30px;"><input type="expandBankAccountNew" onkeydown="tabHandler(event,this);" onblur=checkBankDouble(this) class="required doublecheckcap exbankcardNew"  name="accountCode" id="accountCode'+qy_customerId+'" onpaste="return false" /></div></td>'
				
				+ '<td><input style="margin-left:30px;" type="text" value="'+qy_customerName+'" id="accountName"  class="accoNames"  maxlength="100" /></td>'
				
				+'<td align="center">否</td>'
				+'<td>0</td>'
				+ '<td align="center">'
				+ '<a class="btnDel" onclick="deleteLine(this)"></a>' + '</td>'
				+ '</tr>'; 
		$(addMsg).appendTo("#" + tid).initUI();
		//添加完后绑定双次校验事件liqd  $(document)
		//$("#accountCode"+qy_customerId,parentDiv).doublecheckcap();
		//console.dir($("#bankAccountInfoDiv",$("#policyPayModeInfoDiv2")));
	}
	function tabHandler(event,bank){
    var keyCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
	    if(keyCode==9){
	    	var flage=1;
			twocheckBank(event,bank,flage)
	    }
	}
	//新增银行账号输入两次校验功能  被tanyh 156 
	function checkBankDouble(bank){
		var flage=0;
		var read = $(bank).attr("readonly");
		if(read){
			//只读不再二次校验
		}else{
			twocheckBank("",bank,flage)
		}
		
	}
	
	function twocheckBank(event,bank,flage){
		var cla=$(bank).attr("class");
		   //用class来校验输入了几遍 用户输入内容，且通过其它校验情况
		  if($(bank).val().length>0 && cla.indexOf('error')<0){
			 //第一次输入
	        if(cla.indexOf('one')<0 && cla.indexOf('two')<0){
	        	$(bank).attr("tit",$(bank).val());
	        	$(bank).addClass("one");
	        	$(bank).focus();
			}else if(cla.indexOf('one')>=0){//第二次输入
				$(bank).removeClass("one");
				$(bank).addClass("two");
			}
			//根据class里面one 和two 来校验两次银行卡输入正确否
			var claa=$(bank).attr("class");
			   //第一次输入校验
				if(claa.indexOf('one')>=0){
					$(bank).val('');
					if(flage==1){
						event.returnValue=false;
					}
				}else if(claa.indexOf('two')>=0){//第二次输入校验
					if(flage==0){
						$(bank).removeClass("two");
					}
					if($(bank).val() !=$(bank).attr("tit")){//不一致校验
						if(flage==1){
							alertMsg.info("两次录入内容不一致，请重新录入。");
		 					$(bank).attr("tit",'');
		 					$(bank).val('');
		 					$(bank).removeClass("two");
		 					$(bank).focus();
						}else{
							alertMsg.info("两次录入内容不一致，请重新录入。");
							$(bank).attr("tit",'');
							$(bank).val('');
							$(bank).focus();
						}
					}else{//一致通过校验
						if(flage==0){
							$(bank).attr("tit",'');
						}
					}
				}
			}
	}
	function getData(parentDiv) {
		var deleteAccountIds = $("#deleteAccountIds").val();
		if (deleteAccountIds != "") {
			deleteAccountIds = deleteAccountIds.substring(0,
					deleteAccountIds.length - 1);
			$("#deleteAccountIds").val(deleteAccountIds);
		}

		//var issueBankName = $(".issueBankNames");
		var bankCode = $(".bankCodes",parentDiv);
		var bankAccount = $(".doublecheckcap",parentDiv);
		var accoName = $(".accoNames",parentDiv);

		//	var issueBankNames = ""
		//	for (var a = 0; a < issueBankName.length; a++) {
		//		issueBankNames = issueBankNames + issueBankName[a].value + ",";
		//	}
		//	issueBankNames = issueBankNames.substring(0, issueBankNames.length - 1);
		//	$("#issueBankNames").val(issueBankNames);

		var bankCodes = ""
		for ( var a = 0; a < bankCode.length; a++) {
			bankCodes = bankCodes + $(bankCode[a]).text() + ",";
		}
		bankCodes = bankCodes.substring(0, bankCodes.length - 1);
		$("#bankCodes",parentDiv).val(bankCodes);

		var bankAccounts = ""
		for ( var a = 0; a < bankAccount.length; a++) {
			bankAccount[a].value=bankAccount[a].value.replace(/\s+/g,"");
			bankAccounts = bankAccounts + bankAccount[a].value + ",";
		}
		bankAccounts = bankAccounts.substring(0, bankAccounts.length - 1);
		$("#bankAccounts",parentDiv).val(bankAccounts);

		var accoNames = ""
		for ( var a = 0; a < accoName.length; a++) {
			accoNames = accoNames + accoName[a].value + ",";
		}
		accoNames = accoNames.substring(0, accoNames.length - 1);
		$("#accoNames",parentDiv).val(accoNames);
	}
	/* 保全录入页面，新增客户银行账号 */
	function _csEnt_newBank_save(targe) {
		//校验录入信息完整性
		var parentDiv=$(targe).parents("#policyPayModeInfoDiv").get(0);
		var qy_customerId= $("#qy_customerId",parentDiv).val();
		var accountCode=$("#accountCode"+qy_customerId,parentDiv);
		var accountName=$("#accountName",parentDiv);
		if(!checkBankAccountId(accountCode,parentDiv))
			return;
		if(!checkBankAccountName(accountName,parentDiv))
			return;
		
		getData(parentDiv);
		var bankAcceptId=$($(".oneAcceptId",parentDiv)[0]).val();
		var bankChangeId=$($(".oneChangeId",parentDiv)[0]).val();
		//alert(bankAcceptId+","+bankChangeId);
		$("#bankAcceptId",parentDiv).val(bankAcceptId);
		$("#bankChangeId",parentDiv).val(bankChangeId);
		
		var $form = $("#formBody",parentDiv);
		var qyCustomerId = $("#qy_customerId",parentDiv).val();
		$("input[name='customerId']",$form).val(qyCustomerId);
		
		//var action = "${ctx }/cs/common/saveBankAccountChangePremArap_PA_bankAccountInfoAction.action";
		//var onsubmit = "return divSearch(this,'saveResult');";
		//$form.attr("action", action);
		//$form.attr('onsubmit', onsubmit);
		//console.dir($form.serialize());
		$.ajax({			
			type:"POST",
			url:"${ctx}/cs/common/saveBankAccountChangePremArap_PA_bankAccountInfoAction.action",
			data : $form.serialize(),
			//cache : false,
			success:function(date){
				
				if(date.indexOf("message")!=-1)
				{		
					var obj=JSON.parse(date);
					alertMsg.error(obj.message);
				}
				else{
					//alertMsg.correct("保存成功");
					//$("#saveResult #bankListResult",parentDiv).html(date);
					navTab.reload();
				}
			},
			error:function(date){
				
			}
		});
		//console.dir($form.submit());
	}
	//检查银行账号
	function checkBankAccountId(line,parentDiv) {
		if(line.size()==0)
			return true;;
		var id = line.val();
		if(""==id)
			{
				alertMsg.info("请填写银行账号");
				return false;
			}
		// 根据青岛并行bug 先去除该校验
// 		if(id.length>24){
// 			alertMsg.info("请确认银行账号长度不超过20");
// 			return false;
// 		}
		//校验账号信息
		var $queryForm = $('#formBody',navTab.getCurrentPanel());
		if(!$queryForm.valid()){
        	alertMsg.info("银行账号校验未通过！");
            return false;
        }
		var result = verificationBankCard(id);
		if (!result) {
			alertMsg.info("请输入数字");
			line.val(null);
			return false;
		}
		return true;
	}
	//检查户名
	function checkBankAccountName(line,parentDiv) {		
		if(line.size()==0)
			return true;;
		var name = line.val();
		if(""==name)
		{
			alertMsg.info("请填写户名");
			return false;
		}
		var qy_customerName = $("#qy_customerName",parentDiv).val();
		
		if (qy_customerName != name) {
			 alertMsg.warn("银行帐号户名和权益人姓名不一致，请确认。");
		}
		return true;
	}

	/**
 * liwei_wb1
 * 录入框 获取 光标 自动获取银行卡号（当文本框是空时）
 * @returns
 */
   var flag=true;
function autoGetBankCard(obj){
	if($(obj).val()==''){
		if(confirm('是否需要通过一体机自动识别银行卡来获取银行卡号？')){
			MxGetIcCardInfo(obj);//Ic卡
			if(!flag){
					readCardCT(obj);//磁条卡
			}
		}else{
			$(obj).doublecheckcap();
		}
	}
}
/**
 * liwei_wb1
 *读磁条卡
 * BSTR MxReadCard(long nPortNo, long nTimeOut, long nMode)
 *nPortNo：端口号（0-USB）
 *nTimeOut: 超时时间(毫秒)，建议10000毫秒以上
 *nMode-读取方式(2-读2磁道, 3-读3磁道, 23-读23磁道)
 */
function readCardCT(idd) {
	var ret = fpDevObjss.MxReadCard(1005, 10000, 3);
	// 			$("#commonQueryForm").find("input[value='creditCardNo']").click();
	if (ret != '-1') {
		var num = ret.indexOf("=");
		var retAf = ret.substr(0, num);
		$(idd).val(retAf);
	}else{
			flag=false;
		}
}
/**
 * liwei_wb1
 * 读IC卡
 * MxGetIcCardInfo(long nPortNo, long nCardType, BSTR szTagList);
 * nPortNo - 输入，端口号(0-USB)
 *nCardType - 输入，(0:接触, 1:非接, 2:自动)
 *szTagList - 输入，标签值，含义如下表
 * 1	 A		  账号        *
 * 2	 B	      姓名        *
 * 3	 C	      证件类型    *
 * 4	 D	      证件号码    *
 * 5	 E	      余额        *
 * 6	 F	      余额上限    *
 * 7	 G	      单笔交易限额*
 * 8	 H	      应用货币类型*
 * 9	 I	      失效日期    * 
 * 10	 J	      IC卡序列号  *
 * 11	 K	      二磁道数据  * 
 * 12	 L	      一磁道数据  *
 * ret  获取卡号数据
 */
function MxGetIcCardInfo(idd) {
		var ret = fpDevObjss.MxGetIcCardInfo(1007, 0 , "A");
		if (ret != '-1') {
			$(idd).val(ret);
		}else{
			flag=false;
		}
}
</script>
	<object classid="CLSID:C08148B7-2779-4924-B6ED-93E51C6A6039"
	codebase="${ctx}/plugins/MxDevice.ocx" id="fpDevObjss" height=0 width=0> </object>