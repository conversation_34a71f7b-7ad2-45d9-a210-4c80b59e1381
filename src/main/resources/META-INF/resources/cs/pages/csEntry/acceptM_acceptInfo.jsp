<!--guyy_wb 保全录入-受理和补退费信息-受理修改信息页面-->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png" />受理信息录入
		</h1>
	</div>
	<div id="applyInfoDiv">
		<form id="applicationInfoForm"
			action="${ctx }/cs/csAccept/recordAppInfo_PA_acceptAction.action?menuId=${menuId}"
			onsubmit="return divSearch(this,'step2BodyDiv')" method="post">
			<div class="main_clearfix">
				<div class="divfclass">
					<h1>
						<!-- <input id="showButtonId" class="main_fold"
						onclick="showButton(this,'applyInfoPanel')" value="+"
						type="button"> -->
						<a href="#" id="showButtonId"
							<s:if test="openPage==false">value='-'</s:if><s:else>value='+'</s:else>
							onclick="showButton(this,'applyInfoPanel')"><img
							src="${ctx}/cs/img/icon/three_plus.png" />申请信息</a>
					</h1>
				</div>
				<div id="applyInfoPanel"
					<s:if test="openPage==false">style="display: none;"</s:if>
					class="panelPageFormContent">
					<div class="panelPageFormContent">
						<div class="panelPageFormContent">
							<s:if test="csAcceptInfoEntryVO.customerVO.customerName !=null">
								<input type="hidden"
									name="csAcceptInfoEntryVO.csApplicationVO.customerId"
									value="${csAcceptInfoEntryVO.customerVO.customerId}" />
								<input type="hidden"
									name="csAcceptInfoEntryVO.csApplicationVO.applyName"
									value="${csAcceptInfoEntryVO.customerVO.customerName}" />
							</s:if>
							<s:else>
								<input type="hidden"
									name="csAcceptInfoEntryVO.csApplicationVO.customerId"
									value="${csAcceptInfoEntryVO.csApplicationVO.customerId}" />
								<input type="hidden"
									name="csAcceptInfoEntryVO.csApplicationVO.applyName"
									value="${csAcceptInfoEntryVO.csApplicationVO.applyName}" />
							</s:else>
							<input type="hidden"
								value="${csAcceptInfoEntryVO.csApplicationVO.changeId }"
								name="csAcceptInfoEntryVO.csApplicationVO.changeId" /> <input
								type="hidden" value="03"
								name="csAcceptInfoEntryVO.changeVO.changeSource" /> <input
								type="hidden"
								value="${csAcceptInfoEntryVO.csApplicationVO.applyCode}"
								name="csAcceptInfoEntryVO.csApplicationVO.applyCode" /> <input
								type="hidden"
								value="${csAcceptInfoEntryVO.csApplicationVO.tryCalcNo}"
								name="csAcceptInfoEntryVO.csApplicationVO.tryCalcNo" /> <input
								type="hidden" id="agentName"
								value="${csAcceptInfoEntryVO.csApplicationVO.agentName}" />
							<dl>
								<dt>申请提交日期</dt>
								<dd>
									<input name="csAcceptInfoEntryVO.csApplicationVO.applyTime"
										id="applyDate"
										value='<s:date name="csAcceptInfoEntryVO.csApplicationVO.applyTime" format="yyyy-MM-dd"/>'
										type="text" class="date required"  disabled="disabled"/> <a
										class="inputDateButton" href="javascript:;">选择</a>
								</dd>
							</dl>
							
							<dl>
								<dt>申请方式</dt>
								<dd>
									<input type="hidden" id="serviceType" 
										value="${csAcceptInfoEntryVO.csApplicationVO.serviceType}" />
									<Field:codeTable id="change_type" cssStyle="width: 155px"
										name="csAcceptInfoEntryVO.csApplicationVO.serviceType"
										value="${csAcceptInfoEntryVO.csApplicationVO.serviceType}"
										onChange="_checkShowAgentDiv_1(this)"  defaultValue="2"
										tableName="APP___PAS__DBUSER.T_SERVICE_TYPE" />
									<font color="red">*</font>
							  </dd>
							</dl>
							<dl>
								<dt>受理渠道</dt>
								<dd>
									<input type="hidden" id="sourceType"
										value="${csAcceptInfoEntryVO.csApplicationVO.sourceType}" />
									<Field:codeTable id="sourceTypeValue"
										name="csAcceptInfoEntryVO.csApplicationVO.sourceType"
										value="${csAcceptInfoEntryVO.csApplicationVO.sourceType}"
										nullOption="true" cssClass="combox"
										tableName="APP___PAS__DBUSER.T_ACCEPT_CHANNEL" />

									<input id="nofillFlag"
										name="csAcceptInfoEntryVO.csApplicationVO.nofillFlag"
										type="checkbox"
										value="${csAcceptInfoEntryVO.csApplicationVO.nofillFlag}"
										onclick="clickCheckBox(this)"
										${csAcceptInfoEntryVO.csApplicationVO.nofillFlag==1?'checked':''} />
									免填单 <input name="flag" type="hidden" id="flagHead"
										value="${csAcceptInfoEntryVO.flag}" /> <input name="taskPool"
										type="hidden" value="${csAcceptInfoEntryVO.taskPool}" />
								</dd>
							</dl>
							<dl>
								<dt></dt>
								<dd <s:if test="csAcceptInfoEntryVO.csApplicationVO.beforScanState">style="display: none;"</s:if>>
									<input name="csAcceptInfoEntryVO.csApplicationVO.isInteractiveJoint"
										<s:if test="csAcceptInfoEntryVO.csApplicationVO.isInteractiveJoint==1">checked="checked"</s:if>
										value="${csAcceptInfoEntryVO.csApplicationVO.isInteractiveJoint}"
										onclick="clickCheckBox(this)"
										type="checkbox" class="checkboxCtrl" />是否互动联勤件
								</dd>
						</dl>
							
							
						</div>
						<div id="agentInfoEntryDiv" class="agentInfoEntryDiv">
							<div class="divfclass">
								<h1>
									<img src="${ctx}/cs/img/icon/tubiao.png" />代办人信息
								</h1>
							</div>
							<div class="panelPageFormContent">
								<dl id="agentCodeDl">
									<dt>业务员代码</dt>
									<dd>
										<s:if test="contractMasterVOList.size==0">
											<font color="red">未查询到业务员代码</font>
										</s:if>
										<s:else>
											<select id="agentCode"
												name="csAcceptInfoEntryVO.csApplicationVO.agentCode"
												onchange="showAgent1()">
												<option value="0">请选择</option>
												<s:iterator value="contractMasterVOList"
													var="contractMasterVO">
													<option value="${contractMasterVO.saleAgentCode}"
													<s:if test="#contractMasterVO.saleAgentCode==csAcceptInfoEntryVO.csApplicationVO.agentCode">selected</s:if>
													>
														${contractMasterVO.saleAgentCode}
													</option>
												</s:iterator>
											</select>
										</s:else>
									</dd>
								</dl>
								<dl>
									<dt>新业务员代码</dt>
									<dd>
										<input id="agentCodeNew"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCodeNew"
											class="" type="text"
											value="" />
										<a class="btnLook" onclick="showAgent2()"></a>
									</dd>
								</dl>
								<dl>
									<dt>代办人姓名</dt>
									<dd>
										<input name="csAcceptInfoEntryVO.csApplicationVO.agentName"
											class="required" id="agentNameValue"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentName}"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>代办人证件类型</dt>
									<dd>
										<Field:codeTable nullOption="true" 
											id="agentCertiType"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertiType"
											tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentCertiType}" />
									</dd>
								</dl>
								<dl>
									<dt>代办人证件号码</dt>
									<dd>
										<input id = "agentCertiCode"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertiCode"
											class="required" id="agentCertiCodeValue"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentCertiCode}"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>证件有效期起期</dt>
									<dd>
										<input id = "agentCertStarDate"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertStarDate"
											class="required"
											value="<s:date format="yyyy-MM-dd" name="csAcceptInfoEntryVO.csApplicationVO.agentCertStarDate"/>"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>证件有效期止期</dt>
									<dd>
										<input id = "agentCertEndDate"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertEndDate"
											class="required"
											value="<s:date format="yyyy-MM-dd" name="csAcceptInfoEntryVO.csApplicationVO.agentCertEndDate"/>"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>代办人联系电话</dt>
									<dd>
										<input name="csAcceptInfoEntryVO.csApplicationVO.agentTel"
											class="phone required" id="agentTelValue"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentTel}"
											type="expandMobile" />

									</dd>
								</dl>
								<dl>
									<dt>绩优等级</dt>
									<dd>
										<input name="csAcceptInfoEntryVO.csApplicationVO.agentLevel"
											class="" id="agentLevel"
											value='<Field:codeValue tableName="APP___PAS__DBUSER.T_AGENT_LEVEL" 
									        value="${csAcceptInfoEntryVO.csApplicationVO.agentLevel}"/>'
											type="text" />
									</dd>
								</dl>
								<dl>
								<dt>代办人网点代码</dt>
								<dd>
									<input name="csAcceptInfoEntryVO.csApplicationVO.agentSalesOrganCode" id="agentSalesOrganCode" 
									value="${csAcceptInfoEntryVO.csApplicationVO.agentSalesOrganCode}" type="text" readonly/>
								</dd>
							</dl>
							<dl>
								<dt>代办人银行代码</dt>
								<dd>
									<input name="csAcceptInfoEntryVO.csApplicationVO.agentBankBranchCode" id="agentBankBranchCode" 
									value="${csAcceptInfoEntryVO.csApplicationVO.agentBankBranchCode}" type="text" readonly/>
								</dd>
							</dl>
							<dl>
								<dt>代办人所属机构</dt>
								<dd>
									<input name="csAcceptInfoEntryVO.csApplicationVO.agentOrganCode" id="agentOrganCode" 
									value="${csAcceptInfoEntryVO.csApplicationVO.agentOrganCode}" type="text" readonly/>
								</dd>
							</dl>
							</div>
						</div>
						<!-- 其他人代办 -->
						<div id="agentEntryDiv" class="agentInfoEntryDiv">
							<div class="divfclass">
								<h1>
									<img src="${ctx}/cs/img/icon/tubiao.png" />其他人代办信息
								</h1>
							</div>
							<div class="panelPageFormContent">
								
								<dl>
									<dt>代办人姓名</dt>
									<dd>
										<input name="csAcceptInfoEntryVO.csApplicationVO.agentName"
											class="required" id="agentNameValue"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentName}"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>代办人证件类型</dt>
									<dd>
										<Field:codeTable nullOption="true" 
											id="agentCertiType"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertiType"
											tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentCertiType}" />
									</dd>
								</dl>
								<dl>
									<dt>证件有效期起期</dt>
									<dd>
										<input id = "agentCertStarDateForOther"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertStarDate"
											class="required"
											value="<s:date format="yyyy-MM-dd" name="csAcceptInfoEntryVO.csApplicationVO.agentCertStarDate"/>"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>证件有效期止期</dt>
									<dd>
										<input id = "agentCertEndDateForOther"
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertEndDate"
											class="required"
											value="<s:date format="yyyy-MM-dd" name="csAcceptInfoEntryVO.csApplicationVO.agentCertEndDate"/>"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>代办人证件号码</dt>
									<dd>
										<input
											name="csAcceptInfoEntryVO.csApplicationVO.agentCertiCode"
											class="required" id="agentCertiCodeValue"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentCertiCode}"
											type="text" />
									</dd>
								</dl>
								<dl>
									<dt>代办人联系电话</dt>
									<dd>
										<input name="csAcceptInfoEntryVO.csApplicationVO.agentTel"
											class="phone required" id="agentTelValue"
											value="${csAcceptInfoEntryVO.csApplicationVO.agentTel}"
											type="expandMobile" />

									</dd>
								</dl>
							</div>
						</div>
						
					</div>
					<div class="pageFormbut">
						<button id="saveAppliyButton" type="button" class="but_blue"
							onclick="_appInfo_save(this,'acceptInfoDiv','applicationInfoForm');">保存申请信息</button>
					</div>
				</div>
			</div>
		</form>

		<!--申请变更项 -->
		<div id="csServiceChange" class="main_clearfix">
			<div class="divfclass">
				<h1>
					<a href="#" id="showButtonId"
						onclick="showButton(this,'serviceItemC')"
						<s:if test="openPage==false">value='-'</s:if>
						<s:else>value='+'</s:else>> <!-- <input class="main_fold"	 type="button"> -->
						<img src="${ctx}/cs/img/icon/three_plus.png" />受理变更项目
					</a>
				</h1>
			</div>
			<div id="serviceItemC"
				<s:if test="openPage==false">style="display: none;"</s:if>>
				<div id="csServiceChangeSub" class="panelPageFormContent">
					<form id="selectedServiceForm"
						action="cs/csAccept/csServiceChange_PA_acceptAction.action"
						onsubmit="return divSearch(this,'customerPolicyInfoDiv')"
						method="post">
						<!-- 任务池标记 -->
						<input type="hidden" name="csAcceptInfoEntryVO.taskPool"
							value="${csAcceptInfoEntryVO.taskPool}" /> <input type="hidden"
							name="csAcceptInfoEntryVO.customerVO.customerId"
							value="${csAcceptInfoEntryVO.customerVO.customerId}" /> <input
							type="hidden" name="csAcceptInfoEntryVO.csServiceJsons"
							value="${csAcceptInfoEntryVO.csServiceJsons}" /> <input
							type="hidden"
							value="${csAcceptInfoEntryVO.csApplicationVO.changeId }"
							name="csAcceptInfoEntryVO.changeId" />
						<div>
							<dl>
								<dt>保全项目快速查询</dt>
								<dd>
									<input name="itemName" value="" id="itemName"
										onkeyup="queryCsItemPY();" /> <a class="btnLook"
										onclick="queryCsItem();">查找带回</a>
								</dd>
							</dl>
						</div>
						<div>
							<div class="multipleBox">
								<div class="codeTable">
									<Field:codeTable name=""
										tableName="APP___PAS__DBUSER.T_SERVICE" />
								</div>
								<div class="lMultipleBox">
									<div class="lTop"></div>
									<select size="10" multiple="multiple" style="height: auto;"
										optionType="codeAndValue">

									</select>
								</div>
								<div class="multipleButton ">
									<div class="button lToR">
										<div class="buttonContent">
											<button type="button">增加》</button>
										</div>
									</div>
									<div class="button rToL">
										<div class="buttonContent">
											<button type="button">《删除</button>
										</div>
									</div>
									<div class="button" style="clear: left; margin-top: 20px;">
										<div class="buttonContent">
											<button type="button" style="width: 40px;"
												onclick="_aE_serviceSave('customerPolicyInfoDiv','selectedServiceForm','acceptInfoDiv');">
												确定</button>
										</div>
									</div>
								</div>
								<div class="rMultipleBox">
									<div class="rTop">
										<span style="color: red">(注：最多可以选取4个保全项目)</span>
									</div>
									<select size="10" style="height: auto;" multiple="multiple"
										optionType="codeAndValue">
										<s:iterator value="csAcceptInfoVOList">
											<option value="${serviceCode}" title="退保">
												<Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
													value="${serviceCode}" />(${serviceCode})
											</option>
										</s:iterator>
									</select>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
		<div id="customerPolicyInfoDiv" class="main_clearfix">
			<s:if test="#root.csAcceptInfoEntryVO.policyInfoVOList!=null">
				<div class="divfclass">
					<h1>
						<a href="#" id="showButtonId"
							<s:if test="openPage==false">value='-'</s:if>
							<s:else>value='+'</s:else>
							onclick="showButton(this,'customerPolicyInfoPanel')"> <!-- <input class="main_fold" value="+"	type="button"> -->
							<img src="${ctx}/cs/img/icon/three_plus.png" />客户相关保单列表
						</a>
					</h1>
				</div>
				<div id="customerPolicyInfoPanel"
					<s:if test="openPage==false">style="display: none;"</s:if>>

					<div id="customerPolicyInfoPanelSub">
						<form id="acceptPolicyForm"
							action="${ctx }/cs/csAccept/registPolicyChange_PA_acceptAction.action"
							onsubmit="return divSearch(this,'acceptFlayInfoDiv')"
							method="post">
							<!-- 任务池标记 -->
							<input type="hidden" id="cusId"
								value="${csAcceptInfoEntryVO.customerVO.customerId}"
								name="csAcceptInfoEntryVO.customerVO.customerId" /> <input
								type="hidden" id="taskPool"
								value="${csAcceptInfoEntryVO.taskPool}"
								name="csAcceptInfoEntryVO.taskPool" /> <input type="hidden"
								value="${csAcceptInfoEntryVO.changeId }"
								name="csAcceptInfoEntryVO.changeId" /> <input type="hidden"
								id="csPolicyJsons" value="${csAcceptInfoEntryVO.csPolicyJsons }"
								name="csAcceptInfoEntryVO.csPolicyJsons" />
							<div class="tabdivclass">
								<table id="customerPolicyTable" class="list" style="width: 100%">
									<thead>
										<tr>
											<th colName="policyCode">保单号</th>
											<th>挂起状态</th>
											<th>挂起原因</th>
											<th>保单状态</th>
											<th colName="policyId" inputType="input">客户角色</th>
											<th>犹豫期内标识</th>
											<th>是否可以贷款</th>
											<th>是否参与本次变更</th>
											<th colName="balanceFlag" inputType="checkbox"
												class="balanceFlags">同时结算</th>
											<th>是否同一人</th>
											<th>销售渠道</th>
											<th colName="serviceCode" inputType="select">保全项目</th>
											<th colName="isShow" id="isShowName" style="display: none;"></th>
										</tr>
									</thead>
									<tbody>
										<s:iterator value="#root.csAcceptInfoEntryVO.policyInfoVOList"
											var="var" status="st">
											<tr tr_saveStatus='1'>
												<td>${policyCode}</td>
												<td>${lockFlag }</td>
												<td>${lockCause }</td>
												<td><Field:codeValue
														tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
														value="${liabilityState }" /></td>
												<td>${customerRole}<input type="hidden"
													value="${policyId}" /></td>
												<td><Field:codeValue
														tableName="APP___PAS__DBUSER.T_YES_NO"
														value="${hesitateFlag}" /></td>
												<td><Field:codeValue
														tableName="APP___PAS__DBUSER.T_YES_NO" value="${loanFlag}" /></td>
												<td>${"是"}</td>
												<!-- 159726 add disabled -->
												<td><input id="balanceFlag" name="balanceFlag"
													class="isBalanceCheckBox" type="checkbox" <s:if test=" is485TwoInsured == 1 ">disabled="disabled"</s:if>
													onclick="isBalanceFlag(this)" value="${balanceFlag}" /></td>
												<td>
												<s:if test="onepersonFlag==0 && balanceFlag==1"><span style="font-weight: bold; color: red">否</span></s:if>
												<s:if test="onepersonFlag==0 && balanceFlag==0">否</s:if>
												<s:if test="onepersonFlag==1">是</s:if> 
												</td>
												<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SALES_CHANNEL" value="${var.channelType}" /></td>
												<td><s:if test="serviceCode!=null">
														<Field:codeTable cssClass="combox" nullOption="true"
															name="" value="${serviceCode}"
															tableName="APP___PAS__DBUSER.T_SERVICE"
															whereClause="service_code in (${serviceCodeStr})" />
													</s:if> <s:else>
														<Field:codeTable cssClass="combox" nullOption="true"
															name="" value="" tableName="APP___PAS__DBUSER.T_SERVICE"
															whereClause="service_code in (${serviceCodeStr})" />
													</s:else></td>
												<td style="display: none">${isShow}</td>
											</tr>
										</s:iterator>
									</tbody>
								</table>
							</div>
						</form>
					</div>
					<div class="pageFormbut">
						<button type="button" class="but_blue"
							onclick="_accept_csPolicySave('customerPolicyInfoPanel','acceptFlayInfoDiv');">保存受理信息</button>
					</div>
				</div>
			</s:if>
		</div>
		<div id="acceptFlayInfoDiv" class="main_clearfix">
			<div class="divfclass">
				<h1>
					<a href="#" id="showButtonId"
						<s:if test="openPage==false">value='-'</s:if>
						<s:else>value='+'</s:else>
						onclick="showButton(this,'flagInfoDiv')"> <!-- <input class="main_fold" value="+" type="button"> -->
						<img src="${ctx}/cs/img/icon/three_plus.png" />标识信息
					</a>
				</h1>
			</div>
			<div id="flagInfoDiv"
				<s:if test="openPage==false">style="display: none;"</s:if>>
				<div class="tabdivclass">
					<form
						action="${ctx}/cs/csAccept/saveFlagInfo_PA_acceptAction.action?menuId=${menuId}&acceptCode=${acceptCode}"
						onsubmit="return validateCallback(this);" method="post">
						<input type="hidden" id="csFlagInfoJsons"
							name="csAcceptInfoEntryVO.csFlagInfoJsons"
							value="${csAcceptInfoEntryVO.csFlagInfoJsons}" /> <input
							id="acceptId" type="hidden" id="csFlagInfoJsons"
							value="${acceptId}" /> <input id="acceptCode" type="hidden"
							value="${acceptCode}" /> <input name="" type="hidden"
							id="yushouli" value="${yushouli}" /> <input name=""
							type="hidden" id="accepts" value="${accepts}" /> <input name=""
							type="hidden" id="poliCode" value="${poliCode}" />
							
						<table width="100%" class="list" id="acceptFlagInfoTable">
							<thead>
								<tr>
									<th style="display: none" colName="acceptId" inputType="input">保全项</th>
									<th style="display: none" colName="serviceCode"
										inputType="input">保全项</th>
									<th>保全项目</th>
									<th colName="acceptCode">保全受理号</th>
									<th colName="policyCodeS"
										style="word-wrap: break; word-break: break-all">保单号</th>
									<th colName="urgentFlag1" inputType="checkbox">异地保全</th>
									<th colName="urgentFlag" inputType="checkbox" id="urgentFlagTh">是否紧急件</th>
									<th colName="urgentCause" inputType="select" id="urgentCauseID">紧急原因</th>
									<th colName="urgentDetail" inputType="input"
										id="urgentDetailID">具体紧急原因</th>
									<th colName="convenFlag" inputType="checkbox" id="convenFlagTh">是否常规业务</th>
									<th colName="lostFlag" inputType="checkbox" id="lostFlagTh">保单遗失</th>
									<th colName="policyReissueFlag" inputType="input" id="policyReissueFlagTh">是否补发保单</th>
									<th colName="unconType" inputType="select" id="unconTypeID">非常规业务类别</th>
									<th colName="unconCause" inputType="select" id="unconCauseID">非常规业务原因</th>
									<th colName="preFlag" inputType="checkbox">是否预受理</th>
									<th id="preValidate" colName="preValidateDate"
										inputType="input">指定生效日期</th>
									<th colName="isSignFlag" inputType="checkbox">是否签报</th>
									<th colName="signNoList" inputType="input" class="signNoList">签报号码</th>
									<!-- <th colName="informType" inputType="select">处理结果通知方式</th> -->
									<!--  <th colName="informType" inputType="select">单证发放方式</th>-->
									<!-- <th colName="sourceType" inputType="select">受理渠道</th> -->
									<th colName="balanceFlag" inputType="checkbox">同时结算</th>
									<th colName="onepersonFlag">是否同一人</th>
									<th colName="changeId" inputType="input" style="display: none">changeId</th>
<!-- 									<th colName="medicalFlag" inputType="checkbox">医保标识</th> -->
									<th style="display: none;">是否补发保单</th>
									<th colName="taxRevenueFlag" inputType="checkbox">是否CRS标识</th>
									<th colName="agentLevel">绩优等级</th>
									<th colName="agentDocFlag" inputType="select" >绩优人员代审客户资料</th>
									<th colName="outDay">是否超时受理</th>
									<th colName="highCustFlagName" inputType="input">高客标识</th>
								</tr>
							</thead>
							<tbody align="center">
								<s:iterator value="csAcceptInfoEntryVO.acceptChangeVOList"
									status="st" id="qr" var='var'>
									<tr tr_saveStatus='1'>
										<td style="display: none"><input type="hidden"
											value="${acceptId}"></td>
										<input type="hidden" id="acceptCode" value="${acceptCode}" />
										<input type="hidden" name="serviceCd" value="${serviceCode }" />
										<td style="display: none"><input type="hidden"
											name="serviceCode" value="${serviceCode }" /></td>
										<td td name="sName" id="sName"><Field:codeValue
												tableName="APP___PAS__DBUSER.T_SERVICE"
												value="${serviceCode }" /></td>
										<td>${acceptCode }</td>
										<td
											style="width: 20%; word-wrap: break-word; word-break: break-all;">${policyCodeS }</td>
										<td><s:if test="diffDeptFlag">是</s:if><s:else>否</s:else></td>
										<!-- 是否异地保全 -->
										<!-- 是否紧急件 -->
										<td class="urgentFlagCheckboxTd"><s:if
												test="urgentFlag ==1">
												<input type="checkbox" class="urgentFlagCheckbox"
													name="urgentFlag" onclick="isPreFlag1(this)"
													checked="checked" value="${urgentFlag }" />是
														 </s:if> <s:else>
												<input type="checkbox" class="urgentFlagCheckbox"
													name="urgentFlag" onclick="isPreFlag1(this)"
													value="${urgentFlag }" />是
														 </s:else></td>
										<td class="urgentCauseValueTd">
											<div class="urgentCauseValueDiv">
												<!-- 紧急原因 -->
												<s:if test="urgentCause ==null">
													<Field:codeTable id="urgentCause${st.index}" nullOption="true"
														disabled="true" name="#var.urgentCause"
														tableName="APP___PAS__DBUSER.T_URGENT_CAUSE"
														onChange="_checkShowUrgentDiv(this)" />
												</s:if>
												<s:else>
													<Field:codeTable id="urgentCause${st.index}" nullOption="true"
														disabled="true" name="#var.urgentCause"
														tableName="APP___PAS__DBUSER.T_URGENT_CAUSE"
														onChange="_checkShowUrgentDiv(this)"
														value="${urgentCause}" />
												</s:else>
											</div>
										</td>
										<td class="urgentDetailTd">
											<!-- 具体紧急原因  --> <input maxlength="20" id="urgentDetail"
											name="urgentDetail" value="${urgentDetail }" />
										</td>
										<td class="convenflagCheckBoxTd">
											<!-- 是否常规业务 --> <s:if test="convenFlag ==1">
												<input type="checkbox" class="convenflagCheckBox"
													name="convenFlag" onclick="isPreFlag2(this)"
													checked="checked" value="${convenFlag }" />是
													    </s:if> <s:else>
												<input type="checkbox" class="convenflagCheckBox"
													name="convenFlag" onclick="isPreFlag2(this)"
													value="${convenFlag }" />是
													    </s:else>
										</td>
										<td>
											<!-- 保单遗失 --> 
											<input type="checkbox" id="lostFlag" class="lostFlagCheckBox" <s:if test="lostFlag==1">checked='checked'</s:if>name="lostFlag" onclick="isPolicyLostFlag(this)" value="${lostFlag }" />是
										</td>
										<td>
											<!-- 是否补发保单 -->
											<s:if test="policyReissueFlag==1">
												<span title="保单补发原因：${policyReissueCause}" > 
												是
												</span>
												<input type="hidden" name="policyReissueFlag" value="1" />
											</s:if>
											<s:if test="policyReissueFlag==0">
												否
												<input type="hidden" name="policyReissueFlag" value="0" />
											</s:if>
										</td>
										<!-- 非常规业务类别 start -->
										<td class="unconTypeValueTd">
											<div>
												<s:if test="unconType ==null">
													<Field:codeTable nullOption="true"
														disabled="true" id="unconType${st.index}"
														name="#var.unconType"
														tableName="APP___PAS__DBUSER.T_UNCON_TYPE" />
												</s:if>
												<s:else>
													<Field:codeTable nullOption="true"
														disabled="true" id="unconType${st.index}"
														name="#var.unconType"
														tableName="APP___PAS__DBUSER.T_UNCON_TYPE"
														value="${unconType }" />
												</s:else>
											</div>
										</td>
										<!-- 非常规业务类别 end -->
										<td class="unconCauseValueTd">
											<div>
												<s:if test="unconCause ==null">
													<Field:codeTable readOnly="false"
														nullOption="true" disabled="true"
														id="unconCause${st.index}" name="#var.unconCause"
														tableName="APP___PAS__DBUSER.T_UNCON_CAUSE" />
												</s:if>
												<s:else>
													<Field:codeTable readOnly="false"
														nullOption="true" disabled="true"
														id="unconCause${st.index}" name="#var.unconCause"
														tableName="APP___PAS__DBUSER.T_UNCON_CAUSE"
														value="${unconCause }" />
												</s:else>
											</div>
										</td>
										
										<s:if test=" preFlag==1">
											<td><input type="checkbox" name="preAccept"
												class="checkboxCtrl" onclick="isPreFlag(this)"
												checked="checked" value="${preFlag}" />是</td>
										</s:if>
										<s:else>
											<td><input type="checkbox" name="preAccept"
												class="checkboxCtrl" onclick="isPreFlag(this)"
												value="${preFlag}" />是</td>
										</s:else>
										<td align="left">
										  	<input <s:if test="preFlag!=1">style="display: none;"</s:if> class="date" id="preValidateDate" name="preValidateDate"
											value="<s:date name="preValidateDate" format="yyyy-MM-dd"/>" />
										</td>
										<td><input id="isSignFlag" class="isSignFlagCheckBox"
											type="checkbox"
											<s:if test="isSignFlag==1">checked="checked"</s:if>
											onclick="isbizSingFlag(this)" value="${isSignFlag}" /></td>
										<td><input id="signNoList" class="signNoListSub"
											value="${signNoList }" <s:if test="isSignFlag==0"></s:if> /></td>
										<!--  <td align="left"><Field:codeTable cssClass="combox"
												name="#var.informType"
												tableName="APP___PAS__DBUSER.T_DOC_SEND_TYPE"
												value="${billSendType }" /></td>-->

										<td><input id="balanceFlag${st.index}" name="balanceFlag"
											class="isBalanceCheckBox" type="checkbox"
											onclick="isBalanceFlag(this)" value="${balanceFlag}" /></td>
										<%-- <td style="display:none"> <!-- 受理渠道列不再显示 -->
													 	<Field:codeTable cssClass="combox" nullOption="true"
																name="#var.sourceType" tableName="APP___PAS__DBUSER.T_ACCEPT_CHANNEL" defaultValue="03"/> <!-- 默认渠道置为柜面受理 -->
													</td> --%>
										<td id="onepersonFlag${st.index}"></td>
										<td style="display: none"><input type="hidden"
											value="${changeId }" /> <input type="hidden"
											class="acceptStatusValue" value="${acceptStatus}" /> <input
											type="hidden" class="urgentFlagValue" value="${urgentFlag}" />
											<input type="hidden" class="urgentCauseValue"
											value="${urgentCause}" /> <input type="hidden"
											class="urgentDetailValue" value="${urgentDetail}" /> <input
											type="hidden" class="convenFlagValue" value="${convenFlag}" />
											<input type="hidden" class="unconTypeValue"
											value="${unconType}" /> <input type="hidden"
											class="unconCauseValue" value="${unconCause}" /></td>
<!-- 										<td><input type="checkbox" disabled="disabled" id="sbbs" -->
<!-- 											<s:if test="medicalFlag==1">checked="checked"</s:if> />是</td> -->

								<td style="display: none;">
									<!-- 是否补发保单 --> 
									<input disabled="disabled" type="checkbox" id="lrPolicy" class="lrPolicyBox" name="isLRPolicy"  value="${isLRPolicy }"<s:if test="isLRPolicy == 1">checked='checked'</s:if> />是
								</td>
								
								<s:if test="serviceCode=='AE' or serviceCode=='CM' or serviceCode=='CC' 
									or serviceCode=='RE' or serviceCode=='CT' or serviceCode=='PT' 
									or serviceCode=='XT' or serviceCode=='PG' or serviceCode=='AG' 
									or serviceCode=='AI' or serviceCode=='IT' ">
								<td><!-- CRS标识--> 
									<input disabled="disabled" type="checkbox" class="isCrsFlagCheckBox"  <s:if test="taxRevenueFlag==1" >checked='checked'</s:if>name="taxRevenueFlag"  value="${taxRevenueFlag }" />是
								</td>
								</s:if>
								<s:else>
									<td><input type="checkbox" disabled="disabled" name="taxRevenueFlag" value="1" />是</td>
								</s:else>
								<!-- 绩优等级  -->
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_AGENT_LEVEL" value="${agentLevel}"/></td>
								
								<!-- 绩优人员代审客户资料  -->
								<td readOnly="readOnly">
									<s:if test="agentDocFlag == 1 ">是</s:if>
									<s:elseif test="agentDocFlag == 0 ">否</s:elseif>
									<s:else></s:else>
								</td>
								
								<td>
											<!-- 是否受理超时 -->
											<s:if test="csAcceptInfoEntryVO.outDay==0">
												<span> 
												    是
												</span>
											</s:if>
											<s:if test="csAcceptInfoEntryVO.outDay==1">
												<span>
												    否
												</span>
											</s:if>
								</td>	
								<td><s:if test="rightsQualFlag=='11'">黑钻</s:if>
								    <s:elseif test="rightsQualFlag=='12'">蓝钻</s:elseif>
								    <s:elseif test="rightsQualFlag=='13'">新钻</s:elseif>
								    <s:elseif test="rightsQualFlag=='21'">五星</s:elseif>
								    <s:elseif test="rightsQualFlag=='22'">四星</s:elseif>
								    <s:elseif test="rightsQualFlag=='23'">三星</s:elseif>
								    <s:elseif test="rightsQualFlag=='24'">二星</s:elseif>
								    <s:elseif test="rightsQualFlag=='25'">一星</s:elseif>
								    <s:elseif test="rightsQualFlag=='37'">VIP3</s:elseif>
								    <s:elseif test="rightsQualFlag=='38'">VIP2</s:elseif>
								    <s:elseif test="rightsQualFlag=='39'">VIP1</s:elseif>
								    <s:else></s:else>
								</td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
					</form>
				</div>
				
				<input type="checkbox" id="isOperationCCHKIden" value="${csAcceptInfoEntryVO.isCheck }" disabled="disabled"/>客户基本资料变更
				<button class="but_blue" type="submit" onclick="calCustPolicyChgMsg()">查看客户基本资料</button>
				<a target="navTab" rel="policyStatusQuery" id="calcChangeMsg"
				href="${ctx}/cs/serviceitem_cchk/toShowCustPolicy_PA_csEndorseCCHKAction.action?&customerId=${customerId}&changeId=${changeId}"
				title="查看客户基本资料"></a>
			
				<div class="pageFormbut">
					<button type="button" class="but_blue"
						onclick="_accept_saveFlagInfo('acceptFlagInfoTable');">保存标识信息</button>
				</div>
			</div>
		</div>
	</div>
	 <div class="pageFormbut">
		<button type="button" id="modifyAcceptInfo" class="but_blue"
			onclick="editAcceptInfo('acceptInfoDiv');">修改</button>
		<button type="button" id="saveAcceptInfo" class="but_blue"
			onclick="_acceptM_saveAcceptInfo('acceptInfoDiv','saveAcceptInfoForm');">保存</button>
	</div>
</div>
<object classid="CLSID:C08148B7-2779-4924-B6ED-93E51C6A6039"
	codebase="${ctx}/plugins/MxDevice.ocx" id="fpDevObj" height=0 width=0> </object>
<script type="text/javascript">

    $(function(){
    	//判断快捷键复选框是否勾选
		var isCheck = $("#isOperationCCHKIden", navTab.getCurrentPanel()).val();
		var agentCertEndDate = $("#agentCertEndDate", navTab.getCurrentPanel()).val();
		var agentCertEndDateForOther = $("#agentCertEndDateForOther", navTab.getCurrentPanel()).val();
		if(agentCertEndDate == '9999-12-31'){
			$("#agentCertEndDate", navTab.getCurrentPanel()).val("长期");
		}
		if(agentCertEndDateForOther == '9999-12-31'){
			$("#agentCertEndDateForOther", navTab.getCurrentPanel()).val("长期");
		}
		if(isCheck == '1'){
			$("#isOperationCCHKIden", navTab.getCurrentPanel()).attr("checked",true);
		}
		if($('#serviceType').val() == '2'){
			$("#agentEntryDiv", navTab.getCurrentPanel()).hide();
			
			$("#agentEntryDiv", navTab.getCurrentPanel()).find("#agentNameValue").val(
			"");
			$("#agentEntryDiv", navTab.getCurrentPanel()).find("#agentCertiType").val(
			"");
			$("#agentEntryDiv", navTab.getCurrentPanel()).find("#agentCertiCodeValue").val(
			"");
			$("#agentEntryDiv", navTab.getCurrentPanel()).find("#agentTelValue").val(
			"");
		}else if($('#serviceType').val() == '3'){
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).hide();
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentCode").val(
			"");
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentCodeNew").val(
			"");
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentNameValue").val(
			"");
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentCertiType").val(
			"");
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentCertiCode").val(
			"");
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentTelValue").val(
			"");
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentLevel").val(
			"");
		}else{
			$("#agentInfoEntryDiv", navTab.getCurrentPanel()).hide();
			$("#agentEntryDiv", navTab.getCurrentPanel()).hide();
		}
		
    });
    
  //快捷基本资料查看按钮
 	function calCustPolicyChgMsg(){
		 if($('#isOperationCCHKIden',navTab.getCurrentPanel()).is(':checked')) {
			    $("#calcChangeMsg",navTab.getCurrentPanel()).click();
			}
	} 
    
	$(document).ready(
			function() {
				var b = 0;
				var dataTable = $("#customerPolicyTable",
						navTab.getCurrentPanel()).find("tr");
				for (var a = 1; a < dataTable.length; a++) {
					var testOneTr = $(dataTable[a], navTab.getCurrentPanel());
					//判断是否要选中
					var oneTrName = testOneTr.find("td:eq(8)").find("input")
							.val();
					if (oneTrName == "1") {
						testOneTr.find("input").attr("checked", "checked");
						$("#balanceFlag"+(a-1)).attr("checked", "checked");
					}
					//判断是否需要隐藏
			 		var oneTrName1 = testOneTr.find("td:eq(12)").text();
					if (oneTrName1 == "0") {
						b++;
					} 
				}

 				if (b == dataTable.length) {
					$(".balanceFlags").hide();
					for (var a = 1; a < dataTable.length; a++) {
						testOneTr.find("td:eq(8)").hide();
					}
				} 
				//判断是否异地保全 
				if ("${diffDeptFlag }" == "true") {
					$("#ydbq").attr("checked", "checked");
				}
				var isSignFlag = $("#isSignFlag", navTab.getCurrentPanel())
						.val();
				if (isSignFlag == 0) {
					$(".signNoList", navTab.getCurrentPanel()).hide();
					$(".signNoListSub", navTab.getCurrentPanel()).parent()
							.hide();
				}
/* 
				var balanceFlag = $("#balanceFlag", navTab.getCurrentPanel()).val();
				if (balanceFlag == 1) {
					$("#balanceFlag").val(1);
					$("#balanceFlag").attr("checked", "checked");
				}else{
					$("#balanceFlag").removeAttr("checked");
				} */
				//判断是否在保单生效60天内做的保全(保单贷款保全项) 
				if ("${ValiDateFlag}" == "true") {
					alertMsg.info("保全申请提交日期-保单生效日小于60天,需要采用原收费形式退出!");
				}
				var time = $("#applyDate", navTab.getCurrentPanel()).val();
				//var addction = $("#addction").val();
				var addctionArray = $("input[name = 'convenFlag']");
				var date = new Date();
				var day = date.getDate();
				var month = date.getMonth() + 1;
				var year = date.getFullYear();
				month = month < 10 ? "0" + month : month;
				day = day < 10 ? "0" + day : day;
				var newday = year + "-" + month + "-" + day;
				for (var i = 0; i < addctionArray.size(); i++) {
					if ($(addctionArray[i]).val() == 1 || time < newday) {
						$(addctionArray[i]).attr("checked", "checked");
					}
				}
			});
 
	$(document)
			.ready(
					function() {
						//对紧急件相关列的处理
						var choosedCount = 0;
						$(".urgentFlagValue", navTab.getCurrentPanel()).each(
								function() {
									if ($(this).val() == "1"
											|| $(this).prev().val() == "21") { //如果当前行受理的是紧急件,或者当前的受理状态为复核修改状态，则不隐藏紧急相关的列
										choosedCount = choosedCount + 1;
									}
								});
						if (choosedCount == 0) { //多个受理都不是紧急件的情况下，隐藏紧急相关的标题和列内容
							$("#urgentCauseID", navTab.getCurrentPanel())
									.hide();
							$(".urgentCauseValueTd", navTab.getCurrentPanel())
									.hide();
							$("#urgentDetailID", navTab.getCurrentPanel())
									.hide();
							$(".urgentDetailTd", navTab.getCurrentPanel())
									.hide();
						} else {
							var otherCount = 0; //紧急原因为“其他”的行数
							$(".urgentCauseValueDiv", navTab.getCurrentPanel())
									.each(
											function(index) {
												if ($(this)
														.find(
																"option[value='"
																		+ $(
																				"#urgentCause"
																						+ index)
																				.val()
																		+ "']")
														.text() == "其他") {
													otherCount = otherCount + 1;
												}
											});
							if (otherCount == 0) { //隐藏具体紧急原因列的标题和内容
								$("#urgentDetailID", navTab.getCurrentPanel())
										.hide();
								$(".urgentDetailTd", navTab.getCurrentPanel())
										.hide();
							}
						}
						//对常规业务相关列的处理
						var unChoosedCount = 0;
						$(".convenflagCheckBox", navTab.getCurrentPanel())
								.each(
										function() {
											if ($(this).val() != "1") { //在ie浏览器下，checked=""也会被选中
												$(this).removeAttr("checked");
											}
											if ($(this).val() != "1"
													|| $(this)
															.parent()
															.parent()
															.find(
																	".acceptStatusValue")
															.val() == "21") { //如果当前行受理的是非常规业务,或者当前的受理状态为复核修改状态，则不隐藏常规业务相关的列
												unChoosedCount = unChoosedCount + 1;
											}
										});
						if (unChoosedCount == 0) {//都是常规业务的情况下，隐藏常规业务相关的列
							$("#unconTypeID", navTab.getCurrentPanel()).hide();
							$("#unconCauseID", navTab.getCurrentPanel()).hide();

							$(".unconTypeValueTd", navTab.getCurrentPanel())
									.hide();
							$(".unconCauseValueTd", navTab.getCurrentPanel())
									.hide();
						}
					});

	$(document)
			.ready(
					function() {
						_cs_initMultipleBox();
						var _agentName = $("#applicationInfoForm",
								navTab.getCurrentPanel()).find("#agentName")
								.val();
						if (_agentName == '') {
							$("#agentInfoEntryDiv", navTab.getCurrentPanel())
									.find("a,select,input").attr("disabled",
											"disabled");
							$("#agentInfoEntryDiv", navTab.getCurrentPanel())
									.css("display", 'none');
						}
						var _customerId = $("#changeIdForm",
								navTab.getCurrentPanel()).find("#customerId")
								.val();
						var _changeId = $(
								"input[name='csAcceptInfoEntryVO.csApplicationVO.changeId']",
								navTab.getCurrentPanel()).val();
						var _applyCode = $(
								"input[name='csAcceptInfoEntryVO.csApplicationVO.applyCode']",
								navTab.getCurrentPanel()).val();
						var _tryCalcNo = $(
								"input[name='csAcceptInfoEntryVO.changeVO.tryCalcNo']",
								navTab.getCurrentPanel()).val();
						var _agentName2 = $(
								"input[name='csAcceptInfoEntryVO.csApplicationVO.agentName']",
								navTab.getCurrentPanel()).val();
						$("#changeId", navTab.getCurrentPanel()).val(_changeId);
						$("#applyCode", navTab.getCurrentPanel()).val(
								_applyCode);
						$("#tryCalcNo", navTab.getCurrentPanel()).val(
								_tryCalcNo);
						$("#agentName", navTab.getCurrentPanel()).val(
								_agentName);
						$(
								"input[name='csAcceptInfoEntryVO.customerVO.customerId']",
								navTab.getCurrentPanel()).val(_customerId);
						$("#acceptFlagInfoTable").find("tbody").find("tr").each(function(index){
							var policyCode = $(this).find("td").eq(4).text();
							$("#customerPolicyTable").find("tbody").find("tr").each(function(){
								var policyCode1 = $(this).find("td").eq(0).text();
								if(contains(policyCode,policyCode1)){
									var onepersonFlag = $(this).find("td").eq(9).html();
									$("#onepersonFlag"+index).html(onepersonFlag);
								}
								
							})
						})  

					});

	function cs_ac_entry_up(boxId, formId, step) {
		cs_ac_up(boxId, formId, step);
	}

	function isbizSingFlag(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
			$(".signNoList", navTab.getCurrentPanel()).show();
			$(".signNoListSub", navTab.getCurrentPanel()).parent().show();
			//	$(obj).parent().parent().find("td:eq(15)").show();
		} else {
			$(obj).val(0);
			//判断一下多行情况下是否每一行都没有选中签报，如果是的话，则将每一行的签报号码列隐藏
			var choosedCount = 0;
			$(".isSignFlagCheckBox", navTab.getCurrentPanel()).each(function() {
				if ($(this).attr("checked") == "checked") {
					choosedCount = choosedCount + 1;
				}
			});
			if (choosedCount == 0) {
				$(".signNoList", navTab.getCurrentPanel()).hide();
				$(".signNoListSub", navTab.getCurrentPanel()).parent().hide();
				$(".signNoListSub", navTab.getCurrentPanel()).each(function() {
					$(this).find("input").val("");
				});
			}
		}
	}

	//是否预受理,指定生效日期是否可操作
	function isPreFlag(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
			$(obj).parent().next().find("input").show(); //一行或多行情况下，对当前行的生效日期指定是否可操作
		} else {
			$(obj).val(0);
			$(obj).parent().next().find("input").hide();
			$(obj).parent().next().find("input").val("");
		}
	}

	function isBalanceFlag(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
		} else {
			$(obj).val(0);
		}
	}

	function isPreFlag1(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
			$("#urgentCauseID", navTab.getCurrentPanel()).show(); //wwx
			$(".urgentCauseValueTd", navTab.getCurrentPanel()).show(); // wwx
		} else {
			$(obj).val(0);
			//判断一下多行情况下是否每一行都为非紧急件，即每一行都没有选中，如果是的话，则将每一行的紧急原因和具体紧急原因列隐藏
			var choosedCount = 0;
			$(".urgentFlagCheckbox", navTab.getCurrentPanel()).each(function() {
				if ($(this).attr("checked") == "checked") {
					choosedCount = choosedCount + 1;
				}
			});
			if (choosedCount == 0) {
				$("#urgentCauseID", navTab.getCurrentPanel()).hide();
				$(".urgentCauseValueTd", navTab.getCurrentPanel()).hide();
				$("#urgentDetailID", navTab.getCurrentPanel()).hide();
				$(".urgentDetailTd", navTab.getCurrentPanel()).hide();
				$(".urgentCauseValueTd", navTab.getCurrentPanel()).each(
						function() {
							$(this).find("select").val("");
						});
				$(".urgentDetailTd", navTab.getCurrentPanel()).each(function() {
					$(this).find("input").val("");
				});
			}
		}
	}
	function isPreFlag2(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
			//判断一下多行情况下是否存在某一行没有选中常规业务的，如果不存在的话，则将每一行的非常规业务类型和原因列隐藏
			var unChoosedCount = 0;
			$(".convenflagCheckBox", navTab.getCurrentPanel()).each(function() {
				if (!$(this).attr("checked")) {
					unChoosedCount = unChoosedCount + 1;
				}
			});
			if (unChoosedCount == 0) {
				//隐藏非常规业务类别与原因的列标题
				$("#unconTypeID", navTab.getCurrentPanel()).hide();
				$("#unconCauseID", navTab.getCurrentPanel()).hide();
				//隐藏非常规业务类别与原因的列内容
				$(".unconTypeValueTd", navTab.getCurrentPanel()).hide();
				$(".unconCauseValueTd", navTab.getCurrentPanel()).hide();
				//将隐藏的非常规业务类别和原因的值置为空
				$(".unconTypeValueTd", navTab.getCurrentPanel()).each(
						function() {
							$(this).find("select").val("");
						});
				$(".unconCauseValueTd", navTab.getCurrentPanel()).each(
						function() {
							$(this).find("select").val("");
						});
			}
		} else {
			$(obj).val(0);
			//显示非常规业务类别与原因的列标题
			$("#unconTypeID", navTab.getCurrentPanel()).show();
			$("#unconCauseID", navTab.getCurrentPanel()).show();
			//显示非常规业务类别与原因的列内容
			$(".unconTypeValueTd", navTab.getCurrentPanel()).show();
			$(".unconCauseValueTd", navTab.getCurrentPanel()).show();
		}
	}

	function clickCheckBox(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
		} else {
			$(obj).val(0);
		}
	}

	function _checkShowUrgentDiv(obj) {
		var $this = $(obj);
		var _value = $this.val();
		//var _urgentFlag= $("input[name='urgentFlag']").val();//单行的处理改为如下对多行情况的处理
		var _urgentFlag = $this.parents(".urgentCauseValueTd").prev().find(
				"input[name='urgentFlag']").val();//找到当前行“是否紧急件”列的紧急标记值
		var _text = $this.find("option[value='" + _value + "']").text();
		if (_text == "其他" && _urgentFlag == 1) {
			alertMsg.info("紧急原因为其他时，具体紧急原因为必填！");
			$("#urgentDetailID", navTab.getCurrentPanel()).show();
			$(".urgentDetailTd", navTab.getCurrentPanel()).show();
		} else {
			//判断一下多行情况下是否每一行都为其他，即每一行都没有选中，如果是的话，则将每一行的具体紧急原因列隐藏
			var choosedCount = 0;
			$(".urgentCauseValueDiv", navTab.getCurrentPanel()).each(
					function(index) {
						if ($this.find(
								"option[value='"
										+ $("#urgentCause" + index).val()
										+ "']").text() == "其他") {
							choosedCount = choosedCount + 1;
						}
					});
			if (choosedCount == 0) { //隐藏标题和一行或多行内容
				$("#urgentDetailID", navTab.getCurrentPanel()).hide();
				$(".urgentDetailTd", navTab.getCurrentPanel()).hide();
				$(".urgentDetailTd", navTab.getCurrentPanel()).each(function() {
					$(this).find("input").val("");
				});
			}
		}
	}
	function showAgent1() {
		//alert($("#contractMasterVOList").val().size);
		var agentCode = $("#agentCode option:selected").val();
		showAgent(agentCode);
		$("#agentCodeNew", navTab.getCurrentPanel()).val("");
	}
	function showAgent2() {
		//alert($("#contractMasterVOList").val().length);
		var agentCode = $("#agentCodeNew").val();
		showAgent(agentCode);
		$("#agentCode", navTab.getCurrentPanel()).val("0");
	}

	function showAgent(agentCode) {

		if (agentCode == "" || agentCode == "0") {
			alertMsg.error("业务员代码为空 ");
			return;
		} else {
			$.ajax({
				type : "POST",
				data : agentCode,
				url : "cs/csAccept/showAgent_PA_acceptOtherAction.action?agentCode="
						+ agentCode,
				dataType : "json",
				success : function(json) {
					var name = json.agentVO.agentName;

					if (name == "") {
						alertMsg.error("无业务员信息，申请方式不能为业务员代办");
						$("#agentCodeNew", navTab.getCurrentPanel()).val("");
						$("#agentNameValue", navTab.getCurrentPanel()).val("");
						$("#agentCertiType", navTab.getCurrentPanel()).val("");
						$("#agentCertiCodeValue", navTab.getCurrentPanel())
								.val("");
						$("#agentTelValue", navTab.getCurrentPanel()).val("");
						$("#agentLevel", navTab.getCurrentPanel()).val("");
					}

					$("#agentNameValue", navTab.getCurrentPanel()).val(
							json.agentVO.agentName);
					$("#agentCertiType", navTab.getCurrentPanel()).val(
							json.agentVO.certType);
					/* $("#agentCertiCodeValue", navTab.getCurrentPanel()).val(
							json.agentVO.certiCode); */
					$("#agentInfoEntryDiv", navTab.getCurrentPanel()).find("#agentCertiCode").val(
							json.agentVO.certiCode);
					
					$("#agentTelValue", navTab.getCurrentPanel()).val(
							json.agentVO.agentMobile);
					$("#agentTelValue", navTab.getCurrentPanel()).attr(
							"name",
							$("#agentTelValue", navTab.getCurrentPanel()).next(
									"input").attr("name"));
					$("#agentTelValue", navTab.getCurrentPanel()).next("input")
							.remove();
					$("#agentTelValue", navTab.getCurrentPanel())
							.intputValidExpand();
					$("#agentLevel", navTab.getCurrentPanel()).val(
							json.agentVO.agentLevel);
					$('#agentTel').intputValidExpand();
					$("#agentSalesOrganCode", navTab.getCurrentPanel()).val(json.agentVO.agentSalesOrganCode);
					$("#agentBankBranchCode", navTab.getCurrentPanel()).val(json.agentVO.agentBankBranchCode);
					$("#agentOrganCode", navTab.getCurrentPanel()).val(json.agentVO.agentOrganCode);

				},
				error : function() {
					alertMsg.error("查询失败!");
				}
			});
		}
	}
	
	//保单遗失
	function isPolicyLostFlag(obj) {
		if ($(obj).attr("checked") == "checked") {
			$(obj).val(1);
		} else {
			$(obj).val(0);
		}
	}
	
	function contains(string, substr)
	{
	    var startChar = substr.substring(0, 1);
	    var strLen = substr.length;

	    for (var j = 0; j<string.length - strLen + 1; j++)
	    {
	         if (string.charAt(j) == startChar)  //如果匹配起始字符,开始查找
	         {
	             if (string.substring(j, j+strLen) == substr)  //如果从j开始的字符与str匹配，那ok
	             {
	                 return true;
	             }   
	         }
	    }
	    return false;
	}

	
</script>
