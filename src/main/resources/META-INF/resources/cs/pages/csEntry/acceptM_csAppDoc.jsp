<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-应备资料 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<div>
	<div class="divfclass">
		<h1>
			<!-- 141437 start -->
			<%-- <img src="${ctx}/cs/img/icon/tubiao.png" />应备资料 --%>
			<a href="#" id="showButtonId"
				<s:if test="openPage==false">value='+'</s:if><s:else>value='-'</s:else>
				onclick="showButtonAddCsAppDocPanelDiv(this,'addCsAppDocPanelDiv')">
				<img id="imgPlus" <s:if test="openPage!=false">style="display: none;"</s:if> src="${ctx}/cs/img/icon/three_plus.png" />
				<img id="imgMinus" <s:if test="openPage==false">style="display: none;"</s:if> src="${ctx}/cs/img/icon/three_minus.png" />应备资料</a>
			<!-- 141437 end -->
		</h1>
	</div>
	<!-- 141437 start -->
	<div id="addCsAppDocPanelDiv" <s:if test="openPage==false">style="display: none;"</s:if>>
	<!-- 141437 end -->
	<div id="addCsAppDocPanel" class="tabdivclass">
		<form action="" id="printDataSignReturn" onsubmit="" method="post">
			<input type="hidden" id="changeId" name="changeId"
				value="${acceptCsAppDocMVO.changeId }" />

		</form>
		<form id="addCsAppDocForm"
			action="cs/csEntry/saveCsAppDoc_PA_csEntryAction.action"
			method="post">
			<input type="hidden" name="acceptCsAppDocMVO.changeId"
				value="${acceptCsAppDocMVO.changeId }" /> <input type="hidden"
				id="tableJsonId" name="acceptCsAppDocMVO.addOrModJsons"
				value="${acceptCsAppDocMVO.addOrModJsons}" />
			<table table_saveStatus='0' id="csAppDoc" class="list"
				style="width: 100%">
				<thead>
					<tr>
						<th>申请应备资料</th>
						<th>应备资料属性</th>
						<th>原件/复印件</th>
						<th>备注</th>
					</tr>
				</thead>
				<tbody id="documentTr" addRole="appendTo" addTableGroup="atg1">

					<s:iterator status="st" value="acceptCsAppDocMVO.csAppDocVOList"
						var="appDocTable">
						<tr tr_saveStatus='0' align="center">
							<td align="left"><s:if test="docName=='null'"></s:if> <s:else>${docName}</s:else></td>

							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_DOC_PROPERTY"
									value="${docProperty}" /></td>
							<td><s:if test="originalFlag==0"><span>复印件</span></s:if> 
								<s:else><span>原件</span></s:else></td>
							<td>${docRemark }</td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
		</form>
	</div>
	<!-- 141437 start -->
	</div>
	<!-- 141437 end -->
	<div class="pageFormbut" id="addCsAppDocPanleBar_save" style="display: none">
		<button type="button" class="but_blue" onclick="printDataSignReturn('print')">打印签收回执</button>
		<button type="button" class="but_blue" 
			onclick="printPolicyImformationNotice();">保单信息通知书打印</button>
	</div>
	<div id="printDataSignDiv" style="display:none" ></div>
</div>
<script type="text/javascript">
	$(function() {
		$("#addCsAppDocPanel", navTab.getCurrentPanel()).find(
				"a,select,input,button").attr("disabled", "disabled");
		$("#addCsAppDocPanelBar", navTab.getCurrentPanel()).find(
				"a,select,input,button").attr("disabled", "disabled");
	});
</script>
<!-- 　********************应备资料按钮js********************** -->
<script type="text/javascript">
	//141437 start
	function showButtonAddCsAppDocPanelDiv(obj,str){
		var  objs= $(obj, navTab.getCurrentPanel()).attr("value");
		if("+"==objs){
			$(obj, navTab.getCurrentPanel()).val("\-");
			$("#"+str , navTab.getCurrentPanel()).show();
			$("#imgPlus" , navTab.getCurrentPanel()).hide();
			$("#imgMinus" , navTab.getCurrentPanel()).show();
		}else if("\-"==objs){
			$(obj, navTab.getCurrentPanel()).val("+");
			$("#"+str , navTab.getCurrentPanel()).hide();
			$("#imgPlus" , navTab.getCurrentPanel()).show();
			$("#imgMinus" , navTab.getCurrentPanel()).hide();
		}
	}
	//141437 end
	function haveAddDoc() {
		var addDoc = $
		{
			csAcceptInfoEntryVO.docNames
		}
		;
		if (addDoc == '') {
			alertMsg.info("没有需要添加的单证，请确认！");
		}
	}
	//编辑表单所有输入元素信息 ，列表信息状态位'已保存':'0','修改':'1'
	function _appDoc_M(tableId) {
		_cs_editTable($("#" + tableId, navTab.getCurrentPanel()));
		$("#addCsAppDocPanel", navTab.getCurrentPanel()).find(
				"a,select,input,button").removeAttr("disabled");
		$("#addCsAppDocPanelBar", navTab.getCurrentPanel()).find(
				"a,select,input,button").removeAttr("disabled");
		$("#addCsAppDocPanleBar_save", navTab.getCurrentPanel()).find(
				"a,select,input,button").removeAttr("disabled");
	};

	//应备资料保存
	function cs_entry_saveAppDoc(boxId, formId, jsonInputId) {
		var flag = 0;
		$("#documentTr tr[tr_saveStatus='1']").each(function() {

			var length = $(this).find("td:eq(4)").find("input").val();
			if (length.length > 20) {
				alertMsg.warn("备注文本框中输入超过20个字符，请重新输入");
				flag = 1;
			}
			if ($(this).attr("style") == null) {
				$(this).attr("tr_saveStatus", "0");
			}
		});
		if (flag == 0) {
			$box = $("#" + boxId, navTab.getCurrentPanel());
			$form = $("#" + formId, navTab.getCurrentPanel());
			$inputJson = $form.find("#" + jsonInputId);
			var $table = $form.find("table");
			if (($table).attr("table_saveStatus='0'") == '0') {
				return false;
			}
			;
			var _tableJs = tableToJson($table);
			if ("[]" == _tableJs) {
				alertMsg.info("没有待保存的应备资料数据，请确认！");
				return false;
			}
			$inputJson.val(_tableJs);
			$box.ajaxUrl({
				type : "POST",
				url : $form.attr("action"),
				data : $form.serialize(),
				callback : function() {
					$box.find("[layoutH]").layoutH();
					alertMsg.info("保存成功！");
				}
			});

			/* 	alertMsg.confirm("应备资料无变化，请确认。",{
					okCall : function() {
						$inputJson.val(_tableJs);
						$box.ajaxUrl({
							type : "POST",
							url : $form.attr("action"),
							data : $form.serialize(),
							callback : function() {
								$box.find("[layoutH]").layoutH();
								alertMsg.info("保存成功！");
							}
						});
					},
					cancleCall : function() {
						return false;
					}
				}); */
		}
		//取table里面的值
		function tableToJson(table) {
			var $trs = table.find("tbody tr[tr_saveStatus='0']");
			var _jsons = "[";
			var index = 0;
			$trs.each(function() {
				var $tds = $(this).find("td");
				_jsons += "{";
				_jsons += "'docName':'"
						+ ($($($tds[0]).find("select")[0]).find(
								"option:selected").val() == null ? $($tds[0])
								.text() : $($($tds[0]).find("select")[0]).find(
								"option:selected").val()) + "',";//申请应备资料
				_jsons += "'docProperty':'"
						+  $($($tds[1]).find("select")[0]).find("option:selected").val() 
						+ "',";//应备资料属性
				_jsons += "'docRemark':'"
						+ $($tds[1]).find("input[name='docRemark']").val()
						+ "',";//应备资料属性
				_jsons += "'originalFlag':'"
						+ $($tds[2]).find(
								"input:radio[name='" + index + "']:checked")
								.val() + "',";//原件/影印件
				_jsons += "'signStatus':'"
						+ $($($tds[3]).find("select")[0]).find(
								"option:selected").val() + "',";//签收状态
				_jsons += "'signRemark':'"
						+ $($($tds[4]).find("input")[0]).attr("value") + "',";//备注 
				_jsons += "'appDocId':'"
						+ $($($tds[5]).find("input")[0]).attr("value") + "',";//appDocId
				_jsons += "},";
				index += 1;
			});

			_jsons += "]";
			return _jsons;
		}
	}

	//删除应备资料
	function cs_accept_delAppDoc(obj) {
		var $appDocId = $(obj).parents("td").find("input");
		if ($appDocId.size() == 0) {
			delTrElemt($(obj));
		} else {
			$.ajax({
				type : 'POST',
				url : "${ctx}/cs/csAccept/delCsAppDoc_PA_acceptAction.action",
				data : 'csAppDocVO.appDocId=' + $appDocId.val(),
				cache : false,
				success : function() {

					delTrElemt($(obj));
				},
				error : DWZ.ajaxError
			});
		}
	}
	/*打印回执签收*/
	function printDataSignReturn(viewOrPrint) {
		
		//获取一个受理号 先获取这个表格
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var $dataTable = $("#csEntryTable", navTab.getCurrentPanel());
		var accpetCodeString = $dataTable.find("tr:eq(0) td:eq(0)").text();
	
		$.ajax({    
				type : "POST",
				url : getRootPath() + "/cs/applyvoucher/printDataSignReturn_PA_csEndorseApplyVoucherAction.action",
				data : "changeId="
					+ changeId
					+ "&acceptCode="
					+ accpetCodeString
					+ "&viewOrPrint="
					+ viewOrPrint,
				dataType : 'json',
				async:false,
				success : function(data) {
				if (data.documentResultVo != null) {
					$("#printDataSignDiv",navTab.getCurrentPanel()).css("display","block");
					$("#printDataSignDiv",navTab.getCurrentPanel()).html("<object  type='application/x-java-applet' >"
											+ "<param name=\"code\" value=\"SipRpcltApplet.class\" />"
											+ "<param name=\"archive\" value=\"SipRpclt.jar\" />"
											+ "<param name=\"reqId\" value='"
											+ data.documentResultVo.jobId
											+ "' />"
											+ "<param name=\"server\" value='"
											+ data.documentResultVo.serviceIp
											+ "'/>"
											+ "<param name=\"operation\" value='"
											+ data.documentResultVo.printType
											+ "' />" + "</object>");
					if(viewOrPrint=='view'){//如果是预览
						//不提示
					}else{
						alertMsg.correct('打印签收回执成功！');
					}
					$("#printDataSignDiv",navTab.getCurrentPanel()).css("display","none");
				}else{
					if(viewOrPrint=='view'){//如果是预览
						alertMsg.error('预览签收回执失败！');
					}else{
						alertMsg.error('打印签收回执失败！');
					}
				}
			}
		});
		
	}

	// 保单信息通知书打印
	function printPolicyImformationNotice() {
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		$
				.ajax({
					type : 'POST',
					url : 'cs/csEntry/printPolicyImformationNotice_PA_acceptOtherAction.action',
					data : 'changeId=' + changeId,
					cache : false,
					success : function(data) {
						var json = DWZ.jsonEval(data);
						if (json.statusCode == 200) {
							alertMsg.correct(json.message);
						} else if (json.statusCode == 300) {
							alertMsg.error(json.message);
						}
					}
				});
	}

	
</script>