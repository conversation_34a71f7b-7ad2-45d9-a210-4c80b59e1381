<!--guyy_wb 保全录入-受理和补退费信息-受理修改信息页面-->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<style>
.td {
	color: gray
}
</style>
<div class="pageContent" layoutH="36px">
	<div class="step_header">
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td id="n1" width="2%"><div class="main_n2">1</div></td>
				<td id="step1"><div class="main_stepOther">受理信息修改</div></td>
				<td id="n2" width="2%"><div class="main_n2">2</div></td>
				<td id="step2"><div class="main_stepOther">项目信息录入</div></td>
				<td id="n3" width="2%"><div class="main_n1d">3</div></td>
				<td id="step3"><div class="main_step">录入完成</div></td>
			</tr>
			<tr>
				<td height="8"></td>
			</tr>
		</table>
	</div>
	<input id="changeId" type="hidden" name="changeId"
		value="${finishEntryVO.changeId}" />
	<form id="EntryFinishForm"
		action="${ctx }/cs/csEntry/printRider_PA_csEntryAction.action"
		class="pageForm required-validate" method="post"
		onsubmit="return validateCallback(this)">
		<input type="hidden" name="acceptCode" id="acceptCode" />
	</form>
	<div class="divfclass">
		<h1>
			<img src="images/tubiao.png">录入完成
		</h1>
	</div>
	<div class="tabdivclass">
		<div class="panelBar">
			<ul class="toolBar">
				<li class=""><a class="add" onclick="refush()"
					href="javascript:void(0)"><span>状态刷新</span></a></li>
				<li class="line">line</li>
			</ul>
		</div>
		<table class="list" style="width: 100%" id="CsChangeInfoTable">
			<thead>
				<tr>
					<th>选择</th>
					<th>保全受理号</th>
					<th>保全项目</th>
					<th>保单号</th>
					<th>保全项目状态</th>
					<th>保全受理下一节点</th>
				</tr>
			</thead>
			<tbody>
				<s:iterator value="finishEntryVO.csAcceptInfoVOList" status="csVo">
					<tr onclick="setChangeId(this);">
						<td><input type="radio"  name="acceptCode" value="${acceptCode }" /> 
							<input type="hidden" name="changeId" value="${changeId }"></td>
						<td>${acceptCode}</td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE"
								value="${serviceCode }" /> <input type="hidden"
							name="serviceCode" id="serviceCode" value="${serviceCode }" /></td>
						<td>${policyCodeS}</td>
						<td><Field:codeValue
								tableName="APP___PAS__DBUSER.T_ACCEPT_STATUS"
								value="${acceptStatus }" /></td>
						<td>${nextPoint}</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	</div>
	<div>
		<table>
			<tr>
				<td></td>
				<td>
					<div style="margin-left: 400px" class="buttonContent">
<!-- 						<button type="button" class="but_blue" onclick="acceptPrint('view')">预览免填单申请书</button> -->
<!-- 						<button type="button" class="but_blue" onclick="acceptPrint('print')">打印免填单申请书</button> -->
						<button type="button" class="but_blue" style="display: none" onclick="acceptReceipt('print')">打印签收回执csEntryFinish.jsp</button>
						<button type="button" class="but_blue"
							id="policyImformationNotice" style="display: none;"
							onclick="PolicyImformationPrint()">打印保单信息通知书</button>
						<button type="button" class="but_blue" onclick="scan()">扫描</button>
						<button class="close but_gray">退出</button>
					</div>
				</td>
				<td></td>
			</tr>
		</table>
	</div>
	<div id="printDataSignDiv" style="display: none"></div>
</div>


<script type="text/javascript">
	// 	$(function(){
	// 		var count = 0;
	// 		if(count<2){
	// 			setTimeout(function(){
	// 				alert(count);
	// 			},5000);
	// 		}
	// 	});
	function refush() {
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		navTab.reload("${ctx}/cs/csEntry/refushEntryFinish_PA_csEntryAction.action?changeId="+changeId,	{});
	}

	function printEndorsement(formId) {
		var serviceCode = $("#serviceCode", navTab.getCurrentPanel()).val();
		//alert(serviceCode);
		if (serviceCode == "RB" || serviceCode == "XQ") {
			//alert("此保全项无批单！");
			//alertMsg.correct("此保全项无批单！");
			alertMsg.info("该保全项目不需要打印批单！");
			return;
		}

		$("#" + formId, navTab.getCurrentPanel()).submit();
	}
// 	function acceptPrint(viewOrPrint) {
// 		//获取一个受理号 先获取这个表格
// 		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
// 		var accpetCodeString = "";
// 		var $dataTable = $("#CsChangeInfoTable", navTab.getCurrentPanel());
// 		//获取表格中选中的tr
// 		var $radio = $dataTable.find(":radio:checked");
// 		if ($radio.size() == 0) {//如果没选就默认选第一个
// 			var $radioFirst = $dataTable.find(":radio:eq(0)");
// 			accpetCodeString = $radioFirst.val();
// 		} else {
// 			accpetCodeString = $radio.val();
// 		}

// 		$.ajax({
// 				type : "POST",
// 				url : "${ctx}/cs/applyvoucher/printCsApplyVoucher_PA_csEndorseApplyVoucherAction.action",
// 				data : "changeId=" + changeId + "&acceptCode="
// 						+ accpetCodeString + "&viewOrPrint=" + viewOrPrint,
// 				dataType : 'json',
// 				async : false,
// 				success : function(data) {
// 					if (data.msgVO.successed) {
// 						$("#printDataSignDiv", navTab.getCurrentPanel())
// 								.css("display", "block");
// 						$("#printDataSignDiv", navTab.getCurrentPanel())
// 								.html(
// 										"<object  type='application/x-java-applet' >"
// 												+ "<param name=\"code\" value=\"SipRpcltApplet.class\" />"
// 												+ "<param name=\"archive\" value=\"SipRpclt.jar\" />"
// 												+ "<param name=\"reqId\" value='"
// 										+ data.msgVO.reqId
// 										+ "' />"
// 												+ "<param name=\"server\" value='"
// 										+ data.msgVO.server
// 										+ "'/>"
// 												+ "<param name=\"operation\" value='"
// 										+ data.msgVO.printType
// 										+ "' />"
// 												+ "</object>");
// 						if (viewOrPrint == 'view') {//如果是预览
// 							//不提示
// 						} else {
// 							alertMsg.correct('打印免填单申请书成功！');
// 						}
// 						$("#printDataSignDiv", navTab.getCurrentPanel())
// 								.css("display", "none");
// 					} else {
// 						if (viewOrPrint == 'view') {//如果是预览
// 							//alertMsg.error('预览签收回执失败！');
// 							alertMsg.error(data.msgVO.message);
// 						} else {
// 							alertMsg.error(data.msgVO.message);
// 						}
// 					}
// 				}
// 			});

// 	}

	/**
	 * 签收回执单打印
	 */
	function acceptReceipt(viewOrPrint) {
		//获取一个受理号 先获取这个表格
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var accpetCodeString = "";
		var $dataTable = $("#CsChangeInfoTable", navTab.getCurrentPanel());
		//获取表格中选中的tr
		var $radio = $dataTable.find(":radio:checked");
		if ($radio.size() == 0) {//如果没选就默认选第一个
			var $radioFirst = $dataTable.find(":radio:eq(0)");
			accpetCodeString = $radioFirst.val();
		} else {
			accpetCodeString = $radio.val();
		}

		$
				.ajax({
					type : "POST",
					url : getRootPath()
							+ "/cs/applyvoucher/printDataSignReturn_PA_csEndorseApplyVoucherAction.action",
					data : "changeId=" + changeId + "&acceptCode="
							+ accpetCodeString + "&viewOrPrint=" + viewOrPrint,
					dataType : 'json',
					async : false,
					success : function(data) {
						if (data.documentResultVo != null) {
							$("#printDataSignDiv", navTab.getCurrentPanel())
									.css("display", "block");
							$("#printDataSignDiv", navTab.getCurrentPanel())
									.html(
											"<object  type='application/x-java-applet' >"
													+ "<param name=\"code\" value=\"SipRpcltApplet.class\" />"
													+ "<param name=\"archive\" value=\"SipRpclt.jar\" />"
													+ "<param name=\"reqId\" value='"
											+ data.documentResultVo.jobId
											+ "' />"
													+ "<param name=\"server\" value='"
											+ data.documentResultVo.serviceIp
											+ "'/>"
													+ "<param name=\"operation\" value='"
											+ data.documentResultVo.printType
											+ "' />"
													+ "</object>");
							if (viewOrPrint == 'view') {//如果是预览
								//不提示
							} else {
								alertMsg.correct('打印签收回执成功！');
							}
							$("#printDataSignDiv", navTab.getCurrentPanel())
									.css("display", "none");
						} else {
							if (viewOrPrint == 'view') {//如果是预览
								alertMsg.error('预览签收回执失败！');
							} else {
								alertMsg.error('打印签收回执失败！');
							}
						}
					}
				});

	}

	function PolicyImformationPrint() {
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		$
				.ajax({

					type : "post",
					dataType : "text",
					url : "${ctx}/cs/csAccept/printPolicyImformationNotice_PA_acceptOtherAction.action",
					data : 'changeId=' + changeId,
					success : function(data) {
						var json = jQuery.parseJSON(data);
						alertMsg.info(json.message);
					}
				});
	}

	//设置申请ID
	var acceptStatus = "";
	function setChangeId(obj) {
		var acceptCode = $(obj).parent().parent().find("td:eq(0)").find("input[name='acceptCode']").val();
 		var changeId = $(obj).parent().parent().find("td:eq(0)").find("input[name='changeId']").val();
// 		var acceptCode = $(obj).val();
		$("#acceptCode", navTab.getCurrentPanel()).val(acceptCode);
		$("#changeId", navTab.getCurrentPanel()).val(changeId);
		acceptStatus = $(obj).parent().parent().find("td:eq(5)").find(
				"input:eq(0)").val();
	}

	function scan() {
		var acceptCode = $("#acceptCode", navTab.getCurrentPanel()).val();
		if (acceptCode == "-" || acceptCode == "" || acceptCode == null
				|| acceptCode == 'undefined') {
			alertMsg.error("请选择待扫描任务!");
			return;
		}

		var changeId = $("#changeId", navTab.getCurrentPanel()).val();

		var resultObj = "";
		if (acceptStatus != "") {
			resultObj = acceptStatus;
		} else {
			alertMsg.error("请选择待扫描任务!");
			return;
		}
		var flagParm;
		var intResult = Number(resultObj);
		if (intResult >= 7) {
			flagParm = 2;
		} else {
			flagParm = 0;
		}

		var rootPath = getRootPath();
		$
				.ajax({
					type : 'POST',
					url : rootPath
							+ '/cs/csAccept/dataUpdate_PA_acceptOtherAction.action?changeId='
							+ changeId + '&acceptCode=' + acceptCode
							+ '&imageFlag=2&lpStatusFlag=' + flagParm,
					cache : false,
					success : function(data) {
						window
								.open(
										data,
										'',
										'width='
												+ (window.screen.availWidth - 10)
												+ ',height='
												+ (window.screen.availHeight - 30)
												+ ',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');
					},
					error : DWZ.ajaxError
				});
	}
	//$("#acceptCode", navTab.getCurrentPanel()).val("-");

	
</script>