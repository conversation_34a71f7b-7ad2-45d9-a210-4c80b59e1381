<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-补退费信息-保单补退费div页面 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">


<div class="pageHeader">
	<form method="post"
		action="${ctx}/cs/csEntry/selectReturnNumber_PA_csEntryAction.action"
		onsubmit="return dwzSearch(this, 'dialog');">
		<div class="searchBar">
			<ul class="searchContent">
				<li><label>支行银行名称</label> <input class="textInput"
					name="bankOfDepositVO.correspondentName"
					value="${bankOfDepositVO.correspondentName }" type="text"></li>
				<li><label>联行号</label> <input class="textInput"
					name="bankOfDepositVO.correspondentNo"
					value="${bankOfDepositVO.correspondentNo }" type="text"></li>
			</ul>
			<ul class="searchContent">
				<li><label>银行名称</label> <input class="textInput"
					name="bankOfDepositVO.bankName"
					value="${bankOfDepositVO.bankName }" type="text"></li>
				<li><label>银行代码</label> <input class="textInput"
					name="bankOfDepositVO.bankCode"
					value="${bankOfDepositVO.bankCode }" type="text"></li>
			</ul>
			<div class="subBar">
				<ul>
					<li><div class="buttonActive">
							<div class="buttonContent">
								<button type="submit">查询</button>
							</div>
						</div></li>
				</ul>
			</div>
		</div>
	</form>
</div>




<div class="tabdivclass" id="uuy">
	<s:include value="/cs/pages/csEntry/districtBankCodeReturn.jsp"></s:include>
</div>
