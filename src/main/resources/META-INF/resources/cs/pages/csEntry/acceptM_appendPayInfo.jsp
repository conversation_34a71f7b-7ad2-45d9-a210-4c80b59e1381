<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-补退费信息 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript">
	$(function() {
		$("#appendPayInfoTable", navTab.getCurrentPanel()).find("select").css(
				"width", '100px');
		var pageFlag = $("#pageFlag", navTab.getCurrentPanel()).val();
		if(pageFlag==2){
			$divId = $("#appendPayInfo", navTab.getCurrentPanel());
			$divId.find("select").attr("disabled","disabled");
			$divId.find("a").attr("disabled","disabled");
			$divId.find("input").attr("disabled","disabled");
			$divId.find("button").attr("disabled","disabled");
		}
	});
	// 	$(function() {
	// 		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
	// 		var policyCodes = "";
	// 		$("#appendPayInfoTable", navTab.getCurrentPanel()).find("tr").each(
	// 				function() {
	// 					policyCodes = policyCodes + "'"
	// 							+ $(this).find("td:eq(2)").text() + "',";
	// 				});
	// 		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
	// 		var customerName = $("#customerName", navTab.getCurrentPanel()).val();
	// 		var parentDiv = $("#appendPayInfo", navTab.getCurrentPanel());
	// 		var rel = $("#bankAccountInfoDiv", parentDiv);
	// 		rel
	// 				.loadUrl("${ctx}/cs/common/loadBankAccountInfoPremArap_PA_bankAccountInfoAction.action?customerId="
	// 						+ customerId
	// 						+ "&policyCodes="
	// 						+ policyCodes
	// 						+ "&changeId="
	// 						+ changeId
	// 						+ "&customerName="
	// 						+ encodeURI(encodeURI(customerName)));
	// 	});
	// 	function payChanBank(targe) {
	// 		var check = false;
	// 		var payModeBank = $(targe).find("option:selected").val();
	// 		if (payModeBank == 30 || payModeBank == 32 || payModeBank == 31) {
	// 			check = true;
	// 		}
	// 		if (check) {
	// 			$("#bankAccountInfoDiv", navTab.getCurrentPanel()).show();
	// 		} else {
	// 			$("#bankAccountInfoDiv", navTab.getCurrentPanel()).hide();
	// 		}
	// 	}
	function saveCsAppendPayInfo() {
		var tableJson = "[";
		$("#appendPayInfoTable", navTab.getCurrentPanel()).find("tr").each(
				function() {
					tableJson = tableJson + "{'changeId':'"
							+ $(this).find("td:eq(0)").text() + "',";
					tableJson = tableJson + "'acceptCode':'"
							+ $(this).find("td:eq(1)").text() + "',"
					tableJson = tableJson + "'policyCode':'"
							+ $(this).find("td:eq(2)").text() + "',"
					tableJson = tableJson
							+ "'payMode':'"
							+ $(this).find("td:eq(3)").find('option:selected')
									.val() + "',"
					tableJson = tableJson + "'payeeName':'"
							+ $(this).find("td:eq(4)").find('input').val()
							+ "',"
					tableJson = tableJson
							+ "'certiType':'"
							+ $(this).find("td:eq(5)").find('option:selected')
									.val() + "',"
					tableJson = tableJson + "'certiCode':'"
							+ $(this).find("td:eq(6)").find('input').val()
							+ "',"
					tableJson = tableJson
							+ "'bankCode':'"
							+ $(this).find("td:eq(7)").find('option:selected')
									.val() + "',"
					tableJson = tableJson + "'bankAccount':'"
							+ $(this).find("td:eq(8)").find('input').val()
							+ "',"
					tableJson = tableJson + "'bankUserName':'"
							+ $(this).find("td:eq(9)").find('input').val()
							+ "'},"
				});
		tableJson = tableJson.substring(0, tableJson.length - 1);
		tableJson += "]";
		tableJson = encodeURI(tableJson);
		$("#appendPayTable", navTab.getCurrentPanel()).val(tableJson);
		$("#saveAppendPayForm", navTab.getCurrentPanel()).submit();
	}
</script>
<div class="divfclass">
	<h1>
		<img src="${ctx}/cs/img/icon/tubiao.png" />补退费预收信息
	</h1>
</div>
<input type="hidden" id="pageFlag"
	value="${csAcceptInfoEntryVO.pageFlag}" />
<div id="appendPayInfo">
	<div class="tabdivclass" style="background-color: white;">
		<table class="list" style="width: 100%">
			<thead>
				<tr>
					<th style="display: none;">changeId</th>
					<th style="display: none;">acceptCode</th>
					<th>保单号</th>
					<th>收付费方式</th>
					<th>收付款人姓名</th>
					<th>收付款人证件类型</th>
					<th>收付款人证件号码</th>
					<!-- <th>权益人</th>
					<th>权益人类型</th> -->
					<th>银行编码</th>
					<th>银行账户</th>
					<th>户名</th>
				</tr>
			</thead>
			<tbody id="appendPayInfoTable">
				<s:iterator value="csAppendPayInfoVOs" status="sst">
					<tr>
						<td style="display: none;">${changeId}</td>
						<td style="display: none;">${acceptCode}</td>
						<td>${policyCode}</td>
						<td><Field:codeTable value="${payMode}" name=""
								tableName="APP___PAS__DBUSER.T_POLICY_PAY_MODE_COLL" /></td>
						<td><input type="text" value="${payeeName}"></td>
						<td><Field:codeTable value="${certiType}" name=""
								tableName="APP___PAS__DBUSER.T_CERTI_TYPE" /></td>
						<td><input type="text" value="${certiCode}"></td>
						<%-- <td>${customerName}</td>
						<td>投保人</td> --%>
						<td><Field:codeTable value="${bankCode}" name=""
								tableName="APP___PAS__DBUSER.T_BANK" /></td>
						<td><input type="expandBankAccount" class="required"
							maxlength="28" value="${bankAccount}" /></td>
						<td><input type="text" value="${bankUserName}"></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
	</div>
	<form id="saveAppendPayForm"
		action="cs/csEntry/saveAppendPayInfo_PA_csEntryAction.action?menuId=${menuId}"
		method="post" onsubmit="return validateCallback(this);">
		<input type="hidden" id="appendPayTable" name="appendPayTable"
			value="">
	</form>
	<div class="pageFormbut">
		<dl class="nowrap" style="width: 100%">
			<dd style="width: 100%">
				<table style="width: 100%">
					<tbody>
						<tr>
							<td></td>
							<td style="width: 60px">
								<button type="button" class="but_blue"
									onclick="saveCsAppendPayInfo(this)">保存</button>
							</td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</dd>
		</dl>
	</div>
</div>