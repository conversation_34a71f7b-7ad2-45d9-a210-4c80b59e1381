<!--guyy_wb 保全录入-受理和补退费信息-受理信息修改页面-->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%> 
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_accept.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<script type="text/javascript">
	var bank_index = 1;
</script>
<form id="saveAcceptInfoForm"
	action="cs/csEntry/saveAcceptInfo_PA_csEntryAction.action?menuId=${menuId}"
	method="post" onsubmit="return navTabSearch(this);">
	<input type="hidden" id="changeId" name="changeId" value="${changeId}"
		class="oneChangeId" /> <input type="hidden" id="customerId"
		name="customerId" value="${customerId}" /> <input type="hidden"
		id="acceptId" name="acceptId" value="${acceptId}" class="oneAcceptId" />
	<input type="hidden" id="mtFlag1" name="MtFlag" value="${MtFlag}" />
	<!--免填标记  -->
	<input type="hidden" id="jsonStr" name="jsonStr" value="" /> <input
		type="hidden" id="isSameOperator" value="${isSameOperator}" /> <input
		type="hidden" id="pageFlag" value="${csAcceptInfoEntryVO.pageFlag}" />
	<input type="hidden" id="outTimeFlag" name="outTimeFlag" value="${csAcceptInfoEntryVO.outDay}" />
	<!-- ************************************* -->
</form>
<form id="csAppDocUpForm"
	action="${ctx}/cs/csAccept/showAcceptEntryPage_PA_acceptAction.action?menuId=${menuId}"
	method="post">
	<input type="hidden" value="${csAcceptInfoEntryVO.changeId }"
		name="csAcceptInfoEntryVO.changeId" /> <input type="hidden"
		name="customerId" /> <input type="hidden"
		name="manualCancelApplyVO.acceptIds" /> <input type="hidden"
		value="${csAcceptInfoVOList[0].serviceCode}" name="serviceCode"
		id="serviceCode" /> <input type="hidden"
		value="${csAcceptInfoEntryVO.policyInfoVOList[0].policyCode}"
		name="policyCode" id="policyCode" /> <input type="hidden"
		id="bizCode" name="bizCode"
		value="${csAcceptInfoEntryVO.csApplicationVO.applyCode}" />
		<input type="hidden" name="csReviewVO.customerId" value="${csReviewVO.customerId}"/>
		<input type="hidden" value="${csAcceptInfoVOList[0].acceptCode}"
		name="acceptCode" id="acceptCode" /> 
</form>
<div class="pageContent" layoutH="100" id="useForPosition">
	<!-- ****************进度条***********************begin************ -->
	<s:if test="acceptStatus==07">
	<div class="step_header" id="entryId">
		<table  id="stepTable" width="100%" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr align="center">
				<!-- <td class="n1d" id="n1"></td>
				<td class="step" id="step1">受理信息修改</td>
				<td class="n2" id="n2"></td>
				<td class="step_other" id="step2">项目信息录入</td>
				<td class="n3" id="n3"></td>
				<td class="step_other" id="step3">录入完成</td> -->
				
				<td id="n1" width="2%"><div class="main_n1d">1</div></td>
				<td id="step1"><div class="main_step">受理信息修改</div></td>
				<td id="n2" width="2%"><div class="main_n2">2</div></td>
				<td id="step2"><div class="main_step">项目信息录入</div></td>
				<td id="n3" width="2%"><div class="main_n2">3</div></td>
				<td id="step3"><div class="main_step">录入完成</div></td>
			</tr>
			<tr>
				<td height="8"></td>
			</tr>
			</tbody>
		</table>
	</div>
	</s:if>
	<s:else>
	<div class="step_header" id="entryId">
		<table  id="stepTable" width="100%" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr align="center">
				<td id="n1" width="2%"><div class="main_n1d">1</div></td>
				<td id="step1"><div class="main_step">受理信息修改</div></td>
				<td id="n2" width="2%"><div class="main_n2">2</div></td>
				<td id="step2"><div class="main_stepOther">项目信息录入</div></td>
				<td id="n3" width="2%"><div class="main_n2">3</div></td>
				<td id="step3"><div class="main_stepOther">录入完成</div></td>
			</tr>
			<tr>
				<td height="8"></td>
			</tr>
			</tbody>
		</table>
		</div>
	</s:else>
	<!-- 试算 -->
	<div class="step_header" id="entryNcyId">
		<table width="100%" border="0" cellspacing="0" cellpadding="0">
			    <!-- <tr align="left">
				<td class="n1d" id="n1"></td>
				<td class="step" id="step1">客户查询</td>
				<td class="n2" id="n2"></td>
				<td class="step" id="step2">试算受理信息录入</td>
				<td class="n3" id="n3"></td>
				<td class="step" id="step3">试算项目信息录入</td>
				<td class="n4" id="n4"></td>
				<td class="step_other" id="wsystep4">查看试算结果</td>
			</tr> --> 
			<tr>
				<td id="n1" width="2%"><div class="main_n1d">1</div></td>
				<td id="step1"><div class="main_step">客户查询</div></td>
				<td id="n2" width="2%"><div class="main_n2">2</div></td>
				<td id="step2"><div class="main_stepOther">试算受理信息录入</div></td>
				<td id="n3" width="2%"><div class="main_n2">3</div></td>
				<td id="step3"><div class="main_stepOther">试算项目信息录入</div></td>
				<td id="n4" width="2%"><div class="main_n2">4</div></td>
				<td id="wsystep4"><div class="main_stepOther">查看试算结果</div></td>
			</tr>
			<!-- <tr>
						<td id="n1" width="2%"><div class="main_n1d">1</div></td>
						<td id="step1"><div class="main_step">客户查询</div></td>
						<td id="n2" width="2%"><div class="main_n2">2</div></td>
						<td id="step2"><div class="main_stepOther">试算受理信息录入</div></td>
						<td id="n3" width="2%"><div class="main_n2">3</div></td>
						<td id="step3"><div class="main_stepOther">试算项目信息录入</div></td>
						<td id="n4" width="2%"><div class="main_n2">4</div></td>
						<td id="step4"><div class="main_stepOther">查看试算结果</div></td>
			</tr> -->
			<tr>
				<td height="8"></td>
			</tr>
		</table>
	</div>
	<!-- 试算 -->
	<!-- ****************进度条***********************begin************ -->
	<div class="pageFormContent">
		<form id="changeIdForm">
			<input type="hidden" id="customerId" value="${customerVO.customerId}" />
			<dl>
				<dt>
					<span style="color: red">保全申请号：</span>
				</dt>
				<dd>
					<input type="hidden" id="changeId"
						value="${csAcceptInfoEntryVO.csApplicationVO.changeId }" /> <input
						id="applyCode" readonly="readonly"
						value="${csAcceptInfoEntryVO.csApplicationVO.applyCode }" />
				</dd>
			</dl>
			<s:if test="csAcceptInfoEntryVO.csApplicationVO.tryCalcNo != null">
				<s:if test="csAcceptInfoEntryVO.csApplicationVO.appStatus == '11'">
				<dl id='tryCalcNoDL'>
					<dt>
						<span style="color: red">试算号：</span>
					</dt>
					<dd>
						<input id="tryCalcNo" readonly="readonly"
							value="${csAcceptInfoEntryVO.csApplicationVO.tryCalcNo }" />
					</dd>
				</dl>
				</s:if>
			</s:if>
		</form>
	</div>
	<div class="pageFormContent">

		<dl>
			<dt>姓名</dt>
			<dd>
				<input id="customerName" readonly="readonly"
					value="${customerVO.customerName }" />
			</dd>
		</dl>
		<dl>
			<dt>生日</dt>
			<dd>
				<input readonly="readonly" id="acceptModifyCustomerBirthday"
					value='<s:date name="customerVO.customerBirthday" format="yyyy-MM-dd"/>' />
			</dd>
		</dl>
		<dl>
			<dt>证件类型</dt>
			<dd>
				<input readonly="readonly"
					value='<Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${customerVO.customerCertType }"/>' />
				<input type="hidden" id="acceptModifyCustomerCertType" value="${customerVO.customerCertType }" />
			</dd>
		</dl>
		<dl>
			<dt>证件号码</dt>
			<dd>
				<input readonly="readonly" value="${customerVO.customerCertiCode }" />
				<s:if test="csAcceptInfoEntryVO.csApplicationVO.isAutoInput == 1"><font style="color: red; font-size:10px;">(自动录入)</font></s:if>
				<s:if test="csAcceptInfoEntryVO.csApplicationVO.isAutoInput == 0"><font style="color: red; font-size:10px;">(手动录入)</font></s:if>
			</dd>
		</dl>
		<dl>
			<dt>性别</dt>
			<dd>
				<input readonly="readonly"
					value='<Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value="${customerVO.customerGender }"/>' />
			</dd>
		</dl>
	</div>
	<!-- 受理信息*********begin*************|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||******** -->
	<div id="acceptInfoDiv" class="pageContent" modify_status='0'
		style="background-color: white; display: inline;">
		<s:include value="acceptM_acceptInfo.jsp"></s:include>
	</div>
	<!-- 受理信息*********end***********|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||* -->

	<!-- 身份验真信息*********begin*************|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||******** -->
	<div id="identityDiv" class="pageContent" modify_status='0'
		style="background-color: white; display: inline;">
		<s:include value="acceptM_idetityResult.jsp"></s:include>
	</div>
	<!-- 身份验真信息*********end***********|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||* -->

	<!-- 应备资料*********begin**********|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||** -->
	<div id="addCsAppDocDiv" <s:if test="csAcceptInfoEntryVO.csApplicationVO.tryCalcNo!=null">style="display: none;"</s:if>  >
		<s:include value="acceptM_csAppDoc.jsp"></s:include>
	</div>
	<!-- 应备资料*********end**********|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||** -->


	<!-- 保全项目信息录入*********begin**|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||********** -->
	<div id="csServiceEntryDiv">
		<s:include value="acceptM_csServiceEntry.jsp"></s:include>
	</div>



	<!-- 保全信息录入 补退费信息  start |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||| -->
	<div id="csPremArapDiv">
		<s:if test="csPremArapVOList != null && csPremArapVOList.size()>0 && premSave == 1">
			<s:include value="acceptM_premArapInfo.jsp"></s:include>
		</s:if>
		<s:if test="csAppendPayInfoVOs !=null && csAppendPayInfoVOs.size()!=0">
			<s:include value="acceptM_appendPayInfo.jsp"></s:include>
		</s:if>
	</div>
	<!-- 保全信息录入 补退费信息  end |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||| -->

   <!-- 保全信息录入 回访电话录入  start-->
	<div id="returnVisitTelDiv">
		<s:include value="returnVisitTel.jsp"></s:include>
	</div>
	<!-- 保全信息录入 回访电话录入  end-->

	<!-- 保全项目信息录入*********end**|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||********** -->
	<%-- <div class="pageFormContent" id="acceptSummeryDiv">
		<s:include value="acceptM_summery.jsp"></s:include>
	</div> --%>
	<!-- 保全录入退回受理*********end**|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||********** -->
	<div class="pageFormContent" id="acceptBackToAcceptDiv">
		<s:include value="acceptM_backToAccept.jsp"></s:include>
	</div>
	<!-- 保全受理超时*********end**|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||********** -->
	
	<div class="pageFormContent" id="acceptoutTimeInfoDiv">
		<s:include value="acceptM_outTimeInfo.jsp"></s:include>
	</div>


</div>

<div  id="tricalButtom">

	<!-- **********************************试算***************************************** -->
	<table style="width: 100%">
		<tbody>
			<tr>
				<td></td>
				<td style="width: 300px">
					<!-- <div class="button"> -->
						<div class="pageFormbut">
							<button type="button" class="but_blue"
								onclick="csEntry_tryCalNoFinish('tryCalcNoForm')">试算完成</button>
						<!-- </div> -->
					<!-- </div> -->

					<!-- <div class="button"> -->
						<!-- <div class="pageFormbut"> -->
							<button type="button" class="but_blue"
								onclick="csEntry_acceptTheFormal('acceptTheFormalForm')">转正式受理</button>
					<!-- 	</div> -->
				<!-- 	</div> -->

					<!-- <div class="button"> -->
						<!-- <div class="pageFormbut"> -->
							<button type="button" class="but_blue" onclick="_cs_back()">返回主界面</button>
						<!-- </div> -->
					</div>
				</td>
				<td></td>
			</tr>
		</tbody>
	</table>
	<!-- **********************************试算***************************************** -->


</div>
<div  id="acceptAndPayButton">
	<!-- <ul> -->
		<!-- <li style="float: left;"> -->
			<!-- <div class="button"> -->
			<div class="pageFormbut">
				<!-- <div class="buttonContent"> -->
					<button type="button" class="but_blue" style="position: relative; top: -10px; right: 5px;"
						onclick="_csEntry_mainEntryFinish('csEntryFinishForm')">申请录入完成</button>
						
					<s:if test="isTask == 1">	
						<a id="taskId"
							href="${ctx }/cs/csTask/showCsEntryTaskPoolPage_PA_csEntryTaskPoolAction.action?leftFlag=1&menuId=20290"
							target="navTab" rel="20290" title="保全录入任务池" fresh="false">
							<!-- <button >退出</button> -->
							<button class="but_gray" style="position: relative; top: -10px; right: 5px;">退出</button>
						</a>
					</s:if>
					
						<a target="dialog" rel="policyStatusQuery"
						href="${ctx}/cs/csAccept/policyStatusQuery_PA_acceptOtherAction.action?&menuId=${menuId}&customerId=${customerVO.customerId}"
						title="保单状态查询"><button style="position: relative; top: -10px; right: 5px;" type="submit" class="but_blue">保单状态查询</button></a>
						
					<button style="position: relative; top: -10px; right: 5px;" type="button" class="but_blue" onclick="_scansion()">扫描</button>
						<!-- 111454 disabled="disabled" -->
						<a id="relateImage"  onclick="relateImage()" href="javaScript:void(0)" target="navTab">
							<button style="position: relative; top: -10px; right: 5px;" class="but_blue" >关联影像</button>
						</a> 
						
						<a target="navTab" rel="policyStatusQuery"
							href="${ctx}/cs/common/loadCustomerListInfo_PA_customerIdentityCheckAction.action?applyCode=${csAcceptInfoEntryVO.csApplicationVO.applyCode}&checkProject=2&checkSource=1&checkOperateOrg=${csAcceptInfoEntryVO.csApplicationVO.organCode}&checkPolicyOrg=${csAcceptInfoEntryVO.csApplicationVO.organCode}"
							title="客户身份验真"><button style="position: relative; top: -10px; right: 5px;" type="submit" class="but_blue" >客户身份验真</button></a>
						
						<a target="navTab" rel="policyStatusQuery" width="1000"
						id="csSurvivalSurveyModify" onclick="getHrefModify();" href="javaScript:void(0)"
						title="发起生调"><button style="position: relative; top: -10px; right: 5px;" class="but_blue">发起生调</button></a>
						
						<%-- <a href="javaScript:void(0);" onclick="checkBizCall();"><button style="position: relative; top: -10px; right: 5px;" class="but_blue">发起回访</button></a> 
						<a id="toBizCallClmPage" href="javaScript:void(0)" target="navTab"
						style="display: none;" rel="toBizCallClmPage" ><button style="position: relative; top: -10px; right: 5px;" class="but_blue">发起回访</button></a>
						
						<a target="navTab" rel="policyStatusQuery"
						href="${ctx}/cs/manualitem/toClaimBizTurnPage_PA_claimBizTurnAction.action?applyBizCode=${csAcceptInfoEntryVO.csApplicationVO.applyCode} &applyPolicyCode=${csAcceptInfoEntryVO.policyInfoVOList[0].policyCode }"
						title="发起转办"><button style="position: relative; top: -10px; right: 5px;" class="but_blue" type="submit">发起转办</button></a> --%>
						
						<a target="navTab" rel="policyStatusQuery"
						href="${ctx}/cs/manualitem/toBizSignPage_PA_bizSignPageAction.action?bizCode=${csAcceptInfoEntryVO.csApplicationVO.applyCode}"
						title="发起签报"><button style="position: relative; top: -10px; right: 5px;" class="but_blue" type="submit">发起签报</button></a>
						
						<button style="position: relative; top: -10px; right: 5px;" type="button" class="but_blue" onclick="backoutModify()">撤销</button>
						
						<a target="navTab" rel="policyStatusQuery"
						href="${ctx}/cs/csNote/showNotePad_PA_csNoteAction.action?changeId=${csAcceptInfoEntryVO.changeId}&processCode=3004"
						title="记事本"><button style="position: relative; top: -10px; right: 5px;" class="but_blue"  type="submit">记事本</button></a>
						
						 <s:if test="csAcceptInfoVOList[0].serviceCode == 'IO' || csAcceptInfoVOList[0].serviceCode == 'CC'">
						<button style="position: relative; top: -10px; right: 5px;background:gray;" class="but_blue" disabled = "true">客户基本资料变更</button> 
						</s:if>
						<s:else>
						<a target="navTab" rel="policyStatusQuery" 
						href="${ctx}/cs/serviceitem_cchk/loadCCHKCusPage_PA_csEndorseCCHKAction.action?&customerVO.customerId=${customerVO.customerId}&customerVO.changeId=${csAcceptInfoEntryVO.changeId}&customerVO.acceptId=${acceptId}"
						title="客户基本资料变更"><button style="display: none" class="but_blue" disabled = "true">客户基本资料变更</button></a>
						</s:else> 
						<dl>
							<dt></dt>
							<dd></dd>
					    </dl>
						
						<a class="but_blue main_buta" id="_IDInput"  href="${ctx}/cs/common/initIDInputPage_PA_IDInputAction.action?changeId=${csAcceptInfoEntryVO.changeId}&applyCode=${csAcceptInfoEntryVO.csApplicationVO.applyCode}" maxable="false"
							minable="false" z-index="1" resizable="false" title="" width="1200" height="690" target="dialog"> 
							<span rel="">身份证信息读取</span>
						</a>	
						
						<button type="button" id="scanning" onclick="queryHistoryScanning();" <s:if test="csReviewVO.highFlag eq false">disabled="disabled"</s:if> class="but_blue">风险测评问卷查阅</button>
						
<!-- 						根据妍妍指示去掉该按钮 2018年11月22日15:58:36 -->
<!-- 						<a target="navTab" rel="showCustomerTax" -->
<%-- 							href="${ctx}/cs/csAccept/taxInfoLoad_PA_acceptAction.action?customerVO.customerId=${customerVO.customerId}" --%>
<!-- 							title="居民税收身份采集"><button type="submit" class="but_blue" >居民税收身份采集</button></a> -->
							
						<a target="navTab" rel="showPhoneRepeatTax" 
						   href="${ctx}/cs/csEntry/loadTelRepeatTest_PA_csEntryAction.action?changeId=${csAcceptInfoEntryVO.changeId}"
						   title="重复电话查询"><button  type="button" class="but_blue" >重复电话查询</button></a>
						   
					   <s:if test="csAcceptInfoVOList[0].serviceCode=='TC'">
						<button type="button" class="but_blue" onclick="queryRelateImageOne() ">调取信托公司影像</button>
		               </s:if>
		               
		               <a style="display: none" class="but_blue main_buta" id="_doubleAduit"  close maxable="false" minable="false" z-index="1" resizable="false" title="" 
		               	width="420" height="245" target="dialog" href="${ctx}/cs/pages/common/csDoubleAudit.jsp?changeId=?${csAcceptInfoEntryVO.changeId}" > 
								<span rel="">发起双人审核</span>
					 	</a>
					 	<button type="button" class="but_blue" onclick="openDoubleAudit() ">发起双人审核</button>
						<!-- add by shaocongwang 20200730_RM68551_客户既往验真 END -->
			</div>
</div>
<form id="csEntryFinishForm"
	action="cs/csEntry/csEntryFinish_PA_csEntryAction.action?menuId=${menuId}"
	method="post" class="required-validate"
	onsubmit="return navTabSearch(this);">
	<input type="hidden" name="changeId" value="${changeId }" />
	<input type="hidden" name="acceptId" id = "acceptIds" value="${acceptId}" />
	<input type="hidden" name="outTimeReason" id="outTimeReason" value="">
</form>

<!-- 点击试算完成 执行解业务锁 -->
<form id="tryCalcNoForm"
	action="cs/csEntry/tryCalNoFinish_PA_csEntryAction.action?menuId=${menuId}"
	method="post" class="required-validate"
	onsubmit="return validateCallback(this,navTabAjaxDone);">
	<input type="hidden" name="acceptId" value="${acceptId}" />
	<input type="hidden" name="changeId" value="${changeId }" />
</form>

<!-- 转正式受理 -->

<form id="acceptTheFormalForm"
	action="cs/csEntry/acceptTheFormal_PA_csEntryAction.action?menuId=${menuId}"
	method="post" class="required-validate"
	onsubmit="return validateCallback(this);">
	<input type="hidden" name="changeId" value="${changeId }" /> <input
		type="hidden" name="acceptId" value="${acceptId}" />
</form>

<script type="text/javascript">
	$(function() {
		var isSameOperator = $("#isSameOperator",navTab.getCurrentPanel()).val();
		var outTimeFlag = $("#outTimeFlag",navTab.getCurrentPanel()).val();
		//根据后台传值，0：是;1:否 (否时隐藏)
		if (isSameOperator == '1') {
			$("#acceptBackToAcceptDiv",navTab.getCurrentPanel()).hide();
		}
		//根据后台传值，0：超时不隐藏;1:不超时隐藏 (否时隐藏)
		if(outTimeFlag == 1){
			$("#acceptoutTimeInfoDiv",navTab.getCurrentPanel()).hide();
		}
		//页面类型：0或空：可操作；1-仅查询
		var tPageFlag = $("#pageFlag", navTab.getCurrentPanel()).val();
		if (tPageFlag == '1') {
			$("#acceptAndPayButton", navTab.getCurrentPanel()).hide();
			$("#tricalButtom", navTab.getCurrentPanel()).hide();
		}
		
		
		if (tPageFlag == '2') {
			$("#acceptAndPayButton", navTab.getCurrentPanel()).hide();
			$("#tricalButtom", navTab.getCurrentPanel()).hide();
			$("#acceptBackToAcceptDiv", navTab.getCurrentPanel()).hide();
			$("#buttonDiv", navTab.getCurrentPanel()).hide();
			$("#buttonDiv1", navTab.getCurrentPanel()).hide();
			
			$("#saveAppliyButton", navTab.getCurrentPanel()).attr("disabled","disabled");
			
			/************保存 修改 按钮**************/
			$("#modifyAcceptInfo", navTab.getCurrentPanel()).attr("disabled","disabled");
			$("#saveAcceptInfo", navTab.getCurrentPanel()).attr("disabled","disabled");
			
			/************应备资料 **************/
			$("#csAppDoc", navTab.getCurrentPanel()).attr("disabled","disabled");
			$("#addCsAppDocPanelBar", navTab.getCurrentPanel()).attr("disabled","disabled");
			$("#addCsAppDocPanleBar_save", navTab.getCurrentPanel()).attr("disabled","disabled");
			
			$("#addCsAppDocPanleBar_save",navTab.getCurrentPanel()).find("button").each(function(){
				$(this).attr("disabled","disabled");
			});
		}
		
		//定位至录入位置
		goToTarget();

	});
	
    function goToTarget() {
        $("#useForPosition").animate({scrollTop: $("#csServiceEntryDiv").offset().top - 200 + "px"}, 500);//定位到录入按钮
    }
    
  //调取信托公司影像
	function queryRelateImageOne() {
		//var  rootPath= getRootPath();
		var acceptCode = $("#acceptCode", navTab.getCurrentPanel()).val();
		var _changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var applyCode = $("#applyCode", navTab.getCurrentPanel()).val();
		$
				.ajax({
					type : 'POST',
					url : '${ctx}/cs/csAccept/getImageInfoForQuaryPage2_PA_acceptOtherAction.action?changeId='
							+ _changeId
							+ '&applyCode='
							+ applyCode
							+ '&acceptCode=' + acceptCode + '&imageFlag=4',
					success : function(data) {
						window
								.open(
										data,
										'',
										'width='
												+ (window.screen.availWidth - 10)
												+ ',height='
												+ (window.screen.availHeight - 30)
												+ ',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');
					},
					error : DWZ.ajaxError
				});

	}

  	//开启双人审核流程
	function openDoubleAudit() {
		//弹出双人审核窗口
		var changeId = $("#changeId",navTab.getCurrentPanel()).val();
		var url = "${ctx}/cs/pages/common/csDoubleAudit.jsp?changeId=" + changeId + "&openTypeFlag=" + "1";
		$("#_doubleAduit").attr("href", url).click();
	}

	function showButton(obj,str){
		
		var  objs= $(obj, navTab.getCurrentPanel()).attr("value");
		if("+"==objs){
			$(obj, navTab.getCurrentPanel()).val("\-");
			$("#"+str , navTab.getCurrentPanel()).hide();
		}else if("\-"==objs){
			$(obj, navTab.getCurrentPanel()).val("+");
			$("#"+str , navTab.getCurrentPanel()).show();
		}
	}
</script>

<script type="text/javascript">
	//判断是否发起过回访
	function checkBizCall() {
		var bizCode = $("#bizCode",navTab.getCurrentPanel()).val();
		var sceneCode = 'CS02';
		$
				.ajax({
					url : "cs/manualitem/checkBizCall_PA_bizCallClmAction.action",
					type : "post",
					data : {
						"bizCode" : bizCode,
						"sceneCode" : sceneCode
					},
					dataType : "json",
					cache : false,
					success : function(data) {
						if (data.statusCode == '300') {
							alertMsg
									.confirm(
											"本次业务已经有回访成功的电话任务，是否仍需要继续进行回访?",
											{
												okCall : function() {
													var url = "cs/manualitem/toBizCallClmPage_PA_bizCallClmAction.action?bizCode="
															+ bizCode
															+ "&sceneCode="
															+ sceneCode;
													$(
															"#toBizCallClmPage",
															navTab
																	.getCurrentPanel())
															.attr("href", url)
															.click();
												}
											});
						} else {
							var url = "cs/manualitem/toBizCallClmPage_PA_bizCallClmAction.action?bizCode="
									+ bizCode + "&sceneCode=" + sceneCode;
							$("#toBizCallClmPage", navTab.getCurrentPanel())
									.attr("href", url).click();
						}
					},
					error : function() {
						alertMsg.error("检查回访状态时出错，请稍后重试！");
					}
				});
	}
	/**集成业务公共页面 --lvwq_wb*/
	function openCommonPage() {
		var applyCode = $("#applyCode",navTab.getCurrentPanel()).val();
		var title = "业务公共";
		var tabid = "_commonPage";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csEntry/openCommonPage_PA_csEntryAction.action?applyCode="
				+ applyCode;
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});
	}
</script>
<!-- *********保全项目变更保存按钮************************** -->


<script type="text/javascript">
	function _check_radioChecked(obj) {
		/* var acceptStatu = $(obj).parents("tr").find("input[type='radio']");
		acceptStatu.attr("checked","checked"); */
		//更改保全的状态。
		var changeId = $("#changeId",navTab.getCurrentPanel()).val();
		var parentTr = $(obj).parent().parent().parent();// opr by zkm
		var acceptId = parentTr.find('input[name="thisAcceptId"]').val();// opr by zkm
		var acceptStatus = parentTr.find('#acceptStatusValue').val();// opr by zkm
		// var acceptId = $("input[name='thisAcceptId']").val();
		// var acceptStatus = $("#acceptStatusValue").val();

		if (acceptStatus == "05" || acceptStatus == "01") {
			$
					.ajax({
						type : 'get',
						url : "${ctx}/cs/csEntry/updateStatus_PA_csEntryAction.action?changeId="
								+ changeId + "&acceptId=" + acceptId,
						cache : false,
						success : function(data) {
						}
					});
		}
		//现在使用-2015年10月21日-张颖杰修改 修改原因，进入保全项之前不要求选中radio
		$(obj).next("a").click();
	};
	//不在点击录入的地方更改受理状态
	function _check_radioChecked2(obj) {
		/* var acceptStatu = $(obj).parents("tr").find("input[type='radio']");
		acceptStatu.attr("checked","checked"); */
		/* //更改保全的状态。
		var changeId = $("#changeId").val();
		var parentTr = $(obj).parent().parent().parent();// opr by zkm
		var acceptId = parentTr.find('input[name="thisAcceptId"]').val();// opr by zkm
		var acceptStatus = parentTr.find('#acceptStatusValue').val();// opr by zkm
		// var acceptId = $("input[name='thisAcceptId']").val();
		// var acceptStatus = $("#acceptStatusValue").val();
		
		if(acceptStatus=="05" || acceptStatus=="01"){
			$.ajax({
				type : 'get',
				url : "${ctx}/cs/csEntry/updateStatus_PA_csEntryAction.action?changeId="+changeId+"&acceptId="+acceptId,
				cache : false,
				success : function(data) {
				}
			});
		} */
		//现在使用-2015年10月21日-张颖杰修改 修改原因，进入保全项之前不要求选中radio
		$(obj).next("a").click();
	};
</script>
<script type="text/javascript">

/**
 * 主录入完成操作：1规则校验，通过，进行录入完成处理；
 * 2.规则校验，阻断规则提示；
 * 3.规则校验，警告提示-停留此页面，选择是否继续，是-进行录入完成操作，否-停留此页面
 menuId
 */
 function _csEntry_mainEntryFinish(formId,navTabId){//subTaskCode ,changeId ,后台必传参数
		var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
		var changeId = $("#changeId",navTab.getCurrentPanel()).val()
		var outTimeFlag = $("#outTimeFlag",navTab.getCurrentPanel()).val();
		var projectPath = getRootPath();
		if(outTimeFlag == 0){
			var timeOutCause = $("#timeOutCause",navTab.getCurrentPanel()).val();
			if(timeOutCause == null || timeOutCause == ""){
				alertMsg.info("请先录入超时原因。");
				return false;
			}
		}
		$.ajax({
		 	   type : "post",
		       dataType : "text",
		       url : projectPath+"/cs/serviceitem_ns/checkShortChangeRisk_PA_csEndorseNSAction.action",
		       data : 'acceptId='+acceptId ,
		       success : function(data) {
		    	   
		    	   var json = DWZ.jsonEval(data);
		    	   debugger
					if (json.statusCode == "300") {
						alertMsg.info("转换后的险种不符合规定，请重新选择。");
						return false;
					}else{
						var $form =$("#"+ formId,navTab.getCurrentPanel());
						_csMainEntryFinishRule();
					}	
		    	   debugger;
		       }
		   });
	}

	//转正式受理
	function csEntry_acceptTheFormal(formId) {
		
		$.ajax({
			type : "post",
			url : "${ctx}/cs/csEntry/acceptTheFormal_PA_csEntryAction.action",
			data : $("#" + formId, navTab.getCurrentPanel()).serialize(),
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == DWZ.statusCode.error) {
					alertMsg.error(json.message);
				} else if (json.statusCode == DWZ.statusCode.timeout) {
					DWZ.loadLogin();
				}else{
					// 转正式受理成功 
					//把受理的按钮显示出来
					$("#tryCalcNo",navTab.getCurrentPanel()).val("");
					$("#tryCalcNo", navTab.getCurrentPanel()).hide();
					$("#tryCalcNoDL", navTab.getCurrentPanel()).hide();		
					$("#tricalButtom", navTab.getCurrentPanel()).hide();
					$("#acceptAndPayOther", navTab.getCurrentPanel()).show();
					$("#acceptAndPayButton", navTab.getCurrentPanel()).show();
					$("#EntryButtonId", navTab.getCurrentPanel()).show();
					$("#entryNcyId", navTab.getCurrentPanel()).hide();
					$("#entryId", navTab.getCurrentPanel()).show();
					$("#addCsAppDocDiv", navTab.getCurrentPanel()).show();
					$(".wsyAcceptStatus",navTab.getCurrentPanel()).html(
						"待录入<input type ='hidden' value='05' id = 'acceptStatusValue' name='acceptStatusValueArray'>");
				}
			},
		});

	}

	//试算完成
	function csEntry_tryCalNoFinish(formId) {
		var $form = $("#" + formId, navTab.getCurrentPanel());
		 var onsubmit = "return validateCallback(this,closeTab);"; 
			$form.attr('onsubmit', onsubmit);
			$form.submit();
	};

	//关闭tab页
	function closeTab(json){
		DWZ.ajaxDone(json);
		if(json.statusCode == DWZ.statusCode.ok){
			navTab.closeCurrentTab();
			_cs_back();
		}else{
			alertMsg.error(json.message);
		}
	}
	
	//修改受理信息
	function editAcceptInfo(divId) {
		//141437 start
		var changeId = $("#changeId",navTab.getCurrentPanel()).val()
		$.ajax({
			type : "post",
			url : "${ctx}/cs/csEntry/checkAcceptStatusForApplication_PA_csEntryAction.action",
			data : 'changeId='+changeId ,
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == "300") {
					alertMsg.info("申请下多个受理业务状态不一致，不支持修改申请信息！");
					return false;
				}else{
		//141437 end
		var $div = $("#" + divId, navTab.getCurrentPanel());
		if ($div.attr('modify_status') == '0') {
			$div.find("select, a ,input,button").not(
					$("input[id='radateDate']", navTab.getCurrentPanel()))
					.removeAttr("disabled");
			$("#customerPolicyInfoPanel", navTab.getCurrentPanel()).find(
					"select, a ,input,button").not(
					$("input[id='radateDate']", navTab.getCurrentPanel()))
					.attr("disabled", "disabled");
			$("#csServiceChangeSub", navTab.getCurrentPanel()).find(
					"select, a ,input,button").not(
					$("input[id='radateDate']", navTab.getCurrentPanel()))
					.attr("disabled", "disabled");
			$div.attr('modify_status', '1');
// 			$("#acceptFlagInfoTable", navTab.getCurrentPanel()).find("select, a ,input,button").attr("disabled", "disabled");
// 			$("#acceptFlagInfoTable", navTab.getCurrentPanel()).find("select, a ,input,button").attr("disabled", "disabled");
			$("#showButtonId",navTab.getCurrentPanel()).removeAttr("disabled");
		} else if ($div.attr('modify_status') == '1') {
			$div.find("select, a ,input,button").not(
					$("input[id='radateDate']", navTab.getCurrentPanel()))
					.attr("disabled", "disabled");
			$("#saveAcceptInfo",navTab.getCurrentPanel()).attr("disabled", "disabled");
			$("#modifyAcceptInfo",navTab.getCurrentPanel()).removeAttr("disabled");
			$div.attr('modify_status', '0');
			$("#showButtonId",navTab.getCurrentPanel()).removeAttr("disabled");
// 			$("#acceptFlagInfoTable", navTab.getCurrentPanel()).find("select, a ,input,button").removeAttr("disabled");
		}
		//141437 start
				}
		    }
		});
		//141437 end
	};
	//保存受理信息
	function _acceptM_saveAcceptInfo(divId, formId) {
		var $div = $("#" + divId, navTab.getCurrentPanel());
		if ($div.attr('modify_status') == '1') {
			//变更受理状态为带录入并更新整个页面
			$("#" + formId, navTab.getCurrentPanel()).submit();
		}
	};

	//返回主界面
	function _cs_back() {

		var title = "信息录入";
		var tabid = "_mit_M";
		var fresh = eval("true");
		var external = eval("false");
		var url = "${ctx}/cs/csAccept/loadCsAccept_PA_acceptAction.action?flag=1&menuId=${menuId}";
		navTab.openTab(tabid, url, {
			title : title,
			fresh : fresh,
			external : external
		});

	}
</script>


<!-- ***********保单补退费形式js************** -->

<script type="text/javascript">
	//补退费形式变化,将value值付给下面保单补退费形式table表中，表头'补退费形式'checkbox的值。
	$("#payModeSelect", navTab.getCurrentPanel()).change(function() {
		var _val = $(this).val();
		var _toObjId = $(this).attr("transmitTo");
		$("#" + _toObjId, navTab.getCurrentPanel()).val(_val);
	});
	//选择客户银行账号时，将id赋值给下面保单补退费形式table表中，表头'银行代码/账号'checkbox的值。
	$("#customerAccountInfo input:radio", navTab.getCurrentPanel()).change(
			function() {
				var _val = $(this).val();
				var _toObjId = $(this).attr("transmitTo");
				$("#" + _toObjId, navTab.getCurrentPanel()).val(_val);
			});
</script>
<script type="text/javascript">
	/**
	 * 删除
	 */
	function _cs_divDel(obj) {
		var _url = $(obj).attr("url");
		var $delDiv = $(obj).parents("._cs_divDel");
		$.ajax({
			type : 'get',
			url : _url,
			cache : false,
			success : function(data) {
				alertMsg.correct("删除成功！");
				$delDiv.html(data).initUI();
			},
			error : function() {
				alertMsg.error("删除失败！");
			}
		});
	}
	/**
	 * 编辑
	 */
	function _cs_trEdit(obj) {
		var $tr = $(obj).parents("tr");
		_cs_editTrStatus($tr);
		$tr.find("input,a,select,textarea").removeAttr("disabled");
	};
</script>

<script type="text/javascript">
	// 	保单补退费形式变更保存
	function _cs_table_save(divId) {
		var $table = $("#" + divId, navTab.getCurrentPanel()).find("table");
		if ($table.attr("table_saveStatus") == 0) {
			alertMsg.info("没有修改的内容");
			return false;
		}
		;
		var _jsons = _cs_tableToJson($table);
		var $form = $("#" + divId, navTab.getCurrentPanel()).parents("form");
		$form.find("input[name='jsons']").val(_jsons);
		$form.submit();
	}
</script>

<script type="text/javascript">
	$(document).ready(
			function() {
				//受理完成转录入页面时，申请信息/保全项变更等操作禁用 add by panmd_wb 
				$("#applyInfoDiv", navTab.getCurrentPanel()).find(
						"a,select,input,button").attr("disabled", "disabled");
				$("#showButtonId",navTab.getCurrentPanel()).removeAttr("disabled");
				// 	_cs_initMultipleBox();
				_cs_applyColVal();
				if ('${isSameOperator}' == 1) {
				} else {
				}
				//隐藏按钮
				var tryCalcNo = $("#tryCalcNo",navTab.getCurrentPanel()).val();
				if (tryCalcNo != null && tryCalcNo != "") {
					$("#acceptAndPayOther", navTab.getCurrentPanel()).hide();
					$("#acceptAndPayButton", navTab.getCurrentPanel()).hide();
					$("#EntryButtonId", navTab.getCurrentPanel()).hide();
					/* $("#bankAccountInfoDiv", navTab.getCurrentPanel()).hide(); */
					/* $("#saveMes", navTab.getCurrentPanel()).hide(); */
					$("#entryId", navTab.getCurrentPanel()).hide();
					$("#entryNcyId", navTab.getCurrentPanel()).show();
					/* $("#addCsAppDocDiv", navTab.getCurrentPanel()).hide(); */

					var $acceptStatus = $(".wsyAcceptStatus",navTab.getCurrentPanel());
					var flag = null;
					for (var a = 0; a < $acceptStatus.length; a++) {
						if ($($acceptStatus[a]).text() == "录入完成") {
							flag = true;
						}
					}
					if (flag) {
// 						$("#wsystep4").attr("class", "step");
					}
				} else {
					
					$("#tricalButtom", navTab.getCurrentPanel()).hide();
					$("#acceptAndPayOther", navTab.getCurrentPanel()).show();
					var tPageFlag = $("#pageFlag",navTab.getCurrentPanel()).val();
// 					alert(tPageFlag);
					if (tPageFlag != '2') {//tPageFlag=2 为保全查询页面通过试算号进入保全录入页面  不显示该部分
						$("#acceptAndPayButton", navTab.getCurrentPanel()).show();
					}
					$("#EntryButtonId", navTab.getCurrentPanel()).show();
					/* $("#bankAccountInfoDiv", navTab.getCurrentPanel()).show(); */
					/* $("#saveMes", navTab.getCurrentPanel()).show(); */
					$("#entryNcyId", navTab.getCurrentPanel()).hide();
					$("#entryId", navTab.getCurrentPanel()).show();
					/* $("#addCsAppDocDiv", navTab.getCurrentPanel()).show(); */

				}

			});
</script>
<!-- **********应备资料 *************************************************-->
<script type="text/javascript">
	$(function() {
		$("#applicationInfoDiv", navTab.getCurrentPanel()).find(
				"a,select,input").attr("disabled", "disabled");
		$("#step2FormBar", navTab.getCurrentPanel()).hide();
		$("#step2BodyDiv", navTab.getCurrentPanel()).find(
				"a,select,input,button").attr("disabled", "disabled");
	});
</script>
<script type="text/javascript">
	function cs_accept_delAppDoc(obj) {
		var $appDocId = $(obj).parents("td").find("input");
		if ($appDocId.size() == 0) {
			delTrElemt($(obj));
		} else {
			//删除应备资料
			$.ajax({
				type : 'POST',
				url : "cs/csAccept/delCsAppDoc_PA_acceptAction.action",
				data : 'csAppDocVO.appDocId=' + $appDocId.val(),
				cache : false,
				success : function() {
					delTrElemt($(obj));
				},
				error : DWZ.ajaxError
			});
		}
	}
</script>

<script type="text/javascript">
	function _scansion() {
		var applyCode = $("#applyCode", navTab.getCurrentPanel()).val();
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var acceptCode= $("#acceptCode", navTab.getCurrentPanel()).val();
		var flagParm = 0;
		var arrayResult = new Array();
		arrayResult = $("input[name='acceptStatusValueArray']", navTab
				.getCurrentPanel());
		if (arrayResult.length > 0) {
			for (var i = 0; i < arrayResult.length; i++) {
				var intResult = Number($(arrayResult[i]).val());
				if (intResult > 7) {
					flagParm = 2;
					break;
				}
			}
		}
		var rootPath = getRootPath();
		$
				.ajax({
					type : 'POST',
					url : rootPath
							+ '/cs/csAccept/dataUpdate_PA_acceptOtherAction.action?applyCode='
							+ applyCode + '&changeId='+changeId+'&acceptCode='+acceptCode+'&imageFlag=2&lpStatusFlag='
							+ flagParm,
					cache : false,
					success : function(data) {
						window
								.open(
										data,
										'',
										'width='
												+ (window.screen.availWidth - 10)
												+ ',height='
												+ (window.screen.availHeight - 30)
												+ ',top=0,left=0,resizable=yes,status=yes,menubar=no,scrollbars=yes');

					},
					error : DWZ.ajaxError
				});
	}

	//关联影像
	function relateImage() {
		var accepts = $("#accepts", navTab.getCurrentPanel()).val();
		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var applyCode = $("#applyCode", navTab.getCurrentPanel()).val();
		var tryCalcNo = $("#tryCalcNo", navTab.getCurrentPanel()).val();
		var url = "${ctx}/cs/csAccept/relateImage_PA_acceptOtherAction.action?customerId="
				+ customerId
				+ "&changeId="
				+ changeId
				+ "&applyCode="
				+ applyCode + "&accepts=" + accepts + "&tryCalcNo=" + tryCalcNo;
		$("#relateImage", navTab.getCurrentPanel()).attr("href", url);
	}

	

	function backoutModify() {
		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
		var action = "${ctx }/cs/csCancel/nextStep_PA_csEndorseManualCancelAction.action";
		var onsubmit = "return navTabSearch(this);"
		var $obj = $("#csAppDocUpForm", navTab.getCurrentPanel());
		var acceptId = $("#acceptId",navTab.getCurrentPanel()).val();
		$obj.find("input[name='manualCancelApplyVO.acceptIds']").val(acceptId);
		$obj.find("input[name='customerId']").val(customerId);
		$obj.attr('action', action);
		$obj.attr('onsubmit', onsubmit);
		$obj.submit();
	}

	function getHrefModify() {
		var applyCode = $(
				"input[name='csAcceptInfoEntryVO.csApplicationVO.applyCode']")
				.val();
		var $loanTable = $("#acceptFlagInfoTable",navTab.getCurrentPanel());
		var $trs = $loanTable.find("tbody tr[tr_saveStatus='1']").filter(
				function() {
					return $(this).css("display") != "none";
				});
		var acceptCodeS = "";
		var serviceCodeS = "";
		var policyCodeS = "";
		$trs.each(function(i) {
			serviceCodeS += $($trs[i]).find("input[name='serviceCode']").val();
			acceptCodeS += $($trs[i]).find("td:eq(3)").text();
			policyCodeS += $($trs[i]).find("td:eq(4)").text();
			if ($trs.size() - 1 > i) {
				serviceCodeS += ";";
				acceptCodeS += ";";
				policyCodeS += ";";
			}
		});
		var url = "${ctx}/cs/manualitem/survivalSurveyEnterProcess_PA_csSurvivalSurveyAction.action?applyCode="
				+ applyCode
				+ "&acceptCode="
				+ acceptCodeS
				+ "&serviceCode="
				+ serviceCodeS + "&policyCode=" + policyCodeS + "&inpitFalg=2";
		$("#csSurvivalSurveyModify", navTab.getCurrentPanel())
				.attr("href", url);
	}
	
	/** 查询问卷**/
	function queryHistoryScanning(){
		var customerId = $("input[name='csReviewVO.customerId']", navTab.getCurrentPanel()).val();
		if (typeof(customerId) != "undefined") {
			$.ajax({
				'type':'post',
				'url':'${ctx}/common/highCustomerSignHigh/queryHistoryScanning_PA_highCustomerSignHighAction.action?highCustomerSignVO.customerId='+customerId,
				'datatype':'json',
				'success':function(data){
					var data = eval("(" + data + ")");
					if(data.statusCode==300){
						alertMsg.error(data.message);
						return;
					}
					window.open(data.message);
				},
				'error':function(){
					alertMsg.error("系统出险异常，请联系管理员");
				}
			}); 
		}
	}
	
	
/* 	function checkMethod(){
// 		var checkProject=2;
		var applyCode= ${csAcceptInfoEntryVO.csApplicationVO.applyCode};
		var checkOperateOrg= ${csAcceptInfoEntryVO.csApplicationVO.organCode};
		var checkPolicyOrg = ${csAcceptInfoEntryVO.csApplicationVO.organCode};
		var url = "${ctx}/cs/common/loadCustomerListInfo_PA_customerIdentityCheckAction.action?applyCode="
				+applyCode
				+"&checkOperateOrg="
				+checkOperateOrg
				+"&checkPolicyOrg="
				+checkPolicyOrg
				+"&checkProject=2";
		$("#relateImage", navTab.getCurrentPanel()).attr("href", url);
		
	} */
	
</script>
<script type="text/javascript">
function _csMainEntryFinishRule(){
	debugger;
	var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId",navTab.getCurrentPanel()).val()
	var $form =$("#csEntryFinishForm",navTab.getCurrentPanel());
	var timeOutCause = $("#timeOutCause",navTab.getCurrentPanel()).val();
	$form.find("input[name='outTimeReason']").val(timeOutCause);
	var projectPath = getRootPath();
	
	$.ajax({
		type: 'POST',
		url: projectPath+'/cs/csEntry/csMainEntryFinishRule_PA_csEntryAction.action',
		data: $form.serializeArray(),
		cache: false,
		success: function(response){
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == DWZ.statusCode.error ){ //阻断提示
				if (json.message) alertMsg.error(json.message);
			}else if(json.statusCode== '200'){
				_CsCheckContinuousFlag();
				//_CsRealNameVerify();
					
			} 
			else if(json.statusCode == DWZ.statusCode.timeout){ //警告提示
				alertMsg.confirm(json.message , {
					okCall : function() {
						_CsCheckContinuousFlag();
						//_CsRealNameVerify();
					},
					cancleCall : function() {
						return false;
					}
				});
			} else  {
				alertMsg.correct("申请人9要素信息校验通过。");
				navTab.reload(projectPath+'/cs/csEntry/csEntryFinish_PA_csEntryAction.action', {
					data : $form.serializeArray()
				});
			}
		},
		error: DWZ.ajaxError
	});
	
}

/*检验反洗钱*/
function _CsCheckContinuousFlag(){
	var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId",navTab.getCurrentPanel()).val()
	var projectPath = getRootPath();
	
	$.ajax({
		type: 'POST',
		dataType : "text",
		url: projectPath+'/cs/csEntry/csCheckContinuousFlag_PA_csEntryAction.action',
		data: 'changeId='+changeId ,
		cache: false,
		success: function(response){
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == '301' ){ //校验未通过,非阻断提示
				var message = json.message;
				var messageString = message.split("$");
				
				alertMsg.confirm(messageString[0] , {
					okCall: function() {
						_CsSaveContinuousFlag(messageString[1]);
					},
					cancleCall : function() {
						return false;
					}
				});
			}else if(json.statusCode == '300'){// 出现异常阻断提示
				alertMsg.error("系统出现异常，请联系管理员！");
			}else if(json.statusCode == '200' ){// 校验通过
				_CsRealNameVerify();
			} 
		},
			
	});
}

/*保存反洗钱*/
function _CsSaveContinuousFlag(acceptString){
	var changeId = $("#changeId",navTab.getCurrentPanel()).val()
	var projectPath = getRootPath();
	$.ajax({
		type: 'POST',
		dataType : "text",
		url: projectPath+'/cs/csEntry/csSaveContinuousFlag_PA_csEntryAction.action',
		data: 'acceptString='+acceptString ,
		cache: false,
		success: function(response){
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == '300' ){ //保存轨迹失败,阻断提示
				alertMsg.error(data.message);
				return;
			}else if(json.statusCode == '200' ){// 保存成功
				_CsRealNameVerify();
			} 
		},
	});
}




function _CsRealNameVerify(){
	debugger;
	var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId",navTab.getCurrentPanel()).val()
	var $form =$("#csEntryFinishForm",navTab.getCurrentPanel());
	var projectPath = getRootPath();
	
	
	$.ajax({
		type: 'POST',
		dataType : "text",
		url: projectPath+'/cs/csEntry/CsRealNameVerify_PA_csEntryAction.action',
		data: 'changeId='+changeId ,
		cache: false,
		success: function(response){
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == '333' ){ //实名校验未通过,非阻断提示
				alertMsg.confirm(json.message , {
					okCall: function() {
						_verifyPrem();
					},
						cancleCall : function() {
							return false;
						}
					});
				}else if(json.statusCode == DWZ.statusCode.error ){// 实名校验阻断
					if (json.message) alertMsg.error(json.message);
				}else if(json.statusCode == '200' ){// 实名校验通过
					/* navTab.reload(projectPath+'/cs/csEntry/csEntryFinish_PA_csEntryAction.action', {
						data : $form.serializeArray()
					}); */
					
					_verifyPrem();
				} 
			},
	});
}


function _CheckVerifyCertyType(){
	debugger;
	var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId",navTab.getCurrentPanel()).val()
	var $form =$("#csEntryFinishForm",navTab.getCurrentPanel());
	var projectPath = getRootPath();
	
	$.ajax({
		type: 'POST',
		url: projectPath + '/cs/csEntry/CheckVerifyCertyType_PA_csEntryAction.action',
		data:'changeId=' + changeId,
		cache: false,
		success: function(response) {
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == '333') { //警告提示
				alertMsg.confirm(json.message, {
					okCall: function() {						
						_AutoLogOutPersCheck();
					},
				});
			}
			else if(json.statusCode == '200' ){// 实名校验通过
				/* navTab.reload(projectPath+'/cs/csEntry/csEntryFinish_PA_csEntryAction.action', {
					data : $form.serializeArray()
				}); */
				
				_AutoLogOutPersCheck();
			} 
		},
	});
}


function _AutoLogOutPersCheck(){
	debugger;
	var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId",navTab.getCurrentPanel()).val()
	var $form =$("#csEntryFinishForm",navTab.getCurrentPanel());
	var projectPath = getRootPath();
	
	$.ajax({
		type: 'POST',
		url: projectPath +
			'/cs/csEntry/AutoLogOutPersCheck_PA_csEntryAction.action',
		data: 'changeId=' + changeId,
		cache: false,
		success: function(response) {
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == DWZ.statusCode.error) { //阻断提示
				if (json.message) alertMsg.error(json.message);
			} else if (json.statusCode == DWZ.statusCode.timeout) { //警告提示
				alertMsg.confirm(json.message, {
					okCall: function() {
						_verifyPrem();
					},
					cancleCall: function() {
						return false;
					}
				});
			}else if(json.statusCode == '200' ){// 实名验证通过
				_verifyPrem();
			} 
		},
	});
}

function _verifyPrem(){
	debugger;
	var acceptId = $("#acceptIds",navTab.getCurrentPanel()).val();
	var changeId = $("#changeId",navTab.getCurrentPanel()).val();
	var $form =$("#csEntryFinishForm",navTab.getCurrentPanel());
	var projectPath = getRootPath();
	
	$.ajax({
		type: 'POST',
		url: projectPath +'/cs/csEntry/verifyPrem_PA_csEntryAction.action',
		data: 'changeId=' + changeId,
		cache: false,
		success: function(response) {
			debugger;
			var json = DWZ.jsonEval(response);
			if (json.statusCode == DWZ.statusCode.error) { //阻断提示
				if (json.message) alertMsg.error(json.message);
			}  else if (json.statusCode == '333') { //警告提示
				alertMsg.confirm(json.message, {
					okCall: function() {
						checkDoubleAudit();//双人审核校验判断
					},
					cancleCall: function() {
						return false;
					}
				});
			}else if(json.statusCode == '200' ){//验证通过
				checkDoubleAudit();//双人审核校验判断
			} 
		},
	});
}

	/**申请录入完成双人审核校验*/
	function checkDoubleAudit() {
		debugger;
		var acceptId = $("#acceptIds", navTab.getCurrentPanel()).val();
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var $form = $("#csEntryFinishForm", navTab.getCurrentPanel());
		var projectPath = getRootPath();
		$.ajax({
			type : 'POST',
			url : projectPath + '/cs/csEntry/checkDoubleAudit_PA_csEntryAction.action',
			data : 'changeId=' + changeId,
			cache : false,
			success : function(response) {
				debugger;
				var json = DWZ.jsonEval(response);
				if (json.statusCode == '200') {
					if (json.message == 1) {
						//弹出双人审核窗口
						$("#_doubleAduit").click();
						
					} else {
						navTab.reload(projectPath + '/cs/csEntry/csEntryFinish_PA_csEntryAction.action', {
							data : $form.serializeArray()
						});
					}
				}else{
					_submitEntryFinish();
				}
			},
		});
	}
	
	
	
	function _submitEntryFinish() {
		debugger;
		var $form = $("#csEntryFinishForm", navTab.getCurrentPanel());
		var projectPath = getRootPath();
		navTab.reload(projectPath + '/cs/csEntry/csEntryFinish_PA_csEntryAction.action', {
			data : $form.serializeArray()
		});
	}
</script>

