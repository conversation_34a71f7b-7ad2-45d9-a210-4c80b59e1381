<!--保全变更录入- 客户基本资料变更页面 -地址信息div-修改或添加div-->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<!-- 	地址信息主键 -->
	<input type="hidden" id="addPk" name="id" value="${id }" />
	<dl>
		<dt>地址代码</dt> 
		<dd><input id="addressId" type="text" name="custBaseInfoUpdateVO.addressVO.addressId" 
			value="${custBaseInfoUpdateVO.addressVO.addressId}"/>
		</dd>
	</dl>
	<dl>
		<dt>省/直辖市</dt> 
		<dd><s:select cssClass="combox" name="custBaseInfoUpdateVO.addressVO.state" id="state"
			list="#{'1':'常规','2':'豁免'}" listKey="key" listValue="value"
			headerKey="all" headerValue="请选择" ref="city" 
			refUrl="serviceitem/getCity_PA_custBaseInfoUpdateAction.action?state={value}"
			initVal="%{custBaseInfoUpdateVO.addressVO.city}">
		</s:select>
		</dd>
	</dl>
	<dl>
		<dt>市</dt> 
		<dd><select class="combox" id="city" name="custBaseInfoUpdateVO.addressVO.city" ref="district"
			refUrl="serviceitem/getCounty_PA_custBaseInfoUpdateAction.action?city={value}">
			<option value="">全部</option>
		</select>
		</dd>
	</dl>
	<dl>
		<dt>区县</dt> 
		<dd><select class="combox" name="custBaseInfoUpdateVO.addressVO.district" id="district">
			<option value="">全部</option>
		</select>
		</dd>
	</dl>
	<dl style="width:500px;">
		<dt>地址</dt> 
		<dd><input id="address" type="text" name="custBaseInfoUpdateVO.addressVO.address" 
			value="${custBaseInfoUpdateVO.addressVO.address}" size="50"/>
		</dd>
	</dl>
	<dl>
		<dt>邮政编码</dt> 
		<dd><input id="postCode" type="expandPostCode" name="custBaseInfoUpdateVO.addressVO.postCode" 
			value="${custBaseInfoUpdateVO.addressVO.postCode}"/>
		</dd>
	</dl>
<!-- 按钮 -->
		<div  class="formBar">
            <div class="button" style="float:right;margin-right:20%">
				<div class="buttonContent">
					<button type="button" onclick="addrInfo_saveAddress()" style="width: 50px">保存</button>
				</div>
			</div> 
		</div>
<!-- 按钮结束 -->