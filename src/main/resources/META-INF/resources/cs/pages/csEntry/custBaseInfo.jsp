<!--保全变更录入- 客户基本资料变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<form id="baseForm" action="cs/csEntry/loadCusInfoPolicyL_PA_csEntryAction.action" onsubmit="return navTabSearch(this)">
<!-- 	客户的基本信息、受理号等页面传递信息 -->
	<input type="hidden"  />
</form>
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
		<div class="pageContent">
			<div class="panel">
				<h1>客户基本资料变更</h1>
				<div class="pageFormContent" id="" style="background-color: white;" layoutH="110px">
				<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
					<s:include value="customerInfo_5Items.jsp"/>
					
					<form method="post" action="" class="required-validate" onsubmit="return navTabSearch(this);" id="custBaseInfoForm">
					<div class="panel">
						<h1>基本信息</h1>
						<div>
							<dl>
								<dt>国籍</dt> 
								<dd>
<%-- 										<Field:codeTable id="countryCode" name="custBaseInfoUpdateVO.customerVO.countryCode" --%>
<%--                              				tableName="APP___PAS__DBUSER.T_COUNTRY" nullOption="true" value="${custBaseInfoUpdateVO.customerVO.countryCode}"/> --%>
								</dd>
							</dl>
							<dl>
								<dt>婚姻状况</dt> 
								<dd>
<%-- 										<Field:codeTable id="marriageStatus" name="custBaseInfoUpdateVO.customerVO.marriageStatus" --%>
<%--                              				tableName="APP___PAS__DBUSER.T_MARRIAGE" nullOption="true" value="${custBaseInfoUpdateVO.customerVO.marriageStatus}"/> --%>
								</dd>
							</dl>
							<dl>
								<dt>子女状况</dt> 
								<dd>
								<s:select name="custBaseInfoUpdateVO.customerVO.isParent" id="isParent" cssClass="combox"
									list="#{'1':'邮件','2':'短信'}"/>
								</dd>
							</dl>
							<dl>
								<dt>证件有效期起期</dt> 
								<dd>
								<input id="custCertStarDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertStarDate" 
									value="${custBaseInfoUpdateVO.customerVO.custCertStarDate}"/>
								</dd>
							</dl>
							<dl>
								<dt>证件有效期止期</dt> 
								<dd>
								<input id="custCertEndDate" type="expandDateYMD" name="custBaseInfoUpdateVO.customerVO.custCertEndDate" 
									value="${custBaseInfoUpdateVO.customerVO.custCertEndDate}"/>
								</dd>
							</dl>
							<dl>
								<dt>驾照类型</dt> 
								<dd>
								<s:select name="custBaseInfoUpdateVO.customerVO.driverLicenseType" id="driverLicenseType" cssClass="combox"
									list="#{'1':'邮件','2':'短信'}"/>
								</dd>
							</dl>
							<dl>
								<dt>公司联系客户方式</dt> 
								<dd>
								<s:select name="custBaseInfoUpdateVO.companyNameContactMethod" id="companyNameContactMethod" cssClass="combox"
									list="#{'1':'邮件','2':'短信'}"/>
								</dd>
							</dl>
							<dl>
								<dt>移动电话</dt> 
								<dd>
								<input id="mobileTel" type="expandMobile" name="custBaseInfoUpdateVO.customerVO.mobileTel" 
									value="${custBaseInfoUpdateVO.customerVO.mobileTel}"/>
								</dd>
							</dl>
							<dl>
								<dt>固定电话</dt> 
								<dd>
								<input id="houseTel" type="expandPhone" name="custBaseInfoUpdateVO.customerVO.houseTel" 
									value="${custBaseInfoUpdateVO.customerVO.houseTel}"/>
								</dd>
							</dl>
							<dl>
								<dt>电子邮箱</dt> 
								<dd>
								<input id="email" type="text" name="custBaseInfoUpdateVO.customerVO.email" class="email"
									value="${custBaseInfoUpdateVO.customerVO.email}"/>
								</dd>
							</dl>
							<dl>
								<dt>工作单位</dt> 
								<dd>
								<input id="companyName" type="text" name="custBaseInfoUpdateVO.customerVO.companyName" 
									value="${custBaseInfoUpdateVO.customerVO.companyName}"/>
								</dd>
							</dl>
							<!-- 按钮 -->
							
							<!-- 按钮结束 -->
						</div>
						<div  class="panelBar">
		                    <div class="button" style="float: right;margin-right: 100px;">
								<div class="buttonContent">
									<button type="button" onclick="update()" style="width: 50px">修改</button>
								</div>
							</div> 
						</div>
					</div>
					</form>
					<!--地址信息 -->
					
					<div id="addressInfoDiv">
						<s:include value="custBase_addrInfo.jsp"></s:include>
					</div>
					
				</div>
			</div>
		</div>
<!-- </div> -->
<div class="formBar" style="height: 30px">
	<ul>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="#" id = "upA" onclick="up()">上一步</a>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<button type="button" onclick="toCustBasePolicyL();">下一步</button>
<!-- 					<a href="serviceitem/getMainPage_PA_custBaseInfoPolicyUpdateAction.action"  -->
<%-- 						target="navTab" rel="${menuId}" title="客户基本资料变更">下一步</a> --%>
				</div>
			</div>
		</li>
	</ul>
</div>

<script type="text/javascript">
	//级联下拉框的Id数组
	dwz_combox_myarray = new Array('state','city','district');
	//可选参数
	dwz_combox_mybox = navTab.getCurrentPanel();
	//最后下拉框的值，将其从request中取出
<%-- 	dwz_combox_last_value = "<%=request.getAttribute('district')%>"; --%>
</script>
<script type="text/javascript">
	//基本信息区域--修改按钮--保存变更的客户基本信息
	function update(){
		var $obj = $("#custBaseInfoForm", navTab.getCurrentPanel());
		action = "serviceitem/updateCustBaseInfo_PA_custBaseInfoUpdateAction.action";
		$obj.attr('action', action);
		$obj.submit();
	}

	//上一页
	function up() {
		//系统提示是否保存变更信息，确认--保存，取消--清除
		alertMsg.confirm("请确认是否需要保存录入的信息？",{
			okCall:function(){
				//保存变更信息---返回保全录入主页面
				$("#upA").attr("href",'serviceitem/saveAllCustBaseInfo_PA_custBaseInfoUpdateAction.action');
			},
			cancelCall:function(){
				//清除变更信息---返回客户基本信息录入页面
				$("#upA").attr("href","serviceitem/delAllCustBaseInfo_PA_custBaseInfoUpdateAction.action");
			}
		});
	};
	//进入保单层次的客户基本信息变更页面
	function toCustBasePolicyL(){
		var $form = $("#baseForm",navTab.getCurrentPanel());
		$form.submit();
	};
</script>
<script type="text/javascript">
	$(document).ready(function(){
		csHelpMenu();
	});
</script>