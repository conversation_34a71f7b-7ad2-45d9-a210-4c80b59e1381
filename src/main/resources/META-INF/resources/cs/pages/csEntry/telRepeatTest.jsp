<!--renxd_wb 保全录入-受理和补退费信息-电话重复校验-->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%> 
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<div class="pageContent" layoutH="20" >
<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">查询条件
		</h1>
</div>
	<form id="pagerForm" method="post" action="${ctx}/cs/csEntry/queryRepeatInfo_PA_csEntryAction.action">
	    <input type="hidden" name="pageNum" value="${currentPage.pageNo} " />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<div class="pageFormInfoContent">
		<form id="repeatPolicyForm"
			action="${ctx}/cs/csEntry/queryRepeatInfo_PA_csEntryAction.action"
			method="post" onsubmit="return navTabSearch(this, 'repeatResult')" rel="pagerForm">
			
			<dl>
				<dt>移动电话</dt>
				<dd><input type="hidden" name="customerPolicyAgentCompVO.changeId" id="repeatChangeId" value="${customerPolicyAgentCompVO.changeId}" />
					<input type="text" maxlength="11" name="customerPolicyAgentCompVO.mobileTel" id="mobileTel"
						   value="${customerPolicyAgentCompVO.mobileTel }" class="digits"/>
				</dd>
			</dl>
			<dl>
				<dt>固定电话</dt>
				<dd>
					<input type="text" name="customerPolicyAgentCompVO.offenUseTel" id="offenUseTel"
						   value="${customerPolicyAgentCompVO.offenUseTel }" class="digits"/>
				</dd>
			</dl>
			<dl>
				<dt></dt>
				<dd>
				</dd>
			</dl>
			<dl>
				<dt>回访电话1</dt>
				<dd>
					<input type="text" maxlength="11" name="customerPolicyAgentCompVO.callPhone1" id="callPhone1"
						   value="${customerPolicyAgentCompVO.callPhone1 }" class="digits"/>
				</dd>
			</dl>
			<dl>
				<dt>回访电话2</dt>
				<dd>
					<input type="text" name="customerPolicyAgentCompVO.callPhone2" id="callPhone2"
						   value="${customerPolicyAgentCompVO.callPhone2 }" class="digits"/>
				</dd>
			</dl>
			<div class="pageFormdiv">
				<button type="submit" id="queryRepeat" class="but_blue">查询</button>
				<button type="button" id="exportRPExcelId" class="but_blue" onclick="exportRPExcel();">清单下载</button>
			</div>
		</form>
	</div>
	<div id="repeatResult" class="unitBox">
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">客户电话重复列表
		</h1>
	</div>
	<div id="repeatPolicyTable" class="tabdivclassbr">
		<table id="repeatPolicyDiv" class="list main_dbottom" style="width: 100%;">
			<thead>
				<tr align="center">
					<th nowrap>序号</th>
					<th nowrap>电话号码</th>
					<th style="display:none;" nowrap>客户号</th>
					<th nowrap>客户姓名</th>
					<th nowrap>保单号</th>
					<th nowrap>保单管理机构</th>
					<th nowrap>保全受理号</th>
				</tr>
			</thead>
			<tbody id="">
				<s:if test="currentPage.PageItems == null || currentPage.PageItems.size()==0">
					<tr>
						<td colspan="20">
							<div class="noRueryResult">无符合条件数据！</div>
						</td>
					</tr>
				</s:if>
				<s:iterator value="currentPage.pageItems" status="st">
					<tr align="center">
					   <td>${st.index }</td>
						<td>${mobileTel }</td>
						<td style="display:none;">${customerId }</td>
						<td>${customerName }</td>
						<td>${policyCode }</td>
						<td>${organCode }</td>
						<td>${acceptCode }</td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
	
	<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">客户与业务员电话重复列表
		</h1>
	</div>
	<div id="agentRepeat" class="tabdivclassbr">
		<table id="agentRepeatTable" class="list main_dbottom" width="100%">
			<thead>
				<tr>
					<th nowrap>序号</th>
					<th nowrap>电话号码</th>
					<th nowrap>业务员姓名</th>
					<th nowrap>业务员编码</th>
					<th nowrap>业务员所属分公司</th>
					<!-- <th nowrap>营业区</th>
					<th nowrap>营业部</th>
					<th nowrap>营业组</th> -->
				</tr>
			</thead>
			<tbody id="agentRepeatTbody">
				<s:if test="currentPageAgent.PageItems == null || currentPageAgent.PageItems.size()==0">
					<tr>
						<td colspan="20">
							<div class="noRueryResult">无符合条件数据！</div>
						</td>
					</tr>
				</s:if>
				<s:iterator value="currentPageAgent.pageItems" status="st">
					   <tr align="center">
						<td>${st.index }</td>
						<td>${agentMobile }</td>
						<td>${agentName }</td>
						<td>${agentCode }</td>
						<td>${agentOrganCode }</td>
						<td></td>
						<td></td>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select list="#{5:'5',10:'10',20:'20',50:'50',100:'100'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
</div>
<script type="text/javascript">
	//导出 两个Excel
	function exportRPExcel(){
		var $mobileTel = $("#mobileTel", navTab.getCurrentPanel()).val();
		var $offenUseTel = $("#offenUseTel", navTab.getCurrentPanel()).val();
		var $callPhone1 = $("#callPhone1", navTab.getCurrentPanel()).val();
		var $callPhone2 = $("#callPhone2", navTab.getCurrentPanel()).val();
		debugger;
		if( $mobileTel == '' && $offenUseTel == '' && $callPhone1 == '' && $callPhone2 == '' ){
				alertMsg.info("请至少填写一个查询条件");
				return false;
		}
		alertMsg.confirm("确实要导出这些记录吗?", {
	       	okCall: function() {
	       		
				var $form = $("#repeatPolicyForm", navTab.getCurrentPanel());
				var action="${ctx}/cs/csEntry/exportToExcel_PA_csEntryAction.action";
				$form.attr('action', action);
				$form.attr('onsubmit', null);
				$form.submit();
				$form.attr('action', "${ctx}/cs/csEntry/queryRepeatInfo_PA_csEntryAction.action");
				$form.attr('onsubmit', "return navTabSearch(this, 'repeatResult')");
	        },
	        cancelCall : function() {
				//取消
	        	return false;
			}
		});
		
	}


</script>
</div>