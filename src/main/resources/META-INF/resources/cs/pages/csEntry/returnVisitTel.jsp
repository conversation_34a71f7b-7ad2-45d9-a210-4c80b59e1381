<!--renxd_wb 保全录入-回访电话录入 -->
<%@ page language="java" contentType="text/html; charset=utf-8"	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>

<script type="text/javascript">
	//校验回访电话
	function judgeReturnVisitTel() {
		var callPhone1 = $("#callPhone1", navTab.getCurrentPanel()).val();
		var callPhone2 = $("#callPhone2", navTab.getCurrentPanel()).val();
		var applyCustomerType = $("#applyCustomerType", navTab.getCurrentPanel()).val();
		var applyCustomerCountryType = $("#applyCustomerCountryType", navTab.getCurrentPanel()).val();
		var acceptId = $("#acceptId", navTab.getCurrentPanel()).val();
		var changeId = $("#changeId", navTab.getCurrentPanel()).val();
		var customerId = $("#customerId", navTab.getCurrentPanel()).val();
		var flag = "false";
		if(applyCustomerType != "b" && applyCustomerType != "h" && applyCustomerType != "d" && applyCustomerType != "i"){
			if (callPhone1.length > 0) {
				callPhone1 = callPhone1.replaceAll(" ", "");
				var isnum = /^\d+$/.test(callPhone1);
				if(!isnum){
					alertMsg.error("回访电话1号码首位不能是0，11位全为数字，且数字不能全相同。");
					return false;
				}
				if (callPhone1.match(/^([0-9a-zA-Z])\1*$/) != null){
					alertMsg.error("回访电话1号码首位不能是0，11位全为数字，且数字不能全相同。");
					return false;
			 	}
				
				if (callPhone1.length != 11) {
					alertMsg.error("回访电话1号码首位不能是0，11位全为数字，且数字不能全相同。");
					return false;
				}
				var oneStr = callPhone1.substring(0,1);
				if(oneStr == "0"){
					alertMsg.error("回访电话1号码首位不能是0，11位全为数字，且数字不能全相同。");
					return false;
				}
				
				var str = callPhone1.substring(0,2);
				if(applyCustomerType != "b" && applyCustomerType != "h" && applyCustomerType != "d" && applyCustomerType != "i"){
					if (str != '13' && str != '14' && str != '15' && str != '16' 
						&& str != '17' && str != '18' && str != '19') {
						alertMsg.error("回访电话1只能以“13”、“14”、“15”、“16”、“17”、“18”、“19”开头，请重新录入。");
						return false;
					}
				}
				if(applyCustomerType != "b" && applyCustomerType != "h" && applyCustomerType != "d" && applyCustomerType != "i"){
					if (str == '10' ||  str == '11' ||  str == '12') {
						alertMsg.error("回访电话1号码不能以10、11、12开头。");
						return false;
					}
				}
				
			}
			
			 if (callPhone2.length > 0) {
				
				
				var str = callPhone2.split("-");
				if(str.length < 2 || str.length > 3){
					alertMsg.error("您填写的电话号码不符合要求：区号位数在3-4位（仅包括数字）,区号后面的电话号码不少于7位且最多不超过8位，分机号最长6位,请重新填写。");
					return false;
				}
				if (str[0].match(/^([0-9a-zA-Z])\1*$/) != null){
					alertMsg.error("回访电话2的区号位数为3-4位，仅包括数字，不能以400.800开头。");
					return false;
			 	}
				for (var m = 0 ; m < str.length; m++) {
					
					if (m == 0) {
						var isnum = /^\d+$/.test(str[0]);
						if(!isnum){
							alertMsg.error("回访电话2的区号位数为3-4位，仅包括数字，不能以400.800开头。");
							return false;
						}
						if(str[0].substring(0,3) == '400' || str[0].substring(0,3) == '800'){
							alertMsg.error("回访电话2的区号位数为3-4位，仅包括数字，不能以400.800开头。");
							return false;
						}
						if (str[0].length <3 || str[0].length >4) {
							alertMsg.error("回访电话2的区号位数为3-4位，仅包括数字，不能以400.800开头。");
							return false;
						}
					}
					if (m == 1) {
						var isnum = /^\d+$/.test(str[1]);
						if(!isnum){
							alertMsg.error("回访电话2的"+str[0]+"区号后面的电话位数应为7-8位，仅包括数字且不能全部相同，请重新录入。");
							return false;
						}
						if (str[1].length <7 || str[1].length >8) {
							alertMsg.error("回访电话2的"+str[0]+"区号后面的电话位数应为7-8位，仅包括数字且不能全部相同，请重新录入。");
							return false;
						}
						for (k = 0 ; k < str[1].length; k++) {
							if (str[1].substring(0) != str[1].substring(k+1)) {
								break;
							}
							if (k == str[1].length-1) {
								alertMsg.error("回访电话2区号后面的电话位数应为7-8位，且不能全部相同。");
								return false;
							}
						}
					}
					
					if (m == 2) {
						var isnum = /^\d+$/.test(str[2]);
						if(!isnum){
							alertMsg.error("回访电话2的"+str[2]+"分机号位数应为1-6位，仅包括数字，请重新录入。");
							return false;
						}
						if (str[2] != null && str[2].length >6) {
							alertMsg.error("回访电话2的"+str[2]+"分机号位数应为1-6位，仅包括数字，请重新录入。");
							return false;
						}
					}
				}
			} 
		}else{
			$.ajax({
				type : 'POST',
				url : "cs/csEntry/checkCcCountryCode_PA_csEntryAction.action",						
				data :{
					customerId:customerId,
					acceptId:acceptId,
					changeId:changeId
				},
				cache : true,	
				async:false, 
				success : function(response) {
					debugger;
					var json = DWZ.jsonEval(response);
					if (json.statusCode == 333) {
						if(!checkMobileOrPhoneFormat(callPhone1,"0",applyCustomerType,json.message)){
							flag = "true";
							return false
						}
						if(!checkMobileOrPhoneFormat(callPhone2,"1",applyCustomerType,json.message)){
							flag = "true";
							return false
						}
					}else{
						if(!checkMobileOrPhoneFormat(callPhone1,"0",applyCustomerType,applyCustomerCountryType)){
							flag = "true";
							return false
						}
						if(!checkMobileOrPhoneFormat(callPhone2,"1",applyCustomerType,applyCustomerCountryType)){
							flag = "true";
							return false
						}
					}
				},
		    });
			
		}
		if(flag == "true"){
			return false
		}
		debugger;
		 var serviceType = $("a[name='csAcceptInfoEntryVO.csApplicationVO.serviceType']", navTab.getCurrentPanel()).val();
		 if ("2" == serviceType || "3" == serviceType) {
			 if (callPhone1.length == 0 && callPhone2.length == 0) {
				 alertMsg.error("当申请方式为“业务员代办”、“其他人代办”时，回访电话1、回访电话2不能同时为空。");
					return false;
			 }
		 }
		 
		$("#returnVisitTelForm", navTab.getCurrentPanel()).submit();
	}
	
	//回调函数
	function callbackReturn(json) {
		if (json.statusCode == DWZ.statusCode.ok) {
			alertMsg.correct(json.message);
		}
		if (json.statusCode == DWZ.statusCode.error) {
			alertMsg.error(json.message);
		}
		if (json.statusCode == 333) {
			alertMsg.confirm(json.message , {
				okCall : function() {		
					var url = $("#returnVisitTelForm", navTab.getCurrentPanel()).attr("action");
					var urlstr = url + "&csAcceptChangeVO.sameFlag=1";
					$("#returnVisitTelForm", navTab.getCurrentPanel()).attr("action", urlstr);
					$("#returnVisitTelForm", navTab.getCurrentPanel()).submit();
					$("#returnVisitTelForm", navTab.getCurrentPanel()).attr("action", url);
				},
				cancleCall : function() {
					return false;
				}
			});	
		}
	}
	function show_mol(){
		var options=$("#callPhoneMobil option:selected");  
		var v = options.text();
		if("请选择"==v){
			 $("#callPhone1", navTab.getCurrentPanel()).val();
		}else{
			 $("#callPhone1", navTab.getCurrentPanel()).val(v);
		}
       
    }
	
	 function show_fix(){   
		 var option=$("#callPhoneFix1  option:selected");  
		 var s = option.text();
		 if("请选择"==s){
			 $("#callPhone2", navTab.getCurrentPanel()).val();
		}else{
			 $("#callPhone2", navTab.getCurrentPanel()).val(s);
		}
	 } 
	  
</script>

<div class="divfclass">
	<h1>
		<img src="${ctx}/cs/img/icon/tubiao.png" />回访电话录入
	</h1>
</div>

<div>
	<form id="returnVisitTelForm"	action="cs/csEntry/saveReturnVisitTel_PA_csEntryAction.action?csAcceptChangeVO.changeId=${changeId}&csAcceptChangeVO.acceptId=${acceptId}&csAcceptChangeVO.customerId=${customerId}&csAcceptChangeVO.policyCode=${csAcceptInfoEntryVO.policyInfoVOList[0].policyCode}"	method="post" class="required-validate"	
		onsubmit="return validateCallback(this, callbackReturn);">
		<input type="hidden" id="applyCustomerType" name="applyCustomerType" value="${customerVO.customerCertType }" />
		<input type="hidden" id="applyCustomerCountryType" name="applyCustomerCountryType" value="${customerVO.countryCode }" />
		<input type="hidden" id="changeId" name="changeId" value="${changeId }" />
		<input type="hidden" id="acceptId" name="acceptId" value="${acceptId }" />
		<input type="hidden" id="customerId" name="customerId" value="${customerId}" />
		<s:if test='csAcceptChangeVO.selectFlag ==1'>
		 	<div class="pageFormInfoContent">
			<dl>
				<dt>回访电话1</dt>
				<dd>
					 <input  type="hidden" id="callPhone1" name="csAcceptChangeVO.callPhone1" value="${csAcceptChangeVO.callPhone1 }" class="digits"/> 
					 <s:select list="csAcceptChangeVO.callPhoneMobile"  id="callPhoneMobil"  headerKey="0" headerValue="请选择" 
					 onchange="show_mol()"    cssClass="combox" >
					</s:select>
				</dd>
			</dl>
			<dl>
				<dt>回访电话2</dt>
				<dd>
					 <input  id="callPhone2" type="hidden" name="csAcceptChangeVO.callPhone2" value="${csAcceptChangeVO.callPhone2 }"/> 
					 
					<s:select list="csAcceptChangeVO.callPhoneFix" id="callPhoneFix1"  headerKey="0" headerValue="请选择" 
					  onchange="show_fix()"  cssClass="combox" >
					</s:select>
				</dd>
			</dl>
		</div>
		<div class="pageFormdiv">
			<button class="but_blue" type="button" onclick="judgeReturnVisitTel();">保存</button>
		</div>
		 </s:if>

		  <s:if test='csAcceptChangeVO.selectFlag !=1'>
		 	<div class="pageFormInfoContent">
			<dl>
				<dt>回访电话1</dt>
				<dd>
				<dt style="color: blue" title="移动电话">
								<input type="text" id="callPhone1" name="csAcceptChangeVO.callPhone1" value="${csAcceptChangeVO.callPhone1 }" class="digits"/>
				</dt>
					
				</dd>
			</dl>
			<dl>
				<dt>回访电话2</dt>
				<dd>
					<dt style="color: blue" title="固定电话">
									<input type="text" id="callPhone2" name="csAcceptChangeVO.callPhone2" value="${csAcceptChangeVO.callPhone2 }"/>
					</dt>
					
				</dd>
			</dl>
		</div>
		<div class="pageFormdiv">
			<button class="but_blue" type="button" onclick="judgeReturnVisitTel();">保存</button>
		</div>
	    </s:if>
	</form>
</div>