<!--guyy_wb 保全录入-受理和补退费信息页面-受理修改信息页面-补退费信息 -->
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<div class="panel collapse">
	<!-- <div> -->
	<h1>返回受理操作</h1>
	<div class="panelBar">
		<!-- <div> -->
		<form id="backToAcceptForm"
			action="${ctx}/cs/csAccept/backToAccept_PA_acceptAction.action?menuId=${menuId}"
			method="post" onsubmit="return navTabSearch(this);">
			<input type="hidden" id="changeId" name="changeId"
				value="${changeId}" /> <input type="hidden" id="customerId"
				name="customerId" value="${customerId}" /> <input type="hidden"
				id="acceptId" name="acceptId" value="${acceptId}" /> <input
				type="hidden" id="jsonStr" name="jsonStr" value="" />
			<table style="width: 100%">
				<tr>
					<td width="300px">
						<div style="float: left; line-height: 22px;">
							<span>返回受理原因：</span>
						</div>
						<div style="float: left;">
							<Field:codeTable
								tableName="APP___PAS__DBUSER.T_CS_BACK_REG_CAUSE" name="content"
								nullOption="true" id="backReason" />
						</div>

						<div id="buto">
							<button type="button" class="but_blue"
								onclick="backToAccept('backToAcceptForm');">返回受理</button>
						</div>
					</td>
				</tr>
			</table>
		</form>
	</div>
</div>


<script type="text/javascript">

/**
* 保全项录入页面--点击"返回受理"按钮返回保全受理页面
* <AUTHOR>
*/
function backToAccept(formId) {

	var changeId = $("#changeId", navTab.getCurrentPanel()).val();

	var content = $("#backReason",navTab.getCurrentPanel()).find("option:selected").val();
	if (content == "") {
		alertMsg.info("请选择返回受理原因！");
		return false;
	}
	$("#OldChangeId").val(changeId);
	//alert(changeId);
	//系统提示是否保存变更信息，确认--保存，取消--清除
	//141437 start
	$.ajax({
			type : "post",
			url : "${ctx}/cs/csEntry/checkAcceptStatusForApplication_PA_csEntryAction.action",
			data : 'changeId='+changeId ,
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == "300") {
					alertMsg.info("申请下多个受理业务状态不一致，不支持返回受理！");
					return false;
				}else{
	//141437 end
	alertMsg
			.confirm(
					"请确认是否返回受理？",
					{
						okCall : function() {
							navTab.closeCurrentTab();
							var title = "保全受理";
							var tabid = "_accpetPool_M";
							var fresh = eval("true");
							var external = eval("false");

							var url = "${ctx}/cs/csAccept/backToAccept_PA_acceptAction.action?menuId=${menuId}&csAcceptInfoEntryVO.changeId="
									+ changeId + "&content=" + content;
							navTab.openTab(tabid, url, {
								title : title,
								fresh : fresh,
								external : external
							});
						},
						cancelCall : function() {
							//不保存--返回保全录入页面
						}
					});
		//141437 start
				}
		    }
		});
		//141437 end
}
</script>