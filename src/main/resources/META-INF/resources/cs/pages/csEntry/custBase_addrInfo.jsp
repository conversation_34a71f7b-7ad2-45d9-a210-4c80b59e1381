<!--保全变更录入- 客户基本资料变更页面 -地址信息div-->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<form method="post" action="" class="required-validate" onsubmit="return divSearch(this,'addressInfoDiv');" id="addressForm">
<div class="panel" minH="100">
	<h1>地址信息</h1>
	<div>
		<table class="list" id="" width="100%">
			<thead>
				<tr id="" align="center">
					<th>选择</th>
					<th>地址代码</th>
					<th>省/直辖市</th>
					<th>市</th>
					<th>区县</th>
					<th>地址</th>
					<th>邮政编码</th>
				</tr>
			</thead>
			<tbody>
<%-- 									<s:iterator value="plist" id="plist"> --%>
					<tr align="center">
						<td><input type="radio"/></td>
						<td>${addressVO.addressId}</td>
						<td>${addressVO.state}</td>
						<td>${addressVO.city}</td>
						<td>${addressVO.district}</td>
						<td>${addressVO.address}</td>
						<td>${addressVO.postCode}</td>
					</tr>
<%-- 									</s:iterator> --%>
			</tbody>
		</table>
		
		<!-- 按钮 -->
		<div  class="panelBar">
                  <div class="button">
				<div class="buttonContent" >
					<button type="button" onclick="addrInfo_addAddress()" style="width: 50px">新增</button>
				</div>
			</div>
                  <div class="button" style="margin-left: 10px;">
				<div class="buttonContent">
					<button type="button" onclick="addrInfo_modifyAddress(this)" style="width: 50px">修改</button>
				</div>
			</div>
                  <div class="button" style="margin-left: 10px;">
				<div class="buttonContent">
					<button type="button" onclick="addrInfo_delAddress()" style="width: 50px">删除</button>
				</div>
			</div> 
		</div>
		<!-- 按钮结束 -->
		
		<div id="addrModifyDiv" style="display: none">
			<s:include value="custBase_modifyAddr.jsp"></s:include>
			
		</div>
		
	</div>
</div>
</form>
<script type="text/javascript">
	//地址信息区域--修改按钮---修改通信地址信息
	function addrInfo_modifyAddress(){
		var $obj = $("#addressForm", navTab.getCurrentPanel());
// 		action = "serviceitem/updateAddress_PA_custBaseInfoUpdateAction.action";
// 		$obj.attr('action', action);
// 		$obj.submit();
		var radio = $obj.find("input:radio:checked");
		if(radio.length==0){
			alertMsg.info("请选择要修改的地址");
			return false;
		}
		var onsubmit="return divSearch(this,'addrModifyDiv');";
		var action = "cs/csEntry/modifyCustomerAddr_PA_csEntryAction.action";
		$obj.attr('action', action);
		$obj.attr('onsubmit',onsubmit);
		$obj.submit();
		$("#addrModifyDiv",navTab.getCurrentPanel()).show();
	}
	//地址信息区域--新增按钮--新增一条通信地址信息
	function addrInfo_addAddress(){
		var $obj = $("#addressForm", navTab.getCurrentPanel());
		var onsubmit="return divSearch(this,'addrModifyDiv');";
		var action = "cs/csEntry/addCustomerAddr_PA_csEntryAction.action";
		$obj.attr('action', action);
		$obj.attr('onsubmit',onsubmit);
		$obj.submit();
		$("#addrModifyDiv",navTab.getCurrentPanel()).show();
		$("#addrModifyDiv",navTab.getCurrentPanel()).show();
	}
	//地址信息区域--删除按钮--删除一条通信地址信息
	function addrInfo_delAddress(){
		var $obj = $("#addressForm", navTab.getCurrentPanel());
		var radio = $($obj).find("input:radio:checked",navTab.getCurrentPanel());
		if(radio.length==0){
			alertMsg.info("请选择要修改的地址");
			return false;
		}
		var onsubmit="return divSearch(this,'addressInfoDiv');";
		var action = "cs/csEntry/delCustomerAddr_PA_csEntryAction.action";
		$obj.attr('action', action);
		$obj.attr('onsubmit',onsubmit);
		$obj.submit();
	}
	//地址信息区域--保存按钮--保存录入的地址信息
	function addrInfo_saveAddress(){
		var $obj = $("#addressForm", navTab.getCurrentPanel());
		var action = "cs/csEntry/saveCustomerAddr_PA_csEntryAction.action";
		var onsubmit="return divSearch(this,'addressInfoDiv');";
		$obj.attr('action', action);
		$obj.attr('onsubmit',onsubmit);
		$obj.submit();
	}
</script>