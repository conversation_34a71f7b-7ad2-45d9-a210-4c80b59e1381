<!--保全录入- 基本信息备注项变更页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type = "text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
	<%-- 进度条 --%>
<div class="step_header">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td rowspan="2" class="n1d" id="n1"></td>
			<td class="step" id="step1">保全任务查询</td>
			<td rowspan="2" class="n2" id="n2"></td>
			<td class="step_other" id="step2">保全录入</td>
			<td rowspan="2" class="n3" id="n3"></td>
			<td class="step_other" id="step3">变更信息录入</td>
		</tr>
	</table>
</div>
<form id="hiddenForm">
	<!--保全申请号 -->
	<input name="" type="hidden" value="" />
	<!--保全受理号 -->
	<input name="" type="hidden" value=""/>
	<!--变更信息的json字符串 -->
 	<input name="jsons" type="hidden" value=""/> 
</form>
<div class="pageContent" style="background: white;" layoutH="100px">
	<div class="panelBar">	
	</div>
	<!--客户的5项基本信息（姓名、出生日期、证件类型、证件号码、性别） -->
	<s:include value="customerInfo_5Items.jsp"/>
	<div class="panel">
		<h1>投保人相关保单</h1>
		<div>
			<table id="basicRemarkTable" class="list" style="width: 100%">
				<thead>
					<tr>
						<th>保单号</th>
						<th>投保人</th>
						<th>被保险人</th>
						<th>保单状态</th>
						<th>原基本信息备注信息</th>
						<th colName="c1">变更基本信息备注</th>
					</tr>
				</thead>
				<tbody id="">
<%-- 								<s:iterator value="list" id="" status="st"> --%>
						<tr align="center">
							<td>1001${policyCode}</td>
							<td>${policyHolder }</td>
							<td>${insured}</td>
							<td>${policyState }</td>
							<td>${basicRemark}</td>
							<td ><s:textarea id="rt" value='%{basicRemarkChange}'></s:textarea></td>
						</tr>
						<tr align="center">
							<td>1002${policyCode}</td>
							<td>${policyHolder }</td>
							<td>${insured}</td>
							<td>${policyState }</td>
							<td>${basicRemark}</td>
							<td><s:textarea value='%{basicRemarkChange}'></s:textarea></td>
						</tr>
<%-- 								</s:iterator> --%>
				</tbody>
			</table>
		</div>
	</div>
</div>
<div class="formBar" style="height: 30px">
	<ul>
		<li>
			<div class="button">
				<div class="buttonContent">
					<button type="button" onclick="save();">上一步</button>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<button type="button" onclick="nextStep_basicRemark('basicRemarkTable');" >下一步</button>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">撤销</button>
					</a>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起生调</button>
					</a>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起签报</button>
					</a>
				</div>
			</div>
		</li>
<!-- 		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起回访</button>
					</a>
				</div>
			</div>
		</li>
		<li>
			<div class="button">
				<div class="buttonContent">
					<a href="javascript:;" onclick="">
						<button type="button">发起转办</button>
					</a>
				</div>
			</div>
		</li> -->
	</ul>
</div>
<script type="text/javascript">
	//帮助部分动作
	function MM_changeProp(objId, theProp, theValue) {
		var obj = null;
		with (document) {
			if (getElementById) {
				obj = getElementById(objId);
			}
		}
		if (obj) {
			if (theValue == true || theValue == false) {
				eval("obj.style." + theProp + "=" + theValue);
			} else {
				eval("obj.style." + theProp + "='" + theValue + "'");
			}
		}
	};

	//上一步
	function save() {
		alertMsg.confirm("请确认是否需要保存录入信息", {
			okCall : function() {
				$.post(url, data, DWZ.ajaxDone, "json");
			}
		});
	}
	//下一步 保存基本信息备注项变更信息
	function nextStep_basicRemark(tableId){
	//1.获取变更数据
		var _jsons = basicRmark_jsons(tableId);//获取变更jsons数据
		var $jsonsText = $("input[name='jsons']",navTab.getCurrentPanel());
		$($jsonsText).val(_jsons);
		
	//2.保存变更数据
		var serializeArray = $("#hiddenForm",navTab.getCurrentPanel()).serializeArray();
		$.ajax(
				{
				url:"${ctx }/cs/csEntry/saveCsBasicRemark_PA_csEntryAction.action?",
				type:"post",
				dataType:'html',
				data:serializeArray,
				cache:false,
				success:function(data){
						alertMsg.correct("保存成功！");
						var title = "基本信息备注项";
						var tabid = "${menuId}";
						var fresh = true;
						var external = false;
						var url = unescape("${ctx}/cs/csEntry/saveCsBasicRemark_PA_csEntryAction.action?&menuId=${menuId}").replaceTmById($(navTab).parents(".unitBox:first"));
						navTab.openTab(tabid, url, {
							title : title,
							fresh : fresh,
							external : external
						});
				},
				error:function(){alertMsg.error("保存失败！");}
				}
			);
	};
	
	function basicRmark_jsons(tableId){
		var _jsons = "";
		var $table = $("#"+tableId,navTab.getCurrentPanel());
		var keys = new Array();
		$($table).find("thead tr th").each(function(i){
			if(typeof($(this).attr("colName"))!="undefined"){
				keys[i] = $(this).attr("colName").trim();
			}else{
				keys[i] = "";
			}
		});
		var $trs = $($table).find("tbody tr");
		_jsons = _jsons+"[";
		$($trs).each(function(i){
			_jsons = _jsons+"{";
			var $tds = $(this).find("td");
			$(this).find("td").each(function(j){
				if(keys[j]!=""){
					var _tdVal = "";
					var $txtare = $(this).find("textarea");
					if($txtare.length>0){
						_tdVal = $($txtare).text();
					}else{
						_tdVal = $(this).html();
					}
					_jsons = _jsons+"'"+keys[j]+"':'"+_tdVal+"',";
					
				}
			});
			_jsons = _jsons.substring(0,_jsons.length-1);
			_jsons = _jsons+"},";
		});
		_jsons = _jsons.substring(0,_jsons.length-1);
		_jsons = _jsons+"]";
		return _jsons;
		
	}
</script>
<script type="text/javascript">
	$(document).ready(function(){
		csHelpMenu();//帮助菜单初始化
	});
</script>