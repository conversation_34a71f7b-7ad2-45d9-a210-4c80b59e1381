
package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RegSrvResBizBody complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="RegSrvResBizBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="OutputData" type="{http://www.newchinalife.com/service/bd}OutputData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvResBizBody", propOrder = {
    "outputData"
})
public class RegSrvResBizBody {

    @XmlElement(name = "OutputData", required = true)
    protected OutputData outputData;

    /**
     * ��ȡoutputData���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link OutputData }
     *     
     */
    public OutputData getOutputData() {
        return outputData;
    }

    /**
     * ����outputData���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link OutputData }
     *     
     */
    public void setOutputData(OutputData value) {
        this.outputData = value;
    }

}
