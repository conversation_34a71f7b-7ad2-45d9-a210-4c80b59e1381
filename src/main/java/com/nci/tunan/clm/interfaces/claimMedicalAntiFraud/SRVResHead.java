
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>SRVResHead complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SRVResHead">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Operator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RowNumStart" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PageRowNum" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PageFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TotalRowNum" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OrderFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OrderField" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ManageCom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RiskCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ContNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SysRouteFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SRVResHead", namespace = "http://www.newchinalife.com/service/hd", propOrder = {
   
})
public class SRVResHead {
    
}
