package com.nci.tunan.clm.interfaces.queryCaseFrmsInfo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class claim {

	@XmlElement(name="frms_clmno", required = true)
    protected String frms_clmno;
	
	@XmlElement(name="frms_tabfeemoney", required = true)
    protected double frms_tabfeemoney;
	
	@XmlElement(name="frms_claim_standpay", required = true)
    protected double frms_claim_standpay;
	
	@XmlElement(name="frms_phone", required = true)
    protected String frms_phone;
	
	@XmlElement(name="frms_rgtantphone", required = true)
    protected String frms_rgtantphone;
	
	@XmlElement(name="frms_claim_user", required = true)
    protected String frms_claim_user;
	
	@XmlElement(name="frms_accidentAge", required = true)
    protected int frms_accidentAge;
	
	@XmlElement(name="frms_in_hospital_time", required = true)
    protected int frms_in_hospital_time;
	
	@XmlElement(name="frms_accident", required = true)
    protected String frms_accident;
	
	@XmlElement(name="frms_icd1", required = true)
    protected String frms_icd1;
	
	@XmlElement(name="frms_icd2", required = true)
    protected String frms_icd2;
	
	@XmlElement(name="frms_claim_accdate", required = true)
    protected String frms_claim_accdate;
	
	@XmlElement(name="frms_accidentdate", required = true)
    protected String frms_accidentdate;
	
	@XmlElement(name="frms_rgtdate", required = true)
    protected String frms_rgtdate;
	
	@XmlElement(name="frms_idno6", required = true)
    protected String frms_idno6;
	
	@XmlElement(name="frms_claim_time", required = true)
    protected String frms_claim_time;
	
	@XmlElement(name="frms_realpay", required = true)
    protected double frms_realpay;
	
	@XmlElement(name="frms_claim_givetype", required = true)
    protected String frms_claim_givetype;
	
	@XmlElement(name="frms_claim_conttype", required = true)
    protected String frms_claim_conttype;
	
	@XmlElement(name="frms_personbirthday", required = true)
    protected String frms_personbirthday;
	
	@XmlElement(name="frms_com_code", required = true)
    protected String frms_com_code;
	
	@XmlElement(name="hops", required = true)
    protected List<hop> hops;
    
    @XmlElement(name="conts", required = true)
    protected List<cont> conts;

	public String getFrms_clmno() {
		return frms_clmno;
	}

	public void setFrms_clmno(String frms_clmno) {
		this.frms_clmno = frms_clmno;
	}

	public double getFrms_tabfeemoney() {
		return frms_tabfeemoney;
	}

	public void setFrms_tabfeemoney(double frms_tabfeemoney) {
		this.frms_tabfeemoney = frms_tabfeemoney;
	}

	public double getFrms_claim_standpay() {
		return frms_claim_standpay;
	}

	public void setFrms_claim_standpay(double frms_claim_standpay) {
		this.frms_claim_standpay = frms_claim_standpay;
	}

	public String getFrms_phone() {
		return frms_phone;
	}

	public void setFrms_phone(String frms_phone) {
		this.frms_phone = frms_phone;
	}

	public String getFrms_rgtantphone() {
		return frms_rgtantphone;
	}

	public void setFrms_rgtantphone(String frms_rgtantphone) {
		this.frms_rgtantphone = frms_rgtantphone;
	}

	public String getFrms_claim_user() {
		return frms_claim_user;
	}

	public void setFrms_claim_user(String frms_claim_user) {
		this.frms_claim_user = frms_claim_user;
	}

	public int getFrms_accidentAge() {
		return frms_accidentAge;
	}

	public void setFrms_accidentAge(int frms_accidentAge) {
		this.frms_accidentAge = frms_accidentAge;
	}

	public int getFrms_in_hospital_time() {
		return frms_in_hospital_time;
	}

	public void setFrms_in_hospital_time(int frms_in_hospital_time) {
		this.frms_in_hospital_time = frms_in_hospital_time;
	}

	public String getFrms_accident() {
		return frms_accident;
	}

	public void setFrms_accident(String frms_accident) {
		this.frms_accident = frms_accident;
	}

	public String getFrms_icd1() {
		return frms_icd1;
	}

	public void setFrms_icd1(String frms_icd1) {
		this.frms_icd1 = frms_icd1;
	}

	public String getFrms_icd2() {
		return frms_icd2;
	}

	public void setFrms_icd2(String frms_icd2) {
		this.frms_icd2 = frms_icd2;
	}

	public String getFrms_claim_accdate() {
		return frms_claim_accdate;
	}

	public void setFrms_claim_accdate(String frms_claim_accdate) {
		this.frms_claim_accdate = frms_claim_accdate;
	}

	public String getFrms_accidentdate() {
		return frms_accidentdate;
	}

	public void setFrms_accidentdate(String frms_accidentdate) {
		this.frms_accidentdate = frms_accidentdate;
	}

	public String getFrms_rgtdate() {
		return frms_rgtdate;
	}

	public void setFrms_rgtdate(String frms_rgtdate) {
		this.frms_rgtdate = frms_rgtdate;
	}

	public String getFrms_idno6() {
		return frms_idno6;
	}

	public void setFrms_idno6(String frms_idno6) {
		this.frms_idno6 = frms_idno6;
	}

	public String getFrms_claim_time() {
		return frms_claim_time;
	}

	public void setFrms_claim_time(String frms_claim_time) {
		this.frms_claim_time = frms_claim_time;
	}

	public double getFrms_realpay() {
		return frms_realpay;
	}

	public void setFrms_realpay(double frms_realpay) {
		this.frms_realpay = frms_realpay;
	}

	public String getFrms_claim_givetype() {
		return frms_claim_givetype;
	}

	public void setFrms_claim_givetype(String frms_claim_givetype) {
		this.frms_claim_givetype = frms_claim_givetype;
	}

	public String getFrms_claim_conttype() {
		return frms_claim_conttype;
	}

	public void setFrms_claim_conttype(String frms_claim_conttype) {
		this.frms_claim_conttype = frms_claim_conttype;
	}

	public String getFrms_personbirthday() {
		return frms_personbirthday;
	}

	public void setFrms_personbirthday(String frms_personbirthday) {
		this.frms_personbirthday = frms_personbirthday;
	}

	public String getFrms_com_code() {
		return frms_com_code;
	}

	public void setFrms_com_code(String frms_com_code) {
		this.frms_com_code = frms_com_code;
	}

	public List<hop> getHops() {
		return hops;
	}

	public void setHops(List<hop> hops) {
		this.hops = hops;
	}

	public List<cont> getConts() {
		return conts;
	}

	public void setConts(List<cont> conts) {
		this.conts = conts;
	}
    
}
