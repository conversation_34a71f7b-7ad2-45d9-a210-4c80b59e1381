
package com.nci.tunan.clm.interfaces.queryCaseFrms;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.3
 * 2020-03-30T17:36:22.287+08:00
 * Generated source version: 3.1.3
 * 
 */
public final class ClaimRuleQuerySrvPortType_ClaimRuleQuerySrvBindingQSPort_Client {

    private static final QName SERVICE_NAME = new QName("http://www.newchinalife.com", "claimRuleQuerySrvBindingQSService");

    private ClaimRuleQuerySrvPortType_ClaimRuleQuerySrvBindingQSPort_Client() {
    }

    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = ClaimRuleQuerySrvBindingQSService.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        ClaimRuleQuerySrvBindingQSService ss = new ClaimRuleQuerySrvBindingQSService(wsdlURL, SERVICE_NAME);
        ClaimRuleQuerySrvPortType port = ss.getClaimRuleQuerySrvBindingQSPort();  
        
        {
        com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader _claimRuleQuery_parametersReqHeader = null;
        com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvReqBody _claimRuleQuery_parametersReqBody = null;
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader> _claimRuleQuery_parametersResHeader = new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.common.header.in.SysMsgHeader>();
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvResBody> _claimRuleQuery_parametersResBody = new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd.RegSrvResBody>();
        port.claimRuleQuery(_claimRuleQuery_parametersReqHeader, _claimRuleQuery_parametersReqBody, _claimRuleQuery_parametersResHeader, _claimRuleQuery_parametersResBody);

        }

        System.exit(0);
    }

}
