
package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>claimType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="claimType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="frms_clmno" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="rules" type="{http://www.newchinalife.com/service/bd}rules"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "claimType", propOrder = {
    "frmsClmno",
    "rules"
})
public class ClaimType {

    @XmlElement(name = "frms_clmno", required = true)
    protected String frmsClmno;
    @XmlElement(required = true)
    protected Rules rules;

    /**
     * ��ȡfrmsClmno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsClmno() {
        return frmsClmno;
    }

    /**
     * ����frmsClmno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsClmno(String value) {
        this.frmsClmno = value;
    }

    /**
     * ��ȡrules���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link Rules }
     *     
     */
    public Rules getRules() {
        return rules;
    }

    /**
     * ����rules���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link Rules }
     *     
     */
    public void setRules(Rules value) {
        this.rules = value;
    }

}
