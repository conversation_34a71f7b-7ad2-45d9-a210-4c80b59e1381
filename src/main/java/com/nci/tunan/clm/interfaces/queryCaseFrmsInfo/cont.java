package com.nci.tunan.clm.interfaces.queryCaseFrmsInfo;

import javax.xml.bind.annotation.XmlElement;

public class cont {

	@XmlElement(name="frms_contno", required = true)
    protected String frms_contno;
	
	@XmlElement(name="frms_polno", required = true)
    protected String frms_polno;
	
	@XmlElement(name="frms_contno_conttype", required = true)
    protected String frms_contno_conttype;
	
	@XmlElement(name="frms_riskname", required = true)
    protected String frms_riskname;
	
	@XmlElement(name="frms_riskperiod", required = true)
    protected String frms_riskperiod;
	
	@XmlElement(name="frms_insurance_kind", required = true)
    protected String frms_insurance_kind;
	
	@XmlElement(name="frms_getdutykind", required = true)
    protected String frms_getdutykind;
	
	@XmlElement(name="frms_kindname", required = true)
    protected String frms_kindname;
	
	@XmlElement(name="frms_givereason", required = true)
    protected String frms_givereason;
	
	@XmlElement(name="frms_agentcode", required = true)
    protected String frms_agentcode;
	
	@XmlElement(name="frms_agentname", required = true)
    protected String frms_agentname;
	
	@XmlElement(name="frms_rearagentcode", required = true)
    protected String frms_rearagentcode;
	
	@XmlElement(name="frms_appntmobile", required = true)
    protected String frms_appntmobile;
	
	@XmlElement(name="frms_contno_active_time", required = true)
    protected int frms_contno_active_time;
	
	@XmlElement(name="frms_contno_cvalidate", required = true)
    protected String frms_contno_cvalidate;
	
	@XmlElement(name="frms_insurer_code", required = true)
    protected String frms_insurer_code;
	
	@XmlElement(name="frms_insurer", required = true)
    protected String frms_insurer;
	
	@XmlElement(name="frms_insured_code", required = true)
    protected String frms_insured_code;
	
	@XmlElement(name="frms_insured", required = true)
    protected String frms_insured;
	
	@XmlElement(name="frms_manage8_code", required = true)
    protected String frms_manage8_code;

	public String getFrms_contno() {
		return frms_contno;
	}

	public void setFrms_contno(String frms_contno) {
		this.frms_contno = frms_contno;
	}

	public String getFrms_polno() {
		return frms_polno;
	}

	public void setFrms_polno(String frms_polno) {
		this.frms_polno = frms_polno;
	}

	public String getFrms_contno_conttype() {
		return frms_contno_conttype;
	}

	public void setFrms_contno_conttype(String frms_contno_conttype) {
		this.frms_contno_conttype = frms_contno_conttype;
	}

	public String getFrms_riskname() {
		return frms_riskname;
	}

	public void setFrms_riskname(String frms_riskname) {
		this.frms_riskname = frms_riskname;
	}

	public String getFrms_riskperiod() {
		return frms_riskperiod;
	}

	public void setFrms_riskperiod(String frms_riskperiod) {
		this.frms_riskperiod = frms_riskperiod;
	}

	public String getFrms_insurance_kind() {
		return frms_insurance_kind;
	}

	public void setFrms_insurance_kind(String frms_insurance_kind) {
		this.frms_insurance_kind = frms_insurance_kind;
	}

	public String getFrms_getdutykind() {
		return frms_getdutykind;
	}

	public void setFrms_getdutykind(String frms_getdutykind) {
		this.frms_getdutykind = frms_getdutykind;
	}

	public String getFrms_kindname() {
		return frms_kindname;
	}

	public void setFrms_kindname(String frms_kindname) {
		this.frms_kindname = frms_kindname;
	}

	public String getFrms_givereason() {
		return frms_givereason;
	}

	public void setFrms_givereason(String frms_givereason) {
		this.frms_givereason = frms_givereason;
	}

	public String getFrms_agentcode() {
		return frms_agentcode;
	}

	public void setFrms_agentcode(String frms_agentcode) {
		this.frms_agentcode = frms_agentcode;
	}

	public String getFrms_agentname() {
		return frms_agentname;
	}

	public void setFrms_agentname(String frms_agentname) {
		this.frms_agentname = frms_agentname;
	}

	public String getFrms_rearagentcode() {
		return frms_rearagentcode;
	}

	public void setFrms_rearagentcode(String frms_rearagentcode) {
		this.frms_rearagentcode = frms_rearagentcode;
	}

	public String getFrms_appntmobile() {
		return frms_appntmobile;
	}

	public void setFrms_appntmobile(String frms_appntmobile) {
		this.frms_appntmobile = frms_appntmobile;
	}

	public int getFrms_contno_active_time() {
		return frms_contno_active_time;
	}

	public void setFrms_contno_active_time(int frms_contno_active_time) {
		this.frms_contno_active_time = frms_contno_active_time;
	}

	public String getFrms_contno_cvalidate() {
		return frms_contno_cvalidate;
	}

	public void setFrms_contno_cvalidate(String frms_contno_cvalidate) {
		this.frms_contno_cvalidate = frms_contno_cvalidate;
	}

	public String getFrms_insurer_code() {
		return frms_insurer_code;
	}

	public void setFrms_insurer_code(String frms_insurer_code) {
		this.frms_insurer_code = frms_insurer_code;
	}

	public String getFrms_insurer() {
		return frms_insurer;
	}

	public void setFrms_insurer(String frms_insurer) {
		this.frms_insurer = frms_insurer;
	}

	public String getFrms_insured_code() {
		return frms_insured_code;
	}

	public void setFrms_insured_code(String frms_insured_code) {
		this.frms_insured_code = frms_insured_code;
	}

	public String getFrms_insured() {
		return frms_insured;
	}

	public void setFrms_insured(String frms_insured) {
		this.frms_insured = frms_insured;
	}

	public String getFrms_manage8_code() {
		return frms_manage8_code;
	}

	public void setFrms_manage8_code(String frms_manage8_code) {
		this.frms_manage8_code = frms_manage8_code;
	}
	
}
