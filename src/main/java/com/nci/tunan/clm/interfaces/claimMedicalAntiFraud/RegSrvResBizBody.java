
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RegSrvResBizBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="RegSrvResBizBody">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="OutputData" type="{http://www.newchinalife.com/service/bd}OutputData"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvResBizBody", propOrder = {
    "outputData"
})
public class RegSrvResBizBody {

    @XmlElement(name = "OutputData", required = true)
    protected OutputData outputData;

    /**
     * 获取outputData属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link OutputData
     *      }
     *     
     */
    public OutputData getOutputData() {
        return outputData;
    }

    /**
     * 设置outputData属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link OutputData
     *      }
     *     
     */
    public void setOutputData(OutputData value) {
        this.outputData = value;
    }

}
