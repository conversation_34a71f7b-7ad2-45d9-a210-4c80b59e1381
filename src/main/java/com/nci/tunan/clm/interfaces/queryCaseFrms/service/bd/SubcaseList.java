package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "subcaseList", propOrder = {
    "subcase"
})
public class SubcaseList {
	
    @XmlElement(required = true)
    protected List<Subcase> subcase;
    
    
    public List<Subcase> getSubcase() {
        if (subcase == null) {
        	subcase = new ArrayList<Subcase>();
        }
        return this.subcase;
    }

}
