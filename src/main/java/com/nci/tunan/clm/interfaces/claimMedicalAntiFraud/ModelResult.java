package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "modelResult", propOrder = {
    "clmno",
    "caseno",
    "polno",
    "dutycode",
    "getdutykind",
    "getdutycode",
    "caserelano",
    "modelName",
    "probability",
    "modelExplanation"
})
public class ModelResult {
    //赔案号
    @XmlElement(name="clmno",required=true)
    private String clmno;
    //分案号
    @XmlElement(name="caseno",required=true)
    private String caseno;
    //险种号
    @XmlElement(name="polno",required=true)
    private String polno;
    //责任编码
    @XmlElement(name="dutycode",required=true)
    private String dutycode;
   //给付责任类型
    @XmlElement(name="getdutykind",required=true)
    private String getdutykind;
    //给付责任编码
    @XmlElement(name="getdutycode",required=true)
    private String getdutycode;
    //受理事故号
    @XmlElement(name="caserelano",required=true)
    private String caserelano;
    //模型名称
    @XmlElement(name="modelName",required=true)
    private String modelName;
    //黑样本概率
    @XmlElement(name="probability",required=true)
    private String probability;
    //模型解释代码
    @XmlElement(name="modelExplanation",required=true)
    private String modelExplanation;
 
    public String getClmno() {
        return clmno;
    }

    public String getCaseno() {
        return caseno;
    }

    public String getPolno() {
        return polno;
    }

    public String getDutycode() {
        return dutycode;
    }

    public String getGetdutykind() {
        return getdutykind;
    }

    public String getGetdutycode() {
        return getdutycode;
    }

    public String getCaserelano() {
        return caserelano;
    }

    public String getModelName() {
        return modelName;
    }

    public String getProbability() {
        return probability;
    }

    public String getModelExplanation() {
        return modelExplanation;
    }

    public void setClmno(String clmno) {
        this.clmno = clmno;
    }

    public void setCaseno(String caseno) {
        this.caseno = caseno;
    }

    public void setPolno(String polno) {
        this.polno = polno;
    }

    public void setDutycode(String dutycode) {
        this.dutycode = dutycode;
    }

    public void setGetdutykind(String getdutykind) {
        this.getdutykind = getdutykind;
    }

    public void setGetdutycode(String getdutycode) {
        this.getdutycode = getdutycode;
    }

    public void setCaserelano(String caserelano) {
        this.caserelano = caserelano;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public void setProbability(String probability) {
        this.probability = probability;
    }

    public void setModelExplanation(String modelExplanation) {
        this.modelExplanation = modelExplanation;
    }
    
}
