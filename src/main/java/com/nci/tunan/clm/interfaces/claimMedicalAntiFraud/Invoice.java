package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "invoice", propOrder = {
        "mainfeeno",
        "customerno",
        "clmstate",
        "hospitalcode",
        "hospitalname",
        "customername",
        "feetype",
        "hospstartdate",
        "hospenddate",
        "operator",
        "idno",
        "idtype"
    })
public class Invoice {

    @XmlElement(required = true)
    protected String mainfeeno;
    
    @XmlElement(required = true)
    protected String customerno;
    
    @XmlElement(required = true)
    protected String clmstate;
    
    @XmlElement(required = true)
    protected String hospitalcode;
    
    @XmlElement(required = true)
    protected String hospitalname;
    
    @XmlElement(required = true)
    protected String customername;
    
    @XmlElement(required = true)
    protected String feetype;
    
    @XmlElement(required = true)
    protected String hospstartdate;
    
    @XmlElement(required = true)
    protected String hospenddate;
    
    @XmlElement(required = true)
    protected String operator;
    
    @XmlElement(required = true)
    protected String idno;
    
    @XmlElement(required = true)
    protected String idtype;

    public String getMainfeeno() {
        return mainfeeno;
    }

    public String getCustomerno() {
        return customerno;
    }

    public String getClmstate() {
        return clmstate;
    }

    public String getHospitalcode() {
        return hospitalcode;
    }

    public String getCustomername() {
        return customername;
    }

    public String getFeetype() {
        return feetype;
    }

    public String getHospstartdate() {
        return hospstartdate;
    }

    public String getHospenddate() {
        return hospenddate;
    }

    public String getOperator() {
        return operator;
    }

    public String getIdno() {
        return idno;
    }

    public String getIdtype() {
        return idtype;
    }

    public void setMainfeeno(String mainfeeno) {
        this.mainfeeno = mainfeeno;
    }

    public void setCustomerno(String customerno) {
        this.customerno = customerno;
    }

    public void setClmstate(String clmstate) {
        this.clmstate = clmstate;
    }

    public void setHospitalcode(String hospitalcode) {
        this.hospitalcode = hospitalcode;
    }

    public void setCustomername(String customername) {
        this.customername = customername;
    }

    public void setFeetype(String feetype) {
        this.feetype = feetype;
    }

    public void setHospstartdate(String hospstartdate) {
        this.hospstartdate = hospstartdate;
    }

    public void setHospenddate(String hospenddate) {
        this.hospenddate = hospenddate;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public void setIdno(String idno) {
        this.idno = idno;
    }

    public void setIdtype(String idtype) {
        this.idtype = idtype;
    }

    public String getHospitalname() {
        return hospitalname;
    }

    public void setHospitalname(String hospitalname) {
        this.hospitalname = hospitalname;
    }
    
}
