
package com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.service.bd;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.newchinalife.service.bd package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Request_QNAME = new QName("http://www.newchinalife.com/service/bd", "request");
    private final static QName _Response_QNAME = new QName("http://www.newchinalife.com/service/bd", "response");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.newchinalife.service.bd
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link RegSrvReqBody }
     * 
     */
    public RegSrvReqBody createRegSrvReqBody() {
        return new RegSrvReqBody();
    }

    /**
     * Create an instance of {@link RegSrvResBody }
     * 
     */
    public RegSrvResBody createRegSrvResBody() {
        return new RegSrvResBody();
    }

    /**
     * Create an instance of {@link RegSrvReqBizBody }
     * 
     */
    public RegSrvReqBizBody createRegSrvReqBizBody() {
        return new RegSrvReqBizBody();
    }

    /**
     * Create an instance of {@link RegSrvResBizBody }
     * 
     */
    public RegSrvResBizBody createRegSrvResBizBody() {
        return new RegSrvResBizBody();
    }

    /**
     * Create an instance of {@link InputData }
     * 
     */
    public InputData createInputData() {
        return new InputData();
    }

    /**
     * Create an instance of {@link OutputData }
     * 
     */
    public OutputData createOutputData() {
        return new OutputData();
    }

    /**
     * Create an instance of {@link Claim }
     * 
     */
    public Claim createClaim() {
        return new Claim();
    }

    

    /**
     * Create an instance of {@link ClaimType }
     * 
     */
    public ClaimType createClaimType() {
        return new ClaimType();
    }

    

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegSrvReqBody }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/bd", name = "request")
    public JAXBElement<RegSrvReqBody> createRequest(RegSrvReqBody value) {
        return new JAXBElement<RegSrvReqBody>(_Request_QNAME, RegSrvReqBody.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegSrvResBody }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/bd", name = "response")
    public JAXBElement<RegSrvResBody> createResponse(RegSrvResBody value) {
        return new JAXBElement<RegSrvResBody>(_Response_QNAME, RegSrvResBody.class, null, value);
    }

}
