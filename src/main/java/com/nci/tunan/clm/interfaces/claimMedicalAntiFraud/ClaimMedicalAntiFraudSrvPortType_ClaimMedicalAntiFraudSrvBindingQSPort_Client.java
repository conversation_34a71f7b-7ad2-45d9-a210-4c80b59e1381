
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName; 

/**
 * This class was generated by Apache CXF 3.0.0
 * 2016-12-27T14:51:11.313+08:00
 * Generated source version: 3.0.0
 * 
 */
public final class ClaimMedicalAntiFraudSrvPortType_ClaimMedicalAntiFraudSrvBindingQSPort_Client {

    private static final   QName SERVICE_NAME = new QName("http://www.newchinalife.com", "ClaimListQuerySrvBindingQSService");

    private ClaimMedicalAntiFraudSrvPortType_ClaimMedicalAntiFraudSrvBindingQSPort_Client() {
    }

    public static void main(String[] args) throws java.lang.Exception {
        URL wsdlURL = ClaimMedicalAntiFraudSrvBindingQSService.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        ClaimMedicalAntiFraudSrvBindingQSService ss = new ClaimMedicalAntiFraudSrvBindingQSService(wsdlURL, SERVICE_NAME);
        ClaimMedicalAntiFraudSrvPortType port = ss.getClaimListQuerySrvBindingQSPort();  
        
        {
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader _claimListQuery_parametersReqHeader = null;
        com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvReqBody _claimListQuery_parametersReqBody = null;
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader> _claimListQuery_parametersResHeader =
                new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.SysMsgHeader>();
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvResBody> _claimListQuery_parametersResBody =
                new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.claimMedicalAntiFraud.RegSrvResBody>();
        port.claimListQuery(_claimListQuery_parametersReqHeader, _claimListQuery_parametersReqBody, _claimListQuery_parametersResHeader, _claimListQuery_parametersResBody);
        } 
    }

}
