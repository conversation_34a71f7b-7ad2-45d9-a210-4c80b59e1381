package com.nci.tunan.clm.interfaces.queryCaseFrmsInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "rule", propOrder = {
    "frms_contno",
    "frms_rule_code", 
    "frms_rule_score",
    "frms_customer_count",
    "frms_claim_pay" 
})
public class rule {

	@XmlElement(name="frms_contno", required = true)
    protected String frms_contno;
	
	@XmlElement(name="frms_rule_code", required = true)
    protected String frms_rule_code;
	
	@XmlElement(name="frms_rule_score", required = true)
    protected String frms_rule_score;
	
	@XmlElement(name="frms_customer_count", required = true)
    protected String frms_customer_count;
	
	@XmlElement(name="frms_claim_pay", required = true)
    protected String frms_claim_pay;

	public String getFrms_contno() {
		return frms_contno;
	}

	public void setFrms_contno(String frms_contno) {
		this.frms_contno = frms_contno;
	}

	public String getFrms_rule_code() {
		return frms_rule_code;
	}

	public void setFrms_rule_code(String frms_rule_code) {
		this.frms_rule_code = frms_rule_code;
	}

	public String getFrms_rule_score() {
		return frms_rule_score;
	}

	public void setFrms_rule_score(String frms_rule_score) {
		this.frms_rule_score = frms_rule_score;
	}

	public String getFrms_customer_count() {
		return frms_customer_count;
	}

	public void setFrms_customer_count(String frms_customer_count) {
		this.frms_customer_count = frms_customer_count;
	}

	public String getFrms_claim_pay() {
		return frms_claim_pay;
	}

	public void setFrms_claim_pay(String frms_claim_pay) {
		this.frms_claim_pay = frms_claim_pay;
	}
	
}
