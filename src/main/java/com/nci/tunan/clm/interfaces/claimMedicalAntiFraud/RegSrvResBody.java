
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RegSrvResBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="RegSrvResBody">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="bizHeader" type="{http://www.newchinalife.com/service/hd}SRVResHead"/>
 *         &lt;element name="bizBody" type="{http://www.newchinalife.com/service/bd}RegSrvResBizBody"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvResBody", propOrder = {
    "bizHeader",
    "bizBody"
})
public class RegSrvResBody {

    @XmlElement(required = true)
    protected SRVResHead bizHeader;
    @XmlElement(required = true)
    protected RegSrvResBizBody bizBody;

    /**
     * 获取bizHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link SRVResHead 
     *     }
     *     
     */
    public SRVResHead getBizHeader() {
        return bizHeader;
    }

    /**
     * 设置bizHeader属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link SRVResHead 
     *     }
     *     
     */
    public void setBizHeader(SRVResHead value) {
        this.bizHeader = value;
    }

    /**
     * 获取bizBody属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link RegSrvResBizBody 
     *     }
     *     
     */
    public RegSrvResBizBody getBizBody() {
        return bizBody;
    }

    /**
     * 设置bizBody属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link RegSrvResBizBody 
     *     }
     *     
     */
    public void setBizBody(RegSrvResBizBody value) {
        this.bizBody = value;
    }

}
