package com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

import com.nci.udmp.framework.util.Constants;

/**
 * This class was generated by Apache CXF 3.1.3
 * 2020-03-30T17:36:22.386+08:00
 * Generated source version: 3.1.3
 * 
 */
@WebServiceClient(name = "claimRuleQuerySrvBindingQSService", 
                  wsdlLocation = "http://10.1.160.12:8111/services/P00001900099?wsdl",
                  targetNamespace = "http://www.newchinalife.com") 
public class ClaimRuleQuerySrvBindingQSService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://www.newchinalife.com", "claimRuleQuerySrvBindingQSService");
    public final static QName ClaimRuleQuerySrvBindingQSPort = new QName("http://www.newchinalife.com", "claimRuleQuerySrvBindingQSPort");
    static {
        URL url = null;
        try {
        	url = new URL(Constants.SERVICEENVPARAMAP.get("ESB") + "P00001900099?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ClaimRuleQuerySrvBindingQSService.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", Constants.SERVICEENVPARAMAP.get("ESB") + "P00001900099?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public ClaimRuleQuerySrvBindingQSService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ClaimRuleQuerySrvBindingQSService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ClaimRuleQuerySrvBindingQSService() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public ClaimRuleQuerySrvBindingQSService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public ClaimRuleQuerySrvBindingQSService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public ClaimRuleQuerySrvBindingQSService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns ClaimRuleQuerySrvPortType
     */
    @WebEndpoint(name = "claimRuleQuerySrvBindingQSPort")
    public ClaimRuleQuerySrvPortType getClaimRuleQuerySrvBindingQSPort() {
        return super.getPort(ClaimRuleQuerySrvBindingQSPort, ClaimRuleQuerySrvPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ClaimRuleQuerySrvPortType
     */
    @WebEndpoint(name = "claimRuleQuerySrvBindingQSPort")
    public ClaimRuleQuerySrvPortType getClaimRuleQuerySrvBindingQSPort(WebServiceFeature... features) {
        return super.getPort(ClaimRuleQuerySrvBindingQSPort, ClaimRuleQuerySrvPortType.class, features);
    }

}
