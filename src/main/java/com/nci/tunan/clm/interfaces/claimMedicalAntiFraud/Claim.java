package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import java.math.BigDecimal;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "claim", propOrder = {
        "clmno",
        "mngcom",
        "rgtdate",
        "accdate",
        "accresult1",
        "accresult2",
        "hospitalcode",
        "adjsum",
        "rptdate",
        "frms_biz_code",
        "frms_biz_type",
        "duty"
    })
public class Claim {
    
    @XmlElement(name="clmno", required = true)
    protected String clmno;
    @XmlElement(name="mngcom", required = true)
    protected String mngcom;
    @XmlElement(name="rgtdate", required = true)
    protected String rgtdate;
    @XmlElement(name="accdate",required = true)
    protected String accdate;
    @XmlElement(name="accresult1",required = true)
    protected String accresult1;
    @XmlElement(name="accresult2",required = true)
    protected String accresult2;
    @XmlElement(name="hospitalcode",required = true)
    protected String hospitalcode;
    @XmlElement(name="adjsum",required = true)
    protected BigDecimal adjsum;
    @XmlElement(name="rptdate",required = true)
    protected String rptdate;
    @XmlElement(name="frms_biz_code",required = true)
    protected String frms_biz_code;
    @XmlElement(name="frms_biz_type",required = true)
    protected String frms_biz_type;
    @XmlElement(name="duty", required = true)
    protected List<Duty> duty;
    public String getClmno() {
        return clmno;
    }
    public String getMngcom() {
        return mngcom;
    }
    public String getRgtdate() {
        return rgtdate;
    }
    public String getAccdate() {
        return accdate;
    }
    public String getAccresult1() {
        return accresult1;
    }
    public String getAccresult2() {
        return accresult2;
    }
    public String getHospitalcode() {
        return hospitalcode;
    }
    public BigDecimal getAdjsum() {
        return adjsum;
    }
    public String getRptdate() {
        return rptdate;
    }
    public String getFrms_biz_code() {
        return frms_biz_code;
    }
    public String getFrms_biz_type() {
        return frms_biz_type;
    }
    public List<Duty> getDuty() {
        return duty;
    }
    public void setClmno(String clmno) {
        this.clmno = clmno;
    }
    public void setMngcom(String mngcom) {
        this.mngcom = mngcom;
    }
    public void setRgtdate(String rgtdate) {
        this.rgtdate = rgtdate;
    }
    public void setAccdate(String accdate) {
        this.accdate = accdate;
    }
    public void setAccresult1(String accresult1) {
        this.accresult1 = accresult1;
    }
    public void setAccresult2(String accresult2) {
        this.accresult2 = accresult2;
    }
    public void setHospitalcode(String hospitalcode) {
        this.hospitalcode = hospitalcode;
    }
    public void setAdjsum(BigDecimal adjsum) {
        this.adjsum = adjsum;
    }
    public void setRptdate(String rptdate) {
        this.rptdate = rptdate;
    }
    public void setFrms_biz_code(String frms_biz_code) {
        this.frms_biz_code = frms_biz_code;
    }
    public void setFrms_biz_type(String frms_biz_type) {
        this.frms_biz_type = frms_biz_type;
    }
    public void setDuty(List<Duty> duty) {
        this.duty = duty;
    }
    
    
}
