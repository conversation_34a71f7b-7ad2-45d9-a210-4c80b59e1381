
package com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OutputData complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="OutputData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="claim" type="{http://www.newchinalife.com/service/bd}claimType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutputData", propOrder = {
    "result"
})
public class OutputData {

    @XmlElement(required = true)
    protected ClaimType result;

    

	

	/**
     * ��ȡclaim���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link ClaimType }
     *     
     */
	public ClaimType getResult() {
		return result;
	}

    /**
     * ����claim���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link ClaimType }
     *     
     */
	public void setResult(ClaimType result) {
		this.result = result;
	}

}
