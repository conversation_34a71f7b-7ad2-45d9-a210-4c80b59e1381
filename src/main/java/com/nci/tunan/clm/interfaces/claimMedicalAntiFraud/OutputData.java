
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OutputData complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OutputData">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Result" type="{http://www.newchinalife.com/service/bd}Result" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutputData", propOrder = {
    "modelResult",
    "modelResult1"
})
public class OutputData {

    @XmlElement(name = "modelResult", required = true)
    protected List<ModelResult> modelResult;
    
    @XmlElement(name = "modelResult1", required = true)
    protected List<ModelResult1> modelResult1;

    public List<ModelResult> getModelResult() {
        return modelResult;
    }

    public List<ModelResult1> getModelResult1() {
        return modelResult1;
    }

    public void setModelResult(List<ModelResult> modelResult) {
        this.modelResult = modelResult;
    }

    public void setModelResult1(List<ModelResult1> modelResult1) {
        this.modelResult1 = modelResult1;
    }

    

}
