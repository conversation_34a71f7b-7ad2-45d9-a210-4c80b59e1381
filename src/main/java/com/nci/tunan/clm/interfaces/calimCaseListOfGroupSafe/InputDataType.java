
package com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>InputDataType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="InputDataType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CustomerName">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerGender">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerBirthDay">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerCertType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerCertCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InputDataType", propOrder = {
    "customerName",
    "customerGender",
    "customerBirthDay",
    "customerCertType",
    "customerCertCode"
})
public class InputDataType {

    @XmlElement(name = "CustomerName", required = true)
    protected String customerName;
    @XmlElement(name = "CustomerGender", required = true)
    protected String customerGender;
    @XmlElement(name = "CustomerBirthDay", required = true)
    protected String customerBirthDay;
    @XmlElement(name = "CustomerCertType", required = true)
    protected String customerCertType;
    @XmlElement(name = "CustomerCertCode", required = true)
    protected String customerCertCode;

   /**
    * 
    * @description
    * @version V1.0.0
    * @title
    * <AUTHOR> <EMAIL>
    * @return
    */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * 设置customerName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setCustomerName(String value) {
        this.customerName = value;
    }

    /**
     * 获取customerGender属性的值。
     * 
     * @return
     *     possible object is
     *     {@link 
     *     String }
     *     
     */
    public String getCustomerGender() {
        return customerGender;
    }

    /**
     * 设置customerGender属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setCustomerGender(String value) {
        this.customerGender = value;
    }

    /**
     * 获取customerBirthDay属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getCustomerBirthDay() {
        return customerBirthDay;
    }

    /**
     * 设置customerBirthDay属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setCustomerBirthDay(String value) {
        this.customerBirthDay = value;
    }

    /**
     * 获取customerCertType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link 
     *     String }
     *     
     */
    public String getCustomerCertType() {
        return customerCertType;
    }

    /**
     * 设置customerCertType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setCustomerCertType(String value) {
        this.customerCertType = value;
    }

    /**
     * 获取customerCertCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getCustomerCertCode() {
        return customerCertCode;
    }

    /**
     * 设置customerCertCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setCustomerCertCode(String value) {
        this.customerCertCode = value;
    }

}
