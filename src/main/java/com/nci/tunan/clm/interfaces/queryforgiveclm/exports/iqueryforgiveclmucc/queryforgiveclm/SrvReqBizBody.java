package com.nci.tunan.clm.interfaces.queryforgiveclm.exports.iqueryforgiveclmucc.queryforgiveclm;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final   long serialVersionUID = 1L;
    
    @XmlElement(name = "InputData")
    protected com.nci.tunan.clm.interfaces.vo.queryForgiveClm.forgive.vo.InputData inputData;
    
    public com.nci.tunan.clm.interfaces.vo.queryForgiveClm.forgive.vo.InputData getInputData() {
        return inputData;
    }
    public void setInputData(com.nci.tunan.clm.interfaces.vo.queryForgiveClm.forgive.vo.InputData inputData) {
        this.inputData = inputData;
    }
}


