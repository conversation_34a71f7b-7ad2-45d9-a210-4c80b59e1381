
package com.nci.tunan.clm.interfaces.queryAllPolicyInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>InputDataType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="InputDataType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CustomerName">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerGender">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerBirthDay">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerCertType">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="CustomerCertCode">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InputDataType", propOrder = {
    "Name",
    "Sex",
    "Birthday",
    "IdType",
    "IdNo"
})
public class InputDataType {

    @XmlElement(name = "Name", required = true)
    protected String Name;
    @XmlElement(name = "Sex", required = true)
    protected String Sex;
    @XmlElement(name = "Birthday", required = true)
    protected String Birthday;
    @XmlElement(name = "IdType", required = true)
    protected String IdType;
    @XmlElement(name = "IdNo", required = true)
    protected String IdNo;

   /**
    * 
    * @description
    * @version V1.0.0
    * @title
    * <AUTHOR> <EMAIL>
    * @return
    */
    public String getName() {
        return Name;
    }

    /**
     * 设置customerName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setName(String value) {
        this.Name = value;
    }

    /**
     * 获取customerGender属性的值。
     * 
     * @return
     *     possible object is
     *     {@link 
     *     String }
     *     
     */
    public String getSex() {
        return Sex;
    }

    /**
     * 设置customerGender属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setSex(String value) {
        this.Sex = value;
    }

    /**
     * 获取customerBirthDay属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getBirthDay() {
        return Birthday;
    }

    /**
     * 设置customerBirthDay属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setBirthDay(String value) {
        this.Birthday = value;
    }

    /**
     * 获取customerCertType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link 
     *     String }
     *     
     */
    public String getIdType() {
        return IdType;
    }

    /**
     * 设置customerCertType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setIdType(String value) {
        this.IdType = value;
    }

    /**
     * 获取customerCertCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getIdNo() {
        return IdNo;
    }

    /**
     * 设置customerCertCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setIdNo(String value) {
        this.IdNo = value;
    }

}
