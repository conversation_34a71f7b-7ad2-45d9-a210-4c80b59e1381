package com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.1.3
 * 2020-03-30T17:36:22.376+08:00
 * Generated source version: 3.1.3
 * 
 */
@WebService(targetNamespace = "http://www.newchinalife.com", name = "claimRuleQuerySrvPortType")
@XmlSeeAlso({com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.service.bd.ObjectFactory.class, com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.common.header.in.ObjectFactory.class, com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.service.hd.ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface ClaimRuleQuerySrvPortType {

    @WebMethod(action = "urn:claimIsChecklistQuery")
    public void claimIsChecklistQuery(
        @WebParam(partName = "parametersReqHeader", name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true)
        com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.common.header.in.SysMsgHeader parametersReqHeader,
        @WebParam(partName = "parametersReqBody", name = "request", targetNamespace = "http://www.newchinalife.com/service/bd")
        com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.service.bd.RegSrvReqBody parametersReqBody,
        @WebParam(partName = "parametersResHeader", mode = WebParam.Mode.OUT, name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true)
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.common.header.in.SysMsgHeader> parametersResHeader,
        @WebParam(partName = "parametersResBody", mode = WebParam.Mode.OUT, name = "response", targetNamespace = "http://www.newchinalife.com/service/bd")
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.queryCaseFrmsIsChecklist.service.bd.RegSrvResBody> parametersResBody
    );
}
