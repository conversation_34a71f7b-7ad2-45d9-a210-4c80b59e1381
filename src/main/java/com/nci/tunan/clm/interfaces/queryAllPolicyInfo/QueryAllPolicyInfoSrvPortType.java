package com.nci.tunan.clm.interfaces.queryAllPolicyInfo;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * 
 * @description 
 * <AUTHOR> <EMAIL>
 * @.belongToModule CLM-理赔系统
 * @date 2017年11月22日 下午4:42:12
 */
@WebService(targetNamespace = "http://www.newchinalife.com", name = "ClaimListQuerySrvPortType")
@XmlSeeAlso({ ObjectFactory.class })
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface QueryAllPolicyInfoSrvPortType {

    @WebMethod(operationName = "ClaimListQuery", action = "urn:ClaimListQuery")
     void claimListQuery(
            @WebParam(partName = "parametersReqHeader", name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true) SysMsgHeader parametersReqHeader,
            @WebParam(partName = "parametersReqBody", name = "request", targetNamespace = "http://www.newchinalife.com/service/bd") RegSrvReqBody parametersReqBody,
            @WebParam(partName = "parametersResHeader", mode = WebParam.Mode.OUT, name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true) 
            javax.xml.ws.Holder<SysMsgHeader>  parametersResHeader,
            @WebParam(partName = "parametersResBody", mode = WebParam.Mode.OUT, name = "response", targetNamespace = "http://www.newchinalife.com/service/bd") javax.xml.ws.Holder<RegSrvResBody>
            parametersResBody);
}
