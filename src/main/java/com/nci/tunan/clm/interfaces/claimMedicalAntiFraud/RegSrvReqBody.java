
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RegSrvReqBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="RegSrvReqBody">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="bizHeader" type="{http://www.newchinalife.com/service/hd}SRVReqHead"/>
 *         &lt;element name="bizBody" type="{http://www.newchinalife.com/service/bd}RegSrvReqBizBody"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvReqBody", propOrder = {
    "bizHeader",
    "bizBody"
})
public class RegSrvReqBody {

    @XmlElement(required = true)
    protected SRVReqHead bizHeader;
    @XmlElement(required = true)
    protected RegSrvReqBizBody bizBody;

    /**
     * 获取bizHeader属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link SRVReqHead
     *      }
     *     
     */
    public SRVReqHead getBizHeader() {
        return bizHeader;
    }

    /**
     * 设置bizHeader属性的值。
     * 
     * @param value
     *            allowed object is {
     *            @link SRVReqHead
     *             }
     * 
     */
    public void setBizHeader(SRVReqHead value) {
        this.bizHeader = value;
    }

    /**
     * 获取bizBody属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link RegSrvReqBizBody
     *      }
     *     
     */
    public RegSrvReqBizBody getBizBody() {
        return bizBody;
    }

    /**
     * 设置bizBody属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link RegSrvReqBizBody
     *      }
     *     
     */
    public void setBizBody(RegSrvReqBizBody value) {
        this.bizBody = value;
    }

}
