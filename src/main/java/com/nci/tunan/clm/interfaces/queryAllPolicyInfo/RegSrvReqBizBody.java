
package com.nci.tunan.clm.interfaces.queryAllPolicyInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RegSrvReqBizBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="RegSrvReqBizBody">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="InputData" type="{http://www.newchinalife.com/service/bd}InputDataType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvReqBizBody", propOrder = {
    "inputData"
})
public class RegSrvReqBizBody {

    @XmlElement(name = "InputData", required = true)
    protected InputDataType inputData;

    /**
     * 获取inputData属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link InputDataType 
     *     }
     *     
     */
    public InputDataType getInputData() {
        return inputData;
    }

    /**
     * 设置inputData属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link InputDataType 
     *     }
     *     
     */
    public void setInputData(InputDataType value) {
        this.inputData = value;
    }

}
