package com.nci.tunan.clm.interfaces.queryCaseFrmsInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hop", propOrder = {
    "frms_hospitalname",
    "frms_hospcode", 
    "frms_hospital_grade"
})
public class hop {

	@XmlElement(name="frms_hospitalname", required = true)
    protected String frms_hospitalname;
	
	@XmlElement(name="frms_hospcode", required = true)
    protected String frms_hospcode;
	
	@XmlElement(name="frms_hospital_grade", required = true)
    protected String frms_hospital_grade;

	public String getFrms_hospitalname() {
		return frms_hospitalname;
	}

	public void setFrms_hospitalname(String frms_hospitalname) {
		this.frms_hospitalname = frms_hospitalname;
	}

	public String getFrms_hospcode() {
		return frms_hospcode;
	}

	public void setFrms_hospcode(String frms_hospcode) {
		this.frms_hospcode = frms_hospcode;
	}

	public String getFrms_hospital_grade() {
		return frms_hospital_grade;
	}

	public void setFrms_hospital_grade(String frms_hospital_grade) {
		this.frms_hospital_grade = frms_hospital_grade;
	}
	
}
