
package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>SysMsgHeader complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SysMsgHeader">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="msgId">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="msgDate">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value="\d{4}-\d{2}-\d{2}"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="msgTime">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value="\d{2}:\d{2}:\d{2}.\d{3}"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="servCd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;pattern value=".+"/>
 *               &lt;whiteSpace value="collapse"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="sysCd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;pattern value=".+"/>
 *               &lt;whiteSpace value="collapse"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="bizId">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="bizType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="orgCd">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="resCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resText" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bizResCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bizResText" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ver">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;whiteSpace value="collapse"/>
 *               &lt;pattern value=".+"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SysMsgHeader", namespace = "http://www.newchinalife.com/common/header/in", propOrder = {
    "msgId",
    "msgDate",
    "msgTime",
    "servCd",
    "sysCd",
    "bizId",
    "bizType",
    "orgCd",
    "resCd",
    "resText",
    "bizResCd",
    "bizResText",
    "ver"
})
public class SysMsgHeader {

    @XmlElement(required = true)
    protected String msgId;
    @XmlElement(required = true)
    protected String msgDate;
    @XmlElement(required = true)
    protected String msgTime;
    @XmlElement(required = true)
    protected String servCd;
    @XmlElement(required = true)
    protected String sysCd;
    @XmlElement(required = true)
    protected String bizId;
    @XmlElement(required = true)
    protected String bizType;
    @XmlElement(required = true)
    protected String orgCd;
    @XmlElement(required = true)
    protected String resCd;
    @XmlElement(required = true)
    protected String resText;
    @XmlElement(required = true)
    protected String bizResCd;
    @XmlElement(required = true)
    protected String bizResText;
    @XmlElement(required = true)
    protected String ver;

    /**
     * 获取msgId属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * 设置msgId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setMsgId(String value) {
        this.msgId = value;
    }

    /**
     * 获取msgDate属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getMsgDate() {
        return msgDate;
    }

    /**
     * 设置msgDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setMsgDate(String value) {
        this.msgDate = value;
    }

    /**
     * 获取msgTime属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getMsgTime() {
        return msgTime;
    }

    /**
     * 设置msgTime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setMsgTime(String value) {
        this.msgTime = value;
    }

    /**
     * 获取servCd属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getServCd() {
        return servCd;
    }

    /**
     * 设置servCd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setServCd(String value) {
        this.servCd = value;
    }

    /**
     * 获取sysCd属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getSysCd() {
        return sysCd;
    }

    /**
     * 设置sysCd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setSysCd(String value) {
        this.sysCd = value;
    }

    /**
     * 获取bizId属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getBizId() {
        return bizId;
    }

    /**
     * 设置bizId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setBizId(String value) {
        this.bizId = value;
    }

    /**
     * 获取bizType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * 设置bizType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setBizType(String value) {
        this.bizType = value;
    }

    /**
     * 获取orgCd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getOrgCd() {
        return orgCd;
    }

    /**
     * 设置orgCd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setOrgCd(String value) {
        this.orgCd = value;
    }

    /**
     * 获取resCd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link 
     *     String }
     *     
     */
    public String getResCd() {
        return resCd;
    }

    /**
     * 设置resCd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setResCd(String value) {
        this.resCd = value;
    }

    /**
     * 获取resText属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getResText() {
        return resText;
    }

    /**
     * 设置resText属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setResText(String value) {
        this.resText = value;
    }

    /**
     * 获取bizResCd属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getBizResCd() {
        return bizResCd;
    }

    /**
     * 设置bizResCd属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setBizResCd(String value) {
        this.bizResCd = value;
    }

    /**
     * 获取bizResText属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getBizResText() {
        return bizResText;
    }

    /**
     * 设置bizResText属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setBizResText(String value) {
        this.bizResText = value;
    }

    /**
     * 获取ver属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getVer() {
        return ver;
    }

    /**
     * 设置ver属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link
     *      String }
     *     
     */
    public void setVer(String value) {
        this.ver = value;
    }

}
