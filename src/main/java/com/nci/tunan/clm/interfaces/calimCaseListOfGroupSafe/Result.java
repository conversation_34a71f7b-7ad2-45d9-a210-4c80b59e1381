
package com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Result complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="Result">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ClmNo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClmState" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StateDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClmType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CaseContent" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SurveyFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SedCheckFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PayMoney" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PayState" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PayDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Checker" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Approver" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Result", propOrder = {
    "clmNo",
    "clmState",
    "stateDate",
    "clmType",
    "caseContent",
    "surveyFlag",
    "sedCheckFlag",
    "payMoney",
    "payState",
    "payDate",
    "checker",
    "approver",
    "claimPolicy",
    "claimBusiProd",
    "accResult2",
    "auditRemark",
    "endCaseTime"
})
public class Result {

    @XmlElement(name = "ClmNo", required = true)
    protected String clmNo;
    @XmlElement(name = "ClmState", required = true)
    protected String clmState;
    @XmlElement(name = "StateDate", required = true)
    protected String stateDate;
    @XmlElement(name = "ClmType", required = true)
    protected String clmType;
    @XmlElement(name = "CaseContent", required = true)
    protected String caseContent;
    @XmlElement(name = "SurveyFlag", required = true)
    protected String surveyFlag;
    @XmlElement(name = "SedCheckFlag", required = true)
    protected String sedCheckFlag;
    @XmlElement(name = "PayMoney", required = true)
    protected String payMoney;
    @XmlElement(name = "PayState", required = true)
    protected String payState;
    @XmlElement(name = "PayDate", required = true)
    protected String payDate;
    @XmlElement(name = "Checker", required = true)
    protected String checker;
    @XmlElement(name = "Approver", required = true)
    protected String approver;
    //RM115673-P00001001696 新增 
    @XmlElement(name = "ClaimPolicy", required = true)
    protected String claimPolicy;
    @XmlElement(name = "ClaimBusiProd", required = true)
    protected String claimBusiProd;
    @XmlElement(name = "AccResult2", required = true)
    protected String accResult2;
    @XmlElement(name = "AuditRemark", required = true)
    protected String auditRemark;
    @XmlElement(name = "EndCaseTime", required = true)
    protected String endCaseTime;
    /**
     * 获取clmNo属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getClmNo() {
        return clmNo;
    }

    /**
     * 设置clmNo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setClmNo(String value) {
        this.clmNo = value;
    }

    /**
     * 获取clmState属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getClmState() {
        return clmState;
    }

    /**
     * 设置clmState属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setClmState(String value) {
        this.clmState = value;
    }

    /**
     * 获取stateDate属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getStateDate() {
        return stateDate;
    }

    /**
     * 设置stateDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setStateDate(String value) {
        this.stateDate = value;
    }

    /**
     * 获取clmType属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getClmType() {
        return clmType;
    }

    /**
     * 设置clmType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setClmType(String value) {
        this.clmType = value;
    }

    /**
     * 获取caseContent属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getCaseContent() {
        return caseContent;
    }

    /**
     * 设置caseContent属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setCaseContent(String value) {
        this.caseContent = value;
    }

    /**
     * 获取surveyFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getSurveyFlag() {
        return surveyFlag;
    }

    /**
     * 设置surveyFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setSurveyFlag(String value) {
        this.surveyFlag = value;
    }

    /**
     * 获取sedCheckFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getSedCheckFlag() {
        return sedCheckFlag;
    }

    /**
     * 设置sedCheckFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setSedCheckFlag(String value) {
        this.sedCheckFlag = value;
    }

    /**
     * 获取payMoney属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPayMoney() {
        return payMoney;
    }

    /**
     * 设置payMoney属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setPayMoney(String value) {
        this.payMoney = value;
    }

    /**
     * 获取payState属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPayState() {
        return payState;
    }

    /**
     * 设置payState属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setPayState(String value) {
        this.payState = value;
    }

    /**
     * 获取payDate属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPayDate() {
        return payDate;
    }

    /**
     * 设置payDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setPayDate(String value) {
        this.payDate = value;
    }

    /**
     * 获取checker属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getChecker() {
        return checker;
    }

    /**
     * 设置checker属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setChecker(String value) {
        this.checker = value;
    }

    /**
     * 获取approver属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getApprover() {
        return approver;
    }

    /**
     * 设置approver属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setApprover(String value) {
        this.approver = value;
    }

    
	public String getClaimPolicy() {
		return claimPolicy;
	}

	public void setClaimPolicy(String claimPolicy) {
		this.claimPolicy = claimPolicy;
	}

	public String getClaimBusiProd() {
		return claimBusiProd;
	}

	public void setClaimBusiProd(String claimBusiProd) {
		this.claimBusiProd = claimBusiProd;
	}

	public String getAccResult2() {
		return accResult2;
	}

	public void setAccResult2(String accResult2) {
		this.accResult2 = accResult2;
	}

	public String getAuditRemark() {
		return auditRemark;
	}

	public void setAuditRemark(String auditRemark) {
		this.auditRemark = auditRemark;
	}

	public String getEndCaseTime() {
		return endCaseTime;
	}

	public void setEndCaseTime(String endCaseTime) {
		this.endCaseTime = endCaseTime;
	}
    
}
