
package com.nci.tunan.clm.interfaces.queryAllPolicyInfo;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private static final  QName _Request_QNAME = new QName("http://www.newchinalife.com/service/bd", "request");
    private static final  QName _SRVReqHead_QNAME = new QName("http://www.newchinalife.com/service/hd", "SRVReqHead");
    private static final  QName _Response_QNAME = new QName("http://www.newchinalife.com/service/bd", "response");
    private static final  QName _SysHeader_QNAME = new QName("http://www.newchinalife.com/common/header/in", "sysHeader");
    private static final  QName _SRVResHead_QNAME = new QName("http://www.newchinalife.com/service/hd", "SRVResHead");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link
     *  RegSrvResBody }
     * 
     */
    public RegSrvResBody createRegSrvResBody() {
        return new RegSrvResBody();
    }

    /**
     * Create an instance of {@link 
     * RegSrvReqBody }
     * 
     */
    public RegSrvReqBody createRegSrvReqBody() {
        return new RegSrvReqBody();
    }

    /**
     * Create an instance of {@link
     *  Result }
     * 
     */
    public Result createResult() {
        return new Result();
    }

    /**
     * Create an instance of {@link
     *  RegSrvResBizBody }
     * 
     */
    public RegSrvResBizBody createRegSrvResBizBody() {
        return new RegSrvResBizBody();
    }

    /**
     * Create an instance of {@link
     *  OutputData }
     * 
     */
    public OutputData createOutputData() {
        return new OutputData();
    }

    /**
     * Create an instance of {@link 
     * RegSrvReqBizBody }
     * 
     */
    public RegSrvReqBizBody createRegSrvReqBizBody() {
        return new RegSrvReqBizBody();
    }

    /**
     * Create an instance of {@link
     *  InputDataType }
     * 
     */
    public InputDataType createInputDataType() {
        return new InputDataType();
    }

    /**
     * Create an instance of {@link
     *  SysMsgHeader }
     * 
     */
    public SysMsgHeader createSysMsgHeader() {
        return new SysMsgHeader();
    }

    /**
     * Create an instance of {@link 
     * SRVReqHead }
     * 
     */
    public SRVReqHead createSRVReqHead() {
        return new SRVReqHead();
    }

    /**
     * Create an instance of {@link 
     * SRVResHead }
     * 
     */
    public SRVResHead createSRVResHead() {
        return new SRVResHead();
    }

    /**
     * Create an instance of {@link
     *  JAXBElement }
     *  {@code <
     *  }
     *  {
     *  @link RegSrvReqBody 
     *  }
     *  {@code >
     *  }
     *  }
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/bd", name = "request")
    public JAXBElement<RegSrvReqBody> createRequest(RegSrvReqBody value) {
        return new JAXBElement<RegSrvReqBody>(_Request_QNAME, RegSrvReqBody.class, null, value);
    }

    /**
     * Create an instance of {
     * @link JAXBElement 
     * }{
     * @code <
     * }{
     * @link SRVReqHead 
     * }{
     * @code >
     * }
     * }
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/hd", name = "SRVReqHead")
    public JAXBElement<SRVReqHead> createSRVReqHead(SRVReqHead value) {
        return new JAXBElement<SRVReqHead>(_SRVReqHead_QNAME, SRVReqHead.class, null, value);
    }

    /**
     * Create an instance of {
     * @link JAXBElement 
     * }{
     * @code <
     * }{
     * @link RegSrvResBody
     *  }{
     *  @code >}
     *  }
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/bd", name = "response")
    public JAXBElement<RegSrvResBody> createResponse(RegSrvResBody value) {
        return new JAXBElement<RegSrvResBody>(_Response_QNAME, RegSrvResBody.class, null, value);
    }

    /**
     * Create an instance of {
     * @link JAXBElement 
     * }{
     * @code <
     * }{
     * @link SysMsgHeader 
     * }{
     * @code >}
     * }
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/common/header/in", name = "sysHeader")
    public JAXBElement<SysMsgHeader> createSysHeader(SysMsgHeader value) {
        return new JAXBElement<SysMsgHeader>(_SysHeader_QNAME, SysMsgHeader.class, null, value);
    }

    /**
     * Create an instance of {
     * @link JAXBElement 
     * }{
     * @code <
     * }{
     * @link SRVResHead
     *  }{
     *  @code >}
     *  }
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/hd", name = "SRVResHead")
    public JAXBElement<SRVResHead> createSRVResHead(SRVResHead value) {
        return new JAXBElement<SRVResHead>(_SRVResHead_QNAME, SRVResHead.class, null, value);
    }

}
