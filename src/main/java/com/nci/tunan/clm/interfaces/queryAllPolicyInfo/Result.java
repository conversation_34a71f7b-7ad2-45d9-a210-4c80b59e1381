
package com.nci.tunan.clm.interfaces.queryAllPolicyInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Result complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="Result">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ClmNo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClmState" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="StateDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClmType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CaseContent" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SurveyFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SedCheckFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PayMoney" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PayState" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PayDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Checker" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Approver" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Result", propOrder = {
    "PolicyNo",
    "PolicyType",
    "CustRole",
    "PolicyState",
    "InsuredLiability",
    "EffectiveDate",
    "InsuranceEndDate",
    "PaymentEndDate",
    "RiskGrade",
    "PaymentFrequency",
    "AgentStarLv"
})
public class Result {

    @XmlElement(name = "PolicyNo", required = true)
    protected String PolicyNo;
    @XmlElement(name = "PolicyType", required = true)
    protected String PolicyType;
    @XmlElement(name = "CustRole", required = true)
    protected String CustRole;
    @XmlElement(name = "PolicyState", required = true)
    protected String PolicyState;
    @XmlElement(name = "InsuredLiability", required = true)
    protected String InsuredLiability;
    @XmlElement(name = "EffectiveDate", required = true)
    protected String EffectiveDate;
    @XmlElement(name = "InsuranceEndDate", required = true)
    protected String InsuranceEndDate;
    @XmlElement(name = "PaymentEndDate", required = true)
    protected String PaymentEndDate;
    @XmlElement(name = "RiskGrade", required = true)
    protected String RiskGrade;
    @XmlElement(name = "PaymentFrequency", required = true)
    protected String PaymentFrequency;
    @XmlElement(name = "AgentStarLv", required = true)
    protected String AgentStarLv;

    /**
     * 获取PolicyNo属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPolicyNo() {
        return PolicyNo;
    }

    /**
     * 设置clmNo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setPolicyNo(String value) {
        this.PolicyNo = value;
    }

    /**
     * 获取clmState属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPolicyType() {
        return PolicyType;
    }

    /**
     * 设置clmState属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setPolicyType(String value) {
        this.PolicyType = value;
    }

    /**
     * 获取stateDate属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getCustRole() {
        return CustRole;
    }

    /**
     * 设置stateDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setCustRole(String value) {
        this.CustRole = value;
    }

    /**
     * 获取clmType属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getPolicyState() {
        return PolicyState;
    }

    /**
     * 设置clmType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setPolicyState(String value) {
        this.PolicyState = value;
    }

    /**
     * 获取caseContent属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getInsuredLiability() {
        return InsuredLiability;
    }

    /**
     * 设置caseContent属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setInsuredLiability(String value) {
        this.InsuredLiability = value;
    }

    /**
     * 获取surveyFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getEffectiveDate() {
        return EffectiveDate;
    }

    /**
     * 设置surveyFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setEffectiveDate(String value) {
        this.EffectiveDate = value;
    }

    /**
     * 获取sedCheckFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getInsuranceEndDate() {
        return InsuranceEndDate;
    }

    /**
     * 设置sedCheckFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setInsuranceEndDate(String value) {
        this.InsuranceEndDate = value;
    }

    /**
     * 获取payMoney属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPaymentEndDate() {
        return PaymentEndDate;
    }

    /**
     * 设置payMoney属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setPaymentEndDate(String value) {
        this.PaymentEndDate = value;
    }

    /**
     * 获取payState属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getRiskGrade() {
        return RiskGrade;
    }

    /**
     * 设置payState属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setRiskGrade(String value) {
        this.RiskGrade = value;
    }

    /**
     * 获取payDate属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPaymentFrequency() {
        return PaymentFrequency;
    }

    /**
     * 设置payDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setPaymentFrequency(String value) {
        this.PaymentFrequency = value;
    }

    /**
     * 获取checker属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getAgentStarLv() {
        return AgentStarLv;
    }

    /**
     * 设置checker属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setAgentStarLv(String value) {
        this.AgentStarLv = value;
    }


}
