
package com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>SRVReqHead complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SRVReqHead">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Operator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RowNumStart" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PageRowNum" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PageFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TotalRowNum" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OrderFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OrderField" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ManageCom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RiskCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ContNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SysRouteFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SRVReqHead", namespace = "http://www.newchinalife.com/service/hd", propOrder = {
    "operator",
    "rowNumStart",
    "pageRowNum",
    "pageFlag",
    "totalRowNum",
    "orderFlag",
    "orderField",
    "manageCom",
    "riskCode",
    "contNo",
    "sysRouteFlag"
})
public class SRVReqHead {

    @XmlElement(name = "Operator", required = true)
    protected String operator;
    @XmlElement(name = "RowNumStart", required = true)
    protected String rowNumStart;
    @XmlElement(name = "PageRowNum", required = true)
    protected String pageRowNum;
    @XmlElement(name = "PageFlag", required = true)
    protected String pageFlag;
    @XmlElement(name = "TotalRowNum", required = true)
    protected String totalRowNum;
    @XmlElement(name = "OrderFlag", required = true)
    protected String orderFlag;
    @XmlElement(name = "OrderField", required = true)
    protected String orderField;
    @XmlElement(name = "ManageCom")
    protected String manageCom;
    @XmlElement(name = "RiskCode")
    protected String riskCode;
    @XmlElement(name = "ContNo")
    protected String contNo;
    @XmlElement(name = "SysRouteFlag")
    protected String sysRouteFlag;

    /**
     * 获取operator属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置operator属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setOperator(String value) {
        this.operator = value;
    }

    /**
     * 获取rowNumStart属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getRowNumStart() {
        return rowNumStart;
    }

    /**
     * 设置rowNumStart属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setRowNumStart(String value) {
        this.rowNumStart = value;
    }

    /**
     * 获取pageRowNum属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getPageRowNum() {
        return pageRowNum;
    }

    /**
     * 设置pageRowNum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setPageRowNum(String value) {
        this.pageRowNum = value;
    }

    /**
     * 获取pageFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link
     *      String }
     *     
     */
    public String getPageFlag() {
        return pageFlag;
    }

    /**
     * 设置pageFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link 
     *     String }
     *     
     */
    public void setPageFlag(String value) {
        this.pageFlag = value;
    }

    /**
     * 获取totalRowNum属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getTotalRowNum() {
        return totalRowNum;
    }

    /**
     * 设置totalRowNum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setTotalRowNum(String value) {
        this.totalRowNum = value;
    }

    /**
     * 获取orderFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getOrderFlag() {
        return orderFlag;
    }

    /**
     * 设置orderFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setOrderFlag(String value) {
        this.orderFlag = value;
    }

    /**
     * 获取orderField属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public String getOrderField() {
        return orderField;
    }

    /**
     * 设置orderField属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setOrderField(String value) {
        this.orderField = value;
    }

    /**
     * 获取manageCom属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getManageCom() {
        return manageCom;
    }

    /**
     * 设置manageCom属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setManageCom(String value) {
        this.manageCom = value;
    }

    /**
     * 获取riskCode属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String 
     *     }
     *     
     *     
     */
    public String getRiskCode() {
        return riskCode;
    }

    /**
     * 设置riskCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setRiskCode(String value) {
        this.riskCode = value;
    }

    /**
     * 获取contNo属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getContNo() {
        return contNo;
    }

    /**
     * 设置contNo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String
     *      }
     *     
     */
    public void setContNo(String value) {
        this.contNo = value;
    }

    /**
     * 获取sysRouteFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {
     *     @link String
     *      }
     *     
     */
    public String getSysRouteFlag() {
        return sysRouteFlag;
    }

    /**
     * 设置sysRouteFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {
     *     @link String 
     *     }
     *     
     */
    public void setSysRouteFlag(String value) {
        this.sysRouteFlag = value;
    }

}
