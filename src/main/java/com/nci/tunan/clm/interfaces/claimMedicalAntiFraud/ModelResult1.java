package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "modelResult1", propOrder = {
    "clmno",
    "modelName",
    "probability",
    "modelExplanation"
})
public class ModelResult1 {

    @XmlElement(name="clmno",required=true)
    private String clmno;
    
    @XmlElement(name="modelName",required=true)
    private String modelName;
    
    @XmlElement(name="probability",required=true)
    private String probability;
    
    @XmlElement(name="modelExplanation",required=true)
    private String modelExplanation;

    public String getClmno() {
        return clmno;
    }

    public String getModelName() {
        return modelName;
    }

    public String getProbability() {
        return probability;
    }

    public String getModelExplanation() {
        return modelExplanation;
    }

    public void setClmno(String clmno) {
        this.clmno = clmno;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public void setProbability(String probability) {
        this.probability = probability;
    }

    public void setModelExplanation(String modelExplanation) {
        this.modelExplanation = modelExplanation;
    }
    
    
}
