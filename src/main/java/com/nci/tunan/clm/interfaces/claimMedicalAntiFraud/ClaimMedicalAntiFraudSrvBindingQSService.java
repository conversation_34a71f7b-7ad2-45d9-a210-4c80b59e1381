package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.util.Constants;

/**
 * 
 * @description  wu
 * <AUTHOR> <EMAIL>
 * @.belongToModule CLM-理赔系统
 * @date 2017年11月22日 下午4:12:43
 */
@WebServiceClient(name = "ClaimMedicalAntiFraudSrvBindingQSService", 
                  wsdlLocation = "http://*********:8111/services/P00002002893?wsdl",
                  targetNamespace = "http://www.newchinalife.com") 
public class ClaimMedicalAntiFraudSrvBindingQSService extends Service {

    public final static   URL WSDL_LOCATION;

    public final static   QName SERVICE = new QName("http://www.newchinalife.com", "ClaimListQuerySrvBindingQSService");
    public final static   QName ClaimListQuerySrvBindingQSPort = new QName("http://www.newchinalife.com",
            "ClaimListQuerySrvBindingQSPort");
    static {
        URL url = null;
        try {
            url = new URL(Constants.SERVICEENVPARAMAP.get("ESB") + "P00002002893?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ClaimMedicalAntiFraudSrvBindingQSService.class.getName()).log(
                    java.util.logging.Level.INFO, "Can not initialize the default wsdl from {0}",
                    Constants.SERVICEENVPARAMAP.get("ESB") + "P00002002893?wsdl");
            throw new BizException("Can not initialize the default wsdl from {0}",
                    Constants.SERVICEENVPARAMAP.get("ESB") + "P00002002893?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public ClaimMedicalAntiFraudSrvBindingQSService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ClaimMedicalAntiFraudSrvBindingQSService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ClaimMedicalAntiFraudSrvBindingQSService() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
   /* public ClaimListQuerySrvBindingQSService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }*/

    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    /*public ClaimListQuerySrvBindingQSService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }*/

    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    /*public ClaimListQuerySrvBindingQSService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    } */   

    /**
     *
     * @return
     *     returns ClaimListQuerySrvPortType
     */
    @WebEndpoint(name = "ClaimListQuerySrvBindingQSPort")
    public ClaimMedicalAntiFraudSrvPortType getClaimListQuerySrvBindingQSPort() {
        return super.getPort(ClaimListQuerySrvBindingQSPort, ClaimMedicalAntiFraudSrvPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ClaimListQuerySrvPortType
     */
    @WebEndpoint(name = "ClaimListQuerySrvBindingQSPort")
    public ClaimMedicalAntiFraudSrvPortType getClaimListQuerySrvBindingQSPort(WebServiceFeature... features) {
        return super.getPort(ClaimListQuerySrvBindingQSPort, ClaimMedicalAntiFraudSrvPortType.class, features);
    }

}
