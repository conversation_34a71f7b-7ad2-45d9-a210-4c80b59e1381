
package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import com.nci.tunan.clm.interfaces.queryCaseFrms.service.hd.SRVResHead;


/**
 * <p>RegSrvResBody complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="RegSrvResBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="bizHeader" type="{http://www.newchinalife.com/service/hd}SRVResHead"/&gt;
 *         &lt;element name="bizBody" type="{http://www.newchinalife.com/service/bd}RegSrvResBizBody"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvResBody", propOrder = {
    "bizHeader",
    "bizBody"
})
public class RegSrvResBody {

    @XmlElement(required = true)
    protected SRVResHead bizHeader;
    @XmlElement(required = true)
    protected RegSrvResBizBody bizBody;

    /**
     * ��ȡbizHeader���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link SRVResHead }
     *     
     */
    public SRVResHead getBizHeader() {
        return bizHeader;
    }

    /**
     * ����bizHeader���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link SRVResHead }
     *     
     */
    public void setBizHeader(SRVResHead value) {
        this.bizHeader = value;
    }

    /**
     * ��ȡbizBody���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link RegSrvResBizBody }
     *     
     */
    public RegSrvResBizBody getBizBody() {
        return bizBody;
    }

    /**
     * ����bizBody���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link RegSrvResBizBody }
     *     
     */
    public void setBizBody(RegSrvResBizBody value) {
        this.bizBody = value;
    }

}
