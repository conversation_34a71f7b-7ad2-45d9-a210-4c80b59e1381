package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "duty", propOrder = {
    "caseno",
    "polno", 
    "dutycode",
    "getdutykind",
    "getdutycode", 
    "caserelano",
    "contno",
    "riskcode",
    "standpay",
    "sum_standpay",
    "agentcode",
    "sum_amnt",
    "appntno",
    "insuredno",
    "cvalidate",
    "signdate",
    "lastrevdate",
    "enddate",
    "accienddate",
    "amnt"
})
public class Duty {
    
    @XmlElement(required = true)
    protected String caseno;
    
    @XmlElement(required = true)
    protected String polno;
    
    @XmlElement(required = true)
    protected String dutycode;
    
    @XmlElement(required = true)
    protected String getdutykind;
    
    @XmlElement(required = true)
    protected String getdutycode;
    
    @XmlElement(required = true)
    protected String caserelano;
    
    @XmlElement(required = true)
    protected String contno;
    
    @XmlElement(required = true)
    protected String riskcode;
    
    @XmlElement(required = true)
    protected BigDecimal standpay;
    
    @XmlElement(required = true)
    protected BigDecimal sum_standpay;
    
    @XmlElement(required = true)
    protected String agentcode;
    
    @XmlElement(required = true)
    protected BigDecimal sum_amnt;
    
    @XmlElement(required = true)
    protected String appntno;
    
    @XmlElement(required = true)
    protected String insuredno;
    
    @XmlElement(required = true)
    protected String cvalidate;
    
    @XmlElement(required = true)
    protected String signdate;
    
    @XmlElement(required = true)
    protected String lastrevdate;
    
    @XmlElement(required = true)
    protected String enddate;
    
    @XmlElement(required = true)
    protected String accienddate;
    
    @XmlElement(required = true)
    protected BigDecimal amnt;

    public String getCaseno() {
        return caseno;
    }

    public String getPolno() {
        return polno;
    }

    public String getDutycode() {
        return dutycode;
    }

    public String isGetdutykind() {
        return getdutykind;
    }

    public String getGetdutycode() {
        return getdutycode;
    }

    public String getCaserelano() {
        return caserelano;
    }

    public String getContno() {
        return contno;
    }

    public String getRiskcode() {
        return riskcode;
    }

    public BigDecimal getStandpay() {
        return standpay;
    }

    public BigDecimal getSum_standpay() {
        return sum_standpay;
    }

    public String getAgentcode() {
        return agentcode;
    }

    public BigDecimal getSum_amnt() {
        return sum_amnt;
    }

    public String getAppntno() {
        return appntno;
    }

    public String getInsuredno() {
        return insuredno;
    }

    public String getCvalidate() {
        return cvalidate;
    }

    public String getSigndate() {
        return signdate;
    }

    public String getLastrevdate() {
        return lastrevdate;
    }

    public String getEnddate() {
        return enddate;
    }

    public String getAccienddate() {
        return accienddate;
    }

    public BigDecimal getAmnt() {
        return amnt;
    }

    public void setCaseno(String caseno) {
        this.caseno = caseno;
    }

    public void setPolno(String polno) {
        this.polno = polno;
    }

    public void setDutycode(String dutycode) {
        this.dutycode = dutycode;
    }

    public void setGetdutykind(String getdutykind) {
        this.getdutykind = getdutykind;
    }

    public void setGetdutycode(String getdutycode) {
        this.getdutycode = getdutycode;
    }

    public void setCaserelano(String caserelano) {
        this.caserelano = caserelano;
    }

    public void setContno(String contno) {
        this.contno = contno;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode;
    }

    public void setStandpay(BigDecimal standpay) {
        this.standpay = standpay;
    }

    public void setSum_standpay(BigDecimal sum_standpay) {
        this.sum_standpay = sum_standpay;
    }

    public void setAgentcode(String agentcode) {
        this.agentcode = agentcode;
    }

    public void setSum_amnt(BigDecimal sum_amnt) {
        this.sum_amnt = sum_amnt;
    }

    public void setAppntno(String appntno) {
        this.appntno = appntno;
    }

    public void setInsuredno(String insuredno) {
        this.insuredno = insuredno;
    }

    public void setCvalidate(String cvalidate) {
        this.cvalidate = cvalidate;
    }

    public void setSigndate(String signdate) {
        this.signdate = signdate;
    }

    public void setLastrevdate(String lastrevdate) {
        this.lastrevdate = lastrevdate;
    }

    public void setEnddate(String enddate) {
        this.enddate = enddate;
    }

    public void setAccienddate(String accienddate) {
        this.accienddate = accienddate;
    }

    public void setAmnt(BigDecimal amnt) {
        this.amnt = amnt;
    }
    
}
