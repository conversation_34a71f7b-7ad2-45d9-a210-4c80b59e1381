package com.nci.tunan.clm.interfaces.claimMedicalAntiFraud;

import java.math.BigDecimal;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "claim1", propOrder = {
        "clmno",
        "caseno",
        "daycount_max",
        "daycount",
        "daycount_zy",
        "fee",
        "adjsum",
        "fee_zy",
        "adjsum_zy",
        "accresult1",
        "accresult2",
        "rgtdate",
        "hospitalcode",
        "conttype",
        "frms_biz_code",
        "frms_biz_type",
        "invoice"
    })
public class Claim1 {
    
    
    @XmlElement(name = "clmno" ,required = true)
    protected String clmno;
    
    @XmlElement(name = "caseno" ,required = true)
    protected String caseno;
    
    @XmlElement(name = "daycount_max" ,required = true)
    protected String daycount_max;
    
    @XmlElement(name = "daycount" ,required = true)
    protected String daycount;
    
    @XmlElement(name = "daycount_zy" ,required = true)
    protected String daycount_zy;
    
    @XmlElement(name = "fee" ,required = true)
    protected BigDecimal fee;
    
    @XmlElement(name = "adjsum" ,required = true)
    protected BigDecimal adjsum;
    
    @XmlElement(name = "fee_zy" ,required = true)
    protected BigDecimal fee_zy;
    
    @XmlElement(name = "adjsum_zy" ,required = true)
    protected BigDecimal adjsum_zy;
    
    @XmlElement(name = "accresult1" ,required = true)
    protected String accresult1;
    
    @XmlElement(name = "accresult2" ,required = true)
    protected String accresult2;
    
    @XmlElement(name = "rgtdate" ,required = true)
    protected String rgtdate;
    
    @XmlElement(name = "hospitalcode" ,required = true)
    protected String hospitalcode;
    
    @XmlElement(name = "conttype" ,required = true)
    protected String conttype;
    
    @XmlElement(name = "frms_biz_code" ,required = true)
    protected String frms_biz_code;
    
    @XmlElement(name = "frms_biz_type" ,required = true)
    protected String frms_biz_type;
    
    @XmlElement(name = "invoice" ,required = true)
    protected List<Invoice> invoice;

    public String getClmno() {
        return clmno;
    }

    public String getCaseno() {
        return caseno;
    }

    public String getDaycount_max() {
        return daycount_max;
    }

    public String getDaycount() {
        return daycount;
    }

    public String getDaycount_zy() {
        return daycount_zy;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public BigDecimal getAdjsum() {
        return adjsum;
    }

    public BigDecimal getFee_zy() {
        return fee_zy;
    }

    public BigDecimal getAdjsum_zy() {
        return adjsum_zy;
    }

    public String getAccresult1() {
        return accresult1;
    }

    public String getAccresult2() {
        return accresult2;
    }

    public String getRgtdate() {
        return rgtdate;
    }

    public String getHospitalcode() {
        return hospitalcode;
    }

    public String getConttype() {
        return conttype;
    }

    public String getFrms_biz_code() {
        return frms_biz_code;
    }

    public String getFrms_biz_type() {
        return frms_biz_type;
    }

    public List<Invoice> getInvoice() {
        return invoice;
    }

    public void setClmno(String clmno) {
        this.clmno = clmno;
    }

    public void setCaseno(String caseno) {
        this.caseno = caseno;
    }

    public void setDaycount_max(String daycount_max) {
        this.daycount_max = daycount_max;
    }

    public void setDaycount(String daycount) {
        this.daycount = daycount;
    }

    public void setDaycount_zy(String daycount_zy) {
        this.daycount_zy = daycount_zy;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public void setAdjsum(BigDecimal adjsum) {
        this.adjsum = adjsum;
    }

    public void setFee_zy(BigDecimal fee_zy) {
        this.fee_zy = fee_zy;
    }

    public void setAdjsum_zy(BigDecimal adjsum_zy) {
        this.adjsum_zy = adjsum_zy;
    }

    public void setAccresult1(String accresult1) {
        this.accresult1 = accresult1;
    }

    public void setAccresult2(String accresult2) {
        this.accresult2 = accresult2;
    }

    public void setRgtdate(String rgtdate) {
        this.rgtdate = rgtdate;
    }

    public void setHospitalcode(String hospitalcode) {
        this.hospitalcode = hospitalcode;
    }

    public void setConttype(String conttype) {
        this.conttype = conttype;
    }

    public void setFrms_biz_code(String frms_biz_code) {
        this.frms_biz_code = frms_biz_code;
    }

    public void setFrms_biz_type(String frms_biz_type) {
        this.frms_biz_type = frms_biz_type;
    }

    public void setInvoice(List<Invoice> invoice) {
        this.invoice = invoice;
    }
    
}
    
   
