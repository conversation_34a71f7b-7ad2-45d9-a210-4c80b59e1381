package com.nci.tunan.clm.interfaces.queryclmimage.exports.iqueryclmimageucc.queryclmimage;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExA;
import com.nci.tunan.clm.interfaces.queryclmimage.exports.iqueryclmimageucc.queryclmimage.SrvReqBizBody;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBody", propOrder = { "bizHeader", "bizBody" })
public class SrvReqBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final   long serialVersionUID = 1L;
    
    @XmlElement(required = true)
    protected BizHeaderExA  bizHeader;
    @XmlElement(required = true)
    protected SrvReqBizBody bizBody;
    
    public BizHeaderExA  getBizHeader() {
        return bizHeader;
    }
    public void setBizHeader(BizHeaderExA  bizHeader) {
        this.bizHeader = bizHeader;
    }
    public SrvReqBizBody getBizBody() {
        return bizBody;
    }
    public void setBizBody(SrvReqBizBody bizBody) {
        this.bizBody = bizBody;
    }
}

