
package com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe;

/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName; 

/**
 * This class was generated by Apache CXF 3.0.0
 * 2016-12-27T14:51:11.313+08:00
 * Generated source version: 3.0.0
 * 
 */
public final class ClaimListQuerySrvPortType_ClaimListQuerySrvBindingQSPort_Client {

    private static final   QName SERVICE_NAME = new QName("http://www.newchinalife.com", "ClaimListQuerySrvBindingQSService");

    private ClaimListQuerySrvPortType_ClaimListQuerySrvBindingQSPort_Client() {
    }

    public static void main(String[] args) throws java.lang.Exception {
        URL wsdlURL = ClaimListQuerySrvBindingQSService.WSDL_LOCATION;
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        ClaimListQuerySrvBindingQSService ss = new ClaimListQuerySrvBindingQSService(wsdlURL, SERVICE_NAME);
        ClaimListQuerySrvPortType port = ss.getClaimListQuerySrvBindingQSPort();  
        
        {
        com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe.SysMsgHeader _claimListQuery_parametersReqHeader = null;
        com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe.RegSrvReqBody _claimListQuery_parametersReqBody = null;
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe.SysMsgHeader> _claimListQuery_parametersResHeader =
                new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe.SysMsgHeader>();
        javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe.RegSrvResBody> _claimListQuery_parametersResBody =
                new javax.xml.ws.Holder<com.nci.tunan.clm.interfaces.calimCaseListOfGroupSafe.RegSrvResBody>();
        port.claimListQuery(_claimListQuery_parametersReqHeader, _claimListQuery_parametersReqBody, _claimListQuery_parametersResHeader, _claimListQuery_parametersResBody);
        } 
    }

}
