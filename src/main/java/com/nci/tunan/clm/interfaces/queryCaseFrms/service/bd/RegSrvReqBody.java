
package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import com.nci.tunan.clm.interfaces.queryCaseFrms.service.hd.SRVReqHead;


/**
 * <p>RegSrvReqBody complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="RegSrvReqBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="bizHeader" type="{http://www.newchinalife.com/service/hd}SRVReqHead"/&gt;
 *         &lt;element name="bizBody" type="{http://www.newchinalife.com/service/bd}RegSrvReqBizBody"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvReqBody", propOrder = {
    "bizHeader",
    "bizBody"
})
public class RegSrvReqBody {

    @XmlElement(required = true)
    protected SRVReqHead bizHeader;
    @XmlElement(required = true)
    protected RegSrvReqBizBody bizBody;

    /**
     * ��ȡbizHeader���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link SRVReqHead }
     *     
     */
    public SRVReqHead getBizHeader() {
        return bizHeader;
    }

    /**
     * ����bizHeader���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link SRVReqHead }
     *     
     */
    public void setBizHeader(SRVReqHead value) {
        this.bizHeader = value;
    }

    /**
     * ��ȡbizBody���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link RegSrvReqBizBody }
     *     
     */
    public RegSrvReqBizBody getBizBody() {
        return bizBody;
    }

    /**
     * ����bizBody���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link RegSrvReqBizBody }
     *     
     */
    public void setBizBody(RegSrvReqBizBody value) {
        this.bizBody = value;
    }

}
