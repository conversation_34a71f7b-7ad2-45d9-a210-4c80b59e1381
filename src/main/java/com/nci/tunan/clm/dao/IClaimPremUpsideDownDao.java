package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimPremUpsideDownPO;


/** 
 * @description IClaimPremUpsideDownDao接口
 * <AUTHOR> 
 * @date 2022-07-25 10:22:22  
 */
 public interface IClaimPremUpsideDownDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return ClaimPremUpsideDownPO 添加结果
     */
	 public ClaimPremUpsideDownPO addClaimPremUpsideDown(ClaimPremUpsideDownPO claimPremUpsideDownPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPremUpsideDown(ClaimPremUpsideDownPO claimPremUpsideDownPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return ClaimPremUpsideDownPO 修改结果
     */
	 public ClaimPremUpsideDownPO updateClaimPremUpsideDown(ClaimPremUpsideDownPO claimPremUpsideDownPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return ClaimPremUpsideDownPO 查询结果对象
     */
	 public ClaimPremUpsideDownPO findClaimPremUpsideDown(ClaimPremUpsideDownPO claimPremUpsideDownPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return List<ClaimPremUpsideDownPO> 查询结果List
     */
	 public List<ClaimPremUpsideDownPO> findAllClaimPremUpsideDown(ClaimPremUpsideDownPO claimPremUpsideDownPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimPremUpsideDownTotal(ClaimPremUpsideDownPO claimPremUpsideDownPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPremUpsideDownPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPremUpsideDownPO> queryClaimPremUpsideDownForPage(ClaimPremUpsideDownPO claimPremUpsideDownPO, CurrentPage<ClaimPremUpsideDownPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPremUpsideDown(List<ClaimPremUpsideDownPO> claimPremUpsideDownPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPremUpsideDown(List<ClaimPremUpsideDownPO> claimPremUpsideDownPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPremUpsideDown(List<ClaimPremUpsideDownPO> claimPremUpsideDownPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPremUpsideDownPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPremUpsideDown(ClaimPremUpsideDownPO claimPremUpsideDownPO);
	 
 }
 