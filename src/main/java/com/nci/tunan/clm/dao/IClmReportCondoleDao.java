package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
/**
 * 
 * @description  报案
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:56:17
 */
public interface IClmReportCondoleDao {
	/**
	 *  根据caseID查询
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO 赔案信息
	 * @return
	 */
	public ClaimCasePO queryCaseId(ClaimCasePO claimCasePO);
	/**
	 * 
	 * @description 修改数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO 赔案信息
	 * @return
	 */
	public ClaimCasePO updateComfortFlag(ClaimCasePO claimCasePO);
}
