package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCheckRoleListPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimInspectTaskPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPolicyPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimQcNationalstandardsamPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskInspectProPO;
import com.nci.tunan.clm.interfaces.model.po.ContractAgentPO;
import com.nci.tunan.clm.interfaces.model.po.OrgPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimInspectTaskDao接口
 * <AUTHOR> 
 * @date 2023-02-07 14:13:25  
 */
 public interface IClaimInspectTaskDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return ClaimInspectTaskPO 添加结果
     */
	 public ClaimInspectTaskPO addClaimInspectTask(ClaimInspectTaskPO claimInspectTaskPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInspectTask(ClaimInspectTaskPO claimInspectTaskPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return ClaimInspectTaskPO 修改结果
     */
	 public ClaimInspectTaskPO updateClaimInspectTask(ClaimInspectTaskPO claimInspectTaskPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return ClaimInspectTaskPO 查询结果对象
     */
	 public ClaimInspectTaskPO findClaimInspectTask(ClaimInspectTaskPO claimInspectTaskPO);
	 /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return ClaimInspectTaskPO 查询结果对象
     */
	 public ClaimInspectTaskPO findClaimInspectTaskByTaskCode(ClaimInspectTaskPO claimInspectTaskPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return List<ClaimInspectTaskPO> 查询结果List
     */
	 public List<ClaimInspectTaskPO> findAllClaimInspectTask(ClaimInspectTaskPO claimInspectTaskPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInspectTaskTotal(ClaimInspectTaskPO claimInspectTaskPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInspectTaskPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInspectTaskPO> queryClaimInspectTaskForPage(ClaimInspectTaskPO claimInspectTaskPO, CurrentPage<ClaimInspectTaskPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInspectTask(List<ClaimInspectTaskPO> claimInspectTaskPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInspectTask(List<ClaimInspectTaskPO> claimInspectTaskPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInspectTask(List<ClaimInspectTaskPO> claimInspectTaskPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInspectTask(ClaimInspectTaskPO claimInspectTaskPO);

	 /**
     * @description 查询所有一二三级机构信息（机构选择页面）
     * @version
     * @title
     * <AUTHOR>
     * @param orgPO 对象
     * @return List<OrgPO> 查询结果List
     */
	public List<OrgPO> findAllinspectOrg(OrgPO orgPO);

	/**
     * @description 根据机构id查询机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param orgPO 对象
     * @return OrgPO 查询结果对象
     */
	public OrgPO findOrgByOrganId(OrgPO orgPO);

	//查询赔案：根据抽检要素
	public List<ClaimCasePO> findClaimCaseByCheckItem(ClaimCasePO claimCasePO);

	//通过赔案号查询相应的抽检任务要点id
	public List<ClaimInspectTaskPO> findPointIdByCaseNo(ClaimInspectTaskPO claimInspectTaskPO);

	public List<ClaimPolicyPO> findAllClaimPolicy(ClaimPolicyPO claimPolicyPO);

	public List<ContractAgentPO> findAgentName(ContractAgentPO contractAgentPO);

	public ClaimQcNationalstandardsamPO findSampleNumberByBatch(ClaimQcNationalstandardsamPO claimQcNationalstandardsamPO);

	public List<ClaimRiskInspectProPO> findRiskInspectProByCaseNo(ClaimRiskInspectProPO claimRiskInspectProPO);

	public List<ClaimRiskInspectProPO> findRiskInspectProByTaskPointId(ClaimRiskInspectProPO claimRiskInspectProPO);

//	public List<ClaimCasePO> findClaimCaseByClaimPolicy(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByMatchResult(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByLiabCalc(ClaimCasePO claimCasePO);

	public ClaimInspectTaskPO queryIsRelClmByTaskId(ClaimInspectTaskPO claimInspectTaskPO);
	
//	public List<ClaimCasePO> findClaimCaseByClaimMemo(ClaimCasePO claimCasePO);
	
//	public List<ClaimCasePO> findClaimCaseByClaimBackApply(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimBackAudit(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimRiskLevelLiab(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimRiskDoubtfulInfo(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimRiskDoubtfulLog(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByRiskGrade(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByContractBusiProd(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimPolicyUwPolicy(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimBusiProd(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimPolicyS(ClaimCasePO claimCasePO);

//	public List<ClaimCasePO> findClaimCaseByClaimClaimPolicyAdjustType(ClaimCasePO claimCasePO);
	/**
     * @description 抽取检查任务
     * @version
     * @title
     * <AUTHOR>
     * @param orgPO 对象
     * @return OrgPO 查询结果对象
     */
	public List<ClaimCasePO> findClaimCaseDrawInspectTask(ClaimCasePO claimCasePO);
	/**
     * @description 抽取页面分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskInspectProPO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimInspectTaskPO> queryClaimDrawInspectResPage(ClaimInspectTaskPO claimInspectTaskPO, CurrentPage<ClaimInspectTaskPO> currentPage);
	/**
     * @description 查询抽取数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return List<ClaimInspectTaskPO> 查询结果List
     */
	 public List<ClaimInspectTaskPO> findAllClaimDrawInspectRes(ClaimInspectTaskPO claimInspectTaskPO);

 
	 /**
     * @description 根据赔案号查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectTaskPO 对象
     * @return ClaimInspectTaskPO 查询结果对象
     */
	 public ClaimInspectTaskPO findClaimInspectTaskByCaseNo(ClaimInspectTaskPO claimInspectTaskPO);
	 


	 /**
     * @description 抽取检查任务分页查询赔案
     * @version
     * @title
     * <AUTHOR>
     * @param orgPO 对象
     * @return OrgPO 查询结果对象
     */
	 public CurrentPage<ClaimCasePO> findClaimCaseDrawInspectTaskPage(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);

 }
 