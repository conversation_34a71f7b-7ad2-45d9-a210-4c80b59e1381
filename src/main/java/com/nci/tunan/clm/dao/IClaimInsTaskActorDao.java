package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ClaimInsTaskActorPO;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimInsTaskActorDao接口
 * <AUTHOR> 
 * @date 2023-02-07 14:18:56  
 */
 public interface IClaimInsTaskActorDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return ClaimInsTaskActorPO 添加结果
     */
	 public ClaimInsTaskActorPO addClaimInsTaskActor(ClaimInsTaskActorPO claimInsTaskActorPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInsTaskActor(ClaimInsTaskActorPO claimInsTaskActorPO);
	 /**
 	  * @description 根据检查要点删除数据
 	  * @version
 	  * @title
 	  * <AUTHOR>
 	  * @param claimInsTaskActorPO 对象
 	  * @return boolean 删除是否成功
 	  */
 	 public boolean deleteClaimInsTaskActorByPointId(ClaimInsTaskActorPO claimInsTaskActorPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return ClaimInsTaskActorPO 修改结果
     */
	 public ClaimInsTaskActorPO updateClaimInsTaskActor(ClaimInsTaskActorPO claimInsTaskActorPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return ClaimInsTaskActorPO 查询结果对象
     */
	 public ClaimInsTaskActorPO findClaimInsTaskActor(ClaimInsTaskActorPO claimInsTaskActorPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return List<ClaimInsTaskActorPO> 查询结果List
     */
	 public List<ClaimInsTaskActorPO> findAllClaimInsTaskActor(ClaimInsTaskActorPO claimInsTaskActorPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInsTaskActorTotal(ClaimInsTaskActorPO claimInsTaskActorPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInsTaskActorPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInsTaskActorPO> queryClaimInsTaskActorForPage(ClaimInsTaskActorPO claimInsTaskActorPO, CurrentPage<ClaimInsTaskActorPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInsTaskActor(List<ClaimInsTaskActorPO> claimInsTaskActorPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInsTaskActor(List<ClaimInsTaskActorPO> claimInsTaskActorPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInsTaskActor(List<ClaimInsTaskActorPO> claimInsTaskActorPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInsTaskActorPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInsTaskActor(ClaimInsTaskActorPO claimInsTaskActorPO);
	 
 }
 