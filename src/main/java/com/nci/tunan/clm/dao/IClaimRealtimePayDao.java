package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimRealtimePayPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;

/** 
 * @description IClaimRealtimePayDao接口
 * <AUTHOR> 
 * @date 2021-02-09 15:27:35  
 */
 public interface IClaimRealtimePayDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return ClaimRealtimePayPO 添加结果
     */
	 public ClaimRealtimePayPO saveClaimRealtimePay(ClaimRealtimePayPO claimRealtimePayPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRealtimePay(ClaimRealtimePayPO claimRealtimePayPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return ClaimRealtimePayPO 修改结果
     */
	 public ClaimRealtimePayPO updateClaimRealtimePay(ClaimRealtimePayPO claimRealtimePayPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return ClaimRealtimePayPO 查询结果对象
     */
	 public ClaimRealtimePayPO queryOnOrOffByFlowType(ClaimRealtimePayPO claimRealtimePayPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return List<ClaimRealtimePayPO> 查询结果List
     */
	 public List<ClaimRealtimePayPO> queryAllClaimRealtimePayByFlowType(ClaimRealtimePayPO claimRealtimePayPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRealtimePayTotal(ClaimRealtimePayPO claimRealtimePayPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRealtimePayPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRealtimePayPO> queryClaimRealtimePayByFlowType(ClaimRealtimePayPO claimRealtimePayPO, CurrentPage<ClaimRealtimePayPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRealtimePay(List<ClaimRealtimePayPO> claimRealtimePayPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean updateOldClaimRealtimePayLose(List<ClaimRealtimePayPO> claimRealtimePayPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRealtimePay(List<ClaimRealtimePayPO> claimRealtimePayPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRealtimePayPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRealtimePay(ClaimRealtimePayPO claimRealtimePayPO);

 }
 