package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
/**
 * 
 * @description 理赔提醒
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔提醒
 * @date 2015-05-15 下午4:59:58
 */
public interface IClaimRemindJobBatchDao {
	/**
	 * 
	 * @description  获取办理理赔提醒的赔案
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2015-05-15 下午8:49:06 
	 * @param claimCasePO 赔案主表对象
	 * @return  查询结果数量
	 */
	public int findClaimCaseTotalBatch(ClaimCasePO claimCasePO);
	
	/**
	 * 
	 * @description  查询办理理赔提醒的赔案
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2015-05-15 下午8:49:19 
	 * @param claimCasePO  赔案主表对象
	 * @return   赔案主表列表
	 */
	public List<ClaimCasePO> queryRemindForCase(ClaimCasePO claimCasePO);

}
