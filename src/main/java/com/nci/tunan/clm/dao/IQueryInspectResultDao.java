package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimAfcTaskPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 
 * @description 质检结果
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-质检
 * @date 2015-05-15 下午2:10:31
 */
public interface IQueryInspectResultDao {
    /**
     * 
     * @description 质检结果查询接口
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage  当勤业
     * @return 
     */
    public CurrentPage<ClaimAfcTaskPO> queryInspectResult(CurrentPage<ClaimAfcTaskPO> currentPage);
    
    /**
     * 
     * @description 查询符合条件的总件数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcTaskPO  抽检对象
     * @return 个数
     */
    public CurrentPage<ClaimAfcTaskPO> queryTotalNumber(CurrentPage<ClaimAfcTaskPO> currentPage);
    /**
     * 
     * @description 查询符合条件的抽检件数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcTaskPO    抽检对象
     * @return 个数
     */
    public int queryCheckNumber(ClaimAfcTaskPO claimAfcTaskPO);
    /**
     * 
     * @description 查询符合条件的不合格件数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcTaskPO 抽检对象
     * @return 个数
     */
    public int queryUnqualifiedNumber(ClaimAfcTaskPO claimAfcTaskPO);
    /**
     * 
     * @description 查询表示为问题件（柜员问题记录/影像质检不通过）的质检
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcTaskPO 抽检对象
     * @return 
     */
	public CurrentPage<ClaimAfcTaskPO> queryQuestionResult(
			CurrentPage<ClaimAfcTaskPO> current);
}
