package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimBillPaidRegistPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimBillPaidRegistDao接口
 * <AUTHOR> 
 * @date 2022-06-06 16:24:02  
 */
 public interface IClaimBillPaidRegistDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return ClaimBillPaidRegistPO 添加结果
     */
	 public ClaimBillPaidRegistPO addClaimBillPaidRegist(ClaimBillPaidRegistPO claimBillPaidRegistPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBillPaidRegist(ClaimBillPaidRegistPO claimBillPaidRegistPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return ClaimBillPaidRegistPO 修改结果
     */
	 public ClaimBillPaidRegistPO updateClaimBillPaidRegist(ClaimBillPaidRegistPO claimBillPaidRegistPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return ClaimBillPaidRegistPO 查询结果对象
     */
	 public ClaimBillPaidRegistPO findClaimBillPaidRegist(ClaimBillPaidRegistPO claimBillPaidRegistPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return List<ClaimBillPaidRegistPO> 查询结果List
     */
	 public List<ClaimBillPaidRegistPO> findAllClaimBillPaidRegist(ClaimBillPaidRegistPO claimBillPaidRegistPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBillPaidRegistTotal(ClaimBillPaidRegistPO claimBillPaidRegistPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillPaidRegistPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBillPaidRegistPO> queryClaimBillPaidRegistForPage(ClaimBillPaidRegistPO claimBillPaidRegistPO, CurrentPage<ClaimBillPaidRegistPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBillPaidRegist(List<ClaimBillPaidRegistPO> claimBillPaidRegistPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBillPaidRegist(List<ClaimBillPaidRegistPO> claimBillPaidRegistPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBillPaidRegist(List<ClaimBillPaidRegistPO> claimBillPaidRegistPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidRegistPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBillPaidRegist(ClaimBillPaidRegistPO claimBillPaidRegistPO);
	 
 }
 