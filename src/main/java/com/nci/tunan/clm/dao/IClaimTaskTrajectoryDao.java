package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimTaskTrajectoryPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;



/** 
 * @description ClaimTaskTrajectoryDaoImpl理赔任务轨迹接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2019-11-13 19:25:41  
 */
 public interface IClaimTaskTrajectoryDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return ClaimTaskTrajectoryPO 添加结果
     */
	 public ClaimTaskTrajectoryPO addClaimTaskTrajectory(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimTaskTrajectory(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 /**
	  * 
	  * @description 清除指定赔案轨迹信息
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimTaskTrajectoryPO 理赔任务轨迹对象
	  * @return boolean 删除是否成功
	  */
	 public boolean deleteClaimTaskTrajectorySpe(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return ClaimTaskTrajectoryPO 修改结果
     */
	 public ClaimTaskTrajectoryPO updateClaimTaskTrajectory(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return ClaimTaskTrajectoryPO 查询结果对象
     */
	 public ClaimTaskTrajectoryPO findClaimTaskTrajectory(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return List<ClaimTaskTrajectoryPO> 查询结果List
     */
	 public List<ClaimTaskTrajectoryPO> findAllClaimTaskTrajectory(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return int 查询结果条数
     */
	 public int findClaimTaskTrajectoryTotal(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimTaskTrajectoryPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimTaskTrajectoryPO> queryClaimTaskTrajectoryForPage(ClaimTaskTrajectoryPO claimTaskTrajectoryPO, CurrentPage<ClaimTaskTrajectoryPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPOList 理赔任务轨迹对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimTaskTrajectory(List<ClaimTaskTrajectoryPO> claimTaskTrajectoryPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPOList 理赔任务轨迹对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimTaskTrajectory(List<ClaimTaskTrajectoryPO> claimTaskTrajectoryPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPOList 理赔任务轨迹对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimTaskTrajectory(List<ClaimTaskTrajectoryPO> claimTaskTrajectoryPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaskTrajectoryPO 理赔任务轨迹对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimTaskTrajectory(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);

	 /**
	  * 
	  * @description 降序查询所有数据
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimTaskTrajectoryPO 理赔任务轨迹
	  */
	 public List<ClaimTaskTrajectoryPO> queryClaimTaskTrajectoryByCaseNo(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);

	 /**
	  * 
	  * @description 升序查询所有数据
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimTaskTrajectoryPO 理赔任务轨迹
	  */
	 public List<ClaimTaskTrajectoryPO> queryClaimTaskTrajectoryByCaseNoAsc(ClaimTaskTrajectoryPO claimTaskTrajectoryPO);
	 
	 
 }
 