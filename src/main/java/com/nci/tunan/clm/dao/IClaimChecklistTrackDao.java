package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimChecklistTrackPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimChecklistTrackDao接口
 * <AUTHOR> 
 * @date 2025-07-25 17:35:27  
 */
 public interface IClaimChecklistTrackDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return ClaimChecklistTrackPO 添加结果
     */
	 public ClaimChecklistTrackPO addClaimChecklistTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimChecklistTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return ClaimChecklistTrackPO 修改结果
     */
	 public ClaimChecklistTrackPO updateClaimChecklistTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return ClaimChecklistTrackPO 查询结果对象
     */
	 public ClaimChecklistTrackPO findClaimChecklistTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return List<ClaimChecklistTrackPO> 查询结果List
     */
	 public List<ClaimChecklistTrackPO> findAllClaimChecklistTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimChecklistTrackTotal(ClaimChecklistTrackPO claimChecklistTrackPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimChecklistTrackPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimChecklistTrackPO> queryClaimChecklistTrackForPage(ClaimChecklistTrackPO claimChecklistTrackPO, CurrentPage<ClaimChecklistTrackPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimChecklistTrack(List<ClaimChecklistTrackPO> claimChecklistTrackPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimChecklistTrack(List<ClaimChecklistTrackPO> claimChecklistTrackPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimChecklistTrack(List<ClaimChecklistTrackPO> claimChecklistTrackPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimChecklistTrackPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimChecklistTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
	 /**
	 * 查询单证轨迹数据
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimChecklistTrackPO
	 * @return 
	 */
	public ClaimChecklistTrackPO findClaimChecklistTrackByCaseIdAndIsNewTrack(ClaimChecklistTrackPO claimChecklistTrackPO);
	 
 }
 