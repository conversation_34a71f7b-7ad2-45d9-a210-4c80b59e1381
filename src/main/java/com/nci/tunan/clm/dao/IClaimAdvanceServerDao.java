package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimAdvanceServerPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimAdvanceServerDao接口
 * <AUTHOR> 
 * @date 2025-06-06 10:56:15  
 */
 public interface IClaimAdvanceServerDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return ClaimAdvanceServerPO 添加结果
     */
	 public ClaimAdvanceServerPO addClaimAdvanceServer(ClaimAdvanceServerPO claimAdvanceServerPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAdvanceServer(ClaimAdvanceServerPO claimAdvanceServerPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return ClaimAdvanceServerPO 修改结果
     */
	 public ClaimAdvanceServerPO updateClaimAdvanceServer(ClaimAdvanceServerPO claimAdvanceServerPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return ClaimAdvanceServerPO 查询结果对象
     */
	 public ClaimAdvanceServerPO findClaimAdvanceServer(ClaimAdvanceServerPO claimAdvanceServerPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return List<ClaimAdvanceServerPO> 查询结果List
     */
	 public List<ClaimAdvanceServerPO> findAllClaimAdvanceServer(ClaimAdvanceServerPO claimAdvanceServerPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimAdvanceServerTotal(ClaimAdvanceServerPO claimAdvanceServerPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAdvanceServerPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAdvanceServerPO> queryClaimAdvanceServerForPage(ClaimAdvanceServerPO claimAdvanceServerPO, CurrentPage<ClaimAdvanceServerPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAdvanceServer(List<ClaimAdvanceServerPO> claimAdvanceServerPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAdvanceServer(List<ClaimAdvanceServerPO> claimAdvanceServerPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAdvanceServer(List<ClaimAdvanceServerPO> claimAdvanceServerPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceServerPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAdvanceServer(ClaimAdvanceServerPO claimAdvanceServerPO);

	 /**
	  * 
	 	 * 
	 	 * @description 判断是否重复
	 	 * @version V1.0.0
	 	 * @title
	      * @date 2025年8月4日上午9:50:05
	  */
	public List<ClaimAdvanceServerPO> findAllClaimAdvanceServerByData(
			ClaimAdvanceServerPO claimAdvanceServerPO);
	 
 }
 