package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
/**
 * 
 * @description  单证确认
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-单证确认
 * @date 2015-05-15 下午2:30:47
 */
public interface ICheckConfirmationDao {

    /**
     * 
     * @description 根据caseID查询出险原因，理赔类型，治疗情况
     * @version
     * @title
     * @<NAME_EMAIL> 
     * @param map  赔案子表
     * @return 集合
     */
    public List<Map<String,Object>> findClaimSubCaseByCaseId(ClaimSubCasePO claimSubCasePO);
    /**
     * 
     * @description   查询出险人生日
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-05-15 下午2:28:16 
     * @param customerPO   客户对象
     * @return 客户
     */
	public CustomerPO finInsuredBirthday(CustomerPO customerPO);
}
