package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimPayRelationPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
 

import java.util.List;
 

/**
 * 
 * @description 理赔扣减关系表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔扣减关系
 * @date 2015-05-15 下午4:53:56
 */
 public interface IClaimPayRelationDao {
	 /**
     * @description 增加理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return ClaimPayRelationPO 添加结果
     */
	 public ClaimPayRelationPO addClaimPayRelation(ClaimPayRelationPO claimPayRelationPO);
	 
     /**
     * @description 删除理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPayRelation(ClaimPayRelationPO claimPayRelationPO);
	 
     /**
     * @description 修改理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return ClaimPayRelationPO 修改结果
     */
	 public ClaimPayRelationPO updateClaimPayRelation(ClaimPayRelationPO claimPayRelationPO);
	 
     /**
     * @description 查询单条理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return ClaimPayRelationPO 查询结果对象
     */
	 public ClaimPayRelationPO findClaimPayRelation(ClaimPayRelationPO claimPayRelationPO);
	 
     /**
     * @description 查询所有理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return List<ClaimPayRelationPO> 查询结果List
     */
	 public List<ClaimPayRelationPO> findAllClaimPayRelation(ClaimPayRelationPO claimPayRelationPO);
	 
     /**
     * @description 查询理赔扣减关系数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return int 查询结果条数
     */
	 public int findClaimPayRelationTotal(ClaimPayRelationPO claimPayRelationPO);

     /**
     * @description 分页查询理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPayRelationPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPayRelationPO> queryClaimPayRelationForPage(ClaimPayRelationPO claimPayRelationPO, CurrentPage<ClaimPayRelationPO> currentPage);

     /**
     * @description 批量增加理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPOList 理赔扣减关系对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPayRelation(List<ClaimPayRelationPO> claimPayRelationPOList);

     /**
     * @description 批量修改理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPOList 理赔扣减关系对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPayRelation(List<ClaimPayRelationPO> claimPayRelationPOList);

     /**
     * @description 批量删除理赔扣减关系数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPOList 理赔扣减关系对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPayRelation(List<ClaimPayRelationPO> claimPayRelationPOList);

     /**
     * @description 查询所有理赔扣减关系数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayRelationPO 理赔扣减关系对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPayRelation(ClaimPayRelationPO claimPayRelationPO);
	 
	 /**
	     * @description 根据险种ID查询与其相关的扣减险种
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimPayRelationPO 理赔扣减关系对象
	     * @return List<ClaimPayRelationPO> 查询结果List
	     */
	 public List<ClaimPayRelationPO> queryDeductionBusiPrd(ClaimPayRelationPO claimPayRelationPO);
	 
 }
 