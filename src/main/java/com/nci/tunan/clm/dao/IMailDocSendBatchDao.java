package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.MailDocSendBatchPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IMailDocSendBatchDao接口
 * <AUTHOR> 
 * @date 2024-10-30 10:43:10  
 */
 public interface IMailDocSendBatchDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return MailDocSendBatchPO 添加结果
     */
	 public MailDocSendBatchPO addMailDocSendBatch(MailDocSendBatchPO mailDocSendBatchPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteMailDocSendBatch(MailDocSendBatchPO mailDocSendBatchPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return MailDocSendBatchPO 修改结果
     */
	 public MailDocSendBatchPO updateMailDocSendBatch(MailDocSendBatchPO mailDocSendBatchPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return MailDocSendBatchPO 查询结果对象
     */
	 public MailDocSendBatchPO findMailDocSendBatch(MailDocSendBatchPO mailDocSendBatchPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return List<MailDocSendBatchPO> 查询结果List
     */
	 public List<MailDocSendBatchPO> findAllMailDocSendBatch(MailDocSendBatchPO mailDocSendBatchPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return int 查询结果条数
     */
	 public int findMailDocSendBatchTotal(MailDocSendBatchPO mailDocSendBatchPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<MailDocSendBatchPO> 查询结果的当前页对象
     */
	 public CurrentPage<MailDocSendBatchPO> queryMailDocSendBatchForPage(MailDocSendBatchPO mailDocSendBatchPO, CurrentPage<MailDocSendBatchPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveMailDocSendBatch(List<MailDocSendBatchPO> mailDocSendBatchPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateMailDocSendBatch(List<MailDocSendBatchPO> mailDocSendBatchPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteMailDocSendBatch(List<MailDocSendBatchPO> mailDocSendBatchPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param mailDocSendBatchPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapMailDocSendBatch(MailDocSendBatchPO mailDocSendBatchPO);
	 
	 /**
     * @description 删除回退案件的数据
     * @version
     * @title
     * <AUTHOR>
     * @param MailDocSendBatchPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteMailDocSendBatchByIsBack(MailDocSendBatchPO mailDocSendBatchPO);
	 
	 /**
	 * @description	查询符合条件的数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param mailDocSendBatchPO
	 * @return 
	 */
	 public List<MailDocSendBatchPO> queryMailDocCase(MailDocSendBatchPO mailDocSendBatchPO);
	 
	 /**
	 * @description	查询符合条件的数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param mailDocSendBatchPO
	 * @return 
	 */
	 public List<MailDocSendBatchPO> querySendMailCase(MailDocSendBatchPO mailDocSendBatchPO);
 
	 /**
	 * @description	查询符合条件的数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param mailDocSendBatchPO
	 * @return 
	 */
	 public List<MailDocSendBatchPO> findMailDocSendBatchByCaseId(MailDocSendBatchPO mailDocSendBatchPO);
 }
 