package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimBfSurveyRamntPO;

/**
 * 
 * @description IClaimBfSurveyRamntDao前置调查累计风险保额接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:16:01
 */
 public interface IClaimBfSurveyRamntDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return ClaimBfSurveyRamntPO 添加结果
     */
	 public ClaimBfSurveyRamntPO addClaimBfSurveyRamnt(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBfSurveyRamnt(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return ClaimBfSurveyRamntPO 修改结果
     */
	 public ClaimBfSurveyRamntPO updateClaimBfSurveyRamnt(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return ClaimBfSurveyRamntPO 查询结果对象
     */
	 public ClaimBfSurveyRamntPO findClaimBfSurveyRamnt(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return List<ClaimBfSurveyRamntPO> 查询结果List
     */
	 public List<ClaimBfSurveyRamntPO> findAllClaimBfSurveyRamnt(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return int 查询结果条数
     */
	 public int findClaimBfSurveyRamntTotal(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBfSurveyRamntPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBfSurveyRamntPO> queryClaimBfSurveyRamntForPage(ClaimBfSurveyRamntPO claimBfSurveyRamntPO, CurrentPage<ClaimBfSurveyRamntPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPOList 前置调查累计风险保额对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBfSurveyRamnt(List<ClaimBfSurveyRamntPO> claimBfSurveyRamntPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPOList 前置调查累计风险保额对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBfSurveyRamnt(List<ClaimBfSurveyRamntPO> claimBfSurveyRamntPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPOList 前置调查累计风险保额对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBfSurveyRamnt(List<ClaimBfSurveyRamntPO> claimBfSurveyRamntPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBfSurveyRamnt(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 /**
	     * @description 根据PlanId查询所有调查计划
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimBfSurveyRamntPO 前置调查累计风险保额对象
	     * @return List<ClaimBfSurveyRamntPO> 查询结果List
	     */
	 public List<ClaimBfSurveyRamntPO> findClaimBfSurveyRamntByPlanId(ClaimBfSurveyRamntPO claimBfSurveyRamntPO);
	 
 }
 