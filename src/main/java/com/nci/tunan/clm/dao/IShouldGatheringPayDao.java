package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.PremArapPO;
import com.nci.udmp.framework.exception.app.BizException;

/**
 * 
 * @description  ShouldGatheringPayDaoImpl接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:01:27
 */
public interface IShouldGatheringPayDao {
    
    /**
     * 查询当前CaseNo下所有应收应付记录，根据险种和客户号去重
     * @description
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param premArapPO  应收应付对象
     * @return 应收应付对象集合
     * @throws BizException
     */
    public List<PremArapPO> queryArapDeleteRepetitive(PremArapPO premArapPO) throws BizException;
    
    /**
     * 根据caseno、险种、客户号查询应收应付记录
     * @description
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param premArapPO 应收应付对象
     * @return 应收应付对象集合
     * @throws BizException
     */
    public List<PremArapPO> queryArapByBusiprodCodeAndCustomerId(PremArapPO premArapPO) throws BizException;
    

}
