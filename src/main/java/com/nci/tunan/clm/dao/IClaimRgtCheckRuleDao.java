package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimRgtCheckRulePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description 理赔立案复核规则定义表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔立案复核
 * @date 2015-09-15 14:47:07  
 */
 public interface IClaimRgtCheckRuleDao {
	 /**
     * @description 增加理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return ClaimRgtCheckRulePO 添加结果
     */
	 public ClaimRgtCheckRulePO addClaimRgtCheckRule(ClaimRgtCheckRulePO claimRgtCheckRulePO);
	 
     /**
     * @description 删除理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRgtCheckRule(ClaimRgtCheckRulePO claimRgtCheckRulePO);
	 
     /**
     * @description 修改理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return ClaimRgtCheckRulePO 修改结果
     */
	 public ClaimRgtCheckRulePO updateClaimRgtCheckRule(ClaimRgtCheckRulePO claimRgtCheckRulePO);
	 
     /**
     * @description 查询单条理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return ClaimRgtCheckRulePO 查询结果对象
     */
	 public ClaimRgtCheckRulePO findClaimRgtCheckRule(ClaimRgtCheckRulePO claimRgtCheckRulePO);
	 
     /**
     * @description 查询所有理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return List<ClaimRgtCheckRulePO> 查询结果List
     */
	 public List<ClaimRgtCheckRulePO> findAllClaimRgtCheckRule(ClaimRgtCheckRulePO claimRgtCheckRulePO);
	 
     /**
     * @description 查询理赔立案复核数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return int 查询结果条数
     */
	 public int findClaimRgtCheckRuleTotal(ClaimRgtCheckRulePO claimRgtCheckRulePO);

     /**
     * @description 分页查询理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRgtCheckRulePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRgtCheckRulePO> queryClaimRgtCheckRuleForPage(ClaimRgtCheckRulePO claimRgtCheckRulePO, CurrentPage<ClaimRgtCheckRulePO> currentPage);

     /**
     * @description 批量增加理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePOList 理赔立案复核规则对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRgtCheckRule(List<ClaimRgtCheckRulePO> claimRgtCheckRulePOList);

     /**
     * @description 批量修改理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePOList 理赔立案复核规则对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRgtCheckRule(List<ClaimRgtCheckRulePO> claimRgtCheckRulePOList);

     /**
     * @description 批量删除理赔立案复核数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePOList 理赔立案复核规则对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRgtCheckRule(List<ClaimRgtCheckRulePO> claimRgtCheckRulePOList);

     /**
     * @description 查询所有理赔立案复核数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRgtCheckRulePO 理赔立案复核规则对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRgtCheckRule(ClaimRgtCheckRulePO claimRgtCheckRulePO);
	 
 }
 