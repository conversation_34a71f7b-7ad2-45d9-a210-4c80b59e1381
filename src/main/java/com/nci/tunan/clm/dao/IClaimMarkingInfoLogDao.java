package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimMarkingInfoLogPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimMarkingInfoLogDao接口
 * <AUTHOR> 
 * @date 2024-11-29 09:49:50  
 */
 public interface IClaimMarkingInfoLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return ClaimMarkingInfoLogPO 添加结果
     */
	 public ClaimMarkingInfoLogPO addClaimMarkingInfoLog(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimMarkingInfoLog(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return ClaimMarkingInfoLogPO 修改结果
     */
	 public ClaimMarkingInfoLogPO updateClaimMarkingInfoLog(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return ClaimMarkingInfoLogPO 查询结果对象
     */
	 public ClaimMarkingInfoLogPO findClaimMarkingInfoLog(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return List<ClaimMarkingInfoLogPO> 查询结果List
     */
	 public List<ClaimMarkingInfoLogPO> findAllClaimMarkingInfoLog(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimMarkingInfoLogTotal(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimMarkingInfoLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimMarkingInfoLogPO> queryClaimMarkingInfoLogForPage(ClaimMarkingInfoLogPO claimMarkingInfoLogPO, CurrentPage<ClaimMarkingInfoLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimMarkingInfoLog(List<ClaimMarkingInfoLogPO> claimMarkingInfoLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimMarkingInfoLog(List<ClaimMarkingInfoLogPO> claimMarkingInfoLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimMarkingInfoLog(List<ClaimMarkingInfoLogPO> claimMarkingInfoLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimMarkingInfoLog(ClaimMarkingInfoLogPO claimMarkingInfoLogPO);
	 
 }
 