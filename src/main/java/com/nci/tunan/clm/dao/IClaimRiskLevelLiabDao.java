package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimRiskLevelLiabBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskDoubtfulInfoPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLevelLiabPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimRiskLevelLiabDao保项风险接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2019-03-18 16:49:59  
 */
 public interface IClaimRiskLevelLiabDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return ClaimRiskLevelLiabPO 添加结果
     */
	 public ClaimRiskLevelLiabPO addClaimRiskLevelLiab(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskLevelLiab(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return ClaimRiskLevelLiabPO 修改结果
     */
	 public ClaimRiskLevelLiabPO updateClaimRiskLevelLiab(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return ClaimRiskLevelLiabPO 查询结果对象
     */
	 public ClaimRiskLevelLiabPO findClaimRiskLevelLiab(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return List<ClaimRiskLevelLiabPO> 查询结果List
     */
	 public List<ClaimRiskLevelLiabPO> findAllClaimRiskLevelLiab(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskLevelLiabTotal(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskLevelLiabPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskLevelLiabPO> queryClaimRiskLevelLiabForPage(ClaimRiskLevelLiabPO claimRiskLevelLiabPO, CurrentPage<ClaimRiskLevelLiabPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPOList 保项风险对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskLevelLiab(List<ClaimRiskLevelLiabPO> claimRiskLevelLiabPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPOList 保项风险对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskLevelLiab(List<ClaimRiskLevelLiabPO> claimRiskLevelLiabPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPOList 保项风险对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskLevelLiab(List<ClaimRiskLevelLiabPO> claimRiskLevelLiabPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLevelLiabPO 保项风险对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskLevelLiab(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 /**
	  * 查询所有
	  * @description
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param claimCasePO 赔案信息
	  * @param currentPage 当前页
	  * @return
	  */
	 public CurrentPage<ClaimCasePO> findAllCaseRiskList(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);
	 /**
		 * 
		 * @description 查询所有保项层风险
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimRiskLevelLiabPO 保项风险对象
		 * @param currentPage 当前页
		 * @return
		 */
	 public CurrentPage<ClaimRiskLevelLiabPO> findAllLiabRiskList(ClaimRiskLevelLiabPO claimRiskLevelLiabPO,CurrentPage<ClaimRiskLevelLiabPO> currentPage);
	 /**
		 * 
		 * @description 查询疑点明细 逆选择 虚假发票
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimRiskDoubtfulInfoPO 疑点明细
		 * @param currentPage 疑点明细
		 * @return
		 */
	 public CurrentPage<ClaimRiskDoubtfulInfoPO> findAllDoubtRiskOneList(ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO,CurrentPage<ClaimRiskDoubtfulInfoPO> currentPage);
	 
	 /**
		 * 
		 * @description 查询疑点明细 客户层风险   代理人
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimRiskDoubtfulInfoPO 疑点明细
		 * @param currentPage 疑点明细
		 * @return
		 */
	 public CurrentPage<ClaimRiskDoubtfulInfoPO> findAllDoubtRiskTwoList(ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO,CurrentPage<ClaimRiskDoubtfulInfoPO> currentPage);
	 
	 /**
		 * 查询案件风险
		 * @description
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimCasePO 赔案信息
		 * @return
		 */
	 public List<Map<String, Object>> findAllCaseRiskInfo(ClaimCasePO claimCasePO);
	 /**
		 * 
		 * @description 保项风险等级
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimRiskLevelLiabPO 保项风险等级
		 * @return 保项风险等级集合
		 */
	 public List<ClaimRiskLevelLiabPO> findLiabRisk(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
	 /**
		 * 
		 * @description 疑点提示
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param claimRiskDoubtfulInfoPO 疑点提示
		 * @return
		 */
	 public List<ClaimRiskDoubtfulInfoPO> findRiskDoubtful(ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO);
	 public List<ClaimRiskDoubtfulInfoPO> findRiskDoubtfulCode(ClaimRiskDoubtfulInfoPO claimRiskDoubtfulInfoPO);
	 /**
		 * 查询所有疑点提示
		 * @description
		 * @version V1.0.0
		 * @title
		 * <AUTHOR>
		 * @param doubtfulAndLiabPO 疑点提示
		 * @return  疑点提示集合
		 */
	 public List<ClaimRiskLevelLiabPO> findDoubtfulAndLiab(ClaimRiskLevelLiabPO doubtfulAndLiabPO);
 }
 