package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.DataSwitchSettingsPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IDataSwitchSettingsDao接口
 * <AUTHOR> 
 * @date 2020-12-10 11:41:05  
 */
 public interface IDataSwitchSettingsDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return DataSwitchSettingsPO 添加结果
     */
	 public DataSwitchSettingsPO addDataSwitchSettings(DataSwitchSettingsPO dataSwitchSettingsPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteDataSwitchSettings(DataSwitchSettingsPO dataSwitchSettingsPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return DataSwitchSettingsPO 修改结果
     */
	 public DataSwitchSettingsPO updateDataSwitchSettings(DataSwitchSettingsPO dataSwitchSettingsPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return DataSwitchSettingsPO 查询结果对象
     */
	 public DataSwitchSettingsPO findDataSwitchSettings(DataSwitchSettingsPO dataSwitchSettingsPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return List<DataSwitchSettingsPO> 查询结果List
     */
	 public List<DataSwitchSettingsPO> findAllDataSwitchSettings(DataSwitchSettingsPO dataSwitchSettingsPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return int 查询结果条数
     */
	 public int findDataSwitchSettingsTotal(DataSwitchSettingsPO dataSwitchSettingsPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<DataSwitchSettingsPO> 查询结果的当前页对象
     */
	 public CurrentPage<DataSwitchSettingsPO> queryDataSwitchSettingsForPage(DataSwitchSettingsPO dataSwitchSettingsPO, CurrentPage<DataSwitchSettingsPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveDataSwitchSettings(List<DataSwitchSettingsPO> dataSwitchSettingsPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateDataSwitchSettings(List<DataSwitchSettingsPO> dataSwitchSettingsPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteDataSwitchSettings(List<DataSwitchSettingsPO> dataSwitchSettingsPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapDataSwitchSettings(DataSwitchSettingsPO dataSwitchSettingsPO);
	 
	 /**
	 * @description 查询当前有效设置信息
	 * @version
     * @title
     * <AUTHOR>
     * @param dataSwitchSettingsPO 对象
     * @return DataSwitchSettingsPO 查询结果对象
     */
	 public DataSwitchSettingsPO findDataSwitchSettingsIsValid(DataSwitchSettingsPO dataSwitchSettingsPO);

	 /**
	  * 
	  * @description 按插入时间降序查询所有设置信息 
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param dataSwitchSettingsPO PO对象
	  * @param currentPagePremPO 当前页对象
	  * @return 查询结果的当前页对象
	  */
	 public CurrentPage<DataSwitchSettingsPO> queryDataSwitchSettingsForPageByInsertTimeDesc(DataSwitchSettingsPO dataSwitchSettingsPO,
			CurrentPage<DataSwitchSettingsPO> currentPagePremPO);	 
	 
 }
 