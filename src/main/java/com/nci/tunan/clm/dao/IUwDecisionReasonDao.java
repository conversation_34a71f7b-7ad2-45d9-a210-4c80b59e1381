package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.UwDecisionReasonPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description IUwDecisionReasonDao核保特别约定原因接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-核保特别约定
 * @date 2016-10-26 17:30:32  
 */
 public interface IUwDecisionReasonDao {
	 /**
     * @description 增加核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return UwDecisionReasonPO 添加结果
     */
	 public UwDecisionReasonPO addUwDecisionReason(UwDecisionReasonPO uwDecisionReasonPO);
	 
     /**
     * @description 删除核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteUwDecisionReason(UwDecisionReasonPO uwDecisionReasonPO);
	 
     /**
     * @description 修改核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return UwDecisionReasonPO 修改结果
     */
	 public UwDecisionReasonPO updateUwDecisionReason(UwDecisionReasonPO uwDecisionReasonPO);
	 
     /**
     * @description 查询单条核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return UwDecisionReasonPO 查询结果对象
     */
	 public UwDecisionReasonPO findUwDecisionReason(UwDecisionReasonPO uwDecisionReasonPO);
	 
     /**
     * @description 查询所有核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return List<UwDecisionReasonPO> 查询结果List
     */
	 public List<UwDecisionReasonPO> findAllUwDecisionReason(UwDecisionReasonPO uwDecisionReasonPO);
	 
     /**
     * @description 查询核保特别约定数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return int 查询结果条数
     */
	 public int findUwDecisionReasonTotal(UwDecisionReasonPO uwDecisionReasonPO);

     /**
     * @description 分页查询核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<UwDecisionReasonPO> 查询结果的当前页对象
     */
	 public CurrentPage<UwDecisionReasonPO> queryUwDecisionReasonForPage(UwDecisionReasonPO uwDecisionReasonPO, CurrentPage<UwDecisionReasonPO> currentPage);

     /**
     * @description 批量增加核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPOList 核保特别约定原因对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveUwDecisionReason(List<UwDecisionReasonPO> uwDecisionReasonPOList);

     /**
     * @description 批量修改核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPOList 核保特别约定原因对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateUwDecisionReason(List<UwDecisionReasonPO> uwDecisionReasonPOList);

     /**
     * @description 批量删除核保特别约定数据
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPOList 核保特别约定原因对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteUwDecisionReason(List<UwDecisionReasonPO> uwDecisionReasonPOList);

     /**
     * @description 查询所有核保特别约定数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param uwDecisionReasonPO 核保特别约定原因对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapUwDecisionReason(UwDecisionReasonPO uwDecisionReasonPO);
	 
 }
 