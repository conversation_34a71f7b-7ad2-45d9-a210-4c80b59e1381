package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskBasisListPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimRiskBasisListDao接口
 * <AUTHOR> 
 * @date 2023-02-06 10:42:55  
 */
 public interface IClaimRiskBasisListDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return ClaimRiskBasisListPO 添加结果
     */
	 public ClaimRiskBasisListPO addClaimRiskBasisList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskBasisList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return ClaimRiskBasisListPO 修改结果
     */
	 public ClaimRiskBasisListPO updateClaimRiskBasisList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return ClaimRiskBasisListPO 查询结果对象
     */
	 public ClaimRiskBasisListPO findClaimRiskBasisList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return List<ClaimRiskBasisListPO> 查询结果List
     */
	 public List<ClaimRiskBasisListPO> findAllClaimRiskBasisList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskBasisListTotal(ClaimRiskBasisListPO claimRiskBasisListPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskBasisListPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskBasisListPO> queryClaimRiskBasisListForPage(ClaimRiskBasisListPO claimRiskBasisListPO, CurrentPage<ClaimRiskBasisListPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskBasisList(List<ClaimRiskBasisListPO> claimRiskBasisListPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskBasisList(List<ClaimRiskBasisListPO> claimRiskBasisListPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskBasisList(List<ClaimRiskBasisListPO> claimRiskBasisListPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskBasisList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 /**
     * @description 查询风控依据表数据根据风控依据ID集合
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return List<ClaimRiskBasisListPO> 查询结果List
     */
	 public List<ClaimRiskBasisListPO> findClaimRiskBasisListByBasisIdList(ClaimRiskBasisListPO claimRiskBasisListPO);
	 /**
     * @description 查询所有风控依据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskBasisListPO 对象
     * @return List<ClaimRiskBasisListPO> 查询结果List
     */
	 public List<ClaimRiskBasisListPO> findAllBasisDesc(ClaimRiskBasisListPO claimRiskBasisListPO);
	 
 }
 