package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLibraryPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRoleServicingPO;
import com.nci.udmp.framework.model.CurrentPage;

public interface IClaimRiskWarnQueryDao {

	public List<ClaimRiskLibraryPO> queryClaimRiskWarn(ClaimRiskLibraryPO claimRiskLibraryPO);

	public CurrentPage<ClaimRiskLibraryPO> queryClaimRiskWarnMain(ClaimRiskLibraryPO claimRiskLibraryPO,
			CurrentPage<ClaimRiskLibraryPO> copyCurrentPage);

}
