package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillItemRegistPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimBillItemRegistDao接口
 * <AUTHOR> 
 * @date 2022-06-06 16:24:27  
 */
 public interface IClaimBillItemRegistDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return ClaimBillItemRegistPO 添加结果
     */
	 public ClaimBillItemRegistPO addClaimBillItemRegist(ClaimBillItemRegistPO claimBillItemRegistPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBillItemRegist(ClaimBillItemRegistPO claimBillItemRegistPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return ClaimBillItemRegistPO 修改结果
     */
	 public ClaimBillItemRegistPO updateClaimBillItemRegist(ClaimBillItemRegistPO claimBillItemRegistPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return ClaimBillItemRegistPO 查询结果对象
     */
	 public ClaimBillItemRegistPO findClaimBillItemRegist(ClaimBillItemRegistPO claimBillItemRegistPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return List<ClaimBillItemRegistPO> 查询结果List
     */
	 public List<ClaimBillItemRegistPO> findAllClaimBillItemRegist(ClaimBillItemRegistPO claimBillItemRegistPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBillItemRegistTotal(ClaimBillItemRegistPO claimBillItemRegistPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillItemRegistPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBillItemRegistPO> queryClaimBillItemRegistForPage(ClaimBillItemRegistPO claimBillItemRegistPO, CurrentPage<ClaimBillItemRegistPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBillItemRegist(List<ClaimBillItemRegistPO> claimBillItemRegistPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBillItemRegist(List<ClaimBillItemRegistPO> claimBillItemRegistPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBillItemRegist(List<ClaimBillItemRegistPO> claimBillItemRegistPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemRegistPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBillItemRegist(ClaimBillItemRegistPO claimBillItemRegistPO);
	 
 }
 