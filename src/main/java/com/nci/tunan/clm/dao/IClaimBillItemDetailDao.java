package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimBillItemDetailPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimBillItemDetailDao接口
 * <AUTHOR> 
 * @date 2021-03-23 14:36:32  
 */
 public interface IClaimBillItemDetailDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return ClaimBillItemDetailPO 添加结果
     */
	 public ClaimBillItemDetailPO addClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return ClaimBillItemDetailPO 修改结果
     */
	 public ClaimBillItemDetailPO updateClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return ClaimBillItemDetailPO 查询结果对象
     */
	 public ClaimBillItemDetailPO findClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return List<ClaimBillItemDetailPO> 查询结果List
     */
	 public List<ClaimBillItemDetailPO> findAllClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBillItemDetailTotal(ClaimBillItemDetailPO claimBillItemDetailPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillItemDetailPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBillItemDetailPO> queryClaimBillItemDetailForPage(ClaimBillItemDetailPO claimBillItemDetailPO, CurrentPage<ClaimBillItemDetailPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBillItemDetail(List<ClaimBillItemDetailPO> claimBillItemDetailPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBillItemDetail(List<ClaimBillItemDetailPO> claimBillItemDetailPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBillItemDetail(List<ClaimBillItemDetailPO> claimBillItemDetailPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemDetailPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);

	 /**
	  * 汇总直赔数据接口记录表数据
	  * @param claimBillItemDetailPO
	  * @return
	  */
	public List<ClaimBillItemDetailPO> sumClaimBillItemDetail(ClaimBillItemDetailPO claimBillItemDetailPO);

	/**
	 * 根据赔案ID删除直赔接口数据
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBillItemDetailPO
	 */
	public void deleteClaimBillItemDetailByCaseId(ClaimBillItemDetailPO claimBillItemDetailPO);

	/**
	 * 根据赔案ID和是否查看标识查询明细信息
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param List<ClaimBillItemDetailPO> 查询结果List
	 */
	public List<ClaimBillItemDetailPO> findDetailByCaseIdAndLookFlag(ClaimBillItemDetailPO claimBillItemDetailPO);
	 
 }
 