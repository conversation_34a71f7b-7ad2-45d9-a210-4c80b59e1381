package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimPhoneServicePO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;

/**
 * 
 * @description 服务电话维护表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-服务电话
 * @date 2015-05-15 下午4:54:28
 */
 public interface IClaimPhoneServiceDao {
	 /**
     * @description 增加服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return ClaimPhoneServicePO 添加结果
     */
	 public ClaimPhoneServicePO addClaimPhoneService(ClaimPhoneServicePO claimPhoneServicePO);
	 
     /**
     * @description 删除服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPhoneService(ClaimPhoneServicePO claimPhoneServicePO);
	 
     /**
     * @description 修改服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return ClaimPhoneServicePO 修改结果
     */
	 public ClaimPhoneServicePO updateClaimPhoneService(ClaimPhoneServicePO claimPhoneServicePO);
	 
     /**
     * @description 查询单条服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return ClaimPhoneServicePO 查询结果对象
     */
	 public ClaimPhoneServicePO findClaimPhoneService(ClaimPhoneServicePO claimPhoneServicePO);
	 
     /**
     * @description 查询所有服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return List<ClaimPhoneServicePO> 查询结果List
     */
	 public List<ClaimPhoneServicePO> findAllClaimPhoneService(ClaimPhoneServicePO claimPhoneServicePO);
	 
     /**
     * @description 查询服务电话数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return int 查询结果条数
     */
	 public int findClaimPhoneServiceTotal(ClaimPhoneServicePO claimPhoneServicePO);

     /**
     * @description 分页查询服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPhoneServicePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPhoneServicePO> queryClaimPhoneServiceForPage(ClaimPhoneServicePO claimPhoneServicePO, CurrentPage<ClaimPhoneServicePO> currentPage);

     /**
     * @description 批量增加服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePOList 服务电话对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPhoneService(List<ClaimPhoneServicePO> claimPhoneServicePOList);

     /**
     * @description 批量修改服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePOList 服务电话对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPhoneService(List<ClaimPhoneServicePO> claimPhoneServicePOList);

     /**
     * @description 批量删除服务电话数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePOList 服务电话对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPhoneService(List<ClaimPhoneServicePO> claimPhoneServicePOList);

     /**
     * @description 查询所有服务电话数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPhoneServicePO 服务电话对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPhoneService(ClaimPhoneServicePO claimPhoneServicePO);
	 
 }
 