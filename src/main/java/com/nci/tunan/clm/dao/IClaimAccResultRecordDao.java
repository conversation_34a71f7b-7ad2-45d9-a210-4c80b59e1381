package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimAccResultRecordPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * @description 出险结果记录表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午2:30:51
 */
 public interface IClaimAccResultRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return ClaimAccResultRecordPO 添加结果
     */
	 public ClaimAccResultRecordPO addClaimAccResultRecord(ClaimAccResultRecordPO claimAccResultRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAccResultRecord(ClaimAccResultRecordPO claimAccResultRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return ClaimAccResultRecordPO 修改结果
     */
	 public ClaimAccResultRecordPO updateClaimAccResultRecord(ClaimAccResultRecordPO claimAccResultRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return ClaimAccResultRecordPO 查询结果对象
     */
	 public ClaimAccResultRecordPO findClaimAccResultRecord(ClaimAccResultRecordPO claimAccResultRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return List<ClaimAccResultRecordPO> 查询结果List
     */
	 public List<ClaimAccResultRecordPO> findAllClaimAccResultRecord(ClaimAccResultRecordPO claimAccResultRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return int 查询结果条数
     */
	 public int findClaimAccResultRecordTotal(ClaimAccResultRecordPO claimAccResultRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAccResultRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAccResultRecordPO> queryClaimAccResultRecordForPage(ClaimAccResultRecordPO claimAccResultRecordPO, CurrentPage<ClaimAccResultRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPOList    出现结果记录对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAccResultRecord(List<ClaimAccResultRecordPO> claimAccResultRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPOList    出现结果记录对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAccResultRecord(List<ClaimAccResultRecordPO> claimAccResultRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPOList    出现结果记录对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAccResultRecord(List<ClaimAccResultRecordPO> claimAccResultRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultRecordPO   出险结果记录对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAccResultRecord(ClaimAccResultRecordPO claimAccResultRecordPO);
	 
 }
 