package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimAfcPlanPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimAfcTaskPO;
import com.nci.udmp.app.dao.po.OrganPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 
 * @description 监控质检任务Dao接口
 * <AUTHOR> <EMAIL>
 * @.belongToModule CLM-理赔系统
 * @date 2015-8-31 下午4:34:05
 */
public interface IMonitoringQualityDao {
    /**
     * 
     * @description 查询所有的理赔事后质检信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcPlanPO 监控质检任务参数
     * @return List<ClaimAfcPlanPO> 查询的结果list
     */
    public List<ClaimAfcPlanPO> findAllClaimAfcPlan(ClaimAfcPlanPO claimAfcPlanPO);
    
    /**
     * 
     * @description 通过机构code查询机构
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param organPO 结构参数
     * @return List<OrganPO>  查询结果list
     */
    public List<OrganPO> findOrganNameByCode(OrganPO organPO);
    
    /**
     * 
     * @description 通过机构code查询机构分页
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param organPO 机构参数
     * @return List<OrganPO>  查询结果list
     */
    public CurrentPage<OrganPO> findOrganNameByCodeCurrent(OrganPO organPO, CurrentPage<OrganPO> currentPagePO);
    
    /**
     * 
     * @description 查询符合条件的质检信息的数量
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcTaskPO 监控质检任务参数
     * @return int 查询的结果
     */
    public int findClaimAfcTaskTotal(ClaimAfcTaskPO claimAfcTaskPO);
    /**
     * @description 多条件查询质检的详细信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAfcTaskPO 监控质检任务
     * @param currentPagePO  监控质检任务
     * @return List<ClaimAfcTaskPO> 查询结果list
     */
    public CurrentPage<ClaimAfcTaskPO> findClaimAfcTaskData(ClaimAfcTaskPO claimAfcTaskPO, CurrentPage<ClaimAfcTaskPO> currentPagePO);
}
