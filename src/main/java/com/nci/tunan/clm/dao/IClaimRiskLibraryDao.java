package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLibraryPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;


/** 
 * @description IClaimRiskLibraryDao接口
 * <AUTHOR> 
 * @date 2023-02-03 14:34:45  
 */
 public interface IClaimRiskLibraryDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return ClaimRiskLibraryPO 添加结果
     */
	 public ClaimRiskLibraryPO addClaimRiskLibrary(ClaimRiskLibraryPO claimRiskLibraryPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskLibrary(ClaimRiskLibraryPO claimRiskLibraryPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return ClaimRiskLibraryPO 修改结果
     */
	 public ClaimRiskLibraryPO updateClaimRiskLibrary(ClaimRiskLibraryPO claimRiskLibraryPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return ClaimRiskLibraryPO 查询结果对象
     */
	 public ClaimRiskLibraryPO findClaimRiskLibrary(ClaimRiskLibraryPO claimRiskLibraryPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return List<ClaimRiskLibraryPO> 查询结果List
     */
	 public List<ClaimRiskLibraryPO> findAllClaimRiskLibrary(ClaimRiskLibraryPO claimRiskLibraryPO);
	 /**
	  * @description 定制风控库查询
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimRiskLibraryPO 对象
	  * @return List<ClaimRiskLibraryPO> 查询结果List
	  */
	 public List<ClaimRiskLibraryPO> queryRiskLibraryList(ClaimRiskLibraryPO claimRiskLibraryPO);
	 /**
     * @description 定制风控库查询分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskLibraryPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskLibraryPO> queryRiskLibraryListForPage(ClaimRiskLibraryPO claimRiskLibraryPO, CurrentPage<ClaimRiskLibraryPO> currentPage);
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskLibraryTotal(ClaimRiskLibraryPO claimRiskLibraryPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskLibraryPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskLibraryPO> queryClaimRiskLibraryForPage(ClaimRiskLibraryPO claimRiskLibraryPO, CurrentPage<ClaimRiskLibraryPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskLibrary(List<ClaimRiskLibraryPO> claimRiskLibraryPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskLibrary(List<ClaimRiskLibraryPO> claimRiskLibraryPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskLibrary(List<ClaimRiskLibraryPO> claimRiskLibraryPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskLibrary(ClaimRiskLibraryPO claimRiskLibraryPO);

	 public List<ClaimRiskLibraryPO>  queyErrRate(ClaimRiskLibraryPO claimRiskLibraryPO);

	public List<ClaimRiskLibraryPO> findClaimRiskLibraryByCaseNo(ClaimRiskLibraryPO claimRiskLibraryPO);
		
	public List<ClaimRiskLibraryPO> findClaimRiskLibraryListByCaseNo(ClaimRiskLibraryPO claimRiskLibraryPO);

	/**
	  * @description 定制风控库查询根据制定检查项目
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimRiskLibraryPO 对象
	  * @return List<ClaimRiskLibraryPO> 查询结果List
	  */
	 public List<ClaimRiskLibraryPO> queryRiskLibraryListByInspectProId(ClaimRiskLibraryPO claimRiskLibraryPO);
	 
	 /**
     * @description 根据风险编号查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibraryPO 对象
     * @return ClaimRiskLibraryPO 查询结果对象
     */
	 public ClaimRiskLibraryPO findClaimRiskLibraryByRiskLibCode(ClaimRiskLibraryPO claimRiskLibraryPO);

	public List<ClaimRiskLibraryPO> findRiskLibraryListByInspectProId(
			ClaimRiskLibraryPO claimRiskLibraryPO);
 }
 