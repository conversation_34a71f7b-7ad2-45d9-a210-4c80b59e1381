package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.SurveyChannelUncomPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ISurveyChannelUncomDao固定管理渠道非通用信息接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-调查
 * @date 2015-11-04 16:08:14  
 */
 public interface ISurveyChannelUncomDao {
	 /**
     * @description 增加调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 对象
     * @return SurveyChannelUncomPO 添加结果
     */
	 public SurveyChannelUncomPO addSurveyChannelUncom(SurveyChannelUncomPO surveyChannelUncomPO);
	 
     /**
     * @description 删除调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 固定管理渠道非通用信息对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteSurveyChannelUncom(SurveyChannelUncomPO surveyChannelUncomPO);
	 
     /**
     * @description 修改调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 固定管理渠道非通用信息对象
     * @return SurveyChannelUncomPO 修改结果
     */
	 public SurveyChannelUncomPO updateSurveyChannelUncom(SurveyChannelUncomPO surveyChannelUncomPO);
	 
     /**
     * @description 查询单条调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 固定管理渠道非通用信息对象
     * @return SurveyChannelUncomPO 查询结果对象
     */
	 public SurveyChannelUncomPO findSurveyChannelUncom(SurveyChannelUncomPO surveyChannelUncomPO);
	 
     /**
     * @description 查询所有调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 固定管理渠道非通用信息对象
     * @return List<SurveyChannelUncomPO> 查询结果List
     */
	 public List<SurveyChannelUncomPO> findAllSurveyChannelUncom(SurveyChannelUncomPO surveyChannelUncomPO);
	 
     /**
     * @description 查询调查渠道数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 固定管理渠道非通用信息对象
     * @return int 查询结果条数
     */
	 public int findSurveyChannelUncomTotal(SurveyChannelUncomPO surveyChannelUncomPO);

     /**
     * @description 分页查询调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SurveyChannelUncomPO> 查询结果的当前页对象
     */
	 public CurrentPage<SurveyChannelUncomPO> querySurveyChannelUncomForPage(SurveyChannelUncomPO surveyChannelUncomPO, CurrentPage<SurveyChannelUncomPO> currentPage);

     /**
     * @description 批量增加调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPOList 固定管理渠道非通用信息对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSurveyChannelUncom(List<SurveyChannelUncomPO> surveyChannelUncomPOList);

     /**
     * @description 批量修改调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPOList 固定管理渠道非通用信息对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSurveyChannelUncom(List<SurveyChannelUncomPO> surveyChannelUncomPOList);

     /**
     * @description 批量删除调查渠道数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPOList 固定管理渠道非通用信息对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSurveyChannelUncom(List<SurveyChannelUncomPO> surveyChannelUncomPOList);

     /**
     * @description 查询所有调查渠道数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param surveyChannelUncomPO 固定管理渠道非通用信息对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapSurveyChannelUncom(SurveyChannelUncomPO surveyChannelUncomPO);
	 
 }
 