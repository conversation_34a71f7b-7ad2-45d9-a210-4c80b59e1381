package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.InspectResultTracePO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IInspectResultTraceDao接口
 * <AUTHOR> 
 * @date 2023-02-07 14:17:01  
 */
 public interface IInspectResultTraceDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return InspectResultTracePO 添加结果
     */
	 public InspectResultTracePO addInspectResultTrace(InspectResultTracePO inspectResultTracePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteInspectResultTrace(InspectResultTracePO inspectResultTracePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return InspectResultTracePO 修改结果
     */
	 public InspectResultTracePO updateInspectResultTrace(InspectResultTracePO inspectResultTracePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return InspectResultTracePO 查询结果对象
     */
	 public InspectResultTracePO findInspectResultTrace(InspectResultTracePO inspectResultTracePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return List<InspectResultTracePO> 查询结果List
     */
	 public List<InspectResultTracePO> findAllInspectResultTrace(InspectResultTracePO inspectResultTracePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return int 查询结果条数
     */
	 public int findInspectResultTraceTotal(InspectResultTracePO inspectResultTracePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<InspectResultTracePO> 查询结果的当前页对象
     */
	 public CurrentPage<InspectResultTracePO> queryInspectResultTraceForPage(InspectResultTracePO inspectResultTracePO, CurrentPage<InspectResultTracePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveInspectResultTrace(List<InspectResultTracePO> inspectResultTracePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateInspectResultTrace(List<InspectResultTracePO> inspectResultTracePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteInspectResultTrace(List<InspectResultTracePO> inspectResultTracePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapInspectResultTrace(InspectResultTracePO inspectResultTracePO);

	 /**
     * @description 根据抽检任务要点id查询检查结论轨迹表数据
     * @version
     * @title
     * <AUTHOR>
     * @param inspectResultTracePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<InspectResultTracePO> findInspectResultTraceByPointId(InspectResultTracePO inspectResultTracePO);
	 
 }
 