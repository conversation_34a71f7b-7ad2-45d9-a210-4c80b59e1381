package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimMarkingInfoPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimMarkingInfoDao接口
 * <AUTHOR> 
 * @date 2024-11-29 09:40:52  
 */
 public interface IClaimMarkingInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return ClaimMarkingInfoPO 添加结果
     */
	 public ClaimMarkingInfoPO addClaimMarkingInfo(ClaimMarkingInfoPO claimMarkingInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimMarkingInfo(ClaimMarkingInfoPO claimMarkingInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return ClaimMarkingInfoPO 修改结果
     */
	 public ClaimMarkingInfoPO updateClaimMarkingInfo(ClaimMarkingInfoPO claimMarkingInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return ClaimMarkingInfoPO 查询结果对象
     */
	 public ClaimMarkingInfoPO findClaimMarkingInfo(ClaimMarkingInfoPO claimMarkingInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return List<ClaimMarkingInfoPO> 查询结果List
     */
	 public List<ClaimMarkingInfoPO> findAllClaimMarkingInfo(ClaimMarkingInfoPO claimMarkingInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimMarkingInfoTotal(ClaimMarkingInfoPO claimMarkingInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimMarkingInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimMarkingInfoPO> queryClaimMarkingInfoForPage(ClaimMarkingInfoPO claimMarkingInfoPO, CurrentPage<ClaimMarkingInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimMarkingInfo(List<ClaimMarkingInfoPO> claimMarkingInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimMarkingInfo(List<ClaimMarkingInfoPO> claimMarkingInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimMarkingInfo(List<ClaimMarkingInfoPO> claimMarkingInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimMarkingInfo(ClaimMarkingInfoPO claimMarkingInfoPO);
	 /**
     * @description 睡眠保单清单分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimMarkingInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimMarkingInfoPO> queryClaimMarkingInfoList(ClaimMarkingInfoPO claimMarkingInfoPO, CurrentPage<ClaimMarkingInfoPO> currentPage);
	 /**
     * @description 查询导入数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMarkingInfoPO 对象
     * @return List<ClaimMarkingInfoPO> 查询结果List
     */
	 public List<ClaimMarkingInfoPO> findMarkingInfoList(ClaimMarkingInfoPO claimMarkingInfoPO);
	 /**
	  * @description 超30日批处理执行后汇总数据
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimMarkingInfoPO 对象
	  * @return List<ClaimMarkingInfoPO> 查询结果List
	  */
	 public List<ClaimMarkingInfoPO> findMarkingInfoListByClaimPay(ClaimMarkingInfoPO claimMarkingInfoPO);
	 /**
	  * @description 批量修改处理状态
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimMarkingInfoPOList 对象列表
	  * @return boolean 批量修改是否成功
	  */
	 public boolean batchUpdateClaimMarkingInfoDealStatus(List<ClaimMarkingInfoPO> claimMarkingInfoPOList);
 }
 