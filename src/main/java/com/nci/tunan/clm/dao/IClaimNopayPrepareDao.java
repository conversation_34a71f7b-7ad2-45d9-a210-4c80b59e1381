package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimNopayPreparePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimNopayPrepareDao接口
 * <AUTHOR> 
 * @date 2023-12-18 10:45:49  
 */
 public interface IClaimNopayPrepareDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return ClaimNopayPreparePO 添加结果
     */
	 public ClaimNopayPreparePO addClaimNopayPrepare(ClaimNopayPreparePO claimNopayPreparePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimNopayPrepare(ClaimNopayPreparePO claimNopayPreparePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return ClaimNopayPreparePO 修改结果
     */
	 public ClaimNopayPreparePO updateClaimNopayPrepare(ClaimNopayPreparePO claimNopayPreparePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return ClaimNopayPreparePO 查询结果对象
     */
	 public ClaimNopayPreparePO findClaimNopayPrepare(ClaimNopayPreparePO claimNopayPreparePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return List<ClaimNopayPreparePO> 查询结果List
     */
	 public List<ClaimNopayPreparePO> findAllClaimNopayPrepare(ClaimNopayPreparePO claimNopayPreparePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimNopayPrepareTotal(ClaimNopayPreparePO claimNopayPreparePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimNopayPreparePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimNopayPreparePO> queryClaimNopayPrepareForPage(ClaimNopayPreparePO claimNopayPreparePO, CurrentPage<ClaimNopayPreparePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimNopayPrepare(List<ClaimNopayPreparePO> claimNopayPreparePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimNopayPrepare(List<ClaimNopayPreparePO> claimNopayPreparePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimNopayPrepare(List<ClaimNopayPreparePO> claimNopayPreparePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimNopayPrepare(ClaimNopayPreparePO claimNopayPreparePO);
	 /**
     * @description 未决赔款清单查询
     * @version
     * @title
     * <AUTHOR>
     * @param claimNopayPreparePO 对象
     * @return List<ClaimNopayPreparePO> 查询结果List
	 */
	 public List<ClaimNopayPreparePO> findClaimNopayPrepareList(ClaimNopayPreparePO claimNopayPreparePO);
	 
 }
 