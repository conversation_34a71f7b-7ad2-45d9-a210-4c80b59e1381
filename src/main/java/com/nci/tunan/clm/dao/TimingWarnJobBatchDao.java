package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLibraryPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimWarnPO;

/**
 * 
 * <AUTHOR>
 * @date: 2023年5月22日t
 * @Description:定时风控预警批处理
 */
public interface TimingWarnJobBatchDao {

	List<ClaimRiskLibraryPO> queryTimingWarnJob(ClaimRiskLibraryPO claimRiskLibraryPO);



	List<ClaimWarnPO> findClaimWarnByRiskLibId(ClaimWarnPO claimWarnPOs);

}
