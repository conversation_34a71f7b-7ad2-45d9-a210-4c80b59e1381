package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ClaimProductMessagePO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 产品提示信息配置表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:56:59
 */
 public interface IClaimProductMessageDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return ClaimProductMessagePO 添加结果
     */
	 public ClaimProductMessagePO addClaimProductMessage(ClaimProductMessagePO claimProductMessagePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimProductMessage(ClaimProductMessagePO claimProductMessagePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return ClaimProductMessagePO 修改结果
     */
	 public ClaimProductMessagePO updateClaimProductMessage(ClaimProductMessagePO claimProductMessagePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return ClaimProductMessagePO 查询结果对象
     */
	 public ClaimProductMessagePO findClaimProductMessage(ClaimProductMessagePO claimProductMessagePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return List<ClaimProductMessagePO> 查询结果List
     */
	 public List<ClaimProductMessagePO> findAllClaimProductMessage(ClaimProductMessagePO claimProductMessagePO);
	 
     /**
     * @description 根据险种编码查询特殊产品提示信息配置信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return List<ClaimProductMessagePO> 查询结果List
     */
	 public List<ClaimProductMessagePO> findProductMessageByBusiProdCode(ClaimProductMessagePO claimProductMessagePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return int 查询结果条数
     */
	 public int findClaimProductMessageTotal(ClaimProductMessagePO claimProductMessagePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimProductMessagePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimProductMessagePO> queryClaimProductMessageForPage(ClaimProductMessagePO claimProductMessagePO, CurrentPage<ClaimProductMessagePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePOList 产品提示信息配置对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimProductMessage(List<ClaimProductMessagePO> claimProductMessagePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePOList 产品提示信息配置对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimProductMessage(List<ClaimProductMessagePO> claimProductMessagePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePOList 产品提示信息配置对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimProductMessage(List<ClaimProductMessagePO> claimProductMessagePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimProductMessagePO 产品提示信息配置对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimProductMessage(ClaimProductMessagePO claimProductMessagePO);
	 
 }
 