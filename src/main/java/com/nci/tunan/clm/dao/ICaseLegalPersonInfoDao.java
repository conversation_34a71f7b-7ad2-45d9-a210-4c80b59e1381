package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.CaseLegalPersonInfoPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ICaseLegalPersonInfoDao接口
 * <AUTHOR> 
 * @date 2022-12-29 13:40:19  
 */
 public interface ICaseLegalPersonInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return CaseLegalPersonInfoPO 添加结果
     */
	 public CaseLegalPersonInfoPO addCaseLegalPersonInfo(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCaseLegalPersonInfo(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return CaseLegalPersonInfoPO 修改结果
     */
	 public CaseLegalPersonInfoPO updateCaseLegalPersonInfo(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return CaseLegalPersonInfoPO 查询结果对象
     */
	 public CaseLegalPersonInfoPO findCaseLegalPersonInfo(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return List<CaseLegalPersonInfoPO> 查询结果List
     */
	 public List<CaseLegalPersonInfoPO> findAllCaseLegalPersonInfo(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findCaseLegalPersonInfoTotal(CaseLegalPersonInfoPO caseLegalPersonInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CaseLegalPersonInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<CaseLegalPersonInfoPO> queryCaseLegalPersonInfoForPage(CaseLegalPersonInfoPO caseLegalPersonInfoPO, CurrentPage<CaseLegalPersonInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCaseLegalPersonInfo(List<CaseLegalPersonInfoPO> caseLegalPersonInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCaseLegalPersonInfo(List<CaseLegalPersonInfoPO> caseLegalPersonInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCaseLegalPersonInfo(List<CaseLegalPersonInfoPO> caseLegalPersonInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param caseLegalPersonInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCaseLegalPersonInfo(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
	 /**
	 * @description 查询已发送结案单证邮件的数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param caseLegalPersonInfoPO
	 * @return 
	 */
	 public List<CaseLegalPersonInfoPO> findAllCaseLegalPersonByEmail(CaseLegalPersonInfoPO caseLegalPersonInfoPO);
	 
 }
 