package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimSignatureTracePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimSignatureTraceDao接口
 * <AUTHOR> 
 * @date 2020-07-21 16:10:58  
 */
 public interface IClaimSignatureTraceDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return ClaimSignatureTracePO 添加结果
     */
	 public ClaimSignatureTracePO addClaimSignatureTrace(ClaimSignatureTracePO claimSignatureTracePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSignatureTrace(ClaimSignatureTracePO claimSignatureTracePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return ClaimSignatureTracePO 修改结果
     */
	 public ClaimSignatureTracePO updateClaimSignatureTrace(ClaimSignatureTracePO claimSignatureTracePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return ClaimSignatureTracePO 查询结果对象
     */
	 public ClaimSignatureTracePO findClaimSignatureTrace(ClaimSignatureTracePO claimSignatureTracePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return List<ClaimSignatureTracePO> 查询结果List
     */
	 public List<ClaimSignatureTracePO> findAllClaimSignatureTrace(ClaimSignatureTracePO claimSignatureTracePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimSignatureTraceTotal(ClaimSignatureTracePO claimSignatureTracePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSignatureTracePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSignatureTracePO> queryClaimSignatureTraceForPage(ClaimSignatureTracePO claimSignatureTracePO, CurrentPage<ClaimSignatureTracePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSignatureTrace(List<ClaimSignatureTracePO> claimSignatureTracePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSignatureTrace(List<ClaimSignatureTracePO> claimSignatureTracePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSignatureTrace(List<ClaimSignatureTracePO> claimSignatureTracePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSignatureTracePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSignatureTrace(ClaimSignatureTracePO claimSignatureTracePO);
	 
 }
 