package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.AggregationRiskTypePO;
import com.nci.udmp.framework.model.CurrentPage;

/** 
 * @description Code Table of risk typeDao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-风险类型
 * @date 2015-05-15 下午2:08:48
 */
 public interface IAggregationRiskTypeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return AggregationRiskTypePO 添加结果
     */
	 public AggregationRiskTypePO addAggregationRiskType(AggregationRiskTypePO aggregationRiskTypePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteAggregationRiskType(AggregationRiskTypePO aggregationRiskTypePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return AggregationRiskTypePO 修改结果
     */
	 public AggregationRiskTypePO updateAggregationRiskType(AggregationRiskTypePO aggregationRiskTypePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return AggregationRiskTypePO 查询结果对象
     */
	 public AggregationRiskTypePO findAggregationRiskType(AggregationRiskTypePO aggregationRiskTypePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return List<AggregationRiskTypePO> 查询结果List
     */
	 public List<AggregationRiskTypePO> findAllAggregationRiskType(AggregationRiskTypePO aggregationRiskTypePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return int 查询结果条数
     */
	 public int findAggregationRiskTypeTotal(AggregationRiskTypePO aggregationRiskTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<AggregationRiskTypePO> 查询结果的当前页对象
     */
	 public CurrentPage<AggregationRiskTypePO> queryAggregationRiskTypeForPage(AggregationRiskTypePO aggregationRiskTypePO, CurrentPage<AggregationRiskTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveAggregationRiskType(List<AggregationRiskTypePO> aggregationRiskTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateAggregationRiskType(List<AggregationRiskTypePO> aggregationRiskTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteAggregationRiskType(List<AggregationRiskTypePO> aggregationRiskTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param aggregationRiskTypePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapAggregationRiskType(AggregationRiskTypePO aggregationRiskTypePO);
	 
 }
 