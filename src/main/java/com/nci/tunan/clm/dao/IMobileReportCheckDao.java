package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO; 
import com.nci.tunan.clm.interfaces.model.po.ReportInformationCheckResPO;
/**
 * 
 * @description MobileReportCheckDaoImpl报案信息接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午2:15:14
 */
public interface IMobileReportCheckDao {

	/**
     * 根据条件查询报案信息
     * @description
     * @version
     * @title
     * <AUTHOR> 
     * @param reportInformationCheckReqPO 报案信息
     * @return reportInformationCheckResPO 报案信息
     */
	public List<ReportInformationCheckResPO> findReportInformation(ReportInformationCheckResPO reportInformationCheckResPO);
	/**
     * 根据条件查询签收监控接口(赔案状态追踪) 对签收确认之后的案件进行状态跟踪
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param claimCasePO  赔案信息
     */
	public List<ClaimCasePO> findClaimCaseStatusTrack(ClaimCasePO claimCasePO);
	/**
     * 保全-理赔服务接口
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param claimCasePO  报案信息
     */
	public int findPasClmServiceTrack(ClaimCasePO claimCasePO);
}
