package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimAccidentRecordPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * @description 事件信息记录表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午2:27:33
 */
 public interface IClaimAccidentRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO   事件记录对象
     * @return ClaimAccidentRecordPO 添加结果
     */
	 public ClaimAccidentRecordPO addClaimAccidentRecord(ClaimAccidentRecordPO claimAccidentRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAccidentRecord(ClaimAccidentRecordPO claimAccidentRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO   事件记录对象
     * @return ClaimAccidentRecordPO 修改结果
     */
	 public ClaimAccidentRecordPO updateClaimAccidentRecord(ClaimAccidentRecordPO claimAccidentRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO   事件记录对象
     * @return ClaimAccidentRecordPO 查询结果对象
     */
	 public ClaimAccidentRecordPO findClaimAccidentRecord(ClaimAccidentRecordPO claimAccidentRecordPO);
	 /**
	  * 根据accidentId查询事件log表信息
	  * @description
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimAccidentRecordPO   事件记录对象
	  * @return
	  */
	 public ClaimAccidentRecordPO findClaimAccidentRecordByAccidentId(ClaimAccidentRecordPO claimAccidentRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO   事件记录对象
     * @return List<ClaimAccidentRecordPO> 查询结果List
     */
	 public List<ClaimAccidentRecordPO> findAllClaimAccidentRecord(ClaimAccidentRecordPO claimAccidentRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO   事件记录对象
     * @return int 查询结果条数
     */
	 public int findClaimAccidentRecordTotal(ClaimAccidentRecordPO claimAccidentRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAccidentRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAccidentRecordPO> queryClaimAccidentRecordForPage(ClaimAccidentRecordPO claimAccidentRecordPO, CurrentPage<ClaimAccidentRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPOList   事件记录对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAccidentRecord(List<ClaimAccidentRecordPO> claimAccidentRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPOList   事件记录对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAccidentRecord(List<ClaimAccidentRecordPO> claimAccidentRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPOList   事件记录对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAccidentRecord(List<ClaimAccidentRecordPO> claimAccidentRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentRecordPO   事件记录对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAccidentRecord(ClaimAccidentRecordPO claimAccidentRecordPO);
	 
 }
 