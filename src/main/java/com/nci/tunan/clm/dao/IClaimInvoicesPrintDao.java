package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimDocPrintLogBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimChecklistPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimDocPrintLogPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimFeeMappingPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.MailDocSendBatchPO;
import com.nci.tunan.clm.interfaces.model.po.NoteDocSendBatchPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 单证打印DAO实现层
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:41:41
 */
public interface IClaimInvoicesPrintDao {
    
    /**
     * 分页查询赔案列表
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimCasePO 赔案信息对象
     * @param currentPage 分页对象
     * @return 分页集合
     */
    public CurrentPage<ClaimCasePO> queryCompersatePrintPage(ClaimCasePO claimCasePO,
            CurrentPage<ClaimCasePO> currentPage);
    
    /**
     * 理赔单证通知书查询单证列表
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimChecklistPO 单证对象
     * @return 集合
     */
    public List<ClaimChecklistPO> queryPrintChecklist(ClaimChecklistPO claimChecklistPO);

    /**
     * 赔付通知书查询医疗信息
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimLiabPO 赔案信息对象
     * @return 集合
     */
    public List<ClaimLiabPO> queryPrintMedList(ClaimLiabPO claimLiabPO);
    /**
     * 赔付通知书查询医疗信息
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimLiabPO 赔案信息对象
     * @return 集合
     */
    public List<ClaimLiabPO> queryPrintMedListJi(ClaimLiabPO claimLiabPO);
    
    /**
     * 赔付通知书查询非医疗信息
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimLiabPO 赔案信息对象
     * @return 集合
     */
    public List<ClaimLiabPO> queryPrintNotMedList(ClaimLiabPO claimLiabPO);
    /**
     * 赔付通知书查询非医疗信息
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimLiabPO 赔案信息对象
     * @return 集合
     */
    public List<ClaimLiabPO> queryPrintNotMedListJi(ClaimLiabPO claimLiabPO);
    /**
     * 查询所有单证
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2015-05-15 下午7:17:31 
     * @see com.nci.tunan.clm.dao.IClaimInvoicesPrintDao#queryPrintChecklistOK(com.nci.tunan.clm.interfaces.model.po.ClaimChecklistPO)
     * @param claimChecklistPO 单证对象
     * @return
     */
    
	public List<ClaimChecklistPO> queryPrintChecklistOK(
			ClaimChecklistPO claimChecklistPO);
	/**
	 * 
	 * @description 打印分页查询 查询条件有保单号
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO 赔案信息
	 * @param copyCurrentPage 赔案信息
	 * @return
	 */
	public CurrentPage<ClaimCasePO> queryCompersatePrintPageCode(
			ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> copyCurrentPage);

	/**
	 * 查询结算项名称
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimFeeMappingPO 赔案信息
	 * @return 赔案信息集合
	 */
    public List<ClaimFeeMappingPO> findAdjustDesc(ClaimFeeMappingPO claimFeeMappingPO);
    
    /**
	 * 查询短信发送信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param noteDocSendBatchPO
	 * @return 
	 */
    public List<NoteDocSendBatchPO> findClaimDocumentNote(NoteDocSendBatchPO noteDocSendBatchPO);
    
    /**
     * 打印人列表查询
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     */
	public List<ClaimDocPrintLogPO> queryClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	
	/**
	 * 查询邮件发送信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param MailDocSendBatchPO 邮件发送信息
	 * @return 
	 */
    public List<MailDocSendBatchPO> findClaimDocumentEmail(MailDocSendBatchPO mailDocSendBatchPO);
    
}
