package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimMatchJournaPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 理赔匹配理算日志表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:46:11
 */
 public interface IClaimMatchJournaDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return ClaimMatchJournaPO 添加结果
     */
	 public ClaimMatchJournaPO addClaimMatchJourna(ClaimMatchJournaPO claimMatchJournaPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimMatchJourna(ClaimMatchJournaPO claimMatchJournaPO);
	 /**
 	  * @description 删除数据根据CASE_ID
 	  * @version
 	  * @title
 	  * <AUTHOR>
 	  * @param claimMatchJournaPO 对象
 	  * @return boolean 删除是否成功
 	  */
 	 public boolean deleteClaimMatchJournaByCaseId(ClaimMatchJournaPO claimMatchJournaPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return ClaimMatchJournaPO 修改结果
     */
	 public ClaimMatchJournaPO updateClaimMatchJourna(ClaimMatchJournaPO claimMatchJournaPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return ClaimMatchJournaPO 查询结果对象
     */
	 public ClaimMatchJournaPO findClaimMatchJourna(ClaimMatchJournaPO claimMatchJournaPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return List<ClaimMatchJournaPO> 查询结果List
     */
	 public List<ClaimMatchJournaPO> findAllClaimMatchJourna(ClaimMatchJournaPO claimMatchJournaPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimMatchJournaTotal(ClaimMatchJournaPO claimMatchJournaPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimMatchJournaPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimMatchJournaPO> queryClaimMatchJournaForPage(ClaimMatchJournaPO claimMatchJournaPO, CurrentPage<ClaimMatchJournaPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimMatchJourna(List<ClaimMatchJournaPO> claimMatchJournaPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimMatchJourna(List<ClaimMatchJournaPO> claimMatchJournaPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimMatchJourna(List<ClaimMatchJournaPO> claimMatchJournaPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimMatchJournaPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimMatchJourna(ClaimMatchJournaPO claimMatchJournaPO);
	 
 }
 