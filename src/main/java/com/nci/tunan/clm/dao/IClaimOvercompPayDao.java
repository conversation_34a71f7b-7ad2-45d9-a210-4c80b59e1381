package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ClaimOvercompPayPO;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimOvercompPayDao接口
 * <AUTHOR> 
 * @date 2021-07-28 18:37:14  
 */
 public interface IClaimOvercompPayDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return ClaimOvercompPayPO 添加结果
     */
	 public ClaimOvercompPayPO addClaimOvercompPay(ClaimOvercompPayPO claimOvercompPayPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimOvercompPay(ClaimOvercompPayPO claimOvercompPayPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return ClaimOvercompPayPO 修改结果
     */
	 public ClaimOvercompPayPO updateClaimOvercompPay(ClaimOvercompPayPO claimOvercompPayPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return ClaimOvercompPayPO 查询结果对象
     */
	 public ClaimOvercompPayPO findClaimOvercompPay(ClaimOvercompPayPO claimOvercompPayPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return List<ClaimOvercompPayPO> 查询结果List
     */
	 public List<ClaimOvercompPayPO> findAllClaimOvercompPay(ClaimOvercompPayPO claimOvercompPayPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimOvercompPayTotal(ClaimOvercompPayPO claimOvercompPayPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimOvercompPayPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimOvercompPayPO> queryClaimOvercompPayForPage(ClaimOvercompPayPO claimOvercompPayPO, CurrentPage<ClaimOvercompPayPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimOvercompPay(List<ClaimOvercompPayPO> claimOvercompPayPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimOvercompPay(List<ClaimOvercompPayPO> claimOvercompPayPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimOvercompPay(List<ClaimOvercompPayPO> claimOvercompPayPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimOvercompPayPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimOvercompPay(ClaimOvercompPayPO claimOvercompPayPO);
	 
 }
 