package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimElecBillTaskPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description IClaimElecBillTaskDao接口
 * <AUTHOR> 
 * @date 2024-04-12 18:08:07  
 */
 public interface IClaimElecBillTaskDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return ClaimElecBillTaskPO 添加结果
     */
	 public ClaimElecBillTaskPO addClaimElecBillTask(ClaimElecBillTaskPO claimElecBillTaskPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimElecBillTask(ClaimElecBillTaskPO claimElecBillTaskPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return ClaimElecBillTaskPO 修改结果
     */
	 public ClaimElecBillTaskPO updateClaimElecBillTask(ClaimElecBillTaskPO claimElecBillTaskPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return ClaimElecBillTaskPO 查询结果对象
     */
	 public ClaimElecBillTaskPO findClaimElecBillTask(ClaimElecBillTaskPO claimElecBillTaskPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return List<ClaimElecBillTaskPO> 查询结果List
     */
	 public List<ClaimElecBillTaskPO> findAllClaimElecBillTask(ClaimElecBillTaskPO claimElecBillTaskPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimElecBillTaskTotal(ClaimElecBillTaskPO claimElecBillTaskPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimElecBillTaskPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimElecBillTaskPO> queryClaimElecBillTaskForPage(ClaimElecBillTaskPO claimElecBillTaskPO, CurrentPage<ClaimElecBillTaskPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimElecBillTask(List<ClaimElecBillTaskPO> claimElecBillTaskPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimElecBillTask(List<ClaimElecBillTaskPO> claimElecBillTaskPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimElecBillTask(List<ClaimElecBillTaskPO> claimElecBillTaskPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimElecBillTask(ClaimElecBillTaskPO claimElecBillTaskPO);

	 /**
     * @description 查询入账数据：回退标识为0或空，任务状态为已锁定或入账失败，且已支付
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return List<ClaimElecBillTaskPO> 查询结果List
     */
	 public List<ClaimElecBillTaskPO> queryEntryCases(ClaimElecBillTaskPO claimElecBillTaskPO);

	 /**
     * 查询入账撤销数据：回退标识为是，任务状态为“已入账/撤销入账失败”，会计凭证号和入账日期不为空
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return List<ClaimElecBillTaskPO> 查询结果List
     */
	 public List<ClaimElecBillTaskPO> queryUnEntryCases(ClaimElecBillTaskPO claimElecBillTaskPO);

	 /**
     * 查询锁定撤销数据：理赔电票任务表中回退标识为1，任务状态为已锁定或撤销锁定失败，锁定业务流水号和锁定日期不为空
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return List<ClaimElecBillTaskPO> 查询结果List
     */
	 public List<ClaimElecBillTaskPO> queryUnLockCases(ClaimElecBillTaskPO claimElecBillTaskPO);

	 /**
     * 根据caseId查询理赔电票任务
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillTaskPO 对象
     * @return List<ClaimElecBillTaskPO> 查询结果List
     */
	 public ClaimElecBillTaskPO findClaimElecBillTaskByCaseId(ClaimElecBillTaskPO claimElecBillTaskPO);
	 
	 /**
     * @description 查询回退标识为0或空，任务状态为待锁定或锁定失败的数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimElecBillTaskPO 对象
     * @return List<ClaimElecBillTaskPO> 查询结果List
     */
	 public List<ClaimElecBillTaskPO> queryLockCases(ClaimElecBillTaskPO claimElecBillTaskPO);
		 

 }
 