package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.QueryMishapDeatilPO;

/**
 * 
 * @description  理赔意外细节查询
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔意外细节
 * @date 2015-05-15 下午2:18:28
 */
public interface IQueryMishapDeatilDAO {
    /**
     * 
     * @description 查询所有
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param queryMishapDeatilPO   意外细节
     * @return  意外细节集合
     */
	public List<QueryMishapDeatilPO> queryMishapDeatil(QueryMishapDeatilPO queryMishapDeatilPO);
}
