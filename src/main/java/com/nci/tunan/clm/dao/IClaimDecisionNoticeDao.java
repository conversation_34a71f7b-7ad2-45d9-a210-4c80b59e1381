package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimDecisionNoticePO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description IClaimDecisionNoticeDao理赔决定通知书（拒付）接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔决定通知书（拒付）
 * @date 2015-05-15 下午6:09:33
 */
 public interface IClaimDecisionNoticeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return ClaimDecisionNoticePO 添加结果
     */
	 public ClaimDecisionNoticePO addClaimDecisionNotice(ClaimDecisionNoticePO claimDecisionNoticePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDecisionNotice(ClaimDecisionNoticePO claimDecisionNoticePO);
	 /**
 	  * @description 根据Case_id删除数据
 	  * @version
 	  * @title
 	  * <AUTHOR>
 	  * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
 	  * @return boolean 删除是否成功
 	  */
 	 public boolean deleteClaimDecisionNoticeByCaseId(ClaimDecisionNoticePO claimDecisionNoticePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return ClaimDecisionNoticePO 修改结果
     */
	 public ClaimDecisionNoticePO updateClaimDecisionNotice(ClaimDecisionNoticePO claimDecisionNoticePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return ClaimDecisionNoticePO 查询结果对象
     */
	 public ClaimDecisionNoticePO findClaimDecisionNotice(ClaimDecisionNoticePO claimDecisionNoticePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return List<ClaimDecisionNoticePO> 查询结果List
     */
	 public List<ClaimDecisionNoticePO> findAllClaimDecisionNotice(ClaimDecisionNoticePO claimDecisionNoticePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return int 查询结果条数
     */
	 public int findClaimDecisionNoticeTotal(ClaimDecisionNoticePO claimDecisionNoticePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDecisionNoticePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDecisionNoticePO> queryClaimDecisionNoticeForPage(ClaimDecisionNoticePO claimDecisionNoticePO, CurrentPage<ClaimDecisionNoticePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePOList 理赔决定通知书（拒付）对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDecisionNotice(List<ClaimDecisionNoticePO> claimDecisionNoticePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePOList 理赔决定通知书（拒付）对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDecisionNotice(List<ClaimDecisionNoticePO> claimDecisionNoticePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDecisionNotice(List<ClaimDecisionNoticePO> claimDecisionNoticePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDecisionNotice(ClaimDecisionNoticePO claimDecisionNoticePO);

	 /**
	  * 
	  * @description 修改是否预览
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param claimDecisionNoticePO 理赔决定通知书（拒付）
	  * @return
	  */
	public ClaimDecisionNoticePO updateClaimDecisionNoticeNoticeFlag(
			ClaimDecisionNoticePO claimDecisionNoticePO);
	 
	/**
     * @description 根据赔案id查询指定数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDecisionNoticePO 理赔决定通知书（拒付）对象
     * @return List<ClaimDecisionNoticePO> 查询理赔决定通知书（拒付）结果List
     */
	 public List<ClaimDecisionNoticePO> findAllClaimDecisions(ClaimDecisionNoticePO claimDecisionNoticePO);
 }
 