package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLevelLiabPO;
import com.nci.tunan.clm.interfaces.model.po.ContractAgentPO;
import com.nci.tunan.clm.interfaces.model.po.PremArapPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimSignatureTracePO;
import com.nci.tunan.clm.interfaces.model.po.common.AuditTaskMessagesPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 公共的查询方法dao
 * <AUTHOR> <EMAIL>
 * @.belongToModule CLM-理赔系统
 * @date 2015-8-5 下午4:25:50
 */
public interface IClaimCommonQueryDao {
	 /**
     * 
     * @description 保单通过保单号查询赔案的案件状态
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimCasePO 赔案信息参数
     * @return ClaimCasePO 返回对象
     */
    public  List<ClaimCasePO>  queryClmCaseStatus(ClaimCasePO claimCasePO);
    /**
     * 
     * @description 保单通过保单号查询结案的案件状态是否发生赔付给付
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimCasePO 赔案信息参数
     * @return ClaimCasePO 返回对象
     */
    public  List<ClaimCasePO>  queryClmCaseStatuss(ClaimCasePO claimCasePO);
    
    /**
     * 
     * @description 查询赔案在应收应付中的赔付总金额
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param premArapPO 应收应付参数
     * @return PremArapPO 返回对象
     */
    public PremArapPO queryfeeAmount(PremArapPO premArapPO);
    /**
     * @description 通过出险人查询既往赔案信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO
     *            赔案信息参数
     * @param currentPage  分页参数
     * @return CurrentPage<ClaimCasePO> 查询结果CurrentPage<ClaimCasePO>
     */
    public CurrentPage<ClaimCasePO> queryClaimHisInfo(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);
    /**
     * @description 通过出险人查询既往赔案信息所有
     * @param claimCasePO 赔案信息
     * @return List<ClaimCasePO> 赔案信息
     * <AUTHOR>
     */
    public List<ClaimCasePO> queryClaimHisInfoList(ClaimCasePO claimCasePO);
    
    /**
     * @description 通过出险人查询既往赔案信息所有(查询字段去除责任id)
     * @param claimCasePO 赔案信息
     * @return List<ClaimCasePO> 赔案信息
     * <AUTHOR>
     */
    public List<ClaimCasePO> queryClaimHisInfoLists(ClaimCasePO claimCasePO);
    
    /**
     * @description 通过保单号查询是否为迁移数据
     * @param claimCasePO 赔案信息
     * @return List<ClaimCasePO> 赔案信息
     * <AUTHOR>
     */
    public List<ClaimCasePO> queryClaimLog(ClaimCasePO claimCasePO);

    /**
     * @description 迁移数据查既往赔案
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO
     *            赔案信息参数
     * @param currentPage  分页参数
     * @return CurrentPage<ClaimCasePO> 查询结果CurrentPage<ClaimCasePO>
     */
    public CurrentPage<ClaimCasePO> queryClaimHisInfoLog(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);
    
    /**
     * 
     * @description 通过赔案号查询应收应付数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param premArapPO 应收应付参数
     * @return List<PremArapPO> 查询的结果list
     */
    public List<PremArapPO> findAllPremArap(PremArapPO premArapPO);
    /**
     * 
     * @description 通过caseId查询所有的代理人编码agentCode
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param contractAgentPO  代理人
     * @return List<ContractAgentPO> 代理人
     * @throws BizException
     */
    public List<ContractAgentPO> queryAgentCode(ContractAgentPO contractAgentPO) throws BizException;
    
    /**
     * 
     * @description 通过出险人及产品代码查询既往赔案信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimCasePO 赔案信息
     * @return  赔案信息集合
     */
    public List<ClaimCasePO> queryClaimHisInfoDetail(ClaimCasePO claimCasePO);
    /**
     * 
     * @description 通过出险人及产品代码查询既往新型健康险赔案信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimCasePO 赔案信息
     * @return  赔案信息集合
     */
    public List<ClaimCasePO> queryNewHealthClaimHisInfoDetail(ClaimCasePO claimCasePO);
    /**
     * 
     * @description 通过出险人及产品代码查询既往赔案信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimCasePO 赔案信息
     * @return 赔案信息集合
     * @throws BizException
     */
    public  List<ClaimCasePO> problemCheckList(ClaimCasePO claimCasePO) throws BizException ;
    /**
     * 
     * @description 查询赔案问题件
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimCasePO 赔案信息
     * @return 赔案信息集合
     * @throws BizException
     */
    public  List<ClaimCasePO> problemCheckNewList(ClaimCasePO claimCasePO) throws BizException ;
    /**
     * 
     * @description 通过casNO集合查询个人池所需数据
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param ClaimCasePO 赔案信息
     * @return List<ClaimCasePO> 赔案信息
     * @throws BizException
     */
    public List<ClaimCasePO> queryRegisterTaskMessages(ClaimCasePO claimCasePO) throws BizException; 
    /**
     * 
     * @description 通过casID查询   
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param ClaimCasePO 赔案信息
     * @return List<ClaimCasePO>  赔案信息
     * @throws BizException
     */
    public List<ClaimCasePO> queryStopReasonReturnMessage(ClaimCasePO claimCasePO) throws BizException;
    
    /**
	  * @description 删除数据根据CASE_ID
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimCasePO 赔案信息对象
	  * @return boolean 删除是否成功
	  */
	 public boolean deleteClaimMatchInfoByCaseId(ClaimCasePO claimCasePO);
	 /** 删除抄单数据根据赔案ID和抄单时间
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param claimCasePO 赔案信息
	 * @return 是否成功
	*/
	public boolean deleteMasterByCaseIdAndDate(ClaimCasePO claimCasePO);
	/** 删除抄单数据根据赔案ID和抄单时间
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param claimCasePO 赔案信息
	 * @return 是否成功
	 */
	public boolean deleteMasterByCaseId(ClaimCasePO claimCasePO);
	/**
	 * 
	 * @description 迁移数据查既往赔案(用于理赔页面查询既往赔案信息)
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO 赔案信息
	 * @param copyCurrentPage 赔案信息
	 * @return
	 */
	public CurrentPage<ClaimCasePO> queryClaimHisInfoLog1(
			ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> copyCurrentPage);
	/**
	 * 
	 * @description 通过出险人查询既往赔案信息(用于理赔页面查询既往赔案信息)
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO  赔案信息
	 * @param copyCurrentPage 赔案信息
	 * @return
	 */
	public CurrentPage<ClaimCasePO> queryClaimHisInfo1(ClaimCasePO claimCasePO,
			CurrentPage<ClaimCasePO> copyCurrentPage);
	
	/**
     * @description 通过出险人查询报案信息（供柜面接口使用）
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO 赔案信息参数
     * @return CurrentPage<ClaimCasePO> 查询结果CurrentPage<ClaimCasePO>
     */
    public CurrentPage<ClaimCasePO> queryClaimReportInfoForPage(ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);
    
    /**
     * @description 通过赔案号查询保单号（供核保接口使用）
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO 赔案信息参数
     * @return List<ClaimCasePO>
     */
    public List<ClaimCasePO> queryClaimPolicyByCaseNo(ClaimCasePO claimCasePO);
    
    /**
     * @description 通过业务员号查询赔案（供核保接口使用）
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO 赔案信息参数
     * @return List<ClaimCasePO> 赔案信息集合
     */
    public List<ClaimCasePO> queryClaimByAgent(ClaimCasePO claimCasePO);
    /**
     * 
     * @description 通过casNO集合查询个人池所需数据
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param ClaimCasePO 赔案信息
     * @return List<AuditTaskMessagesPO> 个人池数据
     * @throws BizException
     */
    public  List<AuditTaskMessagesPO> queryAuditTaskMessages(AuditTaskMessagesPO auditTaskMessagesPO) throws BizException;
    
    /**
     * 
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRiskLevelLiabPO
     * @return
     */
    public List<ClaimRiskLevelLiabPO> queryRiskMessage(ClaimRiskLevelLiabPO claimRiskLevelLiabPO);
    /**
     * @description 查询电子签名轨迹所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param SignatureTracePO 电子签名轨迹对象
     * @return CurrentPage<SignatureTracePO> 查询结果List
     */	
	public CurrentPage<ClaimSignatureTracePO> findAllSignatureTrace(ClaimSignatureTracePO signatureTracePO,CurrentPage<ClaimSignatureTracePO> currentPage);
	/**
     * @description 查询电子签名轨迹数据置灰
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param SignatureTracePO 电子签名轨迹对象
     * @return List<ClaimSignatureTracePO> 查询结果List
     */	
	public List<ClaimSignatureTracePO> querySignatureLines(ClaimSignatureTracePO claimSignatureTracePO);
    /**
     * @description 查询责任下的领款人姓名和分配比例
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimPayPO 保单险种信息
     * @return 责任下的领款人姓名和分配比例
    */
    public List<ClaimPayPO> findAllClaimPayeeName(ClaimPayPO claimPayPO);
    /**
     * 
     * @description 通过casNO集合查询个人池所需数据(复核案件)
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param ClaimCasePO 赔案信息
     * @return List<AuditTaskMessagesPO> 个人池数据
     * @throws BizException
     */
    public List<AuditTaskMessagesPO> queryAuditTaskMessageses(AuditTaskMessagesPO auditTaskMessagesPO);
}
