package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimSurveyOrganPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/**
 * 
 * @description  理赔调查规则保单机构表接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-=理赔调查规则保单机构
 * @date 2015-05-15 下午3:53:10
 */
 public interface IClaimSurveyOrganDao {
	 /**
     * @description 增加理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 调查规则保单机构对象
     * @return ClaimSurveyOrganPO 添加结果
     */
	 public ClaimSurveyOrganPO addClaimSurveyOrgan(ClaimSurveyOrganPO claimSurveyOrganPO);
	 
     /**
     * @description 删除理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 调查规则保单机构对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSurveyOrgan(ClaimSurveyOrganPO claimSurveyOrganPO);
	 
     /**
     * @description 修改理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 调查规则保单机构对象
     * @return ClaimSurveyOrganPO 修改结果
     */
	 public ClaimSurveyOrganPO updateClaimSurveyOrgan(ClaimSurveyOrganPO claimSurveyOrganPO);
	 
     /**
     * @description 查询单条理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 对象
     * @return ClaimSurveyOrganPO 查询结果对象
     */
	 public ClaimSurveyOrganPO findClaimSurveyOrgan(ClaimSurveyOrganPO claimSurveyOrganPO);
	 
     /**
     * @description 查询所有理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 对象
     * @return List<ClaimSurveyOrganPO> 查询结果List
     */
	 public List<ClaimSurveyOrganPO> findAllClaimSurveyOrgan(ClaimSurveyOrganPO claimSurveyOrganPO);
	 
     /**
     * @description 查询理赔调查规则保单机构数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 调查规则保单机构对象
     * @return int 查询结果条数
     */
	 public int findClaimSurveyOrganTotal(ClaimSurveyOrganPO claimSurveyOrganPO);

     /**
     * @description 分页查询理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSurveyOrganPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSurveyOrganPO> queryClaimSurveyOrganForPage(ClaimSurveyOrganPO claimSurveyOrganPO, CurrentPage<ClaimSurveyOrganPO> currentPage);

     /**
     * @description 批量增加理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPOList 调查规则保单机构对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSurveyOrgan(List<ClaimSurveyOrganPO> claimSurveyOrganPOList);

     /**
     * @description 批量修改理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPOList 调查规则保单机构对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSurveyOrgan(List<ClaimSurveyOrganPO> claimSurveyOrganPOList);

     /**
     * @description 批量删除理赔调查规则保单机构数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPOList 调查规则保单机构对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSurveyOrgan(List<ClaimSurveyOrganPO> claimSurveyOrganPOList);

     /**
     * @description 查询所有理赔调查规则保单机构数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyOrganPO 调查规则保单机构对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSurveyOrgan(ClaimSurveyOrganPO claimSurveyOrganPO);
     
     /**
      * @description 删除理赔调查规则保单机构数据
      * @version
      * @title
      * <AUTHOR>
      * @param claimSurveyOrganPO 调查规则保单机构对象
      * @return boolean 删除是否成功
      */
     public boolean deleteClaimSurveyOrganBySurveyRuleCode(ClaimSurveyOrganPO claimSurveyOrganPO);
	 
 }
 