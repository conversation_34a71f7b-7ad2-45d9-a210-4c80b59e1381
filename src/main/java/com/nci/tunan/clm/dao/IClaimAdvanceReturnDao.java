package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimAdvanceReturnPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;


/** 
 * @description IClaimAdvanceReturnDao接口
 * <AUTHOR> 
 * @date 2025-03-03 15:22:44  
 */
 public interface IClaimAdvanceReturnDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return ClaimAdvanceReturnPO 添加结果
     */
	 public ClaimAdvanceReturnPO addClaimAdvanceReturn(ClaimAdvanceReturnPO claimAdvanceReturnPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAdvanceReturn(ClaimAdvanceReturnPO claimAdvanceReturnPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return ClaimAdvanceReturnPO 修改结果
     */
	 public ClaimAdvanceReturnPO updateClaimAdvanceReturn(ClaimAdvanceReturnPO claimAdvanceReturnPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return ClaimAdvanceReturnPO 查询结果对象
     */
	 public ClaimAdvanceReturnPO findClaimAdvanceReturn(ClaimAdvanceReturnPO claimAdvanceReturnPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return List<ClaimAdvanceReturnPO> 查询结果List
     */
	 public List<ClaimAdvanceReturnPO> findAllClaimAdvanceReturn(ClaimAdvanceReturnPO claimAdvanceReturnPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimAdvanceReturnTotal(ClaimAdvanceReturnPO claimAdvanceReturnPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAdvanceReturnPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAdvanceReturnPO> queryClaimAdvanceReturnForPage(ClaimAdvanceReturnPO claimAdvanceReturnPO, CurrentPage<ClaimAdvanceReturnPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAdvanceReturn(List<ClaimAdvanceReturnPO> claimAdvanceReturnPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAdvanceReturn(List<ClaimAdvanceReturnPO> claimAdvanceReturnPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAdvanceReturn(List<ClaimAdvanceReturnPO> claimAdvanceReturnPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAdvanceReturnPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAdvanceReturn(ClaimAdvanceReturnPO claimAdvanceReturnPO);

	 /**
     * @description 垫付信息查看列表分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskLibraryPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAdvanceReturnPO> queryAdvanceInfoListForPage(ClaimAdvanceReturnPO claimAdvanceReturnPO, CurrentPage<ClaimAdvanceReturnPO> currentPage);

	 /**
	  * 
	 	 * 
	 	 * @description 显示选中记录的垫付详细信息
	 	 * @version V1.0.0
	 	 * @title
	  */
	public ClaimAdvanceReturnPO queryAdvanceInfoDetail(ClaimAdvanceReturnPO claimAdvanceReturnPO);

	/**
	 * 
	 * @description 根据 claimAdvanceReturnVO caseNo查询垫付详细信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return 
	 */
	public ClaimAdvanceReturnPO queryAdvanceInfoCaseNo(ClaimAdvanceReturnPO claimAdvanceReturnPO);
	 
 }
 