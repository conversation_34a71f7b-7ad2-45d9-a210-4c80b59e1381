package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPreSurveyPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimLiabPreSurveyDao接口
 * <AUTHOR> 
 * @date 2023-12-18 13:48:55  
 */
 public interface IClaimLiabPreSurveyDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return ClaimLiabPreSurveyPO 添加结果
     */
	 public ClaimLiabPreSurveyPO addClaimLiabPreSurvey(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimLiabPreSurvey(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return ClaimLiabPreSurveyPO 修改结果
     */
	 public ClaimLiabPreSurveyPO updateClaimLiabPreSurvey(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return ClaimLiabPreSurveyPO 查询结果对象
     */
	 public ClaimLiabPreSurveyPO findClaimLiabPreSurvey(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return List<ClaimLiabPreSurveyPO> 查询结果List
     */
	 public List<ClaimLiabPreSurveyPO> findAllClaimLiabPreSurvey(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimLiabPreSurveyTotal(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimLiabPreSurveyPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimLiabPreSurveyPO> queryClaimLiabPreSurveyForPage(ClaimLiabPreSurveyPO claimLiabPreSurveyPO, CurrentPage<ClaimLiabPreSurveyPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimLiabPreSurvey(List<ClaimLiabPreSurveyPO> claimLiabPreSurveyPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimLiabPreSurvey(List<ClaimLiabPreSurveyPO> claimLiabPreSurveyPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimLiabPreSurvey(List<ClaimLiabPreSurveyPO> claimLiabPreSurveyPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabPreSurveyPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimLiabPreSurvey(ClaimLiabPreSurveyPO claimLiabPreSurveyPO);
	 
 }
 