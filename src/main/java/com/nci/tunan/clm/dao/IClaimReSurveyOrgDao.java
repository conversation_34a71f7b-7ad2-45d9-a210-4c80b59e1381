package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimReSurveyOrgPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description 复堪调查机构表Dao
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-09-24 11:08:28  
 */
 public interface IClaimReSurveyOrgDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return ClaimReSurveyOrgPO 添加结果
     */
	 public ClaimReSurveyOrgPO addClaimReSurveyOrg(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimReSurveyOrg(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return ClaimReSurveyOrgPO 修改结果
     */
	 public ClaimReSurveyOrgPO updateClaimReSurveyOrg(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return ClaimReSurveyOrgPO 查询结果对象
     */
	 public ClaimReSurveyOrgPO findClaimReSurveyOrg(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return List<ClaimReSurveyOrgPO> 查询结果List
     */
	 public List<ClaimReSurveyOrgPO> findAllClaimReSurveyOrg(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return int 查询结果条数
     */
	 public int findClaimReSurveyOrgTotal(ClaimReSurveyOrgPO claimReSurveyOrgPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimReSurveyOrgPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimReSurveyOrgPO> queryClaimReSurveyOrgForPage(ClaimReSurveyOrgPO claimReSurveyOrgPO, CurrentPage<ClaimReSurveyOrgPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPOList 复堪调查机构对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimReSurveyOrg(List<ClaimReSurveyOrgPO> claimReSurveyOrgPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPOList 复堪调查机构对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimReSurveyOrg(List<ClaimReSurveyOrgPO> claimReSurveyOrgPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPOList 复堪调查机构对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimReSurveyOrg(List<ClaimReSurveyOrgPO> claimReSurveyOrgPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyOrgPO 复堪调查机构对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimReSurveyOrg(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 /**
	  * 
	  * @description 批量添加复堪调查机构表数据
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param claimReSurveyOrgPOs 复堪调查机构对象
	  */
     public void batchSaveReSurveyOrg(List<ClaimReSurveyOrgPO> claimReSurveyOrgPOs);
     /**
      * 
      * @description 通过planId查询所有复堪调查机构表数据
      * <AUTHOR> <EMAIL>
      * @param  claimReSurveyOrgPO  复堪调查机构对象参数
      * @return List<ClaimReSurveyOrgPO> 复堪调查机构对象
      * @throws BizException
      */
     public List<ClaimReSurveyOrgPO> queryAllReSurveyRule(ClaimReSurveyOrgPO claimReSurveyOrgPO);
     /**
      * @description 删除数据
      * @version
      * @title
      * <AUTHOR> <EMAIL>
      * @param claimReSurveyOrgPO 复堪调查机构对象
      * @return boolean 删除是否成功
      */
     public void deleteReSurveyOrgByPlanId(ClaimReSurveyOrgPO claimReSurveyOrgPO);
	 
 }
 