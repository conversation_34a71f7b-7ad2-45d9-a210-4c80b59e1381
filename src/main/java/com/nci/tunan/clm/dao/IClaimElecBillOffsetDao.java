package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimElecBillOffsetPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimElecBillOffsetDao接口
 * <AUTHOR> 
 * @date 2024-04-29 10:56:30  
 */
 public interface IClaimElecBillOffsetDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return ClaimElecBillOffsetPO 添加结果
     */
	 public ClaimElecBillOffsetPO addClaimElecBillOffset(ClaimElecBillOffsetPO claimElecBillOffsetPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimElecBillOffset(ClaimElecBillOffsetPO claimElecBillOffsetPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return ClaimElecBillOffsetPO 修改结果
     */
	 public ClaimElecBillOffsetPO updateClaimElecBillOffset(ClaimElecBillOffsetPO claimElecBillOffsetPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return ClaimElecBillOffsetPO 查询结果对象
     */
	 public ClaimElecBillOffsetPO findClaimElecBillOffset(ClaimElecBillOffsetPO claimElecBillOffsetPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return List<ClaimElecBillOffsetPO> 查询结果List
     */
	 public List<ClaimElecBillOffsetPO> findAllClaimElecBillOffset(ClaimElecBillOffsetPO claimElecBillOffsetPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimElecBillOffsetTotal(ClaimElecBillOffsetPO claimElecBillOffsetPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimElecBillOffsetPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimElecBillOffsetPO> queryClaimElecBillOffsetForPage(ClaimElecBillOffsetPO claimElecBillOffsetPO, CurrentPage<ClaimElecBillOffsetPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimElecBillOffset(List<ClaimElecBillOffsetPO> claimElecBillOffsetPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimElecBillOffset(List<ClaimElecBillOffsetPO> claimElecBillOffsetPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimElecBillOffset(List<ClaimElecBillOffsetPO> claimElecBillOffsetPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillOffsetPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimElecBillOffset(ClaimElecBillOffsetPO claimElecBillOffsetPO);
	 
 }
 