package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskCheckItemPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimRiskCheckItemDao接口
 * <AUTHOR> 
 * @date 2023-02-03 14:34:00  
 */
 public interface IClaimRiskCheckItemDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return ClaimRiskCheckItemPO 添加结果
     */
	 public ClaimRiskCheckItemPO addClaimRiskCheckItem(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskCheckItem(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 /**
     * @description 删除数据根据风控库ID
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskCheckItemByRiskLibId(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return ClaimRiskCheckItemPO 修改结果
     */
	 public ClaimRiskCheckItemPO updateClaimRiskCheckItem(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return ClaimRiskCheckItemPO 查询结果对象
     */
	 public ClaimRiskCheckItemPO findClaimRiskCheckItem(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
	 /**
      * @description 根据风控库id查询抽检要素
      * @version
      * @title
      * <AUTHOR>
      * @param claimRiskCheckItemPO 对象
      * @return ClaimRiskCheckItemPO 查询结果对象
      */
	  public ClaimRiskCheckItemPO findClaimRiskCheckItemByRiskLibId(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return List<ClaimRiskCheckItemPO> 查询结果List
     */
	 public List<ClaimRiskCheckItemPO> findAllClaimRiskCheckItem(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskCheckItemTotal(ClaimRiskCheckItemPO claimRiskCheckItemPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskCheckItemPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskCheckItemPO> queryClaimRiskCheckItemForPage(ClaimRiskCheckItemPO claimRiskCheckItemPO, CurrentPage<ClaimRiskCheckItemPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskCheckItem(List<ClaimRiskCheckItemPO> claimRiskCheckItemPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskCheckItem(List<ClaimRiskCheckItemPO> claimRiskCheckItemPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskCheckItem(List<ClaimRiskCheckItemPO> claimRiskCheckItemPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskCheckItem(ClaimRiskCheckItemPO claimRiskCheckItemPO);

	public List<ClaimRiskCheckItemPO> queryRiskCheckItemByRiskLibId(ClaimRiskCheckItemPO checkItemPO);
	/**
     * @description 查询所有抽检要素描述数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return List<ClaimRiskCheckItemPO> 查询结果List
     */
	 public List<ClaimRiskCheckItemPO> queryAllRiskCheckDesc(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 /**
     * @description 查询所有抽检要素描述数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskCheckItemPO 对象
     * @return List<ClaimRiskCheckItemPO> 查询结果List
     */
	 public List<ClaimRiskCheckItemPO> queryAllRiskCheckDescNew(ClaimRiskCheckItemPO claimRiskCheckItemPO);
	 
 }
 