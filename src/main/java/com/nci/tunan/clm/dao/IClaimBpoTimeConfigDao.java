/**
 * 
 */
package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimBpoTimeConfigPO;
import com.nci.udmp.framework.model.CurrentPage;

/** 
 * @description 
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-外包工作时间配置
 * @date 2021-4-22 下午2:53:46  
 */
public interface IClaimBpoTimeConfigDao {
	/**
	 * 
	 * @description 增加数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param BpoTimeConfigVO
	 * @return
	 */
	public ClaimBpoTimeConfigPO addBpoTimeConfig(ClaimBpoTimeConfigPO BpoTimeConfigPO);
	/**
	 * 
	 * @description 删除数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param BpoTimeConfigVO
	 * @return
	 */
	public boolean deleteBpoTimeConfig(ClaimBpoTimeConfigPO BpoTimeConfigPO);
	/**
	 * 
	 * @description 查询单条数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param BpoTimeConfigVO
	 * @return
	 */
	public ClaimBpoTimeConfigPO findBpoTimeConfig(ClaimBpoTimeConfigPO BpoTimeConfigPO);
	/**
	 * 
	 * @description 查询所有数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param BpoTimeConfigVO
	 * @return
	 */
	public List<ClaimBpoTimeConfigPO> findAllBpoTimeConfig(ClaimBpoTimeConfigPO BpoTimeConfigPO);
	/**
	 * 
	 * @description 查询数据总数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param BpoTimeConfigVO
	 * @return
	 */
	public int findBpoTimeConfigTotal(ClaimBpoTimeConfigPO BpoTimeConfigPO);
	/**
	 * 
	 * @description 分页查询数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param BpoTimeConfigVO
	 * @param currentPage
	 * @return
	 */
	public CurrentPage<ClaimBpoTimeConfigPO> queryBpoTimeConfigForPage(ClaimBpoTimeConfigPO BpoTimeConfigPO, CurrentPage<ClaimBpoTimeConfigPO> currentPage);
}
