package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimInspectType1PO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimInspectType1Dao接口
 * <AUTHOR> 
 * @date 2023-06-27 14:45:33  
 */
 public interface IClaimInspectType1Dao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return ClaimInspectType1PO 添加结果
     */
	 public ClaimInspectType1PO addClaimInspectType1(ClaimInspectType1PO claimInspectType1PO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInspectType1(ClaimInspectType1PO claimInspectType1PO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return ClaimInspectType1PO 修改结果
     */
	 public ClaimInspectType1PO updateClaimInspectType1(ClaimInspectType1PO claimInspectType1PO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return ClaimInspectType1PO 查询结果对象
     */
	 public ClaimInspectType1PO findClaimInspectType1(ClaimInspectType1PO claimInspectType1PO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return List<ClaimInspectType1PO> 查询结果List
     */
	 public List<ClaimInspectType1PO> findAllClaimInspectType1(ClaimInspectType1PO claimInspectType1PO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInspectType1Total(ClaimInspectType1PO claimInspectType1PO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInspectType1PO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInspectType1PO> queryClaimInspectType1ForPage(ClaimInspectType1PO claimInspectType1PO, CurrentPage<ClaimInspectType1PO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1POList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInspectType1(List<ClaimInspectType1PO> claimInspectType1POList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1POList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInspectType1(List<ClaimInspectType1PO> claimInspectType1POList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1POList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInspectType1(List<ClaimInspectType1PO> claimInspectType1POList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType1PO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInspectType1(ClaimInspectType1PO claimInspectType1PO);
	 
 }
 