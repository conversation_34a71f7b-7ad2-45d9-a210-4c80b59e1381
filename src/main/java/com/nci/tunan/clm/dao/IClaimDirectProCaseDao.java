package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimDirectProCasePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimDirectProCaseDao接口
 * <AUTHOR> 
 * @date 2021-11-08 11:12:29  
 */
 public interface IClaimDirectProCaseDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return ClaimDirectProCasePO 添加结果
     */
	 public ClaimDirectProCasePO addClaimDirectProCase(ClaimDirectProCasePO claimDirectProCasePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectProCase(ClaimDirectProCasePO claimDirectProCasePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return ClaimDirectProCasePO 修改结果
     */
	 public ClaimDirectProCasePO updateClaimDirectProCase(ClaimDirectProCasePO claimDirectProCasePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return ClaimDirectProCasePO 查询结果对象
     */
	 public ClaimDirectProCasePO findClaimDirectProCase(ClaimDirectProCasePO claimDirectProCasePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return List<ClaimDirectProCasePO> 查询结果List
     */
	 public List<ClaimDirectProCasePO> findAllClaimDirectProCase(ClaimDirectProCasePO claimDirectProCasePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectProCaseTotal(ClaimDirectProCasePO claimDirectProCasePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectProCasePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectProCasePO> queryClaimDirectProCaseForPage(ClaimDirectProCasePO claimDirectProCasePO, CurrentPage<ClaimDirectProCasePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectProCase(List<ClaimDirectProCasePO> claimDirectProCasePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectProCase(List<ClaimDirectProCasePO> claimDirectProCasePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectProCase(List<ClaimDirectProCasePO> claimDirectProCasePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectProCasePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectProCase(ClaimDirectProCasePO claimDirectProCasePO);

	 /**
	  * 直连问题案件池-页面初始化
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimCasePO
	  * @param copyCurrentPage
	  * @return
	  */
	public CurrentPage<ClaimCasePO> directConnProCaseSharePoolInit(
			ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> copyCurrentPage);

	/**
	 * 直连问题案件池-查询共享池列表
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCasePO
	 * @param copyCurrentPage
	 * @return
	 */
	public CurrentPage<ClaimCasePO> findConnProCaseSharePool(
			ClaimCasePO claimCasePO, CurrentPage<ClaimCasePO> currentPage);

	/**
	 * 直连问题案件池-任务申请至个人池
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimDirectProCasePO
	 */
	public void applyProCaseSelfPool(ClaimDirectProCasePO claimDirectProCasePO);
	 
 }
 