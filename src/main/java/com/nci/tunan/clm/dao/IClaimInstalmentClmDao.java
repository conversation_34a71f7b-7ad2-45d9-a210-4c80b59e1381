package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimInstalmentPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
/**
 * 
 * @description 理赔分期给付接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:39:17
 */
public interface IClaimInstalmentClmDao {

    /**
     * @description 手工分期给付信息分页查询
     * @version
     * @title
     * <AUTHOR> <EMAIL> 
     * @param claimInstalmentPO 理赔分期给付对象
     * @param claimInstalmentPage 当前页
     * @return
     * @throws BizException
     */
    public CurrentPage<ClaimInstalmentPO> findClaimInstalment(ClaimInstalmentPO claimInstalmentPO,CurrentPage<ClaimInstalmentPO> claimInstalmentPage) throws BizException;
    
    /**
     * @description 自动发起分次给付的应付信息查询
     * @version
     * @title
     * <AUTHOR> <EMAIL> 
     * @param claimInstalmentPO 理赔分期给付
     * @param claimInstalmentPage 当前页
     * @return
     * @throws BizException
     */
    public List<ClaimInstalmentPO> findBathClaimInstalment(ClaimInstalmentPO claimInstalmentPO) throws BizException;
}
