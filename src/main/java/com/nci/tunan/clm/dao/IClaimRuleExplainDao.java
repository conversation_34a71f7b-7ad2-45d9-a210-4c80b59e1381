package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ClaimRuleExplainPO;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimRuleExplainDao接口
 * <AUTHOR> 
 * @date 2022-07-09 16:07:12  
 */
 public interface IClaimRuleExplainDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return ClaimRuleExplainPO 添加结果
     */
	 public ClaimRuleExplainPO addClaimRuleExplain(ClaimRuleExplainPO claimRuleExplainPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRuleExplain(ClaimRuleExplainPO claimRuleExplainPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return ClaimRuleExplainPO 修改结果
     */
	 public ClaimRuleExplainPO updateClaimRuleExplain(ClaimRuleExplainPO claimRuleExplainPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return ClaimRuleExplainPO 查询结果对象
     */
	 public ClaimRuleExplainPO findClaimRuleExplain(ClaimRuleExplainPO claimRuleExplainPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return List<ClaimRuleExplainPO> 查询结果List
     */
	 public List<ClaimRuleExplainPO> findAllClaimRuleExplain(ClaimRuleExplainPO claimRuleExplainPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRuleExplainTotal(ClaimRuleExplainPO claimRuleExplainPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRuleExplainPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRuleExplainPO> queryClaimRuleExplainForPage(ClaimRuleExplainPO claimRuleExplainPO, CurrentPage<ClaimRuleExplainPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRuleExplain(List<ClaimRuleExplainPO> claimRuleExplainPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRuleExplain(List<ClaimRuleExplainPO> claimRuleExplainPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRuleExplain(List<ClaimRuleExplainPO> claimRuleExplainPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRuleExplain(ClaimRuleExplainPO claimRuleExplainPO);
	 
 }
 