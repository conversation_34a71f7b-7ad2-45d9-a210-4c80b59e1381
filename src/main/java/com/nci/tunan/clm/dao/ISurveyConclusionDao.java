package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.SurveyConclusionPO;
import com.nci.tunan.clm.interfaces.model.po.SurveyPositiveTracePO;
import com.nci.udmp.framework.model.*;

import java.util.Map;


import java.util.List;



/** 
 * @description ISurveyConclusionDao调查结论表接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-08-25 09:40:02  
 */
 public interface ISurveyConclusionDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return SurveyConclusionPO 添加结果
     */
	 public SurveyConclusionPO addSurveyConclusion(SurveyConclusionPO surveyConclusionPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteSurveyConclusion(SurveyConclusionPO surveyConclusionPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return SurveyConclusionPO 修改结果
     */
	 public SurveyConclusionPO updateSurveyConclusion(SurveyConclusionPO surveyConclusionPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return SurveyConclusionPO 查询结果对象
     */
	 public SurveyConclusionPO findSurveyConclusion(SurveyConclusionPO surveyConclusionPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return List<SurveyConclusionPO> 查询结果List
     */
	 public List<SurveyConclusionPO> findAllSurveyConclusion(SurveyConclusionPO surveyConclusionPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return int 查询结果条数
     */
	 public int findSurveyConclusionTotal(SurveyConclusionPO surveyConclusionPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SurveyConclusionPO> 查询结果的当前页对象
     */
	 public CurrentPage<SurveyConclusionPO> querySurveyConclusionForPage(SurveyConclusionPO surveyConclusionPO, CurrentPage<SurveyConclusionPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPOList 调查结论表对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSurveyConclusion(List<SurveyConclusionPO> surveyConclusionPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPOList 调查结论表对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSurveyConclusion(List<SurveyConclusionPO> surveyConclusionPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPOList 调查结论表列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSurveyConclusion(List<SurveyConclusionPO> surveyConclusionPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param surveyConclusionPO 调查结论表对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapSurveyConclusion(SurveyConclusionPO surveyConclusionPO);
	 /**
	  * 添加数据
	  * @description
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param paramSurveyPositiveTracePO 调查结论表对象
	  * @return 调查结论表对象
	  */
	 public SurveyPositiveTracePO addSurveyPositiveTraceClm(SurveyPositiveTracePO paramSurveyPositiveTracePO);
	 /**
	  * 查询所有
	  * @description
	  * @version V1.0.0
	  * @title
	  * <AUTHOR>
	  * @param paramSurveyPositiveTracePO  调查结论表对象
	  * @return 调查结论表对象集合
	  */
	 public List<SurveyPositiveTracePO> findAllSurveyPositiveTraceClm(SurveyPositiveTracePO paramSurveyPositiveTracePO);

	 /**
	  * 查询全部调查阳性结论或先赔标识修改轨迹
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param surveyPositiveTracePO
	  * @return
	  */
	 public List<SurveyPositiveTracePO> findAllSurveyPosAndPriTraceClm(SurveyPositiveTracePO surveyPositiveTracePO);
	 
 }
 