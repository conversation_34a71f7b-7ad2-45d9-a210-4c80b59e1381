package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimBfSurveyPlanPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description IClaimBfSurveyPlanDao接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:15:35
 */
 public interface IClaimBfSurveyPlanDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return ClaimBfSurveyPlanPO 添加结果
     */
	 public ClaimBfSurveyPlanPO addClaimBfSurveyPlan(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBfSurveyPlan(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return ClaimBfSurveyPlanPO 修改结果
     */
	 public ClaimBfSurveyPlanPO updateClaimBfSurveyPlan(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return ClaimBfSurveyPlanPO 查询结果对象
     */
	 public ClaimBfSurveyPlanPO findClaimBfSurveyPlan(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return List<ClaimBfSurveyPlanPO> 查询结果List
     */
	 public List<ClaimBfSurveyPlanPO> findAllClaimBfSurveyPlan(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBfSurveyPlanTotal(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBfSurveyPlanPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBfSurveyPlanPO> queryClaimBfSurveyPlanForPage(ClaimBfSurveyPlanPO claimBfSurveyPlanPO, CurrentPage<ClaimBfSurveyPlanPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBfSurveyPlan(List<ClaimBfSurveyPlanPO> claimBfSurveyPlanPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBfSurveyPlan(List<ClaimBfSurveyPlanPO> claimBfSurveyPlanPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBfSurveyPlan(List<ClaimBfSurveyPlanPO> claimBfSurveyPlanPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyPlanPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBfSurveyPlan(ClaimBfSurveyPlanPO claimBfSurveyPlanPO);
	 /**
	     * 
	     * @description 前置调查计划名称计划列表分页查询
	     * <AUTHOR> <EMAIL>
	     * @param claimBfSurveyPlanPO
	     *            ，currentPage 对象参数
	     * @return CurrentPage<ClaimBfSurveyPlanPO>
	     * @throws BizException
	     */
     public CurrentPage<ClaimBfSurveyPlanPO> queryBfSurveyPlanMsg(ClaimBfSurveyPlanPO claimBfSurveyPlanPO,
            CurrentPage<ClaimBfSurveyPlanPO> currentPage);
	 
 }
 