package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimRuleExplainLogPO;


/** 
 * @description IClaimRuleExplainLogDao接口
 * <AUTHOR> 
 * @date 2022-07-09 16:07:58  
 */
 public interface IClaimRuleExplainLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return ClaimRuleExplainLogPO 添加结果
     */
	 public ClaimRuleExplainLogPO addClaimRuleExplainLog(ClaimRuleExplainLogPO claimRuleExplainLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRuleExplainLog(ClaimRuleExplainLogPO claimRuleExplainLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return ClaimRuleExplainLogPO 修改结果
     */
	 public ClaimRuleExplainLogPO updateClaimRuleExplainLog(ClaimRuleExplainLogPO claimRuleExplainLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return ClaimRuleExplainLogPO 查询结果对象
     */
	 public ClaimRuleExplainLogPO findClaimRuleExplainLog(ClaimRuleExplainLogPO claimRuleExplainLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return List<ClaimRuleExplainLogPO> 查询结果List
     */
	 public List<ClaimRuleExplainLogPO> findAllClaimRuleExplainLog(ClaimRuleExplainLogPO claimRuleExplainLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRuleExplainLogTotal(ClaimRuleExplainLogPO claimRuleExplainLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRuleExplainLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRuleExplainLogPO> queryClaimRuleExplainLogForPage(ClaimRuleExplainLogPO claimRuleExplainLogPO, CurrentPage<ClaimRuleExplainLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRuleExplainLog(List<ClaimRuleExplainLogPO> claimRuleExplainLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRuleExplainLog(List<ClaimRuleExplainLogPO> claimRuleExplainLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRuleExplainLog(List<ClaimRuleExplainLogPO> claimRuleExplainLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRuleExplainLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRuleExplainLog(ClaimRuleExplainLogPO claimRuleExplainLogPO);
	 
 }
 