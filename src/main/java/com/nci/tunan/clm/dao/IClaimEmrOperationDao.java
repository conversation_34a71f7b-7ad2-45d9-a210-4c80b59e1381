package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimEmrOperationPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimEmrOperationDao接口
 * <AUTHOR> 
 * @date 2021-03-23 15:18:39  
 */
 public interface IClaimEmrOperationDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return ClaimEmrOperationPO 添加结果
     */
	 public ClaimEmrOperationPO addClaimEmrOperation(ClaimEmrOperationPO claimEmrOperationPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimEmrOperation(ClaimEmrOperationPO claimEmrOperationPO);
	 /**
 	  * @description 删除数据根据CaseId
 	  * @version
 	  * @title
 	  * <AUTHOR>
 	  * @param claimEmrOperationPO 对象
 	  * @return boolean 删除是否成功
 	  */
 	 public boolean deleteClaimEmrOperationByCaseId(ClaimEmrOperationPO claimEmrOperationPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return ClaimEmrOperationPO 修改结果
     */
	 public ClaimEmrOperationPO updateClaimEmrOperation(ClaimEmrOperationPO claimEmrOperationPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return ClaimEmrOperationPO 查询结果对象
     */
	 public ClaimEmrOperationPO findClaimEmrOperation(ClaimEmrOperationPO claimEmrOperationPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return List<ClaimEmrOperationPO> 查询结果List
     */
	 public List<ClaimEmrOperationPO> findAllClaimEmrOperation(ClaimEmrOperationPO claimEmrOperationPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimEmrOperationTotal(ClaimEmrOperationPO claimEmrOperationPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimEmrOperationPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimEmrOperationPO> queryClaimEmrOperationForPage(ClaimEmrOperationPO claimEmrOperationPO, CurrentPage<ClaimEmrOperationPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimEmrOperation(List<ClaimEmrOperationPO> claimEmrOperationPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimEmrOperation(List<ClaimEmrOperationPO> claimEmrOperationPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimEmrOperation(List<ClaimEmrOperationPO> claimEmrOperationPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimEmrOperationPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimEmrOperation(ClaimEmrOperationPO claimEmrOperationPO);
	 
 }
 