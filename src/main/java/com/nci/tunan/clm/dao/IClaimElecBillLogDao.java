package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimElecBillLogPO;


/** 
 * @description IClaimElecBillLogDao接口
 * <AUTHOR> 
 * @date 2024-04-16 16:48:23  
 */
 public interface IClaimElecBillLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return ClaimElecBillLogPO 添加结果
     */
	 public ClaimElecBillLogPO addClaimElecBillLog(ClaimElecBillLogPO claimElecBillLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimElecBillLog(ClaimElecBillLogPO claimElecBillLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return ClaimElecBillLogPO 修改结果
     */
	 public ClaimElecBillLogPO updateClaimElecBillLog(ClaimElecBillLogPO claimElecBillLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return ClaimElecBillLogPO 查询结果对象
     */
	 public ClaimElecBillLogPO findClaimElecBillLog(ClaimElecBillLogPO claimElecBillLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return List<ClaimElecBillLogPO> 查询结果List
     */
	 public List<ClaimElecBillLogPO> findAllClaimElecBillLog(ClaimElecBillLogPO claimElecBillLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimElecBillLogTotal(ClaimElecBillLogPO claimElecBillLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimElecBillLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimElecBillLogPO> queryClaimElecBillLogForPage(ClaimElecBillLogPO claimElecBillLogPO, CurrentPage<ClaimElecBillLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimElecBillLog(List<ClaimElecBillLogPO> claimElecBillLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimElecBillLog(List<ClaimElecBillLogPO> claimElecBillLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimElecBillLog(List<ClaimElecBillLogPO> claimElecBillLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecBillLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimElecBillLog(ClaimElecBillLogPO claimElecBillLogPO);
	 
 }
 