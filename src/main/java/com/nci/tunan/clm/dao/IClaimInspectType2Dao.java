package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimInspectType2PO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimInspectType2Dao接口
 * <AUTHOR> 
 * @date 2023-06-27 14:53:09  
 */
 public interface IClaimInspectType2Dao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return ClaimInspectType2PO 添加结果
     */
	 public ClaimInspectType2PO addClaimInspectType2(ClaimInspectType2PO claimInspectType2PO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInspectType2(ClaimInspectType2PO claimInspectType2PO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return ClaimInspectType2PO 修改结果
     */
	 public ClaimInspectType2PO updateClaimInspectType2(ClaimInspectType2PO claimInspectType2PO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return ClaimInspectType2PO 查询结果对象
     */
	 public ClaimInspectType2PO findClaimInspectType2(ClaimInspectType2PO claimInspectType2PO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return List<ClaimInspectType2PO> 查询结果List
     */
	 public List<ClaimInspectType2PO> findAllClaimInspectType2(ClaimInspectType2PO claimInspectType2PO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInspectType2Total(ClaimInspectType2PO claimInspectType2PO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInspectType2PO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInspectType2PO> queryClaimInspectType2ForPage(ClaimInspectType2PO claimInspectType2PO, CurrentPage<ClaimInspectType2PO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2POList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInspectType2(List<ClaimInspectType2PO> claimInspectType2POList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2POList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInspectType2(List<ClaimInspectType2PO> claimInspectType2POList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2POList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInspectType2(List<ClaimInspectType2PO> claimInspectType2POList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectType2PO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInspectType2(ClaimInspectType2PO claimInspectType2PO);
	 
 }
 