package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimBenePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.ContractBenePO;
import com.nci.tunan.clm.interfaces.model.po.InsuredListPO;
import com.nci.tunan.clm.interfaces.model.po.PolicyHolderPO;
import com.nci.tunan.clm.interfaces.model.po.PremArapPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
/**
 * 
 * @description 匹配理算结果
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-匹配理算
 * @date 2015-05-15 下午4:46:39
 */
public interface IClaimMatchResutlDao {
	/**
	 * 
	 * @description 理赔计算信息查询
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO 赔案信息
	 * @return
	 * @throws BizException
	 */
    public List<ClaimCasePO> findClaimCase(ClaimCasePO claimCasePO)  throws BizException;
    /**
     * 
     * @description 理赔类型计算信息查询
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimLiabPO 理赔给付责任理算信息
     * @return
     * @throws BizException
     */
    public List<ClaimLiabPO> findClaimCaseByType(ClaimLiabPO claimLiabPO)  throws BizException;
    /**
     * 
     * @description 保单计算信息查询
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimBusiProdPO 理赔险种理算表
     * @return
     * @throws BizException
     */
    public List<ClaimBusiProdPO> findClaimBusiProd(ClaimBusiProdPO claimBusiProdPO)  throws BizException;
    /**
     * 
     * @description 匹配理算日志
     * <AUTHOR>
     * @param claimBusiProdPO 理赔险种理算表
     * @return
     * @throws BizException
     */
     public List<ClaimBusiProdPO> findClaimBusiProdLog(ClaimBusiProdPO claimBusiProdPO)  throws BizException;
    /**
    * 
    * @description 保项计算信息查询（分页）
    * <AUTHOR> <EMAIL> 
    * @param claimLiabPO  责任理算对象
    * @param currentPage 分页信息
    * @return CurrentPage<ClaimLiabPO> 结果集
    * @throws BizException
    */
    public CurrentPage<ClaimLiabPO> findClaimLiab(ClaimLiabPO claimLiabPO , CurrentPage<ClaimLiabPO> currentPage) throws BizException;
    /**
     * 
     * @description 保项计算信息查询
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimLiabPO 责任理算对象
     * @return
     * @throws BizException
     */
    public List<ClaimLiabPO> findClaimLiab(ClaimLiabPO claimLiabPO)  throws BizException;
    
	/**
	 * 
	 * @description 查询回退赔案前是否支付完成
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param premArapPO 应收应付对象
	 * @return
	 */
	public List<PremArapPO> findPremArapIsPayByCaseNO(PremArapPO premArapPO);
	
	/**
	 * 查询投保人信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyHolderPO 投保人信息
	 * @return
	 */
	public List<PolicyHolderPO> queryPolicyHolderInfo(PolicyHolderPO policyHolderPO);
	
	/**
	 * 查询被保人信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param insuredListPO  被保人信息
	 * @return
	 */
	public List<InsuredListPO> queryInsuredListInfo(InsuredListPO insuredListPO);
	/**
	 * 查询指定受益人信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractBenePO 受益人信息
	 * @return
	 */
	public List<ContractBenePO> queryContractBeneInfo(ContractBenePO contractBenePO);
	
	/**
	 * 查询受益人信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimBenePO   受益人信息
	 * @return
	 */
	public List<ClaimBenePO> queryBeneName(ClaimBenePO claimBenePO);
}
