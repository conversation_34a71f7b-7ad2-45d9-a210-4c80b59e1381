package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimPayDeductionPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;

/**
 * @description 赔付金额扣减明细表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:52:17
 */
 public interface IClaimPayDeductionDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return ClaimPayDeductionPO 添加结果
     */
	 public ClaimPayDeductionPO addClaimPayDeduction(ClaimPayDeductionPO claimPayDeductionPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPayDeduction(ClaimPayDeductionPO claimPayDeductionPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return ClaimPayDeductionPO 修改结果
     */
	 public ClaimPayDeductionPO updateClaimPayDeduction(ClaimPayDeductionPO claimPayDeductionPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return ClaimPayDeductionPO 查询结果对象
     */
	 public ClaimPayDeductionPO findClaimPayDeduction(ClaimPayDeductionPO claimPayDeductionPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return List<ClaimPayDeductionPO> 查询结果List
     */
	 public List<ClaimPayDeductionPO> findAllClaimPayDeduction(ClaimPayDeductionPO claimPayDeductionPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return int 查询结果条数
     */
	 public int findClaimPayDeductionTotal(ClaimPayDeductionPO claimPayDeductionPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPayDeductionPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPayDeductionPO> queryClaimPayDeductionForPage(ClaimPayDeductionPO claimPayDeductionPO, CurrentPage<ClaimPayDeductionPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPOList 赔付金额扣减明细对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPayDeduction(List<ClaimPayDeductionPO> claimPayDeductionPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPOList 赔付金额扣减明细对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPayDeduction(List<ClaimPayDeductionPO> claimPayDeductionPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPOList 赔付金额扣减明细对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPayDeduction(List<ClaimPayDeductionPO> claimPayDeductionPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 赔付金额扣减明细对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPayDeduction(ClaimPayDeductionPO claimPayDeductionPO);
	 /**
     * @description 根据赔案ID删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayDeductionPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteClaimPayDeductionByCaseId(ClaimPayDeductionPO claimPayDeductionPO);
 }
 