package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.AgentLevelDivisionPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IAgentLevelDivisionDao接口
 * <AUTHOR> 
 * @date 2020-10-16 17:15:33  
 */
 public interface IAgentLevelDivisionDao {
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteAgentLevelDivision(AgentLevelDivisionPO agentLevelDivisionPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return AgentLevelDivisionPO 修改结果
     */
	 public AgentLevelDivisionPO updateAgentLevelDivision(AgentLevelDivisionPO agentLevelDivisionPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return AgentLevelDivisionPO 查询结果对象
     */
	 public AgentLevelDivisionPO findAgentLevelDivision(AgentLevelDivisionPO agentLevelDivisionPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return List<AgentLevelDivisionPO> 查询结果List
     */
	 public List<AgentLevelDivisionPO> findAllAgentLevelDivision(AgentLevelDivisionPO agentLevelDivisionPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return int 查询结果条数
     */
	 public int findAgentLevelDivisionTotal(AgentLevelDivisionPO agentLevelDivisionPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<AgentLevelDivisionPO> 查询结果的当前页对象
     */
	 public CurrentPage<AgentLevelDivisionPO> queryAgentLevelDivisionForPage(AgentLevelDivisionPO agentLevelDivisionPO, CurrentPage<AgentLevelDivisionPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveAgentLevelDivision(List<AgentLevelDivisionPO> agentLevelDivisionPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateAgentLevelDivision(List<AgentLevelDivisionPO> agentLevelDivisionPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteAgentLevelDivision(List<AgentLevelDivisionPO> agentLevelDivisionPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapAgentLevelDivision(AgentLevelDivisionPO agentLevelDivisionPO);
	 
     /**
     * @description 查询所有数据-保单库
     * @version
     * @title
     * <AUTHOR>
     * @param agentLevelDivisionPO 对象
     * @return List<AgentLevelDivisionPO> 查询结果List
     */
	 public List<AgentLevelDivisionPO> findAllAgentLevelDivisionPas(AgentLevelDivisionPO agentLevelDivisionPO);
	 
 }
 