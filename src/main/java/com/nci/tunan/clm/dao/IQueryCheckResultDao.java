package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;
/**
 * 质检结果
 * @description 
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 上午11:18:00
 */
public interface IQueryCheckResultDao {
	/**
	 * 
	 * @description 查询明细
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param checkResult 质检结果
	 * @return
	 */
	public List<Map<String, Object>> queryCheckResult(
			Map<String, Object> checkResult);

	/**
	 * 
	 * @description 查询质检件数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param checkResult 质检结果
	 * @return 
	 */
	public Map<String, Object> queryCheckSum(Map<String, Object> checkResult);

	/**
	 * 
	 * @description 查询质检问题件数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param checkResult 质检结果
	 * @return
	 */
	public Map<String, Object> queryCheckFailSum(Map<String, Object> checkResult);

	/**
	 * 
	 * @description 查询总件数
	 * @version V1.0.0 
	 * @title
	 * <AUTHOR>
	 * @param checkResult 质检结果
	 * @return
	 */
	public Map<String, Object> queryCheckTotal(Map<String, Object> checkResult);

	/**
	 * 
	 * @description 查询问题件明细
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param checkResult 质检结果
	 * @return
	 */
	public List<Map<String, Object>> queryMemoDetail(
			Map<String, Object> checkResult);

	/**
	 *  查询问题件总数
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param checkResult 质检结果
	 * @return
	 */
	public Map<String, Object> queryMemoTotal(Map<String, Object> checkResult);
}
