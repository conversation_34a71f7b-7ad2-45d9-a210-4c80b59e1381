package com.nci.tunan.clm.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.SpecialDiseaseTypePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ISpecialDiseaseTypeDao特定费用类型代码接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-07-15 15:16:38  
 */
 public interface ISpecialDiseaseTypeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return SpecialDiseaseTypePO 添加结果
     */
	 public SpecialDiseaseTypePO addSpecialDiseaseType(SpecialDiseaseTypePO specialDiseaseTypePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteSpecialDiseaseType(SpecialDiseaseTypePO specialDiseaseTypePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return SpecialDiseaseTypePO 修改结果
     */
	 public SpecialDiseaseTypePO updateSpecialDiseaseType(SpecialDiseaseTypePO specialDiseaseTypePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return SpecialDiseaseTypePO 查询结果对象
     */
	 public SpecialDiseaseTypePO findSpecialDiseaseType(SpecialDiseaseTypePO specialDiseaseTypePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return List<SpecialDiseaseTypePO> 查询结果List
     */
	 public List<SpecialDiseaseTypePO> findAllSpecialDiseaseType(SpecialDiseaseTypePO specialDiseaseTypePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return int 查询结果条数
     */
	 public int findSpecialDiseaseTypeTotal(SpecialDiseaseTypePO specialDiseaseTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SpecialDiseaseTypePO> 查询结果的当前页对象
     */
	 public CurrentPage<SpecialDiseaseTypePO> querySpecialDiseaseTypeForPage(SpecialDiseaseTypePO specialDiseaseTypePO, CurrentPage<SpecialDiseaseTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePOList 特定费用类型代码对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSpecialDiseaseType(List<SpecialDiseaseTypePO> specialDiseaseTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePOList 特定费用类型代码对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSpecialDiseaseType(List<SpecialDiseaseTypePO> specialDiseaseTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSpecialDiseaseType(List<SpecialDiseaseTypePO> specialDiseaseTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseTypePO 特定费用类型代码对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapSpecialDiseaseType(SpecialDiseaseTypePO specialDiseaseTypePO);
	 
 }
 