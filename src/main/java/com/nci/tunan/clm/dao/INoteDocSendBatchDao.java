package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.NoteDocSendBatchPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description INoteDocSendBatchDao接口
 * <AUTHOR> 
 * @date 2023-10-10 15:45:06  
 */
 public interface INoteDocSendBatchDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return NoteDocSendBatchPO 添加结果
     */
	 public NoteDocSendBatchPO addNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return NoteDocSendBatchPO 修改结果
     */
	 public NoteDocSendBatchPO updateNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return NoteDocSendBatchPO 查询结果对象
     */
	 public NoteDocSendBatchPO findNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
	 
	 /**
     * @description 根据list_id查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return NoteDocSendBatchPO 查询结果对象
     */
	 public NoteDocSendBatchPO findNoteDocSendBatchByListId(NoteDocSendBatchPO noteDocSendBatchPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return List<NoteDocSendBatchPO> 查询结果List
     */
	 public List<NoteDocSendBatchPO> findAllNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return int 查询结果条数
     */
	 public int findNoteDocSendBatchTotal(NoteDocSendBatchPO noteDocSendBatchPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<NoteDocSendBatchPO> 查询结果的当前页对象
     */
	 public CurrentPage<NoteDocSendBatchPO> queryNoteDocSendBatchForPage(NoteDocSendBatchPO noteDocSendBatchPO, CurrentPage<NoteDocSendBatchPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveNoteDocSendBatch(List<NoteDocSendBatchPO> noteDocSendBatchPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateNoteDocSendBatch(List<NoteDocSendBatchPO> noteDocSendBatchPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteNoteDocSendBatch(List<NoteDocSendBatchPO> noteDocSendBatchPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
	 
	 
	 /**
     * @description 删除回退案件的数据
     * @version
     * @title
     * <AUTHOR>
     * @param noteDocSendBatchPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteNoteDocSendBatchByIsBack(NoteDocSendBatchPO noteDocSendBatchPO);
 	 
 	 
 	/**
      * @description 查询符合条件的数据
      * @version
      * @title
      * <AUTHOR>
      * @param noteDocSendBatchPO 对象
      * @return List<NoteDocSendBatchPO> 查询结果List
      */
 	 public List<NoteDocSendBatchPO> findAllssNoteDocSendBatch(NoteDocSendBatchPO noteDocSendBatchPO);
 	 
 	/**
      * @description 查询符合短信发送的所有数据
      * @version
      * @title
      * <AUTHOR>
      * @param noteDocSendBatchPO 对象
      * @return List<NoteDocSendBatchPO> 查询结果List
      */
 	 public List<NoteDocSendBatchPO> findAllssNoteDocBatch(NoteDocSendBatchPO noteDocSendBatchPO);
 	 
 	/**
      * @description 查询符合短信发送的所有数据
      * @version
      * @title
      * <AUTHOR>
      * @param noteDocSendBatchPO 对象
      * @return List<NoteDocSendBatchPO> 查询结果List
      */
 	 public List<NoteDocSendBatchPO> findAllssNoteDocBatchs(NoteDocSendBatchPO noteDocSendBatchPO);
 	 
 	 /**
      * @description 查询符合短信发送的所有数据
      * @version
      * @title
      * <AUTHOR>
      * @param noteDocSendBatchPO 对象
      * @return List<NoteDocSendBatchPO> 查询结果List
      */
 	 public List<NoteDocSendBatchPO> findAllssNoteDocBatchss(NoteDocSendBatchPO noteDocSendBatchPO);

 	 /**
      * @description 查询需要生成赔付依据与说明通知书pdf文件的数据
      * @version
      * @title
      * <AUTHOR>
      * @param noteDocSendBatchPO 对象
      * @return List<NoteDocSendBatchPO> 查询结果List
      */
	 public List<NoteDocSendBatchPO> queryCreatePayDocPDF(NoteDocSendBatchPO noteDocSendBatchPO);

	 /**
      * @description 查询已产生赔付依据与说明通知书pdf文件，但还未发送短信成功的数据
      * @version
      * @title
      * <AUTHOR>
      * @param noteDocSendBatchPO 对象
      * @return List<NoteDocSendBatchPO> 查询结果List
      */
	 public List<NoteDocSendBatchPO> querySendPayDocTXT(NoteDocSendBatchPO noteDocSendBatchPO);

 }
 