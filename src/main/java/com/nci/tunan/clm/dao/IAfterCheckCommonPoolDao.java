package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
/**
 * 
 * @description  事后质检共享池接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-质检
 * @date 2015-05-15 下午1:27:56
 */
public interface IAfterCheckCommonPoolDao {
	/**
	 * 
	 * @description   根据赔案号查询
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2015-05-15 下午1:28:16 
	 * @param claimCasePO  赔案主表对象
	 * @return  List<ClaimCasePO>   赔案主表对象集合
	 */
	List<ClaimCasePO> findAllClaimCaseByCaseNo(ClaimCasePO claimCasePO);
	/**
	 *  
	 * @description   时候质检
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2015-05-15 下午1:29:20 
	 * @param claimCasePO   赔案主表对象
	 * @return  List<ClaimCasePO>   赔案主表对象集合
	 */
	List<ClaimCasePO> findAllClaimCaseForSelf(ClaimCasePO claimCasePO);

}
