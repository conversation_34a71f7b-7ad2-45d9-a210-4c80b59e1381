package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimDirectConnBusiPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimDirectConnBusiDao接口
 * <AUTHOR> 
 * @date 2021-11-08 11:12:29  
 */
 public interface IClaimDirectConnBusiDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return ClaimDirectConnBusiPO 添加结果
     */
	 public ClaimDirectConnBusiPO addClaimDirectConnBusi(ClaimDirectConnBusiPO claimDirectConnBusiPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectConnBusi(ClaimDirectConnBusiPO claimDirectConnBusiPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return ClaimDirectConnBusiPO 修改结果
     */
	 public ClaimDirectConnBusiPO updateClaimDirectConnBusi(ClaimDirectConnBusiPO claimDirectConnBusiPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return ClaimDirectConnBusiPO 查询结果对象
     */
	 public ClaimDirectConnBusiPO findClaimDirectConnBusi(ClaimDirectConnBusiPO claimDirectConnBusiPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return List<ClaimDirectConnBusiPO> 查询结果List
     */
	 public List<ClaimDirectConnBusiPO> findAllClaimDirectConnBusi(ClaimDirectConnBusiPO claimDirectConnBusiPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectConnBusiTotal(ClaimDirectConnBusiPO claimDirectConnBusiPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectConnBusiPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectConnBusiPO> queryClaimDirectConnBusiForPage(ClaimDirectConnBusiPO claimDirectConnBusiPO, CurrentPage<ClaimDirectConnBusiPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectConnBusi(List<ClaimDirectConnBusiPO> claimDirectConnBusiPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectConnBusi(List<ClaimDirectConnBusiPO> claimDirectConnBusiPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectConnBusi(List<ClaimDirectConnBusiPO> claimDirectConnBusiPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnBusiPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectConnBusi(ClaimDirectConnBusiPO claimDirectConnBusiPO);
	 
 }
 