package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimPayChangeLogPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 
 * @description 付费变更轨迹表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:51:22
 */
 public interface IClaimPayChangeLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return ClaimPayChangeLogPO 添加结果
     */
	 public ClaimPayChangeLogPO addClaimPayChangeLog(ClaimPayChangeLogPO claimPayChangeLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPayChangeLog(ClaimPayChangeLogPO claimPayChangeLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return ClaimPayChangeLogPO 修改结果
     */
	 public ClaimPayChangeLogPO updateClaimPayChangeLog(ClaimPayChangeLogPO claimPayChangeLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return ClaimPayChangeLogPO 查询结果对象
     */
	 public ClaimPayChangeLogPO findClaimPayChangeLog(ClaimPayChangeLogPO claimPayChangeLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return List<ClaimPayChangeLogPO> 查询结果List
     */
	 public List<ClaimPayChangeLogPO> findAllClaimPayChangeLog(ClaimPayChangeLogPO claimPayChangeLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return int 查询结果条数
     */
	 public int findClaimPayChangeLogTotal(ClaimPayChangeLogPO claimPayChangeLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPayChangeLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPayChangeLogPO> queryClaimPayChangeLogForPage(ClaimPayChangeLogPO claimPayChangeLogPO, CurrentPage<ClaimPayChangeLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPOList 付费变更轨迹对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPayChangeLog(List<ClaimPayChangeLogPO> claimPayChangeLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPOList 付费变更轨迹对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPayChangeLog(List<ClaimPayChangeLogPO> claimPayChangeLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPOList 付费变更轨迹对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPayChangeLog(List<ClaimPayChangeLogPO> claimPayChangeLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayChangeLogPO 付费变更轨迹对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPayChangeLog(ClaimPayChangeLogPO claimPayChangeLogPO);
	 /**
	     * @description 修改数据
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimPayChangeLogPO 付费变更轨迹对象
	     * @return ClaimPayChangeLogPO 付费变更轨迹修改结果
	     */
	 public ClaimPayChangeLogPO updateClaimPayChangeLogByApplyId(ClaimPayChangeLogPO claimPayChangeLogPO);
 }
 