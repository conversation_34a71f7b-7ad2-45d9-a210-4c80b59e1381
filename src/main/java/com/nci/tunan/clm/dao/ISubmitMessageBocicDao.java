package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO;
import com.nci.tunan.clm.interfaces.model.po.SubmitMessageBocicPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ISubmitMessageBocicDao接口
 * <AUTHOR> 
 * @date 2022-11-09 16:36:41  
 */
 public interface ISubmitMessageBocicDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return SubmitMessageBocicPO 添加结果
     */
	 public SubmitMessageBocicPO addSubmitMessageBocic(SubmitMessageBocicPO submitMessageBocicPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteSubmitMessageBocic(SubmitMessageBocicPO submitMessageBocicPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return SubmitMessageBocicPO 修改结果
     */
	 public SubmitMessageBocicPO updateSubmitMessageBocic(SubmitMessageBocicPO submitMessageBocicPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return SubmitMessageBocicPO 查询结果对象
     */
	 public SubmitMessageBocicPO findSubmitMessageBocic(SubmitMessageBocicPO submitMessageBocicPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return List<SubmitMessageBocicPO> 查询结果List
     */
	 public List<SubmitMessageBocicPO> findAllSubmitMessageBocic(SubmitMessageBocicPO submitMessageBocicPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return int 查询结果条数
     */
	 public int findSubmitMessageBocicTotal(SubmitMessageBocicPO submitMessageBocicPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SubmitMessageBocicPO> 查询结果的当前页对象
     */
	 public CurrentPage<SubmitMessageBocicPO> querySubmitMessageBocicForPage(SubmitMessageBocicPO submitMessageBocicPO, CurrentPage<SubmitMessageBocicPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSubmitMessageBocic(List<SubmitMessageBocicPO> submitMessageBocicPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSubmitMessageBocic(List<SubmitMessageBocicPO> submitMessageBocicPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSubmitMessageBocic(List<SubmitMessageBocicPO> submitMessageBocicPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapSubmitMessageBocic(SubmitMessageBocicPO submitMessageBocicPO);
	 /**
     * @description 查询批处理数据
     * @version
     * @title
     * <AUTHOR>
     * @param submitMessageBocicPO 对象
     * @return List<SubmitMessageBocicPO> 查询结果List
     */
	 public List<SubmitMessageBocicPO> findAllSubmitMessageBocicBatch(SubmitMessageBocicPO submitMessageBocicPO);
	 /**
	 * @description 查询报送出险日期
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param submitMessageBocicPO 对象
	 * @return List<SubmitMessageBocicPO> 查询结果List
	 */
	 public SubmitMessageBocicPO findSubmitMessageClaimDate(SubmitMessageBocicPO submitMessageBocicPO);
	 /**
	 * @description 查询报送应赔付金额
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param submitMessageBocicPO 对象
	 * @return List<SubmitMessageBocicPO> 查询结果List
	 */
	 public SubmitMessageBocicPO findSubmitMessageClaimAmount(SubmitMessageBocicPO submitMessageBocicPO);
	 /**
	  * @description 查询死亡理赔类型数量
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param submitMessageBocicPO 对象
	  * @return List<SubmitMessageBocicPO> 查询结果List
	  */
	 public SubmitMessageBocicPO findSubmitMessageDeathIndiCount(SubmitMessageBocicPO submitMessageBocicPO);
	 /**
	  * @description 查询死亡日期
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param submitMessageBocicPO 对象
	  * @return List<SubmitMessageBocicPO> 查询结果List
	  */
	 public SubmitMessageBocicPO findSubmitMessageDeathDate(SubmitMessageBocicPO submitMessageBocicPO);
 }
 