package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimDocPrintLogPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimDocPrintLogDao接口
 * <AUTHOR> 
 * @date 2023-10-10 15:44:45  
 */
 public interface IClaimDocPrintLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return ClaimDocPrintLogPO 添加结果
     */
	 public ClaimDocPrintLogPO addClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return ClaimDocPrintLogPO 修改结果
     */
	 public ClaimDocPrintLogPO updateClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return ClaimDocPrintLogPO 查询结果对象
     */
	 public ClaimDocPrintLogPO findClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return List<ClaimDocPrintLogPO> 查询结果List
     */
	 public List<ClaimDocPrintLogPO> findAllClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDocPrintLogTotal(ClaimDocPrintLogPO claimDocPrintLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDocPrintLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDocPrintLogPO> queryClaimDocPrintLogForPage(ClaimDocPrintLogPO claimDocPrintLogPO, CurrentPage<ClaimDocPrintLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDocPrintLog(List<ClaimDocPrintLogPO> claimDocPrintLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDocPrintLog(List<ClaimDocPrintLogPO> claimDocPrintLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDocPrintLog(List<ClaimDocPrintLogPO> claimDocPrintLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocPrintLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDocPrintLog(ClaimDocPrintLogPO claimDocPrintLogPO);
	 
 }
 