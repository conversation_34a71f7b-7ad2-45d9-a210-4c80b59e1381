package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimBillEntryPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimElecbillInfoPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimElecbillInfoDao接口
 * <AUTHOR> 
 * @date 2024-10-24 16:27:22  
 */
public interface IClaimElecbillInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return ClaimElecbillInfoPO 添加结果
     */
	 public ClaimElecbillInfoPO addClaimElecbillInfo(ClaimElecbillInfoPO claimElecbillInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimElecbillInfo(ClaimElecbillInfoPO claimElecbillInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return ClaimElecbillInfoPO 修改结果
     */
	 public ClaimElecbillInfoPO updateClaimElecbillInfo(ClaimElecbillInfoPO claimElecbillInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return ClaimElecbillInfoPO 查询结果对象
     */
	 public ClaimElecbillInfoPO findClaimElecbillInfo(ClaimElecbillInfoPO claimElecbillInfoPO);
	 
     /**
     * @description 根据CaseId查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return ClaimElecbillInfoPO 查询结果对象
     */
	 public ClaimElecbillInfoPO findClaimElecbillInfoByCaseId(ClaimElecbillInfoPO claimElecbillInfoPO);
 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return List<ClaimElecbillInfoPO> 查询结果List
     */
	 public List<ClaimElecbillInfoPO> findAllClaimElecbillInfo(ClaimElecbillInfoPO claimElecbillInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimElecbillInfoTotal(ClaimElecbillInfoPO claimElecbillInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimElecbillInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimElecbillInfoPO> queryClaimElecbillInfoForPage(ClaimElecbillInfoPO claimElecbillInfoPO, CurrentPage<ClaimElecbillInfoPO> currentPage);
	 

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimElecbillInfo(List<ClaimElecbillInfoPO> claimElecbillInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimElecbillInfo(List<ClaimElecbillInfoPO> claimElecbillInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimElecbillInfo(List<ClaimElecbillInfoPO> claimElecbillInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimElecbillInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimElecbillInfo(ClaimElecbillInfoPO claimElecbillInfoPO);
}
