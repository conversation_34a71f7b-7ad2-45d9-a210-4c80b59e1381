package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPolicyPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimProductPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
/**
 * 
 * @description  豁免处理Dao
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午8:51:10
 */
public interface IClaimRemitManageDao {
/**
 * 查询保单
 * @description
 * @version
 * @title
 * <AUTHOR> <EMAIL> 
 * @param po  保单
 * @return 保单集合
 * @throws BizException
 */
	public List<ClaimPolicyPO> queryProdAndPolicyMsg(ClaimPolicyPO po) throws BizException;
/**
 * 查询理赔表
 * @description
 * @version
 * @title
 * <AUTHOR> <EMAIL> 
 * @param po 险种
 * @return 险种集合
 * @throws BizException
 */
	public List<ClaimBusiProdPO>queryBusiProdMsg(ClaimBusiProdPO po)throws BizException;

	/**
	 * 
	 * @description  保存豁免处理
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param po  险种
	 * @throws BizException
	 */
	public void saveRemitManage(ClaimProductPO po) throws BizException;
	/**
     * 
     * @description 豁免处理列表分页查询
     * <AUTHOR> <EMAIL>
     * @param claimCasePO  赔案信息
     * @param   currentPage 对象参数
     * @return CurrentPage<ClaimCasePO>
     * @throws BizException
     */
    public CurrentPage<ClaimCasePO> queryWaiverManageInfo(ClaimCasePO claimCasePO,
            CurrentPage<ClaimCasePO> currentPage)throws BizException;
}
