package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimVariableInfoPO;


/** 
 * @description IClaimVariableInfoDao接口
 * <AUTHOR> 
 * @date 2024-04-16 16:53:39  
 */
 public interface IClaimVariableInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return ClaimVariableInfoPO 添加结果
     */
	 public ClaimVariableInfoPO addClaimVariableInfo(ClaimVariableInfoPO claimVariableInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimVariableInfo(ClaimVariableInfoPO claimVariableInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return ClaimVariableInfoPO 修改结果
     */
	 public ClaimVariableInfoPO updateClaimVariableInfo(ClaimVariableInfoPO claimVariableInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return ClaimVariableInfoPO 查询结果对象
     */
	 public ClaimVariableInfoPO findClaimVariableInfo(ClaimVariableInfoPO claimVariableInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return List<ClaimVariableInfoPO> 查询结果List
     */
	 public List<ClaimVariableInfoPO> findAllClaimVariableInfo(ClaimVariableInfoPO claimVariableInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimVariableInfoTotal(ClaimVariableInfoPO claimVariableInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimVariableInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimVariableInfoPO> queryClaimVariableInfoForPage(ClaimVariableInfoPO claimVariableInfoPO, CurrentPage<ClaimVariableInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimVariableInfo(List<ClaimVariableInfoPO> claimVariableInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimVariableInfo(List<ClaimVariableInfoPO> claimVariableInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimVariableInfo(List<ClaimVariableInfoPO> claimVariableInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimVariableInfo(ClaimVariableInfoPO claimVariableInfoPO);

	 /**
     * @description 根据key查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimVariableInfoPO 对象
     * @return ClaimVariableInfoPO 查询结果对象
     */
	 public ClaimVariableInfoPO findClaimVariableInfoByKey(ClaimVariableInfoPO claimVariableInfoPO);

 }
 