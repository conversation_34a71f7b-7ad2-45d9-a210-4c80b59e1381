package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLibImportPO;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimRiskLibImportDao接口
 * <AUTHOR> 
 * @date 2023-02-06 10:46:01  
 */
 public interface IClaimRiskLibImportDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return ClaimRiskLibImportPO 添加结果
     */
	 public ClaimRiskLibImportPO addClaimRiskLibImport(ClaimRiskLibImportPO claimRiskLibImportPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskLibImport(ClaimRiskLibImportPO claimRiskLibImportPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return ClaimRiskLibImportPO 修改结果
     */
	 public ClaimRiskLibImportPO updateClaimRiskLibImport(ClaimRiskLibImportPO claimRiskLibImportPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return ClaimRiskLibImportPO 查询结果对象
     */
	 public ClaimRiskLibImportPO findClaimRiskLibImport(ClaimRiskLibImportPO claimRiskLibImportPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return List<ClaimRiskLibImportPO> 查询结果List
     */
	 public List<ClaimRiskLibImportPO> findAllClaimRiskLibImport(ClaimRiskLibImportPO claimRiskLibImportPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskLibImportTotal(ClaimRiskLibImportPO claimRiskLibImportPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskLibImportPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskLibImportPO> queryClaimRiskLibImportForPage(ClaimRiskLibImportPO claimRiskLibImportPO, CurrentPage<ClaimRiskLibImportPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskLibImport(List<ClaimRiskLibImportPO> claimRiskLibImportPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskLibImport(List<ClaimRiskLibImportPO> claimRiskLibImportPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskLibImport(List<ClaimRiskLibImportPO> claimRiskLibImportPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskLibImportPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskLibImport(ClaimRiskLibImportPO claimRiskLibImportPO);
	 
 }
 