package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimDocumentNoticePO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimDocumentNoticeDao接口
 * <AUTHOR> 
 * @date 2023-10-10 15:45:21  
 */
 public interface IClaimDocumentNoticeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return ClaimDocumentNoticePO 添加结果
     */
	 public ClaimDocumentNoticePO addClaimDocumentNotice(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDocumentNotice(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return ClaimDocumentNoticePO 修改结果
     */
	 public ClaimDocumentNoticePO updateClaimDocumentNotice(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return ClaimDocumentNoticePO 查询结果对象
     */
	 public ClaimDocumentNoticePO findClaimDocumentNotice(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return List<ClaimDocumentNoticePO> 查询结果List
     */
	 public List<ClaimDocumentNoticePO> findAllClaimDocumentNotice(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDocumentNoticeTotal(ClaimDocumentNoticePO claimDocumentNoticePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDocumentNoticePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDocumentNoticePO> queryClaimDocumentNoticeForPage(ClaimDocumentNoticePO claimDocumentNoticePO, CurrentPage<ClaimDocumentNoticePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDocumentNotice(List<ClaimDocumentNoticePO> claimDocumentNoticePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDocumentNotice(List<ClaimDocumentNoticePO> claimDocumentNoticePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDocumentNotice(List<ClaimDocumentNoticePO> claimDocumentNoticePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDocumentNotice(ClaimDocumentNoticePO claimDocumentNoticePO);

	 /**
     * @description 根据caseId查询指定数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return List<ClaimDocumentNoticePO> 查询结果List
     */
	 public List<ClaimDocumentNoticePO> findAllByCaseId(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
	 /**
     * @description 根据caseId查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return ClaimDocumentNoticePO 查询结果对象
     */
	 public ClaimDocumentNoticePO findClaimDocumentNoticeByCaseId(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
	 
	 /**
     * @description 根据sendName查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return ClaimDocumentNoticePO 查询结果对象
     */
	 public ClaimDocumentNoticePO findAllBySendName(ClaimDocumentNoticePO claimDocumentNoticePO);
 
 
	 /**
     * @description 根据ListId查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return ClaimDocumentNoticePO 查询结果对象
     */
	 public ClaimDocumentNoticePO findClaimDocumentNoticeByListId(ClaimDocumentNoticePO claimDocumentNoticePO);
 
	 
	 /**
     * @description 根据caseId查询指定数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDocumentNoticePO 对象
     * @return List<ClaimDocumentNoticePO> 查询结果List
     */
	 public List<ClaimDocumentNoticePO> findAllByCaseIdAndTpCode(ClaimDocumentNoticePO claimDocumentNoticePO);
	 
	 /**
	 * @description 根据赔案号和单证删除数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimDocumentNoticePO
	 * @return 
	 */
	 public boolean deleteClaimDocumentNoticeByCaseIdAndCode(ClaimDocumentNoticePO claimDocumentNoticePO);
 }
 