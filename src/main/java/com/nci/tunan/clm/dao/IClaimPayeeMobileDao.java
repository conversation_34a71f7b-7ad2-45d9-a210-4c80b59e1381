package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimPayeeMobilePO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;


/**
 * @description 移动新华领款人信息表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-移动新华领款人信息
 * @date 2015-05-15 下午4:53:23
 */
 public interface IClaimPayeeMobileDao {
	 /**
     * @description 增加移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return ClaimPayeeMobilePO 添加结果
     */
	 public ClaimPayeeMobilePO addClaimPayeeMobile(ClaimPayeeMobilePO claimPayeeMobilePO);
	 
     /**
     * @description 删除移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimPayeeMobile(ClaimPayeeMobilePO claimPayeeMobilePO);
	 
     /**
     * @description 修改移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return ClaimPayeeMobilePO 修改结果
     */
	 public ClaimPayeeMobilePO updateClaimPayeeMobile(ClaimPayeeMobilePO claimPayeeMobilePO);
	 
     /**
     * @description 查询单条移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return ClaimPayeeMobilePO 查询结果对象
     */
	 public ClaimPayeeMobilePO findClaimPayeeMobile(ClaimPayeeMobilePO claimPayeeMobilePO);
	 
     /**
     * @description 查询所有移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return List<ClaimPayeeMobilePO> 查询结果List
     */
	 public List<ClaimPayeeMobilePO> findAllClaimPayeeMobile(ClaimPayeeMobilePO claimPayeeMobilePO);
	 
     /**
     * @description 查询数据移动新华领款人信息条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return int 查询结果条数
     */
	 public int findClaimPayeeMobileTotal(ClaimPayeeMobilePO claimPayeeMobilePO);

     /**
     * @description 分页查询移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPayeeMobilePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimPayeeMobilePO> queryClaimPayeeMobileForPage(ClaimPayeeMobilePO claimPayeeMobilePO, CurrentPage<ClaimPayeeMobilePO> currentPage);

     /**
     * @description 批量增加移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePOList 移动新华领款人信息对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimPayeeMobile(List<ClaimPayeeMobilePO> claimPayeeMobilePOList);

     /**
     * @description 批量修改移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePOList 移动新华领款人信息对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimPayeeMobile(List<ClaimPayeeMobilePO> claimPayeeMobilePOList);

     /**
     * @description 批量删除移动新华领款人信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePOList 移动新华领款人信息对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimPayeeMobile(List<ClaimPayeeMobilePO> claimPayeeMobilePOList);

     /**
     * @description 查询所有移动新华领款人信息数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimPayeeMobilePO 移动新华领款人信息对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimPayeeMobile(ClaimPayeeMobilePO claimPayeeMobilePO);
	 
 }
 