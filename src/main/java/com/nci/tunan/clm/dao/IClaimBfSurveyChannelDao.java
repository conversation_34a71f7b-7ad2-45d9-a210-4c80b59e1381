package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimBfSurveyChannelPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;

/**
 * 
 * @description IClaimBfSurveyChannelDao接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:13:51
 */
 public interface IClaimBfSurveyChannelDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return ClaimBfSurveyChannelPO 添加结果
     */
	 public ClaimBfSurveyChannelPO addClaimBfSurveyChannel(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBfSurveyChannel(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return ClaimBfSurveyChannelPO 修改结果
     */
	 public ClaimBfSurveyChannelPO updateClaimBfSurveyChannel(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return ClaimBfSurveyChannelPO 查询结果对象
     */
	 public ClaimBfSurveyChannelPO findClaimBfSurveyChannel(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return List<ClaimBfSurveyChannelPO> 查询结果List
     */
	 public List<ClaimBfSurveyChannelPO> findAllClaimBfSurveyChannel(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBfSurveyChannelTotal(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBfSurveyChannelPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBfSurveyChannelPO> queryClaimBfSurveyChannelForPage(ClaimBfSurveyChannelPO claimBfSurveyChannelPO, CurrentPage<ClaimBfSurveyChannelPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBfSurveyChannel(List<ClaimBfSurveyChannelPO> claimBfSurveyChannelPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBfSurveyChannel(List<ClaimBfSurveyChannelPO> claimBfSurveyChannelPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBfSurveyChannel(List<ClaimBfSurveyChannelPO> claimBfSurveyChannelPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBfSurveyChannelPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBfSurveyChannel(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 /**
	     * @description 根据前置计划ID查询所有记录
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimBfSurveyChannelPO 对象
	     * @return List<ClaimBfSurveyChannelPO> 查询结果List
	     */
	 public List<ClaimBfSurveyChannelPO> findClaimBfSurveyChannelByPlanId(ClaimBfSurveyChannelPO claimBfSurveyChannelPO);
	 
 }
 