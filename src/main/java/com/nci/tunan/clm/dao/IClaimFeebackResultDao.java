package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimFeebackResultPO;


/** 
 * @description IClaimFeebackResultDao接口
 * <AUTHOR> 
 * @date 2024-10-09 11:05:46  
 */
 public interface IClaimFeebackResultDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return ClaimFeebackResultPO 添加结果
     */
	 public ClaimFeebackResultPO addClaimFeebackResult(ClaimFeebackResultPO claimFeebackResultPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimFeebackResult(ClaimFeebackResultPO claimFeebackResultPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return ClaimFeebackResultPO 修改结果
     */
	 public ClaimFeebackResultPO updateClaimFeebackResult(ClaimFeebackResultPO claimFeebackResultPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return ClaimFeebackResultPO 查询结果对象
     */
	 public ClaimFeebackResultPO findClaimFeebackResult(ClaimFeebackResultPO claimFeebackResultPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return List<ClaimFeebackResultPO> 查询结果List
     */
	 public List<ClaimFeebackResultPO> findAllClaimFeebackResult(ClaimFeebackResultPO claimFeebackResultPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimFeebackResultTotal(ClaimFeebackResultPO claimFeebackResultPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimFeebackResultPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimFeebackResultPO> queryClaimFeebackResultForPage(ClaimFeebackResultPO claimFeebackResultPO, CurrentPage<ClaimFeebackResultPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimFeebackResult(List<ClaimFeebackResultPO> claimFeebackResultPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimFeebackResult(List<ClaimFeebackResultPO> claimFeebackResultPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimFeebackResult(List<ClaimFeebackResultPO> claimFeebackResultPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimFeebackResult(ClaimFeebackResultPO claimFeebackResultPO);
	 
	 /**
     * @description 查询理赔结果反馈任务表中待发送和发送失败的数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeebackResultPO 对象
     * @return List<ClaimFeebackResultPO> 查询结果List
     */
	 public List<ClaimFeebackResultPO> queryFeedbackCases(ClaimFeebackResultPO claimFeebackResultPO);
	
 }
 