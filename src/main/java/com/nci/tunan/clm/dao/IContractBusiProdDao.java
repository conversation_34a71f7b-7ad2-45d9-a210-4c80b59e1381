package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ContractBusiProdPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description 保单险种抄单表DaoImpl
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-保单险种抄单
 * @date 2015-07-23 14:03:04  
 */
 public interface IContractBusiProdDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return ContractBusiProdPO 添加结果
     */
	 public ContractBusiProdPO addContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return ContractBusiProdPO 修改结果
     */
	 public ContractBusiProdPO updateContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return ContractBusiProdPO 查询结果对象
     */
	 public ContractBusiProdPO findContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	 
	 
	 /**
     * @description 查询保单主险数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return ContractBusiProdPO 查询结果对象
     */
     public ContractBusiProdPO findMainContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	     
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return List<ContractBusiProdPO> 查询结果List
     */
	 public List<ContractBusiProdPO> findAllContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return int 查询结果条数
     */
	 public int findContractBusiProdTotal(ContractBusiProdPO contractBusiProdPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ContractBusiProdPO> 查询结果的当前页对象
     */
	 public CurrentPage<ContractBusiProdPO> queryContractBusiProdForPage(ContractBusiProdPO contractBusiProdPO, CurrentPage<ContractBusiProdPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPOList 保单险种抄单对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveContractBusiProd(List<ContractBusiProdPO> contractBusiProdPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPOList 保单险种抄单对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateContractBusiProd(List<ContractBusiProdPO> contractBusiProdPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPOList 保单险种抄单对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteContractBusiProd(List<ContractBusiProdPO> contractBusiProdPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapContractBusiProd(ContractBusiProdPO contractBusiProdPO);
	 /**
      * @description 根据保单号查询缴费止期的最大值
      * @version
      * @title
      * <AUTHOR> <EMAIL> 
      * @param contractBusiProdPO 保单险种抄单对象
      * @return 保单险种抄单表
      */
     public ContractBusiProdPO findContractBusiProdMax(ContractBusiProdPO contractBusiProdPO);

     /**
      * 查询保单下的险种ID列表
      * @description
      * @version
      * @title
      * @<NAME_EMAIL>
      * @param contractBusiProdPO 保单险种抄单对象
      * @return findAllUsiProdContractBusiProd 保单险种抄单表
      */
    public List<ContractBusiProdPO> findAllUsiProdContractBusiProd(ContractBusiProdPO contractBusiProdPO);
    /**
     * 
     * @description 查询险种名称
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单对象
     * @return 保单险种抄单表
     */
    public List<ContractBusiProdPO> findAllContractBusiProdName(ContractBusiProdPO contractBusiProdPO);
    /**
     * 查询险种详细信息
     * @description
     * @version
     * @title
     * @<NAME_EMAIL> 
	 * @param contractBusiProdPO 保单险种抄单表
     * @return 险种详细信息
     */
    public List<ContractBusiProdPO> findAllContractBusiProdDetailed(ContractBusiProdPO contractBusiProdPO);
    /**
     * @description 查询险种保额
     * @version
     * @title
     * <AUTHOR>    <EMAIL>
     * @param contractBusiProdPO 保单险种抄单表对象
     * @return ContractBusiProdPO 保单险种抄单表
     */
     public ContractBusiProdPO findContractBusiProdAmount(ContractBusiProdPO contractBusiProdPO);
     /**
      * @description 根据主键查询
      * @version
      * @title
      * <AUTHOR>    <EMAIL>
      * @param contractBusiProdPO 保单险种抄单表对象
      * @return ContractBusiProdPO 保单险种抄单表
      */
     public ContractBusiProdPO findContractBusiProdByBusiItemId(ContractBusiProdPO contractBusiProdPO);
     /**
      * 
      * @description 查询可续保险种
      * @version V1.0.0
      * @title
      * <AUTHOR>
      * @param contractBusiProdPO 保单险种抄单表
      * @return
      */
	public List<ContractBusiProdPO> findAllUsiProdContractBusiProdRenew(ContractBusiProdPO contractBusiProdPO);

	/**
	 * @description 查询特殊险种的保单生效日期、保单终止日期
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractBusiProdPO 险种信息
	 * @return 返回保单有效期
	*/
	public List<ContractBusiProdPO> findValiddateDateByBusiProd(
			ContractBusiProdPO contractBusiProdPO);
	 /**
     * @description 根据保单ID及赔案ID查询报案报送税优涉案险种
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单表对象
     * @return List<ContractBusiProdPO> 保单险种抄单表查询结果List
     */
	 public List<ContractBusiProdPO> findReportTaxTransBusiByPolicyIdAndCaseId(ContractBusiProdPO contractBusiProdPO);
	 /**
	  * @description 根据产品ID查询税优报送产品信息
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param contractBusiProdPO 产品ID
	  * @return ContractBusiProdPO 税优报送信息
	  */
	 public ContractBusiProdPO findProductBocicByBusiPrdId(ContractBusiProdPO contractBusiProdPO);
	 /**
     * @description 根据出险人ID查询险种
     * @version
     * @title
     * <AUTHOR>
     * @param contractBusiProdPO 保单险种抄单表对象
     * @return List<ContractBusiProdPO> 保单险种抄单表查询结果List
     */
	 public List<ContractBusiProdPO> findAllBusiProdByInsuredId(ContractBusiProdPO contractBusiProdPO);

	 /**
	 * 
	 * @description 根据保单号,赔案号查询险种信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractBusiProdPO 
	 * @return List<ContractBusiProdPO>
	 */
	 public List<ContractBusiProdPO> findContractBusiProdByPolicyCaseId(ContractBusiProdPO contractBusiProdPO);
	 
	 /**
	 * 
	 * @description 根据险种id查询客户信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractBusiProdPO 
	 * @return List<ContractBusiProdPO>
	 */
	 public List<ContractBusiProdPO> findCustomerIdByPolicyCaseBusiId(ContractBusiProdPO contractBusiProdPO);
	 
	 /**
	  * @description 根据赔案id查询生效日期
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param contractBusiProdPO CaseId
	  * @return 
	  */
	 public List<ContractBusiProdPO> findValidateByCaseId(ContractBusiProdPO contractBusiProdPO);
	 /**
	 * 
	 * @description 根据casid查询当前赔案当前出险人名下的附加险
	 * @version
	 * @title
	 * <AUTHOR> @newchinalife.com
	 * @param contractBusiProdPO 
	 * @return List<ContractBusiProdPO>
	 */
	 public List<ContractBusiProdPO> findBusiProdByMaster(ContractBusiProdPO contractBusiProdPO);
 }
 