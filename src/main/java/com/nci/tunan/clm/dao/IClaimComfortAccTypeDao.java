package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimComfortAccTypePO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description ClaimComfortAccTypeDaoImpl实现类
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔慰问出险类型
 * @date 2015-05-15 下午3:32:24
 */
 public interface IClaimComfortAccTypeDao {
	 /**
     * @description 增加理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return ClaimComfortAccTypePO 添加结果
     */
	 public ClaimComfortAccTypePO addClaimComfortAccType(ClaimComfortAccTypePO claimComfortAccTypePO);
	 
     /**
     * @description 删除理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimComfortAccType(ClaimComfortAccTypePO claimComfortAccTypePO);
	 
     /**
     * @description 修改理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return ClaimComfortAccTypePO 修改结果
     */
	 public ClaimComfortAccTypePO updateClaimComfortAccType(ClaimComfortAccTypePO claimComfortAccTypePO);
	 
     /**
     * @description 查询单条理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return ClaimComfortAccTypePO 查询结果对象
     */
	 public ClaimComfortAccTypePO findClaimComfortAccType(ClaimComfortAccTypePO claimComfortAccTypePO);
	 
     /**
     * @description 查询所有理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return List<ClaimComfortAccTypePO> 查询结果List
     */
	 public List<ClaimComfortAccTypePO> findAllClaimComfortAccType(ClaimComfortAccTypePO claimComfortAccTypePO);
	 
     /**
     * @description 查询理赔慰问出险类型数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return int 查询结果条数
     */
	 public int findClaimComfortAccTypeTotal(ClaimComfortAccTypePO claimComfortAccTypePO);

     /**
     * @description 分页查询理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimComfortAccTypePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimComfortAccTypePO> queryClaimComfortAccTypeForPage(ClaimComfortAccTypePO claimComfortAccTypePO, CurrentPage<ClaimComfortAccTypePO> currentPage);

     /**
     * @description 批量增加理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePOList 理赔慰问出险类型对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimComfortAccType(List<ClaimComfortAccTypePO> claimComfortAccTypePOList);

     /**
     * @description 批量修改理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePOList 理赔慰问出险类型对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimComfortAccType(List<ClaimComfortAccTypePO> claimComfortAccTypePOList);

     /**
     * @description 批量删除理赔慰问出险类型数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePOList 理赔慰问出险类型对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimComfortAccType(List<ClaimComfortAccTypePO> claimComfortAccTypePOList);

     /**
     * @description 查询所有理赔慰问出险类型数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimComfortAccTypePO 理赔慰问出险类型对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimComfortAccType(ClaimComfortAccTypePO claimComfortAccTypePO);
	 
 }
 