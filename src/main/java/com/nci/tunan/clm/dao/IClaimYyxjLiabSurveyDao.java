package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimYyxjLiabSurveyPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimYyxjLiabSurveyDao接口
 * <AUTHOR> 
 * @date 2022-07-09 16:02:10  
 */
 public interface IClaimYyxjLiabSurveyDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return ClaimYyxjLiabSurveyPO 添加结果
     */
	 public ClaimYyxjLiabSurveyPO addClaimYyxjLiabSurvey(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimYyxjLiabSurvey(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return ClaimYyxjLiabSurveyPO 修改结果
     */
	 public ClaimYyxjLiabSurveyPO updateClaimYyxjLiabSurvey(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return ClaimYyxjLiabSurveyPO 查询结果对象
     */
	 public ClaimYyxjLiabSurveyPO findClaimYyxjLiabSurvey(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return List<ClaimYyxjLiabSurveyPO> 查询结果List
     */
	 public List<ClaimYyxjLiabSurveyPO> findAllClaimYyxjLiabSurvey(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimYyxjLiabSurveyTotal(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimYyxjLiabSurveyPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimYyxjLiabSurveyPO> queryClaimYyxjLiabSurveyForPage(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO, CurrentPage<ClaimYyxjLiabSurveyPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimYyxjLiabSurvey(List<ClaimYyxjLiabSurveyPO> claimYyxjLiabSurveyPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimYyxjLiabSurvey(List<ClaimYyxjLiabSurveyPO> claimYyxjLiabSurveyPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimYyxjLiabSurvey(List<ClaimYyxjLiabSurveyPO> claimYyxjLiabSurveyPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimYyxjLiabSurveyPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimYyxjLiabSurvey(ClaimYyxjLiabSurveyPO claimYyxjLiabSurveyPO);
	 
 }
 