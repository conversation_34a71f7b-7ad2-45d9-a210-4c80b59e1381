package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;

import java.math.BigDecimal;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimAccResultBusiPO;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimAccResultBusiDao接口
 * <AUTHOR> 
 * @date 2022-07-09 16:04:33  
 */
 public interface IClaimAccResultBusiDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return ClaimAccResultBusiPO 添加结果
     */
	 public ClaimAccResultBusiPO addClaimAccResultBusi(ClaimAccResultBusiPO claimAccResultBusiPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAccResultBusi(ClaimAccResultBusiPO claimAccResultBusiPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return ClaimAccResultBusiPO 修改结果
     */
	 public ClaimAccResultBusiPO updateClaimAccResultBusi(ClaimAccResultBusiPO claimAccResultBusiPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return ClaimAccResultBusiPO 查询结果对象
     */
	 public ClaimAccResultBusiPO findClaimAccResultBusi(ClaimAccResultBusiPO claimAccResultBusiPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return List<ClaimAccResultBusiPO> 查询结果List
     */
	 public List<ClaimAccResultBusiPO> findAllClaimAccResultBusi(ClaimAccResultBusiPO claimAccResultBusiPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimAccResultBusiTotal(ClaimAccResultBusiPO claimAccResultBusiPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAccResultBusiPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAccResultBusiPO> queryClaimAccResultBusiForPage(ClaimAccResultBusiPO claimAccResultBusiPO, CurrentPage<ClaimAccResultBusiPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAccResultBusi(List<ClaimAccResultBusiPO> claimAccResultBusiPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAccResultBusi(List<ClaimAccResultBusiPO> claimAccResultBusiPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAccResultBusi(List<ClaimAccResultBusiPO> claimAccResultBusiPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultBusiPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAccResultBusi(ClaimAccResultBusiPO claimAccResultBusiPO);

	 /**
	  * @description 根据出险结果2查询数据
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimAccResultBusiPO 对象
	  * @return ClaimAccResultBusiPO 查询结果对象
	  */
	 public ClaimAccResultBusiPO findClaimAccResultBusiByAccResult2(ClaimAccResultBusiPO claimAccResultBusiPO);
		 
 }
 