package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimAccResultSurveyPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimAccResultSurveyDao接口
 * <AUTHOR> 
 * @date 2022-07-09 15:59:42  
 */
 public interface IClaimAccResultSurveyDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return ClaimAccResultSurveyPO 添加结果
     */
	 public ClaimAccResultSurveyPO addClaimAccResultSurvey(ClaimAccResultSurveyPO claimAccResultSurveyPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAccResultSurvey(ClaimAccResultSurveyPO claimAccResultSurveyPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return ClaimAccResultSurveyPO 修改结果
     */
	 public ClaimAccResultSurveyPO updateClaimAccResultSurvey(ClaimAccResultSurveyPO claimAccResultSurveyPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return ClaimAccResultSurveyPO 查询结果对象
     */
	 public ClaimAccResultSurveyPO findClaimAccResultSurvey(ClaimAccResultSurveyPO claimAccResultSurveyPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return List<ClaimAccResultSurveyPO> 查询结果List
     */
	 public List<ClaimAccResultSurveyPO> findAllClaimAccResultSurvey(ClaimAccResultSurveyPO claimAccResultSurveyPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimAccResultSurveyTotal(ClaimAccResultSurveyPO claimAccResultSurveyPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAccResultSurveyPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAccResultSurveyPO> queryClaimAccResultSurveyForPage(ClaimAccResultSurveyPO claimAccResultSurveyPO, CurrentPage<ClaimAccResultSurveyPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAccResultSurvey(List<ClaimAccResultSurveyPO> claimAccResultSurveyPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAccResultSurvey(List<ClaimAccResultSurveyPO> claimAccResultSurveyPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAccResultSurvey(List<ClaimAccResultSurveyPO> claimAccResultSurveyPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccResultSurveyPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAccResultSurvey(ClaimAccResultSurveyPO claimAccResultSurveyPO);
	 
	 /**
	  * @description 根据出险结果2查询数据
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimAccResultSurveyPO 对象
	  * @return ClaimAccResultSurveyPO 查询结果对象
	  */
	 public ClaimAccResultSurveyPO findClaimAccResultSurveyByAccResult2(ClaimAccResultSurveyPO claimAccResultSurveyPO);
		 
 }
 