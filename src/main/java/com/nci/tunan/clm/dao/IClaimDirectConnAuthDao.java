package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimDirectConnAuthPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimDirectConnAuthDao接口
 * <AUTHOR> 
 * @date 2021-11-08 11:12:28  
 */
 public interface IClaimDirectConnAuthDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return ClaimDirectConnAuthPO 添加结果
     */
	 public ClaimDirectConnAuthPO addClaimDirectConnAuth(ClaimDirectConnAuthPO claimDirectConnAuthPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectConnAuth(ClaimDirectConnAuthPO claimDirectConnAuthPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return ClaimDirectConnAuthPO 修改结果
     */
	 public ClaimDirectConnAuthPO updateClaimDirectConnAuth(ClaimDirectConnAuthPO claimDirectConnAuthPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return ClaimDirectConnAuthPO 查询结果对象
     */
	 public ClaimDirectConnAuthPO findClaimDirectConnAuth(ClaimDirectConnAuthPO claimDirectConnAuthPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return List<ClaimDirectConnAuthPO> 查询结果List
     */
	 public List<ClaimDirectConnAuthPO> findAllClaimDirectConnAuth(ClaimDirectConnAuthPO claimDirectConnAuthPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectConnAuthTotal(ClaimDirectConnAuthPO claimDirectConnAuthPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectConnAuthPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectConnAuthPO> queryClaimDirectConnAuthForPage(ClaimDirectConnAuthPO claimDirectConnAuthPO, CurrentPage<ClaimDirectConnAuthPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectConnAuth(List<ClaimDirectConnAuthPO> claimDirectConnAuthPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectConnAuth(List<ClaimDirectConnAuthPO> claimDirectConnAuthPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectConnAuth(List<ClaimDirectConnAuthPO> claimDirectConnAuthPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDirectConnAuthPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectConnAuth(ClaimDirectConnAuthPO claimDirectConnAuthPO);

	 /**
	  * 根据赔案ID查询表数据
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimDirectConnAuthPO
	  * @return
	  */
	 public ClaimDirectConnAuthPO findClaimDirectConnAuthByCaseId(ClaimDirectConnAuthPO claimDirectConnAuthPO);
	 
	 /**
	     * @description 查询单条数据
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param claimDirectConnAuthPO 对象
	     * @return ClaimDirectConnAuthPO 查询结果对象
	     */
		 public ClaimDirectConnAuthPO findClaimDirectConnAuthByListId(ClaimDirectConnAuthPO claimDirectConnAuthPO);
 }
 