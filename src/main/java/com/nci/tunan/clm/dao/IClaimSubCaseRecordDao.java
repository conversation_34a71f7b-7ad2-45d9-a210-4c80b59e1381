package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimSubCaseRecordPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description 子案件记录表接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2017-10-18 10:22:25  
 */
 public interface IClaimSubCaseRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 子案件记录表对象
     * @return ClaimSubCaseRecordPO 添加结果
     */
	 public ClaimSubCaseRecordPO addClaimSubCaseRecord(ClaimSubCaseRecordPO claimSubCaseRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 子案件记录表对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSubCaseRecord(ClaimSubCaseRecordPO claimSubCaseRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 对象
     * @return ClaimSubCaseRecordPO 修改结果
     */
	 public ClaimSubCaseRecordPO updateClaimSubCaseRecord(ClaimSubCaseRecordPO claimSubCaseRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 对象
     * @return ClaimSubCaseRecordPO 查询结果对象
     */
	 public ClaimSubCaseRecordPO findClaimSubCaseRecord(ClaimSubCaseRecordPO claimSubCaseRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 子案件记录表对象
     * @return List<ClaimSubCaseRecordPO> 查询结果List
     */
	 public List<ClaimSubCaseRecordPO> findAllClaimSubCaseRecord(ClaimSubCaseRecordPO claimSubCaseRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 子案件记录表对象
     * @return int 查询结果条数
     */
	 public int findClaimSubCaseRecordTotal(ClaimSubCaseRecordPO claimSubCaseRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSubCaseRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSubCaseRecordPO> queryClaimSubCaseRecordForPage(ClaimSubCaseRecordPO claimSubCaseRecordPO, CurrentPage<ClaimSubCaseRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPOList 子案件记录表对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSubCaseRecord(List<ClaimSubCaseRecordPO> claimSubCaseRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPOList 子案件记录表对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSubCaseRecord(List<ClaimSubCaseRecordPO> claimSubCaseRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPOList 子案件记录表对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSubCaseRecord(List<ClaimSubCaseRecordPO> claimSubCaseRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSubCaseRecordPO 子案件记录表对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSubCaseRecord(ClaimSubCaseRecordPO claimSubCaseRecordPO);
	 
 }
 