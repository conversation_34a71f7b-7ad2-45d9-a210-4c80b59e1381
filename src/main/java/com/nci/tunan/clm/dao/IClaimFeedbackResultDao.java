package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimFeedbackResultPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimFeedbackResultDao接口
 * <AUTHOR> 
 * @date 2025-06-13 14:47:02  
 */
 public interface IClaimFeedbackResultDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return ClaimFeedbackResultPO 添加结果
     */
	 public ClaimFeedbackResultPO addClaimFeedbackResult(ClaimFeedbackResultPO claimFeedbackResultPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimFeedbackResult(ClaimFeedbackResultPO claimFeedbackResultPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return ClaimFeedbackResultPO 修改结果
     */
	 public ClaimFeedbackResultPO updateClaimFeedbackResult(ClaimFeedbackResultPO claimFeedbackResultPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return ClaimFeedbackResultPO 查询结果对象
     */
	 public ClaimFeedbackResultPO findClaimFeedbackResult(ClaimFeedbackResultPO claimFeedbackResultPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return List<ClaimFeedbackResultPO> 查询结果List
     */
	 public List<ClaimFeedbackResultPO> findAllClaimFeedbackResult(ClaimFeedbackResultPO claimFeedbackResultPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimFeedbackResultTotal(ClaimFeedbackResultPO claimFeedbackResultPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimFeedbackResultPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimFeedbackResultPO> queryClaimFeedbackResultForPage(ClaimFeedbackResultPO claimFeedbackResultPO, CurrentPage<ClaimFeedbackResultPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimFeedbackResult(List<ClaimFeedbackResultPO> claimFeedbackResultPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimFeedbackResult(List<ClaimFeedbackResultPO> claimFeedbackResultPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimFeedbackResult(List<ClaimFeedbackResultPO> claimFeedbackResultPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimFeedbackResultPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimFeedbackResult(ClaimFeedbackResultPO claimFeedbackResultPO);
	 
 }
 