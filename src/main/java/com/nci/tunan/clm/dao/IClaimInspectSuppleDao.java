package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimBenePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimInsuredPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayeePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPushTaskPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRealnameCheckPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRegisterPushTaskPO;
import com.nci.tunan.clm.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ContractProductPO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
import com.nci.tunan.clm.interfaces.model.po.PolicyHolderPO;
import com.nci.tunan.clm.interfaces.model.po.PremArapPO;

/**
 * 
 * @description 实名查验登记批处理Dao接口
 * <AUTHOR> <EMAIL>
 * @date 2021年12月22日上午10:55:24
 * @.belongToModule CLM-理赔系统
 */
public interface IClaimInspectSuppleDao {

	List<ClaimRegisterPushTaskPO> queryInspectSuppleCase(ClaimRegisterPushTaskPO claimRegisterPushTaskPO);

	List<ContractBusiProdPO> findAllContractBusiProdPO(
			ContractBusiProdPO contractBusiProdPO);

	ContractProductPO sumContractProduct(ContractProductPO contractProductPO);

	List<PremArapPO> findPremArapByPolicyCode(PremArapPO premArapPO);

	List<PolicyHolderPO> findAllPolicyHolder(PolicyHolderPO policyHolderPO);

	List<ClaimInsuredPO> findAllClaimInsured(ClaimInsuredPO claimInsuredPO);

	List<ClaimBenePO> findAllClaimBene(ClaimBenePO claimBenePO);

	CustomerPO findCustomerByCustomerId(CustomerPO customerPO);

	List<ClaimRealnameCheckPO> findAllClaimRealnameCheckPO(
			ClaimRealnameCheckPO claimRealnameCheckPO);

	ClaimRegisterPushTaskPO updateClaimRegisterPushTask(
			ClaimRegisterPushTaskPO claimRegisterPushTaskPO);

	List<ClaimRegisterPushTaskPO> queryInspectSuppleCaseByFail(
			ClaimRegisterPushTaskPO claimRegisterPushTaskPO);

	PremArapPO findAllPayPremArapByCaseId(PremArapPO premArapPO);

	List<ClaimPayPO> findClaimPayPOByPolicyCode(ClaimPayPO claimPayPO);

	ClaimPayeePO findClaimPayeePOByPayeeId(ClaimPayeePO claimPayeePO);

	ClaimBenePO findClaimBenePOByBeneId(ClaimBenePO claimBenePO);

}
