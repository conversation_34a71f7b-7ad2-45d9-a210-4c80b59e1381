package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimCheckRoleListPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimCheckRoleListDao接口
 * <AUTHOR> 
 * @date 2023-05-23 13:52:36  
 */
 public interface IClaimCheckRoleListDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return ClaimCheckRoleListPO 添加结果
     */
	 public ClaimCheckRoleListPO addClaimCheckRoleList(ClaimCheckRoleListPO claimCheckRoleListPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimCheckRoleList(ClaimCheckRoleListPO claimCheckRoleListPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return ClaimCheckRoleListPO 修改结果
     */
	 public ClaimCheckRoleListPO updateClaimCheckRoleList(ClaimCheckRoleListPO claimCheckRoleListPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return ClaimCheckRoleListPO 查询结果对象
     */
	 public ClaimCheckRoleListPO findClaimCheckRoleList(ClaimCheckRoleListPO claimCheckRoleListPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return List<ClaimCheckRoleListPO> 查询结果List
     */
	 public List<ClaimCheckRoleListPO> findAllClaimCheckRoleList(ClaimCheckRoleListPO claimCheckRoleListPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimCheckRoleListTotal(ClaimCheckRoleListPO claimCheckRoleListPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimCheckRoleListPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimCheckRoleListPO> queryClaimCheckRoleListForPage(ClaimCheckRoleListPO claimCheckRoleListPO, CurrentPage<ClaimCheckRoleListPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimCheckRoleList(List<ClaimCheckRoleListPO> claimCheckRoleListPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimCheckRoleList(List<ClaimCheckRoleListPO> claimCheckRoleListPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimCheckRoleList(List<ClaimCheckRoleListPO> claimCheckRoleListPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimCheckRoleListPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimCheckRoleList(ClaimCheckRoleListPO claimCheckRoleListPO);
	 
 }
 