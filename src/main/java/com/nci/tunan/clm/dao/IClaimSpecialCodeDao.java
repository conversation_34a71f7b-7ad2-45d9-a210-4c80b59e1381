package com.nci.tunan.clm.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimSpecialCodePO;
import com.nci.udmp.framework.model.CurrentPage;

/** 
 * @description 特种费用代码接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-07-21 16:18:37  
 */
 public interface IClaimSpecialCodeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return ClaimSpecialCodePO 添加结果
     */
	 public ClaimSpecialCodePO addClaimSpecialCode(ClaimSpecialCodePO claimSpecialCodePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSpecialCode(ClaimSpecialCodePO claimSpecialCodePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return ClaimSpecialCodePO 修改结果
     */
	 public ClaimSpecialCodePO updateClaimSpecialCode(ClaimSpecialCodePO claimSpecialCodePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return ClaimSpecialCodePO 查询结果对象
     */
	 public ClaimSpecialCodePO findClaimSpecialCode(ClaimSpecialCodePO claimSpecialCodePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return List<ClaimSpecialCodePO> 查询结果List
     */
	 public List<ClaimSpecialCodePO> findAllClaimSpecialCode(ClaimSpecialCodePO claimSpecialCodePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return int 查询结果条数
     */
	 public int findClaimSpecialCodeTotal(ClaimSpecialCodePO claimSpecialCodePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSpecialCodePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSpecialCodePO> queryClaimSpecialCodeForPage(ClaimSpecialCodePO claimSpecialCodePO, CurrentPage<ClaimSpecialCodePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePOList 特种费用代码对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSpecialCode(List<ClaimSpecialCodePO> claimSpecialCodePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePOList 特种费用代码对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSpecialCode(List<ClaimSpecialCodePO> claimSpecialCodePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSpecialCode(List<ClaimSpecialCodePO> claimSpecialCodePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialCodePO 特种费用代码对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSpecialCode(ClaimSpecialCodePO claimSpecialCodePO);
	 
 }
 