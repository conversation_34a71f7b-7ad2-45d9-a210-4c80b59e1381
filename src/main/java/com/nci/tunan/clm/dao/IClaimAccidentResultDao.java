package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.Accident2PO;
import com.nci.tunan.clm.interfaces.model.po.ClaimAccidentResultPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description IClaimAccidentResultDao理赔出险结果接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午2:28:13
 */
 public interface IClaimAccidentResultDao {
     /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return ClaimAccidentResultPO 添加结果
     */
     public ClaimAccidentResultPO addClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
     
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return boolean 删除是否成功
     */
     public boolean deleteClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
     
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return ClaimAccidentResultPO 修改结果
     */
     public ClaimAccidentResultPO updateClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
     
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return ClaimAccidentResultPO 查询结果对象
     */
     public ClaimAccidentResultPO findClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
     
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return List<ClaimAccidentResultPO> 查询结果List
     */
     public List<ClaimAccidentResultPO> findAllClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);

     /**
     * @description 查询查询accident2
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return List<ClaimAccidentResultPO> 查询结果List
     */
     public List<Accident2PO> findAccidentResult2(Accident2PO accident2);
     
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return int 查询结果条数
     */
     public int findClaimAccidentResultTotal(ClaimAccidentResultPO claimAccidentResultPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAccidentResultPO> 查询结果的当前页对象
     */
     public CurrentPage<ClaimAccidentResultPO> queryClaimAccidentResultForPage(ClaimAccidentResultPO claimAccidentResultPO, CurrentPage<ClaimAccidentResultPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPOList 对象列表
     * @return boolean 批量添加是否成功
     */
     public boolean batchSaveClaimAccidentResult(List<ClaimAccidentResultPO> claimAccidentResultPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPOList 对象列表
     * @return boolean 批量修改是否成功
     */
     public boolean batchUpdateClaimAccidentResult(List<ClaimAccidentResultPO> claimAccidentResultPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPOList 对象列表
     * @return boolean 批量删除是否成功
     */
     public boolean batchDeleteClaimAccidentResult(List<ClaimAccidentResultPO> claimAccidentResultPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
     public List<Map<String, Object>> findAllMapClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
     /**
      * @description 删除数据通过CaseId
      * @version
      * @title
      * <AUTHOR>
      * @param claimAccidentResultPO   理赔出险结果对象
      */
      public void deleteClaimAccidentResultByCaseId(ClaimAccidentResultPO po);
      /**
       * @description 批量增加数据
       * @version
       * @title
       * <AUTHOR>
       * @param claimAccidentResultPOList 对象列表
       */
       public void batchSaveClaimAccidentResultInfo(List<ClaimAccidentResultPO> listpo);
       /**
        * @description 修改数据
        * @version
        * @title
        * <AUTHOR>
        * @param claimAccidentResultPO   理赔出险结果对象
        * @return ClaimAccidentResultPO 修改结果
        */
        public ClaimAccidentResultPO updateClaimAccidentResultByCaseId(ClaimAccidentResultPO claimAccidentResultPO);
        /**
		 * 
		 * @description   根据caseID查询出现结果
		 * @version V1.0.0
		 * @title
		 * <AUTHOR> <EMAIL>
		 * @date 2015-05-15 下午2:52:26 
		 * @see com.nci.tunan.clm.dao.IClaimAccidentResultDao#findClaimAccidentResultDistinct(com.nci.tunan.clm.interfaces.model.po.ClaimAccidentResultPO)
		 * @param claimAccidentResultPO   理赔出险结果对象
		 * @return
		 */
	public List<ClaimAccidentResultPO> findClaimAccidentResultDistinct(
			ClaimAccidentResultPO claimAccidentResultPO);
	/**
	 * 
	 * @description   查询出险结果2名称
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2015-05-15 下午2:51:42 
	 * @see com.nci.tunan.clm.dao.IClaimAccidentResultDao#findAccResult2Name(com.nci.tunan.clm.interfaces.model.po.Accident2PO)
	 * @param accResult2  出险结果2
	 * @return
	 */
	public Accident2PO findAccResult2Name(Accident2PO accResult2);
	
	
	
	/**
	 * 
	 * @description   查询当前赔案出险结果
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2022-01-111 下午1:41:42 
	 * @see com.nci.tunan.clm.dao.IClaimAccidentResultDao#findAccResult2Name(com.nci.tunan.clm.interfaces.model.po.Accident2PO)
	 * @param claimAccidentResultPO  出险结果
	 * @return
	 */
//	public ClaimAccidentResultPO findCurrentClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
	public List<ClaimAccidentResultPO> findCurrentClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);
	/**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAccidentResultPO   理赔出险结果对象
     * @return ClaimAccidentResultPO 查询结果对象
     */
     public List<ClaimAccidentResultPO> findClaimAccidentResultByCaseId(ClaimAccidentResultPO claimAccidentResultPO);

	public List<ClaimAccidentResultPO> queryAllClaimAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);

	public ClaimAccidentResultPO findChronicDiseaseByAccidentResult(ClaimAccidentResultPO claimAccidentResultPO);

	public List<ClaimAccidentResultPO> findFirstAccidentResult1(ClaimAccidentResultPO claimAccidentResultPO);

}
 