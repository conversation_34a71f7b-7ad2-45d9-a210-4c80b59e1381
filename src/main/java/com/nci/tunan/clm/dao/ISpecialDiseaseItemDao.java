package com.nci.tunan.clm.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.JointPO;
import com.nci.tunan.clm.interfaces.model.po.SpecialDiseaseItemPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ISpecialDiseaseItemDao特定费用代码接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-特定费用
 * @date 2015-07-15 15:16:54  
 */
 public interface ISpecialDiseaseItemDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return SpecialDiseaseItemPO 添加结果
     */
	 public SpecialDiseaseItemPO addSpecialDiseaseItem(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteSpecialDiseaseItem(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return SpecialDiseaseItemPO 修改结果
     */
	 public SpecialDiseaseItemPO updateSpecialDiseaseItem(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return SpecialDiseaseItemPO 查询结果对象
     */
	 public SpecialDiseaseItemPO findSpecialDiseaseItem(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return List<SpecialDiseaseItemPO> 查询结果List
     */
	 public List<SpecialDiseaseItemPO> findAllSpecialDiseaseItem(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return int 查询结果条数
     */
	 public int findSpecialDiseaseItemTotal(SpecialDiseaseItemPO specialDiseaseItemPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SpecialDiseaseItemPO> 查询结果的当前页对象
     */
	 public CurrentPage<SpecialDiseaseItemPO> querySpecialDiseaseItemForPage(SpecialDiseaseItemPO specialDiseaseItemPO, CurrentPage<SpecialDiseaseItemPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPOList 特定费用代码对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSpecialDiseaseItem(List<SpecialDiseaseItemPO> specialDiseaseItemPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPOList 特定费用代码对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSpecialDiseaseItem(List<SpecialDiseaseItemPO> specialDiseaseItemPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPOList 特定费用代码对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSpecialDiseaseItem(List<SpecialDiseaseItemPO> specialDiseaseItemPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapSpecialDiseaseItem(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
	 
	 /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param specialDiseaseItemPO 特定费用代码对象
     * @return List<SpecialDiseaseItemPO> 查询结果List
     */
	 public List<SpecialDiseaseItemPO> findSpecialDiseaseItemByRelaCode(SpecialDiseaseItemPO specialDiseaseItemPO);
	 
	 
	 /**
	     * @description 查询所有数据
	     * @version
	     * @title
	     * <AUTHOR>
	     * @param specialDiseaseItemPO 特定费用代码对象
	     * @return List<SpecialDiseaseItemPO> 查询结果List
	     */
		 public List<JointPO> findAllJointCode(JointPO jointPO);
		 
 }
 