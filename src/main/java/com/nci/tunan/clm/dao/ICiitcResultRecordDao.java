package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.CiitcResultRecordPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description ICiitcResultRecordDao接口
 * <AUTHOR> 
 * @date 2020-12-10 18:37:34  
 */
 public interface ICiitcResultRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return CiitcResultRecordPO 添加结果
     */
	 public CiitcResultRecordPO addCiitcResultRecord(CiitcResultRecordPO ciitcResultRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCiitcResultRecord(CiitcResultRecordPO ciitcResultRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return CiitcResultRecordPO 修改结果
     */
	 public CiitcResultRecordPO updateCiitcResultRecord(CiitcResultRecordPO ciitcResultRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return CiitcResultRecordPO 查询结果对象
     */
	 public CiitcResultRecordPO findCiitcResultRecord(CiitcResultRecordPO ciitcResultRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return List<CiitcResultRecordPO> 查询结果List
     */
	 public List<CiitcResultRecordPO> findAllCiitcResultRecord(CiitcResultRecordPO ciitcResultRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return int 查询结果条数
     */
	 public int findCiitcResultRecordTotal(CiitcResultRecordPO ciitcResultRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CiitcResultRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<CiitcResultRecordPO> queryCiitcResultRecordForPage(CiitcResultRecordPO ciitcResultRecordPO, CurrentPage<CiitcResultRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCiitcResultRecord(List<CiitcResultRecordPO> ciitcResultRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCiitcResultRecord(List<CiitcResultRecordPO> ciitcResultRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCiitcResultRecord(List<CiitcResultRecordPO> ciitcResultRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param ciitcResultRecordPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCiitcResultRecord(CiitcResultRecordPO ciitcResultRecordPO);
	 
 }
 