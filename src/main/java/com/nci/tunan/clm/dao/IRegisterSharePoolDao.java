package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseAndCustBO;
import com.nci.tunan.clm.interfaces.model.bo.RegisterSharePoolResClientBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;

/**
 * 
 * @description 立案共享池查询dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-立案共享池
 * @date 2015-05-15 下午2:32:01
 */
public interface IRegisterSharePoolDao {

	/**
	 * 
	 * @description 立案共享池查询方法
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param responseBO   立案共享池
	 * @return 立案共享池
	 * @throws Exception
	 */
	public List<ClaimCaseAndCustBO> findAllClaimCase(RegisterSharePoolResClientBO responseBO) throws Exception;
	/**
	 * 
	 * @description 查询立案个人池
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param claimCasePO   赔案信息
	 * @return  赔案信息
	 */
	public List<ClaimCasePO> findAllClaimCaseForSelf(ClaimCasePO claimCasePO);
}
