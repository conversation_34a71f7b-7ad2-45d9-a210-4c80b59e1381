package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimRiskResultInfoPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimRiskResultInfoDao接口
 * <AUTHOR> 
 * @date 2022-10-13 13:59:25  
 */
 public interface IClaimRiskResultInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return ClaimRiskResultInfoPO 添加结果
     */
	 public ClaimRiskResultInfoPO addClaimRiskResultInfo(ClaimRiskResultInfoPO claimRiskResultInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskResultInfo(ClaimRiskResultInfoPO claimRiskResultInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return ClaimRiskResultInfoPO 修改结果
     */
	 public ClaimRiskResultInfoPO updateClaimRiskResultInfo(ClaimRiskResultInfoPO claimRiskResultInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return ClaimRiskResultInfoPO 查询结果对象
     */
	 public ClaimRiskResultInfoPO findClaimRiskResultInfo(ClaimRiskResultInfoPO claimRiskResultInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return List<ClaimRiskResultInfoPO> 查询结果List
     */
	 public List<ClaimRiskResultInfoPO> findAllClaimRiskResultInfo(ClaimRiskResultInfoPO claimRiskResultInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskResultInfoTotal(ClaimRiskResultInfoPO claimRiskResultInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskResultInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskResultInfoPO> queryClaimRiskResultInfoForPage(ClaimRiskResultInfoPO claimRiskResultInfoPO, CurrentPage<ClaimRiskResultInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskResultInfo(List<ClaimRiskResultInfoPO> claimRiskResultInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskResultInfo(List<ClaimRiskResultInfoPO> claimRiskResultInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskResultInfo(List<ClaimRiskResultInfoPO> claimRiskResultInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskResultInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskResultInfo(ClaimRiskResultInfoPO claimRiskResultInfoPO);
	 
 }
 