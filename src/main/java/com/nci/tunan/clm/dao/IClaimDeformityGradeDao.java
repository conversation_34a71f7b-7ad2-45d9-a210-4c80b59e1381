package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimDeformityGradePO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description ClaimDeformityGradeDaoImpl伤残等级实现类
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:45:45
 */
 public interface IClaimDeformityGradeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 伤残等级对象
     * @return ClaimDeformityGradePO 添加结果
     */
	 public ClaimDeformityGradePO addClaimDeformityGrade(ClaimDeformityGradePO claimDeformityGradePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 伤残等级对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDeformityGrade(ClaimDeformityGradePO claimDeformityGradePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 伤残等级对象
     * @return ClaimDeformityGradePO 修改结果
     */
	 public ClaimDeformityGradePO updateClaimDeformityGrade(ClaimDeformityGradePO claimDeformityGradePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 伤残等级对象
     * @return ClaimDeformityGradePO 查询结果对象
     */
	 public ClaimDeformityGradePO findClaimDeformityGrade(ClaimDeformityGradePO claimDeformityGradePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 伤残等级对象
     * @return List<ClaimDeformityGradePO> 查询结果List
     */
	 public List<ClaimDeformityGradePO> findAllClaimDeformityGrade(ClaimDeformityGradePO claimDeformityGradePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDeformityGradeTotal(ClaimDeformityGradePO claimDeformityGradePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDeformityGradePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDeformityGradePO> queryClaimDeformityGradeForPage(ClaimDeformityGradePO claimDeformityGradePO, CurrentPage<ClaimDeformityGradePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDeformityGrade(List<ClaimDeformityGradePO> claimDeformityGradePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDeformityGrade(List<ClaimDeformityGradePO> claimDeformityGradePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePOList 伤残等级对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDeformityGrade(List<ClaimDeformityGradePO> claimDeformityGradePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimDeformityGradePO 伤残等级对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDeformityGrade(ClaimDeformityGradePO claimDeformityGradePO);
	 
	 /**
	  * @description 通过对象查询伤残等级
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimDeformityGradeVO 伤残等级对象
	  * @return List<ClaimDeformityGradeVO> 查询结果存放到list中 
	  */
	 public List<ClaimDeformityGradePO> findDeformityGradeByTypeId(ClaimDeformityGradePO claimDeformityGradePO);
	 
 }
 