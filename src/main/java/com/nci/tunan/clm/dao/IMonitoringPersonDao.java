package com.nci.tunan.clm.dao;

import java.math.BigDecimal;

import com.nci.tunan.clm.interfaces.model.bo.ClaimMonitoringPersonBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimMonitoringPersonPO;
import com.nci.tunan.cs.model.po.AutoWorkTaskListPO;
import com.nci.udmp.framework.model.CurrentPage;

/** 
 * @description 核赔监测范围配置Dao
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2020-11-19 15:16:09  
 */
 public interface IMonitoringPersonDao {
	 /**
     * @description 核赔监测范围配置按条件查询query
     * @version
     * @title
     * <AUTHOR>
     * @param ClaimMonitoringPersonPO 维护核赔人员配置对象
     * @return MonitoringPersonPO 添加结果
     */
    public CurrentPage<ClaimMonitoringPersonPO> queryMonitoringPersonInfo(ClaimMonitoringPersonPO monitoringPersonPO, CurrentPage<ClaimMonitoringPersonPO> currentPage);
    /**
     * @description 校验核赔监测范围配置check
     * @version
     * @title
     * <AUTHOR>
     * @param ClaimMonitoringPersonPO 维护核赔人员配置对象
     * @return MonitoringPersonPO 
     */
	 public ClaimMonitoringPersonPO checkMonitoringPerson(ClaimMonitoringPersonPO claimMonitoringPersonPO);
	 /**
	  * @description 新增核赔监测范围配置add
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param ClaimMonitoringPersonPO 维护核赔人员配置对象
	  * @return MonitoringPersonPO 添加结果
	  */     
      public ClaimMonitoringPersonPO addClaimMonitoringPerson(ClaimMonitoringPersonPO claimMonitoringPersonPO);
      /**
 	  * @description 删除核赔监测范围配置delete
 	  * @version
 	  * @title
 	  * <AUTHOR>
 	  * @param ClaimMonitoringPersonPO 维护核赔人员配置对象
 	  * @return boolean 添加结果
 	  */
      public  boolean  deleteClaimMonitoringPerson(ClaimMonitoringPersonPO  claimMonitoringPersonPO);
      /** 查询核赔配置人员列表ByOperatorId
       * @description
       * @version
       * @title
       * <AUTHOR> <EMAIL>
       * @param String 
       * @return ClaimMonitoringPersonPO
      */ 
      public ClaimMonitoringPersonPO queryMonPersonSingle(ClaimMonitoringPersonPO claimMonitoringPersonPO);
      /** 修改核赔配置人员列表ByOperatorId
       * @description
       * @version
       * @title
       * <AUTHOR> <EMAIL>
       * @param String 
       * @return boolean
      */
      public ClaimMonitoringPersonPO updateClaimMonitoringPerson(ClaimMonitoringPersonPO  claimMonitoringPersonPO);
 }
 