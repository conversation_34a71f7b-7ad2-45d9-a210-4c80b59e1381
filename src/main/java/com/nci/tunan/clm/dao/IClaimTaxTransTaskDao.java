package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimTaxTransTaskPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimTaxTransTaskDao接口
 * <AUTHOR> 
 * @date 2023-11-08 10:35:59  
 */
 public interface IClaimTaxTransTaskDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return ClaimTaxTransTaskPO 添加结果
     */
	 public ClaimTaxTransTaskPO addClaimTaxTransTask(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimTaxTransTask(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return ClaimTaxTransTaskPO 修改结果
     */
	 public ClaimTaxTransTaskPO updateClaimTaxTransTask(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return ClaimTaxTransTaskPO 查询结果对象
     */
	 public ClaimTaxTransTaskPO findClaimTaxTransTask(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return List<ClaimTaxTransTaskPO> 查询结果List
     */
	 public List<ClaimTaxTransTaskPO> findAllClaimTaxTransTask(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimTaxTransTaskTotal(ClaimTaxTransTaskPO claimTaxTransTaskPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimTaxTransTaskPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimTaxTransTaskPO> queryClaimTaxTransTaskForPage(ClaimTaxTransTaskPO claimTaxTransTaskPO, CurrentPage<ClaimTaxTransTaskPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimTaxTransTask(List<ClaimTaxTransTaskPO> claimTaxTransTaskPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimTaxTransTask(List<ClaimTaxTransTaskPO> claimTaxTransTaskPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimTaxTransTask(List<ClaimTaxTransTaskPO> claimTaxTransTaskPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimTaxTransTask(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
     * @description 税优产品理赔信息上传批处理数据查询类
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return List<ClaimTaxTransTaskPO> 查询结果List
     */
	 public List<ClaimTaxTransTaskPO> findAllClaimTaxTransTaskBatch(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
	  * @description 税优产品理赔退保信息上传批处理数据查询类
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimTaxTransTaskPO 对象
	  * @return List<ClaimTaxTransTaskPO> 查询结果List
	  */
	 public List<ClaimTaxTransTaskPO> findAllClaimTaxTransBQTaskBatch(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
	  * @description 税优产品理赔退费上传批处理数据
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimTaxTransTaskPO 对象
	  * @return List<ClaimTaxTransTaskPO> 查询结果List
	  */
	 public List<ClaimTaxTransTaskPO> findAllTaxTransBQTFBatch(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
	  * @description 理赔税优报送任务表待查询数据
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimTaxTransTaskPO 对象
	  * @return List<ClaimTaxTransTaskPO> 查询结果List
	  */
	 public List<ClaimTaxTransTaskPO> findAllClaimTaxTransToBeInquired(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
	  * @description 理赔税优报送任务表撤销数据
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimTaxTransTaskPO 对象
	  * @return List<ClaimTaxTransTaskPO> 查询结果List
	  */
	 public List<ClaimTaxTransTaskPO> findAllCloseTaxTransCase(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
	  * @description 理赔报案税优报送数据查询
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimTaxTransTaskPO 对象
	  * @return List<ClaimTaxTransTaskPO> 查询结果List
	  */
	 public List<ClaimTaxTransTaskPO> findAllReportTaxTransCase(ClaimTaxTransTaskPO claimTaxTransTaskPO);
	 /**
     * @description 根据赔案及客户号查询税优报送信息
     * @version
     * @title
     * <AUTHOR>
     * @param claimTaxTransTaskPO 对象
     * @return List<ClaimTaxTransTaskPO> 查询结果List
     */
	 public List<ClaimTaxTransTaskPO> findAllClaimTaxTransTaskByCaseIdAndCustomerId(ClaimTaxTransTaskPO claimTaxTransTaskPO);
 }
 