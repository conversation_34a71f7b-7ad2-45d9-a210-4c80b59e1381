package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClientHistoryReqPO;
import com.nci.tunan.clm.interfaces.model.po.ClientHistoryResPO;

/**
 * 客户历史服务查询dao
 * @description 
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午8:28:53
 */
public interface IQueryClientHistoryDao {
    /**
     * 根据客户号或者客户五要素查询客户历史服务记录
     * @description
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.sign.service.IQueryClientHistoryService#queryClientHistory(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO  赔案信息
     * @return
     */
    public List<ClientHistoryResPO> queryClientHistory(ClientHistoryReqPO clientHistoryReqPO);
}
