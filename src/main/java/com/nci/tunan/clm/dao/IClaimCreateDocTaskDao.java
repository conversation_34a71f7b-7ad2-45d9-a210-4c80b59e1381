package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimCreateDocTaskPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description IClaimCreateDocTaskDao接口
 * <AUTHOR> 
 * @date 2024-01-24 16:37:57  
 */
 public interface IClaimCreateDocTaskDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return ClaimCreateDocTaskPO 添加结果
     */
	 public ClaimCreateDocTaskPO addClaimCreateDocTask(ClaimCreateDocTaskPO claimCreateDocTaskPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimCreateDocTask(ClaimCreateDocTaskPO claimCreateDocTaskPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return ClaimCreateDocTaskPO 修改结果
     */
	 public ClaimCreateDocTaskPO updateClaimCreateDocTask(ClaimCreateDocTaskPO claimCreateDocTaskPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return ClaimCreateDocTaskPO 查询结果对象
     */
	 public ClaimCreateDocTaskPO findClaimCreateDocTask(ClaimCreateDocTaskPO claimCreateDocTaskPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return List<ClaimCreateDocTaskPO> 查询结果List
     */
	 public List<ClaimCreateDocTaskPO> findAllClaimCreateDocTask(ClaimCreateDocTaskPO claimCreateDocTaskPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimCreateDocTaskTotal(ClaimCreateDocTaskPO claimCreateDocTaskPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimCreateDocTaskPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimCreateDocTaskPO> queryClaimCreateDocTaskForPage(ClaimCreateDocTaskPO claimCreateDocTaskPO, CurrentPage<ClaimCreateDocTaskPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimCreateDocTask(List<ClaimCreateDocTaskPO> claimCreateDocTaskPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimCreateDocTask(List<ClaimCreateDocTaskPO> claimCreateDocTaskPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimCreateDocTask(List<ClaimCreateDocTaskPO> claimCreateDocTaskPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimCreateDocTask(ClaimCreateDocTaskPO claimCreateDocTaskPO);

	 /**
     * @description 根据caseId查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return ClaimCreateDocTaskPO 查询结果对象
     */
	 public ClaimCreateDocTaskPO findClaimCreateDocTaskByCaseId(ClaimCreateDocTaskPO claimCreateDocTaskPO);

	 /**
     * @description 根据任务状态查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCreateDocTaskPO 对象
     * @return ClaimCreateDocTaskPO 查询结果对象
     */
	 public List<ClaimCreateDocTaskPO> queryCreateDocTaskByTaskStatus(ClaimCreateDocTaskPO claimCreateDocTaskPO);
	 
 }
 