package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimCaseOrbitRecordPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
 

import java.util.List;
 
/**
 * 
 * @description 赔案轨迹记录信息表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:27:22
 */
 public interface IClaimCaseOrbitRecordDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return ClaimCaseOrbitRecordPO 添加结果
     */
	 public ClaimCaseOrbitRecordPO addClaimCaseOrbitRecord(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimCaseOrbitRecord(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return ClaimCaseOrbitRecordPO 修改结果
     */
	 public ClaimCaseOrbitRecordPO updateClaimCaseOrbitRecord(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return ClaimCaseOrbitRecordPO 查询结果对象
     */
	 public ClaimCaseOrbitRecordPO findClaimCaseOrbitRecord(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return List<ClaimCaseOrbitRecordPO> 查询结果List
     */
	 public List<ClaimCaseOrbitRecordPO> findAllClaimCaseOrbitRecord(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimCaseOrbitRecordTotal(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimCaseOrbitRecordPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimCaseOrbitRecordPO> queryClaimCaseOrbitRecordForPage(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO, CurrentPage<ClaimCaseOrbitRecordPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimCaseOrbitRecord(List<ClaimCaseOrbitRecordPO> claimCaseOrbitRecordPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimCaseOrbitRecord(List<ClaimCaseOrbitRecordPO> claimCaseOrbitRecordPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimCaseOrbitRecord(List<ClaimCaseOrbitRecordPO> claimCaseOrbitRecordPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimCaseOrbitRecordPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimCaseOrbitRecord(ClaimCaseOrbitRecordPO claimCaseOrbitRecordPO);
	 
 }
 