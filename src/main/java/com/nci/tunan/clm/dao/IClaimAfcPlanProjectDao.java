package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimAfcPlanRelaPO;
import com.nci.udmp.framework.exception.app.BizException;
/**
 * 
 * @description 制定质检计划的Dao接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-制定质检计划
 * @date 2015-05-15 下午2:51:27
 */
public interface IClaimAfcPlanProjectDao {

    /**
     * @description 根据理赔事后质检计划表的主键查询质检要点列表信息
     * @version
     * @title
     * <AUTHOR> <EMAIL> 
     * @param claimAfcPlanRelaPO  质检计划
     * @return 质检计划集合
     * @throws BizException
     */
    public List<ClaimAfcPlanRelaPO> queryClaimAfcPlanRela(ClaimAfcPlanRelaPO claimAfcPlanRelaPO) throws BizException;
}
