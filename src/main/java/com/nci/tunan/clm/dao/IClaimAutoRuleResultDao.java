package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimAutoRuleResultPO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 赔案自核不通过原因信息结果表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午2:58:55
 */
 public interface IClaimAutoRuleResultDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return ClaimAutoRuleResultPO 添加结果
     */
	 public ClaimAutoRuleResultPO addClaimAutoRuleResult(ClaimAutoRuleResultPO claimAutoRuleResultPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimAutoRuleResult(ClaimAutoRuleResultPO claimAutoRuleResultPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return ClaimAutoRuleResultPO 修改结果
     */
	 public ClaimAutoRuleResultPO updateClaimAutoRuleResult(ClaimAutoRuleResultPO claimAutoRuleResultPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return ClaimAutoRuleResultPO 查询结果对象
     */
	 public ClaimAutoRuleResultPO findClaimAutoRuleResult(ClaimAutoRuleResultPO claimAutoRuleResultPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return List<ClaimAutoRuleResultPO> 查询结果List
     */
	 public List<ClaimAutoRuleResultPO> findAllClaimAutoRuleResult(ClaimAutoRuleResultPO claimAutoRuleResultPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimAutoRuleResultTotal(ClaimAutoRuleResultPO claimAutoRuleResultPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimAutoRuleResultPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimAutoRuleResultPO> queryClaimAutoRuleResultForPage(ClaimAutoRuleResultPO claimAutoRuleResultPO, CurrentPage<ClaimAutoRuleResultPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimAutoRuleResult(List<ClaimAutoRuleResultPO> claimAutoRuleResultPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimAutoRuleResult(List<ClaimAutoRuleResultPO> claimAutoRuleResultPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimAutoRuleResult(List<ClaimAutoRuleResultPO> claimAutoRuleResultPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimAutoRuleResult(ClaimAutoRuleResultPO claimAutoRuleResultPO);
	 /**
     * @description 查询所有操作/针对页面风险评估报告 
     * @version
     * @title
     * <AUTHOR>
     * @param claimAutoRuleResultPO 对象
     * @return List<ClaimAutoRuleResultPO> 查询结果List
     */
	 public List<ClaimAutoRuleResultPO> findClaimAutoRuleResultToRisk(ClaimAutoRuleResultPO claimAutoRuleResultPO);
 }
 