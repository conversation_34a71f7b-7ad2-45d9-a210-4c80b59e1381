package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.PerformExamParamPO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;


/** 
 * @description 绩效考核参数设置Dao接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-绩效考核参数
 * @date 2015-11-18 10:55:00  
 */
 public interface IPerformExamParamDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return PerformExamParamPO 添加结果
     */
	 public PerformExamParamPO addPerformExamParam(PerformExamParamPO performExamParamPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return boolean 删除是否成功
     */
	 public boolean deletePerformExamParam(PerformExamParamPO performExamParamPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return PerformExamParamPO 修改结果
     */
	 public PerformExamParamPO updatePerformExamParam(PerformExamParamPO performExamParamPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return PerformExamParamPO 查询结果对象
     */
	 public PerformExamParamPO findPerformExamParam(PerformExamParamPO performExamParamPO);
	 
	 /**
	 * @description 索引查询
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param performExamParamPO 绩效考核参数设置对象
	 * @return 
	*/
	public PerformExamParamPO findPerformExamParamByOthers(PerformExamParamPO performExamParamPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return List<PerformExamParamPO> 查询结果List
     */
	 public List<PerformExamParamPO> findAllPerformExamParam(PerformExamParamPO performExamParamPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return int 查询结果条数
     */
	 public int findPerformExamParamTotal(PerformExamParamPO performExamParamPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PerformExamParamPO> 查询结果的当前页对象
     */
	 public CurrentPage<PerformExamParamPO> queryPerformExamParamForPage(PerformExamParamPO performExamParamPO, CurrentPage<PerformExamParamPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPOList 绩效考核参数设置对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePerformExamParam(List<PerformExamParamPO> performExamParamPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPOList 绩效考核参数设置对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePerformExamParam(List<PerformExamParamPO> performExamParamPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPOList 绩效考核参数设置对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePerformExamParam(List<PerformExamParamPO> performExamParamPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param performExamParamPO 绩效考核参数设置对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapPerformExamParam(PerformExamParamPO performExamParamPO);
	 
 }
 