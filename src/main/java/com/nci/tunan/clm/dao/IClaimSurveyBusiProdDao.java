package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimSurveyBusiProdPO;
import com.nci.udmp.framework.model.*;
import java.util.Map; 
import java.util.List; 


/** 
 * @description 理赔调查规则险种表Dao
 * <AUTHOR>
  * @.belongToModule CLM-理赔系统
 * @date 2015-07-23 14:03:03  
 */
 public interface IClaimSurveyBusiProdDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return ClaimSurveyBusiProdPO 添加结果
     */
	 public ClaimSurveyBusiProdPO addClaimSurveyBusiProd(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSurveyBusiProd(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return ClaimSurveyBusiProdPO 修改结果
     */
	 public ClaimSurveyBusiProdPO updateClaimSurveyBusiProd(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return ClaimSurveyBusiProdPO 查询结果对象
     */
	 public ClaimSurveyBusiProdPO findClaimSurveyBusiProd(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return List<ClaimSurveyBusiProdPO> 查询结果List
     */
	 public List<ClaimSurveyBusiProdPO> findAllClaimSurveyBusiProd(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return int 查询结果条数
     */
	 public int findClaimSurveyBusiProdTotal(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSurveyBusiProdPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSurveyBusiProdPO> queryClaimSurveyBusiProdForPage(ClaimSurveyBusiProdPO claimSurveyBusiProdPO, CurrentPage<ClaimSurveyBusiProdPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPOList 理赔调查规则险种对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSurveyBusiProd(List<ClaimSurveyBusiProdPO> claimSurveyBusiProdPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPOList 理赔调查规则险种对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSurveyBusiProd(List<ClaimSurveyBusiProdPO> claimSurveyBusiProdPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPOList 理赔调查规则险种对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSurveyBusiProd(List<ClaimSurveyBusiProdPO> claimSurveyBusiProdPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBusiProdPO 理赔调查规则险种对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSurveyBusiProd(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
     
     /**
      * @description 删除数据
      * @version
      * @title
      * <AUTHOR>
      * @param claimSurveyBusiProdPO 理赔调查规则险种对象
      * @return boolean 删除是否成功
      */
    public boolean deleteClaimSurveyBusiProdBySurveyRuleCode(ClaimSurveyBusiProdPO claimSurveyBusiProdPO);
	 
 }
 