package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimBillPaidConnPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimBillPaidConnDao接口
 * <AUTHOR> 
 * @date 2025-07-14 11:30:26  
 */
 public interface IClaimBillPaidConnDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return ClaimBillPaidConnPO 添加结果
     */
	 public ClaimBillPaidConnPO addClaimBillPaidConn(ClaimBillPaidConnPO claimBillPaidConnPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBillPaidConn(ClaimBillPaidConnPO claimBillPaidConnPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return ClaimBillPaidConnPO 修改结果
     */
	 public ClaimBillPaidConnPO updateClaimBillPaidConn(ClaimBillPaidConnPO claimBillPaidConnPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return ClaimBillPaidConnPO 查询结果对象
     */
	 public ClaimBillPaidConnPO findClaimBillPaidConn(ClaimBillPaidConnPO claimBillPaidConnPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return List<ClaimBillPaidConnPO> 查询结果List
     */
	 public List<ClaimBillPaidConnPO> findAllClaimBillPaidConn(ClaimBillPaidConnPO claimBillPaidConnPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBillPaidConnTotal(ClaimBillPaidConnPO claimBillPaidConnPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillPaidConnPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBillPaidConnPO> queryClaimBillPaidConnForPage(ClaimBillPaidConnPO claimBillPaidConnPO, CurrentPage<ClaimBillPaidConnPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBillPaidConn(List<ClaimBillPaidConnPO> claimBillPaidConnPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBillPaidConn(List<ClaimBillPaidConnPO> claimBillPaidConnPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBillPaidConn(List<ClaimBillPaidConnPO> claimBillPaidConnPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillPaidConnPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBillPaidConn(ClaimBillPaidConnPO claimBillPaidConnPO);

     /**
  * @description 删除数据根据Case_id
  * @version
  * @title
  * <AUTHOR>
  * @param claimBillPaidConnPO 对象
  * @return boolean 删除是否成功
  */
     public boolean deleteClaimBillPaidConnByCaseId(ClaimBillPaidConnPO claimBillPaidConnPO);
	 
 }
 