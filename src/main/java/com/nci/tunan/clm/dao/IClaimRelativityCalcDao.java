package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimRelativityCalcPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;

/**
 * 
 * @description 理赔理算相关性计算定义表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:59:35
 */
 public interface IClaimRelativityCalcDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return ClaimRelativityCalcPO 添加结果
     */
	 public ClaimRelativityCalcPO addClaimRelativityCalc(ClaimRelativityCalcPO claimRelativityCalcPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRelativityCalc(ClaimRelativityCalcPO claimRelativityCalcPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return ClaimRelativityCalcPO 修改结果
     */
	 public ClaimRelativityCalcPO updateClaimRelativityCalc(ClaimRelativityCalcPO claimRelativityCalcPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return ClaimRelativityCalcPO 查询结果对象
     */
	 public ClaimRelativityCalcPO findClaimRelativityCalc(ClaimRelativityCalcPO claimRelativityCalcPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return List<ClaimRelativityCalcPO> 查询结果List
     */
	 public List<ClaimRelativityCalcPO> findAllClaimRelativityCalc(ClaimRelativityCalcPO claimRelativityCalcPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return int 查询结果条数
     */
	 public int findClaimRelativityCalcTotal(ClaimRelativityCalcPO claimRelativityCalcPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRelativityCalcPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRelativityCalcPO> queryClaimRelativityCalcForPage(ClaimRelativityCalcPO claimRelativityCalcPO, CurrentPage<ClaimRelativityCalcPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPOList 理赔理算相关性计算定义对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRelativityCalc(List<ClaimRelativityCalcPO> claimRelativityCalcPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPOList 理赔理算相关性计算定义对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRelativityCalc(List<ClaimRelativityCalcPO> claimRelativityCalcPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPOList 理赔理算相关性计算定义对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRelativityCalc(List<ClaimRelativityCalcPO> claimRelativityCalcPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRelativityCalcPO 理赔理算相关性计算定义对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRelativityCalc(ClaimRelativityCalcPO claimRelativityCalcPO);
	 
 }
 