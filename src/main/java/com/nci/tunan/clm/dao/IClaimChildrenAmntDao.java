package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimChildrenAmntPO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;

/**
 * 
 * @description ClaimChildrenAmntDaoImpl实现类 未成年人保额标准维护
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:30:15
 */
public interface IClaimChildrenAmntDao {
    /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return ClaimChildrenAmntPO 添加结果
     */
    public ClaimChildrenAmntPO addClaimChildrenAmnt(ClaimChildrenAmntPO claimChildrenAmntPO);

    /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return boolean 删除是否成功
     */
    public boolean deleteClaimChildrenAmnt(ClaimChildrenAmntPO claimChildrenAmntPO);

    /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return ClaimChildrenAmntPO 修改结果
     */
    public ClaimChildrenAmntPO updateClaimChildrenAmnt(ClaimChildrenAmntPO claimChildrenAmntPO);

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return ClaimChildrenAmntPO 查询结果对象
     */
    public ClaimChildrenAmntPO findClaimChildrenAmnt(ClaimChildrenAmntPO claimChildrenAmntPO);

    /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return List<ClaimChildrenAmntPO> 查询结果List
     */
    public List<ClaimChildrenAmntPO> findAllClaimChildrenAmnt(ClaimChildrenAmntPO claimChildrenAmntPO);

    /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return int 查询结果条数
     */
    public int findClaimChildrenAmntTotal(ClaimChildrenAmntPO claimChildrenAmntPO);

    /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage
     *            当前页对象
     * @param claimChildrenAmntPO 参数
     * @return CurrentPage<ClaimChildrenAmntPO> 查询结果的当前页对象
     */
    public CurrentPage<ClaimChildrenAmntPO> queryClaimChildrenAmntForPage(ClaimChildrenAmntPO claimChildrenAmntPO,
            CurrentPage<ClaimChildrenAmntPO> currentPage);

    /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPOList
     *            对象列表
     * @return boolean 批量添加是否成功
     */
    public boolean batchSaveClaimChildrenAmnt(List<ClaimChildrenAmntPO> claimChildrenAmntPOList);

    /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPOList
     *            对象列表
     * @return boolean 批量修改是否成功
     */
    public boolean batchUpdateClaimChildrenAmnt(List<ClaimChildrenAmntPO> claimChildrenAmntPOList);

    /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPOList
     *            对象列表
     * @return boolean 批量删除是否成功
     */
    public boolean batchDeleteClaimChildrenAmnt(List<ClaimChildrenAmntPO> claimChildrenAmntPOList);

    /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
    public List<Map<String, Object>> findAllMapClaimChildrenAmnt(ClaimChildrenAmntPO claimChildrenAmntPO);
    /**
     * @description 查询未成年人保额用来理算
     * @version
     * @title
     * <AUTHOR>
     * @param claimChildrenAmntPO
     *            对象
     * @return List<ClaimChildrenAmntPO> 查询结果List
     */
    public List<ClaimChildrenAmntPO> findClaimChildrenAmntToCalc(ClaimChildrenAmntPO claimChildrenAmntPO);

}
 