package com.nci.tunan.clm.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimSpecialTypePO;
import com.nci.udmp.framework.model.CurrentPage;

/** 
 * @description 特种费用类型接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-07-21 15:52:28  
 */
 public interface IClaimSpecialTypeDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return ClaimSpecialTypePO 添加结果
     */
	 public ClaimSpecialTypePO addClaimSpecialType(ClaimSpecialTypePO claimSpecialTypePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSpecialType(ClaimSpecialTypePO claimSpecialTypePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return ClaimSpecialTypePO 修改结果
     */
	 public ClaimSpecialTypePO updateClaimSpecialType(ClaimSpecialTypePO claimSpecialTypePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return ClaimSpecialTypePO 查询结果对象
     */
	 public ClaimSpecialTypePO findClaimSpecialType(ClaimSpecialTypePO claimSpecialTypePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return List<ClaimSpecialTypePO> 查询结果List
     */
	 public List<ClaimSpecialTypePO> findAllClaimSpecialType(ClaimSpecialTypePO claimSpecialTypePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return int 查询结果条数
     */
	 public int findClaimSpecialTypeTotal(ClaimSpecialTypePO claimSpecialTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSpecialTypePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSpecialTypePO> queryClaimSpecialTypeForPage(ClaimSpecialTypePO claimSpecialTypePO, CurrentPage<ClaimSpecialTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePOList 特种费用类型对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSpecialType(List<ClaimSpecialTypePO> claimSpecialTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePOList 特种费用类型对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSpecialType(List<ClaimSpecialTypePO> claimSpecialTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePOList 特种费用类型对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSpecialType(List<ClaimSpecialTypePO> claimSpecialTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSpecialTypePO 特种费用类型对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSpecialType(ClaimSpecialTypePO claimSpecialTypePO);
	 
 }
 