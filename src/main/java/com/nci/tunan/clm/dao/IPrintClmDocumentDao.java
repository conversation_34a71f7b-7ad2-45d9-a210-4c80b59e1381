package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.PrintClmDocumentPO;
import com.nci.udmp.framework.model.CurrentPage;

import java.util.Map;
 

import java.util.List;
 

/**
 * 
 * @description  理赔通知书打印记录信息表Dao接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午5:45:15
 */
 public interface IPrintClmDocumentDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return PrintClmDocumentPO 添加结果
     */
	 public PrintClmDocumentPO addPrintClmDocument(PrintClmDocumentPO printClmDocumentPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return boolean 删除是否成功
     */
	 public boolean deletePrintClmDocument(PrintClmDocumentPO printClmDocumentPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return PrintClmDocumentPO 修改结果
     */
	 public PrintClmDocumentPO updatePrintClmDocument(PrintClmDocumentPO printClmDocumentPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return PrintClmDocumentPO 查询结果对象
     */
	 public PrintClmDocumentPO findPrintClmDocument(PrintClmDocumentPO printClmDocumentPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return List<PrintClmDocumentPO> 查询结果List
     */
	 public List<PrintClmDocumentPO> findAllPrintClmDocument(PrintClmDocumentPO printClmDocumentPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return int 查询结果条数
     */
	 public int findPrintClmDocumentTotal(PrintClmDocumentPO printClmDocumentPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PrintClmDocumentPO> 查询结果的当前页对象
     */
	 public CurrentPage<PrintClmDocumentPO> queryPrintClmDocumentForPage(PrintClmDocumentPO printClmDocumentPO, CurrentPage<PrintClmDocumentPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPOList 理赔通知书打印记录信息对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePrintClmDocument(List<PrintClmDocumentPO> printClmDocumentPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPOList 理赔通知书打印记录信息对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePrintClmDocument(List<PrintClmDocumentPO> printClmDocumentPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPOList 理赔通知书打印记录信息对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePrintClmDocument(List<PrintClmDocumentPO> printClmDocumentPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param printClmDocumentPO 理赔通知书打印记录信息对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapPrintClmDocument(PrintClmDocumentPO printClmDocumentPO);
	 
	 public PrintClmDocumentPO findWeChatCaseNo(PrintClmDocumentPO printClmDocumentPO);
	 
 }
 