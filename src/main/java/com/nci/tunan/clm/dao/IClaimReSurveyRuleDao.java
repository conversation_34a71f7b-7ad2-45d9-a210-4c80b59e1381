package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimReSurveyRulePO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;
/**
 * @description 复堪调查规则表Dao
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-09-06 17:28:18
 */
public interface IClaimReSurveyRuleDao {
    /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            对象
     * @return ClaimReSurveyRulePO 添加结果
     */
    public ClaimReSurveyRulePO addClaimReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            复堪调查规则对象
     * @return boolean 删除是否成功
     */
    public boolean deleteClaimReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            复堪调查规则对象
     * @return ClaimReSurveyRulePO 修改结果
     */
    public ClaimReSurveyRulePO updateClaimReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            复堪调查规则对象
     * @return ClaimReSurveyRulePO 查询结果对象
     */
    public ClaimReSurveyRulePO findClaimReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            复堪调查规则对象
     * @return List<ClaimReSurveyRulePO> 查询结果List
     */
    public List<ClaimReSurveyRulePO> findAllClaimReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            复堪调查规则对象
     * @return int 查询结果条数
     */
    public int findClaimReSurveyRuleTotal(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage
     *            当前页对象
     * @return CurrentPage<ClaimReSurveyRulePO> 查询结果的当前页对象
     */
    public CurrentPage<ClaimReSurveyRulePO> queryClaimReSurveyRuleForPage(ClaimReSurveyRulePO claimReSurveyRulePO,
            CurrentPage<ClaimReSurveyRulePO> currentPage);

    /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePOList
     *            复堪调查规则对象列表
     * @return boolean 批量添加是否成功
     */
    public boolean batchSaveClaimReSurveyRule(List<ClaimReSurveyRulePO> claimReSurveyRulePOList);

    /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePOList
     *            复堪调查规则对象列表
     * @return boolean 批量修改是否成功
     */
    public boolean batchUpdateClaimReSurveyRule(List<ClaimReSurveyRulePO> claimReSurveyRulePOList);

    /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePOList
     *            复堪调查规则对象列表
     * @return boolean 批量删除是否成功
     */
    public boolean batchDeleteClaimReSurveyRule(List<ClaimReSurveyRulePO> claimReSurveyRulePOList);

    /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO
     *            复堪调查规则对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
    public List<Map<String, Object>> findAllMapClaimReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);

    /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePOs
     *            复堪调查规则对象列表
     */
    public void batchSaveReSurveyRule(List<ClaimReSurveyRulePO> claimReSurveyRulePOs);
    /**
     * 
     * @description 通过planId查询所有复堪调查规则数据
     * <AUTHOR> <EMAIL>
     * @param  claimReSurveyRulePO  复堪调查规则对象参数
     * @return List<ClaimReSurveyRulePO>
     * @throws BizException
     */
    public List<ClaimReSurveyRulePO> queryAllReSurveyRule(ClaimReSurveyRulePO claimReSurveyRulePO);
    /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyRulePO 复堪调查规则对象
     */
    public void deleteReSurveyRuleByPlanId(ClaimReSurveyRulePO claimReSurveyRulePO);

}
