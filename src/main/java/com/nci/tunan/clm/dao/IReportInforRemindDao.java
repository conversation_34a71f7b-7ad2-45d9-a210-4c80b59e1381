package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;
import com.nci.tunan.clm.interfaces.model.po.ReportInforRemindPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ReportInforRemindDaoImpl消息提醒记录dao
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-消息提醒记录
 * @date 2017-10-18 10:13:23  
 */
 public interface IReportInforRemindDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return ReportInforRemindPO 添加结果
     */
	 public ReportInforRemindPO addReportInforRemind(ReportInforRemindPO reportInforRemindPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteReportInforRemind(ReportInforRemindPO reportInforRemindPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return ReportInforRemindPO 修改结果
     */
	 public ReportInforRemindPO updateReportInforRemind(ReportInforRemindPO reportInforRemindPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return ReportInforRemindPO 查询结果对象
     */
	 public ReportInforRemindPO findReportInforRemind(ReportInforRemindPO reportInforRemindPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return List<ReportInforRemindPO> 查询结果List
     */
	 public List<ReportInforRemindPO> findAllReportInforRemind(ReportInforRemindPO reportInforRemindPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return int 查询结果条数
     */
	 public int findReportInforRemindTotal(ReportInforRemindPO reportInforRemindPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ReportInforRemindPO> 查询结果的当前页对象
     */
	 public CurrentPage<ReportInforRemindPO> queryReportInforRemindForPage(ReportInforRemindPO reportInforRemindPO, CurrentPage<ReportInforRemindPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPOList 消息提醒记录对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveReportInforRemind(List<ReportInforRemindPO> reportInforRemindPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPOList 消息提醒记录对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateReportInforRemind(List<ReportInforRemindPO> reportInforRemindPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPOList 消息提醒记录对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteReportInforRemind(List<ReportInforRemindPO> reportInforRemindPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param reportInforRemindPO 消息提醒记录对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapReportInforRemind(ReportInforRemindPO reportInforRemindPO);
	 
 }
 