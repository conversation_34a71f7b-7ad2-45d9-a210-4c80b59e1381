package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.DocumentTemplatePO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description 通知书定义Dao接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-通知书模板
 * @date 2015-08-17 11:38:49  
 */
 public interface IDocumentTemplateDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return DocumentTemplatePO 添加结果
     */
	 public DocumentTemplatePO addDocumentTemplate(DocumentTemplatePO documentTemplatePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteDocumentTemplate(DocumentTemplatePO documentTemplatePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return DocumentTemplatePO 修改结果
     */
	 public DocumentTemplatePO updateDocumentTemplate(DocumentTemplatePO documentTemplatePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return DocumentTemplatePO 查询结果对象
     */
	 public DocumentTemplatePO findDocumentTemplate(DocumentTemplatePO documentTemplatePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return List<DocumentTemplatePO> 查询结果List
     */
	 public List<DocumentTemplatePO> findAllDocumentTemplate(DocumentTemplatePO documentTemplatePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return int 查询结果条数
     */
	 public int findDocumentTemplateTotal(DocumentTemplatePO documentTemplatePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<DocumentTemplatePO> 查询结果的当前页对象
     */
	 public CurrentPage<DocumentTemplatePO> queryDocumentTemplateForPage(DocumentTemplatePO documentTemplatePO, CurrentPage<DocumentTemplatePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePOList 通知书定义对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveDocumentTemplate(List<DocumentTemplatePO> documentTemplatePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePOList 通知书定义对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateDocumentTemplate(List<DocumentTemplatePO> documentTemplatePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePOList 通知书定义对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteDocumentTemplate(List<DocumentTemplatePO> documentTemplatePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param documentTemplatePO 通知书定义对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapDocumentTemplate(DocumentTemplatePO documentTemplatePO);
	 
 }
 