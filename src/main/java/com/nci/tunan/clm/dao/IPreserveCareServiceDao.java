package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCareVisitPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 理赔维护关怀服务信息
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-理赔维护关怀服务
 * @date 2015-05-15 下午8:26:28
 */
public interface IPreserveCareServiceDao {

	 /**
     * 添加关怀任务-查询赔案信息(按结案日期升序排序)
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimCaseVO  赔案信息
     * @return  赔案信息
     * @throws BizException
     */
    public List<ClaimCasePO> findClaimCaseByCondition(ClaimCasePO claimCasePO);
    
    /**
     * 添加关怀任务-查询赔案信息(按下次回访日期升序排序)
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.clm.dao.IPreserveCareServiceDao#findClaimCaseByConditionIsDo(com.nci.tunan.clm.interfaces.model.po.ClaimCasePO)
     * @param claimCasePO 赔案信息
     * @return 赔案信息集合
     */
    public List<ClaimCasePO> findClaimCaseByConditionIsDo(ClaimCasePO claimCasePO);
    
    /**
     * 查询条件有回访人维护关怀服务信息查询
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimCaseVO 赔案信息
     * @param currentPage  当前页
     * @return 关怀集合
     * @throws BizException
     */
    public CurrentPage<ClaimCareVisitPO> queryPerserveCareServiceMsg(ClaimCareVisitPO claimCareVisitPO,
            CurrentPage<ClaimCareVisitPO> currentPage);
    
    /**
     * 根据赔案号（回访人，回访标志）查询关怀次数和下次回访日期
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param claimCareVisitPO  理赔关怀对象
     * @return 关怀次数和下次回访日期
     */
    public List<ClaimCareVisitPO> findCareVisitByCondition(ClaimCareVisitPO claimCareVisitPO);
    
    /**
     * 维护关怀服务信息查询--查询条件无回访人
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.clm.dao.IPreserveCareServiceDao#queryPerserveCareServiceMsgNoVisit(com.nci.tunan.clm.interfaces.model.po.ClaimCasePO, com.nci.udmp.framework.model.CurrentPage)
     * @param claimCasePO  赔案信息
     * @param currentPage 当前页
     * @return  关怀集合
     */
    public CurrentPage<ClaimCareVisitPO> queryPerserveCareServiceMsgNoVisit(ClaimCareVisitPO claimCareVisitPO,
            CurrentPage<ClaimCareVisitPO> currentPage);
    
    
}
