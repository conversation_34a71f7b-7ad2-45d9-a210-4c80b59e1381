package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimBackApplyPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimInsuredRelationPO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCaseVO;

public interface IClaimIntelPortraitDao {
	
	 /**
     * @description 查询关联赔案数据
     * @version
     * @title
     * <AUTHOR>
     * @param ClaimCasePO 对象
     * @return ClaimCasePO 添加结果
     */
	 public List<ClaimCasePO> queryRelevanceCase(ClaimCasePO claimCasePO);
	 
	 
	 /**
	 * @description 查询关联赔案数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param ClaimCasePO 对象
	 * @return ClaimCasePO 添加结果
	 */
	 public List<ClaimBackApplyPO> findClaimBackApply(ClaimBackApplyPO claimBackApplyPO);

	 /**
	 * @description 查询出险人既往赔案数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param ClaimCasePO 对象
	 * @return ClaimCasePO 添加结果
	 */
	 public List<ClaimCasePO> findPastCaseInfo(ClaimCasePO claimCasePO);
	 /**
	 * @description 查询既往赔案信息不包含回退赔案
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param ClaimCasePO 对象
	 * @return ClaimCasePO 添加结果
	 */
	public List<ClaimCasePO> findPastCaseInfoNotRedo(ClaimCasePO claimCasePO);
	
	 /**
		 * @description 查询既往赔案信息
		 * @version
		 * @title
		 * <AUTHOR>
		 * @param ClaimCasePO 对象
		 * @return ClaimCasePO 添加结果
		 */
		public List<ClaimCasePO> findPastCaseInfoByInsuredId(ClaimCasePO claimCasePO);

}
