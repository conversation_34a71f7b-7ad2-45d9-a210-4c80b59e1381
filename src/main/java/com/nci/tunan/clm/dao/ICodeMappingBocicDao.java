package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.CodeMappingBocicPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ICodeMappingBocicDao接口
 * <AUTHOR> 
 * @date 2022-11-17 09:34:16  
 */
 public interface ICodeMappingBocicDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return CodeMappingBocicPO 添加结果
     */
	 public CodeMappingBocicPO addCodeMappingBocic(CodeMappingBocicPO codeMappingBocicPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCodeMappingBocic(CodeMappingBocicPO codeMappingBocicPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return CodeMappingBocicPO 修改结果
     */
	 public CodeMappingBocicPO updateCodeMappingBocic(CodeMappingBocicPO codeMappingBocicPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return CodeMappingBocicPO 查询结果对象
     */
	 public CodeMappingBocicPO findCodeMappingBocic(CodeMappingBocicPO codeMappingBocicPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return List<CodeMappingBocicPO> 查询结果List
     */
	 public List<CodeMappingBocicPO> findAllCodeMappingBocic(CodeMappingBocicPO codeMappingBocicPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return int 查询结果条数
     */
	 public int findCodeMappingBocicTotal(CodeMappingBocicPO codeMappingBocicPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CodeMappingBocicPO> 查询结果的当前页对象
     */
	 public CurrentPage<CodeMappingBocicPO> queryCodeMappingBocicForPage(CodeMappingBocicPO codeMappingBocicPO, CurrentPage<CodeMappingBocicPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCodeMappingBocic(List<CodeMappingBocicPO> codeMappingBocicPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCodeMappingBocic(List<CodeMappingBocicPO> codeMappingBocicPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCodeMappingBocic(List<CodeMappingBocicPO> codeMappingBocicPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param codeMappingBocicPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCodeMappingBocic(CodeMappingBocicPO codeMappingBocicPO);
	 
 }
 