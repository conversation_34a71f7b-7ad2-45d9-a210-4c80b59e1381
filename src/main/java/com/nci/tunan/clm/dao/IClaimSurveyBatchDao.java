package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimSurveyBatchPO;
import com.nci.tunan.clm.interfaces.model.po.SurveyApplyPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;


/** 
 * @description 调查任务批次表DaoImpl
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-08-24 20:37:47  
 */
 public interface IClaimSurveyBatchDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return ClaimSurveyBatchPO 添加结果
     */
	 public ClaimSurveyBatchPO addClaimSurveyBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSurveyBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return ClaimSurveyBatchPO 修改结果
     */
	 public ClaimSurveyBatchPO updateClaimSurveyBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return ClaimSurveyBatchPO 查询结果对象
     */
	 public ClaimSurveyBatchPO findClaimSurveyBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return List<ClaimSurveyBatchPO> 查询结果List
     */
	 public List<ClaimSurveyBatchPO> findAllClaimSurveyBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
	 /**
      * 查询批次下的调查项目
      * @description
      * @version V1.0.0
      * @title
      * <AUTHOR> <EMAIL>
      * @param claimSurveyBatchPO 调查任务批次
      * @return
      */
     public List<ClaimSurveyBatchPO> findAllSurveyItemBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return int 查询结果条数
     */
	 public int findClaimSurveyBatchTotal(ClaimSurveyBatchPO claimSurveyBatchPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSurveyBatchPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSurveyBatchPO> queryClaimSurveyBatchForPage(ClaimSurveyBatchPO claimSurveyBatchPO, CurrentPage<ClaimSurveyBatchPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPOList 调查任务批次对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSurveyBatch(List<ClaimSurveyBatchPO> claimSurveyBatchPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPOList 调查任务批次对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSurveyBatch(List<ClaimSurveyBatchPO> claimSurveyBatchPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPOList 调查任务批次对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSurveyBatch(List<ClaimSurveyBatchPO> claimSurveyBatchPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSurveyBatch(ClaimSurveyBatchPO claimSurveyBatchPO);
	 
	 /**
     * @description 按条件查询所有数据-按分页形式
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBatchPO 调查任务批次对象
     * @return List<ClaimSurveyBatchPO> 查询结果List
     */
	 public CurrentPage<ClaimSurveyBatchPO> queryClaimSurveyBatchOfPage(ClaimSurveyBatchPO claimSurveyBatchPO,  CurrentPage<ClaimSurveyBatchPO> batchPage);

	 /**
	  * 
	  * @description 查询批次任务量
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param surveyBatchPO
	  * @return
	  */
	 public Integer findAllSurveyItemSum(ClaimSurveyBatchPO surveyBatchPO);
	 
 }
 