package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.core.common.interfaces.model.po.RealnameCheckPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBenePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimInsuredPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRealnameCheckPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRealnameInspectionPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimSubCasePO;
import com.nci.tunan.clm.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
import com.nci.tunan.clm.interfaces.model.po.RealnameCodePO;

public interface IClaimRealNameCheckDao {

	/**
	 * 
	 * @description 查询赔案信息（赔案号，签收机构，理赔给付金额,理赔类型）,受托人信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseVO 赔案VO
	 * @return
	 */
	public ClaimCasePO findClaimCaseBOInfo(ClaimCasePO claimCasePO);
	/**
	 * 
	 * @description 查询受益人
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseVO
	 * @return
	 */
	public List<ClaimBenePO> findClaimBeneBOInfo(ClaimBenePO claimBenePO);
	/**
	 * 
	 * @description 查询客户信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param customerBO 顾客VO
	 * @return
	 */
	public CustomerPO findCustomerByCustomerId(CustomerPO customerPO);
	/**
	 * 
	 * @description 查询被保险人
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimInsuredPO 被保险人VO
	 * @return
	 */
	public List<ClaimInsuredPO> findAllClaimInsured(ClaimInsuredPO claimInsuredPO);
	/**
	 * 
	 * @description 查询保单信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractBusiProdPO 保单PO
	 * @return
	 */
	public List<ContractBusiProdPO> findAllContractBusiProdPO(
			ContractBusiProdPO contractBusiProdPO);
	/**
	 * 
	 * @description 查询实名查验配置信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimRealnameInspectionPO 实名查验配置PO
	 * @return
	 */
	public List<ClaimRealnameInspectionPO> findClaimRealnameInspectionForCheck(
			ClaimRealnameInspectionPO claimRealnameInspectionPO);
	
	/**
	 * 
	 * @description 查询理赔类型
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimLiabPO 责任理算表PO
	 * @return
	 */
	public List<ClaimLiabPO> findClaimLiaByCaseId(ClaimLiabPO claimLiabPO);
	/**
	 * 
	 * @description 查询实名查验相关的码值表
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramentMap
	 * @return
	 */
	public RealnameCodePO findCodeNameByType(RealnameCodePO realnameCodePO);
	/**
	 * 
	 * @description 查询实名查验记录信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param paramentMap
	 * @return
	 */
	public List<RealnameCheckPO> findRealNameChecks(
			RealnameCheckPO realnameCheckPO);
	/**
	 * 
	 * @description 查询实名查验预警记录信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return
	 */
	public int findClaimRealnameCheckPOList(ClaimRealnameCheckPO claimRealnameCheckPO);
	/**
	 * 
	 * @description 查询已发送实名查验预警邮件数量
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return
	 */
	public int findSendContentLogList(ClaimRealnameCheckPO claimRealnameCheckPO);
	/**
	 * 
	 * @description 查询理赔类型
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimLiabPO 责任理算表PO
	 * @return
	 */
	public List<ClaimSubCasePO> findClaimSubCaseByCaseId(
			ClaimSubCasePO claimSubCasePO);

}
