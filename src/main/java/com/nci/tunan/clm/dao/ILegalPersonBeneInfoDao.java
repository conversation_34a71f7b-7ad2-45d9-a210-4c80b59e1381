package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.LegalPersonBeneInfoPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description ILegalPersonBeneInfoDao接口
 * <AUTHOR> 
 * @date 2021-10-06 09:39:39  
 */
 public interface ILegalPersonBeneInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return LegalPersonBeneInfoPO 添加结果
     */
	 public LegalPersonBeneInfoPO addLegalPersonBeneInfo(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteLegalPersonBeneInfo(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return LegalPersonBeneInfoPO 修改结果
     */
	 public LegalPersonBeneInfoPO updateLegalPersonBeneInfo(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return LegalPersonBeneInfoPO 查询结果对象
     */
	 public LegalPersonBeneInfoPO findLegalPersonBeneInfo(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return List<LegalPersonBeneInfoPO> 查询结果List
     */
	 public List<LegalPersonBeneInfoPO> findAllLegalPersonBeneInfo(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findLegalPersonBeneInfoTotal(LegalPersonBeneInfoPO legalPersonBeneInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<LegalPersonBeneInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<LegalPersonBeneInfoPO> queryLegalPersonBeneInfoForPage(LegalPersonBeneInfoPO legalPersonBeneInfoPO, CurrentPage<LegalPersonBeneInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveLegalPersonBeneInfo(List<LegalPersonBeneInfoPO> legalPersonBeneInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateLegalPersonBeneInfo(List<LegalPersonBeneInfoPO> legalPersonBeneInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteLegalPersonBeneInfo(List<LegalPersonBeneInfoPO> legalPersonBeneInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapLegalPersonBeneInfo(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
	 
	 /**
     * @description 根据法人主键ID查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param legalPersonBeneInfoPO 对象
     * @return List<LegalPersonBeneInfoPO> 查询结果List
     */
	 public List<LegalPersonBeneInfoPO> findLegalPersonBeneInfoByLegalPersonId(LegalPersonBeneInfoPO legalPersonBeneInfoPO);
 }
 