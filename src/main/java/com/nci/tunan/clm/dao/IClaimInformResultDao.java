package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimInformResultPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;


/** 
 * @description IClaimInformResultDao接口
 * <AUTHOR> 
 * @date 2025-05-28 09:36:37  
 */
 public interface IClaimInformResultDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return ClaimInformResultPO 添加结果
     */
	 public ClaimInformResultPO addClaimInformResult(ClaimInformResultPO claimInformResultPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInformResult(ClaimInformResultPO claimInformResultPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return ClaimInformResultPO 修改结果
     */
	 public ClaimInformResultPO updateClaimInformResult(ClaimInformResultPO claimInformResultPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return ClaimInformResultPO 查询结果对象
     */
	 public ClaimInformResultPO findClaimInformResult(ClaimInformResultPO claimInformResultPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return List<ClaimInformResultPO> 查询结果List
     */
	 public List<ClaimInformResultPO> findAllClaimInformResult(ClaimInformResultPO claimInformResultPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInformResultTotal(ClaimInformResultPO claimInformResultPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInformResultPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInformResultPO> queryClaimInformResultForPage(ClaimInformResultPO claimInformResultPO, CurrentPage<ClaimInformResultPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInformResult(List<ClaimInformResultPO> claimInformResultPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInformResult(List<ClaimInformResultPO> claimInformResultPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInformResult(List<ClaimInformResultPO> claimInformResultPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInformResult(ClaimInformResultPO claimInformResultPO);

     /**
     * @description 查询“中保科联”直赔转快赔结案的赔案
     * @version
     * @title
     * <AUTHOR>
     * @param claimInformResultPO
     * @return List<ClaimInformResultPO> 查询结果List
     */
	 public List<ClaimInformResultPO> queryClaimInformResult(ClaimInformResultPO claimInformResultPO);
	 
     /**
     * @description 查询主动赔理赔结果回传信息
     * @version
     * @title
     * <AUTHOR>
     * @param claimCasePO
     * @return ClaimCasePO
     */
	 public ClaimCasePO queryClaimInformResultByCaseId(ClaimCasePO claimCasePO);
 }
 