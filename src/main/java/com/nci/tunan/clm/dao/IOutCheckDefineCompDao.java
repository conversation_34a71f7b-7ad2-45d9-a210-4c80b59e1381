package com.nci.tunan.clm.dao;

import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.OutsourceDataCheckDefinePO;

/**
 * 
 * @description  外包数据校验定义Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 上午10:04:47
 */
 public interface IOutCheckDefineCompDao {
	 
     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param outsourceDataCheckDefinePOList 外包数据校验对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateOutsourceDataCheckDefine(List<OutsourceDataCheckDefinePO> outsourceDataCheckDefinePOList);
    
	 /**
	  * 根据必填项查询
	  * @description
	  * @version
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param outPO 外包数据校验
	  * @return
	  */
	 public List<OutsourceDataCheckDefinePO> findAllByFlag(OutsourceDataCheckDefinePO outPO);
	 
 }
 