package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.tunan.clm.interfaces.model.po.ClaimInspectUserPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimInspectUserDao接口
 * <AUTHOR> 
 * @date 2023-02-07 14:11:43  
 */
 public interface IClaimInspectUserDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return ClaimInspectUserPO 添加结果
     */
	 public ClaimInspectUserPO addClaimInspectUser(ClaimInspectUserPO claimInspectUserPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimInspectUser(ClaimInspectUserPO claimInspectUserPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return ClaimInspectUserPO 修改结果
     */
	 public ClaimInspectUserPO updateClaimInspectUser(ClaimInspectUserPO claimInspectUserPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return ClaimInspectUserPO 查询结果对象
     */
	 public ClaimInspectUserPO findClaimInspectUser(ClaimInspectUserPO claimInspectUserPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return List<ClaimInspectUserPO> 查询结果List
     */
	 public List<ClaimInspectUserPO> findAllClaimInspectUser(ClaimInspectUserPO claimInspectUserPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimInspectUserTotal(ClaimInspectUserPO claimInspectUserPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimInspectUserPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimInspectUserPO> queryClaimInspectUserForPage(ClaimInspectUserPO claimInspectUserPO, CurrentPage<ClaimInspectUserPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimInspectUser(List<ClaimInspectUserPO> claimInspectUserPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimInspectUser(List<ClaimInspectUserPO> claimInspectUserPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimInspectUser(List<ClaimInspectUserPO> claimInspectUserPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimInspectUserPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimInspectUser(ClaimInspectUserPO claimInspectUserPO);
	 
 }
 