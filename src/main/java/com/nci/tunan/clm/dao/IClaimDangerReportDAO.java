package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimRiskReportConfigPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskReportPO;

/**
 * 
 * @description 定制风险评估报告dao实现类
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午3:42:28
 */
public interface IClaimDangerReportDAO {
    /**
     * @description 查询所有的风险评估报告配置
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRiskReportConfigPO
     *            参数
     * @return List<ClaimRiskReportConfigPO> 查询结果list
     */
    public List<ClaimRiskReportConfigPO> findClaimRiskReportConfig(ClaimRiskReportConfigPO claimRiskReportConfigPO);
    
    /**
     * @description 批量增加定制风险配置数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskReportList
     *            对象列表
     * @return boolean 批量添加是否成功
     */
    public boolean batchSaveClaimRiskReport(List<ClaimRiskReportPO> claimRiskReportList);
    
    /**
     * 
     * @description 查询多个项目类型的定制风险评估报告配置数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRiskReportConfigPO 参数
     * @return List<ClaimRiskReportConfigPO> 查询结果list
     */
    public List<ClaimRiskReportConfigPO> findClaimRiskReporInItemType(ClaimRiskReportConfigPO claimRiskReportConfigPO);
    
    /**
     * 
     * @description 通过userId查询出该用的所有风险评估报告配置
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRiskReportPO 参数
     * @return List<ClaimRiskReportPO> 查询结果list
     */
    public List<ClaimRiskReportPO> findClaimRiskReporByUserId(ClaimRiskReportPO claimRiskReportPO);
    
    /**
     * @description 批量删除定制风险评估配置数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskReportPOList 对象列表
     * @return boolean 批量删除是否成功
     */
     public boolean batchDeleteClaimRiskReport(List<ClaimRiskReportPO> claimRiskReportPOList);

}
