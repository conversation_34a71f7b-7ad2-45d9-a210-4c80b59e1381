package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimOccupationRatePO;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description 理赔职业给付比例表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:49:51
 */
 public interface IClaimOccupationRateDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 理赔职业给付比例对象
     * @return ClaimOccupationRatePO 添加结果
     */
	 public ClaimOccupationRatePO addClaimOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 理赔职业给付比例对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 理赔职业给付比例对象
     * @return ClaimOccupationRatePO 修改结果
     */
	 public ClaimOccupationRatePO updateClaimOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 理赔职业给付比例对象
     * @return ClaimOccupationRatePO 查询结果对象
     */
	 public ClaimOccupationRatePO findClaimOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 理赔职业给付比例对象
     * @return List<ClaimOccupationRatePO> 查询结果List
     */
	 public List<ClaimOccupationRatePO> findAllClaimOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 理赔职业给付比例对象
     * @return int 查询结果条数
     */
	 public int findClaimOccupationRateTotal(ClaimOccupationRatePO claimOccupationRatePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimOccupationRatePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimOccupationRatePO> queryClaimOccupationRateForPage(ClaimOccupationRatePO claimOccupationRatePO, CurrentPage<ClaimOccupationRatePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePOList 理赔职业给付比例对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimOccupationRate(List<ClaimOccupationRatePO> claimOccupationRatePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePOList 理赔职业给付比例对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimOccupationRate(List<ClaimOccupationRatePO> claimOccupationRatePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePOList 理赔职业给付比例对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimOccupationRate(List<ClaimOccupationRatePO> claimOccupationRatePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimOccupationRatePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
	 /**
	     * @description 查询职业给付系数信息
	     * <AUTHOR>
	     * @param claimOccupationRatePO 理赔职业给付比例对象参数
	     * @return List<ClaimOccupationRatePO>
	     */
     public List<ClaimOccupationRatePO> queryOccupationRateByCaseId(ClaimOccupationRatePO claimOccupationRatePO);
     /**
      * 
      * @description 修改录入职业给付信息
      * @version
      * @title
      * <AUTHOR>
      * @param claimOccupationRatePO 理赔职业给付比例对象参数
      * @return
      */
     public void updateClaimOccupationRateByCaseId(ClaimOccupationRatePO claimOccupationRatePO);
     /**
      * @description 删除数据
      * @version
      * @title
      * <AUTHOR>
      * @param claimOccupationRatePO 理赔职业给付比例对象
      */
      public void deleteClmOccupationRate(ClaimOccupationRatePO claimOccupationRatePO);
  	 /**
	     * @description 查询职业给付系数信息
	     * <AUTHOR>
	     * @param claimOccupationRatePO 理赔职业给付比例对象参数
	     * @return List<ClaimOccupationRatePO>
	     */
      public List<ClaimOccupationRatePO>  queryOldOccupationRateByCaseId(ClaimOccupationRatePO claimOccupationRatePO);

 }
 