package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.bo.PayDetailBO;
import com.nci.tunan.clm.interfaces.model.po.BeneAndPayeePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimAuditApprovePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBenePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimContractRelievePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPayPO;
import com.nci.tunan.clm.interfaces.model.po.PayDetailPO;
import com.nci.tunan.clm.interfaces.model.po.SurveyApplyPO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimContractRelieveVO;
import com.nci.udmp.framework.exception.app.BizException;

/**
 * 
 * @description  出具审核结论
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午5:18:22
 */
public interface IClaimAuditConclusionDao {
    /**
     * 查询受益人和领款人信息  
     * @param claimPayPO  领款人
     * @return  List<BeneAndPayeeBO>受益人与领款人信息的集合
     * @throws BizException
     */
    public List<BeneAndPayeePO> queryBeneAndPayeeMsg(ClaimPayPO claimPayPO) throws BizException;
    /**
     * 
     * @description 理赔关怀查询受益人信息
     * @version
     * @title
     * <AUTHOR> <EMAIL> 
     * @see com.nci.tunan.clm.dao.IClaimAuditConclusionDao#queryBeneAndPayeeMsg(com.nci.tunan.clm.interfaces.model.po.BeneAndPayeePO)
     * @param claimPayPO
     * @return
     * @throws BizException
     */
   public List<ClaimBenePO> queryBeneForClaimCare(
           ClaimBenePO claimBenePO) throws BizException;
   /**
    * 
    * @description 审核模块  查询医疗赔付明细信息
    * @version V1.0.0
    * @title
    * <AUTHOR>
    * @param payDetailPO 医疗赔付明细
    * @return
    * @throws BizException
    */
    public List<PayDetailPO> queryMedicalPayDetail(PayDetailPO payDetailPO)  throws BizException;
    /**
     * 
     * @description 审核模块  查询非医疗赔付明细信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param payDetailPO  医疗赔付明细信息
     * @return
     * @throws BizException
     */
    public List<PayDetailPO> queryUnMedicalPayDetail(PayDetailPO payDetailPO)  throws BizException;
    /**
     * 
     * @description 根据caseId查询审核结论
     * @version
     * @title
     * <AUTHOR> <EMAIL> 
     * @param claimAuditApprovePO  审核审批结论
     * @return List<ClaimAuditApprovePO>
     * @throws BizException
     */
   public List<ClaimAuditApprovePO> queryClaimAuditApproveByCaseId(ClaimAuditApprovePO claimAuditApprovePO) throws BizException;
   /**
    * 
    * @description 根据caseId查询审核结论踢出审核意见为空的
    * @version
    * @title
    * <AUTHOR> <EMAIL> 
    * @param claimAuditApprovePO   审核审批结论
    * @return List<ClaimAuditApprovePO>
    * @throws BizException
    */
   public List<ClaimAuditApprovePO> queryClaimAuditApproveRegression(ClaimAuditApprovePO claimAuditApprovePO) throws BizException;
   /**
    * 
    * @description 更新案件赔付结论
    * @version V1.0.0
    * @title
    * <AUTHOR>
    * @param claimAuditApprovePO  审核审批结论
    * @return
    * @throws BizException
    */
   public ClaimAuditApprovePO updateClaimAuditApprove(ClaimAuditApprovePO claimAuditApprovePO) throws  BizException;
   /**
    * 
    * @description 保存案件赔付结论
    * @version V1.0.0
    * @title
    * <AUTHOR>
    * @param claimAuditApprovePO  审核审批结论
    * @return
    * @throws BizException
    */
   public ClaimAuditApprovePO saveClaimAuditApprove(ClaimAuditApprovePO claimAuditApprovePO) throws BizException;
   /**
    * 
    * @description 出具审核结论更新赔付明细备注
    * @version
    * @title
    * <AUTHOR> <EMAIL> 
    * @param payDetailBO 赔付明细
    * @return
    * @throws BizException
    */
   public PayDetailBO updateClaimLiabForMark(PayDetailBO payDetailBO) throws BizException;
	 /**
       /**
   * 查询调查信息
   * @description
   * @version V1.0.0
   * @title
   * <AUTHOR>
   * @param surveyApplyPO  调查申请
   * @return
   */
    public List<SurveyApplyPO> querySurveyInfoByCaseId(SurveyApplyPO surveyApplyPO);
    
    
}
