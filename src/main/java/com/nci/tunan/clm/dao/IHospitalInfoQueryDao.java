package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimHospitalServicePO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 
 * @description  医院信息查询功能，DAO层的接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午8:16:32
 */
	public interface IHospitalInfoQueryDao {
	
		/**
		 * 
		 * @description  查询所有的方法
		 * @version V1.0.0
		 * @title
		 * <AUTHOR> <EMAIL>
		 * @date 2015-05-15 下午1:54:43 
		 * @param hospitalPO  医院
		 * @return  医院
		 */  
	public List<ClaimHospitalServicePO> findHospitalPO(ClaimHospitalServicePO hospitalPO)throws BizException;
	
	/**
	 * 
	 * @description  分页查询的方法
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2015-05-15 下午1:55:13 
	 * @param hospitalPO  医院
	 * @param currentPage 当前页
	 * @return  医院
	 */
	public CurrentPage<ClaimHospitalServicePO> showHospital(ClaimHospitalServicePO hospitalPO,CurrentPage<ClaimHospitalServicePO> currentPage)throws BizException;
}
