package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ContractMasterPO;
/**
 * 
 * @description  保单条款 dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统-保单条款
 * @date 2015-05-15 下午2:22:32
 */
public interface IQueryPolicyClauseDao {
	/**
	 * 查看保单条款 根据赔案号查询相关保单号和险种代码
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param contractMasterPO 保单抄单表对象
	 * @return  保单抄单表对象集合
	 */
    public List<ContractMasterPO> queryPolicyListByCaseId(ContractMasterPO contractMasterPO);
}
