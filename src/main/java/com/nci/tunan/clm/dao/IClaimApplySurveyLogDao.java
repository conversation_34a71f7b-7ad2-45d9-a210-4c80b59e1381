package com.nci.tunan.clm.dao;
import com.nci.tunan.clm.interfaces.model.po.ClaimApplySurveyLogPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimApplySurveyLogDao接口
 * <AUTHOR> 
 * @date 2022-07-09 11:19:25  
 */
 public interface IClaimApplySurveyLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return ClaimApplySurveyLogPO 添加结果
     */
	 public ClaimApplySurveyLogPO addClaimApplySurveyLog(ClaimApplySurveyLogPO claimApplySurveyLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimApplySurveyLog(ClaimApplySurveyLogPO claimApplySurveyLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return ClaimApplySurveyLogPO 修改结果
     */
	 public ClaimApplySurveyLogPO updateClaimApplySurveyLog(ClaimApplySurveyLogPO claimApplySurveyLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return ClaimApplySurveyLogPO 查询结果对象
     */
	 public ClaimApplySurveyLogPO findClaimApplySurveyLog(ClaimApplySurveyLogPO claimApplySurveyLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return List<ClaimApplySurveyLogPO> 查询结果List
     */
	 public List<ClaimApplySurveyLogPO> findAllClaimApplySurveyLog(ClaimApplySurveyLogPO claimApplySurveyLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimApplySurveyLogTotal(ClaimApplySurveyLogPO claimApplySurveyLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimApplySurveyLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimApplySurveyLogPO> queryClaimApplySurveyLogForPage(ClaimApplySurveyLogPO claimApplySurveyLogPO, CurrentPage<ClaimApplySurveyLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimApplySurveyLog(List<ClaimApplySurveyLogPO> claimApplySurveyLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimApplySurveyLog(List<ClaimApplySurveyLogPO> claimApplySurveyLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimApplySurveyLog(List<ClaimApplySurveyLogPO> claimApplySurveyLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimApplySurveyLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimApplySurveyLog(ClaimApplySurveyLogPO claimApplySurveyLogPO);
	 
 }
 