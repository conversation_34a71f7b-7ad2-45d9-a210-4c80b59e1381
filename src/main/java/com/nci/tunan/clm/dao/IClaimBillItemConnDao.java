package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.ClaimBillItemConnPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimBillItemConnDao接口
 * <AUTHOR> 
 * @date 2025-07-14 11:30:13  
 */
 public interface IClaimBillItemConnDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return ClaimBillItemConnPO 添加结果
     */
	 public ClaimBillItemConnPO addClaimBillItemConn(ClaimBillItemConnPO claimBillItemConnPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBillItemConn(ClaimBillItemConnPO claimBillItemConnPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return ClaimBillItemConnPO 修改结果
     */
	 public ClaimBillItemConnPO updateClaimBillItemConn(ClaimBillItemConnPO claimBillItemConnPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return ClaimBillItemConnPO 查询结果对象
     */
	 public ClaimBillItemConnPO findClaimBillItemConn(ClaimBillItemConnPO claimBillItemConnPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return List<ClaimBillItemConnPO> 查询结果List
     */
	 public List<ClaimBillItemConnPO> findAllClaimBillItemConn(ClaimBillItemConnPO claimBillItemConnPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBillItemConnTotal(ClaimBillItemConnPO claimBillItemConnPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillItemConnPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBillItemConnPO> queryClaimBillItemConnForPage(ClaimBillItemConnPO claimBillItemConnPO, CurrentPage<ClaimBillItemConnPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBillItemConn(List<ClaimBillItemConnPO> claimBillItemConnPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBillItemConn(List<ClaimBillItemConnPO> claimBillItemConnPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBillItemConn(List<ClaimBillItemConnPO> claimBillItemConnPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBillItemConnPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBillItemConn(ClaimBillItemConnPO claimBillItemConnPO);
	 
	 /**
	  * 删除数据根据Case_id
	  * @param claimBillItemConnPO
	  */
     public boolean deleteClaimBillItemConnByCaseId(ClaimBillItemConnPO claimBillItemConnPO);
	 
 }
 