package com.nci.tunan.clm.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimDirectBusiLogPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimDirectBusiLogDao接口
 * <AUTHOR>  
 * @date 2025-07-08 17:11:39  
 */
 public interface IClaimDirectBusiLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return ClaimDirectBusiLogPO 添加结果
     */
	 public ClaimDirectBusiLogPO addClaimDirectBusiLog(ClaimDirectBusiLogPO claimDirectBusiLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimDirectBusiLog(ClaimDirectBusiLogPO claimDirectBusiLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return ClaimDirectBusiLogPO 修改结果
     */
	 public ClaimDirectBusiLogPO updateClaimDirectBusiLog(ClaimDirectBusiLogPO claimDirectBusiLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return ClaimDirectBusiLogPO 查询结果对象
     */
	 public ClaimDirectBusiLogPO findClaimDirectBusiLog(ClaimDirectBusiLogPO claimDirectBusiLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return List<ClaimDirectBusiLogPO> 查询结果List
     */
	 public List<ClaimDirectBusiLogPO> findAllClaimDirectBusiLog(ClaimDirectBusiLogPO claimDirectBusiLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimDirectBusiLogTotal(ClaimDirectBusiLogPO claimDirectBusiLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> 
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectBusiLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectBusiLogPO> queryClaimDirectBusiLogForPage(ClaimDirectBusiLogPO claimDirectBusiLogPO, CurrentPage<ClaimDirectBusiLogPO> currentPage);
     
     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> 
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDirectBusiLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimDirectBusiLogPO> findClaimDirectBusiLogsPage(ClaimDirectBusiLogPO claimDirectBusiLogPO, CurrentPage<ClaimDirectBusiLogPO> currentPage);
     

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimDirectBusiLog(List<ClaimDirectBusiLogPO> claimDirectBusiLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimDirectBusiLog(List<ClaimDirectBusiLogPO> claimDirectBusiLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimDirectBusiLog(List<ClaimDirectBusiLogPO> claimDirectBusiLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> 
     * @param claimDirectBusiLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimDirectBusiLog(ClaimDirectBusiLogPO claimDirectBusiLogPO);
	 
 }
 