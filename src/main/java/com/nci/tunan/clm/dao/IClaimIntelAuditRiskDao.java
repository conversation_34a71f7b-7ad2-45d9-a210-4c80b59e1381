package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimIntelAuditRiskPO;


/** 
 * @description IClaimIntelAuditRiskDao接口
 * <AUTHOR> 
 * @date 2022-07-25 10:20:42  
 */
 public interface IClaimIntelAuditRiskDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return ClaimIntelAuditRiskPO 添加结果
     */
	 public ClaimIntelAuditRiskPO addClaimIntelAuditRisk(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimIntelAuditRisk(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return ClaimIntelAuditRiskPO 修改结果
     */
	 public ClaimIntelAuditRiskPO updateClaimIntelAuditRisk(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return ClaimIntelAuditRiskPO 查询结果对象
     */
	 public ClaimIntelAuditRiskPO findClaimIntelAuditRisk(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return List<ClaimIntelAuditRiskPO> 查询结果List
     */
	 public List<ClaimIntelAuditRiskPO> findAllClaimIntelAuditRisk(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimIntelAuditRiskTotal(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimIntelAuditRiskPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimIntelAuditRiskPO> queryClaimIntelAuditRiskForPage(ClaimIntelAuditRiskPO claimIntelAuditRiskPO, CurrentPage<ClaimIntelAuditRiskPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimIntelAuditRisk(List<ClaimIntelAuditRiskPO> claimIntelAuditRiskPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimIntelAuditRisk(List<ClaimIntelAuditRiskPO> claimIntelAuditRiskPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimIntelAuditRisk(List<ClaimIntelAuditRiskPO> claimIntelAuditRiskPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimIntelAuditRisk(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
	 /**
     * @description 根据赔案ID删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIntelAuditRiskPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteClaimIntelAuditRiskByCaseId(ClaimIntelAuditRiskPO claimIntelAuditRiskPO);
 }
 