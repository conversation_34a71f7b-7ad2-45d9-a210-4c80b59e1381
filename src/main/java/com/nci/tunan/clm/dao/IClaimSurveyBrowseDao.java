package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimSurveyBrowsePO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description 调查结论浏览记录信息表Dao
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2017-03-16 17:08:21  
 */
 public interface IClaimSurveyBrowseDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return ClaimSurveyBrowsePO 添加结果
     */
	 public ClaimSurveyBrowsePO addClaimSurveyBrowse(ClaimSurveyBrowsePO claimSurveyBrowsePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimSurveyBrowse(ClaimSurveyBrowsePO claimSurveyBrowsePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return ClaimSurveyBrowsePO 修改结果
     */
	 public ClaimSurveyBrowsePO updateClaimSurveyBrowse(ClaimSurveyBrowsePO claimSurveyBrowsePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return ClaimSurveyBrowsePO 查询结果对象
     */
	 public ClaimSurveyBrowsePO findClaimSurveyBrowse(ClaimSurveyBrowsePO claimSurveyBrowsePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return List<ClaimSurveyBrowsePO> 查询结果List
     */
	 public List<ClaimSurveyBrowsePO> findAllClaimSurveyBrowse(ClaimSurveyBrowsePO claimSurveyBrowsePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return int 查询结果条数
     */
	 public int findClaimSurveyBrowseTotal(ClaimSurveyBrowsePO claimSurveyBrowsePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSurveyBrowsePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimSurveyBrowsePO> queryClaimSurveyBrowseForPage(ClaimSurveyBrowsePO claimSurveyBrowsePO, CurrentPage<ClaimSurveyBrowsePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePOList 调查结论浏览记录对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimSurveyBrowse(List<ClaimSurveyBrowsePO> claimSurveyBrowsePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePOList 调查结论浏览记录对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimSurveyBrowse(List<ClaimSurveyBrowsePO> claimSurveyBrowsePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePOList 调查结论浏览记录对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimSurveyBrowse(List<ClaimSurveyBrowsePO> claimSurveyBrowsePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimSurveyBrowsePO 调查结论浏览记录对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimSurveyBrowse(ClaimSurveyBrowsePO claimSurveyBrowsePO);
	 
 }
 