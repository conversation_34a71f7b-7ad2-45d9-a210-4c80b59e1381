package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimLiabFormulaPO;
import com.nci.udmp.framework.model.CurrentPage;
/**
 * 
 * @description 理赔责任公式明细表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:45:08
 */
 public interface IClaimLiabFormulaDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return ClaimLiabFormulaPO 添加结果
     */
	 public ClaimLiabFormulaPO addClaimLiabFormula(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimLiabFormula(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
	 /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimLiabFormulaByCaseId(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return ClaimLiabFormulaPO 修改结果
     */
	 public ClaimLiabFormulaPO updateClaimLiabFormula(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return ClaimLiabFormulaPO 查询结果对象
     */
	 public ClaimLiabFormulaPO findClaimLiabFormula(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return List<ClaimLiabFormulaPO> 查询结果List
     */
	 public List<ClaimLiabFormulaPO> findAllClaimLiabFormula(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimLiabFormulaTotal(ClaimLiabFormulaPO claimLiabFormulaPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimLiabFormulaPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimLiabFormulaPO> queryClaimLiabFormulaForPage(ClaimLiabFormulaPO claimLiabFormulaPO, CurrentPage<ClaimLiabFormulaPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimLiabFormula(List<ClaimLiabFormulaPO> claimLiabFormulaPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimLiabFormula(List<ClaimLiabFormulaPO> claimLiabFormulaPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimLiabFormula(List<ClaimLiabFormulaPO> claimLiabFormulaPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabFormulaPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimLiabFormula(ClaimLiabFormulaPO claimLiabFormulaPO);
	 
 }
 