package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimZjElecbillTaskPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimZjElecbillTaskDao接口
 * <AUTHOR> 
 * @date 2024-10-23 18:06:10  
 */
 public interface IClaimZjElecbillTaskDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return ClaimZjElecbillTaskPO 添加结果
     */
	 public ClaimZjElecbillTaskPO addClaimZjElecbillTask(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimZjElecbillTask(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return ClaimZjElecbillTaskPO 修改结果
     */
	 public ClaimZjElecbillTaskPO updateClaimZjElecbillTask(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return ClaimZjElecbillTaskPO 查询结果对象
     */
	 public ClaimZjElecbillTaskPO findClaimZjElecbillTask(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);
	 
	 /**
     * @description 根据caseId查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return ClaimZjElecbillTaskPO 查询结果对象
     */
	 public ClaimZjElecbillTaskPO findClaimZjElecbillTaskByCaseId(ClaimZjElecbillTaskPO zjElecbillTaskPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return List<ClaimZjElecbillTaskPO> 查询结果List
     */
	 public List<ClaimZjElecbillTaskPO> findAllClaimZjElecbillTask(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimZjElecbillTaskTotal(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimZjElecbillTaskPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimZjElecbillTaskPO> queryClaimZjElecbillTaskForPage(ClaimZjElecbillTaskPO claimZjElecbillTaskPO, CurrentPage<ClaimZjElecbillTaskPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimZjElecbillTask(List<ClaimZjElecbillTaskPO> claimZjElecbillTaskPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimZjElecbillTask(List<ClaimZjElecbillTaskPO> claimZjElecbillTaskPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimZjElecbillTask(List<ClaimZjElecbillTaskPO> claimZjElecbillTaskPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimZjElecbillTaskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimZjElecbillTask(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);

	 /**
     * 查询需要票据选定的数据：理赔浙江保险综合平台电票任务表中任务状态是“待锁定/锁定失败”,且回退标识不为1的赔案
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimZjElecbillTaskPO 票据选定信息
     * @return List<ClaimZjElecbillTaskPO> 结果集
     */
	 public List<ClaimZjElecbillTaskPO> queryZJLockCases(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);

	 /**
     * 查询需要理赔信息同步的数据：理赔浙江保险综合平台电票任务表中任务状态是“待入账/入账失败”，且回退标识不为1的赔案，同时案件已支付完成
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimZjElecbillTaskPO 票据理赔信息同步信息
     * @return List<ClaimZjElecbillTaskPO> 结果集
     */
	 public List<ClaimZjElecbillTaskPO> queryZJEntryCases(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);

	 /**
     * 查询理赔浙江保险综合平台电票任务表中任务状态是“已入账/撤销入账失败”，且回退标识为1的数据
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimZjElecbillTaskPO 票据理赔信息同步撤销信息
     * @return List<ClaimZjElecbillTaskPO> 结果集
     */
	 public List<ClaimZjElecbillTaskPO> queryZJUnEntryCases(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);

	 /**
     * 查询理赔浙江保险综合平台电票任务表中任务状态是“待入账/入账失败/撤销锁定失败”，且回退标识为1的数据
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimZjElecbillTaskPO 票据撤销选定信息
     * @return List<ClaimZjElecbillTaskPO> 结果集
     */
	 public List<ClaimZjElecbillTaskPO> queryZJUnLockCases(ClaimZjElecbillTaskPO claimZjElecbillTaskPO);

 }
 