package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasecompPO;
import com.nci.udmp.framework.exception.app.BizException;
/**
 * 报案确认
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-报案确认
 * @date 2015-05-15 下午8:53:46
 */
public interface IClaimReportConfirmDao {
    /**
     * 根据caseId去查询表t_claim_case工作流所需数据
     * @param claimCaseBO   赔案主表对象
     * @return  赔案主表对象列表
     * @throws BizException
     */
    public ClaimCasecompPO findClaimBpmRequest(ClaimCasePO claimCasePO) throws BizException;
    /**
     * 
     * @description 报案共享池初始化数据查询
     * @version
     * @title
     * @<NAME_EMAIL> 
     * @param claimCasePO 赔案主表对象
     * @return  赔案主表对象列表
     * @throws BizException
     */
    public List<ClaimCasecompPO> findClaimShareRequest(ClaimCasePO claimCasePO) throws BizException;
    /**
     * 报案共享池初始化数据查询
     * <AUTHOR>
     * @param claimCasePO   赔案主表对象
     * @return  赔案主表对象列表
     * @throws BizException  
     */
    public List<ClaimCasePO> findReportPoolData(ClaimCasePO claimCasePO) throws BizException;
    /**
     * 
     * @description 查询出险人未结案或关闭的“垫付类”调查类型的赔案
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCasePO
     * @return
     * @throws BizException
     */
	public List<ClaimCasePO> findClaimCaseBySurveyType(ClaimCasePO claimCasePO) throws BizException;
}
