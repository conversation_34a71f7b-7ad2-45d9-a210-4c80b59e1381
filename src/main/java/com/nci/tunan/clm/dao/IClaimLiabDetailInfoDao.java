package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimLiabDetailInfoPO;
import com.nci.udmp.framework.model.CurrentPage;
import java.util.Map;
import java.util.List;


/**
 * @description T_CLAIM_LIAB_DETAIL_INFODao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 下午4:44:46
 */
 public interface IClaimLiabDetailInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return ClaimLiabDetailInfoPO 添加结果
     */
	 public ClaimLiabDetailInfoPO addClaimLiabDetailInfo(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimLiabDetailInfo(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return ClaimLiabDetailInfoPO 修改结果
     */
	 public ClaimLiabDetailInfoPO updateClaimLiabDetailInfo(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return ClaimLiabDetailInfoPO 查询结果对象
     */
	 public ClaimLiabDetailInfoPO findClaimLiabDetailInfo(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return List<ClaimLiabDetailInfoPO> 查询结果List
     */
	 public List<ClaimLiabDetailInfoPO> findAllClaimLiabDetailInfo(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimLiabDetailInfoTotal(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimLiabDetailInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimLiabDetailInfoPO> queryClaimLiabDetailInfoForPage(ClaimLiabDetailInfoPO claimLiabDetailInfoPO, CurrentPage<ClaimLiabDetailInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimLiabDetailInfo(List<ClaimLiabDetailInfoPO> claimLiabDetailInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimLiabDetailInfo(List<ClaimLiabDetailInfoPO> claimLiabDetailInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimLiabDetailInfo(List<ClaimLiabDetailInfoPO> claimLiabDetailInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimLiabDetailInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimLiabDetailInfo(ClaimLiabDetailInfoPO claimLiabDetailInfoPO);
	 
 }
 