package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimRoleServicingPO;
import com.nci.udmp.framework.model.CurrentPage;

import java.util.Map;
 

import java.util.List;
 
/** 
 * @description 理赔维护角色配置表DaoImpl
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2018-06-19 09:42:22  
 */
 public interface IClaimRoleServicingDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return ClaimRoleServicingPO 添加结果
     */
	 public ClaimRoleServicingPO addClaimRoleServicing(ClaimRoleServicingPO claimRoleServicingPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRoleServicing(ClaimRoleServicingPO claimRoleServicingPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return ClaimRoleServicingPO 修改结果
     */
	 public ClaimRoleServicingPO updateClaimRoleServicing(ClaimRoleServicingPO claimRoleServicingPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return ClaimRoleServicingPO 查询结果对象
     */
	 public ClaimRoleServicingPO findClaimRoleServicing(ClaimRoleServicingPO claimRoleServicingPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return List<ClaimRoleServicingPO> 查询结果List
     */
	 public List<ClaimRoleServicingPO> findAllClaimRoleServicing(ClaimRoleServicingPO claimRoleServicingPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return int 查询结果条数
     */
	 public int findClaimRoleServicingTotal(ClaimRoleServicingPO claimRoleServicingPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRoleServicingPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRoleServicingPO> queryClaimRoleServicingForPage(ClaimRoleServicingPO claimRoleServicingPO, CurrentPage<ClaimRoleServicingPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPOList 维护角色配置对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRoleServicing(List<ClaimRoleServicingPO> claimRoleServicingPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPOList 维护角色配置对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRoleServicing(List<ClaimRoleServicingPO> claimRoleServicingPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPOList 维护角色配置对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRoleServicing(List<ClaimRoleServicingPO> claimRoleServicingPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingPO 维护角色配置对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRoleServicing(ClaimRoleServicingPO claimRoleServicingPO);
	 
 }
 