package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.LinkSwitchSettingsPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;


/** 
 * @description ILinkSwitchSettingsDao接口
 * <AUTHOR> 
 * @date 2020-12-08 17:34:27  
 */
 public interface ILinkSwitchSettingsDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return LinkSwitchSettingsPO 添加结果
     */
	 public LinkSwitchSettingsPO addLinkSwitchSettings(LinkSwitchSettingsPO linkSwitchSettingsPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteLinkSwitchSettings(LinkSwitchSettingsPO linkSwitchSettingsPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return LinkSwitchSettingsPO 修改结果
     */
	 public LinkSwitchSettingsPO updateLinkSwitchSettings(LinkSwitchSettingsPO linkSwitchSettingsPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return LinkSwitchSettingsPO 查询结果对象
     */
	 public LinkSwitchSettingsPO findLinkSwitchSettings(LinkSwitchSettingsPO linkSwitchSettingsPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return List<LinkSwitchSettingsPO> 查询结果List
     */
	 public List<LinkSwitchSettingsPO> findAllLinkSwitchSettings(LinkSwitchSettingsPO linkSwitchSettingsPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return int 查询结果条数
     */
	 public int findLinkSwitchSettingsTotal(LinkSwitchSettingsPO linkSwitchSettingsPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<LinkSwitchSettingsPO> 查询结果的当前页对象
     */
	 public CurrentPage<LinkSwitchSettingsPO> queryLinkSwitchSettingsForPage(LinkSwitchSettingsPO linkSwitchSettingsPO, CurrentPage<LinkSwitchSettingsPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveLinkSwitchSettings(List<LinkSwitchSettingsPO> linkSwitchSettingsPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateLinkSwitchSettings(List<LinkSwitchSettingsPO> linkSwitchSettingsPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteLinkSwitchSettings(List<LinkSwitchSettingsPO> linkSwitchSettingsPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param linkSwitchSettingsPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapLinkSwitchSettings(LinkSwitchSettingsPO linkSwitchSettingsPO);

	public List<LinkSwitchSettingsPO> findLinkSwitchSettingsList(
			LinkSwitchSettingsPO addLinkSwitchSettingsPO);

	public LinkSwitchSettingsPO findLinkSwitchSettingsByListId(
			LinkSwitchSettingsPO addLinkSwitchSettingsPO);
	 
 }
 