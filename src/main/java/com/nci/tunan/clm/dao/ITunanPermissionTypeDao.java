package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.PermissionTypePO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description IPermissionTypeDao 权限类型接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-09-25 09:17:55  
 */
 public interface ITunanPermissionTypeDao {
     /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return PermissionTypePO 添加结果
     */
     public PermissionTypePO addPermissionType(PermissionTypePO permissionTypePO);
     
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return boolean 删除是否成功
     */
     public boolean deletePermissionType(PermissionTypePO permissionTypePO);
     
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return PermissionTypePO 修改结果
     */
     public PermissionTypePO updatePermissionType(PermissionTypePO permissionTypePO);
     
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return PermissionTypePO 查询结果对象
     */
     public PermissionTypePO findPermissionType(PermissionTypePO permissionTypePO);
     
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return List<PermissionTypePO> 查询结果List
     */
     public List<PermissionTypePO> findAllPermissionType(PermissionTypePO permissionTypePO);
     
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return int 查询结果条数
     */
     public int findPermissionTypeTotal(PermissionTypePO permissionTypePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PermissionTypePO> 查询结果的当前页对象
     */
     public CurrentPage<PermissionTypePO> queryPermissionTypeForPage(PermissionTypePO permissionTypePO, CurrentPage<PermissionTypePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePOList  权限类型对象列表
     * @return boolean 批量添加是否成功
     */
     public boolean batchSavePermissionType(List<PermissionTypePO> permissionTypePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePOList  权限类型对象列表
     * @return boolean 批量修改是否成功
     */
     public boolean batchUpdatePermissionType(List<PermissionTypePO> permissionTypePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePOList  权限类型对象列表
     * @return boolean 批量删除是否成功
     */
     public boolean batchDeletePermissionType(List<PermissionTypePO> permissionTypePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param permissionTypePO  权限类型对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
     public List<Map<String, Object>> findAllMapPermissionType(PermissionTypePO permissionTypePO);
     
     /**
      * 案件审批权限
      * @description
      * @version
      * @title
      * @<NAME_EMAIL>
      * @param permissionTypePO  权限类型
      * @return  权限类型集合
      */
     public List<PermissionTypePO> queryPermissionTypeList(PermissionTypePO permissionTypePO);
     
 }
 