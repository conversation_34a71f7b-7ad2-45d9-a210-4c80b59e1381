package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimInspectTaskPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskInspectProPO;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description IClaimRiskInspectProDao接口
 * <AUTHOR> 
 * @date 2023-02-06 10:46:42  
 */
 public interface IClaimRiskInspectProDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return ClaimRiskInspectProPO 添加结果
     */
	 public ClaimRiskInspectProPO addClaimRiskInspectPro(ClaimRiskInspectProPO claimRiskInspectProPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimRiskInspectPro(ClaimRiskInspectProPO claimRiskInspectProPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return ClaimRiskInspectProPO 修改结果
     */
	 public ClaimRiskInspectProPO updateClaimRiskInspectPro(ClaimRiskInspectProPO claimRiskInspectProPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return ClaimRiskInspectProPO 查询结果对象
     */
	 public ClaimRiskInspectProPO findClaimRiskInspectPro(ClaimRiskInspectProPO claimRiskInspectProPO);
	 
	 /**
     * @description 根据检查批次号查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return ClaimRiskInspectProPO 查询结果对象
     */
	 public ClaimRiskInspectProPO findClaimRiskInspectProByInspectProCode(ClaimRiskInspectProPO claimRiskInspectProPO);
	  	
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return List<ClaimRiskInspectProPO> 查询结果List
     */
	 public List<ClaimRiskInspectProPO> findAllClaimRiskInspectPro(ClaimRiskInspectProPO claimRiskInspectProPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimRiskInspectProTotal(ClaimRiskInspectProPO claimRiskInspectProPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRiskInspectProPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimRiskInspectProPO> queryClaimRiskInspectProForPage(ClaimRiskInspectProPO claimRiskInspectProPO, CurrentPage<ClaimRiskInspectProPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimRiskInspectPro(List<ClaimRiskInspectProPO> claimRiskInspectProPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimRiskInspectPro(List<ClaimRiskInspectProPO> claimRiskInspectProPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimRiskInspectPro(List<ClaimRiskInspectProPO> claimRiskInspectProPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimRiskInspectPro(ClaimRiskInspectProPO claimRiskInspectProPO);

	 /**
     * @description 根据工作流返回的赔案号查询任务详细信息
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return List<ClaimRiskInspectProPO> 查询结果List
     */

	 public List<ClaimInspectTaskPO> findReviseTaskByCaseNo(ClaimInspectTaskPO claimInspectTaskPO);

	public List<ClaimInspectTaskPO> findAllInspectTaskForSelf(ClaimInspectTaskPO claimInspectTaskPO);

	public List<ClaimRiskInspectProPO> findClaimRiskInspectProByCaseNo(ClaimRiskInspectProPO claimRiskInspectProPO);
	
	public List<ClaimRiskInspectProPO> findClaimRiskInspectProListByCaseNo(ClaimRiskInspectProPO claimRiskInspectProPO);
	/**
	 * @description 上载赔案-点击选择按钮
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimRiskInspectProVO 
	*/
	public List<ClaimRiskInspectProPO> findInspectProDetail(ClaimRiskInspectProPO claimRiskInspectProPO);
	/**
     * @description 根据制定检查项目页面条件查询
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return List<ClaimRiskInspectProPO> 查询结果List
     */
	 public List<ClaimRiskInspectProPO> queryCheckProjectList(ClaimRiskInspectProPO claimRiskInspectProPO);
	 /**
	  * @description 根据制定检查项目页面条件分页查询
	  * @version
	  * @title
	  * <AUTHOR>
	  * @param claimRiskInspectProPO 对象
	  * @return List<ClaimRiskInspectProPO> 查询结果List
	  */
	 public CurrentPage<ClaimRiskInspectProPO> queryCheckProjectListPage(ClaimRiskInspectProPO claimRiskInspectProPO, CurrentPage<ClaimRiskInspectProPO> currentPage);
	 /**
     * @description 查询制定检查项目详细信息
     * @version
     * @title
     * <AUTHOR>
     * @param claimRiskInspectProPO 对象
     * @return ClaimRiskInspectProPO 查询结果对象
     */
	 public ClaimRiskInspectProPO findClaimRiskInspectProDetail(ClaimRiskInspectProPO claimRiskInspectProPO);
 }
 