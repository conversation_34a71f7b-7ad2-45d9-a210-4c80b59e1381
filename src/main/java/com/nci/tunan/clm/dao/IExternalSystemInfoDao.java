package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ExternalSystemInfoPO;
import com.nci.udmp.framework.model.*;
import java.util.Map; 
import java.util.List; 

/**
 * 
 * @description IExternalSystemInfoDao外部系统信息接口
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 上午9:47:15
 */
 public interface IExternalSystemInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return ExternalSystemInfoPO 添加结果
     */
	 public ExternalSystemInfoPO addExternalSystemInfo(ExternalSystemInfoPO externalSystemInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteExternalSystemInfo(ExternalSystemInfoPO externalSystemInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return ExternalSystemInfoPO 修改结果
     */
	 public ExternalSystemInfoPO updateExternalSystemInfo(ExternalSystemInfoPO externalSystemInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return ExternalSystemInfoPO 查询结果对象
     */
	 public ExternalSystemInfoPO findExternalSystemInfo(ExternalSystemInfoPO externalSystemInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return List<ExternalSystemInfoPO> 查询结果List
     */
	 public List<ExternalSystemInfoPO> findAllExternalSystemInfo(ExternalSystemInfoPO externalSystemInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return int 查询结果条数
     */
	 public int findExternalSystemInfoTotal(ExternalSystemInfoPO externalSystemInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ExternalSystemInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ExternalSystemInfoPO> queryExternalSystemInfoForPage(ExternalSystemInfoPO externalSystemInfoPO, CurrentPage<ExternalSystemInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPOList 外部系统信息对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveExternalSystemInfo(List<ExternalSystemInfoPO> externalSystemInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPOList 外部系统信息对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateExternalSystemInfo(List<ExternalSystemInfoPO> externalSystemInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPOList 外部系统信息对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteExternalSystemInfo(List<ExternalSystemInfoPO> externalSystemInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param externalSystemInfoPO 外部系统信息对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapExternalSystemInfo(ExternalSystemInfoPO externalSystemInfoPO);
	 
 }
 