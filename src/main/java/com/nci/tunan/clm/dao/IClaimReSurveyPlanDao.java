package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimReSurveyPlanPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;


/** 
 * @description 复勘调查计划表Dao
 * <AUTHOR> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-09-24 11:08:10  
 */
 public interface IClaimReSurveyPlanDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 复勘调查计划对象
     * @return ClaimReSurveyPlanPO 添加结果
     */
	 public ClaimReSurveyPlanPO addClaimReSurveyPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 复勘调查计划对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimReSurveyPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 复勘调查计划对象
     * @return ClaimReSurveyPlanPO 修改结果
     */
	 public ClaimReSurveyPlanPO updateClaimReSurveyPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 复勘调查计划对象
     * @return ClaimReSurveyPlanPO 查询结果对象
     */
	 public ClaimReSurveyPlanPO findClaimReSurveyPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 复勘调查计划对象
     * @return List<ClaimReSurveyPlanPO> 查询结果List
     */
	 public List<ClaimReSurveyPlanPO> findAllClaimReSurveyPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 复勘调查计划对象
     * @return int 查询结果条数
     */
	 public int findClaimReSurveyPlanTotal(ClaimReSurveyPlanPO claimReSurveyPlanPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimReSurveyPlanPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimReSurveyPlanPO> queryClaimReSurveyPlanForPage(ClaimReSurveyPlanPO claimReSurveyPlanPO, CurrentPage<ClaimReSurveyPlanPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPOList 复勘调查计划对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimReSurveyPlan(List<ClaimReSurveyPlanPO> claimReSurveyPlanPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPOList 复勘调查计划对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimReSurveyPlan(List<ClaimReSurveyPlanPO> claimReSurveyPlanPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPOList 复勘调查计划对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimReSurveyPlan(List<ClaimReSurveyPlanPO> claimReSurveyPlanPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimReSurveyPlanPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimReSurveyPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 /**
	     * 
	     * @description 复堪计划列表分页查询
	     * <AUTHOR> <EMAIL>
	     * @param claimReSurveyPlanPO 复勘调查计划
	     * @param currentPage  对象参数
	     * @return CurrentPage<ClaimReSurveyPlanPO>
	     * @throws BizException
	     */
     public CurrentPage<ClaimReSurveyPlanPO> queryRecheckPlan(ClaimReSurveyPlanPO claimReSurveyPlanPO,
            CurrentPage<ClaimReSurveyPlanPO> currentPage);
     /**
      * @description 查询复勘计划通过复勘计划名称
      * <AUTHOR> <EMAIL>
      * @param claimReSurveyPlanPO  复勘调查计划
      * @return ClaimReSurveyPlanPO  复勘调查计划
      */
     public ClaimReSurveyPlanPO queryReSurveyPlanByName(ClaimReSurveyPlanPO claimReSurveyPlanPO);
	 
 }
 