package com.nci.tunan.clm.dao;

import com.nci.tunan.clm.interfaces.model.po.ClaimIdentifyInfoPO;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;


/** 
 * @description IClaimIdentifyInfoDao接口
 * <AUTHOR> 
 * @date 2024-10-16 14:51:41  
 */
 public interface IClaimIdentifyInfoDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return ClaimIdentifyInfoPO 添加结果
     */
	 public ClaimIdentifyInfoPO addClaimIdentifyInfo(ClaimIdentifyInfoPO claimIdentifyInfoPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimIdentifyInfo(ClaimIdentifyInfoPO claimIdentifyInfoPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return ClaimIdentifyInfoPO 修改结果
     */
	 public ClaimIdentifyInfoPO updateClaimIdentifyInfo(ClaimIdentifyInfoPO claimIdentifyInfoPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return ClaimIdentifyInfoPO 查询结果对象
     */
	 public ClaimIdentifyInfoPO findClaimIdentifyInfo(ClaimIdentifyInfoPO claimIdentifyInfoPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return List<ClaimIdentifyInfoPO> 查询结果List
     */
	 public List<ClaimIdentifyInfoPO> findAllClaimIdentifyInfo(ClaimIdentifyInfoPO claimIdentifyInfoPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimIdentifyInfoTotal(ClaimIdentifyInfoPO claimIdentifyInfoPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimIdentifyInfoPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimIdentifyInfoPO> queryClaimIdentifyInfoForPage(ClaimIdentifyInfoPO claimIdentifyInfoPO, CurrentPage<ClaimIdentifyInfoPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimIdentifyInfo(List<ClaimIdentifyInfoPO> claimIdentifyInfoPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimIdentifyInfo(List<ClaimIdentifyInfoPO> claimIdentifyInfoPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimIdentifyInfo(List<ClaimIdentifyInfoPO> claimIdentifyInfoPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimIdentifyInfoPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimIdentifyInfo(ClaimIdentifyInfoPO claimIdentifyInfoPO);
	 
 }
 