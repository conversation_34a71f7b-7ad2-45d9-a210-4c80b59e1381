package com.nci.tunan.clm.dao;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRiskLibraryPO;

/**
 * 
 * <AUTHOR>
 * @date: 2023年8月2日
 * @Description:实时风控预警批处理
 */
public interface RealTimeWarnJobBatchDao {

	List<ClaimRiskLibraryPO> queryRealTimeWarnJob(ClaimRiskLibraryPO claimRiskLibraryPO);

	List<ClaimCasePO> findAllBizNoticeResult(ClaimCasePO claimCasePO);

	public int findAllRealTimeWarnJobBatchCount(ClaimRiskLibraryPO claimRiskLibraryPO);
}
