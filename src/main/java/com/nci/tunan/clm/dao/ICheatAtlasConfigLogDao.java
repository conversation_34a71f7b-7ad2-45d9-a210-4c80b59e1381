package com.nci.tunan.clm.dao;


import com.nci.tunan.clm.interfaces.model.po.CheatAtlasConfigLogPO;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description ICheatAtlasConfigLogDao接口
 * <AUTHOR> 
 * @date 2025-08-25 10:58:35  
 */
 public interface ICheatAtlasConfigLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return CheatAtlasConfigLogPO 添加结果
     */
	 public CheatAtlasConfigLogPO addCheatAtlasConfigLog(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteCheatAtlasConfigLog(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return CheatAtlasConfigLogPO 修改结果
     */
	 public CheatAtlasConfigLogPO updateCheatAtlasConfigLog(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return CheatAtlasConfigLogPO 查询结果对象
     */
	 public CheatAtlasConfigLogPO findCheatAtlasConfigLog(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return List<CheatAtlasConfigLogPO> 查询结果List
     */
	 public List<CheatAtlasConfigLogPO> findAllCheatAtlasConfigLog(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return int 查询结果条数
     */
	 public int findCheatAtlasConfigLogTotal(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CheatAtlasConfigLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<CheatAtlasConfigLogPO> queryCheatAtlasConfigLogForPage(CheatAtlasConfigLogPO cheatAtlasConfigLogPO, CurrentPage<CheatAtlasConfigLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCheatAtlasConfigLog(List<CheatAtlasConfigLogPO> cheatAtlasConfigLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCheatAtlasConfigLog(List<CheatAtlasConfigLogPO> cheatAtlasConfigLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCheatAtlasConfigLog(List<CheatAtlasConfigLogPO> cheatAtlasConfigLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param cheatAtlasConfigLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapCheatAtlasConfigLog(CheatAtlasConfigLogPO cheatAtlasConfigLogPO);

	 /**
	  * 
	 	 * 
	 	 * @description 根据主表主键id查询信息
	 	 * @version V1.0.0
	 * @param currentPage 
	 	 * @title
	 	 * @return
	  */
	public List<CheatAtlasConfigLogPO> findCheatAtlasConfigLogMainListId(
			CheatAtlasConfigLogPO cheatAtlasConfigLogPO, CurrentPage currentPage);

	/**
	 * 
		 * 
		 * @description 根据主表主键id查询信息
		 * @version V1.0.0
		 * @title
		 * @return
	 */
	public CurrentPage<CheatAtlasConfigLogPO> findCheatAtlasConfigLogMainListIdPage(
			CheatAtlasConfigLogPO cheatAtlasConfigLogPO, CurrentPage currentPage);


	 
 }
 