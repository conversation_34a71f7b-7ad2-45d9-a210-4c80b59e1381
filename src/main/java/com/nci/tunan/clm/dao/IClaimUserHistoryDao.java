package com.nci.tunan.clm.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.clm.interfaces.model.po.ClaimUserHistoryPO;


/** 
 * @description IClaimUserHistoryDao接口
 * <AUTHOR> 
 * @date 2022-01-04 15:51:29  
 */
 public interface IClaimUserHistoryDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return ClaimUserHistoryPO 添加结果
     */
	 public ClaimUserHistoryPO addClaimUserHistory(ClaimUserHistoryPO claimUserHistoryPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimUserHistory(ClaimUserHistoryPO claimUserHistoryPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return ClaimUserHistoryPO 修改结果
     */
	 public ClaimUserHistoryPO updateClaimUserHistory(ClaimUserHistoryPO claimUserHistoryPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return ClaimUserHistoryPO 查询结果对象
     */
	 public ClaimUserHistoryPO findClaimUserHistory(ClaimUserHistoryPO claimUserHistoryPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return List<ClaimUserHistoryPO> 查询结果List
     */
	 public List<ClaimUserHistoryPO> findAllClaimUserHistory(ClaimUserHistoryPO claimUserHistoryPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return int 查询结果条数
     */
	 public int findClaimUserHistoryTotal(ClaimUserHistoryPO claimUserHistoryPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimUserHistoryPO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimUserHistoryPO> queryClaimUserHistoryForPage(ClaimUserHistoryPO claimUserHistoryPO, CurrentPage<ClaimUserHistoryPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimUserHistory(List<ClaimUserHistoryPO> claimUserHistoryPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimUserHistory(List<ClaimUserHistoryPO> claimUserHistoryPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimUserHistory(List<ClaimUserHistoryPO> claimUserHistoryPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimUserHistoryPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimUserHistory(ClaimUserHistoryPO claimUserHistoryPO);
	 
 }
 