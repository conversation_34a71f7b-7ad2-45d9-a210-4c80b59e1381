package com.nci.tunan.clm.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.ClaimBusiSpecialRulePO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IClaimBusiSpecialRuleDao接口
 * <AUTHOR> 
 * @date 2022-10-20 20:12:52  
 */
 public interface IClaimBusiSpecialRuleDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return ClaimBusiSpecialRulePO 添加结果
     */
	 public ClaimBusiSpecialRulePO addClaimBusiSpecialRule(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteClaimBusiSpecialRule(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return ClaimBusiSpecialRulePO 修改结果
     */
	 public ClaimBusiSpecialRulePO updateClaimBusiSpecialRule(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return ClaimBusiSpecialRulePO 查询结果对象
     */
	 public ClaimBusiSpecialRulePO findClaimBusiSpecialRule(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return List<ClaimBusiSpecialRulePO> 查询结果List
     */
	 public List<ClaimBusiSpecialRulePO> findAllClaimBusiSpecialRule(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return int 查询结果条数
     */
	 public int findClaimBusiSpecialRuleTotal(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBusiSpecialRulePO> 查询结果的当前页对象
     */
	 public CurrentPage<ClaimBusiSpecialRulePO> queryClaimBusiSpecialRuleForPage(ClaimBusiSpecialRulePO claimBusiSpecialRulePO, CurrentPage<ClaimBusiSpecialRulePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveClaimBusiSpecialRule(List<ClaimBusiSpecialRulePO> claimBusiSpecialRulePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateClaimBusiSpecialRule(List<ClaimBusiSpecialRulePO> claimBusiSpecialRulePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimBusiSpecialRule(List<ClaimBusiSpecialRulePO> claimBusiSpecialRulePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapClaimBusiSpecialRule(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 /**
     * @description 根据理赔类型集合查询
     * @version
     * @title
     * <AUTHOR>
     * @param claimBusiSpecialRulePO 对象
     * @return List<ClaimBusiSpecialRulePO> 查询结果List
     */
	 public List<ClaimBusiSpecialRulePO> findAllSpecialRuleByClaimTypes(ClaimBusiSpecialRulePO claimBusiSpecialRulePO);
	 
 }
 