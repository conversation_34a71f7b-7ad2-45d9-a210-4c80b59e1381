package com.nci.tunan.clm.dao;


import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.po.SeriousDisabilityPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description ISeriousDisabilityDao重度失能接口
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统-重度失能
 * @date 2015-07-04 16:12:54  
 */
 public interface ISeriousDisabilityDao {
	 /**
     * @description 增加重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return SeriousDisabilityPO 添加结果
     */
	 public SeriousDisabilityPO addSeriousDisability(SeriousDisabilityPO seriousDisabilityPO);
	 
     /**
     * @description 删除重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteSeriousDisability(SeriousDisabilityPO seriousDisabilityPO);
	 
     /**
     * @description 修改重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return SeriousDisabilityPO 修改结果
     */
	 public SeriousDisabilityPO updateSeriousDisability(SeriousDisabilityPO seriousDisabilityPO);
	 
     /**
     * @description 查询单条重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return SeriousDisabilityPO 查询结果对象
     */
	 public SeriousDisabilityPO findSeriousDisability(SeriousDisabilityPO seriousDisabilityPO);
	 
     /**
     * @description 查询所有重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return List<SeriousDisabilityPO> 查询结果List
     */
	 public List<SeriousDisabilityPO> findAllSeriousDisability(SeriousDisabilityPO seriousDisabilityPO);
	 
     /**
     * @description 查询重度失能数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return int 查询结果条数
     */
	 public int findSeriousDisabilityTotal(SeriousDisabilityPO seriousDisabilityPO);

     /**
     * @description 分页查询重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SeriousDisabilityPO> 查询结果的当前页对象
     */
	 public CurrentPage<SeriousDisabilityPO> querySeriousDisabilityForPage(SeriousDisabilityPO seriousDisabilityPO, CurrentPage<SeriousDisabilityPO> currentPage);

     /**
     * @description 批量增加重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPOList 重度失能对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSeriousDisability(List<SeriousDisabilityPO> seriousDisabilityPOList);

     /**
     * @description 批量修改重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPOList 重度失能对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSeriousDisability(List<SeriousDisabilityPO> seriousDisabilityPOList);

     /**
     * @description 批量删除重度失能数据
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPOList 重度失能对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSeriousDisability(List<SeriousDisabilityPO> seriousDisabilityPOList);

     /**
     * @description 查询所有重度失能数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param seriousDisabilityPO 重度失能对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapSeriousDisability(SeriousDisabilityPO seriousDisabilityPO);
	 
 }
 