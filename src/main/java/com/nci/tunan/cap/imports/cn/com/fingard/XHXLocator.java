/**
 * XHXLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.nci.tunan.cap.imports.cn.com.fingard;

import com.nci.tunan.cap.util.PayrefConfigUtil;

public class XHXLocator extends org.apache.axis.client.Service implements com.nci.tunan.cap.imports.cn.com.fingard.XHX {

    /** 
    * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
    */ 
    
    private static final long serialVersionUID = 1L;

    public XHXLocator() {
    }


    public XHXLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public XHXLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for XHXSoap
    private java.lang.String xhxSoapAddress = PayrefConfigUtil
            .getValueByTypeCode(PayrefConfigUtil.TYPE__DEBIT, PayrefConfigUtil.TYPE__NONREAL_URL)+"/NCI/XHX.asmx";

    public java.lang.String getXHXSoapAddress() {
        return xhxSoapAddress;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String xhxSoapWsddServiceName = "XHXSoap";

    public java.lang.String getXHXSoapWSDDServiceName() {
        return xhxSoapWsddServiceName;
    }

    public void setXHXSoapWSDDServiceName(java.lang.String name) {
        xhxSoapWsddServiceName = name;
    }

    public com.nci.tunan.cap.imports.cn.com.fingard.XHXSoap getXHXSoap() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(xhxSoapAddress);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getXHXSoap(endpoint);
    }

    public com.nci.tunan.cap.imports.cn.com.fingard.XHXSoap getXHXSoap(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            com.nci.tunan.cap.imports.cn.com.fingard.XHXSoapStub _stub = new com.nci.tunan.cap.imports.cn.com.fingard.XHXSoapStub(portAddress, this);
            _stub.setPortName(getXHXSoapWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setXHXSoapEndpointAddress(java.lang.String address) {
        xhxSoapAddress = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (com.nci.tunan.cap.imports.cn.com.fingard.XHXSoap.class.isAssignableFrom(serviceEndpointInterface)) {
                com.nci.tunan.cap.imports.cn.com.fingard.XHXSoapStub stub = new com.nci.tunan.cap.imports.cn.com.fingard.XHXSoapStub(new java.net.URL(xhxSoapAddress), this);
                stub.setPortName(getXHXSoapWSDDServiceName());
                return stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("XHXSoap".equals(inputPortName)) {
            return getXHXSoap();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://fingard.com/", "XHX");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://fingard.com/", "XHXSoap"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address)
            throws javax.xml.rpc.ServiceException {

        if ("XHXSoap".equals(portName)) {
            setXHXSoapEndpointAddress(address);
        } else { // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
