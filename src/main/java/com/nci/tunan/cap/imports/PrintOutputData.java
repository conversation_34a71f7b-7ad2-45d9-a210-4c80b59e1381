package com.nci.tunan.cap.imports;



import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/** 
 * @ClassName: PrintOutputData 
 * @description 打印结果返回报文体
 * <AUTHOR>
 * @date 2014年04月22日 上午9:22:52 
 * @.belongToModule 收付费-打印接口
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "outputData", propOrder = {
    "sendJobResponse"
})
public class PrintOutputData {

    /** 
     * 返回报文体类
     */ 
    @XmlElement(required = true)
    protected com.nci.tunan.cap.imports.SendJobResponse sendJobResponse;

    public com.nci.tunan.cap.imports.SendJobResponse getSendJobResponse() {
        return sendJobResponse;
    }
    public void setSendJobResponse(com.nci.tunan.cap.imports.SendJobResponse value) {
        this.sendJobResponse = value;
    }

}
