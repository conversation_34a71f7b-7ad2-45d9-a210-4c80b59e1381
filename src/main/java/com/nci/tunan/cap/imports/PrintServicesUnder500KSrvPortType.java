package com.nci.tunan.cap.imports;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

import com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd.SrvReqBody;


/** 
 * @ClassName: PrintServicesUnder500KSrvPortType 
 * @description 调用打印接口类
 * <AUTHOR>
 * @date 2016-03-11 15:48:31
 * @.belongToModule 收付费-调用打印接口类
 */ 
@WebService(targetNamespace = "http://www.newchinalife.com", name = "PrintServicesUnder500KSrvPortType")
@XmlSeeAlso({PrintObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface PrintServicesUnder500KSrvPortType {

    /** 
     * @description 调用打印接口方法
     * @param parametersReqHeader 入参头
     * @param parametersReqBody 入参体
     * @param parametersResHeader 出参头
     * @param parametersResBody  出参体 
     * @throws 
     */
    @WebMethod(operationName = "PrintServicesUnder500K", action = "PrintServicesUnder500K")
    public void printServicesUnder500K(
        @WebParam(partName = "parametersReqHeader", name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true)
        SysMsgHeader parametersReqHeader,
        @WebParam(partName = "parametersReqBody", name = "request", targetNamespace = "http://www.newchinalife.com/service/bd")
        SrvReqBody parametersReqBody,
        @WebParam(partName = "parametersResHeader", mode = WebParam.Mode.OUT, name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true)
        javax.xml.ws.Holder<SysMsgHeader> parametersResHeader,
        @WebParam(partName = "parametersResBody", mode = WebParam.Mode.OUT, name = "response", targetNamespace = "http://www.newchinalife.com/service/bd")
        javax.xml.ws.Holder<SrvResBody> parametersResBody
    );
}
