package com.nci.tunan.cap.imports.impl.querypaystatus.bd;
import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.tunan.cap.imports.impl.realtimeindemnity.hd.IndemnityInputBizHeaderVO;

/** 
 * @ClassName: SrvReqBody 
 * @description: 实时代付与资金交易报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:30:31 
 * @.belongToModule 收付费-实时代付  
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBody", propOrder = { "bizHeader", "bizBody" })
public class SrvReqBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(required = true)
    private IndemnityInputBizHeaderVO  bizHeader;
    @XmlElement(required = true)
    private SrvReqBizBody bizBody;
    
    public  IndemnityInputBizHeaderVO  getBizHeader() {
        return bizHeader;
    }
    public void setBizHeader(IndemnityInputBizHeaderVO  bizHeader) {
        this.bizHeader = bizHeader;
    }
    public SrvReqBizBody getBizBody() {
        return bizBody;
    }
    public void setBizBody(SrvReqBizBody bizBody) {
        this.bizBody = bizBody;
    }
}
