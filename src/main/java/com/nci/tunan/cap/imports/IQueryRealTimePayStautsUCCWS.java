package com.nci.tunan.cap.imports;

import com.nci.tunan.cap.interfaces.model.vo.querypaystauts.QueryRealTimePayStatusInputVO;
import com.nci.tunan.cap.interfaces.model.vo.querypaystauts.QueryRealTimePayStatusOutputVO;

/** 
 * @ClassName: QueryRealTimePayStautsUCCWSImpl 
 * @description 实时付费查询接口
 * <AUTHOR>
 * @date 2021年12月17日 下午4:55:48 
 * @.belongToModule 收付费-实时收付
 */ 
public interface IQueryRealTimePayStautsUCCWS {

    /** 
     * @description 实时付费查询接口方法
     * @param inputData 入参
     * @return  QueryRealTimePayStatusOutputVO 返回参数
     * @throws 
     */
    public QueryRealTimePayStatusOutputVO queryPayStatus(QueryRealTimePayStatusInputVO inputData);
}
