package com.nci.tunan.cap.imports.cn.com.fingard;


/** 
 * @ClassName: IndemnityBizHeaderVO 
 * @description 实时代付与资金交易报文头
 * <AUTHOR>
 * @date 2017年12月11日 上午11:27:52 
 * @.belongToModule 收付费-实时代付 
 */ 
public class NonRealTimePaymentInputHeader {

    /**
     * @Fields transSource : 交易来源 (ERP来源标识（系统代码简称，如XinHeXin01）)
     */
    private String TransSource = "XinHeXin01";

    /**
     * @Fields transCode : 交易编码(固定值，1988代付，9488收款到账核对)
     */
    private String TransCode;

    /**
     * @Fields transDate : 交易日期  格式是yyyyMMdd
     */
    private String TransDate;

    /**
     * @Fields transTime : 交易时间   格式是HH24miss
     */
    private String TransTime;

    /**
     * @Fields transSeq : 交易流水号   时间戳，格式是yyyymmddhh24missff4
     */
    private String TransSeq;

    public String getTransSource() {
        return TransSource;
    }

    public void setTransSource(String transSource) {
        TransSource = transSource;
    }

    public String getTransCode() {
        return TransCode;
    }

    public void setTransCode(String transCode) {
        TransCode = transCode;
    }

    public String getTransDate() {
        return TransDate;
    }

    public void setTransDate(String transDate) {
        TransDate = transDate;
    }

    public String getTransTime() {
        return TransTime;
    }

    public void setTransTime(String transTime) {
        TransTime = transTime;
    }

    public String getTransSeq() {
        return TransSeq;
    }

    public void setTransSeq(String transSeq) {
        TransSeq = transSeq;
    }
    
}
