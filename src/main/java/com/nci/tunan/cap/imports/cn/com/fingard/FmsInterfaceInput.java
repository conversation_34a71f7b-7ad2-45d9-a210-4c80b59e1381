package com.nci.tunan.cap.imports.cn.com.fingard;

import java.util.List;

/**
 * @description E保通提交，请求报文 update
 * @<NAME_EMAIL>
 * @date 2016-2-29 上午10:51:57
 * @.belongToModule 收付费-E保通
 */
public class FmsInterfaceInput {
    /**
     * 交易流水号
     */
    private String NoteCode;

    public String getNoteCode() {
        return NoteCode;
    }

    public void setNoteCode(String noteCode) {
        NoteCode = noteCode;
    }

    /**
     * 业务类型
     */
    private String BusType;
    /**
     * 支付商编码
     */
    private String ServicerCode = "";
    /**
     * 投保单号
     */
    private String ProposalFormNo;
    /**
     * 银行编码
     */
    private String BankCode;
    /**
     * 账号
     */
    private String AccountNo;
    /**
     * 账号名
     */
    private String AccountName;
    /**
     * id类型
     */
    private String IDType;
    /**
     * id号码
     */
    private String IDNo;
    /**
     * 费用
     */
    private String Premium;
    /**
     * Currency
     */
    private String Currency;
    /**
     * 手机号
     */
    private String MobilePhone;
    /**
     * 客户id
     */
    private String CustUserID = "";
    /**
     * 验证标志
     */
    private String VerifyFlag;
    /**
     * 备注
     */
    private String Remark;
    /**
     * 校验来源
     */
    private String TransSource = "HEXIN";
    /**
     * 管理机构
     */
    private String ManageCom;
    /**
     * 扩展字段1
     */
    private String InReserved1 = "";
    /**
     * 扩展字段2
     */
    private String InReserved2 = "";
    /**
     * 扩展字段3
     */
    private String InReserved3 = "";
    /**
     * 平台来源
     */
    private String Platform = "EBT";
    /**
     * 商户id
     */
    private String MerchantID = "";
    /**
     * 批次id
     */
    private String BatchID;
    /**
     * 交易号
     */
    private String TradeSN;
    /**
     * 状态
     */
    private String Status;
    /**
     * 查询类型
     */
    private String QueryType;
    /**
     * 开始日期
     */
    private String StartDate;
    /**
     * 结束日期
     */
    private String EndDate;
    /**
     * 所有者
     */
    private String BussOwner;
    /**
     * 证件类型
     */
    private String CertifyType;
    /**
     * 证件号
     */
    private String CertifyNo;
    /**
     * 开始时间
     */
    private String EnterAccDate;
    /**
     * 结束时间
     */
    private String EnterAccTime;
    /**
     * 账户类型
     */
    private String AccountType;
    /**
     * 银行编码
     */
    private String CorpBankCode;
    /**
     * 银行名称
     */
    private String CorpBankName;
    /**
     * 银行账号
     */
    private String CorpAccount;
    /**
     * 用户编码
     */
    private String UserCode;

    /**
     * 对账码
     */
    private String Abstract;

    public String getAbstract() {
        return Abstract;
    }

    public void setAbstract(String Abstract) {
        this.Abstract = Abstract;
    }

    /**
     * 银保信账户标识
     */
    private String IsInvestAcc;

    /**
     * 实收/实付流水号
     */
    private String PaymentCode;

    /**
     * 业务明细集合
     */
    private List<BusinessDetail> BusinessDetails;

    public String getPaymentCode() {
        return PaymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        PaymentCode = paymentCode;
    }

    public List<BusinessDetail> getBusinessDetails() {
        return BusinessDetails;
    }

    public void setBusinessDetails(List<BusinessDetail> businessDetails) {
        BusinessDetails = businessDetails;
    }

    public String getIsInvestAcc() {
        return IsInvestAcc;
    }

    public void setIsInvestAcc(String isInvestAcc) {
        IsInvestAcc = isInvestAcc;
    }

    public String getBusType() {
        return BusType;
    }

    public void setBusType(String busType) {
        BusType = busType;
    }

    public String getServicerCode() {
        return ServicerCode;
    }

    public void setServicerCode(String servicerCode) {
        ServicerCode = servicerCode;
    }

    public String getProposalFormNo() {
        return ProposalFormNo;
    }

    public void setProposalFormNo(String proposalFormNo) {
        ProposalFormNo = proposalFormNo;
    }

    public String getBankCode() {
        return BankCode;
    }

    public void setBankCode(String bankCode) {
        BankCode = bankCode;
    }

    public String getAccountNo() {
        return AccountNo;
    }

    public void setAccountNo(String accountNo) {
        AccountNo = accountNo;
    }

    public String getAccountName() {
        return AccountName;
    }

    public void setAccountName(String accountName) {
        AccountName = accountName;
    }

    public String getIDType() {
        return IDType;
    }

    public void setIDType(String iDType) {
        IDType = iDType;
    }

    public String getIDNo() {
        return IDNo;
    }

    public void setIDNo(String iDNo) {
        IDNo = iDNo;
    }

    public String getPremium() {
        return Premium;
    }

    public void setPremium(String premium) {
        Premium = premium;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String currency) {
        Currency = currency;
    }

    public String getMobilePhone() {
        return MobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        MobilePhone = mobilePhone;
    }

    public String getCustUserId() {
        return CustUserID;
    }

    public void setCustUserId(String custUserId) {
        CustUserID = custUserId;
    }

    public String getVerifyFlag() {
        return VerifyFlag;
    }

    public void setVerifyFlag(String verifyFlag) {
        VerifyFlag = verifyFlag;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        this.Remark = remark;
    }

    public String getTransSource() {
        return TransSource;
    }

    public void setTransSource(String transSource) {
        TransSource = transSource;
    }

    public String getManageCom() {
        return ManageCom;
    }

    public void setManageCom(String manageCom) {
        ManageCom = manageCom;
    }

    public String getInReserved1() {
        return InReserved1;
    }

    public void setInReserved1(String inReserved1) {
        InReserved1 = inReserved1;
    }

    public String getInReserved2() {
        return InReserved2;
    }

    public void setInReserved2(String inReserved2) {
        InReserved2 = inReserved2;
    }

    public String getInReserved3() {
        return InReserved3;
    }

    public void setInReserved3(String inReserved3) {
        InReserved3 = inReserved3;
    }

    public String getPlatform() {
        return Platform;
    }

    public void setPlatform(String platform) {
        Platform = platform;
    }

    public String getMerchantID() {
        return MerchantID;
    }

    public void setMerchantID(String merchantID) {
        MerchantID = merchantID;
    }

    public String getBatchID() {
        return BatchID;
    }

    public void setBatchID(String batchID) {
        BatchID = batchID;
    }

    public String getTradeSN() {
        return TradeSN;
    }

    public void setTradeSN(String tradeSN) {
        TradeSN = tradeSN;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getQueryType() {
        return QueryType;
    }

    public void setQueryType(String queryType) {
        QueryType = queryType;
    }

    public String getStartDate() {
        return StartDate;
    }

    public void setStartDate(String startDate) {
        StartDate = startDate;
    }

    public String getEndDate() {
        return EndDate;
    }

    public void setEndDate(String endDate) {
        EndDate = endDate;
    }

    public String getBussOwner() {
        return BussOwner;
    }

    public void setBussOwner(String bussOwner) {
        BussOwner = bussOwner;
    }

    public String getCustUserID() {
        return CustUserID;
    }

    public void setCustUserID(String custUserID) {
        CustUserID = custUserID;
    }

    public String getCertifyType() {
        return CertifyType;
    }

    public void setCertifyType(String certifyType) {
        CertifyType = certifyType;
    }

    public String getCertifyNo() {
        return CertifyNo;
    }

    public void setCertifyNo(String certifyNo) {
        CertifyNo = certifyNo;
    }

    public String getEnterAccDate() {
        return EnterAccDate;
    }

    public void setEnterAccDate(String enterAccDate) {
        EnterAccDate = enterAccDate;
    }

    public String getEnterAccTime() {
        return EnterAccTime;
    }

    public void setEnterAccTime(String enterAccTime) {
        EnterAccTime = enterAccTime;
    }

    public String getAccountType() {
        return AccountType;
    }

    public void setAccountType(String accountType) {
        AccountType = accountType;
    }

    public String getCorpBankCode() {
        return CorpBankCode;
    }

    public void setCorpBankCode(String corpBankCode) {
        CorpBankCode = corpBankCode;
    }

    public String getCorpBankName() {
        return CorpBankName;
    }

    public void setCorpBankName(String corpBankName) {
        CorpBankName = corpBankName;
    }

    public String getCorpAccount() {
        return CorpAccount;
    }

    public void setCorpAccount(String corpAccount) {
        CorpAccount = corpAccount;
    }

    public String getUserCode() {
        return UserCode;
    }

    public void setUserCode(String userCode) {
        UserCode = userCode;
    }

    @Override
    public String toString() {
        return "FmsInterfaceInput [ServicerCode=" + ServicerCode + ", ProposalFormNo=" + ProposalFormNo + ", BankCode="
                + BankCode + ", AccountNo=" + AccountNo + ", AccountName=" + AccountName + ", IDType=" + IDType
                + ", IDNo=" + IDNo + ", Premium=" + Premium + ", Currency=" + Currency + ", MobilePhone=" + MobilePhone
                + ", CustUserID=" + CustUserID + ", VerifyFlag=" + VerifyFlag + ", Remark=" + Remark + ", TransSource="
                + TransSource + ", ManageCom=" + ManageCom + ", InReserved1=" + InReserved1 + ", InReserved2="
                + InReserved2 + ", InReserved3=" + InReserved3 + ", Platform=" + Platform + ", MerchantID="
                + MerchantID + ", BatchID=" + BatchID + ", TradeSN=" + TradeSN + ", Status=" + Status + ", QueryType="
                + QueryType + ", StartDate=" + StartDate + ", EndDate=" + EndDate + ", BussOwner=" + BussOwner
                + ", CertifyType=" + CertifyType + ", CertifyNo=" + CertifyNo + ", EnterAccDate=" + EnterAccDate
                + ", EnterAccTime=" + EnterAccTime + ", AccountType=" + AccountType + ", CorpBankCode=" + CorpBankCode
                + ", CorpBankName=" + CorpBankName + ", CorpAccount=" + CorpAccount + ", UserCode=" + UserCode + "]";
    }

}
