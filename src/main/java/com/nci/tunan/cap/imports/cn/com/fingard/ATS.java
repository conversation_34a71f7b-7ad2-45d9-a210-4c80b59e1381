package com.nci.tunan.cap.imports.cn.com.fingard;

import com.thoughtworks.xstream.annotations.XStreamAlias;
@XStreamAlias("ATS")
public class ATS {

    private NonRealTimePaymentInputHeader PUB;
    private NonRealTimePaymentInput IN;

    public NonRealTimePaymentInputHeader getPUB() {
        return PUB;
    }

    public void setPUB(NonRealTimePaymentInputHeader pub) {
        PUB = pub;
    }



    public NonRealTimePaymentInput getIN() {
        return IN;
    }



    public void setIN(NonRealTimePaymentInput iN) {
        IN = iN;
    }

}
