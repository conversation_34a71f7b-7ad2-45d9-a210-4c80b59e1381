package com.nci.tunan.cap.imports;

import java.io.Serializable;

import com.nci.udmp.component.serviceinvoke.client.ServiceInvokeClient;
import com.nci.udmp.component.serviceinvoke.client.ServiceInvokeClientFactory;
import com.nci.udmp.component.serviceinvoke.message.ServiceParameter;
import com.nci.udmp.component.serviceinvoke.message.ServiceResult;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.context.AppUserContext;

/**
 * @description 客户端创建factory
 * <AUTHOR> <EMAIL>
 * @date 2014-12-10 下午2:09:17
 * @.belongToModule 收付费-打印接口
 */
public class ClientFactory {

    /**
     * @description 服务客户端
     * @version 0.1
     * @title
     * <AUTHOR> <EMAIL>
     * @date 2014-12-10 下午2:09:28
     * @param serviceRegConst 服务常量编号
     * @param parameterData 服务调用参数
     * @param factoryType 工厂类型
     * @return
     * @throws Exception
     */
    public <T extends Serializable> T progress(String serviceRegConst, ServiceParameter parameterData,
            String factoryType) throws Exception {

        //@invalid 模拟登陆用户
        AppUser appUser = new AppUser();
        appUser.setUserId(1);
        AppUserContext.setCurrentUser(appUser);

        ServiceInvokeClientFactory factory = ServiceInvokeClientFactory.newInstance();
        factory.setType(factoryType);
        ServiceInvokeClient client = factory.createClient();
        ServiceResult result = client.invokeService(serviceRegConst, //@invalid 服务常量编号
                parameterData); //@invalid 服务调用参数
        T response = (T) result.getResponse();

        return response;
    }
}
