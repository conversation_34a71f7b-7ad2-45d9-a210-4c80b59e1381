package com.nci.tunan.cap.imports.com.fingard;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

import com.nci.tunan.cap.util.PayrefConfigUtil;

/**
 * This class was generated by Apache CXF 3.0.0 2022-11-15T15:26:02.323+08:00
 * Generated source version: 3.0.0
 * 
 */
@WebServiceClient(name = "XHX", wsdlLocation = "http://10.1.90.183/NCI/XHX.asmx?wsdl", targetNamespace = "http://fingard.com/")
public class XHX extends Service {

    public static final URL WSDL_LOCATION;
    public static final QName SERVICE = new QName("http://fingard.com/", "XHX");
    public static final QName XHXSoap12 = new QName("http://fingard.com/", "XHXSoap12");
    public static final QName XHXSoap = new QName("http://fingard.com/", "XHXSoap");
    static {
        URL url = null;
        String debitUrl = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__DEBIT,
                PayrefConfigUtil.TYPE__NONREAL_URL);
        try {
            url = new URL(debitUrl + "/NCI/XHX.asmx?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(XHX.class.getName()).log(java.util.logging.Level.INFO,
                    "Can not initialize the default wsdl from {0}", "http://10.1.90.183/NCI/XHX.asmx?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public XHX(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public XHX(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public XHX() {
        super(WSDL_LOCATION, SERVICE);
    }

    // This constructor requires JAX-WS API 2.2. You will need to endorse the
    // 2.2
    // API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS
    // 2.1
    // compliant code instead.
    public XHX(WebServiceFeature... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    // This constructor requires JAX-WS API 2.2. You will need to endorse the
    // 2.2
    // API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS
    // 2.1
    // compliant code instead.
    public XHX(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, SERVICE, features);
    }

    // This constructor requires JAX-WS API 2.2. You will need to endorse the
    // 2.2
    // API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS
    // 2.1
    // compliant code instead.
    public XHX(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return returns XHXSoap
     */
    @WebEndpoint(name = "XHXSoap12")
    public XHXSoap getXHXSoap12() {
        return super.getPort(XHXSoap12, XHXSoap.class);
    }

    /**
     * 
     * @param features A list of {@link javax.xml.ws.WebServiceFeature} to
     *            configure on the proxy. Supported features not in the
     *            <code>features</code> parameter will have their default
     *            values.
     * @return returns XHXSoap
     */
    @WebEndpoint(name = "XHXSoap12")
    public XHXSoap getXHXSoap12(WebServiceFeature... features) {
        return super.getPort(XHXSoap12, XHXSoap.class, features);
    }

    /**
     * 
     * @return returns XHXSoap
     */
    @WebEndpoint(name = "XHXSoap")
    public XHXSoap getXHXSoap() {
        return super.getPort(XHXSoap, XHXSoap.class);
    }

    /**
     * 
     * @param features A list of {@link javax.xml.ws.WebServiceFeature} to
     *            configure on the proxy. Supported features not in the
     *            <code>features</code> parameter will have their default
     *            values.
     * @return returns XHXSoap
     */
    @WebEndpoint(name = "XHXSoap")
    public XHXSoap getXHXSoap(WebServiceFeature... features) {
        return super.getPort(XHXSoap, XHXSoap.class, features);
    }

}
