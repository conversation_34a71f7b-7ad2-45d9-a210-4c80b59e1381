package com.nci.tunan.cap.imports.cn.com.fingard;

public class XHXSoapProxy implements com.nci.tunan.cap.imports.cn.com.fingard.XHXSoap {
    private String endPoint = null;
    private com.nci.tunan.cap.imports.cn.com.fingard.XHXSoap xhxSoap = null;

    public XHXSoapProxy() {
        initXHXSoapProxy();
    }

    public XHXSoapProxy(String endpoint) {
        endPoint = endpoint;
        initXHXSoapProxy();
    }

    private void initXHXSoapProxy() {
        try {
            xhxSoap = (new com.nci.tunan.cap.imports.cn.com.fingard.XHXLocator()).getXHXSoap();
            if (xhxSoap != null) {
                if (endPoint != null) {
                    ((javax.xml.rpc.Stub) xhxSoap)._setProperty("javax.xml.rpc.service.endpoint.address", endPoint);
                } else {
                    endPoint = (String) ((javax.xml.rpc.Stub) xhxSoap)
                            ._getProperty("javax.xml.rpc.service.endpoint.address");
                }
            }

        } catch (javax.xml.rpc.ServiceException serviceException) {
            serviceException.printStackTrace();
        }
    }

    public String getEndpoint() {
        return endPoint;
    }

    public void setEndpoint(String endpoint) {
        endPoint = endpoint;
        if (xhxSoap != null) {
            ((javax.xml.rpc.Stub) xhxSoap)._setProperty("javax.xml.rpc.service.endpoint.address", endPoint);
        }
    }

    public com.nci.tunan.cap.imports.cn.com.fingard.XHXSoap getXHXSoap() {
        if (xhxSoap == null){
            initXHXSoapProxy();
        }
        return xhxSoap;
    }

    public java.lang.String[] paymentSubmit(java.lang.String pReqXml, java.lang.String pDigest)
            throws java.rmi.RemoteException {
        if (xhxSoap == null){
            initXHXSoapProxy();
        }
        return xhxSoap.paymentSubmit(pReqXml, pDigest);
    }
}