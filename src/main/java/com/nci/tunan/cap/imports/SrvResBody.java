
package com.nci.tunan.cap.imports;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/** 
 * @description 交易响应报文体
 * @<NAME_EMAIL> 
 * @date 2015-12-17 上午10:42:28 
 * @.belongToModule 收付费-打印接口
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBody", propOrder = {
    "bizHeader",
    "bizBody"
})
public class SrvResBody {

    @XmlElement(required = true)
    protected SRVResHead bizHeader;
    @XmlElement(required = true)
    protected SrvResBizBody bizBody;

    public SRVResHead getBizHeader() {
        return bizHeader;
    }

    public void setBizHeader(SRVResHead value) {
        this.bizHeader = value;
    }

    public SrvResBizBody getBizBody() {
        return bizBody;
    }

    public void setBizBody(SrvResBizBody value) {
        this.bizBody = value;
    }

}
