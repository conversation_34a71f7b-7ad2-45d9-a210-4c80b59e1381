
package com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/** 
 * @description 交易请求报文体
 * @<NAME_EMAIL> 
 * @date 2015-12-17 上午10:42:28 
 * @.belongToModule 收付费-打印接口
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody", propOrder = {
    "inputData"
})
public class SrvReqBizBody {

    @XmlElement(required = true)
    protected PrintInputData inputData;

    public PrintInputData getInputData() {
        return inputData;
    }

    public void setInputData(PrintInputData value) {
        this.inputData = value;
    }

}
