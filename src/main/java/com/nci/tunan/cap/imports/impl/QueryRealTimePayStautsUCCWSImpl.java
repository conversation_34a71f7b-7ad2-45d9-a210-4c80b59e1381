package com.nci.tunan.cap.imports.impl;

import java.util.Map;

import javax.xml.ws.Holder;

import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.slf4j.Logger;

import com.nci.tunan.cap.imports.IQueryRealTimePayStautsUCCWS;
import com.nci.tunan.cap.imports.impl.querypaystatus.bd.SrvReqBizBody;
import com.nci.tunan.cap.imports.impl.querypaystatus.bd.SrvReqBody;
import com.nci.tunan.cap.imports.impl.querypaystatus.bd.SrvResBody;
import com.nci.tunan.cap.imports.impl.querypaystatus.ws.QueryRealTimePayStautsUCCWS;
import com.nci.tunan.cap.interfaces.model.vo.querypaystauts.QueryRealTimePayStatusInputVO;
import com.nci.tunan.cap.interfaces.model.vo.querypaystauts.QueryRealTimePayStatusOutputVO;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.component.serviceinvoke.ServiceCommonMethod;
import com.nci.udmp.component.serviceinvoke.ServiceInvokeParameterBean;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @ClassName: QueryRealTimePayStautsUCCWSImpl 
 * @description 实时付费查询接口
 * <AUTHOR>
 * @date 2021年12月17日 下午4:55:48 
 * @.belongToModule 收付费-实时收付
 */ 
public class QueryRealTimePayStautsUCCWSImpl implements IQueryRealTimePayStautsUCCWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    
    /**
     * @Description: 实时付款查询接口
     * @param inputData 入參
     * @return 设定文件
     * @throws
     */
    public QueryRealTimePayStatusOutputVO queryPayStatus(QueryRealTimePayStatusInputVO inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        /*
         * 1.1 调用的webService的wsdl
         */
        String wsdl = Constants.SERVICEENVPARAMAP.get("ESB") + "P00001900410?wsdl";
        /*
         * 1.2 服务接口路径
         */
        String interfacePath = "com.nci.tunan.cap.imports.impl.querypaystatus.ws.QueryRealTimePayStautsUCCWS";
        /*
         * 1.3 报文参数
         */
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<SrvResBody> parametersResBody = new Holder<SrvResBody>();
        /*
         * 1.4 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd("P00001900410");
        sysheader.setSysCd("075");
        parametersResHeader.value = sysheader;
        /*
         * 1.5 定义系统报文体
         */
        SrvReqBody srvReqBody = new SrvReqBody();
        /*
         * 1.6 定义业务报文体
         */
        SrvReqBizBody bizBody = new SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "fms" + "_" + "QueryRealTimePayStatusOutputVO" + "_" + "queryPayStatus";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(inputData.getBizHeader());
        srvReqBody.getBizBody().setInputData(inputData.getInputDate());
        /*
         * 1.7 添加调用前的日志输出
         */
        try {
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
                    + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.8 返回参数
         */
        QueryRealTimePayStatusOutputVO result = new QueryRealTimePayStatusOutputVO();
        String dealStatus = "2";
        try {
            /*
             * 1.8.1 调用公共webservice工厂方法
             */
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod
                    .CommonWebServiceFactory(wsdl, interfacePath);
            /*
             * 1.8.2 通过工厂创建接口实例
             */
            QueryRealTimePayStautsUCCWS ipubcapunimodeluccws = (QueryRealTimePayStautsUCCWS) soapFactory
                    .create();
            /*
             * 1.8.3 调用接口的指定方法，将参数传入
             */
            ipubcapunimodeluccws.queryPayStatus(sysheader, srvReqBody,parametersResHeader, parametersResBody);
            /*
             * 1.8.4 将业务报文体的outputData赋值给返回值
             */
            result.setOutputData(parametersResBody.value.getBizBody().getOutputData());
            result.setBizHeader(parametersResBody.value.getBizHeader());
            /*
             * 1.8.5 添加调用后的日志输出
             */
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody));
            logger.info("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.info("webservice调用业务接口异常");
            dealStatus = "3";
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.info("找不到接口类");
            dealStatus = "3";
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.info("解析返回的报文的JSON字符串发生异常");
            dealStatus = "3";
        } finally {
            if (dealSwitch && parametersResBody.value == null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader, null,
                        null, DealTrigger.WEBSERVICE, dealNo, dealTime,
                        dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.info("开始记录交易响应日志");
            }

        }
        return result;
    }
}
