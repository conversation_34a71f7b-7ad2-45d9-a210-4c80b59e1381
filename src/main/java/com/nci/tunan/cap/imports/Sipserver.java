package com.nci.tunan.cap.imports;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * 
 * @description definitions of sipserver
 * @<NAME_EMAIL> 
 * @date 2015-9-18 下午6:33:31
 * @.belongToModule 收付费-调用打印接口
 */
@WebServiceClient(name = "sipserver", wsdlLocation = "http://**********:5999/?wsdl", targetNamespace = "urn:sipserverType")
public class Sipserver extends Service {

    /**
     * 接口地址
     */
    public static final URL WSDL_LOCATION;
    /**
     * 接口域名
     */
    public static final QName SERVICE = new QName("urn:sipserverType", "sipserver");
    /**
     * 接口域名
     */
    public static final QName SIPSERVER = new QName("urn:sipserverType", "sipserver");
    static {
        URL url = null;
        try {
            url = new URL("http://**********:5999/?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(Sipserver.class.getName()).log(java.util.logging.Level.INFO,
                    "Can not initialize the default wsdl from {0}", 
                    "http://**********9:5999/?wsdl");
        }
        WSDL_LOCATION = url;
    }

    /**
     * 接口请求方法
     * <p>Title: </p> 
     * <p>Description: </p> 
     * @param wsdlLocation wsdlLocation
     */
    public Sipserver(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    /**
     * 接口请求方法
     * <p>Title: </p> 
     * <p>Description: </p> 
     * @param wsdlLocation wsdlLocation
     * @param serviceName serviceName
     */
    public Sipserver(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 接口请求方法
     * <p>Title: </p> 
     * <p>Description: </p>
     */
    public Sipserver() {
        super(WSDL_LOCATION, SERVICE);
    }

    /**
     * 
     * @return returns SipserverPortType
     */
    @WebEndpoint(name = "sipserver")
    public ISipserverPortType getSipserver() {
        return super.getPort(SIPSERVER, ISipserverPortType.class);
    }

    /**
     * 
     * @param features
     *            A list of {@link javax.xml.ws.WebServiceFeature} to configure
     *            on the proxy. Supported features not in the
     *            <code>features</code> parameter will have their default
     *            values.
     * @return returns SipserverPortType
     */
    @WebEndpoint(name = "sipserver")
    public ISipserverPortType getSipserver(WebServiceFeature... features) {
        return super.getPort(SIPSERVER, ISipserverPortType.class, features);
    }

}
