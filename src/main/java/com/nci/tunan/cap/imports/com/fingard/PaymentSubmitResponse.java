
package com.nci.tunan.cap.imports.com.fingard;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PaymentSubmitResult" type="{http://fingard.com/}ArrayOfString" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "paymentSubmitResult"
})
@XmlRootElement(name = "PaymentSubmitResponse")
public class PaymentSubmitResponse {

    @XmlElement(name = "PaymentSubmitResult")
    protected ArrayOfString paymentSubmitResult;

    /**
     * ��ȡpaymentSubmitResult���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfString }
     *     
     */
    public ArrayOfString getPaymentSubmitResult() {
        return paymentSubmitResult;
    }

    /**
     * ����paymentSubmitResult���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfString }
     *     
     */
    public void setPaymentSubmitResult(ArrayOfString value) {
        this.paymentSubmitResult = value;
    }

}
