package com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd;




import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/** 
 * @ClassName: PrintInputData 
 * @description 打印接口报文体参数类
 * <AUTHOR>
 * @date 2014年04月22日 上午9:16:31 
 * @.belongToModule 收付费-打印接口
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "inputData", propOrder = {
    "sendJob"
})
public class PrintInputData {

    /** 
     * 报文体
     */ 
    @XmlElement(required = true)
    protected com.nci.tunan.cap.imports.SendJob sendJob;

    public com.nci.tunan.cap.imports.SendJob getSendJob() {
        return sendJob;
    }

    public void setSendJob(com.nci.tunan.cap.imports.SendJob value) {
        this.sendJob = value;
    }

}
