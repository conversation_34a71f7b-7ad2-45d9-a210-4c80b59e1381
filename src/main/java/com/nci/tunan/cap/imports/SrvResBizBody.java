
package com.nci.tunan.cap.imports;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/** 
 * @description 交易报文体
 * @<NAME_EMAIL> 
 * @date 2015-12-17 上午10:42:28 
 * @.belongToModule 收付费-打印接口
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody", propOrder = {
    "outputData"
})
public class SrvResBizBody {

    @XmlElement(required = true)
    protected PrintOutputData outputData;

    public PrintOutputData getOutputData() {
        return outputData;
    }

    public void setOutputData(PrintOutputData value) {
        this.outputData = value;
    }

}
