package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder={"ProductType", "ProductCode", "ApplicationNo", "PolicyNo", "ProApplicationAmount" })
public class APBusinessDetail {
	
	/**
	 * 产品分类代码 100 普通型产品 210 分红型
	 *  220 万能型 230 投资连结型  290 其他新型产品
	 */
	private String ProductType;
	/**
	 * 产品代码
	 */
	private String ProductCode;
	/**
	 * 业务申请单号（业务号）
	 */
	private String ApplicationNo;
	/**
	 * 保单号
	 */
	private String PolicyNo;
	/**
	 * 金额
	 */
	private String ProApplicationAmount;
	public String getProductType() {
		return ProductType;
	}
	public void setProductType(String productType) {
		ProductType = productType;
	}
	public String getProductCode() {
		return ProductCode;
	}
	public void setProductCode(String productCode) {
		ProductCode = productCode;
	}
	public String getApplicationNo() {
		return ApplicationNo;
	}
	public void setApplicationNo(String applicationNo) {
		ApplicationNo = applicationNo;
	}
	public String getPolicyNo() {
		return PolicyNo;
	}
	public void setPolicyNo(String policyNo) {
		PolicyNo = policyNo;
	}
	public String getProApplicationAmount() {
		return ProApplicationAmount;
	}
	public void setProApplicationAmount(String proApplicationAmount) {
		ProApplicationAmount = proApplicationAmount;
	}
	
}
