
package com.nci.tunan.cap.imports;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;



/** 
 * @ClassName: SendJobResponse 
 * @description 调用打印系统接口
 * <AUTHOR> <EMAIL>
 * @date 2014年04月17日 下午5:29:16 
 * @.belongToModule 收付费-调用打印接口
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sendJobResponse", propOrder = {
    "jobId",
    "error"
})
public class SendJobResponse {

    /** 
     * 任务ID
     */ 
    @XmlElement(required = true)
    protected String jobId;
    /** 
     * 错误消息
     */ 
    @XmlElement(required = true)
    protected String error;

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String value) {
        this.jobId = value;
    }

    public String getError() {
        return error;
    }

    public void setError(String value) {
        this.error = value;
    }

}
