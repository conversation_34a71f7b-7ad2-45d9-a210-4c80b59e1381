/*
package com.nci.tunan.cap.imports.cn.com.fingard;

*//**
 * Please modify this class to meet your needs
 * This class is not complete
 *//*

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;


*//** 
 * @ClassName: BusNciXinDebitSoap_BusNciXinDebitSoap_Client 
 * @description E保通实时收费接口测试方法类
 * <AUTHOR>
 * @date 2016年04月27日 下午14:22:19 
 * @.belongToModule 收付费-E保通实时收费接口测试方法类
 *  
 *//* 
public final class BusNciXinDebitSoap_BusNciXinDebitSoap_Client {

    *//** 
     * 接口地址 
     *//* 
    private static final QName SERVICE_NAME = new QName("http://FinGard.com.cn/", "BusNciXinDebit");

    *//** 
     * @description 构造方法
     * @return 返回类型 
     * @throws 
     *//*
    private BusNciXinDebitSoap_BusNciXinDebitSoap_Client() {
    }

    *//** 
     * @description 测试用main方法
     * @param args 字符串集合
     * @throws java.lang.Exception  跑出异常
     * @throws 
     *//*
    public static void main(String args[]) throws java.lang.Exception {
        URL wsdlURL = BusNciXinDebit.getWsdlLocation();
        if (args.length > 0 && args[0] != null && !"".equals(args[0])) { 
            File wsdlFile = new File(args[0]);
            try {
                if (wsdlFile.exists()) {
                    wsdlURL = wsdlFile.toURI().toURL();
                } else {
                    wsdlURL = new URL(args[0]);
                }
            } catch (MalformedURLException e) {
                e.printStackTrace();
            }
        }
      
        BusNciXinDebit ss = new BusNciXinDebit(wsdlURL, SERVICE_NAME);
        BusNciXinDebitSoap port = ss.getBusNciXinDebitSoap();  
        
        {
        java.lang.String _submit_pStrReqData = "";
        java.lang.String _submit__return = port.submit(_submit_pStrReqData);


        }

        System.exit(0);
    }

}
*/