package com.nci.tunan.cap.imports.cn.com.fingard;

import java.util.List;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * @description E保通提交报文 update
 * @<NAME_EMAIL>
 * @date 2016-2-27 上午10:55:55
 * @.belongToModule 收付费-E保通提交报文
 */
public class BaseInfo {
    /**
     * 交易类型
     */
    private String TransType = "";
    /**
     * 交易编码
     */
    private String TransCode = "";
    /**
     * 报文板本号
     */
    private String HeaderVervsion = "";
    /**
     * 子交易序号
     */
    private String SubTransCode = "";
    /**
     * 交易日期
     */
    private String TransDate = "";
    /**
     * 交易时间
     */
    private String TransTime = "";
    /**
     * 交易实例号
     */
    private String TransSeq = "";
    /**
     * 操作员代码
     */
    private String Operator = "001";
    /**
     * 总行数
     */
    private String TotalRowNum = "";
    /**
     * 游标位置
     */
    private String RowNumStart = "1";
    /**
     * 交易结果码
     */
    private String ResultCode = "";
    /**
     * 交易结果描述
     */
    private String ResultMsg = "";
    /**
     * 客户端系统标识
     */
    private String CltSysCode = "01";
    /**
     * 客户端IP
     */
    private String ClinetIp = "";
    /**
     * 机器号
     */
    private String MachineNo = "";
    /**
     * 操作员机构
     */
    private String OperatorCom = "";
    /**
     * 服务端系统标识
     */
    private String SvrSysCode = "";
    /**
     * 路由主键1
     */
    private String RouteKey1 = "";
    /**
     * 路由主键2
     */
    private String RouteKey2 = "";
    /**
     * 路由主键3
     */
    private String RouteKey3 = "";
    /**
     * 登录认证码
     */
    private String RegCertifId = "";
    /**
     * 交易结束日期
     */
    private String TransEndDATE = "";
    /**
     * 交易结束时间
     */
    private String TransEndTime = "";
    /**
     * 限额1
     */
    private String ValConstrLimit1 = "";
    /**
     * 限额2
     */
    private String ValConstrLimit2 = "";
    /**
     * 班长ID
     */
    private String GranterNo = "";
    /**
     * 班长口令
     */
    private String GranterPwd = "";
    /**
     * 业务主键
     */
    private String BS_NO = "";
    /**
     * 扩展字段1
     */
    private String EXT_KEY1 = "";
    /**
     * 扩展字段2
     */
    private String EXT_KEY2 = "";
    /**
     * 扩展字段3
     */
    private String EXT_KEY3 = "";
    /**
     * E保通提交，请求报文
     */
    @XStreamAlias("Input")
    private FmsInterfaceInput Input;
    /**
     * E保通提交，返回报文
     */
    private List<FmsInterfaceOutput> Output;
    /**
     * E保通提交，请求报文 集合
     */
    @XStreamAlias("Output")
    private List<FmsInterfaceInput> ListInput;

    public String getTransType() {
        return TransType;
    }

    public void setTransType(String transType) {
        TransType = transType;
    }

    public String getTransCode() {
        return TransCode;
    }

    public void setTransCode(String transCode) {
        TransCode = transCode;
    }

    public String getHeaderVervsion() {
        return HeaderVervsion;
    }

    public void setHeaderVervsion(String headerVervsion) {
        HeaderVervsion = headerVervsion;
    }

    public String getSubTransCode() {
        return SubTransCode;
    }

    public void setSubTransCode(String subTransCode) {
        SubTransCode = subTransCode;
    }

    public String getTransDate() {
        return TransDate;
    }

    public void setTransDate(String transDate) {
        TransDate = transDate;
    }

    public String getTransTime() {
        return TransTime;
    }

    public void setTransTime(String transTime) {
        TransTime = transTime;
    }

    public String getTransSeq() {
        return TransSeq;
    }

    public void setTransSeq(String transSeq) {
        TransSeq = transSeq;
    }

    public String getOperator() {
        return Operator;
    }

    public void setOperator(String operator) {
        Operator = operator;
    }

    public String getTotalRowNum() {
        return TotalRowNum;
    }

    public void setTotalRowNum(String totalRowNum) {
        TotalRowNum = totalRowNum;
    }

    public String getRowNumStart() {
        return RowNumStart;
    }

    public void setRowNumStart(String rowNumStart) {
        RowNumStart = rowNumStart;
    }

    public String getResultCode() {
        return ResultCode;
    }

    public void setResultCode(String resultCode) {
        ResultCode = resultCode;
    }

    public String getResultMsg() {
        return ResultMsg;
    }

    public void setResultMsg(String resultMsg) {
        ResultMsg = resultMsg;
    }

    public String getCltSysCode() {
        return CltSysCode;
    }

    public void setCltSysCode(String cltSysCode) {
        CltSysCode = cltSysCode;
    }

    public String getClinetIp() {
        return ClinetIp;
    }

    public void setClinetIp(String clinetIp) {
        ClinetIp = clinetIp;
    }

    public String getMachineNo() {
        return MachineNo;
    }

    public void setMachineNo(String machineNo) {
        MachineNo = machineNo;
    }

    public String getOperatorCom() {
        return OperatorCom;
    }

    public void setOperatorCom(String operatorCom) {
        OperatorCom = operatorCom;
    }

    public String getSvrSysCode() {
        return SvrSysCode;
    }

    public void setSvrSysCode(String svrSysCode) {
        SvrSysCode = svrSysCode;
    }

    public String getRouteKey1() {
        return RouteKey1;
    }

    public void setRouteKey1(String routeKey1) {
        RouteKey1 = routeKey1;
    }

    public String getRouteKey2() {
        return RouteKey2;
    }

    public void setRouteKey2(String routeKey2) {
        RouteKey2 = routeKey2;
    }

    public String getRouteKey3() {
        return RouteKey3;
    }

    public void setRouteKey3(String routeKey3) {
        RouteKey3 = routeKey3;
    }

    public String getRegCertifId() {
        return RegCertifId;
    }

    public void setRegCertifId(String regCertifId) {
        RegCertifId = regCertifId;
    }

    public String getTransEndDATE() {
        return TransEndDATE;
    }

    public void setTransEndDATE(String transEndDATE) {
        TransEndDATE = transEndDATE;
    }

    public String getTransEndTime() {
        return TransEndTime;
    }

    public void setTransEndTime(String transEndTime) {
        TransEndTime = transEndTime;
    }

    public String getValConstrLimit1() {
        return ValConstrLimit1;
    }

    public void setValConstrLimit1(String valConstrLimit1) {
        ValConstrLimit1 = valConstrLimit1;
    }

    public String getValConstrLimit2() {
        return ValConstrLimit2;
    }

    public void setValConstrLimit2(String valConstrLimit2) {
        ValConstrLimit2 = valConstrLimit2;
    }

    public String getGranterNo() {
        return GranterNo;
    }

    public void setGranterNo(String granterNo) {
        GranterNo = granterNo;
    }

    public String getGranterPwd() {
        return GranterPwd;
    }

    public void setGranterPwd(String granterPwd) {
        GranterPwd = granterPwd;
    }

    public String getBS_NO() {
        return BS_NO;
    }

    public void setBS_NO(String bS_NO) {
        BS_NO = bS_NO;
    }

    public String getEXT_KEY1() {
        return EXT_KEY1;
    }

    public void setEXT_KEY1(String eXT_KEY1) {
        EXT_KEY1 = eXT_KEY1;
    }

    public String getEXT_KEY2() {
        return EXT_KEY2;
    }

    public void setEXT_KEY2(String eXT_KEY2) {
        EXT_KEY2 = eXT_KEY2;
    }

    public String getEXT_KEY3() {
        return EXT_KEY3;
    }

    public void setEXT_KEY3(String eXT_KEY3) {
        EXT_KEY3 = eXT_KEY3;
    }

    public FmsInterfaceInput getInput() {
        return Input;
    }

    public void setInput(FmsInterfaceInput input) {
        Input = input;
    }

    public List<FmsInterfaceOutput> getOutput() {
        return Output;
    }

    public void setOutput(List<FmsInterfaceOutput> output) {
        Output = output;
    }

    public List<FmsInterfaceInput> getListInput() {
        return ListInput;
    }

    public void setListInput(List<FmsInterfaceInput> listInput) {
        ListInput = listInput;
    }

}
