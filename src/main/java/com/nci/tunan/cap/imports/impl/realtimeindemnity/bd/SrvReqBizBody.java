package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/** 
 * @ClassName: SrvReqBizBody 
 * @description: 实时代付与资金交易报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:28:47 
 * @.belongToModule 收付费-实时代付  
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    @XmlElement(name = "InputData")
    private IndemnityInputVO inputData;

    public IndemnityInputVO getInputData() {
        return inputData;
    }

    public void setInputData(IndemnityInputVO inputData) {
        this.inputData = inputData;
    }
}
