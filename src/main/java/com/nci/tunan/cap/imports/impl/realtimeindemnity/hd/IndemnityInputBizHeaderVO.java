package com.nci.tunan.cap.imports.impl.realtimeindemnity.hd;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.udmp.framework.model.BaseVO;

/** 
 * @ClassName: IndemnityBizHeaderVO 
 * @description 实时代付与资金交易报文头
 * <AUTHOR>
 * @date 2017年12月11日 上午11:27:52 
 * @.belongToModule 收付费-实时代付 
 */ 
@XmlType(propOrder={"transSource", "transCode", "transDate", "transTime", "transSeq"})
public class IndemnityInputBizHeaderVO extends BaseVO {

    /** 
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
     */ 
    private static final long serialVersionUID = 7655904708215012541L;

    /**
     * @Fields transSource : 交易来源  HX
     */
    private String transSource = "HX";

    /**
     * @Fields transCode : 交易编码  1908
     */
    private String transCode = "1908";

    /**
     * @Fields transDate : 交易日期  格式是yyyyMMdd
     */
    private String transDate;

    /**
     * @Fields transTime : 交易时间   格式是HH24miss
     */
    private String transTime;

    /**
     * @Fields transSeq : 交易流水号   时间戳，格式是yyyymmddhh24missff4
     */
    private String transSeq;

    public String getTransSource() {
        return transSource;
    }
    @XmlElement(name="TransSource")
    public void setTransSource(String transSource) {
        this.transSource = transSource;
    }

    public String getTransCode() {
        return transCode;
    }
    @XmlElement(name="TransCode")
    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public String getTransDate() {
        return transDate;
    }
    @XmlElement(name="TransDate")
    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransTime() {
        return transTime;
    }
    @XmlElement(name="TransTime")
    public void setTransTime(String transTime) {
        this.transTime = transTime;
    }

    public String getTransSeq() {
        return transSeq;
    }
    @XmlElement(name="TransSeq")
    public void setTransSeq(String transSeq) {
        this.transSeq = transSeq;
    }

    @Override
    public String getBizId() {
        return null;
    }

}
