package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.udmp.framework.model.BaseVO;
/** 
 * @ClassName: IndemnityOutputVO 
 * @description: 实时代付与资金交易响应报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:28:47 
 * @.belongToModule 收付费-实时代付  
 */
public class IndemnityOutputVO extends BaseVO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */
    private static final long serialVersionUID = 1071078479803501891L;

    @XmlElement(name="Result")
    private List<IndemnityOutputResultVO> Result;

    public List<IndemnityOutputResultVO> getResult() {
        return Result;
    }
    @XmlElement(name="Result")
    public void setResult(List<IndemnityOutputResultVO> result) {
        Result = result;
    }

    @Override
    public String getBizId() {
        return null;
    }

}
