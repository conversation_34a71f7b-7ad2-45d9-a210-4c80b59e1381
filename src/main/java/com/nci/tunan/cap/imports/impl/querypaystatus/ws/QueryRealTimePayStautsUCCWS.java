package com.nci.tunan.cap.imports.impl.querypaystatus.ws;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;

import com.nci.tunan.cap.imports.impl.querypaystatus.bd.SrvReqBody;
import com.nci.tunan.cap.imports.impl.querypaystatus.bd.SrvResBody;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
/** 
 * @ClassName: QueryRealTimePayStautsUCCWSImpl 
 * @description 实时付费查询接口类
 * <AUTHOR>
 * @date 2021年12月15日 上午11:29:25 
 * @.belongToModule 收付费-实时付费
 */
@WebService(targetNamespace = "http://www.newchinalife.com", name = "QueryRealTimePayStauts")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface QueryRealTimePayStautsUCCWS {

    /** 
     * @description 实时付费查询接口方法
     * @param parametersReqHeader 入参头
     * @param parametersReqBody 入参体
     * @param parametersResHeader 出参头
     * @param parametersResBody  出参体 
     * @throws 
     */
    @WebMethod(operationName = "Real-timePaymentStatusQuery", action = "Real-timePaymentStatusQuery")
    public void queryPayStatus(
            @WebParam(partName = "parametersReqHeader", name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true)
            SysHeader parametersReqHeader,
            @WebParam(partName = "parametersReqBody", name = "request", targetNamespace = "http://www.newchinalife.com/service/bd") 
            SrvReqBody parametersReqBody,
            @WebParam(partName = "parametersResHeader", mode = WebParam.Mode.OUT, name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true) 
            Holder<SysHeader> parametersResHeader,
            @WebParam(partName = "parametersResBody", mode = WebParam.Mode.OUT, name = "response", targetNamespace = "http://www.newchinalife.com/service/bd") 
            Holder<SrvResBody> parametersResBody);

}
