
package com.nci.tunan.cap.imports.cn.com.fingard;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/** 
 * @ClassName: SubmitResponse 
 * @description E保通交易请求类
 * <AUTHOR>
 * @date 2015年12月17日 下午3:40:26 
 * @.belongToModule 收付费-E保通交易请求类
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "submitResult"
})
@XmlRootElement(name = "SubmitResponse")
public class SubmitResponse {

    /** 
     * 请求提交结果
     */ 
    @XmlElement(name = "SubmitResult")
    protected String submitResult;

    public String getSubmitResult() {
        return submitResult;
    }

    public void setSubmitResult(String value) {
        this.submitResult = value;
    }

}
