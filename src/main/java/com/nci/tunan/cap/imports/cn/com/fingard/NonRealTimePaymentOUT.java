package com.nci.tunan.cap.imports.cn.com.fingard;

import com.thoughtworks.xstream.annotations.XStreamOmitField;


public class NonRealTimePaymentOUT  {
    
   
    
    /** 
    * @Fields ReqSeqID : 交易批号
    */ 
    @XStreamOmitField
    private String ReqSeqID;
    
    /** 
    * @Fields ReqReserved1 : 保留字段
    */ 
    private String ReqReserved1;
    
    /** 
    * @Fields ReqReserved2 : 保留字段
    */ 
    private String ReqReserved2;
    
    
    public String getReqSeqID() {
        return ReqSeqID;
    }


    public void setReqSeqID(String reqSeqID) {
        ReqSeqID = reqSeqID;
    }


    public String getReqReserved1() {
        return ReqReserved1;
    }


    public void setReqReserved1(String reqReserved1) {
        ReqReserved1 = reqReserved1;
    }


    public String getReqReserved2() {
        return ReqReserved2;
    }


    public void setReqReserved2(String reqReserved2) {
        ReqReserved2 = reqReserved2;
    }
}
