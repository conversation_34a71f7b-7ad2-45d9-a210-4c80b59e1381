package com.nci.tunan.cap.imports;

import com.nci.udmp.framework.signature.IService;

/**
 * 邮件服务实现类
 * 
 * <AUTHOR> <EMAIL>
 * @version 0.0.1
 * @date 2014年08月22日 下午5:29:16 
 * @.belongToModule 收付费-邮件服务实现类
 */
public class MailServiceImpl implements IService {

    /**
     * 发送邮件时调用
     */
    public void sendMail() {

        // 1、制盘文件传输完成发送邮件通知资金结算人员（邮件组）

        // 2、疑似重复数据收付数据，邮件发送至资金结算人员（邮件组）

    }

}
