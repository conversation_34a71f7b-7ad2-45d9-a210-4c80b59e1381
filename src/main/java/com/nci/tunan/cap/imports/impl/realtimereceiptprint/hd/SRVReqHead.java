
package com.nci.tunan.cap.imports.impl.realtimereceiptprint.hd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SRVReqHead", namespace = "http://www.newchinalife.com/service/hd", propOrder = {
    "transType",
    "userCode",
    "roleId",
    "manageCode",
    "sysCode",
    "mouldCode",
    "buttonCode",
    "tranCode",
    "transSerialno",
    "userName",
    "manageCom",
    "recordLog",
    "logDetail",
    "ipStr",
    "pageId",
    "pageId4Help",
    "inputId",
    "keyCode"
})
public class SRVReqHead {

    @XmlElement(required = true)
    protected String transType = "1000";
    @XmlElement(required = true)
    protected String userCode = "";
    @XmlElement(required = true)
    protected String roleId = "";
    @XmlElement(required = true)
    protected String manageCode = "";
    @XmlElement(required = true)
    protected String sysCode = "";
    @XmlElement(required = true)
    protected String mouldCode = "";
    @XmlElement(required = true)
    protected String buttonCode = "";
    @XmlElement(required = true)
    protected String tranCode = "";
    @XmlElement(required = true)
    protected String transSerialno = "";
    @XmlElement(required = true)
    protected String userName = "";
    @XmlElement(required = true)
    protected String manageCom = "";
    @XmlElement(required = true)
    protected String recordLog = "";
    @XmlElement(required = true)
    protected String logDetail = "";
    @XmlElement(required = true)
    protected String ipStr = "";
    @XmlElement(required = true)
    protected String pageId = "";
    @XmlElement(required = true)
    protected String pageId4Help = "";
    @XmlElement(required = true)
    protected String inputId = "";
    @XmlElement(required = true)
    protected String keyCode = "";

    public String getTransType() {
        return transType;
    }

    public void setTransType(String value) {
        this.transType = value;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String value) {
        this.userCode = value;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String value) {
        this.roleId = value;
    }

    public String getManageCode() {
        return manageCode;
    }

    public void setManageCode(String value) {
        this.manageCode = value;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String value) {
        this.sysCode = value;
    }

    public String getMouldCode() {
        return mouldCode;
    }

    public void setMouldCode(String value) {
        this.mouldCode = value;
    }

    public String getButtonCode() {
        return buttonCode;
    }

    public void setButtonCode(String value) {
        this.buttonCode = value;
    }

    public String getTranCode() {
        return tranCode;
    }

    public void setTranCode(String value) {
        this.tranCode = value;
    }

    public String getTransSerialno() {
        return transSerialno;
    }

    public void setTransSerialno(String value) {
        this.transSerialno = value;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String value) {
        this.userName = value;
    }

    public String getManageCom() {
        return manageCom;
    }

    public void setManageCom(String value) {
        this.manageCom = value;
    }

    public String getRecordLog() {
        return recordLog;
    }

    public void setRecordLog(String value) {
        this.recordLog = value;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String value) {
        this.logDetail = value;
    }

    public String getIpStr() {
        return ipStr;
    }

    public void setIpStr(String value) {
        this.ipStr = value;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String value) {
        this.pageId = value;
    }

    public String getPageId4Help() {
        return pageId4Help;
    }

    public void setPageId4Help(String value) {
        this.pageId4Help = value;
    }

    public String getInputId() {
        return inputId;
    }

    public void setInputId(String value) {
        this.inputId = value;
    }

    public String getKeyCode() {
        return keyCode;
    }

    public void setKeyCode(String value) {
        this.keyCode = value;
    }

}
