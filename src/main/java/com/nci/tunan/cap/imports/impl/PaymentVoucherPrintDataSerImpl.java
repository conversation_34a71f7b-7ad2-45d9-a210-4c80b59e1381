package com.nci.tunan.cap.imports.impl;

import java.net.URL;

import javax.xml.namespace.QName;

import org.slf4j.Logger;

import com.nci.tunan.cap.imports.IPaymentVocuherPrintDataSer;
import com.nci.tunan.cap.imports.ISipserverPortType;
import com.nci.tunan.cap.imports.Sipserver;
import com.nci.tunan.cap.interfaces.model.vo.DocumentPrintClientVO;
import com.nci.tunan.cap.util.DocumentTypeUtil;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 
 * @description 付费凭证打印确认接口
 * <AUTHOR> <EMAIL>
 * @date 2015-8-23 下午4:16:58
 * @.belongToModule 收付费-付费凭证打印确认接口
 */
public class PaymentVoucherPrintDataSerImpl implements IPaymentVocuherPrintDataSer {
    /** 
     * 接口域名
     */ 
    private static final QName SERVICE_NAME = new QName("urn:sipserverType", "sipserver");
    /**
     * 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();
    
    /**
     * 调用打印系统的服务，进行预览、打印
     * 
     * @param ip        IP
     * @param userName  用户名
     * @param sendType  发送类型
     * @param jarPath   jar路径
     * @param xmlPath   XML路径
     * @param fileName  文件名
     * @param printType 打印类型
     * @return          Vo
     * @throws Exception 异常
     */
    @Override
    public DocumentPrintClientVO voucherInfoSend(String ip, String userName, String sendType, String jarPath,
            String xmlPath, String fileName, String printType) throws Exception {
        //@invalid 调用打印服务
        //@invalid SipClient sc1 = new SipClient("http://***********:5999");
        javax.xml.ws.Holder<java.lang.String> sendJobId = new javax.xml.ws.Holder<String>();
        javax.xml.ws.Holder<java.lang.String> sendJobError = new javax.xml.ws.Holder<String>();
        //1.获取配置文件内容
       //@invalid Map<String, String> proMap = PropertiesUtil.getMapFromPropertiesPath(ConstantUtil.NB_UW_LOGINADMIN_PATH);
        String documentip = DocumentTypeUtil.DOCUMENTSERVICE; //@invalid 通知书服务地址
        try {
            URL wsdlURL = new URL(documentip);
            Sipserver ss = new Sipserver(wsdlURL, SERVICE_NAME);
            ISipserverPortType port = ss.getSipserver();

            java.lang.String sendJobClientIP = ip; 
            java.lang.String sendJobUserName = userName;
            java.lang.String sendJobJobName = fileName;
            java.lang.String sendJobvp = sendType;
            //@invalid 输入字节流
            byte[] sendJobSplFile = xmlPath.getBytes();
            int sendJobStage = -1;
            //2.执行通知书发送数据的方法
            port.sendJob(sendJobClientIP, sendJobUserName, sendJobvp, sendJobJobName, sendJobSplFile,
                    sendJobStage, sendJobId, sendJobError);
        } catch (BizException e) {
            logger.error(e.getMessage());
        }
        //3.通知书打印内容发送结束并获取发送数据的jobid
        
        DocumentPrintClientVO documentvo = new DocumentPrintClientVO();
        if (sendJobId.value != null && !sendJobId.value.equals("")) {
            documentvo.setServiceIp(DocumentTypeUtil.DOCUMENTIP);
            documentvo.setJobId(sendJobId.value);
        }
        return documentvo;
    }
}