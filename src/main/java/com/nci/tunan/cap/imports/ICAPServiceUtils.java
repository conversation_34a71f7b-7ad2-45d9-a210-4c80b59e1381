package com.nci.tunan.cap.imports;

import java.util.List;
import java.util.Map;

import com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputVO;
import com.nci.tunan.cap.interfaces.model.bo.PremArapBO;
import com.nci.tunan.cap.interfaces.model.vo.PayeeVO;
import com.nci.tunan.cap.interfaces.model.vo.RealTimeIndemnityInputVO;
import com.nci.tunan.cap.interfaces.model.vo.RealTimeIndemnityOutputVO;
import com.nci.tunan.cap.interfaces.vo.report.StatementFileAndSapVO;
import com.nci.tunan.clm.interfaces.vo.claimChangePayMode.ClaimChangePayModeReqVO;
import com.nci.tunan.clm.interfaces.vo.claimChangePayMode.ClaimChangePayModeResVO;

/**
 * @description 服务总接口
 * <AUTHOR>
 * @date 2015-11-06 10:14:22
 * @.belongToModule 收付费-服务总接口
 * 
 */
public interface ICAPServiceUtils {

    /**
     * @description 收付费上传影像回写契约接口
     * @param inputData 输入参数VO:com.nci.tunan.nb.interfaces.paymentImageScan.vo.InputImageScanReqData
     * @return 输出的参数VO：com.nci.tunan.nb.interfaces.paymentImageScan.vo.OutputImageScanResData
    */
    public com.nci.tunan.nb.interfaces.paymentImageScan.vo.OutputImageScanResData nbimagescanpaymentuccinsertImagescan(
            com.nci.tunan.nb.interfaces.paymentImageScan.vo.InputImageScanReqData inputData);
    
    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData paipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData uwipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData nbipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);
    
    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData cipipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData prdipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData clmipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData csipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData mmsipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO:com.nci.core.common.interfaces.vo.system.feeconfirm.
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData cssipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData);

    /**
     * @Description: (这里用一句话描述这个方法的作用)
     * @param inputData
     *            入參
     * @return 设定文件
     * @throws
     */
    public RealTimeIndemnityOutputVO payCommit(RealTimeIndemnityInputVO inputData);

    /**
     * @description 回调收费通知接口
     * @version 0.0.1
     * @title callbackSubSystem
     * @<NAME_EMAIL>
     * @param lstBO
     *            应收应付集合
     * @param otherInfo
     *            应包含FeeStatus,FinishTime,FundsRtnCode,BankDealDate,PayrefNo
     */
    public void callbackSubSystem(List<PremArapBO> lstBO, PremArapBO otherInfo);
    /**
     * @description 回调收费通知接口
     * @version 0.0.1
     * @title callbackSubSystem
     * @<NAME_EMAIL>
     * @param lstBO
     *            应收应付集合
     * @return 结果
     */
    public List<FeeConfirmOutputVO> callbackSubSystem(Map<PremArapBO, List<PremArapBO>> lstBO);
    /**
     * @description 收付费上海医保缴费结果回写契约接口
     * @param inputData 输入参数VO:com.nci.tunan.nb.interfaces.shhealthcare.vo.InputSHPaymentReqData
     * @return 输出的参数VO：com.nci.tunan.nb.interfaces.shhealthcare.vo.OutputSHPaymentResData
    */
    public com.nci.tunan.nb.interfaces.shhealthcare.vo.OutputSHPaymentResData nbSHPaymentResult(com.nci.tunan.nb.interfaces.shhealthcare.vo.InputSHPaymentReqData inputData) ;
    
    
    /**
     * @description 调保单接口根据客户ID查询客户9要素信息
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param inputData 客户ID
     * @return com.nci.tunan.pa.interfaces.customerinformation.exports.vo.QueryCustomerInformationResDataVO
    */
    public PayeeVO paCustomerInfo(String customerId) ;

	/**
	 * 通知理赔修改支付方式接口
	 * @param clmvo
	 * @return ClaimChangePayModeResVO
	 */
	ClaimChangePayModeResVO claimChangePayMode(ClaimChangePayModeReqVO clmvo);
	
	public com.nci.tunan.nb.interfaces.queryqyserviceinformation.vo.OutputData nbReturn(com.nci.tunan.nb.interfaces.queryqyserviceinformation.vo.InputData applyCode);
	
	/**
	 * @description 投保单号查询契约投保单合同总保费
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param inputData 投保单号
	 * @return 0:总保费未超额;1:总保费超额,投被保人为同一人;2总保费超额,投被保人为同一人.
	*/
	public String queryPolicyPremium(String inputData);
    
	/**
     * @description 新增制表信息接口
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param sapVO
     * @return 是否成功
    */
	public boolean createStatementFileAndSap(StatementFileAndSapVO sapVO);
	
	/**
     * 组装返回报文
     * @param lstBO 应收应付列表
     * @param otherInfo 应收应付bo对象
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData callbackSubSystem1(List<PremArapBO> lstBO, PremArapBO otherInfo);
    
	
}
