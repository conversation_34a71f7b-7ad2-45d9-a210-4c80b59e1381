package com.nci.tunan.cap.imports.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.xml.ws.Holder;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.slf4j.Logger;

import com.caucho.hessian.client.HessianProxyFactory;
import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData;
import com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData;
import com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputVO;
import com.nci.core.common.interfaces.vo.system.feeconfirm.PremArapVO;
import com.nci.tunan.cap.imports.ICAPServiceUtils;
import com.nci.tunan.cap.imports.impl.realtimeindemnity.bd.SrvReqBizBody;
import com.nci.tunan.cap.imports.impl.realtimeindemnity.bd.SrvReqBody;
import com.nci.tunan.cap.imports.impl.realtimeindemnity.bd.SrvResBody;
import com.nci.tunan.cap.imports.impl.realtimeindemnity.ws.IndemnityPayCommit;
import com.nci.tunan.cap.interfaces.model.bo.PremArapBO;
import com.nci.tunan.cap.interfaces.model.vo.PayeeVO;
import com.nci.tunan.cap.interfaces.model.vo.RealTimeIndemnityInputVO;
import com.nci.tunan.cap.interfaces.model.vo.RealTimeIndemnityOutputVO;
import com.nci.tunan.cap.interfaces.model.vo.RepeatPremArapVO;
import com.nci.tunan.cap.util.CodeCst;
import com.nci.tunan.clm.interfaces.vo.claimChangePayMode.ClaimChangePayModeResVO;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.component.serviceinvoke.ServiceCommonMethod;
import com.nci.udmp.component.serviceinvoke.ServiceInvokeParameterBean;
import com.nci.udmp.component.serviceinvoke.message.ServiceResult;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * @description 服务总接口工具实现类
 * <AUTHOR>
 * @date 2016-04-19 16:26:14a 
 * @.belongToModule 收付费-服务总接口
 * 
 */
public class CAPServiceUtils implements ICAPServiceUtils {
	

    /**
     * @Fields logger : 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();
    
    
    /**
     * @description 收付费上传影像回写契约接口
     * @param inputData 输入参数VO:com.nci.tunan.nb.interfaces.paymentImageScan.vo.InputImageScanReqData
     * @return 输出的参数VO：com.nci.tunan.nb.interfaces.paymentImageScan.vo.OutputImageScanResData
    */
    public com.nci.tunan.nb.interfaces.paymentImageScan.vo.OutputImageScanResData nbimagescanpaymentuccinsertImagescan(
            com.nci.tunan.nb.interfaces.paymentImageScan.vo.InputImageScanReqData inputData) {
        
        /*
         * 1.1 生成hessian地址
         */
        String hsUrl = Constants.SERVICEENVPARAMAP.get("NB") + "remoting/NB_imageScanPaymentUccinsertImagescanAddr";
        /*
         * 1.2 报文参数
         */
        /*
         * 1.2.1 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.2.2 定义系统报文体
         */
        com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvReqBody srvReqBody = new 
              com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvReqBody();
        /*
         * 1.2.3 定义业务报文体
         */
        com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvReqBizBody bizBody = new 
              com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvReqBizBody();

        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.3 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        
        /*
         * 1.4 返回参数
         */
        com.nci.tunan.nb.interfaces.paymentImageScan.vo.OutputImageScanResData result = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvResBody resultbody = new com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvResBody();
        try {
            /*
             * 1.5 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6 通过工厂创建接口实例
             */
            com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.hs.ImageScanPaymentUccHS imagescanpaymentucchs = (com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.hs.ImageScanPaymentUccHS) factory.create(hsUrl);
            /*
             * 1.7 通过接口实例调用接口方法
             */
            tempresult = imagescanpaymentucchs.insertImagescan(sysheader, srvReqBody);
            /*
             * 1.8 返回值赋值
             */
            resultbody = 
                    (com.nci.tunan.nb.interfaces.paymentImageScan.exports.imagescanpaymentucc.insertimagescan.SrvResBody)tempresult.getResponse();
            result = resultbody.getBizBody().getOutputData();
            /*
             * 1.9 添加调用后的日志输出
             */
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData paipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "pa" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeAPIDealManagement(DealTrigger.ACTION,
                    dealNo, dealTime);
        }
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        try {
            logger.debug("pafeeDataConfirm保单收付费确认传入参数：{}",XmlHelper.classToXml(inputData));
            result = BOServiceFactory.getPubCapUniModelUCC().feeDataConfirm(
                    inputData);
            logger.info("pafeeDataConfirm保单收付费确认返回参数:{}",XmlHelper.classToXml(result));
        } catch (Exception e) {
            logger.info("pafeeDataConfirm保单收付费确认调用接口异常:{}",e);
        } finally {
            if (dealSwitch) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterAPIDealManagement(DealTrigger.ACTION,
                        dealNo, dealTime);
            }
        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData uwipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        /*
         * 1.1 调用的webService的wsdl
         */
        String wsdl = Constants.SERVICEENVPARAMAP.get("UW")
                + "webservice/pubCapUniModelUCCfeeDataConfirmAddr?wsdl";
        if (Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("1144") != null
                && Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("1144").equals("Y")) {
            wsdl = Constants.SERVICEENVPARAMAP.get("ESB")
                    + Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1144")
                    + "?wsdl";
        }
        /*
         * 1.2 服务接口路径
         */
        String interfacePath = "com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS";
        /*
         * 1.3 报文参数
         */
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody> parametersResBody = 
                new Holder<com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody>();
        /*
         * 1.4 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1144"));
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        parametersResHeader.value = sysheader;
        /*
         * 1.5 定义系统报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody srvReqBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody();
        /*
         * 1.6 定义业务报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody bizBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "uw" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.7 添加调用前的日志输出
         */
        try {
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
                    + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(sysheader,
                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.WEBSERVICE, dealNo, dealTime);
        }
        /*
         * 1.8 返回参数
         */
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        String dealStatus = "2";
        try {
            /*
             * 1.8.1 调用公共webservice工厂方法
             */
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod
                    .CommonWebServiceFactory(wsdl, interfacePath);
            /*
             * 1.8.2 通过工厂创建接口实例
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS ipubcapunimodeluccws = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS) soapFactory
                    .create();
            logger.info("uwfeeDataConfirm核保收付费确认传入参数:{}", DataSerialJSon.fromObject(srvReqBody));
            /*
             * 1.8.3 调用接口的指定方法，将参数传入
             */
            ipubcapunimodeluccws.feeDataConfirm(sysheader, srvReqBody,
                    parametersResHeader, parametersResBody);
            /*
             * 1.8.4 将业务报文体的outputData赋值给返回值
             */
            result = parametersResBody.value.getBizBody().getOutputData();
            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
            CommonHeaderDeal.setBIZHEADERTHREAD(parametersResBody.value
                    .getBizHeader());
            /*
             * 1.8.5 添加调用后的日志输出
             */
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.info("uwfeeDataConfirm核保收付费确认返回参数:{}", DataSerialJSon.fromObject(parametersResBody));
        } catch (BizException e) {
            e.printStackTrace();
            logger.info("webservice调用业务接口异常",e);
            dealStatus = "3";
        } catch (ClassNotFoundException e1) {
            logger.info("uwfeeDataConfirm找不到接口类",e1);
            dealStatus = "3";
        } catch (Exception e2) {
            logger.info("uwfeeDataConfirm核保收付费确认异常",e2);
            dealStatus = "3";
        } finally {
            if (dealSwitch && parametersResBody.value == null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader, null,
                        null, DealTrigger.WEBSERVICE, dealNo, dealTime,
                        dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        parametersResBody.value.getBizHeader(),
                        parametersResBody.value.getBizBody(),
                        DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            }

        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData nbipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String hsUrl = Constants.SERVICEENVPARAMAP.get("NB")
                + "remoting/pubCapUniModelUCCfeeDataConfirmAddr";
        /*
         * 1.1 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1145"));
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        /*
         * 1.2 定义系统报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody srvReqBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody();
        /*
         * 1.3 定义业务报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody bizBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "nb" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.4 添加调用前的日志输出
         */
        try {
            logger.info("nbifeeDataConfirmUrl：" + sysheader.getMsgId()+"##"+hsUrl + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info("nbifeeDataConfirm契约收付费确认传入报文：{}",
                    DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(sysheader,
                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.HESSIAN, dealNo, dealTime);
        }
        /*
         * 1.5 返回参数
         */
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        try {
            /*
             * 1.5.1 调用公共webservice工厂方法
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.5.2 通过工厂创建接口实例
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.hs.IPubCapUniModelUCCHS ipubcapunimodeluccws = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.hs.IPubCapUniModelUCCHS) factory
                    .create(hsUrl);
            /*
             * 1.5.3 调用接口的指定方法，将参数传入
             */
            com.nci.udmp.component.serviceinvoke.message.ServiceResult tempresult = ipubcapunimodeluccws
                    .feeDataConfirm(sysheader, srvReqBody);
            /*
             * 1.5.4 返回值赋值
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody resultbody = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody) tempresult
                    .getResponse();
            result = resultbody.getBizBody().getOutputData();
            logger.info("ipubcapunimodeluccws.feeDataConfirm返回报文:{}",   DataSerialJSon.fromObject(result));
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("hessian调用业务接口异常");
        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData cipipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String hsUrl = Constants.SERVICEENVPARAMAP.get("CIPJRQD")
                + "remoting/pubCapUniModelUCCfeeDataConfirmAddr";
        /*
         * 1.1 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1145"));
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        /*
         * 1.2 定义系统报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody srvReqBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody();
        /*
         * 1.3 定义业务报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody bizBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "nb" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.4 添加调用前的日志输出
         */
        try {
            logger.info("cipifeeDataConfirmUrl：" + sysheader.getMsgId()+"##"+hsUrl + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info("cipifeeDataConfirm接入渠道收付费确认传入报文：{}",
                    DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(sysheader,
                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.HESSIAN, dealNo, dealTime);
        }
        /*
         * 1.5 返回参数
         */
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        try {
            /*
             * 1.5.1 调用公共webservice工厂方法
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.5.2 通过工厂创建接口实例
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.hs.IPubCapUniModelUCCHS ipubcapunimodeluccws = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.hs.IPubCapUniModelUCCHS) factory
                    .create(hsUrl);
            /*
             * 1.5.3 调用接口的指定方法，将参数传入
             */
            com.nci.udmp.component.serviceinvoke.message.ServiceResult tempresult = ipubcapunimodeluccws
                    .feeDataConfirm(sysheader, srvReqBody);
            /*
             * 1.5.4 返回值赋值
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody resultbody = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody) tempresult
                    .getResponse();
            result = resultbody.getBizBody().getOutputData();
            logger.info("ipubcapunimodeluccws.feeDataConfirm返回报文:{}",   DataSerialJSon.fromObject(result));
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("hessian调用业务接口异常");
        }
        return result;
    }
    
    
    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData prdipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "prd" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeAPIDealManagement(DealTrigger.ACTION,
                    dealNo, dealTime);
        }
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        try {
            logger.info("API调用接口开始");
            logger.info("业务公共-收付费确认请求数据："
                    + inputData.getPremArapList().toString());
            result = BOServiceFactory.getPubCapUniModelUCC().feeDataConfirm(
                    inputData);
            logger.info("API调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("API接口调用失败");
        } finally {
            if (dealSwitch) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterAPIDealManagement(DealTrigger.ACTION,
                        dealNo, dealTime);
            }
        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData clmipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "clm" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeAPIDealManagement(DealTrigger.ACTION,
                    dealNo, dealTime);
        }
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        try {
            logger.info("API调用接口开始");
            logger.info("业务公共-收付费确认请求数据："
                    + inputData.getPremArapList().toString());
            logger.debug("clmfeeDataConfirm理赔收付费确认传入参数：{}",XmlHelper.classToXml(inputData));
            result = BOServiceFactory.getPubCapUniModelUCC().feeDataConfirm(
                    inputData);
            logger.info("clmfeeDataConfirm理赔收付费确认返回参数:{}",XmlHelper.classToXml(result));
        } catch (Exception e) {
            logger.info("clmfeeDataConfirm理赔收付费确认API接口调用异常",e);
        } finally {
            if (dealSwitch) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterAPIDealManagement(DealTrigger.ACTION,
                        dealNo, dealTime);
            }
        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData csipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "cs" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeAPIDealManagement(DealTrigger.ACTION,
                    dealNo, dealTime);
        }
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        try {
            logger.info("API调用接口开始");
            logger.info("业务公共-收付费确认请求数据："
                    + inputData.getPremArapList().toString());
            logger.debug("csifeeDataConfirm保全收付费确认传入参数：{}",XmlHelper.classToXml(inputData));
            result = BOServiceFactory.getPubCapUniModelUCC().feeDataConfirm(
                    inputData);
            logger.info("csifeeDataConfirm保全收付费确认返回参数:{}",XmlHelper.classToXml(result));
        } catch (Exception e) {
         
            logger.info("csifeeDataConfirm保全收付费确认API接口调用异常",e);
        } finally {
            if (dealSwitch) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterAPIDealManagement(DealTrigger.ACTION,
                        dealNo, dealTime);
            }
        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData mmsipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        /*
         * 1.1 调用的webService的wsdl
         */
        String wsdl = Constants.SERVICEENVPARAMAP.get("MMS")
                + "webservice/pubCapUniModelUCCfeeDataConfirmAddr?wsdl";
        if (Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("1151") != null
                && Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("1151").equals("Y")) {
            wsdl = Constants.SERVICEENVPARAMAP.get("ESB")
                    + Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1151")
                    + "?wsdl";
        }
        /*
         * 1.2 服务接口路径
         */
        String interfacePath = "com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS";
        /*
         * 1.3 报文参数
         */
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody> parametersResBody = 
                new Holder<com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody>();
        /*
         * 1.4 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1151"));
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        parametersResHeader.value = sysheader;
        /*
         * 1.5 定义系统报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody srvReqBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody();
        /*
         * 1.6 定义业务报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody bizBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "mms" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.7 添加调用前的日志输出
         */
        try {
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
                    + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(sysheader,
                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.WEBSERVICE, dealNo, dealTime);
        }
        /*
         * 1.8 返回参数
         */
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        String dealStatus = "2";
        try {
            /*
             * 1.8.1 调用公共webservice工厂方法
             */
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod
                    .CommonWebServiceFactory(wsdl, interfacePath);
            /*
             * 1.8.2 通过工厂创建接口实例
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS ipubcapunimodeluccws = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS) soapFactory
                    .create();
            /*
             * 1.8.3 调用接口的指定方法，将参数传入
             */
            ipubcapunimodeluccws.feeDataConfirm(sysheader, srvReqBody,
                    parametersResHeader, parametersResBody);
            /*
             * 1.8.4 将业务报文体的outputData赋值给返回值
             */
            result = parametersResBody.value.getBizBody().getOutputData();
            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
            CommonHeaderDeal.setBIZHEADERTHREAD(parametersResBody.value
                    .getBizHeader());
            /*
             * 1.8.5 添加调用后的日志输出
             */
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文体：" + DataSerialJSon.fromObject(parametersResBody));
            logger.info("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.info("webservice调用业务接口异常");
            dealStatus = "3";
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.info("找不到接口类");
            dealStatus = "3";
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.info("解析返回的报文的JSON字符串发生异常");
            dealStatus = "3";
        } finally {
            if (dealSwitch && parametersResBody.value == null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader, null,
                        null, DealTrigger.WEBSERVICE, dealNo, dealTime,
                        dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        parametersResBody.value.getBizHeader(),
                        parametersResBody.value.getBizBody(),
                        DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            }

        }
        return result;
    }

    /**
     * @description 业务公共-收付费确认
     * @param inputData
     *            输入参数VO
     *            FeeConfirmInputData
     * @return 输出的参数VO：com.nci.core.common.interfaces.vo.system.feeconfirm.
     *         FeeConfirmOutputData
     */
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData cssipubcapunimodeluccfeeDataConfirm(
            com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmInputData inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        /*
         * 1.1 调用的webService的wsdl
         */
        String wsdl = Constants.SERVICEENVPARAMAP.get("CSS")
                + "webservice/pubCapUniModelUCCfeeDataConfirmAddr?wsdl";
        if (Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("1152") != null
                && Constants.SERVICE_PUBLISH_ID_ESB_MAP.get("1152").equals("Y")) {
            wsdl = Constants.SERVICEENVPARAMAP.get("ESB")
                    + Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1152")
                    + "?wsdl";
        }
        /*
         * 1.2 服务接口路径
         */
        String interfacePath = "com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS";
        /*
         * 1.3 报文参数
         */
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody> parametersResBody = 
                new Holder<com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvResBody>();
        /*
         * 1.4 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd(Constants.SERVICE_PUBLISH_ID_CODE_MAP.get("1152"));
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);
        parametersResHeader.value = sysheader;
        /*
         * 1.5 定义系统报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody srvReqBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBody();
        /*
         * 1.6 定义业务报文体
         */
        com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody bizBody = 
                new com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "css" + "_" + "ipubcapunimodeluccws" + "_"
                + "feeDataConfirm";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.7 添加调用前的日志输出
         */
        try {
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
                    + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        if (dealSwitch) {
            logger.info("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(sysheader,
                    srvReqBody.getBizHeader(), srvReqBody.getBizBody(),
                    DealTrigger.WEBSERVICE, dealNo, dealTime);
        }
        /*
         * 1.8 返回参数
         */
        com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData result = null;
        String dealStatus = "2";
        try {
            /*
             * 1.8.1 调用公共webservice工厂方法
             */
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod
                    .CommonWebServiceFactory(wsdl, interfacePath);
            /*
             * 1.8.2 通过工厂创建接口实例
             */
            com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS ipubcapunimodeluccws = 
                    (com.nci.core.common.interfaces.cap.exports.ipubcapunimodelucc.feedataconfirm.ws.IPubCapUniModelUCCWS) soapFactory.create();
            /*
             * 1.8.3 调用接口的指定方法，将参数传入
             */
            ipubcapunimodeluccws.feeDataConfirm(sysheader, srvReqBody,
                    parametersResHeader, parametersResBody);
            /*
             * 1.8.4 将业务报文体的outputData赋值给返回值
             */
            result = parametersResBody.value.getBizBody().getOutputData();
            CommonHeaderDeal.setSYSHEADERTHREAD(parametersResHeader.value);
            CommonHeaderDeal.setBIZHEADERTHREAD(parametersResBody.value
                    .getBizHeader());
            /*
             * 1.8.5 添加调用后的日志输出
             */
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文体：" + DataSerialJSon.fromObject(parametersResBody));
            logger.info("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.info("webservice调用业务接口异常");
            dealStatus = "3";
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.info("找不到接口类");
            dealStatus = "3";
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.info("解析返回的报文的JSON字符串发生异常");
            dealStatus = "3";
        } finally {
            if (dealSwitch && parametersResBody.value == null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader, null,
                        null, DealTrigger.WEBSERVICE, dealNo, dealTime,
                        dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader,
                        parametersResBody.value.getBizHeader(),
                        parametersResBody.value.getBizBody(),
                        DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            }

        }
        return result;
    }

    /**
     * @Description: 实时代付交易提交方法
     * @param inputData 入參
     * @return 设定文件
     * @throws
     */
    public RealTimeIndemnityOutputVO payCommit(RealTimeIndemnityInputVO inputData) {
        Map<String, Object> applicationMap = ParaDefInitConst
                .getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        /*
         * 1.1 调用的webService的wsdl
         */
        String wsdl = Constants.SERVICEENVPARAMAP.get("ESB")
                + "P00002002655?wsdl";
//        wsdl = "http://**********:8111/services/P00002002655?wsdl";
        /*
         * 1.2 服务接口路径
         */
        String interfacePath = "com.nci.tunan.cap.imports.impl.realtimeindemnity.ws.IndemnityPayCommit";
        /*
         * 1.3 报文参数
         */
        Holder<SysHeader> parametersResHeader = new Holder<SysHeader>();
        Holder<SrvResBody> parametersResBody = new Holder<SrvResBody>();
        /*
         * 1.4 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        if (sysheader.getOrgCd() == null || "".equals(sysheader.getOrgCd())) {
            sysheader.setOrgCd("86");
        }
        sysheader.setServCd("P00002002655");
        sysheader.setSysCd("075");
        parametersResHeader.value = sysheader;
        /*
         * 1.5 定义系统报文体
         */
        SrvReqBody srvReqBody = new SrvReqBody();
        /*
         * 1.6 定义业务报文体
         */
        SrvReqBizBody bizBody = new SrvReqBizBody();
        String dealTime = DateUtilsEx.getTodayTime();
        String dealNo = "fms" + "_" + "indemnityPayCommit" + "_" + "payCommit";
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(inputData.getBizHeader());
        srvReqBody.getBizBody().setInputData(inputData.getInputData());
        /*
         * 1.7 添加调用前的日志输出
         */
        try {
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(sysheader));
            logger.info(" 消息id：" + sysheader.getMsgId() + "-技术报文体："
                    + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.info("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.8 返回参数
         */
        RealTimeIndemnityOutputVO result = new RealTimeIndemnityOutputVO();
        String dealStatus = "2";
        try {
            /*
             * 1.8.1 调用公共webservice工厂方法
             */
            JaxWsProxyFactoryBean soapFactory = ServiceCommonMethod
                    .CommonWebServiceFactory(wsdl, interfacePath);
            /*
             * 1.8.2 通过工厂创建接口实例
             */
            IndemnityPayCommit ipubcapunimodeluccws = (IndemnityPayCommit) soapFactory
                    .create();
            /*
             * 1.8.3 调用接口的指定方法，将参数传入
             */
            ipubcapunimodeluccws.payCommit(sysheader, srvReqBody,
                    parametersResHeader, parametersResBody);
            /*
             * 1.8.4 将业务报文体的outputData赋值给返回值
             */
            result.setOutputVO(parametersResBody.value.getBizBody()
                    .getOutputData());
            result.setBizHeader(parametersResBody.value.getBizHeader());
            /*
             * 1.8.5 添加调用后的日志输出
             */
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.info(" WebService返回结果的消息id：" + sysheader.getMsgId()
                    + "-技术报文体：" + DataSerialJSon.fromObject(parametersResBody));
            logger.info("webservice调用接口成功");
        } catch (BizException e) {
            e.printStackTrace();
            logger.info("webservice调用业务接口异常");
            dealStatus = "3";
        } catch (ClassNotFoundException e1) {
            e1.printStackTrace();
            logger.info("找不到接口类");
            dealStatus = "3";
        } catch (Exception e2) {
            e2.printStackTrace();
            logger.info("解析返回的报文的JSON字符串发生异常");
            dealStatus = "3";
        } finally {
            if (dealSwitch && parametersResBody.value == null) {
                logger.info("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysheader, null,
                        null, DealTrigger.WEBSERVICE, dealNo, dealTime,
                        dealStatus);
            } else if (dealSwitch && parametersResBody.value != null) {
                logger.info("开始记录交易响应日志");
            }

        }
        return result;
    }
    /**
     * 组装返回报文
     * @param lstBO 应收应付列表
     * @param otherInfo 应收应付bo对象
     * 非制返盘推送方法
     */
    @Override
    public void callbackSubSystem(List<PremArapBO> lstBO, PremArapBO otherInfo) {
        List<PremArapVO> nbCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> paCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> csCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> clmCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> uwCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> cipCallBackList = new ArrayList<PremArapVO>();
        PremArapVO vo;
        for (PremArapBO bo : lstBO) {
            vo = new PremArapVO();
            vo.setFeeStatus(otherInfo.getFeeStatus());
            if(!StringUtilsEx.isNullOrEmpty(otherInfo.getFundsRtnCode())){
                vo.setFundsRtnCode(otherInfo.getFundsRtnCode());
            }
            if(!StringUtilsEx.isNullOrEmpty(otherInfo.getPayrefNo())){
                vo.setPayrefNo(otherInfo.getPayrefNo());
            }
            if(null != otherInfo.getBankDealDate()){
                vo.setBankDealDate(otherInfo.getBankDealDate());
            }
            if(CodeCst.FEE_STATUS__FINISH.equals(otherInfo.getFeeStatus())){
                if(otherInfo.getFinishTime()!=null){
                    vo.setFinishTime(otherInfo.getFinishTime());
                }else{
                    vo.setFinishTime(bo.getFinishTime());
                }
            }
            vo.setUnitNumber(bo.getUnitNumber());
            vo.setDerivType(bo.getDerivType());
            vo.setDerivType(bo.getDerivType());
            vo.setFailTimes(bo.getFailTimes());
            vo.setBusinessCode(bo.getBusinessCode());
            vo.setPolicyCode(bo.getPolicyCode());
            vo.setApplyCode(bo.getApplyCode());
            vo.setPayMode(bo.getPayMode());
            vo.setBankAccount(bo.getBankAccount());
            vo.setBankCode(bo.getBankCode());
            vo.setBankUserName(bo.getBankUserName());
            vo.setBusinessType(bo.getBusinessType());
            vo.setCipBankCode(bo.getCipBankCode());
            if (CodeCst.DERIV_TYPE_PA.equals(bo.getDerivType())) {
                paCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_NBS.equals(bo.getDerivType()) && ("YHZQDL".equals(bo.getSubmitChannel()) || CodeCst.USER_NAME_BY_CIP.equals(bo.getInsertName()))) {
                cipCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_NBS.equals(bo.getDerivType()) && !"YHZQDL".equals(bo.getSubmitChannel()) && !CodeCst.USER_NAME_BY_CIP.equals(bo.getInsertName())) {
            	//处理业务公共契约收费确认
                if(StringUtils.isEmpty(vo.getApplyCode())){
                	vo.setApplyCode(bo.getBusinessCode());
                } //生产缺陷#31726:契约72-微信支付成功，不回传银行账号信息
            	if(CodeCst.PAY_MODE__WE_CHAT.equals(bo.getPayMode()) ){
                    vo.setBankAccount(null);
                }
                nbCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_UW.equals(bo.getDerivType())) {
                uwCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_CS.equals(bo.getDerivType())) {
                csCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_CLM.equals(bo.getDerivType())) {
                vo.setFeeStatusBy(new BigDecimal(AppUserContext.getCurrentUser().getUserId()) );
                clmCallBackList.add(vo);
            }
        }
        String rightFlag = "0";
        if (nbCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            List<PremArapVO> repeaPrem=toRepeat(nbCallBackList);//@invalid 去重
            data.setPremArapList(repeaPrem);
            FeeConfirmOutputData resultList = this.nbipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (paCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(paCallBackList);
            FeeConfirmOutputData resultList = this.paipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (csCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(csCallBackList);
            FeeConfirmOutputData resultList = this.csipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (clmCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(clmCallBackList);
            FeeConfirmOutputData resultList = this.clmipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (uwCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(uwCallBackList);
            FeeConfirmOutputData resultList = this.uwipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (cipCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(cipCallBackList);
            FeeConfirmOutputData resultList = this.cipipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
    }
    /**
     * 组装到账确认参数
     * @param lstMap 应收应付参数列表
     * 制返盘推送方法
     */
    @Override
    public List<FeeConfirmOutputVO> callbackSubSystem(Map<PremArapBO, List<PremArapBO>> lstMap) {
        List<FeeConfirmOutputVO> feeConfirmOutputVOList = new ArrayList<FeeConfirmOutputVO>();
        List<PremArapVO> nbCallBackList = new ArrayList<PremArapVO>();//@invalid 契约
        List<PremArapVO> paCallBackList = new ArrayList<PremArapVO>();//@invalid 保单
        List<PremArapVO> csCallBackList = new ArrayList<PremArapVO>();//@invalid 保全
        List<PremArapVO> clmCallBackList = new ArrayList<PremArapVO>();//@invalid 理赔
        List<PremArapVO> uwCallBackList = new ArrayList<PremArapVO>();//@invalid 核保
        List<PremArapVO> cipCallBackList = new ArrayList<PremArapVO>();//小契约
        PremArapVO vo;
        for(PremArapBO otherInfo :lstMap.keySet()){
            List<PremArapBO> lstBO = lstMap.get(otherInfo);
            for (PremArapBO bo : lstBO) {
                vo = new PremArapVO();
                vo.setFeeStatus(otherInfo.getFeeStatus());
                if(!StringUtilsEx.isNullOrEmpty(otherInfo.getFundsRtnCode())){
                    vo.setFundsRtnCode(otherInfo.getFundsRtnCode());
                }
                if(!StringUtilsEx.isNullOrEmpty(otherInfo.getPayrefNo())){
                    vo.setPayrefNo(otherInfo.getPayrefNo());
                }
                if(null != otherInfo.getBankDealDate()) {
                    vo.setBankDealDate(otherInfo.getBankDealDate());
                }
                if(CodeCst.FEE_STATUS__FINISH.equals(otherInfo.getFeeStatus())){
                    if(otherInfo.getFinishTime()!=null){
                        vo.setFinishTime(otherInfo.getFinishTime());
                    }else{
                        vo.setFinishTime(bo.getFinishTime());
                    }
                }
                vo.setUnitNumber(bo.getUnitNumber());
                vo.setDerivType(bo.getDerivType());
                vo.setDerivType(bo.getDerivType());
                vo.setBusinessCode(bo.getBusinessCode());
                vo.setPolicyCode(bo.getPolicyCode());
                if(!StringUtilsEx.isNullOrEmpty(bo.getApplyCode())) {
                	vo.setApplyCode(bo.getApplyCode());
                }
                vo.setPayMode(bo.getPayMode());//@invalid E宝通实时到账通知用
                vo.setBankAccount(bo.getBankAccount());
                vo.setBankCode(bo.getBankCode());
                vo.setBankUserName(bo.getBankUserName());
                vo.setBusinessType(bo.getBusinessType());
                vo.setFailTimes(bo.getFailTimes());
                vo.setFundsRtnCode(otherInfo.getFundsRtnCode());
                vo.setCipBankCode(bo.getCipBankCode());
                if (CodeCst.DERIV_TYPE_PA.equals(bo.getDerivType())) {
                    paCallBackList.add(vo);
                } else if (CodeCst.DERIV_TYPE_NBS.equals(bo.getDerivType())&& !CodeCst.USER_NAME_BY_CIP.equals(otherInfo.getInsertName()) && !CodeCst.USER_NAME_BY_CIP.equals(bo.getInsertName())) {
                    nbCallBackList.add(vo);
                }else if (CodeCst.DERIV_TYPE_NBS.equals(bo.getDerivType()) && (CodeCst.USER_NAME_BY_CIP.equals(otherInfo.getInsertName()) || CodeCst.USER_NAME_BY_CIP.equals(bo.getInsertName()))) {
                    cipCallBackList.add(vo);
                } else if (CodeCst.DERIV_TYPE_UW.equals(bo.getDerivType())) {
                    uwCallBackList.add(vo);
                } else if (CodeCst.DERIV_TYPE_CS.equals(bo.getDerivType())) {
                    csCallBackList.add(vo);
                } else if (CodeCst.DERIV_TYPE_CLM.equals(bo.getDerivType())) {
                    clmCallBackList.add(vo);
                }
            }
        }
        
        String rightFlag = "0";
        //@invalid 契约
        if (nbCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            List<PremArapVO> repeaPrem=toRepeat(nbCallBackList);//@invalid 去重
            data.setPremArapList(repeaPrem);
            FeeConfirmOutputData resultList = this.nbipubcapunimodeluccfeeDataConfirm(data);
            feeConfirmOutputVOList.addAll(resultList.getFeeConfirmOutputVOList());
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        //@invalid 保单
        if (paCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            List<PremArapVO> repeaPrem=toRepeat(paCallBackList);//@invalid 去重
            data.setPremArapList(repeaPrem);
            FeeConfirmOutputData resultList = this.paipubcapunimodeluccfeeDataConfirm(data);
            feeConfirmOutputVOList.addAll(resultList.getFeeConfirmOutputVOList());
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        //@invalid 保全
        if (csCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            List<PremArapVO> repeaPrem=toRepeat(csCallBackList);//@invalid 去重
            data.setPremArapList(repeaPrem);
            FeeConfirmOutputData resultList = this.csipubcapunimodeluccfeeDataConfirm(data);
            feeConfirmOutputVOList.addAll(resultList.getFeeConfirmOutputVOList());
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        //@invalid 理赔
        if (clmCallBackList.size() > 0) {
        	 FeeConfirmInputData data = new FeeConfirmInputData();
             List<PremArapVO> repeaPrem=toRepeat(clmCallBackList);//@invalid 去重
            data.setPremArapList(repeaPrem);
            FeeConfirmOutputData resultList = this.clmipubcapunimodeluccfeeDataConfirm(data);
            feeConfirmOutputVOList.addAll(resultList.getFeeConfirmOutputVOList());
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        //@invalid 核保
        if (uwCallBackList.size() > 0) {
        	FeeConfirmInputData data = new FeeConfirmInputData();
            List<PremArapVO> repeaPrem=toRepeat(uwCallBackList);//@invalid 核保去重
            data.setPremArapList(repeaPrem);
            FeeConfirmOutputData resultList = this.uwipubcapunimodeluccfeeDataConfirm(data);
            feeConfirmOutputVOList.addAll(resultList.getFeeConfirmOutputVOList());
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (cipCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(cipCallBackList);
            FeeConfirmOutputData resultList = this.cipipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        return feeConfirmOutputVOList;
    }

    /**
     * @description 收付费上海医保缴费结果回写契约接口
     * @param inputData 输入参数VO:com.nci.tunan.nb.interfaces.shhealthcare.vo.InputSHPaymentReqData
     * @return 输出的参数VO：com.nci.tunan.nb.interfaces.shhealthcare.vo.OutputSHPaymentResData
    */
    public com.nci.tunan.nb.interfaces.shhealthcare.vo.OutputSHPaymentResData nbSHPaymentResult(com.nci.tunan.nb.interfaces.shhealthcare.vo.InputSHPaymentReqData inputData) {
        String hsUrl = Constants.SERVICEENVPARAMAP.get("NB") + "remoting/NB_shPaymentResult";       
        /*
         * 1.1 报文参数
         */
        /*
         * 1.2 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.3 定义系统报文体
         */
        com.nci.tunan.nb.interfaces.shhealthcare.SrvReqBody srvReqBody = new com.nci.tunan.nb.interfaces.shhealthcare.SrvReqBody();
        /*
         * 1.4 定义业务报文体
         */
        com.nci.tunan.nb.interfaces.shhealthcare.SrvReqBizBody bizBody = new com.nci.tunan.nb.interfaces.shhealthcare.SrvReqBizBody();      
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        /*
         * 1.5 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.6 返回参数
         */
        com.nci.tunan.nb.interfaces.shhealthcare.vo.OutputSHPaymentResData result = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.nb.interfaces.shhealthcare.SrvResBody resultbody = new com.nci.tunan.nb.interfaces.shhealthcare.SrvResBody();
        try {
            /*
             * 1.6.1 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6.2 通过工厂创建接口实例
             */
            com.nci.tunan.nb.interfaces.shhealthcare.hs.InbSHPaymentResultUccHS inbshpaymentresultucchs = (com.nci.tunan.nb.interfaces.shhealthcare.hs.InbSHPaymentResultUccHS) factory.create(hsUrl);
            /*
             * 1.6.3 通过接口实例调用接口方法
             */
            tempresult = inbshpaymentresultucchs.updateTPremArapMedical(sysheader, srvReqBody);
            /*
             * 1.6.4 返回值赋值
             */
            resultbody = 
                    (com.nci.tunan.nb.interfaces.shhealthcare.SrvResBody)tempresult.getResponse();
            result = resultbody.getBizBody().getOutputData();
            /*
             * 1.6.5 添加调用后的日志输出
             */
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return result;
    }
    /**
     * 对List<PremArapVO> 对象根据RepeatPremArapVO17个字段去重,返回新对象
     * @param oldCallBackList 应收应付集合
     * @return
     */
    public List<PremArapVO> toRepeat(List<PremArapVO> oldCallBackList){
    	/*
    	 * 1.1 因为PremArapVO不是收付费vo，该vo属性数目不可控，故循环复制，组装RepeatPremArapVO对象
    	 */
    	Set<RepeatPremArapVO> reset=new HashSet<RepeatPremArapVO>();
    	for (PremArapVO premArapVO : oldCallBackList) {
    		RepeatPremArapVO revo=new RepeatPremArapVO();
    		
    		revo.setApplyCode(premArapVO.getApplyCode());
    		revo.setBankAccount(premArapVO.getBankAccount());
    		revo.setBankCode(premArapVO.getBankCode());
    		revo.setBankUserName(premArapVO.getBankUserName());
    		revo.setBusinessCode(premArapVO.getBusinessCode());
    		
    		revo.setBusinessType(premArapVO.getBusinessType());
    		revo.setFeeStatus(premArapVO.getFeeStatus());
    		revo.setFeeType(premArapVO.getFeeType());
    		revo.setFundsRtnCode(premArapVO.getFundsRtnCode());
    		revo.setPayMode(premArapVO.getPayMode());
    		
    		revo.setPolicyCode(premArapVO.getPolicyCode());
    		revo.setUnitNumber(premArapVO.getUnitNumber());
    		revo.setDerivType(premArapVO.getDerivType());
    		revo.setPayrefNo(premArapVO.getPayrefNo());
    		revo.setBankDealDate(premArapVO.getBankDealDate());
    		
    		revo.setFinishTime(premArapVO.getFinishTime());
    		revo.setFailTimes(premArapVO.getFailTimes());
    		revo.setCipBankCode(premArapVO.getCipBankCode());
    		reset.add(revo);
		}
    	List<PremArapVO> newRelist=new ArrayList<PremArapVO>();
    	/*
    	 * 1.2 循环去重后的set集合赋值给 PremArapVO 17个属性，不用复制属性字段方法防止后面业务公共新增字段未通知出错
    	 */
    	for (RepeatPremArapVO prem : reset) {
    		PremArapVO vo = new PremArapVO();
    		vo.setApplyCode(prem.getApplyCode());
    		vo.setBankAccount(prem.getBankAccount());
            vo.setBankCode(prem.getBankCode());
            vo.setBankUserName(prem.getBankUserName());
            vo.setBusinessCode(prem.getBusinessCode());
            
            vo.setBusinessType(prem.getBusinessType());
            vo.setFeeStatus(prem.getFeeStatus());
            vo.setFeeType(prem.getFeeType());
            vo.setFundsRtnCode(prem.getFundsRtnCode());
            vo.setPayMode(prem.getPayMode());
            
            vo.setPolicyCode(prem.getPolicyCode());
            vo.setDerivType(prem.getDerivType());
            vo.setUnitNumber(prem.getUnitNumber());
            vo.setPayrefNo(prem.getPayrefNo());
            vo.setBankDealDate(prem.getBankDealDate());
            
            vo.setFinishTime(prem.getFinishTime());
            vo.setFailTimes(prem.getFailTimes());
            vo.setCipBankCode(prem.getCipBankCode());
            newRelist.add(vo); 
		}
    	return newRelist;
    }

    /**
     * @description 根据客户ID查询客户9要素信息
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.cap.imports.ICAPServiceUtils#paCustomerInfo(java.lang.String)
     * @param customerId
     * @return 
    */
    @Override
    public PayeeVO paCustomerInfo(String customerId) {
        com.nci.tunan.pa.interfaces.customerinformation.exports.vo.QueryCustomerInformationReqDataVO inputData=
                new com.nci.tunan.pa.interfaces.customerinformation.exports.vo.QueryCustomerInformationReqDataVO();
        inputData.setCustomerId(customerId);
        String hsUrl = Constants.SERVICEENVPARAMAP.get("PA") + "remoting/PA_QueryCustomerInformationByNineElementUccImplqueryCustomerInformationByNineElementAddr";       
//        hsUrl = "http://**********:9080/ls/remoting/PA_QueryCustomerInformationByNineElementUccImplqueryCustomerInformationByNineElementAddr";
        
        /*
         * 1.1 报文参数
         */
        /*
         * 1.2 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.3 定义系统报文体
         */
        com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvReqBody srvReqBody = 
                new com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvReqBody();
        /*
         * 1.4 定义业务报文体
         */
        com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvReqBizBody bizBody = 
                new com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvReqBizBody();      
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(inputData);
        PayeeVO payee = new PayeeVO();
        /*
         * 1.5 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.6 返回参数
         */
        com.nci.tunan.pa.interfaces.customerinformation.exports.vo.QueryCustomerInformationResDataVO element = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvResBody resultbody = 
                new com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvResBody();
        try {
            /*
             * 1.6.1 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6.2 通过工厂创建接口实例
             */
            com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.hs.IQueryCustomerInformationByNineElementUccHS paCustomerInfohs = 
                    (com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.hs.IQueryCustomerInformationByNineElementUccHS) factory.create(hsUrl);
            /*
             * 1.6.3 通过接口实例调用接口方法
             */
            tempresult = paCustomerInfohs.queryCustomerInformationByNineElement(sysheader, srvReqBody);
            /*
             * 1.6.4 返回值赋值
             */
            resultbody = 
                    (com.nci.tunan.pa.interfaces.customerinformation.exports.iquerycustomerinformationbynineelementucc.querycustomerinformationbynineelement.SrvResBody)tempresult.getResponse();
            element = resultbody.getBizBody().getOutputData();
            
            com.nci.tunan.pa.interfaces.customerinformation.exports.vo.Result result = element.getCustomerinfo();
            
            payee.setPayeeName(result.getCustomerName());
            payee.setCountryCode(result.getCountryCode());
            payee.setJobCode(result.getJobCode());
            payee.setPayeeCertEndDate(DateUtilsEx.formatToDate(result.getCustCertEndDate(), "yyyy-MM-dd"));
            payee.setPayeeCertStarDate(DateUtilsEx.formatToDate(result.getCustCertStarDate(),"yyyy-MM-dd"));
            payee.setPayeeCertEndDateString(result.getCustCertEndDate());
            payee.setPayeeCertStarDateString(result.getCustCertStarDate());
            payee.setPayeeCertiCode(result.getCustomerCertiCode());
            payee.setPayeeCertType(result.getCustomerCertType());
            payee.setPayeeGender(Integer.valueOf(result.getGender()));
            payee.setPayeeBirthdayString(result.getCustomerBirthday());
            payee.setPayeeBirthday(DateUtilsEx.formatToDate(result.getCustomerBirthday(),"yyyy-MM-dd"));
            if(null != result.getMobileTel() && !"".equals(result.getMobileTel())){
                payee.setPhone(result.getMobileTel());
            } else {
                payee.setPhone(result.getFixedTel());
            }
            payee.setCity(result.getCityCode()); //市
            payee.setDistrict(result.getDistrictCode()); //区
            payee.setState(result.getStateCode()); //省
            payee.setAddress(result.getAddress()); //地址
            payee.setCityName(result.getCityName()); //市名称
            payee.setDistrictName(result.getDistrict()); //区名称
            payee.setJob(result.getJob()); //职业名称
            /*
             * 1.6.5 添加调用后的日志输出
             */
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return payee;
    }
    
    
    
    /**
     * @description 通知理赔修改支付方式接口
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.cap.imports.ICAPServiceUtils#claimChangePayMode(com.nci.tunan.clm.interfaces.vo.claimChangePayMode.ClaimChangePayModeReqVO clmvo)
     * @param clmvo
     * @return ClaimChangePayModeResVO
    */
    @Override
    public ClaimChangePayModeResVO claimChangePayMode(com.nci.tunan.clm.interfaces.vo.claimChangePayMode.ClaimChangePayModeReqVO clmvo) {
        String hsUrl = Constants.SERVICEENVPARAMAP.get("CLM") + "remoting/claimChangePayModeUCCClaimChangePayMode";       
//        hsUrl = "http://**********:9080/ls/remoting/claimChangePayModeUCCClaimChangePayMode";
        
        /*
         * 1.1 报文参数
         */
        /*
         * 1.2 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.3 定义系统报文体
         */
        com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBody srvReqBody = 
                new com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBody();
        /*
         * 1.4 定义业务报文体
         */
        com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBizBody bizBody = 
                new com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBizBody();      
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(clmvo);
        /*
         * 1.5 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.6 返回参数
         */
        ClaimChangePayModeResVO element = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvResBody resultbody = 
                new com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvResBody();
        try {
            /*
             * 1.6.1 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6.2 通过工厂创建接口实例
             */
            com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.hs.IClaimChangePayModeUCCHS claimChangePayModehs = 
                    (com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.hs.IClaimChangePayModeUCCHS) factory.create(hsUrl);
            /*
             * 1.6.3 通过接口实例调用接口方法
             */
            tempresult = claimChangePayModehs.changePayMode(sysheader, srvReqBody);
            /*
             * 1.6.4 返回值赋值
             */
            resultbody = 
                    (com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvResBody)tempresult.getResponse();
            element = resultbody.getBizBody().getOutputData();
            /*
             * 1.6.5 添加调用后的日志输出
             */
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return  element;
    }

    /**
     * @description 用投保单号调契约
     * @version
     * @title
     * <AUTHOR> @see 
     * @param clmvo
     * @return 
    */
    @Override
    public com.nci.tunan.nb.interfaces.queryqyserviceinformation.vo.OutputData nbReturn(com.nci.tunan.nb.interfaces.queryqyserviceinformation.vo.InputData applyCode) {
        String hsUrl = Constants.SERVICEENVPARAMAP.get("NB") + "remoting/NB_queryQYServiceInformationUccqueryQYServiceInformationAddr";       
   //     hsUrl = "http://**********:9080/ls/remoting/NB_queryQYServiceInformationUccqueryQYServiceInformationAddr";
        
        /*
         * 1.1 报文参数 
         */
        /*
         * 1.2 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.3 定义系统报文体
         */
        /*com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBody srvReqBody = 
                new com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBody();*/
        com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvReqBody srvReqBody =
                new com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvReqBody();
        /*
         * 1.4 定义业务报文体
         */
        /*com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBizBody bizBody = 
                new com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvReqBizBody();  */    
        com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvReqBizBody bizBody = 
                new com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvReqBizBody();    
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        
        //srvReqBody.getBizBody().getInputData().setApplyCode(applyCode);  
        srvReqBody.getBizBody().setInputData(applyCode);
        /*
         * 1.5 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.6 返回参数
         */
        //ClaimChangePayModeResVO element = null;
        com.nci.tunan.nb.interfaces.queryqyserviceinformation.vo.OutputData element = null;
        ServiceResult tempresult = new ServiceResult();
        /*com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvResBody resultbody = 
                new com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvResBody();*/
        com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvResBody resultbody = 
                new com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvResBody();
        try {
            /*
             * 1.6.1 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6.2 通过工厂创建接口实例
             */
            /*com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.hs.IClaimChangePayModeUCCHS claimChangePayModehs = 
                    (com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.hs.IClaimChangePayModeUCCHS) factory.create(hsUrl);*/
            com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.hs.IQueryQYServiceInformationUccHS nbReturnhs =
                    (com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.hs.IQueryQYServiceInformationUccHS) factory.create(hsUrl);
            /*
             * 1.6.3 通过接口实例调用接口方法
             */
            //tempresult = claimChangePayModehs.changePayMode(sysheader, srvReqBody);
            tempresult = nbReturnhs.queryQYServiceInformation(sysheader, srvReqBody);
            /*
             * 1.6.4 返回值赋值
             */
            /*resultbody = 
                    (com.nci.tunan.clm.interfaces.exports.hs.iclaimChangePayModeUCC.claimChangePayMode.SrvResBody)tempresult.getResponse();
            element = resultbody.getBizBody().getOutputData();*/
            resultbody = 
                    (com.nci.tunan.nb.interfaces.queryqyserviceinformation.iqueryqyserviceinformationucc.queryqyserviceinformation.SrvResBody)tempresult.getResponse();
            element = resultbody.getBizBody().getOutputData();
            /*
             * 1.6.5 添加调用后的日志输出
             */
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return  element;
    }
    
    /**
     * @description 投保单号查询契约投保单合同总保费
     * @version
     * @title
     * <AUTHOR> @see 
     * @param inputData 投保单号
     * @return 0:总保费未超额;1:总保费超额,投被保人为同一人;2总保费超额,投被保人非同一人;3:调用失败
    */
    @Override
    public String queryPolicyPremium(String applyCode) {
        String altFlag = "3";
        String hsUrl = Constants.SERVICEENVPARAMAP.get("NB") + "remoting/NB_queryPremAndRelationUccqueryPremAndRelationAddr";       
//        hsUrl = "http://**********:9080/ls/remoting/NB_queryPremAndRelationUccqueryPremAndRelationAddr";
        /*
         * 1.1 报文参数 
         */
        /*
         * 1.2 公共处理报文的函数，将报文头赋值
         */
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.3 定义系统报文体
         */
        com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvReqBody srvReqBody = new 
                com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvReqBody();
        /*
         * 1.4 定义业务报文体
         */
        com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvReqBizBody bizBody = new 
                com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvReqBizBody();
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        com.nci.tunan.nb.interfaces.queryPremAndRelation.vo.InputData inputData = new com.nci.tunan.nb.interfaces.queryPremAndRelation.vo.InputData();
        if(null != applyCode && !"".equals(applyCode)){
            inputData.setApplyCode(applyCode);
            srvReqBody.getBizBody().setInputData(inputData);
        } else {
            
        }
        /*
         * 1.5 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.6 返回参数
         */
        com.nci.tunan.nb.interfaces.queryPremAndRelation.vo.OutputData result = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvResBody resultbody = 
                new com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvResBody();
        try {
            /*
             * 1.6.1 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6.2 通过工厂创建接口实例
             */
            com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.hs.IQueryPremAndRelationUccHS iquerypremandrelationucchs = 
                    (com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.hs.IQueryPremAndRelationUccHS) factory.create(hsUrl);
            //通过接口实例调用接口方法
            tempresult = iquerypremandrelationucchs.queryPremAndRelation(sysheader, srvReqBody);
            //返回值赋值
            resultbody = 
                    (com.nci.tunan.nb.interfaces.queryPremAndRelation.exports.iquerypremandrelationucc.querypremandrelation.SrvResBody)tempresult.getResponse();
            result = resultbody.getBizBody().getOutputData();
            //添加调用后的日志输出
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-业务报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
            if (null != result && null != result.getFailReason() && !"".equals(result.getFailReason())) {
                if(result.getFailReason().contains("不能为空") || result.getFailReason().contains("异常")){
                    altFlag = "3"; // 投保单号不能为空,接口调用异常
                }
                if(result.getFailReason().contains("险种")){
                    altFlag = "4"; // 未查到该投保单的险种信息
                }
                if(result.getFailReason().contains("被保人")){
                    altFlag = "5"; // 未查到该投保单的被保人信息
                }
                return altFlag;
            } else if (null != result.getTotalPremAf() && null != result.getRelationToPh()) {
                if (result.getTotalPremAf().doubleValue() < 20000) {
                    altFlag = "0";
                    return altFlag;
                } else {
                    /*
                     * 投被保人关系Map<BigDecimal, String> relationToPh
                     * <被保人客户ID，投被保人关系> 见码表DEV_PAS.T_LA_PH_RELA
                     */
                    Collection<String> values = result.getRelationToPh().values();
                    if ((values.size() == 1 && values.contains("00"))) {
                        altFlag = "1";
                    } else {
                        altFlag = "2";
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return altFlag;
    }
    
    /**
     * @description 日结制表接口
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.cap.imports.ICAPServiceUtils#claimChangePayMode(com.nci.tunan.clm.interfaces.vo.claimChangePayMode.ClaimChangePayModeReqVO clmvo)
     * @param clmvo
     * @return ClaimChangePayModeResVO
    */
    @Override
    public boolean createStatementFileAndSap(com.nci.tunan.cap.interfaces.vo.report.StatementFileAndSapVO sapVO) {
        String hsUrl = Constants.SERVICEENVPARAMAP.get("CAP") + "remoting/CAP_statementFileUCCCreateStatementFileAndSapAddr";       
//        hsUrl = "http://**********:9080/ls/remoting/CAP_statementFileUCCCreateStatementFileAndSapAddr";
        
        /*
         * 1.1 报文参数
         */
        /*
         * 1.2 公共处理报文的函数，将报文头赋值
         */
        boolean isOK = false;
        SysHeader sysheader = ServiceInvokeParameterBean.sysHeaderInit();
        BizHeader reqhead = ServiceInvokeParameterBean.bizHeaderInit(sysheader);      
        /*
         * 1.3 定义系统报文体
         */
        com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvReqBody srvReqBody = 
                new com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvReqBody();
        /*
         * 1.4 定义业务报文体
         */
        com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvReqBizBody bizBody = 
                new com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvReqBizBody();      
        srvReqBody.setBizBody(bizBody);
        srvReqBody.setBizHeader(reqhead);
        srvReqBody.getBizBody().setInputData(sapVO);
        /*
         * 1.5 添加调用前的日志输出
         */
        try {
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(sysheader));
            logger.debug(" 消息id：" + sysheader.getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(srvReqBody));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析报文的JSON字符串发生异常");
        }
        /*
         * 1.6 返回参数
         */
        com.nci.tunan.cap.interfaces.vo.report.OutputData element = null;
        ServiceResult tempresult = new ServiceResult();
        com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvResBody resultbody = 
                new com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvResBody();
        try {
            /*
             * 1.6.1 创建hessian工厂
             */
            HessianProxyFactory factory = new HessianProxyFactory();
            /*
             * 1.6.2 通过工厂创建接口实例
             */
            com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.hs.IStatementFileUCCHS ucc = 
                    (com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.hs.IStatementFileUCCHS) factory.create(hsUrl);
            /*
             * 1.6.3 通过接口实例调用接口方法
             */
            tempresult = ucc.createStatementFileAndSap(sysheader, srvReqBody);
            /*
             * 1.6.4 返回值赋值
             */
            resultbody = 
                    (com.nci.tunan.cap.interfaces.report.exports.istatementfileucc.createstatementfileandsap.SrvResBody)tempresult.getResponse();
            element = resultbody.getBizBody().getOutputData();
            if("1".equals(element.getRtnCode())){
                isOK = true;
            }
            /*
             * 1.6.5 添加调用后的日志输出
             */
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文头："
                        + DataSerialJSon.fromObject(tempresult.getSysHeader()));
            logger.debug(" hessian返回结果的消息id：" + sysheader.getMsgId() + "-技术报文体："
                        + DataSerialJSon.fromObject(tempresult.getResponse()));
            logger.debug("hessian调用接口成功");
        } catch (Exception e) {
            e.printStackTrace();
            logger.debug("hessian调用业务接口异常");
        }
        return  isOK;
    }
    
    /**
     * 组装返回报文
     * @param lstBO 应收应付列表
     * @param otherInfo 应收应付bo对象
     */
    @Override
    public com.nci.core.common.interfaces.vo.system.feeconfirm.FeeConfirmOutputData callbackSubSystem1(List<PremArapBO> lstBO, PremArapBO otherInfo) {
        List<PremArapVO> nbCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> paCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> csCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> clmCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> uwCallBackList = new ArrayList<PremArapVO>();
        List<PremArapVO> cipCallBackList = new ArrayList<PremArapVO>();
        PremArapVO vo;
        FeeConfirmOutputData resultList = new FeeConfirmOutputData();
        for (PremArapBO bo : lstBO) {
            vo = new PremArapVO();
            vo.setFeeStatus(otherInfo.getFeeStatus());
            if(!StringUtilsEx.isNullOrEmpty(otherInfo.getFundsRtnCode())){
                vo.setFundsRtnCode(otherInfo.getFundsRtnCode());
            }
            if(!StringUtilsEx.isNullOrEmpty(otherInfo.getPayrefNo())){
                vo.setPayrefNo(otherInfo.getPayrefNo());
            }
            if(null != otherInfo.getBankDealDate()){
                vo.setBankDealDate(otherInfo.getBankDealDate());
            }
            if(CodeCst.FEE_STATUS__FINISH.equals(otherInfo.getFeeStatus())){
                if(otherInfo.getFinishTime()!=null){
                    vo.setFinishTime(otherInfo.getFinishTime());
                }else{
                    vo.setFinishTime(bo.getFinishTime());
                }
            }
            vo.setUnitNumber(bo.getUnitNumber());
            vo.setDerivType(bo.getDerivType());
            vo.setDerivType(bo.getDerivType());
            vo.setFailTimes(bo.getFailTimes());
            vo.setBusinessCode(bo.getBusinessCode());
            vo.setPolicyCode(bo.getPolicyCode());
            vo.setApplyCode(bo.getApplyCode());
            vo.setPayMode(bo.getPayMode());//E宝通实时到账通知用
            vo.setBankAccount(bo.getBankAccount());
            vo.setBankCode(bo.getBankCode());
            vo.setBankUserName(bo.getBankUserName());
            vo.setBusinessType(bo.getBusinessType());
            vo.setCipBankCode(bo.getCipBankCode());
            if (CodeCst.DERIV_TYPE_PA.equals(bo.getDerivType())) {
                paCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_NBS.equals(bo.getDerivType()) && ("YHZQDL".equals(bo.getSubmitChannel()) || CodeCst.USER_NAME_BY_CIP.equals(bo.getInsertName()))) {
                cipCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_NBS.equals(bo.getDerivType()) && !"YHZQDL".equals(bo.getSubmitChannel()) && !CodeCst.USER_NAME_BY_CIP.equals(bo.getInsertName())) {
            	//处理业务公共契约收费确认
                if(StringUtils.isEmpty(vo.getApplyCode())){
                	vo.setApplyCode(bo.getBusinessCode());
                } //生产缺陷#31726:契约72-微信支付成功，不回传银行账号信息
                if(CodeCst.PAY_MODE__WE_CHAT.equals(bo.getPayMode()) ){
                    vo.setBankAccount(null);
                }
                nbCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_UW.equals(bo.getDerivType())) {
                uwCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_CS.equals(bo.getDerivType())) {
                csCallBackList.add(vo);
            } else if (CodeCst.DERIV_TYPE_CLM.equals(bo.getDerivType())) {
                vo.setFeeStatusBy(new BigDecimal(AppUserContext.getCurrentUser().getUserId()) );
                clmCallBackList.add(vo);
            }
        }
        String rightFlag = "0";
        if (nbCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            List<PremArapVO> repeaPrem=toRepeat(nbCallBackList);//去重
            data.setPremArapList(repeaPrem);
            resultList = this.nbipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (paCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(paCallBackList);
            resultList = this.paipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (csCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(csCallBackList);
            resultList = this.csipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (clmCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(clmCallBackList);
            resultList = this.clmipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (uwCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(uwCallBackList);
            resultList = this.uwipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        if (cipCallBackList.size() > 0) {
            FeeConfirmInputData data = new FeeConfirmInputData();
            data.setPremArapList(cipCallBackList);
            resultList = this.cipipubcapunimodeluccfeeDataConfirm(data);
            for (FeeConfirmOutputVO resultVO : resultList.getFeeConfirmOutputVOList()) {
                if (!rightFlag.equals(resultVO.getResultFlag())) {
                    throw new BizException(resultVO.getReason());
                }
            }
        }
        return resultList;
    }
    
}
