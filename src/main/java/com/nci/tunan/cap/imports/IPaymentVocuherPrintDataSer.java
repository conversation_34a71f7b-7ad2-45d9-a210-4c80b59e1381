package com.nci.tunan.cap.imports;

import com.nci.tunan.cap.interfaces.model.vo.DocumentPrintClientVO;
import com.nci.udmp.framework.signature.IService;

/** 
 * @ClassName: IPaymentVocuherPrintDataSer 
 * @description 付赔款打印消费接口
 * <AUTHOR>
 * @date 2014年04月17日 下午6:02:28 
 * @.belongToModule 收付费-付费凭证打印确认接口
 *  
 */ 
public interface IPaymentVocuherPrintDataSer extends IService {

    /**
     * 调用打印系统的服务，进行预览、打印
     * 
     * @param ip        IP
     * @param userName  用户名
     * @param sendType  发送类型
     * @param jarPath   jar路径
     * @param xmlPath   XML路径
     * @param fileName  文件名
     * @param printType 打印类型
     * @return          Vo
     * @throws Exception 异常
     */
    public DocumentPrintClientVO voucherInfoSend(String ip, String userName, String sendType, String jarPath,
            String xmlPath, String fileName, String printType) throws Exception;
}
