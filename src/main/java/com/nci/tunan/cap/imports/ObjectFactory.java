package com.nci.tunan.cap.imports;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the sipservertype package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
/** 
 * @description E保通交易请求类
 * <AUTHOR>
 * @date 2015年12月17日 下午3:40:26 
 * @.belongToModule 收付费-E保通交易请求方法类
 */
@XmlRegistry
public class ObjectFactory {

    /**
     * @Fields URN_SIPSERVER_TYPE : URN_SIPSERVER_TYPE
     */
    private static final String URN_SIPSERVER_TYPE = "urn:sipserverType";
    private static final QName INFONAME_QNAME = new QName(URN_SIPSERVER_TYPE, "infoName");
    private static final QName OUTFILE_QNAME = new QName(URN_SIPSERVER_TYPE, "outFile");
    private static final QName SPLFILE_QNAME = new QName(URN_SIPSERVER_TYPE, "splFile");
    private static final QName PRINTER_QNAME = new QName(URN_SIPSERVER_TYPE, "printer");
    private static final QName INSTRING_QNAME = new QName(URN_SIPSERVER_TYPE, "inString");
    private static final QName FILETYPE_QNAME = new QName(URN_SIPSERVER_TYPE, "fileType");
    private static final QName ERROR_QNAME = new QName(URN_SIPSERVER_TYPE, "error");
    private static final QName SIPJT_QNAME = new QName(URN_SIPSERVER_TYPE, "sipJt");
    private static final QName OUTSTRING_QNAME = new QName(URN_SIPSERVER_TYPE, "outString");
    private static final QName INFOSTR_QNAME = new QName(URN_SIPSERVER_TYPE, "infoStr");
    private static final QName RETCODE_QNAME = new QName(URN_SIPSERVER_TYPE, "retCode");
    private static final QName FLAG_QNAME = new QName(URN_SIPSERVER_TYPE, "flag");
    private static final QName START_QNAME = new QName(URN_SIPSERVER_TYPE, "start");
    private static final QName INFILE_QNAME = new QName(URN_SIPSERVER_TYPE, "inFile");
    private static final QName ATTFORMAT_QNAME = new QName(URN_SIPSERVER_TYPE, "attFormat");
    private static final QName JOBNAME_QNAME = new QName(URN_SIPSERVER_TYPE, "jobName");
    private static final QName STAGE_QNAME = new QName(URN_SIPSERVER_TYPE, "stage");
    private static final QName VP_QNAME = new QName(URN_SIPSERVER_TYPE, "vp");
    private static final QName OBJFILE_QNAME = new QName(URN_SIPSERVER_TYPE, "objFile");
    private static final QName JOBID_QNAME = new QName(URN_SIPSERVER_TYPE, "jobId");
    private static final QName USERNAME_QNAME = new QName(URN_SIPSERVER_TYPE, "userName");
    private static final QName CLIENTIP_QNAME = new QName(URN_SIPSERVER_TYPE, "clientIP");
    private static final QName END_QNAME = new QName(URN_SIPSERVER_TYPE, "end");
    private static final QName OPERATION_QNAME = new QName(URN_SIPSERVER_TYPE, "operation");
    private static final QName OBJTYPE_QNAME = new QName(URN_SIPSERVER_TYPE, "objType");
    private static final QName JOBFILE_QNAME = new QName(URN_SIPSERVER_TYPE, "jobFile");
    private static final QName OBJNAME_QNAME = new QName(URN_SIPSERVER_TYPE, "objName");

    /**
     * Create a new ObjectFactory that can be used to create new instances of
     * schema derived classes for package: sipservertype
     * 
     */
    public ObjectFactory() {
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "infoName")
    public JAXBElement<String> createInfoName(String value) {
        return new JAXBElement<String>(INFONAME_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "outFile")
    public JAXBElement<byte[]> createOutFile(byte[] value) {
        return new JAXBElement<byte[]>(OUTFILE_QNAME, byte[].class, null, (byte[]) value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "splFile")
    public JAXBElement<byte[]> createSplFile(byte[] value) {
        return new JAXBElement<byte[]>(SPLFILE_QNAME, byte[].class, null, (byte[]) value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "printer")
    public JAXBElement<String> createPrinter(String value) {
        return new JAXBElement<String>(PRINTER_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "inString")
    public JAXBElement<String> createInString(String value) {
        return new JAXBElement<String>(INSTRING_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "fileType")
    public JAXBElement<String> createFileType(String value) {
        return new JAXBElement<String>(FILETYPE_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "error")
    public JAXBElement<String> createError(String value) {
        return new JAXBElement<String>(ERROR_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "sipJt")
    public JAXBElement<String> createSipJt(String value) {
        return new JAXBElement<String>(SIPJT_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "outString")
    public JAXBElement<String> createOutString(String value) {
        return new JAXBElement<String>(OUTSTRING_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "infoStr")
    public JAXBElement<String> createInfoStr(String value) {
        return new JAXBElement<String>(INFOSTR_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "retCode")
    public JAXBElement<Integer> createRetCode(Integer value) {
        return new JAXBElement<Integer>(RETCODE_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "flag")
    public JAXBElement<Integer> createFlag(Integer value) {
        return new JAXBElement<Integer>(FLAG_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "start")
    public JAXBElement<Integer> createStart(Integer value) {
        return new JAXBElement<Integer>(START_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "inFile")
    public JAXBElement<byte[]> createInFile(byte[] value) {
        return new JAXBElement<byte[]>(INFILE_QNAME, byte[].class, null, (byte[]) value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "attFormat")
    public JAXBElement<Integer> createAttFormat(Integer value) {
        return new JAXBElement<Integer>(ATTFORMAT_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "jobName")
    public JAXBElement<String> createJobName(String value) {
        return new JAXBElement<String>(JOBNAME_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "stage")
    public JAXBElement<Integer> createStage(Integer value) {
        return new JAXBElement<Integer>(STAGE_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "vp")
    public JAXBElement<String> createVp(String value) {
        return new JAXBElement<String>(VP_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "objFile")
    public JAXBElement<byte[]> createObjFile(byte[] value) {
        return new JAXBElement<byte[]>(OBJFILE_QNAME, byte[].class, null, (byte[]) value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "jobId")
    public JAXBElement<String> createJobId(String value) {
        return new JAXBElement<String>(JOBID_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "userName")
    public JAXBElement<String> createUserName(String value) {
        return new JAXBElement<String>(USERNAME_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "clientIP")
    public JAXBElement<String> createClientIP(String value) {
        return new JAXBElement<String>(CLIENTIP_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "end")
    public JAXBElement<Integer> createEnd(Integer value) {
        return new JAXBElement<Integer>(END_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "operation")
    public JAXBElement<Integer> createOperation(Integer value) {
        return new JAXBElement<Integer>(OPERATION_QNAME, Integer.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "objType")
    public JAXBElement<String> createObjType(String value) {
        return new JAXBElement<String>(OBJTYPE_QNAME, String.class, null, value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "jobFile")
    public JAXBElement<byte[]> createJobFile(byte[] value) {
        return new JAXBElement<byte[]>(JOBFILE_QNAME, byte[].class, null, (byte[]) value);
    }

    @XmlElementDecl(namespace = URN_SIPSERVER_TYPE, name = "objName")
    public JAXBElement<String> createObjName(String value) {
        return new JAXBElement<String>(OBJNAME_QNAME, String.class, null, value);
    }

}
