package com.nci.tunan.cap.imports.cn.com.fingard;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;

import com.nci.tunan.cap.util.PayrefConfigUtil;

/** 
 * @ClassName: BusNciXinDebit 
 * @description E保通实时收费接口提交请求方法类
 * <AUTHOR>
 * @date 2016年04月27日 下午14:22:19 
 * @.belongToModule 收付费-E保通实时收费接口提交请求方法类
 *  
 */ 
@WebServiceClient(name = "BusNciXinDebit", 
                  wsdlLocation = "http://**********:84/NCI/BusNciXinDebit.asmx?wsdl",
                  targetNamespace = "http://FinGard.com.cn/") 
public class BusNciXinDebit extends Service {

    /** 
     * 接口域名
     */ 
    public static final  QName SERVICE = new QName("http://FinGard.com.cn/", "BusNciXinDebit");
    /** 
     * 接口域名
     */ 
    public static final  QName BusNciXinDebitSoap12 = new QName("http://FinGard.com.cn/", "BusNciXinDebitSoap12");
    /** 
     * 接口域名
     */ 
    public static final  QName BusNciXinDebitSoap = new QName("http://FinGard.com.cn/", "BusNciXinDebitSoap");
    /** 
     * @description 获取接口地址方法 
     * @return  设定文件 
     * @throws 
     */
    public static URL getWsdlLocation(){
        
        URL url = null;
        String debitUrl =  PayrefConfigUtil
                .getValueByTypeCode(PayrefConfigUtil.TYPE__DEBIT, PayrefConfigUtil.TYPE__DEBIT_URL);
        try {
            url = new URL(debitUrl + "/NCI/BusNciXinDebit.asmx?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(BusNciXinDebit.class.getName()).log(java.util.logging.Level.INFO,
                    "Can not initialize the default wsdl from {0}"
                    , debitUrl + "/NCI/BusNciXinDebit.asmx?wsdl");
        }
        return url;
    }

    public BusNciXinDebit(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public BusNciXinDebit(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public BusNciXinDebit() {
        super(BusNciXinDebit.getWsdlLocation(), SERVICE);
    }
    
    // //This constructor requires JAX-WS API 2.2. You will need to endorse the
    // 2.2
    // //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS
    // 2.1
    // //compliant code instead.
    // public BusNciXinDebit(WebServiceFeature ... features) {
    // super(WSDL_LOCATION, SERVICE, features);
    // }
    //
    // //This constructor requires JAX-WS API 2.2. You will need to endorse the
    // 2.2
    // //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS
    // 2.1
    // //compliant code instead.
    // public BusNciXinDebit(URL wsdlLocation, WebServiceFeature ... features) {
    // super(wsdlLocation, SERVICE, features);
    // }
    //
    // //This constructor requires JAX-WS API 2.2. You will need to endorse the
    // 2.2
    // //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS
    // 2.1
    // //compliant code instead.
    // public BusNciXinDebit(URL wsdlLocation, QName serviceName,
    // WebServiceFeature ... features) {
    // super(wsdlLocation, serviceName, features);
    // }

    /**
     *
     * @return
     *     returns BusNciXinDebitSoap
     */
    @WebEndpoint(name = "BusNciXinDebitSoap12")
    public IBusNciXinDebitSoap getBusNciXinDebitSoap12() {
        return super.getPort(BusNciXinDebitSoap12, IBusNciXinDebitSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns BusNciXinDebitSoap
     */
    @WebEndpoint(name = "BusNciXinDebitSoap12")
    public IBusNciXinDebitSoap getBusNciXinDebitSoap12(WebServiceFeature... features) {
        return super.getPort(BusNciXinDebitSoap12, IBusNciXinDebitSoap.class, features);
    }
    /**
     *
     * @return
     *     returns BusNciXinDebitSoap
     */
    @WebEndpoint(name = "BusNciXinDebitSoap")
    public IBusNciXinDebitSoap getBusNciXinDebitSoap() {
        return super.getPort(BusNciXinDebitSoap, IBusNciXinDebitSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns BusNciXinDebitSoap
     */
    @WebEndpoint(name = "BusNciXinDebitSoap")
    public IBusNciXinDebitSoap getBusNciXinDebitSoap(WebServiceFeature... features) {
        return super.getPort(BusNciXinDebitSoap, IBusNciXinDebitSoap.class, features);
    }

}
