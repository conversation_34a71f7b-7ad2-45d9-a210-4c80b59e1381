package com.nci.tunan.cap.imports.com.fingard;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.0.0
 * 2022-11-15T15:26:02.319+08:00
 * Generated source version: 3.0.0
 * 
 */
@WebService(targetNamespace = "http://fingard.com/", name = "XHXSoap")
@XmlSeeAlso({ObjectFactory.class})
public interface XHXSoap {

    @WebResult(name = "PaymentSubmitResult", targetNamespace = "http://fingard.com/")
    @RequestWrapper(localName = "PaymentSubmit", targetNamespace = "http://fingard.com/", className = "com.fingard.PaymentSubmit")
    @WebMethod(operationName = "PaymentSubmit", action = "http://fingard.com/PaymentSubmit")
    @ResponseWrapper(localName = "PaymentSubmitResponse", targetNamespace = "http://fingard.com/", className = "com.fingard.PaymentSubmitResponse")
    public com.nci.tunan.cap.imports.com.fingard.ArrayOfString paymentSubmit(
        @WebParam(name = "pReqXml", targetNamespace = "http://fingard.com/")
        java.lang.String pReqXml,
        @WebParam(name = "pDigest", targetNamespace = "http://fingard.com/")
        java.lang.String pDigest
    );
}
