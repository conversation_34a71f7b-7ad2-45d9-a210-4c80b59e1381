package com.nci.tunan.cap.imports.cn.com.fingard;


/** 
 * @ClassName: IndemnityBizHeaderVO 
 * @description 实时代付与资金交易报文头
 * <AUTHOR>
 * @date 2017年12月11日 上午11:27:52 
 * @.belongToModule 收付费-实时代付 
 */ 
public class NonRealTimePaymentOutputHeader {

    /**
     * @Fields transSource : 交易来源 (ERP来源标识（系统代码简称，如XinHeXin01）)
     */
    private String TransSource;

    /**
     * @Fields transCode : 交易编码(固定值，1988代付，9488收款到账核对)
     */
    private String TransCode;

    /**
     * @Fields transDate : 交易日期  格式是yyyyMMdd
     */
    private String TransDate;

    /**
     * @Fields transTime : 交易时间   格式是HH24miss
     */
    private String TransTime;
/** 
* @Fields TransSeq : 交易流水号
*/ 
private String TransSeq;
    /**
     * @Fields RtnSeq : ATS流水号
     */
    private String RtnSeq;
    /** 
    * @Fields RtnCode : 交易返回码(success:处理成功;fail:处理失败;
    * exception:处理异常[备注：不能作为支付失败，需保持批次号流水号不变重新提交];duplicate:批次已存在)
    */ 
    private String RtnCode;
    /** 
    * @Fields RtnMsg : 交易返回描述
    */ 
    private String RtnMsg;
    
    
    public String getRtnCode() {
        return RtnCode;
    }

    public void setRtnCode(String rtnCode) {
        RtnCode = rtnCode;
    }

    public String getRtnMsg() {
        return RtnMsg;
    }

    public void setRtnMsg(String rtnMsg) {
        RtnMsg = rtnMsg;
    }

    public String getTransSource() {
        return TransSource;
    }

    public void setTransSource(String transSource) {
        TransSource = transSource;
    }

    public String getTransCode() {
        return TransCode;
    }

    public String getTransSeq() {
        return TransSeq;
    }

    public void setTransSeq(String transSeq) {
        TransSeq = transSeq;
    }

    public void setTransCode(String transCode) {
        TransCode = transCode;
    }

    public String getTransDate() {
        return TransDate;
    }

    public void setTransDate(String transDate) {
        TransDate = transDate;
    }

    public String getTransTime() {
        return TransTime;
    }

    public void setTransTime(String transTime) {
        TransTime = transTime;
    }

    public String getRtnSeq() {
        return RtnSeq;
    }

    public void setRtnSeq(String rtnSeq) {
        RtnSeq = rtnSeq;
    }

}
