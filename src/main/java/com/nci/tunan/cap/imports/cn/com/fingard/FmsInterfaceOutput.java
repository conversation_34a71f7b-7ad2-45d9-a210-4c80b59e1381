package com.nci.tunan.cap.imports.cn.com.fingard;

/**
 * @description E保通提交,返回报文 update
 * @<NAME_EMAIL>
 * @date 2016-2-29 下午5:16:49
 * @.belongToModule 收付费-E保通提交返回报文体
 */
public class FmsInterfaceOutput {
    /**
     * 返回代码：本响应码为处理的最终响应码。成功-0000
     */
    private String ReturnCode;
    /**
     * 返回信息：成功-交易成功；失败-具体失败原因
     */
    private String ReturnMsg;
    /**
     * 账号：银行卡或存折账号
     */
    private String AccountNo;
    /**
     * 账户名：银行卡或存折上的所有人姓名
     */
    private String AccountName;
    /**
     * 账户类型：00-不详；01-存折；02-借记卡；03-信用卡（商户判断并返回）
     */
    private String AccountType;
    /**
     * 扣款成功的日期 格式：2011-05-31
     */
    private String EnterAccDate;
    /**
     * 扣款成功的时间 格式：17:53:10
     */
    private String EnterAccTime;
    /**
     * 每笔交易的唯一批次号
     */
    private String BatchID;
    /**
     * 批次内流水号，服务商自定义，如没有默认0
     */
    private String TradeSN;
    /**
     * 交易状态条件, 1成功,2失败,3 支付中
     */
    private String Status;
    /**
     * 收方银行代码:资金系统通过哪个银行进行代收的
     */
    private String CorpBankCode;
    /**
     * 收方银行名称：资金系统通过哪个银行进行代收的
     */
    private String CorpBankName;
    /**
     * 收方账号:资金系统通过哪个账号代收的
     */
    private String CorpAccount;
    /**
     * 数据来源：来源于哪个系统的数据（需要定义具体的值）
     */
    private String TransSource;
    /**
     * 管理机构:该交易属于哪个机构的，6位，如：862100
     */
    private String ManageCom;
    /**
     * 输出备用字段1
     */
    private String OutReserved1;
    /**
     * 输出备用字段2
     */
    private String OutReserved2;
    /**
     * 输出备用字段3
     */
    private String OutReserved3;
    /**
     * 服务商代码
     */
    private String ServicerCode;
    /**
     * 商户代码：服务商分配给新华的商户代码；总公司一个代码，每个分公司各一个
     */
    private String MerchantID;
    /**
     * 投保单号码：投保单号或保单号
     */
    private String ProposalFormNo;
    /**
     * 银行代码
     */
    private String BankCode;
    /**
     * 货币类型:人民币：CNY, 港元：HKD，美元：USD。默认为人民币
     */
    private String Currency;
    /**
     * 金额：保留两位小数
     */
    private String Premium;
    /**
     * 用户编号:默认为空
     */
    private String UserCode;
    /**
     * 自定义用户号：商户自定义的用户号， 用户号要唯一
     */
    private String CustUserID;
    /**
     * 账户类型:00-不详；01-存折；02-借记卡；03-信用卡
     */
    private String Remark;

    /**
     * 对账码
     */
    private String Abstract;

    /**
     * 交易流水号
     */
    private String NoteCode;

    public String getNoteCode() {
        return NoteCode;
    }

    public void setNoteCode(String noteCode) {
        NoteCode = noteCode;
    }

    public String getAbstract() {
        return Abstract;
    }

    public void setAbstract(String Abstract) {
        this.Abstract = Abstract;
    }

    public String getReturnCode() {
        return ReturnCode;
    }

    public void setReturnCode(String returnCode) {
        ReturnCode = returnCode;
    }

    public String getReturnMsg() {
        return ReturnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        ReturnMsg = returnMsg;
    }

    public String getAccountNo() {
        return AccountNo;
    }

    public void setAccountNo(String accountNo) {
        AccountNo = accountNo;
    }

    public String getAccountName() {
        return AccountName;
    }

    public void setAccountName(String accountName) {
        AccountName = accountName;
    }

    public String getAccountType() {
        return AccountType;
    }

    public void setAccountType(String accountType) {
        AccountType = accountType;
    }

    public String getEnterAccDate() {
        return EnterAccDate;
    }

    public void setEnterAccDate(String enterAccDate) {
        EnterAccDate = enterAccDate;
    }

    public String getEnterAccTime() {
        return EnterAccTime;
    }

    public void setEnterAccTime(String enterAccTime) {
        EnterAccTime = enterAccTime;
    }

    public String getBatchID() {
        return BatchID;
    }

    public void setBatchID(String batchID) {
        BatchID = batchID;
    }

    public String getTradeSN() {
        return TradeSN;
    }

    public void setTradeSN(String tradeSN) {
        TradeSN = tradeSN;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getCorpBankCode() {
        return CorpBankCode;
    }

    public void setCorpBankCode(String corpBankCode) {
        CorpBankCode = corpBankCode;
    }

    public String getCorpBankName() {
        return CorpBankName;
    }

    public void setCorpBankName(String corpBankName) {
        CorpBankName = corpBankName;
    }

    public String getCorpAccount() {
        return CorpAccount;
    }

    public void setCorpAccount(String corpAccount) {
        CorpAccount = corpAccount;
    }

    public String getTransSource() {
        return TransSource;
    }

    public void setTransSource(String transSource) {
        TransSource = transSource;
    }

    public String getManageCom() {
        return ManageCom;
    }

    public void setManageCom(String manageCom) {
        ManageCom = manageCom;
    }

    public String getOutReserved1() {
        return OutReserved1;
    }

    public void setOutReserved1(String outReserved1) {
        OutReserved1 = outReserved1;
    }

    public String getOutReserved2() {
        return OutReserved2;
    }

    public void setOutReserved2(String outReserved2) {
        OutReserved2 = outReserved2;
    }

    public String getOutReserved3() {
        return OutReserved3;
    }

    public void setOutReserved3(String outReserved3) {
        OutReserved3 = outReserved3;
    }

    public String getServicerCode() {
        return ServicerCode;
    }

    public void setServicerCode(String servicerCode) {
        ServicerCode = servicerCode;
    }

    public String getMerchantID() {
        return MerchantID;
    }

    public void setMerchantID(String merchantID) {
        MerchantID = merchantID;
    }

    public String getProposalFormNo() {
        return ProposalFormNo;
    }

    public void setProposalFormNo(String proposalFormNo) {
        ProposalFormNo = proposalFormNo;
    }

    public String getBankCode() {
        return BankCode;
    }

    public void setBankCode(String bankCode) {
        BankCode = bankCode;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String currency) {
        Currency = currency;
    }

    public String getPremium() {
        return Premium;
    }

    public void setPremium(String premium) {
        Premium = premium;
    }

    public String getUserCode() {
        return UserCode;
    }

    public void setUserCode(String userCode) {
        UserCode = userCode;
    }

    public String getCustUserID() {
        return CustUserID;
    }

    public void setCustUserID(String custUserID) {
        CustUserID = custUserID;
    }

    public String getRemark() {
        return Remark;
    }

    public void setRemark(String remark) {
        Remark = remark;
    }

}
