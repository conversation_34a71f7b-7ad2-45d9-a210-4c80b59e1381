package com.nci.tunan.cap.imports.cn.com.fingard;

import com.thoughtworks.xstream.annotations.XStreamAlias;
@XStreamAlias("ATS")
public class RTATS {

    /** 
    * @Fields serialVersionUID : 版本号
    */ 
    
    private NonRealTimePaymentOutputHeader PUB;
    private NonRealTimePaymentOUT OUT;


    public NonRealTimePaymentOutputHeader getPUB() {
        return PUB;
    }

    public void setPUB(NonRealTimePaymentOutputHeader pUB) {
        PUB = pUB;
    }

    public NonRealTimePaymentOUT getOUT() {
        return OUT;
    }

    public void setOUT(NonRealTimePaymentOUT oUT) {
        OUT = oUT;
    }

}
