
package com.nci.tunan.cap.imports;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/** 
 * @description 交易报文头
 * @<NAME_EMAIL> 
 * @date 2015-12-17 上午10:42:28 
 * @.belongToModule 收付费-收付费-打印接口
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SysMsgHeader", namespace = "http://www.newchinalife.com/common/header/in", propOrder = {
    "msgId",
    "msgDate",
    "msgTime",
    "servCd",
    "sysCd",
    "bizId",
    "bizType",
    "orgCd",
    "resCd",
    "resText",
    "bizResCd",
    "bizResText",
    "ver"
})
public class SysMsgHeader {

    @XmlElement(required = true)
    protected String msgId;
    @XmlElement(required = true)
    protected String msgDate;
    @XmlElement(required = true)
    protected String msgTime;
    @XmlElement(required = true)
    protected String servCd;
    @XmlElement(required = true)
    protected String sysCd;
    @XmlElement(required = true)
    protected String bizId;
    @XmlElement(required = true)
    protected String bizType;
    @XmlElement(required = true)
    protected String orgCd;
    @XmlElement(required = true)
    protected String resCd;
    @XmlElement(required = true)
    protected String resText;
    @XmlElement(required = true)
    protected String bizResCd;
    @XmlElement(required = true)
    protected String bizResText;
    @XmlElement(required = true)
    protected String ver;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String value) {
        this.msgId = value;
    }

    public String getMsgDate() {
        return msgDate;
    }

    public void setMsgDate(String value) {
        this.msgDate = value;
    }

    public String getMsgTime() {
        return msgTime;
    }

    public void setMsgTime(String value) {
        this.msgTime = value;
    }

    public String getServCd() {
        return servCd;
    }

    public void setServCd(String value) {
        this.servCd = value;
    }

    public String getSysCd() {
        return sysCd;
    }

    public void setSysCd(String value) {
        this.sysCd = value;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String value) {
        this.bizId = value;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String value) {
        this.bizType = value;
    }

    public String getOrgCd() {
        return orgCd;
    }

    public void setOrgCd(String value) {
        this.orgCd = value;
    }

    public String getResCd() {
        return resCd;
    }

    public void setResCd(String value) {
        this.resCd = value;
    }

    public String getResText() {
        return resText;
    }

    public void setResText(String value) {
        this.resText = value;
    }

    public String getBizResCd() {
        return bizResCd;
    }

    public void setBizResCd(String value) {
        this.bizResCd = value;
    }

    public String getBizResText() {
        return bizResText;
    }

    public void setBizResText(String value) {
        this.bizResText = value;
    }

    public String getVer() {
        return ver;
    }

    public void setVer(String value) {
        this.ver = value;
    }

}
