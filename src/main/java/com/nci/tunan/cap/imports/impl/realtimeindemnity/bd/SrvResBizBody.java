package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/** 
 * @ClassName: SrvResBizBody 
 * @description: 实时代付与资金交易报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:31:18 
 * @.belongToModule 收付费-实时代付 
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    private IndemnityOutputResultVO outputData;

    public IndemnityOutputResultVO getOutputData() {
        return outputData;
    }
    public void setOutputData(IndemnityOutputResultVO outputData) {
        this.outputData = outputData;
    }
}
