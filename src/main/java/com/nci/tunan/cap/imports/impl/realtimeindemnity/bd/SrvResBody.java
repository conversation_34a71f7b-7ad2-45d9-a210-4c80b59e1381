package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;
import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.tunan.cap.imports.impl.realtimeindemnity.hd.IndemnityOutputBizHeaderVO;

/** 
 * @ClassName: SrvResBody 
 * @description  实时代付与资金交易报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:31:41 
 * @.belongToModule 收付费-实时代付 
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBody", propOrder = { "bizHeader", "bizBody" })
public class SrvResBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(required = true)
    private IndemnityOutputBizHeaderVO  bizHeader;
    
    @XmlElement(required = true)
    private SrvResBizBody bizBody;
    
    public  IndemnityOutputBizHeaderVO  getBizHeader() {
        return bizHeader;
    }
    public void setBizHeader(IndemnityOutputBizHeaderVO  bizHeader) {
        this.bizHeader = bizHeader;
    }
    public SrvResBizBody getBizBody() {
        return bizBody;
    }
    public void setBizBody(SrvResBizBody bizBody) {
        this.bizBody = bizBody;
    }
}
