package com.nci.tunan.cap.imports;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * 
 * @description 打印接口类
 * @<NAME_EMAIL> 
 * @date 2015-9-18 下午6:37:10
 * @.belongToModule 收付费-打印接口类
 */
@WebService(targetNamespace = "urn:sipserverType", name = "sipserverPortType")
@XmlSeeAlso({ ObjectFactory.class })
@SOAPBinding(style = SOAPBinding.Style.RPC)
public interface ISipserverPortType {

    /**
     * 
     * @description Service definition of function sip__previewMeta
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP IP
     * @param userName 用户名
     * @param jobId     jobId
     * @param retCode   retCode
     * @param error     error
     */
    @WebMethod
    public void previewMeta(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "jobId", name = "jobId") java.lang.String jobId,
            @WebParam(partName = "retCode", mode = WebParam.Mode.OUT, name = "retCode") javax.xml.ws.Holder<java.lang.Integer> retCode,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__putJobFile
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP IP
     * @param userName  用户名
     * @param jobId     jobId
     * @param fileType  文件类型
     * @param jobFile   jobFile
     * @param retCode   retCode
     * @param error     error
     */
    @WebMethod
    public void putJobFile(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "jobId", name = "jobId") java.lang.String jobId,
            @WebParam(partName = "fileType", name = "fileType") java.lang.String fileType,
            @WebParam(partName = "jobFile", name = "jobFile") byte[] jobFile,
            @WebParam(partName = "retCode", mode = WebParam.Mode.OUT, name = "retCode") javax.xml.ws.Holder<java.lang.Integer> retCode,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__echoString
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param inString inString
     * @return inString
     */
    @WebResult(name = "outString", targetNamespace = "urn:sipserverType", partName = "outString")
    @WebMethod
    public java.lang.String echoString(@WebParam(partName = "inString", name = "inString") java.lang.String inString);

    /**
     * 
     * @description Service definition of function sip__getJobFile
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP IP
     * @param userName userName
     * @param jobId     jobId
     * @param fileType  fileType
     * @param attFormat attFormat
     * @param jobFile   jobFile
     * @param error     error
     */
    @WebMethod
    public void getJobFile(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "jobId", name = "jobId") java.lang.String jobId,
            @WebParam(partName = "fileType", name = "fileType") java.lang.String fileType,
            @WebParam(partName = "attFormat", name = "attFormat") int attFormat,
            @WebParam(partName = "jobFile", mode = WebParam.Mode.OUT, name = "jobFile") javax.xml.ws.Holder<byte[]> jobFile,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__setConfig
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param objName objName
     * @param objType objType
     * @param operation operation
     * @param objFile objFile
     * @param retCode retCode
     * @param error  error
     */
    @WebMethod
    public void setConfig(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "objName", name = "objName") java.lang.String objName,
            @WebParam(partName = "objType", name = "objType") java.lang.String objType,
            @WebParam(partName = "operation", name = "operation") int operation,
            @WebParam(partName = "objFile", name = "objFile") byte[] objFile,
            @WebParam(partName = "retCode", mode = WebParam.Mode.OUT, name = "retCode") javax.xml.ws.Holder<java.lang.Integer> retCode,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__getJobInfo
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param jobId jobId
     * @param infoName infoName
     * @param infoStr infoStr
     * @param error error
     */
    @WebMethod
    public void getJobInfo(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "jobId", name = "jobId") java.lang.String jobId,
            @WebParam(partName = "infoName", name = "infoName") java.lang.String infoName,
            @WebParam(partName = "infoStr", mode = WebParam.Mode.OUT, name = "infoStr") javax.xml.ws.Holder<java.lang.String> infoStr,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__getConfig
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param objName objName
     * @param objType objType
     * @param attFormat attFormat
     * @param objFile objFile
     * @param error error
     */
    @WebMethod
    public void getConfig(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "objName", name = "objName") java.lang.String objName,
            @WebParam(partName = "objType", name = "objType") java.lang.String objType,
            @WebParam(partName = "attFormat", name = "attFormat") int attFormat,
            @WebParam(partName = "objFile", mode = WebParam.Mode.OUT, name = "objFile") javax.xml.ws.Holder<byte[]> objFile,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__printMeta
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param jobId jobId
     * @param printer printer
     * @param flag flag
     * @param start start
     * @param end end
     * @param retCode retCode
     * @param error error
     */
    @WebMethod
    public void printMeta(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "jobId", name = "jobId") java.lang.String jobId,
            @WebParam(partName = "printer", name = "printer") java.lang.String printer,
            @WebParam(partName = "flag", name = "flag") int flag,
            @WebParam(partName = "start", name = "start") int start,
            @WebParam(partName = "end", name = "end") int end,
            @WebParam(partName = "retCode", mode = WebParam.Mode.OUT, name = "retCode") javax.xml.ws.Holder<java.lang.Integer> retCode,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__issueSipJt
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param vp vp
     * @param jobName jobName
     * @param sipJt sipJt
     * @param stage stage
     * @param jobId jobId
     * @param error error
     */
    @WebMethod
    public void issueSipJt(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "vp", name = "vp") java.lang.String vp,
            @WebParam(partName = "jobName", name = "jobName") java.lang.String jobName,
            @WebParam(partName = "sipJt", name = "sipJt") java.lang.String sipJt,
            @WebParam(partName = "stage", name = "stage") int stage,
            @WebParam(partName = "jobId", mode = WebParam.Mode.OUT, name = "jobId") javax.xml.ws.Holder<java.lang.String> jobId,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__openJobFile
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param jobId jobId
     * @param fileType fileType
     * @param retCode retCode
     * @param error error
     */
    @WebMethod
    public void openJobFile(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "jobId", name = "jobId") java.lang.String jobId,
            @WebParam(partName = "fileType", name = "fileType") java.lang.String fileType,
            @WebParam(partName = "retCode", mode = WebParam.Mode.OUT, name = "retCode") javax.xml.ws.Holder<java.lang.Integer> retCode,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);

    /**
     * 
     * @description Service definition of function sip__echoAttachment
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param inFile inFile
     * @return out
     */
    @WebResult(name = "outFile", targetNamespace = "urn:sipserverType", partName = "outFile")
    @WebMethod
    public byte[] echoAttachment(@WebParam(partName = "inFile", name = "inFile") byte[] inFile);

    /**
     * 
     * @description Service definition of function sip__sendJob
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param clientIP clientIP
     * @param userName userName
     * @param vp vp
     * @param jobName jobName
     * @param splFile splFile
     * @param stage stage
     * @param jobId jobId
     * @param error error
     */
    @WebMethod
    public void sendJob(
            @WebParam(partName = "clientIP", name = "clientIP") java.lang.String clientIP,
            @WebParam(partName = "userName", name = "userName") java.lang.String userName,
            @WebParam(partName = "vp", name = "vp") java.lang.String vp,
            @WebParam(partName = "jobName", name = "jobName") java.lang.String jobName,
            @WebParam(partName = "splFile", name = "splFile") byte[] splFile,
            @WebParam(partName = "stage", name = "stage") int stage,
            @WebParam(partName = "jobId", mode = WebParam.Mode.OUT, name = "jobId") javax.xml.ws.Holder<java.lang.String> jobId,
            @WebParam(partName = "error", mode = WebParam.Mode.OUT, name = "error") javax.xml.ws.Holder<java.lang.String> error);
}
