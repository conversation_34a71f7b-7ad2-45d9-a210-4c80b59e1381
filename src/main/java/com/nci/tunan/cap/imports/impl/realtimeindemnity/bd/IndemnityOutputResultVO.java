package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import javax.xml.bind.annotation.XmlElement;

import com.nci.udmp.framework.model.BaseVO;

/**
 * @ClassName: IndemnityOutputResultVO
 * @description: 实时代付与资金交易响应报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:28:47
 * @.belongToModule 收付费-实时代付
 */
public class IndemnityOutputResultVO extends BaseVO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */
    private static final long serialVersionUID = 4435204038085533941L;

    /**
     * @Fields noteCode : 交易流水号，用于EAST报送
     */
    private String noteCode;

    /**
     * @Fields rdSeq : 交易指令流水号(全局唯一，同请求报文的交易指令流水号)
     */
    private String rdSeq;

    /**
     * @Fields corpAct : 企业方账户 (新华付款账户)
     */
    private String corpAct;

    /**
     * @Fields corpEntity : 企业方账户所在机构(新华付款账户所在机构)
     */
    private String corpEntity;

    /**
     * @Fields corpBank : 企业方账户所属银行(新华付款账户所在银行)
     */
    private String corpBank;

    /**
     * @Fields oppAct : 交易方账户(收款人账户)
     */
    private String oppAct;

    /**
     * @Fields transState : 支付状态(支付状态 1：未支付 2：成功 3：失败 4：支付中 5：未知)
     */
    private String transState;

    /**
     * @Fields payInfoCode : 支付信息码(参考附录 统一支付信息码)
     */
    private String payInfoCode;

    /**
     * @Fields payInfo : 支付信息描述(其他错误,户名不符,格式错误,主动作废等编码及名称)
     */
    private String payInfo;

    /**
     * @Fields payMadeDate : 银行反馈时间(yyyyMMddHHmmss（以该日期为到账日期）)
     */
    private String payMadeDate;

    /**
     * @Fields Abstract : 对账码
     */
    private String Abstract;

    /**
     * @Fields serviceCode : 服务商代码(例：SHTL-上海通联)
     */
    private String serviceCode;

    /**
     * @Fields reqReserved1 : 保留字段
     */
    private String reqReserved1;

    /**
     * @Fields reqReserved2 : 保留字段
     */
    private String reqReserved2;

    @Override
    public String getBizId() {
        return null;
    }

    @XmlElement(name = "RdSeq")
    public String getRdSeq() {
        return rdSeq;
    }

    public void setRdSeq(String rdSeq) {
        this.rdSeq = rdSeq;
    }

    @XmlElement(name = "CorpAct")
    public String getCorpAct() {
        return corpAct;
    }

    public void setCorpAct(String corpAct) {
        this.corpAct = corpAct;
    }

    @XmlElement(name = "CorpEntity")
    public String getCorpEntity() {
        return corpEntity;
    }

    public void setCorpEntity(String corpEntity) {
        this.corpEntity = corpEntity;
    }

    @XmlElement(name = "CorpBank")
    public String getCorpBank() {
        return corpBank;
    }

    public void setCorpBank(String corpBank) {
        this.corpBank = corpBank;
    }

    @XmlElement(name = "OppAct")
    public String getOppAct() {
        return oppAct;
    }

    public void setOppAct(String oppAct) {
        this.oppAct = oppAct;
    }

    @XmlElement(name = "TransState")
    public String getTransState() {
        return transState;
    }

    public void setTransState(String transState) {
        this.transState = transState;
    }

    @XmlElement(name = "PayInfoCode")
    public String getPayInfoCode() {
        return payInfoCode;
    }

    public void setPayInfoCode(String payInfoCode) {
        this.payInfoCode = payInfoCode;
    }

    @XmlElement(name = "PayInfo")
    public String getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(String payInfo) {
        this.payInfo = payInfo;
    }

    @XmlElement(name = "PayMadeDate")
    public String getPayMadeDate() {
        return payMadeDate;
    }

    public void setPayMadeDate(String payMadeDate) {
        this.payMadeDate = payMadeDate;
    }

    @XmlElement(name = "Abstract")
    public String getAbstract() {
        return Abstract;
    }

    public void setAbstract(String abstract1) {
        Abstract = abstract1;
    }

    @XmlElement(name = "ServiceCode")
    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    @XmlElement(name = "ReqReserved1")
    public String getReqReserved1() {
        return reqReserved1;
    }

    public void setReqReserved1(String reqReserved1) {
        this.reqReserved1 = reqReserved1;
    }

    @XmlElement(name = "ReqReserved2")
    public String getReqReserved2() {
        return reqReserved2;
    }

    public void setReqReserved2(String reqReserved2) {
        this.reqReserved2 = reqReserved2;
    }

    @XmlElement(name = "NoteCode")
    public String getNoteCode() {
        return noteCode;
    }

    public void setNoteCode(String noteCode) {
        this.noteCode = noteCode;
    }

}
