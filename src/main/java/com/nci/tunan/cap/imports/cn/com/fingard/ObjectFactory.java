
package com.nci.tunan.cap.imports.cn.com.fingard;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the cn.com.fingard package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
/** 
 * @description E保通交易请求类
 * <AUTHOR>
 * @date 2015年12月17日 下午3:40:26 
 * @.belongToModule 收付费-E保通交易请求方法类
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: cn.com.fingard
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link 
     *  SubmitResponse }
     * 
     */
    public SubmitResponse createSubmitResponse() {
        return new SubmitResponse();
    }

    /**
     * Create an instance of {@link 
     *  Submit }
     * 
     */
    public Submit createSubmit() {
        return new Submit();
    }

}
