
package com.nci.tunan.cap.imports.cn.com.fingard;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/** 
 * @ClassName: SubmitResponse 
 * @description E保通交易请求类
 * <AUTHOR>
 * @date 2015年12月17日 下午3:40:26 
 * @.belongToModule 收付费-E保通交易请求方法类
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "pStrReqData"
})
@XmlRootElement(name = "Submit")
public class Submit {

    /** 
     * 提交请求方法入参
     */ 
    @XmlElement(name = "p_StrReqData")
    protected String pStrReqData;

    public String getPStrReqData() {
        return pStrReqData;
    }

    public void setPStrReqData(String value) {
        this.pStrReqData = value;
    }

}
