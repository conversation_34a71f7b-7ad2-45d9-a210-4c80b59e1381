package com.nci.tunan.cap.imports.cn.com.fingard;

import java.math.BigDecimal;
import java.util.Date;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * @description FmsNonrealtimeVO对象
 * <AUTHOR>
 * @date 2021-10-29 16:00:41
 */

public class FmsNonRealTimeVO {
    
    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    @XStreamAlias("Cur")
    private String moneyCode;
    /**
     * @Fields bankAccount : 账号
     */
    @XStreamAlias("OppAct")
    private String bankAccount;
    /**
     * @Fields transState :支付状态/核对状态 付费接口：2-成功 3-失败 6-退票， 收款核对接口：1-未核对，2-已核对
     */
    @XStreamAlias("TransState")
    private String transState;
    /**
     * @Fields payeePhone : 手机号码
     */
    @XStreamAlias("Cellphone")
    private String payeePhone = "";
    /**
     * @Fields reserved3 : 备用，目前无意义（空）
     */
    @XStreamAlias("ReqReserved3")
    private String reserved3 = "";
    /**
     * @Fields unitNumber : 应收付收付唯一号
     */
    @XStreamAlias("RdSeq")
    private String unitNumber;
    /**
     * @Fields cipDistrictBankCode : 开户行
     */
    @XStreamAlias("CNAPSCode")
    private String cipDistrictBankCode;
    /**
     * @Fields fastFlag : 加急标志1：是 0：否（付费接口：必输，收款核对接口：选输项）
     */
    @XStreamAlias("FastFlag")
    private String fastFlag;
    /**
     * @Fields isCorporate : 对公标志 (1：对私 0：对公（付费接口：必输，收款核对接口：选输项）)
     */
    @XStreamAlias("PrivateFlag")
    private String isCorporate;
    /**
     * @Fields rtnMsg : 交易返回描述
     */
    @XStreamAlias("PayInfo")
    private String rtnMsg;
    /**
     * @Fields checkingCode : 对账码
     */
    @XStreamAlias("Abstract")
    private String checkingCode;
    /**
     * @Fields reserved4 : 备用，目前无意义（空）
     */
    @XStreamAlias("ReqReserved4")
    private String reserved4 = "";
    /**
     * @Fields purpose : 用途 传入备注信息（传到银行）
     */
    @XStreamAlias("Purpose")
    private String purpose = "新华付款";
    /**
     * @Fields organCode : 管理机构(付费数据产生的机构，6位字符，不足6位右侧补0占位)
     */
    @XStreamAlias("ManageEntity")
    private String organCode;
    /**
     * @Fields memo : 备注
     */
    @XStreamAlias("Memo")
    private String memo = "";
    /**
     * @Fields certiCode : 证件号码
     */
    @XStreamAlias("CertNumber")
    private String certiCode = "";
    /**
     * @Fields mailAddr : 邮件地址
     */
    @XStreamAlias("MailAddr")
    private String mailAddr = "";
    /**
     * @Fields corpBankLocation : 企业方开户行,实际发生的本方账户开户行
     */
    @XStreamAlias("CorpBankLocation")
    private String corpBankLocation = "";
    /**
     * @Fields listId : 序号
     */
    private BigDecimal listId;
    /**
     * @Fields feeAmount : 金额
     */
    @XStreamAlias("Amount")
    private BigDecimal feeAmount;
    /**
     * @Fields cardType : 卡折类型,ATS默认为借记卡2（（付费接口：必输，默认传2，收款核对接口：选输项）
     */
    @XStreamAlias("CardType")
    private String cardType = "2";
    /**
     * @Fields mailFlag : 邮件通知标志
     */
    @XStreamAlias("MailFlag")
    private String mailFlag = "";
    /**
     * @Fields noteCode : 单据号码
     */
    private String noteCode;
    /**
     * @Fields finaNoteCode : 交易流水号，用于east报送
     */
    @XStreamAlias("NoteCode")
    private String finaNoteCode;
    /**
     * @Fields failType : 付费接口：0-正常，1-提交效验失败，2-手工作废，3-支付失败
     *         收费核对接口：0-正常，1-提交效验失败，3-核对失败（即未核对）
     */
    @XStreamAlias("FailType")
    private String failType;
    /**
     * @Fields description : 摘要
     */
    @XStreamAlias("Description")
    private String description = "";
    /**
     * @Fields corpAct : 企业方账户,实际发生的本方账户
     */
    @XStreamAlias("CorpAct")
    private String corpAct = "";
    /**
     * @Fields bankUserName : 账户所有人姓名
     */
    @XStreamAlias("OppActName")
    private String bankUserName;
    /**
     * @Fields payMadeDate : 银行反馈时间,yyyyMMddHHmmss 付费接口：银行支付结果确认时间，收费核对接口：收费核对时间
     */
    @XStreamOmitField
    private Date payMadeDate;
    /**
     * @Fields payMadeDate : 银行反馈时间,yyyyMMddHHmmss 付费接口：银行支付结果确认时间，收费核对接口：收费核对时间
     */
    private String PayMadeDate;
    /**
     * @Fields capOrganCode : 收付费申请机构代码 申请组织(申请机构代码（核心组织机构代码，如：862100）)
     */
    @XStreamAlias("ApplyEntity")
    private String capOrganCode;
    /**
     * @Fields rtnCode : 交易返回码
     */
    @XStreamAlias("PayInfoCode")
    private String rtnCode;
    /**
     * @Fields paySentDate : 提交银行时间,yyyyMMddHHmmss 付费接口：发送银行时间，收费核对接口：作为银行反馈到账时间
     */
    @XStreamOmitField
    private Date paySentDate;
    /**
     * @Fields PaySentDate : 提交银行时间,yyyyMMddHHmmss 付费接口：发送银行时间，收费核对接口：作为银行反馈到账时间
     */
    private String PaySentDate;
    /**
     * @Fields payMode : 收付方式。码值参考码表T 付费接口：1-转账，2-转账支票，3-现金支票
     *         收款核对接口：1-转账，2-转账支票，3-现金支票，4-现金送款簿
     */
    @XStreamAlias("SettlementMode")
    private String payMode;
    /**
     * @Fields specialAccountFalg : 专户标识，Y-大病医保，N-类大病医保，空-非专户支付数据。大病和类大病医保数据必输
     */
    @XStreamAlias("SpecialAccountFalg")
    private String specialAccountFalg = "";
    /**
     * @Fields batchId : 批次ID
     */
    @XStreamOmitField
    private String batchId;
    /**
     * @Fields arapFlag : 收付类型 (付费接口：固定传1，收费核对接口：固定传2)
     */
    @XStreamAlias("PayType")
    private String arapFlag;
    /**
     * @Fields bankCode : 银行编码
     */
    @XStreamAlias("OppBankCode")
    private String bankCode;

    /**
     * @Fields bankCode : 银行名称
     */
    @XStreamAlias("OppBankName")
    private String bankName;

    /**
     * @Fields payDate : 支付日期
     */
    @XStreamOmitField
    private Date payDate;
    /**
     * @Fields payDate : 支付日期
     */
    private String PayDate;
    /**
     * @Fields oppBankLocation : 交易方开户行名称
     */
    @XStreamAlias("OppBankLocation")
    private String oppBankLocation;
    /**
     * @Fields certiType : 证件类型
     */
    @XStreamAlias("CertType")
    private String certiType;

    /**
     * @Fields OppAreaCode : 交易方区域编码
     */
    private String OppAreaCode = "";
    /**
     * @Fields OppAreaName : 交易方区域名称
     */
    private String OppAreaName = "";

    /**
     * @Fields CNAPSName : 交易方联行号名称
     */
    private String CNAPSName = "";
    /**
     * @Fields SourceNoteCode : 来源系统单据号码
     */
    private String SourceNoteCode = "";

    /**
     * @Fields SMSFlag : 短信通知标志
     */
    private String SMSFlag = "";

    /**
     * @Fields payDate : 业务来源
     */
    @XStreamOmitField
    private String derivType;

    /**
     * @Fields updateBy : 操作员代码
     */
    @XStreamOmitField
    private String updateBy;

    /**
     * @Fields capOrganName : 操作机构名称
     */
    @XStreamOmitField
    private String capOrganName;

    /**
     * @Fields organName : 管理机构名称
     */
    @XStreamOmitField
    private String organName;
    /**
     * @Fields startPayDate : 支付起始日期
     */
    @XStreamOmitField
    private Date startPayDate;
    /**
     * @Fields endPayDate : 支付终止日期
     */
    @XStreamOmitField
    private Date endPayDate;
    /**
     * @Fields startDate : 应付起始日期
     */
    @XStreamOmitField
    private Date startDate;
    /**
     * @Fields endDate : 应付终止日期
     */
    @XStreamOmitField
    private Date endDate;

    /**
     * @Fields dueTime : 应缴应付日
     */
    @XStreamOmitField
    private Date dueTime;
    /**
     * @Fields userName : 操作用户
     */
    @XStreamOmitField
    private String userName;
    /**
     * @Fields businessCode : 业务号
     */
    @XStreamOmitField
    private String businessCode;

    /**
     * @Fields feeStatus : 支付状态
     */
    @XStreamOmitField
    private String feeStatus;
    /** 
    * @Fields insertTimestamp : 入机时间
    */ 
    @XStreamOmitField
    private Date insertTimestamp;
    /** 
    * @Fields finishTime : 实收实付时间
    */ 
    @XStreamOmitField
    private Date finishTime;
    
    /** 
    * @Fields statisticalDate : 统计时间
    */ 
    @XStreamOmitField
    private Date statisticalDate;
    
    /** 
    * @Fields reqSeqID : 提交批号
    */ 
    @XStreamAlias("ReqSeqID")
    private String reqSeqID;
    
    /** 
    * @Fields CorpEntity : 企业方账户所在机构
    */ 
    @XStreamOmitField
    private String CorpEntity;
    /** 
    * @Fields CorpBank : 企业方账户所属银行
    */ 
    @XStreamOmitField
    private String CorpBank;
    
    public String getFinaNoteCode() {
        return finaNoteCode;
    }

    public void setFinaNoteCode(String finaNoteCode) {
        this.finaNoteCode = finaNoteCode;
    }

    public String getCorpEntity() {
        return CorpEntity;
    }

    public String getCorpBank() {
        return CorpBank;
    }

    public void setCorpBank(String corpBank) {
        CorpBank = corpBank;
    }

    public void setCorpEntity(String corpEntity) {
        CorpEntity = corpEntity;
    }

    public String getReqSeqID() {
        return reqSeqID;
    }

    public void setReqSeqID(String reqSeqID) {
        this.reqSeqID = reqSeqID;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public String getOppBankLocation() {
        return oppBankLocation;
    }

    public void setOppBankLocation(String oppBankLocation) {
        this.oppBankLocation = oppBankLocation;
    }

    public Date getStatisticalDate() {
        return statisticalDate;
    }

    public void setStatisticalDate(Date statisticalDate) {
        this.statisticalDate = statisticalDate;
    }

    public Date getInsertTimestamp() {
        return insertTimestamp;
    }

    public void setInsertTimestamp(Date insertTimestamp) {
        this.insertTimestamp = insertTimestamp;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCapOrganName() {
        return capOrganName;
    }

    public void setCapOrganName(String capOrganName) {
        this.capOrganName = capOrganName;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public Date getStartPayDate() {
        return startPayDate;
    }

    public void setStartPayDate(Date startPayDate) {
        this.startPayDate = startPayDate;
    }

    public Date getEndPayDate() {
        return endPayDate;
    }

    public void setEndPayDate(Date endPayDate) {
        this.endPayDate = endPayDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getSMSFlag() {
        return SMSFlag;
    }

    public void setSMSFlag(String sMSFlag) {
        SMSFlag = sMSFlag;
    }

    public String getCNAPSName() {
        return CNAPSName;
    }

    public void setCNAPSName(String cNAPSName) {
        CNAPSName = cNAPSName;
    }

    public String getSourceNoteCode() {
        return SourceNoteCode;
    }

    public void setPayMadeDate(String payMadeDate) {
        PayMadeDate = payMadeDate;
    }

    public String getPAYMadeDate() {
        return PayMadeDate;
    }
    public void setPaySentDate(String paySentDate) {
        PaySentDate = paySentDate;
    }
    public String getPAYSentDate() {
        return PaySentDate;
    }
    public void setSourceNoteCode(String sourceNoteCode) {
        SourceNoteCode = sourceNoteCode;
    }

    public String getOppAreaCode() {
        return OppAreaCode;
    }

    public void setOppAreaCode(String oppAreaCode) {
        OppAreaCode = oppAreaCode;
    }

    public String getOppAreaName() {
        return OppAreaName;
    }

    public void setOppAreaName(String oppAreaName) {
        OppAreaName = oppAreaName;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setTransState(String transState) {
        this.transState = transState;
    }

    public String getTransState() {
        return transState;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = payeePhone;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setReserved3(String reserved3) {
        this.reserved3 = reserved3;
    }

    public String getReserved3() {
        return reserved3;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setCipDistrictBankCode(String cipDistrictBankCode) {
        this.cipDistrictBankCode = cipDistrictBankCode;
    }

    public String getCipDistrictBankCode() {
        return cipDistrictBankCode;
    }

    public void setFastFlag(String fastFlag) {
        this.fastFlag = fastFlag;
    }

    public String getFastFlag() {
        return fastFlag;
    }

    public void setIsCorporate(String isCorporate) {
        this.isCorporate = isCorporate;
    }

    public String getIsCorporate() {
        return isCorporate;
    }

    public void setRtnMsg(String rtnMsg) {
        this.rtnMsg = rtnMsg;
    }

    public String getRtnMsg() {
        return rtnMsg;
    }

    public void setCheckingCode(String checkingCode) {
        this.checkingCode = checkingCode;
    }

    public String getCheckingCode() {
        return checkingCode;
    }

    public void setReserved4(String reserved4) {
        this.reserved4 = reserved4;
    }

    public String getReserved4() {
        return reserved4;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setMailAddr(String mailAddr) {
        this.mailAddr = mailAddr;
    }

    public String getMailAddr() {
        return mailAddr;
    }

    public void setCorpBankLocation(String corpBankLocation) {
        this.corpBankLocation = corpBankLocation;
    }

    public String getCorpBankLocation() {
        return corpBankLocation;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardType() {
        return cardType;
    }

    public void setMailFlag(String mailFlag) {
        this.mailFlag = mailFlag;
    }

    public String getMailFlag() {
        return mailFlag;
    }

    public void setNoteCode(String noteCode) {
        this.noteCode = noteCode;
    }

    public String getNoteCode() {
        return noteCode;
    }

    public void setFailType(String failType) {
        this.failType = failType;
    }

    public String getFailType() {
        return failType;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setCorpAct(String corpAct) {
        this.corpAct = corpAct;
    }

    public String getCorpAct() {
        return corpAct;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setPayMadeDate(Date payMadeDate) {
        this.payMadeDate = payMadeDate;
    }

    public Date getPayMadeDate() {
        return payMadeDate;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setRtnCode(String rtnCode) {
        this.rtnCode = rtnCode;
    }

    public String getRtnCode() {
        return rtnCode;
    }

    public void setPaySentDate(Date paySentDate) {
        this.paySentDate = paySentDate;
    }

    public Date getPaySentDate() {
        return paySentDate;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setSpecialAccountFalg(String specialAccountFalg) {
        this.specialAccountFalg = specialAccountFalg;
    }

    public String getSpecialAccountFalg() {
        return specialAccountFalg;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public Date getPayDate() {
        return payDate;
    }

  

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public void setPayDate(String payDate) {
        PayDate = payDate;
    }

    @Override
    public String toString() {
        return "FmsNonRealTimeVO [moneyCode=" + moneyCode + ", bankAccount=" + bankAccount + ", transState="
                + transState + ", payeePhone=" + payeePhone + ", reserved3=" + reserved3 + ", unitNumber=" + unitNumber
                + ", cipDistrictBankCode=" + cipDistrictBankCode + ", fastFlag=" + fastFlag + ", isCorporate="
                + isCorporate + ", rtnMsg=" + rtnMsg + ", checkingCode=" + checkingCode + ", reserved4=" + reserved4
                + ", purpose=" + purpose + ", organCode=" + organCode + ", memo=" + memo + ", certiCode=" + certiCode
                + ", mailAddr=" + mailAddr + ", corpBankLocation=" + corpBankLocation + ", listId=" + listId
                + ", feeAmount=" + feeAmount + ", cardType=" + cardType + ", mailFlag=" + mailFlag + ", noteCode="
                + noteCode + ", failType=" + failType + ", description=" + description + ", corpAct=" + corpAct
                + ", bankUserName=" + bankUserName + ", payMadeDate=" + payMadeDate + ", capOrganCode=" + capOrganCode
                + ", rtnCode=" + rtnCode + ", paySentDate=" + paySentDate + ", payMode=" + payMode
                + ", specialAccountFalg=" + specialAccountFalg + ", batchId=" + batchId + ", arapFlag=" + arapFlag
                + ", bankCode=" + bankCode + ", bankName=" + bankName + ", payDate=" + payDate + ", PayDate=" + PayDate
                + ", oppBankLocation=" + oppBankLocation + ", certiType=" + certiType + ", OppAreaCode=" + OppAreaCode
                + ", OppAreaName=" + OppAreaName + ", CNAPSName=" + CNAPSName + ", SourceNoteCode=" + SourceNoteCode
                + ", SMSFlag=" + SMSFlag + ", derivType=" + derivType + ", updateBy=" + updateBy + ", capOrganName="
                + capOrganName + ", organName=" + organName + ", startPayDate=" + startPayDate + ", endPayDate="
                + endPayDate + ", startDate=" + startDate + ", endDate=" + endDate + ", dueTime=" + dueTime
                + ", userName=" + userName + ", businessCode=" + businessCode + ", feeStatus=" + feeStatus
                + ", insertTimestamp=" + insertTimestamp + ", finishTime=" + finishTime + ", statisticalDate="
                + statisticalDate + "]";
    }

    

    
}
