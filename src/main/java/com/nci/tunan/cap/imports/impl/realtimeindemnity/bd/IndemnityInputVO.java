package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/** 
 * @ClassName: IndemnityInputVO 
 * @description  实时代付与资金交易报文体
 * <AUTHOR>
 * @date 2017年12月15日 上午11:31:41 
 * @.belongToModule 收付费-实时代付 
 */  
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder={"RdSeq", "ApplyEntity", "PayType", "CorpAct", "OppAct", "OppActName", "OppArea", "OppBank", 
        "Cur", "Amount", "PrivateFlag", "Purpose", "Memo", "Description", "CertType", "CertNumber", "SourceSystem", 
        "SourceNoteCode", "Category", "BusType", "ReqReserved1", "ReqReserved2","IsInvestAcc","BusinessDetails","RecAsknumber"})
public class IndemnityInputVO {


    /**
     * @Fields rdSeq : 交易指令流水号 (交易指令的流水号，全局唯一(收付唯一号))
     */
    private String RdSeq;

    /**
     * @Fields applyEntity : 申请组织 (单据的业务发生组织)
     */
    private String ApplyEntity;

    /**
     * @Fields payType : 交易类型 (业务类型（传：理赔）)
     */
    private String PayType;

    /**
     * @Fields corpAct : 企业方账户 (没有可不传)
     */
    private String CorpAct;

    /**
     * @Fields oppAct : 交易方账户 (收款的客户的银行账户)
     */
    private String OppAct;

    /**
     * @Fields oppActName : 交易方户名
     */
    private String OppActName;

    /**
     * @Fields oppArea : 交易方区域 (银行区域，支持代码或名称，没有可不传)
     */
    private String OppArea;

    /**
     * @Fields oppBank : 交易方银行 (传核心系统银行代码，如8600010-工行)
     */
    private String OppBank;

    /**
     * @Fields cur : 币种
     */
    private String Cur;

    /**
     * @Fields amount : 金额 (无正负号)
     */
    private BigDecimal Amount;

    /**
     * @Fields privateFlag : 公私标志 (1：对公， 0：对私；个险数据默认0-对私。)
     */
    private BigDecimal PrivateFlag;

    /**
     * @Fields purpose : 用途
     */
    private String Purpose;

    /**
     * @Fields memo : 备注
     */
    private String Memo;

    /**
     * @Fields description : 摘要
     */
    private String Description;

    /**
     * @Fields certType : 证件类型
     */
    private String CertType;

    /**
     * @Fields certNumber : 证件号码
     */
    private String CertNumber;

    /**
     * @Fields sourceSystem : 来源系统 (1：老核心 2：新核心)
     */
    private BigDecimal SourceSystem;

    /**
     * @Fields sourceNoteCode : 来源系统单据号码（外部系统需要在资金建立关系的可识别业务单据号（业务号/赔案号））
     */
    private String SourceNoteCode;

    /**
     * @Fields category : 现金流量
     */
    private String Category;
    
    /**
     * @Fields category : 现金流量
     */
    private String BusType;

    /**
     * @Fields reqReserved1 : 预留
     */
    private String ReqReserved1;

    /**
     * @Fields reqReserved2 : 预留
     */
    private String ReqReserved2;
    
    /**
     * 银保信账户标识
     */
    private String IsInvestAcc;
    
    /**
     * 业务明细集合
     */
    private BusinessDetails BusinessDetails;
    
    /**
     * 原申请单编号
     */
    private String RecAsknumber;

	public String getRdSeq() {
		return RdSeq;
	}

	public void setRdSeq(String rdSeq) {
		RdSeq = rdSeq;
	}

	public String getApplyEntity() {
		return ApplyEntity;
	}

	public void setApplyEntity(String applyEntity) {
		ApplyEntity = applyEntity;
	}

	public String getPayType() {
		return PayType;
	}

	public void setPayType(String payType) {
		PayType = payType;
	}

	public String getCorpAct() {
		return CorpAct;
	}

	public void setCorpAct(String corpAct) {
		CorpAct = corpAct;
	}

	public String getOppAct() {
		return OppAct;
	}

	public void setOppAct(String oppAct) {
		OppAct = oppAct;
	}

	public String getOppActName() {
		return OppActName;
	}

	public void setOppActName(String oppActName) {
		OppActName = oppActName;
	}

	public String getOppArea() {
		return OppArea;
	}

	public void setOppArea(String oppArea) {
		OppArea = oppArea;
	}

	public String getOppBank() {
		return OppBank;
	}

	public void setOppBank(String oppBank) {
		OppBank = oppBank;
	}

	public String getCur() {
		return Cur;
	}

	public void setCur(String cur) {
		Cur = cur;
	}

	public BigDecimal getAmount() {
		return Amount;
	}

	public void setAmount(BigDecimal amount) {
		Amount = amount;
	}

	public BigDecimal getPrivateFlag() {
		return PrivateFlag;
	}

	public void setPrivateFlag(BigDecimal privateFlag) {
		PrivateFlag = privateFlag;
	}

	public String getPurpose() {
		return Purpose;
	}

	public void setPurpose(String purpose) {
		Purpose = purpose;
	}

	public String getMemo() {
		return Memo;
	}

	public void setMemo(String memo) {
		Memo = memo;
	}

	public String getDescription() {
		return Description;
	}

	public void setDescription(String description) {
		Description = description;
	}

	public String getCertType() {
        return CertType;
    }

    public void setCertType(String certType) {
        CertType = certType;
    }

	public String getCertNumber() {
		return CertNumber;
	}

	public void setCertNumber(String certNumber) {
		CertNumber = certNumber;
	}

	public BigDecimal getSourceSystem() {
		return SourceSystem;
	}

	public void setSourceSystem(BigDecimal sourceSystem) {
		SourceSystem = sourceSystem;
	}

	public String getSourceNoteCode() {
		return SourceNoteCode;
	}

	public void setSourceNoteCode(String sourceNoteCode) {
		SourceNoteCode = sourceNoteCode;
	}

	public String getCategory() {
		return Category;
	}

	public void setCategory(String category) {
		Category = category;
	}

	public String getBusType() {
		return BusType;
	}

	public void setBusType(String busType) {
		BusType = busType;
	}

	public String getReqReserved1() {
		return ReqReserved1;
	}

	public void setReqReserved1(String reqReserved1) {
		ReqReserved1 = reqReserved1;
	}

	public String getReqReserved2() {
		return ReqReserved2;
	}

	public void setReqReserved2(String reqReserved2) {
		ReqReserved2 = reqReserved2;
	}

	public String getIsInvestAcc() {
		return IsInvestAcc;
	}

	public void setIsInvestAcc(String isInvestAcc) {
		IsInvestAcc = isInvestAcc;
	}

	public BusinessDetails getBusinessDetails() {
		return BusinessDetails;
	}

	public void setBusinessDetails(BusinessDetails businessDetails) {
		BusinessDetails = businessDetails;
	}
	   
	public String getRecAsknumber() {
	   return RecAsknumber;
	}

	public void setRecAsknumber(String recAsknumber) {
	   RecAsknumber = recAsknumber;
	}

}
