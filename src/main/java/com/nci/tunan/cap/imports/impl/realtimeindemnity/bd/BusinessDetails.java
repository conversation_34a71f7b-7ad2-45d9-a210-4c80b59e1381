
package com.nci.tunan.cap.imports.impl.realtimeindemnity.bd;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;


/**
 * <p>BusinessDetails complex type�� Java �ࡣ
 * 
 * <p>以下模式片段指定包含在此类中的预期内容
 * 
 * <pre>
 * &lt;complexType name="BusinessDetails">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BusinessDetail" type="{http://www.newchinalife.com/service/bd}BusinessDetail" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class BusinessDetails {

	@XmlElement(name = "BusinessDetail")
    protected List<APBusinessDetail> BusinessDetail;

	public List<APBusinessDetail> getApBusinessDetails() {
		return BusinessDetail;
	}
	
	public void setApBusinessDetails(List<APBusinessDetail> BusinessDetail) {
		this.BusinessDetail = BusinessDetail;
	}

	

    /**
     * Gets the value of the businessDetail property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the businessDetail property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getBusinessDetail().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link BusinessDetail }
     * 
     * 
     */
    
    
    

}
