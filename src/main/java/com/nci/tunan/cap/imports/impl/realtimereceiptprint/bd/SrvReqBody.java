
package com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.tunan.cap.imports.impl.realtimereceiptprint.hd.SRVReqHead;

/** 
 * @description 交易请求报文体
 * @<NAME_EMAIL> 
 * @date 2015-12-17 上午10:42:28 
 * @.belongToModule 收付费-打印接口
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBody", propOrder = {
    "bizHeader",
    "bizBody"
})
public class SrvReqBody {

    @XmlElement(required = true)
    protected SRVReqHead bizHeader;
    @XmlElement(required = true)
    protected SrvReqBizBody bizBody;

    public SRVReqHead getBizHeader() {
        return bizHeader;
    }

    public void setBizHeader(SRVReqHead value) {
        this.bizHeader = value;
    }

    public SrvReqBizBody getBizBody() {
        return bizBody;
    }

    public void setBizBody(SrvReqBizBody value) {
        this.bizBody = value;
    }

}
