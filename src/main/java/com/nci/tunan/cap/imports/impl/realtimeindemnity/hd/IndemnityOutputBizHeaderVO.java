package com.nci.tunan.cap.imports.impl.realtimeindemnity.hd;

import javax.xml.bind.annotation.XmlElement;

import com.nci.udmp.framework.model.BaseVO;

/**
 * @ClassName: IndemnityOutputBizHeaderVO 
 * @description: 实时代付与资金交易报文头
 * <AUTHOR>
 * @date 2017年12月11日 上午11:27:52 
 * @.belongToModule 收付费-实时代付
 */ 
public class IndemnityOutputBizHeaderVO extends BaseVO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */
    private static final long serialVersionUID = 408924493899836532L;

    /**
     * @Fields transSource : 交易来源  (HX)
     */
    private String transSource;

    /**
     * @Fields transCode : 交易编码  (1908)
     */
    private String transCode;

    /**
     * @Fields transDate : 交易日期  (格式是yyyyMMdd)
     */
    private String transDate;

    /**
     * @Fields transTime : 交易时间   (格式是HH24miss)
     */
    private String transTime;

    /**
     * @Fields transSeq : 交易流水号   (时间戳，格式是yyyymmddhh24missff4)
     */
    private String transSeq;
    
    /** 
     * @Fields rtnCode : 交易返回码
     */ 
    private String rtnCode;
    
    /** 
     * @Fields rtnMsg : 交易返回描述
     */ 
    private String rtnMsg;

    @XmlElement(name="TransSource")
    public String getTransSource() {
        return transSource;
    }

    public void setTransSource(String transSource) {
        this.transSource = transSource;
    }
    
    @XmlElement(name="TransCode")
    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }
    @XmlElement(name="TransDate")
    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }
    @XmlElement(name="TransTime")
    public String getTransTime() {
        return transTime;
    }

    public void setTransTime(String transTime) {
        this.transTime = transTime;
    }
    
    @XmlElement(name="TransSeq")
    public String getTransSeq() {
        return transSeq;
    }

    public void setTransSeq(String transSeq) {
        this.transSeq = transSeq;
    }
    @XmlElement(name="RtnCode")
    public String getRtnCode() {
        return rtnCode;
    }
    
    public void setRtnCode(String rtnCode) {
        this.rtnCode = rtnCode;
    }
    
    @XmlElement(name="RtnMsg")
    public String getRtnMsg() {
        return rtnMsg;
    }
    
    public void setRtnMsg(String rtnMsg) {
        this.rtnMsg = rtnMsg;
    }

    @Override
    public String getBizId() {
        return null;
    }

}
