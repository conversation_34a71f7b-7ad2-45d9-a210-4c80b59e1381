
package com.nci.tunan.cap.imports;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

import com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd.PrintInputData;
import com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd.SrvReqBizBody;
import com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd.SrvReqBody;
import com.nci.tunan.cap.imports.impl.realtimereceiptprint.hd.SRVReqHead;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.nci.tunan.cap.imports package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
/**
 * @description 客户端创建factory
 * <AUTHOR> <EMAIL>
 * @date 2014-12-10 下午2:09:17
 * @.belongToModule 收付费-打印接口
 */
@XmlRegistry
public class PrintObjectFactory {

    private static final  QName _Request_QNAME = new QName("http://www.newchinalife.com/service/bd", "request");
    private static final  QName _SRVReqHead_QNAME = new QName("http://www.newchinalife.com/service/hd", "SRVReqHead");
    private static final  QName _Response_QNAME = new QName("http://www.newchinalife.com/service/bd", "response");
    private static final  QName _SysHeader_QNAME = new QName("http://www.newchinalife.com/common/header/in", "sysHeader");
    private static final  QName _SRVResHead_QNAME = new QName("http://www.newchinalife.com/service/hd", "SRVResHead");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.nci.tunan.cap.imports
     * 
     */
    public PrintObjectFactory() {
    }

    /**
     * Create an instance of {@link 
     *      SysMsgHeader }
     * 
     */
    public SysMsgHeader createSysMsgHeader() {
        return new SysMsgHeader();
    }

    /**
     * Create an instance of {@link 
     *      SrvResBody }
     * 
     */
    public SrvResBody createSrvResBody() {
        return new SrvResBody();
    }

    /**
     * Create an instance of {@link 
     *      SrvReqBody }
     * 
     */
    public SrvReqBody createSrvReqBody() {
        return new SrvReqBody();
    }

    /**
     * Create an instance of {@link 
     *      SendJob }
     * 
     */
    public SendJob createSendJob() {
        return new SendJob();
    }

    /**
     * Create an instance of {@link 
     *      SrvReqBizBody }
     * 
     */
    public SrvReqBizBody createSrvReqBizBody() {
        return new SrvReqBizBody();
    }

    /**
     * Create an instance of {@link 
     *      OutputData }
     * 
     */
    public PrintOutputData createOutputData() {
        return new PrintOutputData();
    }

    /**
     * Create an instance of {@link 
     *      SendJobResponse }
     * 
     */
    public SendJobResponse createSendJobResponse() {
        return new SendJobResponse();
    }

    /**
     * Create an instance of {@link 
     *      InputData }
     * 
     */
    public PrintInputData createInputData() {
        return new PrintInputData();
    }

    /**
     * Create an instance of {@link 
     *      SrvResBizBody }
     * 
     */
    public SrvResBizBody createSrvResBizBody() {
        return new SrvResBizBody();
    }

    /**
     * Create an instance of {@link 
     *      SRVReqHead }
     * 
     */
    public SRVReqHead createSRVReqHead() {
        return new SRVReqHead();
    }

    /**
     * Create an instance of {@link 
     *      SRVResHead }
     * 
     */
    public SRVResHead createSRVResHead() {
        return new SRVResHead();
    }

    /**
     * Create an instance of {@link 
     *      JAXBElement }{@code 
     *          <}{@link 
     *              SrvReqBody }{@code 
     *                  >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/bd", name = "request")
    public JAXBElement<SrvReqBody> createRequest(SrvReqBody value) {
        return new JAXBElement<SrvReqBody>(_Request_QNAME, SrvReqBody.class, null, value);
    }

    /**
     * Create an instance of {@link 
     *      JAXBElement }{@code 
     *          <}{@link 
     *              SRVReqHead }{@code 
     *                  >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/hd", name = "SRVReqHead")
    public JAXBElement<SRVReqHead> createSRVReqHead(SRVReqHead value) {
        return new JAXBElement<SRVReqHead>(_SRVReqHead_QNAME, SRVReqHead.class, null, value);
    }

    /**
     * Create an instance of {@link 
     *      JAXBElement }{@code 
     *          <}{@link 
     *              SrvResBody }{@code 
     *                  >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/bd", name = "response")
    public JAXBElement<SrvResBody> createResponse(SrvResBody value) {
        return new JAXBElement<SrvResBody>(_Response_QNAME, SrvResBody.class, null, value);
    }

    /**
     * Create an instance of {@link 
     *      JAXBElement }{@code 
     *          <}{@link 
     *              SysMsgHeader }{@code 
     *                  >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/common/header/in", name = "sysHeader")
    public JAXBElement<SysMsgHeader> createSysHeader(SysMsgHeader value) {
        return new JAXBElement<SysMsgHeader>(_SysHeader_QNAME, SysMsgHeader.class, null, value);
    }

    /**
     * Create an instance of {@link 
     *      JAXBElement }{@code 
     *          <}{@link 
     *              SRVResHead }{@code 
     *                  >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.newchinalife.com/service/hd", name = "SRVResHead")
    public JAXBElement<SRVResHead> createSRVResHead(SRVResHead value) {
        return new JAXBElement<SRVResHead>(_SRVResHead_QNAME, SRVResHead.class, null, value);
    }

}
