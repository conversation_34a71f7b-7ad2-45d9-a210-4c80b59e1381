
package com.nci.tunan.cap.imports;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;



/** 
 * @ClassName: SendJob 
 * @description 调用打印系统接口
 * <AUTHOR> <EMAIL>
 * @date 2014年04月17日 下午5:29:16 
 * @.belongToModule 收付费-调用打印接口
 */ 
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sendJob", propOrder = {
    "clientIP",
    "userName",
    "vp",
    "jobName",
    "splFile",
    "stage"
})
public class SendJob {

    /** 
     * 用户的 IP 位置
     */ 
    @XmlElement(required = true)
    protected String clientIP;
    /** 
     * 用户ID
     */ 
    @XmlElement(required = true)
    protected String userName;
    /** 
     * 打印类型
     */ 
    @XmlElement(required = true)
    protected String vp;
    /** 
     * 打印任务名称
     */ 
    @XmlElement(required = true)
    protected String jobName;
    /** 
     * 指要 SIP Server 处理成报表的数据文件,XML报文
     */ 
    @XmlElement(required = true)
    protected String splFile;
    /** 
     * 参考值
     */ 
    @XmlElement(required = true)
    protected String stage;

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String value) {
        this.clientIP = value;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String value) {
        this.userName = value;
    }

    public String getVp() {
        return vp;
    }

    public void setVp(String value) {
        this.vp = value;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String value) {
        this.jobName = value;
    }

    public String getSplFile() {
        return splFile;
    }

    public void setSplFile(String value) {
        this.splFile = value;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String value) {
        this.stage = value;
    }

}
