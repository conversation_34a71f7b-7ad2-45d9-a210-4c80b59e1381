package com.nci.tunan.cap.imports;

import java.util.logging.Logger;

import com.nci.tunan.cap.imports.impl.realtimereceiptprint.bd.SrvReqBody;

/**
 * @ClassName: PrintServicesUnder500KSrvBindingQSPortImpl
 * @description 打印接口实现
 * <AUTHOR>
 * @date 2019年12月18日 上午10:14:05
 * @.belongToModule 收付费-打印接口
 */
@javax.jws.WebService(serviceName = "PrintServicesUnder500KSrvBindingQSService", portName = "PrintServicesUnder500KSrvBindingQSPort", targetNamespace = "http://www.newchinalife.com", wsdlLocation = "http://***********:8111/services/P00002001217?wsdl", endpointInterface = "com.nci.tunan.cap.imports.PrintServicesUnder500KSrvPortType")
public class PrintServicesUnder500KSrvBindingQSPortImpl implements PrintServicesUnder500KSrvPortType {

    /**
     * 日志类
     */
    private static final Logger LOG = Logger.getLogger(PrintServicesUnder500KSrvBindingQSPortImpl.class.getName());

    /**
     * @description 调用打印接口方法实现
     * @param parametersReqHeader 技术报文头
     * @param parametersReqBody 技术报文体
     * @param parametersResHeader 业务报文头
     * @param parametersResBody 业务报文体
     * @see com.nci.tunan.cap.imports.PrintServicesUnder500KSrvPortType#printServicesUnder500K(com.nci.tunan.cap.imports.SysMsgHeader,
     *      com.nci.tunan.cap.imports.SrvReqBody, javax.xml.ws.Holder,
     *      javax.xml.ws.Holder)
     */
    public void printServicesUnder500K(SysMsgHeader parametersReqHeader, SrvReqBody parametersReqBody,
            javax.xml.ws.Holder<SysMsgHeader> parametersResHeader, javax.xml.ws.Holder<SrvResBody> parametersResBody) {
        LOG.info("Executing operation printServicesUnder500K");
        try {
            com.nci.tunan.cap.imports.SysMsgHeader parametersResHeaderValue = null;
            parametersResHeader.value = parametersResHeaderValue;
            com.nci.tunan.cap.imports.SrvResBody parametersResBodyValue = null;
            parametersResBody.value = parametersResBodyValue;
        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

}
