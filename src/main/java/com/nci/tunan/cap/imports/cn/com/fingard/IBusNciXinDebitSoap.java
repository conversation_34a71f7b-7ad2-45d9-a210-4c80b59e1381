package com.nci.tunan.cap.imports.cn.com.fingard;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/** 
 * @ClassName: BusNciXinDebitSoap 
 * @description  实时收费接口类
 * <AUTHOR>
 * @date 2016年04月27日 下午14:22:19
 * @.belongToModule 收付费-实时收费接口
 *  
 */ 
@WebService(targetNamespace = "http://FinGard.com.cn/", name = "BusNciXinDebitSoap")
@XmlSeeAlso({ObjectFactory.class})
public interface IBusNciXinDebitSoap {

    /** 
     * @description 接口提交方法
     * @param pStrReqData 字符串类型
     * @return  设定文件 
     * @throws 
     */
    @WebResult(name = "SubmitResult", targetNamespace = "http://FinGard.com.cn/")
    @RequestWrapper(localName = "Submit", targetNamespace = "http://FinGard.com.cn/", className = "cn.com.fingard.Submit")
    @WebMethod(operationName = "Submit", action = "http://FinGard.com.cn/Submit")
    @ResponseWrapper(localName = "SubmitResponse", targetNamespace = "http://FinGard.com.cn/", className = "cn.com.fingard.SubmitResponse")
    public java.lang.String submit(
        @WebParam(name = "p_StrReqData", targetNamespace = "http://FinGard.com.cn/")
        java.lang.String pStrReqData
    );
}
