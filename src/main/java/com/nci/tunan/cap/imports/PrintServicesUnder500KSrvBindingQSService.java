package com.nci.tunan.cap.imports;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;

/** 
 * @ClassName: PrintServicesUnder500KSrvBindingQSService 
 * @description 打印接口x
 * <AUTHOR>
 * @date 2016-03-11 15:48:31
 * @.belongToModule 收付费-打印接口
 */ 
@WebServiceClient(name = "PrintServicesUnder500KSrvBindingQSService", 
                  wsdlLocation = "http://***********:8111/services/P00002001217?wsdl",
                  targetNamespace = "http://www.newchinalife.com") 
public class PrintServicesUnder500KSrvBindingQSService extends Service {

    /** 
     * 接口地址
     */ 
    public static final  URL WSDL_LOCATION;

    /** 
     * 接口域名
     */ 
    public static final  QName SERVICE = new QName("http://www.newchinalife.com", "PrintServicesUnder500KSrvBindingQSService");
    /** 
     * 接口域名
     */ 
    public static final  QName PrintServicesUnder500KSrvBindingQSPort = new QName("http://www.newchinalife.com", "PrintServicesUnder500KSrvBindingQSPort");
    static {
        URL url = null;
        try {
            url = new URL("http://***********:8111/services/P00002001217?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(PrintServicesUnder500KSrvBindingQSService.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", 
                     "http://***********:8111/services/P00002001217?wsdl");
        }
        WSDL_LOCATION = url;
    }

    /** 
     * @description 接口调用实现
     * @param wsdlLocation  接口地址
     * @return 返回类型 
     * @throws 
     */
    public PrintServicesUnder500KSrvBindingQSService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    /** 
     * @description 接口调用方法实现
     * @param wsdlLocation 接口地址
     * @param serviceName  接口域名 
     * @return 返回类型 
     * @throws 
     */
    public PrintServicesUnder500KSrvBindingQSService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /** 
     * @description 接口调用方法
     * @return 返回类型 
     * @throws 
     */
    public PrintServicesUnder500KSrvBindingQSService() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    /** 
     * @description 获取调用方法
     * @return  设定文件 
     * @throws 
     */
    @WebEndpoint(name = "PrintServicesUnder500KSrvBindingQSPort")
    public PrintServicesUnder500KSrvPortType getPrintServicesUnder500KSrvBindingQSPort() {
        return super.getPort(PrintServicesUnder500KSrvBindingQSPort, PrintServicesUnder500KSrvPortType.class);
    }

    /** 
     * @description 获取接口调用方法
     * @param features 特征
     * @return  设定文件 
     * @throws 
     */
    @WebEndpoint(name = "PrintServicesUnder500KSrvBindingQSPort")
    public PrintServicesUnder500KSrvPortType getPrintServicesUnder500KSrvBindingQSPort(WebServiceFeature... features) {
        return super.getPort(PrintServicesUnder500KSrvBindingQSPort, PrintServicesUnder500KSrvPortType.class, features);
    }

}
