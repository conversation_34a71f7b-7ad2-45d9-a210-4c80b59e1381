package com.nci.tunan.cap.imports.cn.com.fingard;

import java.math.BigDecimal;
import java.util.List;

import com.thoughtworks.xstream.annotations.XStreamImplicit;

public class NonRealTimePaymentInput {
    
    /** 
    * @Fields ReqSeqID : 交易批号(全局唯一)
    */ 
    private String ReqSeqID = "";
    
    /** 
    * @Fields TotalCount : 总笔数(批次内内的指令笔数)
    */ 
    private int TotalCount;
    
    /** 
    * @Fields TotalAmount : 总金额(批次内的总金额,无正负号，以元为单位，保留两位小数点批次内的总金额,无正负号，以元为单位，保留两位小数点)
    */ 
    private BigDecimal TotalAmount;
    
    /** 
    * @Fields ReqReserved1 : 保留字段(备用,目前无意义)
    */ 
    private String ReqReserved1 = "";
    
    /** 
    * @Fields ReqReserved2 : 保留字段(备用,目前无意义)
    */ 
    private String ReqReserved2 = "";
    
    @XStreamImplicit(itemFieldName="RD")
    private List<FmsNonRealTimeVO> nonRealTimeList;

    public String getReqSeqID() {
        return ReqSeqID;
    }

    public void setReqSeqID(String reqSeqID) {
        ReqSeqID = reqSeqID;
    }
    
    public int getTotalCount() {
        return TotalCount;
    }

    public void setTotalCount(int totalCount) {
        TotalCount = totalCount;
    }

    public BigDecimal getTotalAmount() {
		return TotalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		TotalAmount = totalAmount;
	}

    public String getReqReserved1() {
        return ReqReserved1;
    }

    public void setReqReserved1(String reqReserved1) {
        ReqReserved1 = reqReserved1;
    }

    public String getReqReserved2() {
        return ReqReserved2;
    }

    public void setReqReserved2(String reqReserved2) {
        ReqReserved2 = reqReserved2;
    }
    

   

    public List<FmsNonRealTimeVO> getNonRealTimeList() {
        return nonRealTimeList;
    }

    public void setNonRealTimeList(List<FmsNonRealTimeVO> nonRealTimeList) {
        this.nonRealTimeList = nonRealTimeList;
    }

    
}
