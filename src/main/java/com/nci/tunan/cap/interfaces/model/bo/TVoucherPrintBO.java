package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @ClassName: TVoucherPrintVO
 * @description 收费凭证打印表表
 * <AUTHOR> <EMAIL>
 * @date 
 * 
 */
public class TVoucherPrintBO {
	/** 
     * 主键ID
     */ 
    private BigDecimal listId; 

    /** 
     * 应收付业务流水标识
     */ 
    private String unitNumber; 

    /** 
     * 打印次数
     */ 
    private BigDecimal printTimes; 

    /** 
     * 打印时间
     */ 
    private Date printTime; 

    /** 
     * 打印人
     */ 
    private BigDecimal printOperator;
    
    /** 
     * 单证类型:EF002-收费凭证,EF001-付费凭证
     */ 
    private String printType; 
    
    /** 
     * 打印状态:1-已打印,2-作废
     */ 
    private BigDecimal printStatus;
    
    /** 
     * 实付号
     */ 
    private String payrefno; 
    
    /** 
     * 加密前防伪码
     */ 
    private String securityCodeBefore; 
    
    /** 
     * 加密后防伪码
     */ 
    private String securityCodeAfter;
    
	public String getPrintType() {
        return printType;
    }

    public void setPrintType(String printType) {
        this.printType = printType;
    }

    public BigDecimal getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(BigDecimal printStatus) {
        this.printStatus = printStatus;
    }

    public String getPayrefno() {
        return payrefno;
    }

    public void setPayrefno(String payrefno) {
        this.payrefno = payrefno;
    }

    public String getSecurityCodeBefore() {
        return securityCodeBefore;
    }

    public void setSecurityCodeBefore(String securityCodeBefore) {
        this.securityCodeBefore = securityCodeBefore;
    }

    public String getSecurityCodeAfter() {
        return securityCodeAfter;
    }

    public void setSecurityCodeAfter(String securityCodeAfter) {
        this.securityCodeAfter = securityCodeAfter;
    }

    public BigDecimal getListId() {
		return listId;
	}

	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}

	public String getUnitNumber() {
		return unitNumber;
	}

	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public BigDecimal getPrintTimes() {
		return printTimes;
	}

	public void setPrintTimes(BigDecimal printTimes) {
		this.printTimes = printTimes;
	}

	public Date getPrintTime() {
		return printTime;
	}

	public void setPrintTime(Date printTime) {
		this.printTime = printTime;
	}

	public BigDecimal getPrintOperator() {
		return printOperator;
	}

	public void setPrintOperator(BigDecimal printOperator) {
		this.printOperator = printOperator;
	}

    @Override
    public String toString() {
        return "TVoucherPrintBO [listId=" + listId + ", unitNumber=" + unitNumber + ", printTimes=" + printTimes
                + ", printTime=" + printTime + ", printOperator=" + printOperator + ", printType=" + printType
                + ", printStatus=" + printStatus + ", payrefno=" + payrefno + ", securityCodeBefore="
                + securityCodeBefore + ", securityCodeAfter=" + securityCodeAfter + "]";
    }

}
