package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 记账字段映射表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class GlFieldMappingBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 995125149836210656L;
    /**
     * @Fields columnName : 记账字段名称
     */
    private String columnName;
    /**
     * @Fields feeTable : 数据类型
     */
    private String feeTable;
    /**
     * @Fields mappingId : 映射ID
     */
    private BigDecimal mappingId;
    /**
     * @Fields fieldId : 属性ID
     */
    private BigDecimal fieldId;

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setMappingId(BigDecimal mappingId) {
        this.mappingId = mappingId;
    }

    public BigDecimal getMappingId() {
        return mappingId;
    }

    public void setFieldId(BigDecimal fieldId) {
        this.fieldId = fieldId;
    }

    public BigDecimal getFieldId() {
        return fieldId;
    }

    public void setFeeTable(String feeTable) {
        this.feeTable = feeTable;
    }

    public String getFeeTable() {
        return feeTable;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "GlFieldMappingBO [" + "columnName=" + columnName + ", mappingId=" + mappingId + ", fieldId=" + fieldId
                + ", feeTable=" + feeTable + "]";
    }
}
