package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 记账字段定义表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class GlFieldBO extends BaseBO {
    
    /** 
    * @Fields serialVersionUID : 序列化 
    */   
    private static final long serialVersionUID = 7283525692393773677L;
    /**
     * @Fields codeTable : 取值表名称
     */
    private String codeTable;
    /**
     * @Fields fieldName : 属性名称
     */
    private String fieldName;
    /**
     * @Fields fieldId : 属性ID
     */
    private BigDecimal fieldId;
    /**
     * @Fields fieldProperty : 属性值
     */
    private String fieldProperty;
    /**
     * @Fields fieldDesc : 属性描述
     */
    private String fieldDesc;
    /**
     * @Fields fieldType : 属性类型
     */
    private BigDecimal fieldType;
    /**
     * @Fields whereCause : 取值表条件
     */
    private String whereCause;
    /**
     * @Fields defaultValue : 默认值
     */
    private String defaultValue;
    /**
     * @Fields feeTable : 数据类型
     */
    private String feeTable;
    /**
     * @Fields length : 长度
     */
    private BigDecimal length;

    public void setCodeTable(String codeTable) {
        this.codeTable = codeTable;
    }

    public String getCodeTable() {
        return codeTable;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldId(BigDecimal fieldId) {
        this.fieldId = fieldId;
    }

    public BigDecimal getFieldId() {
        return fieldId;
    }

    public void setFieldProperty(String fieldProperty) {
        this.fieldProperty = fieldProperty;
    }

    public String getFieldProperty() {
        return fieldProperty;
    }

    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc;
    }

    public String getFieldDesc() {
        return fieldDesc;
    }

    public void setFieldType(BigDecimal fieldType) {
        this.fieldType = fieldType;
    }

    public BigDecimal getFieldType() {
        return fieldType;
    }

    public void setWhereCause(String whereCause) {
        this.whereCause = whereCause;
    }

    public String getWhereCause() {
        return whereCause;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setFeeTable(String feeTable) {
        this.feeTable = feeTable;
    }

    public String getFeeTable() {
        return feeTable;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getLength() {
        return length;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "GlFieldBO [codeTable=" + codeTable + ", fieldName=" + fieldName + ", fieldId=" + fieldId
                + ", fieldProperty=" + fieldProperty + ", fieldDesc=" + fieldDesc + ", fieldType=" + fieldType
                + ", whereCause=" + whereCause + ", defaultValue=" + defaultValue + ", feeTable=" + feeTable
                + ", length=" + length + "]";
    }

}
