package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description StatementToSapVO对象
 * <AUTHOR> 
 * @date 2023-12-28 14:35:43  
 */
public class StatementToSapBO extends BaseBO {	
	 /** 
    * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
    */ 
    
    private static final long serialVersionUID = 1L;
    /** 
	* @Fields statementPeriod :  D-日;M-月
 	*/ 
	private String statementPeriod;
		 /** 
	* @Fields fileName :  文件名称
 	*/ 
	private String fileName;
			 /** 
	* @Fields varianceData :  差异说明
 	*/ 
	private String varianceData;
	 /** 
	* @Fields reviewStatus :  复核通过状态，使用码表T
 	*/ 
	private String reviewStatus;
	 /** 
	* @Fields listId :  主键序列
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields statementData :  日结数据
 	*/ 
	private String statementData;
					
	 public void setStatementPeriod(String statementPeriod) {
		this.statementPeriod = statementPeriod;
	}
	
	public String getStatementPeriod() {
		return statementPeriod;
	}
		 public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	public String getFileName() {
		return fileName;
	}
	 public void setReviewStatus(String reviewStatus) {
		this.reviewStatus = reviewStatus;
	}
	
	public String getReviewStatus() {
		return reviewStatus;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	
					
	@Override
    public String getBizId() {
        return null;
    }
    
    public String getVarianceData() {
        return varianceData;
    }

    public void setVarianceData(String varianceData) {
        this.varianceData = varianceData;
    }

    public String getStatementData() {
        return statementData;
    }

    public void setStatementData(String statementData) {
        this.statementData = statementData;
    }

    @Override
    public String toString() {
        return "StatementToSapVO [" +
				"statementPeriod="+statementPeriod+","+
"fileName="+fileName+","+
"varianceData="+varianceData+","+
"reviewStatus="+reviewStatus+","+
"listId="+listId+","+
"statementData="+statementData+"]";
    }
}
