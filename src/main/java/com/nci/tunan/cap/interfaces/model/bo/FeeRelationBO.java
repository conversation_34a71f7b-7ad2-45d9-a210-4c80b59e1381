package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class FeeRelationBO extends BaseBO{

    @Override
    public String getBizId() {
        return null;
    }
    
    /** 
     * @Fields serialVersionUID : 序列化
     */ 
    private static final long serialVersionUID = 2533018778748178624L;
    /** 
     * @Fields serialVersionUID : 流水号
     */ 
    private BigDecimal listId;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields relUnitNumber : 关联应收应付流水标识
     */
    private String relUnitNumber;
    /**
     * @Fields feeRelType : 关联类型：01-重复支付
     */
    private String feeRelType;
    /**
     * @Fields insertBy : 记录插入人INSERT_BY
     */
    private BigDecimal insertBy;
    /**
     * @Fields insertBy : 记录更新人UPDATE_BY
     */
    private BigDecimal updateBy;
    /**
     * @Fields insertTime : 记录插入时间INSERT_TIME
     */
    private Date insertTime;
    /**
     * @Fields insertTime : 记录更新时间UPDATE_TIME
     */
    private Date updateTime;
    /**
     * @Fields insertTimestamp : 记录插入时间戳INSERT_TIMESTAMP，默认值SYSDATE
     */
    private Date insertTimestamp;
    /**
     * @Fields updateTimestamp : 记录更新时间戳UPDATE_TIMESTAMP，默认值SYSDATE
     */
    private Date updateTimestamp;
    public BigDecimal getListId() {
        return listId;
    }
    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }
    public String getUnitNumber() {
        return unitNumber;
    }
    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }
    public String getRelUnitNumber() {
        return relUnitNumber;
    }
    public void setRelUnitNumber(String relUnitNumber) {
        this.relUnitNumber = relUnitNumber;
    }
    public String getFeeRelType() {
        return feeRelType;
    }
    public void setFeeRelType(String feeRelType) {
        this.feeRelType = feeRelType;
    }
    public BigDecimal getInsertBy() {
        return insertBy;
    }
    public void setInsertBy(BigDecimal insertBy) {
        this.insertBy = insertBy;
    }
    public BigDecimal getUpdateBy() {
        return updateBy;
    }
    public void setUpdateBy(BigDecimal updateBy) {
        this.updateBy = updateBy;
    }
    public Date getInsertTime() {
        return insertTime;
    }
    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public Date getInsertTimestamp() {
        return insertTimestamp;
    }
    public void setInsertTimestamp(Date insertTimestamp) {
        this.insertTimestamp = insertTimestamp;
    }
    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }
    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
    @Override
    public String toString() {
        return "FeeRelationBO [listId=" + listId + ", unitNumber=" + unitNumber + ", relUnitNumber=" + relUnitNumber
                + ", feeRelType=" + feeRelType + ", insertBy=" + insertBy + ", updateBy=" + updateBy + ", insertTime="
                + insertTime + ", updateTime=" + updateTime + ", insertTimestamp=" + insertTimestamp
                + ", updateTimestamp=" + updateTimestamp + "]";
    }
    
    
}
