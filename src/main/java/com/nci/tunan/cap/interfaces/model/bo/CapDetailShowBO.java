package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 与实收付和应收付公共信息的映射
 * <AUTHOR>
 * @date 2015-7-20 下午6:42:07
 * @.belongToModule CAP-收付费系统 
 */
public class CapDetailShowBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */
    private static final long serialVersionUID = 5666768459550288616L;
    /**
     * @Fields payMode : 收付费方式
     */
    private String payMode;
    /** 
    * @Fields policyCode : 保单号码 
    */ 
    private String policyCode;
    /**
     * @Fields capOrganCode : 收付费机构
     */
    private String capOrganCode;
    /**
     * @Fields organCode : 管理机构
     */
    private String policyOrganCode;
    /**
     * @Fields feeSum : 收付金额
     */
    private BigDecimal feeSum;
    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;

    /**
     * @Fields arapFeeSum : 应收应付金额 (对应 应收应付明细表中的fee_amount字段)
     */
    private BigDecimal arapFeeSum;
    /**
     * @Fields dueTime : 应收应付日期
     */
    private Date dueTime;
    /**
     * @Fields feeStatus : (收付状态)费用状态01；待收付 02；已收付 03；回退 04；中止 05；不可收付
     */
    private String feeStatus;
    /**
     * @Fields businessCode : 业务号
     */
    private String businessCode;
    /**
     * @Fields finishTime : 核销日期(对应 实收实付明细表中的finish_time字段)
     */
    private Date finishTime;
    /**
     * @Fields payNo : 实付号码(对应 实收实付明细表中的 payrefno 字段)
     */
    private String payrefno;
    /**
     * @Fields operator : 操作员姓名(对应 实收实付表中的insert_by字段)
     */
    private BigDecimal insertBy;
    /**
     * @Fields insertTime : 实际收/付费日期
     */
    private Date insertTime;

    /** 
    * @Fields unitNumber : 应收付业务流水标识 
    */ 
    private String unitNumber;

    /** 
    * @Fields derivType : 业务来源 
    */ 
    private String derivType; 

    /** 
    * @Fields businessType : 业务类型代码 
    */ 
    private String businessType; 

    /** 
    * @Fields feeAmount : 收付金额
    */ 
    private BigDecimal feeAmount;
    /** 
    * @Fields busiProdName : 险种名称 
    */ 
    private String busiProdName;
    
    /**
     * @Fields operatorBy : 操作员姓名(对应 实收实付表中的operator_by字段)
     */
    private BigDecimal operatorBy;

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public BigDecimal getFeeSum() {
        return feeSum;
    }

    public void setFeeSum(BigDecimal feeSum) {
        this.feeSum = feeSum;
    }

    public BigDecimal getArapFeeSum() {
        return arapFeeSum;
    }

    public void setArapFeeSum(BigDecimal arapFeeSum) {
        this.arapFeeSum = arapFeeSum;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public String getPayrefno() {
        return payrefno;
    }

    public void setPayrefno(String payrefno) {
        this.payrefno = payrefno;
    }

    public BigDecimal getInsertBy() {
        return insertBy;
    }

    public void setInsertBy(BigDecimal insertBy) {
        this.insertBy = insertBy;
    }

    
    public BigDecimal getOperatorBy() {
		return operatorBy;
	}

	public void setOperatorBy(BigDecimal operatorBy) {
		this.operatorBy = operatorBy;
	}

	@Override
    public String toString() {
        return "CapDetailShowBO [policyCode=" + policyCode + ", capOrganCode=" + capOrganCode + ", policyOrganCode="
                + policyOrganCode + ", feeSum=" + feeSum + ", payeeName=" + payeeName + ", businessCode="
                + businessCode + ", arapFeeSum=" + arapFeeSum + ", dueTime=" + dueTime + ", feeStatus=" + feeStatus
                + ", finishTime=" + finishTime + ", payrefno=" + payrefno + ", insertBy=" + insertBy + ", insertTime="
                + insertTime + ", unitNumber=" + unitNumber + ", derivType=" + derivType + ", businessType="
                + businessType + ", feeAmount=" + feeAmount + ", busiProdName=" + busiProdName + ", operatorBy=" + operatorBy + "]";
    }

    @Override
    public String getBizId() {
        return null;
    }
}
