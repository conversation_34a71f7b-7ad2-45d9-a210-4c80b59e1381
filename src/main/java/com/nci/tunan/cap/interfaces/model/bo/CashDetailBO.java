package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;
/**
 * @description CashDetailBO对象
 * @<NAME_EMAIL>
 * @date 2016-2-1 下午3:05:18
 * @.belongToModule 收付费-转实收
 */
public class CashDetailBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 转实收
     */

    private static final long serialVersionUID = 1L;

    /**
     * @Fields invoiceNo : 发票号
     */
    private String invoiceNo;
    
    /**
     * @Fields receiptStartTime : 发票打印开始日期
     */
    private Date receiptStartTime;
    
    /**
     * @Fields receiptEndTime : 发票打印结束日期
     */
    private Date receiptEndTime;
    /**
     * @Fields receiptBatchStatus : 发票批次状态
     */
    private String receiptBatchStatus;
    
    /**
     * @Fields taskMark : 发票批处理进行时，提高效率 收付费操作，无具体业务意义
     */
    private BigDecimal taskMark;
    /**
     * @Fields policyOrganCode : 保单直属机构
     */
    private String policyOrganCode;
    /**
     * @Fields isItemMain : 主组合产品标识
     */
    private BigDecimal isItemMain;
    /**
     * @Fields holderName : 投保人姓名
     */
    private String holderName;
    /**
     * @Fields payrefno : 一次实收产生的收付号
     */
    private String payrefno;
    /**
     * @Fields bookkeepingId : 记账中间表主键ID
     */
    private BigDecimal bookkeepingId;
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields paidCount : 缴费次数
     */
    private BigDecimal paidCount;
    /**
     * @Fields customerAccountFlag : 是否使用客户账户标识
     */
    private BigDecimal customerAccountFlag;
    /**
     * @Fields feeType : 费用业务类型
     */
    private String feeType;
    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;
    /**
     * @Fields organCode : 系统内往来对方机构 管理机构
     */
    private String organCode;
    /**
     * @Fields redBookkeepingTime : 红冲记录记账时间
     */
    private Date redBookkeepingTime;
    /**
     * @Fields channelType : 渠道类型
     */
    private String channelType;
    /**
     * @Fields handlerName : 经办人姓名
     */
    private String handlerName;
    /**
     * @Fields arapBankAccount : 收/付款方银行账号
     */
    private String arapBankAccount;
    /**
     * @Fields chargeYear : 缴费年期
     */
    private BigDecimal chargeYear;
    /**
     * @Fields isRiskMain : 主险附加险标识
     */
    private BigDecimal isRiskMain;
    /**
     * @Fields bookkeepingFlag : 是否记账
     */
    private BigDecimal bookkeepingFlag;
    /**
     * @Fields redBookkeepingBy : 红冲记账操作人
     */
    private BigDecimal redBookkeepingBy;
    /**
     * @Fields arapBelnr : 付费凭证号/暂收据号
     */
    private String arapBelnr;
    /**
     * @Fields insuredName : 被保人姓名
     */
    private String insuredName;
    /**
     * @Fields receiptCount : 票据打印次数
     */
    private BigDecimal receiptCount;
    /**
     * @Fields insuredId : 被保人ID
     */
    private BigDecimal insuredId;
    /**
     * @Fields productChannel : 产品渠道
     */
    private String productChannel;
    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;
    /**
     * @Fields payMode : 收付方式 10;现金 11;现金送款薄 20;支票 21;现金支票 22;转账支票 30;银行转账
     *         31;银行转账（非制返盘） 32;银行转账（制返盘） 33;网银转账 40;实时收费 41;POS收款 50;内部转账
     *         51;普通内部转账 52;预存内部转账 60;银保通 70;第三方支付 80;客户账户 90;其它
     */
    private String payMode;
    /**
     * @Fields operatorBy : 业务操作员（出单人员)
     */
    private BigDecimal operatorBy;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;
    /**
     * @Fields businessType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String businessType;
    /**
     * @Fields receiptStatus : 打印状态 1;可打印 2;已打印 3;打印中
     */
    private String receiptStatus;
    /**
     * @Fields actualBankAccount : 实际到帐银行账号
     */
    private String actualBankAccount;
    /**
     * @Fields redBelnr : SAP红冲凭证号
     */
    private String redBelnr;
    /**
     * @Fields groupCode : 产品组合代码
     */
    private String groupCode;
    /**
     * @Fields certiType : 收付款人证件类型
     */
    private String certiType;
    /**
     * @Fields actualBankCode : 实际到帐银行编码
     */
    private String actualBankCode;
    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;
    /**
     * @Fields arapBankCode : 收/付款方银行
     */
    private String arapBankCode;
    /**
     * @Fields redBookkeepingFlag : 红冲标识
     */
    private BigDecimal redBookkeepingFlag;
    /**
     * @Fields redBookkeepingReason : 红冲原因
     */
    private String redBookkeepingReason;
    /**
     * @Fields cipDistrictBankCode : 分行代码
     */
    private String cipDistrictBankCode;
    /**
     * @Fields customerId : 收付款人 ID
     */
    private BigDecimal customerId;
    /**
     * @Fields finishTime : 费用到帐时间
     */
    private Date finishTime;
    /**
     * @Fields receiptTime : 打印操作时间
     */
    private Date receiptTime;
    /**
     * @Fields isReceipt : 是否允许打印票据
     */
    private BigDecimal isReceipt;
    /**
     * @Fields dueTime : 应缴应付日
     */
    private Date dueTime;
    /**
     * @Fields certiCode : 收付款人证件号码
     */
    private String certiCode;
    /**
     * @Fields groupName : 产品组合名称
     */
    private String groupName;
    /**
     * @Fields cipBankCode : 银行代码
     */
    private String cipBankCode;
    /**
     * @Fields belnr : SAP回写凭证号信息
     */
    private String belnr;
    /**
     * @Fields cipBranchBankCode : 网点代码
     */
    private String cipBranchBankCode;
    /**
     * @Fields feeAmount : 收付金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields handlerType : 经办人证件类型
     */
    private String handlerType;
    /**
     * @Fields serviceCode : 付款项目
     */
    private String serviceCode;
    /**
     * @Fields holderId : 投保人编码
     */
    private BigDecimal holderId;
    /**
     * @Fields receiptBy : 打印操作人
     */
    private BigDecimal receiptBy;
    /**
     * @Fields withdrawType : 记账业务类型
     */
    private String withdrawType;
    /**
     * @Fields rollbackUnitNumber : 回退应收付业务流水标识
     */
    private String rollbackUnitNumber;
    /**
     * @Fields policyYear : 年度当前年度 待确认，次数？年？
     */
    private BigDecimal policyYear;
    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;
    /**
     * @Fields capOrganCode : 收付费机构
     */
    private String capOrganCode;
    /**
     * @Fields feeStatus : 收付状态 收付完成
     */
    private String feeStatus;
    /**
     * @Fields derivType : 业务来源 001;新契约 003;续保 004;保全 005;理赔
     */
    private String derivType;
    /**
     * @Fields redBookkeepingId : 红冲记账中间表主键ID
     */
    private BigDecimal redBookkeepingId;
    /**
     * @Fields bookkeepingBy : 记账操作人
     */
    private BigDecimal bookkeepingBy;
    /**
     * @Fields handlerCode : 经办人证件号码
     */
    private String handlerCode;
    /**
     * @Fields cashDetailId : 实收付明细流水号
     */
    private BigDecimal cashDetailId;
    /**
     * @Fields arapFlag : 收付类型
     */
    private String arapFlag;
    /**
     * @Fields refeflag : 参考项
     */
    private String refeflag;
    /**
     * @Fields bookkeepingTime : 记录记账时间
     */
    private Date bookkeepingTime;
    /**
     * @Fields agentCode : 业务员CODE
     */
    private String agentCode;
    /**
     * @Fields premFreq : 年缴、月缴、趸交等等 1;趸缴 2;月缴 3;季缴 4;半年缴 5;年缴 6;不定期缴 9;其他
     */
    private BigDecimal premFreq;
    /**
     * @Fields insertBy : 记录插入人
     */
    private BigDecimal insertBy;
    /** 
    * @Fields typename : 类型名称
    */ 
    private String typename;
    /** 
    * @Fields modename : 方式名称
    */ 
    private String modename;
    /** 
    * @Fields feesum : 金额合计
    */ 
    private BigDecimal feesum;
    /** 
    * @Fields organName : 机构名称
    */ 
    private String organName;
    /** 
    * @Fields capOrganName : 收付机构名称
    */ 
    private String capOrganName;
    /** 
    * @Fields insertbeginTime : 插入起始时间
    */ 
    private String insertbeginTime;
    /** 
    * @Fields insertendTime : 插入截止时间
    */ 
    private String insertendTime;
    /** 
    * @Fields newprodCode : 新产品码 
    */ 
    private String newprodCode;
  
    /**
     * @Fields realName : 操作员姓名
     */
    private String realName;
    /**
     * @Fields invoiceCount : 发票打印时，判断发票拆分个数
     */
    private int invoiceCount;
    
    /** 
    * @Fields receiptId : 收据ID
    */ 
    private BigDecimal receiptId;
    
    /**
     * @Fields arapDate : 费用到帐时间
     */
    private Date arapDate;
    
    /** 
     * @Fields isBlockingGL : 是否阻断记账(1阻断;0或者空不阻断)
     */ 
    private String isBlockingGL;
    
    /**
     * @Fields tradeSn : 订单号
     */
    private String tradeSn; 
    /**
     * @Fields applyCode : 订单号
     */
    private String applyCode; 
    /**
     * @Fields payInformation : 支付商信息
     */
    private String payInformation;
    /**
     * @Fields applyCode :银行名称
     */
    private String bankName;
    
    /**
     * @Fields specialAccountFlag : 特殊账户标记  1-中银保信个人养老金账户
     */
     private String specialAccountFlag;
     
     /**
     * @Fields partiAppSheetNo : 申请单编号
     */
    private String partiAppSheetNo;
    
    /**
     * @Fields chequeNo : 支票号码
     */
     private String chequeNo;
     
     /**
      * @Fields StartDate : 收费开始日期
      */
     private Date startDate;

     /**
      * @Fields EndDate : 收费结束日期
      */
     private Date endDate;
     
     /** 
      * @Fields platform : 平台来源
      */ 
    private String submitChannel;
    
    /** 
     * @Fields noteCode : 资金系统返回的交易流水号，用于EAST报送
     */ 
    private String noteCode;
    
    /**
     * @Fields @Fields : 是否再次请求扣费，1-是、0（或空）-否
     */
    private String payAgain;
    
    /**
     * 对公标识
     */
    private String isCorporate;

	public String getIsCorporate() {
        return isCorporate;
    }

    public void setIsCorporate(String isCorporate) {
        this.isCorporate = isCorporate;
    }

    public String getPayAgain() {
		return payAgain;
	}

	public void setPayAgain(String payAgain) {
		this.payAgain = payAgain;
	}

	public String getNoteCode() {
        return noteCode;
    }

    public void setNoteCode(String noteCode) {
        this.noteCode = noteCode;
    }

    public String getSubmitChannel() {
        return submitChannel;
    }

    public void setSubmitChannel(String submitChannel) {
        this.submitChannel = submitChannel;
    }

    public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getChequeNo() {
		return chequeNo;
	}

	public void setChequeNo(String chequeNo) {
		this.chequeNo = chequeNo;
	}

	public String getPartiAppSheetNo() {
		return partiAppSheetNo;
	}

	public void setPartiAppSheetNo(String partiAppSheetNo) {
		this.partiAppSheetNo = partiAppSheetNo;
	}

	public String getSpecialAccountFlag() {
		return specialAccountFlag;
	}

	public void setSpecialAccountFlag(String specialAccountFlag) {
		this.specialAccountFlag = specialAccountFlag;
	}
    
	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getTradeSn() {
		return tradeSn;
	}

	public void setTradeSn(String tradeSn) {
		this.tradeSn = tradeSn;
	}

	public String getPayInformation() {
		return payInformation;
	}

	public void setPayInformation(String payInformation) {
		this.payInformation = payInformation;
	}

	public String getNewprodCode() {
		return newprodCode;
	}

	public void setNewprodCode(String newprodCode) {
		this.newprodCode = newprodCode;
	}

	public String getIsBlockingGL() {
        return isBlockingGL;
    }

    public void setIsBlockingGL(String isBlockingGL) {
        this.isBlockingGL = isBlockingGL;
    }
    
    public Date getArapDate() {
        return arapDate;
    }

    public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    public BigDecimal getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(BigDecimal receiptId) {
        this.receiptId = receiptId;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public Date getReceiptStartTime() {
        return receiptStartTime;
    }

    public void setReceiptStartTime(Date receiptStartTime) {
        this.receiptStartTime = receiptStartTime;
    }

    public Date getReceiptEndTime() {
        return receiptEndTime;
    }

    public void setReceiptEndTime(Date receiptEndTime) {
        this.receiptEndTime = receiptEndTime;
    }

    public String getReceiptBatchStatus() {
        return receiptBatchStatus;
    }

    public void setReceiptBatchStatus(String receiptBatchStatus) {
        this.receiptBatchStatus = receiptBatchStatus;
    }

    public void setTaskMark(BigDecimal taskMark) {
        this.taskMark = taskMark;
    }

    public BigDecimal getTaskMark() {
        return taskMark;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setIsItemMain(BigDecimal isItemMain) {
        this.isItemMain = isItemMain;
    }

    public BigDecimal getIsItemMain() {
        return isItemMain;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setPayrefno(String payrefno) {
        this.payrefno = payrefno;
    }

    public String getPayrefno() {
        return payrefno;
    }

    public void setBookkeepingId(BigDecimal bookkeepingId) {
        this.bookkeepingId = bookkeepingId;
    }

    public BigDecimal getBookkeepingId() {
        return bookkeepingId;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public void setCustomerAccountFlag(BigDecimal customerAccountFlag) {
        this.customerAccountFlag = customerAccountFlag;
    }

    public BigDecimal getCustomerAccountFlag() {
        return customerAccountFlag;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setRedBookkeepingTime(Date redBookkeepingTime) {
        this.redBookkeepingTime = redBookkeepingTime;
    }

    public Date getRedBookkeepingTime() {
        return redBookkeepingTime;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }

    public String getHandlerName() {
        return handlerName;
    }

    public void setArapBankAccount(String arapBankAccount) {
        this.arapBankAccount = arapBankAccount;
    }

    public String getArapBankAccount() {
        return arapBankAccount;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public void setIsRiskMain(BigDecimal isRiskMain) {
        this.isRiskMain = isRiskMain;
    }

    public BigDecimal getIsRiskMain() {
        return isRiskMain;
    }

    public void setBookkeepingFlag(BigDecimal bookkeepingFlag) {
        this.bookkeepingFlag = bookkeepingFlag;
    }

    public BigDecimal getBookkeepingFlag() {
        return bookkeepingFlag;
    }

    public void setRedBookkeepingBy(BigDecimal redBookkeepingBy) {
        this.redBookkeepingBy = redBookkeepingBy;
    }

    public BigDecimal getRedBookkeepingBy() {
        return redBookkeepingBy;
    }

    public void setArapBelnr(String arapBelnr) {
        this.arapBelnr = arapBelnr;
    }

    public String getArapBelnr() {
        return arapBelnr;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setReceiptCount(BigDecimal receiptCount) {
        this.receiptCount = receiptCount;
    }

    public BigDecimal getReceiptCount() {
        return receiptCount;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setOperatorBy(BigDecimal operatorBy) {
        this.operatorBy = operatorBy;
    }

    public BigDecimal getOperatorBy() {
        return operatorBy;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setReceiptStatus(String receiptStatus) {
        this.receiptStatus = receiptStatus;
    }

    public String getReceiptStatus() {
        return receiptStatus;
    }

    public void setActualBankAccount(String actualBankAccount) {
        this.actualBankAccount = actualBankAccount;
    }

    public String getActualBankAccount() {
        return actualBankAccount;
    }

    public void setRedBelnr(String redBelnr) {
        this.redBelnr = redBelnr;
    }

    public String getRedBelnr() {
        return redBelnr;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    public void setActualBankCode(String actualBankCode) {
        this.actualBankCode = actualBankCode;
    }

    public String getActualBankCode() {
        return actualBankCode;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setArapBankCode(String arapBankCode) {
        this.arapBankCode = arapBankCode;
    }

    public String getArapBankCode() {
        return arapBankCode;
    }

    public void setRedBookkeepingFlag(BigDecimal redBookkeepingFlag) {
        this.redBookkeepingFlag = redBookkeepingFlag;
    }

    public BigDecimal getRedBookkeepingFlag() {
        return redBookkeepingFlag;
    }

    public void setRedBookkeepingReason(String redBookkeepingReason) {
        this.redBookkeepingReason = redBookkeepingReason;
    }

    public String getRedBookkeepingReason() {
        return redBookkeepingReason;
    }

    public void setCipDistrictBankCode(String cipDistrictBankCode) {
        this.cipDistrictBankCode = cipDistrictBankCode;
    }

    public String getCipDistrictBankCode() {
        return cipDistrictBankCode;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setReceiptTime(Date receiptTime) {
        this.receiptTime = receiptTime;
    }

    public Date getReceiptTime() {
        return receiptTime;
    }

    public void setIsReceipt(BigDecimal isReceipt) {
        this.isReceipt = isReceipt;
    }

    public BigDecimal getIsReceipt() {
        return isReceipt;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setCipBankCode(String cipBankCode) {
        this.cipBankCode = cipBankCode;
    }

    public String getCipBankCode() {
        return cipBankCode;
    }

    public void setBelnr(String belnr) {
        this.belnr = belnr;
    }

    public String getBelnr() {
        return belnr;
    }

    public void setCipBranchBankCode(String cipBranchBankCode) {
        this.cipBranchBankCode = cipBranchBankCode;
    }

    public String getCipBranchBankCode() {
        return cipBranchBankCode;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setHandlerType(String handlerType) {
        this.handlerType = handlerType;
    }

    public String getHandlerType() {
        return handlerType;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setHolderId(BigDecimal holderId) {
        this.holderId = holderId;
    }

    public BigDecimal getHolderId() {
        return holderId;
    }

    public void setReceiptBy(BigDecimal receiptBy) {
        this.receiptBy = receiptBy;
    }

    public BigDecimal getReceiptBy() {
        return receiptBy;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setRollbackUnitNumber(String rollbackUnitNumber) {
        this.rollbackUnitNumber = rollbackUnitNumber;
    }

    public String getRollbackUnitNumber() {
        return rollbackUnitNumber;
    }

    public void setPolicyYear(BigDecimal policyYear) {
        this.policyYear = policyYear;
    }

    public BigDecimal getPolicyYear() {
        return policyYear;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setRedBookkeepingId(BigDecimal redBookkeepingId) {
        this.redBookkeepingId = redBookkeepingId;
    }

    public BigDecimal getRedBookkeepingId() {
        return redBookkeepingId;
    }

    public void setBookkeepingBy(BigDecimal bookkeepingBy) {
        this.bookkeepingBy = bookkeepingBy;
    }

    public BigDecimal getBookkeepingBy() {
        return bookkeepingBy;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    public String getHandlerCode() {
        return handlerCode;
    }

    public void setCashDetailId(BigDecimal cashDetailId) {
        this.cashDetailId = cashDetailId;
    }

    public BigDecimal getCashDetailId() {
        return cashDetailId;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setRefeflag(String refeflag) {
        this.refeflag = refeflag;
    }

    public String getRefeflag() {
        return refeflag;
    }

    public void setBookkeepingTime(Date bookkeepingTime) {
        this.bookkeepingTime = bookkeepingTime;
    }

    public Date getBookkeepingTime() {
        return bookkeepingTime;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public BigDecimal getInsertBy() {
        return insertBy;
    }

    public void setInsertBy(BigDecimal insertBy) {
        this.insertBy = insertBy;
    }

    public String getTypename() {
        return typename;
    }

    public void setTypename(String typename) {
        this.typename = typename;
    }

    public String getModename() {
        return modename;
    }

    public void setModename(String modename) {
        this.modename = modename;
    }

    public BigDecimal getFeesum() {
        return feesum;
    }

    public void setFeesum(BigDecimal feesum) {
        this.feesum = feesum;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getCapOrganName() {
        return capOrganName;
    }

    public void setCapOrganName(String capOrganName) {
        this.capOrganName = capOrganName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public int getInvoiceCount() {
        return invoiceCount;
    }

    public void setInvoiceCount(int invoiceCount) {
        this.invoiceCount = invoiceCount;
    }

    public String getInsertbeginTime() {
        return insertbeginTime;
    }

    public void setInsertbeginTime(String insertbeginTime) {
        this.insertbeginTime = insertbeginTime;
    }

    public String getInsertendTime() {
        return insertendTime;
    }

    public void setInsertendTime(String insertendTime) {
        this.insertendTime = insertendTime;
    }

	@Override
	public String toString() {
		return "CashDetailBO [invoiceNo=" + invoiceNo + ", receiptStartTime="
				+ receiptStartTime + ", receiptEndTime=" + receiptEndTime
				+ ", receiptBatchStatus=" + receiptBatchStatus + ", taskMark="
				+ taskMark + ", policyOrganCode=" + policyOrganCode
				+ ", isItemMain=" + isItemMain + ", holderName=" + holderName
				+ ", payrefno=" + payrefno + ", bookkeepingId=" + bookkeepingId
				+ ", busiProdName=" + busiProdName + ", unitNumber="
				+ unitNumber + ", paidCount=" + paidCount
				+ ", customerAccountFlag=" + customerAccountFlag + ", feeType="
				+ feeType + ", busiProdCode=" + busiProdCode + ", organCode="
				+ organCode + ", redBookkeepingTime=" + redBookkeepingTime
				+ ", channelType=" + channelType + ", handlerName="
				+ handlerName + ", arapBankAccount=" + arapBankAccount
				+ ", chargeYear=" + chargeYear + ", isRiskMain=" + isRiskMain
				+ ", bookkeepingFlag=" + bookkeepingFlag
				+ ", redBookkeepingBy=" + redBookkeepingBy + ", arapBelnr="
				+ arapBelnr + ", insuredName=" + insuredName
				+ ", receiptCount=" + receiptCount + ", insuredId=" + insuredId
				+ ", productChannel=" + productChannel + ", policyCode="
				+ policyCode + ", payMode=" + payMode + ", operatorBy="
				+ operatorBy + ", branchCode=" + branchCode + ", businessType="
				+ businessType + ", receiptStatus=" + receiptStatus
				+ ", actualBankAccount=" + actualBankAccount + ", redBelnr="
				+ redBelnr + ", groupCode=" + groupCode + ", certiType="
				+ certiType + ", actualBankCode=" + actualBankCode
				+ ", moneyCode=" + moneyCode + ", businessCode=" + businessCode
				+ ", payeeName=" + payeeName + ", arapBankCode=" + arapBankCode
				+ ", redBookkeepingFlag=" + redBookkeepingFlag
				+ ", redBookkeepingReason=" + redBookkeepingReason
				+ ", cipDistrictBankCode=" + cipDistrictBankCode
				+ ", customerId=" + customerId + ", finishTime=" + finishTime
				+ ", receiptTime=" + receiptTime + ", isReceipt=" + isReceipt
				+ ", dueTime=" + dueTime + ", certiCode=" + certiCode
				+ ", groupName=" + groupName + ", cipBankCode=" + cipBankCode
				+ ", belnr=" + belnr + ", cipBranchBankCode="
				+ cipBranchBankCode + ", feeAmount=" + feeAmount
				+ ", handlerType=" + handlerType + ", serviceCode="
				+ serviceCode + ", holderId=" + holderId + ", receiptBy="
				+ receiptBy + ", withdrawType=" + withdrawType
				+ ", rollbackUnitNumber=" + rollbackUnitNumber
				+ ", policyYear=" + policyYear + ", bankUserName="
				+ bankUserName + ", capOrganCode=" + capOrganCode
				+ ", feeStatus=" + feeStatus + ", derivType=" + derivType
				+ ", redBookkeepingId=" + redBookkeepingId + ", bookkeepingBy="
				+ bookkeepingBy + ", handlerCode=" + handlerCode
				+ ", cashDetailId=" + cashDetailId + ", arapFlag=" + arapFlag
				+ ", refeflag=" + refeflag + ", bookkeepingTime="
				+ bookkeepingTime + ", agentCode=" + agentCode + ", premFreq="
				+ premFreq + ", insertBy=" + insertBy + ", typename="
				+ typename + ", modename=" + modename + ", feesum=" + feesum
				+ ", organName=" + organName + ", capOrganName=" + capOrganName
				+ ", insertbeginTime=" + insertbeginTime + ", insertendTime="
				+ insertendTime + ", newprodCode=" + newprodCode
				+ ", realName=" + realName + ", invoiceCount=" + invoiceCount
				+ ", receiptId=" + receiptId + ", arapDate=" + arapDate
				+ ", isBlockingGL=" + isBlockingGL + "]";
	}


	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}



	

   

}
