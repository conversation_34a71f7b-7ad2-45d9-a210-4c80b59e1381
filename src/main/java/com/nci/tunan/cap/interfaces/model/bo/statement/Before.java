package com.nci.tunan.cap.interfaces.model.bo.statement;

public class Before {
    
    /** 
     * @Fields SERIAL_NUM : 序号
     */ 
     private String SERIAL_NUM;
     
     /** 
    * @Fields BF_BUSI_PROD_CODE : 原险种编码/原险种代码
    */ 
    private String BF_BUSI_PROD_CODE;
    
    /** 
    * @Fields BF_BUSI_PROD_NAME : 原险种 
    */ 
    private String BF_BUSI_PROD_NAME;
    
    /** 
    * @Fields CHANNEL_TYPE : 销售渠道 
    */ 
    private String CHANNEL_TYPE;
    
    /** 
     * @Fields AGENTCOM : 代理机构
     */ 
    private String AGENTCOM;
     
     /** 
      * @Fields AMOUNT : 金额/本日金额
      */ 
    private String AMOUNT;
      
    /** 
    * @Fields TRANSFER_OUT_FEE : 转出费用 
    */ 
    private String TRANSFER_OUT_FEE;
    
    /** 
    * @Fields FEE_AMOUNT_TAX : 增值税
    */ 
    private String FEE_AMOUNT_TAX;
    
    /** 
    * @Fields PIECES : 件数
    */ 
    private String PIECES;
    
    /** 
     * @Fields PREM_INCOM_TAX : 保费收入增值税
     */ 
    private String PREM_INCOM_TAX;
    
    /** 
     * @Fields FIRST_DEDUCT : 初始扣费
     */ 
    private String FIRST_DEDUCT;
    
    /** 
     * @Fields SURRENDER_FEE : 退保费用
     */ 
    private String SURRENDER_FEE;
    
    /** 
     * @Fields OTHER_AMOUNT_TAX : 其他收入增值税
     */ 
    private String OTHER_AMOUNT_TAX;
    
    

    public String getOTHER_AMOUNT_TAX() {
        return OTHER_AMOUNT_TAX;
    }

    public void setOTHER_AMOUNT_TAX(String oTHER_AMOUNT_TAX) {
        OTHER_AMOUNT_TAX = oTHER_AMOUNT_TAX;
    }

    public String getSURRENDER_FEE() {
        return SURRENDER_FEE;
    }

    public void setSURRENDER_FEE(String sURRENDER_FEE) {
        SURRENDER_FEE = sURRENDER_FEE;
    }

    public String getFIRST_DEDUCT() {
        return FIRST_DEDUCT;
    }

    public void setFIRST_DEDUCT(String fIRST_DEDUCT) {
        FIRST_DEDUCT = fIRST_DEDUCT;
    }

    public String getPREM_INCOM_TAX() {
        return PREM_INCOM_TAX;
    }

    public void setPREM_INCOM_TAX(String pREM_INCOM_TAX) {
        PREM_INCOM_TAX = pREM_INCOM_TAX;
    }

    public String getSERIAL_NUM() {
        return SERIAL_NUM;
    }

    public void setSERIAL_NUM(String sERIAL_NUM) {
        SERIAL_NUM = sERIAL_NUM;
    }

    public String getBF_BUSI_PROD_CODE() {
        return BF_BUSI_PROD_CODE;
    }

    public void setBF_BUSI_PROD_CODE(String bF_BUSI_PROD_CODE) {
        BF_BUSI_PROD_CODE = bF_BUSI_PROD_CODE;
    }

    public String getBF_BUSI_PROD_NAME() {
        return BF_BUSI_PROD_NAME;
    }

    public void setBF_BUSI_PROD_NAME(String bF_BUSI_PROD_NAME) {
        BF_BUSI_PROD_NAME = bF_BUSI_PROD_NAME;
    }

    public String getCHANNEL_TYPE() {
        return CHANNEL_TYPE;
    }

    public void setCHANNEL_TYPE(String cHANNEL_TYPE) {
        CHANNEL_TYPE = cHANNEL_TYPE;
    }

    public String getAGENTCOM() {
        return AGENTCOM;
    }

    public void setAGENTCOM(String aGENTCOM) {
        AGENTCOM = aGENTCOM;
    }

    public String getAMOUNT() {
        return AMOUNT;
    }

    public void setAMOUNT(String aMOUNT) {
        AMOUNT = aMOUNT;
    }

    public String getTRANSFER_OUT_FEE() {
        return TRANSFER_OUT_FEE;
    }

    public void setTRANSFER_OUT_FEE(String tRANSFER_OUT_FEE) {
        TRANSFER_OUT_FEE = tRANSFER_OUT_FEE;
    }

    public String getFEE_AMOUNT_TAX() {
        return FEE_AMOUNT_TAX;
    }

    public void setFEE_AMOUNT_TAX(String fEE_AMOUNT_TAX) {
        FEE_AMOUNT_TAX = fEE_AMOUNT_TAX;
    }

    public String getPIECES() {
        return PIECES;
    }

    public void setPIECES(String pIECES) {
        PIECES = pIECES;
    }
    
    

}
