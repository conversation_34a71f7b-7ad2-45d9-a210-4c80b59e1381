package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.tunan.cap.util.CapVerify;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * 
 * @description VSM请求对象
 * @<NAME_EMAIL>
 * @date 2016年6月17日 上午10:22:23
 * @.belongToModule 收付费-VMS
 */
@XStreamAlias("DataList")
public class PremArapVMSBO {
    
    /** 
     * @Fields kpCode :  开票项目代码
     */ 
     @XStreamAlias("KPSPBS")
     private String kpCode = "";
     
     /** 
      * @Fields customerType :  客户类型
      */ 
      @XStreamAlias("KHLX")
      private String customerType = "";

    /** 
    * @Fields transCode :  流水号
    */ 
    @XStreamAlias("LSH")
    private String batchNo = "";
    
    @XStreamAlias("JYJG")
    private String organCode = ""; 
    /** 
    * @Fields branchCode :  公司代码
    */ 
    @XStreamAlias("FRAM_CODE")
    private String branchCode = ""; 
    /** 
    * @Fields holderName : 投保人名称
    */ 
    @XStreamAlias("TBRXM")
    private String holderName = ""; 
    /** 
    * @Fields businessCode :  业务编号
    */ 
    @XStreamAlias("YWBH")
    private String businessCode = ""; 
    /** 
    * @Fields validateDate : 交易日期
    */ 
    @XStreamAlias("JYRQ")
    private String validateDate = ""; 
    /** 
    * @Fields busiProdCode :  险种代码
    */ 
    @XStreamAlias("XZDM")
    private String busiProdCode = ""; 
    /** 
    * @Fields productAbbrName :  险种简称
    */ 
    @XStreamAlias("XZJC")
    private String productAbbrName = ""; 
    /** 
    * @Fields busiProdName :  险种全称
    */ 
    @XStreamAlias("XZQC")
    private String busiProdName = ""; 
    /** 
    * @Fields serviceCode :  保全项目代码
    */ 
    @XStreamAlias("BQXMDM")
    private String serviceCode = ""; 
    /** 
    * @Fields serviceName : 保全项目名称
    */ 
    @XStreamAlias("BQXMMC")
    private String serviceName = ""; 
    /** 
    * @Fields policyType : 是否个人业务
    */ 
    @XStreamAlias("ISGRYW")
    private String policyType = ""; 
    /** 
    * @Fields feeAmount :  含税金额
    */ 
    @XStreamAlias("HSJE")
    private String feeAmount = ""; 
    /** 
    * @Fields moneyCode :  币种代码
    */ 
    @XStreamAlias("BZBM")
    private String moneyCode = ""; 
    /** 
    * @Fields hl :  汇率
    */ 
    @XStreamAlias("HL")
    private String hl = ""; 
    /** 
    * @Fields jyrmbje :  外币折算后人民币金额
    */ 
    @XStreamAlias("JYRMBJE")
    private String jyrmbje = ""; 
    /** 
    * @Fields amount :  不含税金额
    */ 
    @XStreamAlias("JE")
    private String amount = ""; 
    /** 
    * @Fields taxRate :  税率
    */ 
    @XStreamAlias("SYSL")
    private String taxRate = ""; 
    /** 
    * @Fields taxamount :  税额
    */ 
    @XStreamAlias("HSSE")
    private String taxamount = ""; 
    /** 
    * @Fields applyCode :  投保单号
    */ 
    @XStreamAlias("TBDH")
    private String applyCode = ""; 
    /** 
    * @Fields agentCode : 服务人员编号
    */ 
    @XStreamAlias("FWRYBH")
    private String agentCode = ""; 
    /** 
    * @Fields agentName : 服务人员姓名 
    */ 
    @XStreamAlias("FWRYXM")
    private String agentName = ""; 
    /** 
    * @Fields area : 区
    */ 
    @XStreamAlias("QMC")
    private String area = ""; 
    /** 
    * @Fields part : 部
    */ 
    @XStreamAlias("BMC")
    private String part = ""; 
    /** 
    * @Fields group : 组
    */ 
    @XStreamAlias("ZMC")
    private String group = ""; 
    /** 
    * @Fields chargeYearStart : 缴费起期
    */ 
    @XStreamAlias("JFQSRQ")
    private String chargeYearStart = ""; 
    /** 
    * @Fields chargeYearEnd : 缴费止期
    */ 
    @XStreamAlias("JFJZRQ")
    private String chargeYearEnd = ""; 
    /** 
    * @Fields oldBussCode : 原业务编号 
    */ 
    @XStreamAlias("YYWBH")
    private String oldBussCode = ""; 
    /** 
    * @Fields lyxt : 来源系统
    */ 
    @XStreamAlias("LYXT")
    private String lyxt = "xhx"; 
    /** 
    * @Fields issqyw : 是否首期业务
    */ 
    @XStreamAlias("ISSQYW")
    private String issqyw = ""; 
    /** 
    * @Fields payeePhone : 手机号
    */ 
    @XStreamAlias("SJH")
    private String payeePhone = ""; 
    /** 
    * @Fields payeeEmail : 电子邮箱
    */ 
    @XStreamAlias("DZYX")
    private String payeeEmail = ""; 
    /** 
    * @Fields certiType : TODO(用一句话描述这个变量表示什么) 
    */ 
    @XStreamAlias("ZJLX")
    private String certiType = ""; // 证件类型
    /** 
    * @Fields certiCode : 证件号码
    */ 
    @XStreamAlias("ZJHM")
    private String certiCode = ""; 
    /** 
    * @Fields hjdh : 汇交单号(需求不确定传什么值，目前传空)
    */ 
    @XStreamAlias("HJDH")
    private String hjdh = ""; 
    /** 
    * @Fields xpxslh : 学平险受理号(需求不确定传什么值，目前传空)
    */ 
    @XStreamAlias("XPXSLH")
    private String xpxslh = ""; 
    /** 
    * @Fields nsrsbh : 纳税人识别号(需求不确定传什么值，目前传空)
    */ 
    @XStreamAlias("NSRSBH")
    private String nsrsbh = ""; 
    /** 
    * @Fields derivType :  业务来源
    */ 
    @XStreamAlias("YWLX")
    private String derivType = ""; 
    /** 
    * @Fields salecom : 保单来源(传空)
    */ 
    @XStreamAlias("SALECOM")
    private String salecom = ""; 
    /** 
    * @Fields sysbm : 税优识别码(传空)
    * #34582 关于增加核心业务系统向VMS系统传输字段的需求  add by wenzh
    */ 
    @XStreamAlias("SYSBM")
    private String tradeSno=""; 
    /** 
    * @Fields jffs :  缴费方式(传空)
    */ 
    @XStreamAlias("JFFS")
    private String jffs="";  
    // <ISSQYW>是否首期业务</ISSQYW>
    // <SJH>手机号</SJH>
    // <DZYX>电子邮箱</DZYX>
    // <ZJLX>证件类型</ZJLX>
    // <ZJHM>证件号码</ZJHM>
    // < HJDH >汇交单号</ HJDH >
    // < XPXSLH >学平险受理号< /XPXSLH >
    // <NSRSBH >纳税人识别号<NSRSBH />

    /** 
     * @Fields policyCode : 保单号
     */ 
    @XStreamAlias("BDH")
    private String policyCode;

	/** 
	* @Fields EXPCOL1 : 预留字段1
	*/ 
	@XStreamOmitField
    private String EXPCOL1; 
    /** 
    * @Fields EXPCOL2 : 预留字段2
    */ 
    @XStreamOmitField
    private String EXPCOL2; 
    /** 
    * @Fields EXPCOL3 :  预留字段3
    */ 
    @XStreamOmitField
    private String EXPCOL3; 
    /** 
    * @Fields EXPCOL4 : 预留字段4
    */ 
    @XStreamOmitField
    private String EXPCOL4; 
    /** 
    * @Fields EXPCOL5 : 预留字段5
    */ 
    @XStreamOmitField
    private String EXPCOL5; 

    /** 
    * @Fields unitNumber : 收付唯一号
    */ 
    @XStreamOmitField
    private String unitNumber;
    
    /**
     * @Fields listId : 流水号，无业务含义
     */
    @XStreamOmitField
    private BigDecimal listId;
    
    /**
     * @Fields feeType : 费用类型
     */
	@XStreamOmitField
    private String feeType;

	
    public String getKpCode() {
        return kpCode;
    }

    public void setKpCode(String kpCode) {
        this.kpCode = kpCode;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getFeeType() {
		return feeType;
	}

	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}

	public BigDecimal getListId() {
		return listId;
	}

	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = strEquals(organCode);
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = strEquals(holderName);
    }

    public String getValidateDate() {
        return validateDate;
    }

    public void setValidateDate(String validateDate) {
        String str = strEquals(validateDate);
        if (!StringUtilsEx.isBlank(str)) {
            Date parse = DateUtilsEx.formatToDate(validateDate, "dd/MM/yyyy");
            str = DateUtilsEx.formatToString(parse, "yyyy-MM-dd");
        }
        this.validateDate = str;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = strEquals(busiProdCode);
    }

    public String getProductAbbrName() {
        return productAbbrName;
    }

    public void setProductAbbrName(String productAbbrName) {
        this.productAbbrName = strEquals(productAbbrName);
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = strEquals(busiProdName);
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = strEquals(serviceCode);
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = strEquals(serviceName);
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(String feeAmount) {
        String str = strEquals(feeAmount);
        if (!StringUtilsEx.isBlank(str)) {
            BigDecimal b = new BigDecimal(str);
            BigDecimal setScale = b.setScale(2, BigDecimal.ROUND_HALF_UP);
            str = setScale.toString();
        }
        this.feeAmount = str;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = strEquals(moneyCode);
    }

    public String getHl() {
        return hl;
    }

    public void setHl(String hl) {
        this.hl = strEquals(hl);
    }

    public String getJyrmbje() {
        return jyrmbje;
    }

    public void setJyrmbje(String jyrmbje) {
        String str = strEquals(jyrmbje);
        if (!StringUtilsEx.isBlank(str)) {
            BigDecimal b = new BigDecimal(str);
            BigDecimal setScale = b.setScale(2, BigDecimal.ROUND_HALF_UP);
            str = setScale.toString();
        }
        this.jyrmbje = str;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        String str = strEquals(amount);
        if (!StringUtilsEx.isBlank(str)) {
            BigDecimal b = new BigDecimal(str);
            BigDecimal setScale = b.setScale(2, BigDecimal.ROUND_HALF_UP);
            str = setScale.toString();
        }
        this.amount = str;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = strEquals(taxRate);
    }

    public String getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(String taxamount) {
        String str = strEquals(taxamount);
        if (!StringUtilsEx.isBlank(str)) {
            BigDecimal b = new BigDecimal(str);
            BigDecimal setScale = b.setScale(2, BigDecimal.ROUND_HALF_UP);
            str = setScale.toString();
        }
        this.taxamount = str;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = strEquals(applyCode);
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = strEquals(agentCode);
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = strEquals(agentName);
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = strEquals(area);
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = strEquals(part);
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = strEquals(group);
    }

    public String getChargeYearStart() {
        return chargeYearStart;
    }

    public void setChargeYearStart(String chargeYearStart) {
        String str = strEquals(chargeYearStart);
        if (!StringUtilsEx.isBlank(str)) {
            Date parse = DateUtilsEx.formatToDate(chargeYearStart, "dd/MM/yyyy");
            str = DateUtilsEx.formatToString(parse, "yyyy-MM-dd");
        }
        this.chargeYearStart = str;

    }

    public String getChargeYearEnd() {
        return chargeYearEnd;
    }

    public void setChargeYearEnd(String chargeYearEnd) {
        String str = strEquals(chargeYearEnd);
        if (!StringUtilsEx.isBlank(str)) {
            Date parse = DateUtilsEx.formatToDate(chargeYearEnd, "dd/MM/yyyy");
            str = DateUtilsEx.formatToString(parse, "yyyy-MM-dd");
        }
        this.chargeYearEnd = str;
    }

    public String getOldBussCode() {
        return oldBussCode;
    }

    public void setOldBussCode(String oldBussCode) {
        this.oldBussCode = strEquals(oldBussCode);
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = strEquals(derivType);
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = strEquals(policyCode);
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = strEquals(unitNumber);
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = strEquals(branchCode);
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = strEquals(businessCode);
    }

    public String getIssqyw() {
        return issqyw;
    }

    public void setIssqyw(String issqyw) {
        this.issqyw = issqyw;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = CapVerify.ternaryOperator(StringUtilsEx.isBlank(payeePhone), "", "");
    }

    public String getPayeeEmail() {
        return payeeEmail;
    }

    public void setPayeeEmail(String payeeEmail) {
        this.payeeEmail = strEquals(payeeEmail);
    }

    public String getCertiType() {
        return certiType;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getHjdh() {
        return hjdh;
    }

    public void setHjdh(String hjdh) {
        this.hjdh = hjdh;
    }

    public String getXpxslh() {
        return xpxslh;
    }

    public void setXpxslh(String xpxslh) {
        this.xpxslh = xpxslh;
    }

    public String getNsrsbh() {
        return nsrsbh;
    }

    public void setNsrsbh(String nsrsbh) {
        this.nsrsbh = nsrsbh;
    }
    
    public String getLyxt() {
        return lyxt;
    }
    
    public void setLyxt(String lyxt) {
        this.lyxt = lyxt;
    }
    
    public String getSalecom() {
        return salecom;
    }

    public void setSalecom(String salecom) {
        this.salecom = salecom;
    }

    public String getEXPCOL1() {
        return EXPCOL1;
    }

    public void setEXPCOL1(String eXPCOL1) {
        EXPCOL1 = eXPCOL1;
    }

    public String getEXPCOL2() {
        return EXPCOL2;
    }

    public void setEXPCOL2(String eXPCOL2) {
        EXPCOL2 = eXPCOL2;
    }

    public String getEXPCOL3() {
        return EXPCOL3;
    }

    public void setEXPCOL3(String eXPCOL3) {
        EXPCOL3 = eXPCOL3;
    }

    public String getEXPCOL4() {
        return EXPCOL4;
    }

    public void setEXPCOL4(String eXPCOL4) {
        EXPCOL4 = eXPCOL4;
    }

    public String getEXPCOL5() {
        return EXPCOL5;
    }

    public void setEXPCOL5(String eXPCOL5) {
        EXPCOL5 = eXPCOL5;
    }
    /**
   	 * @return the sysbm
   	 */
   	public String getTradSno() {
   		return tradeSno;
   	}

   	/**
   	 * @param sysbm the sysbm to set
   	 */
   	public void setTradSno(String tradSeno) {
   		this.tradeSno = strEquals(tradSeno);
   	}

   	/**
   	 * @return the jffs
   	 */
   	public String getJffs() {
   		return jffs;
   	}

   	/**
   	 * @param jffs the jffs to set
   	 */
   	public void setJffs(String jffs) {
   		this.jffs = strEquals(jffs);
   	}

    private String strEquals(String parem) {
        return CapVerify.ternaryOperator(StringUtilsEx.isBlank(parem), "", parem);
    }
}