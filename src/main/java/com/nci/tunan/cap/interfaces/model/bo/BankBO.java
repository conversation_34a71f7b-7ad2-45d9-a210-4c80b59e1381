package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description BankBO对象
 * <AUTHOR>
 * @date 2015-07-03 13:54:22
 * @.belongToModule 收付费-银行信息查询
 * 
 */
public class BankBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化版本号
     */

    private static final long serialVersionUID = 5814545619512836464L;
    /**
     * @Fields listId : 实时业务银行配置表ID（t_bank_cfg）
     */
    private BigDecimal listId;
    /**
     * @Fields addressId : 地址编号
     */
    private BigDecimal addressId;

    /**
     * @Fields parentBank : 银行网点上级机构
     */
    private String parentBank;
    /**
     * @Fields deptId : 专管员直属部门代码
     */
    private BigDecimal deptId;
    /**
     * @Fields branchId : 被代理机构所属分公司编号
     */
    private BigDecimal branchId;
    /**
     * @Fields idType : 注册类型编号
     */
    private String idType;
    /**
     * @Fields status : 网点代理状态
     */
    private String status;
    /**
     * @Fields idNumber : 注册数
     */
    private String idNumber;
    /**
     * @Fields transferType : 转帐分组类型
     */
    private String transferType;
    /**
     * @Fields agencyType : 代理业务类型
     */
    private String agencyType;
    /**
     * @Fields channelType : 渠道类别
     */
    private String channelType;
    /**
     * @Fields recallDate : 网点撤消时间
     */
    private Date recallDate;
    /**
     * @Fields updaterId : 记录更新人
     */
    private BigDecimal updaterId;
    /**
     * @Fields recorderId : 记录插入人
     */
    private BigDecimal recorderId;
    /**
     * @Fields bankOrgId : 银行中心支公司编号
     */
    private BigDecimal bankOrgId;
    /**
     * @Fields organId : 被代理机构的代码
     */
    private BigDecimal organId;
    /**
     * @Fields leaderId : 负责人代码
     */
    private BigDecimal leaderId;
    /**
     * @Fields headId : 被代理机构所属总公司编号
     */
    private BigDecimal headId;
    /**
     * @Fields bankClass : 银行级别
     */
    private String bankClass;
    /**
     * @Fields isBasic : 是否基础代码
     */
    private String isBasic;
    /**
     * @Fields companyId : 公司客户编号
     */
    private BigDecimal companyId;
    /**
     * @Fields telephone : 联系电话
     */
    private String telephone;
    /**
     * @Fields bankName : 银行名称
     */
    private String bankName;
    /**
     * @Fields comebackDate : 网点恢复时间
     */
    private Date comebackDate;
    /**
     * @Fields managerId : 保险公司专管员代码
     */
    private BigDecimal managerId;
    /**
     * @Fields abbrName : 银行机构简称
     */
    private String abbrName;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields createDate : 网点新建时间
     */
    private Date createDate;
    /**
     * @Fields bankType : 银行机构类别
     */
    private String bankType;
    /**
     * @Fields branchBank : 分支银行
     */
    private String branchBank;
    /**
     * @Fields internalCode : 银行内部的机构代码
     */
    private String internalCode;
    /**
     * @Fields businessCate : 业务性质
     */
    private String businessCate;

    public BigDecimal getListId() {
		return listId;
	}

	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}

	public void setAddressId(BigDecimal addressId) {
        this.addressId = addressId;
    }

    public BigDecimal getAddressId() {
        return addressId;
    }

    public void setDeptId(BigDecimal deptId) {
        this.deptId = deptId;
    }

    public BigDecimal getDeptId() {
        return deptId;
    }

    public void setParentBank(String parentBank) {
        this.parentBank = parentBank;
    }

    public String getParentBank() {
        return parentBank;
    }

    public void setBranchId(BigDecimal branchId) {
        this.branchId = branchId;
    }

    public BigDecimal getBranchId() {
        return branchId;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdType() {
        return idType;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

    public String getTransferType() {
        return transferType;
    }

    public void setAgencyType(String agencyType) {
        this.agencyType = agencyType;
    }

    public String getAgencyType() {
        return agencyType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setRecallDate(Date recallDate) {
        this.recallDate = recallDate;
    }

    public Date getRecallDate() {
        return recallDate;
    }

    public void setUpdaterId(BigDecimal updaterId) {
        this.updaterId = updaterId;
    }

    public BigDecimal getUpdaterId() {
        return updaterId;
    }

    public void setRecorderId(BigDecimal recorderId) {
        this.recorderId = recorderId;
    }

    public BigDecimal getRecorderId() {
        return recorderId;
    }

    public void setBankOrgId(BigDecimal bankOrgId) {
        this.bankOrgId = bankOrgId;
    }

    public BigDecimal getBankOrgId() {
        return bankOrgId;
    }

    public void setOrganId(BigDecimal organId) {
        this.organId = organId;
    }

    public BigDecimal getOrganId() {
        return organId;
    }

    public void setLeaderId(BigDecimal leaderId) {
        this.leaderId = leaderId;
    }

    public BigDecimal getLeaderId() {
        return leaderId;
    }

    public void setHeadId(BigDecimal headId) {
        this.headId = headId;
    }

    public BigDecimal getHeadId() {
        return headId;
    }

    public void setBankClass(String bankClass) {
        this.bankClass = bankClass;
    }

    public String getBankClass() {
        return bankClass;
    }

    public void setIsBasic(String isBasic) {
        this.isBasic = isBasic;
    }

    public String getIsBasic() {
        return isBasic;
    }

    public void setCompanyId(BigDecimal companyId) {
        this.companyId = companyId;
    }

    public BigDecimal getCompanyId() {
        return companyId;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setComebackDate(Date comebackDate) {
        this.comebackDate = comebackDate;
    }

    public Date getComebackDate() {
        return comebackDate;
    }

    public void setManagerId(BigDecimal managerId) {
        this.managerId = managerId;
    }

    public BigDecimal getManagerId() {
        return managerId;
    }

    public void setAbbrName(String abbrName) {
        this.abbrName = abbrName;
    }

    public String getAbbrName() {
        return abbrName;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBranchBank(String branchBank) {
        this.branchBank = branchBank;
    }

    public String getBranchBank() {
        return branchBank;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getInternalCode() {
        return internalCode;
    }

    public void setBusinessCate(String businessCate) {
        this.businessCate = businessCate;
    }

    public String getBusinessCate() {
        return businessCate;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "BankBO [addressId=" + addressId + ", deptId=" + deptId + ", parentBank=" + parentBank + ", branchId="
                + branchId + ", idType=" + idType + ", status=" + status + ", idNumber=" + idNumber + ", transferType="
                + transferType + ", agencyType=" + agencyType + ", channelType=" + channelType + ", recallDate="
                + recallDate + ", updaterId=" + updaterId + ", recorderId=" + recorderId + ", bankOrgId=" + bankOrgId
                + ", organId=" + organId + ", leaderId=" + leaderId + ", headId=" + headId + ", bankClass=" + bankClass
                + ", isBasic=" + isBasic + ", companyId=" + companyId + ", telephone=" + telephone + ", bankName="
                + bankName + ", comebackDate=" + comebackDate + ", managerId=" + managerId + ", abbrName=" + abbrName
                + ", bankCode=" + bankCode + ", createDate=" + createDate + ", bankType=" + bankType + ", branchBank="
                + branchBank + ", internalCode=" + internalCode + ", businessCate=" + businessCate + "]";
    }
}
