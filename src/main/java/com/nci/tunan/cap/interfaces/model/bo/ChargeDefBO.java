package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description 银保通收费机构配置表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL> 
 * @date 2017-03-22 14:09:46  
 */
public class ChargeDefBO extends BaseBO {	
	
	/** 
	* @Fields serialVersionUID : 序列化 
	*/ 
	private static final long serialVersionUID = -1042829128633723080L;
	/** 
	* @Fields organCode :  管理机构
 	*/ 
	private String organCode;
	 /** 
	* @Fields bankClass :  银行级别
 	*/ 
	private String bankClass;
	 /** 
	* @Fields chargeCom :  收费机构
 	*/ 
	private String chargeCom;
	 /** 
	* @Fields listId :  流水号
 	*/ 
	private BigDecimal listId;
		
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
	 public void setBankClass(String bankClass) {
		this.bankClass = bankClass;
	}
	
	public String getBankClass() {
		return bankClass;
	}
	 public void setChargeCom(String chargeCom) {
		this.chargeCom = chargeCom;
	}
	
	public String getChargeCom() {
		return chargeCom;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ChargeDefBO [" +
				"organCode="+organCode+","+
"bankClass="+bankClass+","+
"chargeCom="+chargeCom+","+
"listId="+listId+"]";
    }
}
