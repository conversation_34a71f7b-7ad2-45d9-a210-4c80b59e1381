package com.nci.tunan.cap.interfaces.model.bo.statement;

public class After {

    /**
     * @Fields SERIAL_NUM : 序号
     */
    private String SERIAL_NUM;

    /**
     * @Fields AF_BUSI_PROD_CODE : 转换后险种编码/权益转移后险种代码
     */
    private String AF_BUSI_PROD_CODE;
    /**
     * @Fields AF_BUSI_PROD_NAME : 转换后险种/权益转移后险种
     */
    private String AF_BUSI_PROD_NAME;

    /**
     * @Fields CHANNEL_TYPE : 销售渠道
     */
    private String CHANNEL_TYPE;

    /**
     * @Fields AGENTCOM : 代理机构
     */
    private String AGENTCOM;

    /**
     * @Fields AMOUNT : 金额/本日金额
     */
    private String AMOUNT;

    /** 
     * @Fields TRANSFER_IN_FEE : 转入费用 
     */ 
     private String TRANSFER_IN_FEE;

    /**
     * @Fields FEE_AMOUNT_TAX : 增值税
     */
    private String FEE_AMOUNT_TAX;

    /**
     * @Fields PIECES : 件数
     */
    private String PIECES;
    
    /** 
     * @Fields PREM_INCOM_TAX : 保费收入增值税
     */ 
    private String PREM_INCOM_TAX;
    
    /** 
     * @Fields FIRST_DEDUCT : 初始扣费
     */ 
    private String FIRST_DEDUCT;
    
    /** 
     * @Fields SURRENDER_FEE : 退保费用
     */ 
    private String SURRENDER_FEE;
    
    /** 
     * @Fields OTHER_AMOUNT_TAX : 其他收入增值税
     */ 
    private String OTHER_AMOUNT_TAX;
    
    

    public String getPREM_INCOM_TAX() {
        return PREM_INCOM_TAX;
    }

    public void setPREM_INCOM_TAX(String pREM_INCOM_TAX) {
        PREM_INCOM_TAX = pREM_INCOM_TAX;
    }

    public String getFIRST_DEDUCT() {
        return FIRST_DEDUCT;
    }

    public void setFIRST_DEDUCT(String fIRST_DEDUCT) {
        FIRST_DEDUCT = fIRST_DEDUCT;
    }

    public String getSURRENDER_FEE() {
        return SURRENDER_FEE;
    }

    public void setSURRENDER_FEE(String sURRENDER_FEE) {
        SURRENDER_FEE = sURRENDER_FEE;
    }

    public String getOTHER_AMOUNT_TAX() {
        return OTHER_AMOUNT_TAX;
    }

    public void setOTHER_AMOUNT_TAX(String oTHER_AMOUNT_TAX) {
        OTHER_AMOUNT_TAX = oTHER_AMOUNT_TAX;
    }

    public String getSERIAL_NUM() {
        return SERIAL_NUM;
    }

    public void setSERIAL_NUM(String sERIAL_NUM) {
        SERIAL_NUM = sERIAL_NUM;
    }

    public String getCHANNEL_TYPE() {
        return CHANNEL_TYPE;
    }

    public void setCHANNEL_TYPE(String cHANNEL_TYPE) {
        CHANNEL_TYPE = cHANNEL_TYPE;
    }

    public String getAGENTCOM() {
        return AGENTCOM;
    }

    public void setAGENTCOM(String aGENTCOM) {
        AGENTCOM = aGENTCOM;
    }

    public String getAMOUNT() {
        return AMOUNT;
    }

    public void setAMOUNT(String aMOUNT) {
        AMOUNT = aMOUNT;
    }

    public String getTRANSFER_IN_FEE() {
        return TRANSFER_IN_FEE;
    }

    public void setTRANSFER_IN_FEE(String tRANSFER_IN_FEE) {
        TRANSFER_IN_FEE = tRANSFER_IN_FEE;
    }

    public String getFEE_AMOUNT_TAX() {
        return FEE_AMOUNT_TAX;
    }

    public void setFEE_AMOUNT_TAX(String fEE_AMOUNT_TAX) {
        FEE_AMOUNT_TAX = fEE_AMOUNT_TAX;
    }

    public String getPIECES() {
        return PIECES;
    }

    public void setPIECES(String pIECES) {
        PIECES = pIECES;
    }

    public String getAF_BUSI_PROD_CODE() {
        return AF_BUSI_PROD_CODE;
    }

    public void setAF_BUSI_PROD_CODE(String aF_BUSI_PROD_CODE) {
        AF_BUSI_PROD_CODE = aF_BUSI_PROD_CODE;
    }

    public String getAF_BUSI_PROD_NAME() {
        return AF_BUSI_PROD_NAME;
    }

    public void setAF_BUSI_PROD_NAME(String aF_BUSI_PROD_NAME) {
        AF_BUSI_PROD_NAME = aF_BUSI_PROD_NAME;
    }

}
