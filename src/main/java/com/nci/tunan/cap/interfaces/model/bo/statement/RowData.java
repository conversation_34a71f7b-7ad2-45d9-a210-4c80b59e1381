package com.nci.tunan.cap.interfaces.model.bo.statement;


/** 
 * @description 日结推送报文-会总行信息
 * <AUTHOR> <EMAIL> 
 * @date 2024-1-11 下午2:41:11  
*/
public class RowData {
    /** 
    * @Fields SERIAL_NUM : 序号
    */ 
    private String SERIAL_NUM;
    /** 
    * @Fields AGENTCOM : 代理机构
    */ 
    private String AGENTCOM ;
    /** 
    * @Fields BUSI_PROD_CODE : 险种编码/险种代码
    */ 
    private String BUSI_PROD_CODE;
    /** 
    * @Fields BUSI_PROD_NAME : 险种/险种名称
    */ 
    private String BUSI_PROD_NAME;
    /** 
    * @Fields BF_BUSI_PROD_CODE : 原险种编码/原险种代码            
    */ 
    private String BF_BUSI_PROD_CODE;
    /** 
    * @Fields BF_BUSI_PROD_NAME : 原险种                      
    */ 
    private String BF_BUSI_PROD_NAME;     
    /** 
    * @Fields AF_BUSI_PROD_CODE : 转换后险种编码/权益转移后险种代码
    */ 
    private String AF_BUSI_PROD_CODE;
    /** 
    * @Fields AF_BUSI_PROD_NAME : 转换后险种/权益转移后险种        
    */ 
    private String AF_BUSI_PROD_NAME;
    /** 
    * @Fields CHANNEL_TYPE : 销售渠道
    */ 
    private String CHANNEL_TYPE;
    /** 
    * @Fields CHARGE_YEAR : 交费年限/缴费年限
    */ 
    private String CHARGE_YEAR;
    /** 
    * @Fields AMOUNT : 金额/本日金额                    
    */ 
    private String AMOUNT;
    /** 
    * @Fields FEE_AMOUNT_TAX : 增值税
    */ 
    private String FEE_AMOUNT_TAX;
    /** 
    * @Fields PIECES : 件数
    */ 
    private String PIECES;
    /** 
    * @Fields LOAN_CAPITAL : 保单质押贷款本金
    */ 
    private String LOAN_CAPITAL;
    /** 
    * @Fields LOAN_INTEREST : 保单贷款质押利息/保单质押贷款利息
    */ 
    private String LOAN_INTEREST;
    /** 
    * @Fields WITHHOLD_STAMP_DUTY : 代扣印花税金额 
    */ 
    private String WITHHOLD_STAMP_DUTY;
    /** 
    * @Fields FEE_AMOUNT : 应付金额 
    */ 
    private String FEE_AMOUNT;
    /** 
    * @Fields CHARGE_COUNT : 交费次数 
    */ 
    private String CHARGE_COUNT;
    /** 
    * @Fields FIRST_DEDUCT : 初始扣费 
    */ 
    private String FIRST_DEDUCT;
    /** 
    * @Fields OTHER_AMOUNT_TAX : 其他收入增值税/其它收入增值税
    */ 
    private String OTHER_AMOUNT_TAX;
    /** 
    * @Fields RISK_PREM : 风险保费 
    */ 
    private String RISK_PREM;
    /** 
    * @Fields PREM : 保费
    */ 
    private String PREM ;
    /** 
    * @Fields INTEREST : 利息/本日利息
    */ 
    private String INTEREST ;
    /** 
    * @Fields INTEREST_AMOUNT_TAX : 利息收入增值税 
    */ 
    private String INTEREST_AMOUNT_TAX ;
    /** 
    * @Fields PAY_AMOUNT : 给付金额 
    */ 
    private String PAY_AMOUNT;
    /** 
    * @Fields ADJUST_AMOUNT : 调整金额 
    */ 
    private String ADJUST_AMOUNT;
    /** 
    * @Fields RECEIVE_TYPE : 领取方式 
    */ 
    private String RECEIVE_TYPE;
    /** 
    * @Fields HEALTH_CONSULT_FEE : 应付健康服务费-咨询费
    */ 
    private String HEALTH_CONSULT_FEE;
    /** 
    * @Fields HEALTH_CHECKUP_FEE : 应付健康服务费-体检费
    */ 
    private String HEALTH_CHECKUP_FEE;
    /** 
    * @Fields COLLECTION_POUNDAGE : 部分领取手续费 
    */ 
    private String COLLECTION_POUNDAGE ;
    /** 
    * @Fields SURRENDER_FEE : 退保费用 
    */ 
    private String SURRENDER_FEE;
    /** 
    * @Fields POLICY_MANAGE_FEE_PERIOD : 保单管理费 
    */ 
    private String POLICY_MANAGE_FEE_PERIOD;
    /** 
    * @Fields ASSET_M_FEE : 资产和账户管理费 
    */ 
    private String ASSET_M_FEE;
    /** 
    * @Fields DEFER_TAX_AMOUNT : 递延税款金额/延税款金额
    */ 
    private String DEFER_TAX_AMOUNT;
    /** 
    * @Fields WITHHOLD_TAX_INCENTIVE : 代扣税收优惠 
    */ 
    private String WITHHOLD_TAX_INCENTIVE;
    /** 
    * @Fields CASH_BONUS : 现金红利/现金红利金额
    */ 
    private String CASH_BONUS;
    /** 
    * @Fields REISSUE_CASH_INTEREST : 补发红利利息 
    */ 
    private String REISSUE_CASH_INTEREST ;
    /** 
    * @Fields FLAT_COST : 工本费 
    */ 
    private String FLAT_COST;
    /** 
    * @Fields PREM_INCOM_TAX : 保费收入增值税 
    */ 
    private String PREM_INCOM_TAX;
    /** 
    * @Fields TRANSFER_OUT_FEE : 转出费用 
    */ 
    private String TRANSFER_OUT_FEE;
    /** 
    * @Fields TRANSFER_IN_FEE : 转入费用 
    */ 
    private String TRANSFER_IN_FEE;
    /** 
    * @Fields LOAN_AMOUNT : 贷款金额 
    */ 
    private String LOAN_AMOUNT;
    /** 
    * @Fields HEALTH_SERVICE_FEE : 健康服务管理费 
    */ 
    private String HEALTH_SERVICE_FEE;
    /** 
    * @Fields APPLY_CODE : 投保单号 
    */ 
    private String APPLY_CODE;
    /** 
    * @Fields REFUND_TYPE : 退费类型 
    */ 
    private String REFUND_TYPE;
    /** 
    * @Fields FEE_TYPE : 收费类型/付费类型
    */ 
    private String FEE_TYPE ;
    /** 
    * @Fields INSURANCE_CONTRACT : 当日小计-保险合同
    */ 
    private String INSURANCE_CONTRACT;
    /** 
    * @Fields INVEST_CONTRACT : 当日小计-非保险合同
    */ 
    private String INVEST_CONTRACT;
    /** 
    * @Fields DAY_TOTAL : 当日小计/本日小计
    */ 
    private String DAY_TOTAL;
    /** 
    * @Fields PAY_MODE : 收费方式/付费方式
    */ 
    private String PAY_MODE ;
    /** 
    * @Fields CAP_BANK : 收费银行 
    */ 
    private String CAP_BANK ;
    /** 
    * @Fields PREM_PAY_MODE : 交费类型 
    */ 
    private String PREM_PAY_MODE;
    /** 
    * @Fields INTERNAL_TYPE : 内转类型 
    */ 
    private String INTERNAL_TYPE;
    /** 
    * @Fields INTERNAL_PAY_MODE : 收付费方式 
    */ 
    private String INTERNAL_PAY_MODE ;
    /** 
    * @Fields CLAIM_ITEM : 理赔项目 
    */ 
    private String CLAIM_ITEM;
    /** 
    * @Fields CLAIM_PAY_TYPE : 给付金类型 
    */ 
    private String CLAIM_PAY_TYPE;
    /** 
    * @Fields DEDUCT_ITEM : 扣回项目 
    */ 
    private String DEDUCT_ITEM;
    /** 
    * @Fields OVER_ACCOUNT_BALANCE : 超账户价值给付 
    */ 
    private String OVER_ACCOUNT_BALANCE;
    /** 
    * @Fields ADVANCE_ITEM : 预付项目 
    */ 
    private String ADVANCE_ITEM;
    /** 
    * @Fields OVER_COMP_PAY : 超期补偿金额 
    */ 
    private String OVER_COMP_PAY;
    /** 
    * @Fields CLAIM_BUSI_PROD_CODE : 险种代码（出险险种） 
    */ 
    private String CLAIM_BUSI_PROD_CODE;
    /** 
    * @Fields CLAIM_BUSI_PROD_NAME : 险种（出险险种） 
    */ 
    private String CLAIM_BUSI_PROD_NAME;
    /** 
    * @Fields WAIVE_BUSI_PROD_CODE : 险种代码（豁免险种） 
    */ 
    private String WAIVE_BUSI_PROD_CODE;
    /** 
    * @Fields WAIVE_BUSI_PROD_NAME : 险种（豁免险种）
    */ 
    private String WAIVE_BUSI_PROD_NAME;
    
    /** 
     * @Fields Before : 转换前数据
     */ 
    private Before Before;
     
     /** 
      * @Fields After : 转换后数据
      */ 
    private After After;
      
    /** 
    * @Fields claim : 出险险种
    */ 
    private Claim Claim;
    
    /** 
    * @Fields waive : 豁免险种
    */ 
    private Waive Waive;
    
    public Claim getClaim() {
        return Claim;
    }
    public void setClaim(Claim claim) {
        Claim = claim;
    }
    public Waive getWaive() {
        return Waive;
    }
    public void setWaive(Waive waive) {
        Waive = waive;
    }
    public Before getBefore() {
        return Before;
    }
    public void setBefore(Before before) {
        Before = before;
    }
    public After getAfter() {
        return After;
    }
    public void setAfter(After after) {
        After = after;
    }
    public String getSERIAL_NUM() {
        return SERIAL_NUM;
    }
    public void setSERIAL_NUM(String sERIAL_NUM) {
        SERIAL_NUM = sERIAL_NUM;
    }
    public String getAGENTCOM() {
        return AGENTCOM;
    }
    public void setAGENTCOM(String aGENTCOM) {
        AGENTCOM = aGENTCOM;
    }
    public String getBUSI_PROD_CODE() {
        return BUSI_PROD_CODE;
    }
    public void setBUSI_PROD_CODE(String bUSI_PROD_CODE) {
        BUSI_PROD_CODE = bUSI_PROD_CODE;
    }
    public String getBUSI_PROD_NAME() {
        return BUSI_PROD_NAME;
    }
    public void setBUSI_PROD_NAME(String bUSI_PROD_NAME) {
        BUSI_PROD_NAME = bUSI_PROD_NAME;
    }
    public String getBF_BUSI_PROD_CODE() {
        return BF_BUSI_PROD_CODE;
    }
    public void setBF_BUSI_PROD_CODE(String bF_BUSI_PROD_CODE) {
        BF_BUSI_PROD_CODE = bF_BUSI_PROD_CODE;
    }
    public String getBF_BUSI_PROD_NAME() {
        return BF_BUSI_PROD_NAME;
    }
    public void setBF_BUSI_PROD_NAME(String bF_BUSI_PROD_NAME) {
        BF_BUSI_PROD_NAME = bF_BUSI_PROD_NAME;
    }
    public String getAF_BUSI_PROD_CODE() {
        return AF_BUSI_PROD_CODE;
    }
    public void setAF_BUSI_PROD_CODE(String aF_BUSI_PROD_CODE) {
        AF_BUSI_PROD_CODE = aF_BUSI_PROD_CODE;
    }
    public String getAF_BUSI_PROD_NAME() {
        return AF_BUSI_PROD_NAME;
    }
    public void setAF_BUSI_PROD_NAME(String aF_BUSI_PROD_NAME) {
        AF_BUSI_PROD_NAME = aF_BUSI_PROD_NAME;
    }
    public String getCHANNEL_TYPE() {
        return CHANNEL_TYPE;
    }
    public void setCHANNEL_TYPE(String cHANNEL_TYPE) {
        CHANNEL_TYPE = cHANNEL_TYPE;
    }
    public String getCHARGE_YEAR() {
        return CHARGE_YEAR;
    }
    public void setCHARGE_YEAR(String cHARGE_YEAR) {
        CHARGE_YEAR = cHARGE_YEAR;
    }
    public String getAMOUNT() {
        return AMOUNT;
    }
    public void setAMOUNT(String aMOUNT) {
        AMOUNT = aMOUNT;
    }
    public String getFEE_AMOUNT_TAX() {
        return FEE_AMOUNT_TAX;
    }
    public void setFEE_AMOUNT_TAX(String fEE_AMOUNT_TAX) {
        FEE_AMOUNT_TAX = fEE_AMOUNT_TAX;
    }
    public String getPIECES() {
        return PIECES;
    }
    public void setPIECES(String pIECES) {
        PIECES = pIECES;
    }
    public String getLOAN_CAPITAL() {
        return LOAN_CAPITAL;
    }
    public void setLOAN_CAPITAL(String lOAN_CAPITAL) {
        LOAN_CAPITAL = lOAN_CAPITAL;
    }
    public String getLOAN_INTEREST() {
        return LOAN_INTEREST;
    }
    public void setLOAN_INTEREST(String lOAN_INTEREST) {
        LOAN_INTEREST = lOAN_INTEREST;
    }
    public String getWITHHOLD_STAMP_DUTY() {
        return WITHHOLD_STAMP_DUTY;
    }
    public void setWITHHOLD_STAMP_DUTY(String wITHHOLD_STAMP_DUTY) {
        WITHHOLD_STAMP_DUTY = wITHHOLD_STAMP_DUTY;
    }
    public String getFEE_AMOUNT() {
        return FEE_AMOUNT;
    }
    public void setFEE_AMOUNT(String fEE_AMOUNT) {
        FEE_AMOUNT = fEE_AMOUNT;
    }
    public String getCHARGE_COUNT() {
        return CHARGE_COUNT;
    }
    public void setCHARGE_COUNT(String cHARGE_COUNT) {
        CHARGE_COUNT = cHARGE_COUNT;
    }
    public String getFIRST_DEDUCT() {
        return FIRST_DEDUCT;
    }
    public void setFIRST_DEDUCT(String fIRST_DEDUCT) {
        FIRST_DEDUCT = fIRST_DEDUCT;
    }
    public String getOTHER_AMOUNT_TAX() {
        return OTHER_AMOUNT_TAX;
    }
    public void setOTHER_AMOUNT_TAX(String oTHER_AMOUNT_TAX) {
        OTHER_AMOUNT_TAX = oTHER_AMOUNT_TAX;
    }
    public String getRISK_PREM() {
        return RISK_PREM;
    }
    public void setRISK_PREM(String rISK_PREM) {
        RISK_PREM = rISK_PREM;
    }
    public String getPREM() {
        return PREM;
    }
    public void setPREM(String pREM) {
        PREM = pREM;
    }
    public String getINTEREST() {
        return INTEREST;
    }
    public void setINTEREST(String iNTEREST) {
        INTEREST = iNTEREST;
    }
    public String getINTEREST_AMOUNT_TAX() {
        return INTEREST_AMOUNT_TAX;
    }
    public void setINTEREST_AMOUNT_TAX(String iNTEREST_AMOUNT_TAX) {
        INTEREST_AMOUNT_TAX = iNTEREST_AMOUNT_TAX;
    }
    public String getPAY_AMOUNT() {
        return PAY_AMOUNT;
    }
    public void setPAY_AMOUNT(String pAY_AMOUNT) {
        PAY_AMOUNT = pAY_AMOUNT;
    }
    public String getADJUST_AMOUNT() {
        return ADJUST_AMOUNT;
    }
    public void setADJUST_AMOUNT(String aDJUST_AMOUNT) {
        ADJUST_AMOUNT = aDJUST_AMOUNT;
    }
    public String getRECEIVE_TYPE() {
        return RECEIVE_TYPE;
    }
    public void setRECEIVE_TYPE(String rECEIVE_TYPE) {
        RECEIVE_TYPE = rECEIVE_TYPE;
    }
    public String getHEALTH_CONSULT_FEE() {
        return HEALTH_CONSULT_FEE;
    }
    public void setHEALTH_CONSULT_FEE(String hEALTH_CONSULT_FEE) {
        HEALTH_CONSULT_FEE = hEALTH_CONSULT_FEE;
    }
    public String getHEALTH_CHECKUP_FEE() {
        return HEALTH_CHECKUP_FEE;
    }
    public void setHEALTH_CHECKUP_FEE(String hEALTH_CHECKUP_FEE) {
        HEALTH_CHECKUP_FEE = hEALTH_CHECKUP_FEE;
    }
    public String getCOLLECTION_POUNDAGE() {
        return COLLECTION_POUNDAGE;
    }
    public void setCOLLECTION_POUNDAGE(String cOLLECTION_POUNDAGE) {
        COLLECTION_POUNDAGE = cOLLECTION_POUNDAGE;
    }
    public String getSURRENDER_FEE() {
        return SURRENDER_FEE;
    }
    public void setSURRENDER_FEE(String sURRENDER_FEE) {
        SURRENDER_FEE = sURRENDER_FEE;
    }
    public String getPOLICY_MANAGE_FEE_PERIOD() {
        return POLICY_MANAGE_FEE_PERIOD;
    }
    public void setPOLICY_MANAGE_FEE_PERIOD(String pOLICY_MANAGE_FEE_PERIOD) {
        POLICY_MANAGE_FEE_PERIOD = pOLICY_MANAGE_FEE_PERIOD;
    }
    public String getASSET_M_FEE() {
        return ASSET_M_FEE;
    }
    public void setASSET_M_FEE(String aSSET_M_FEE) {
        ASSET_M_FEE = aSSET_M_FEE;
    }
    public String getDEFER_TAX_AMOUNT() {
        return DEFER_TAX_AMOUNT;
    }
    public void setDEFER_TAX_AMOUNT(String dEFER_TAX_AMOUNT) {
        DEFER_TAX_AMOUNT = dEFER_TAX_AMOUNT;
    }
    public String getWITHHOLD_TAX_INCENTIVE() {
        return WITHHOLD_TAX_INCENTIVE;
    }
    public void setWITHHOLD_TAX_INCENTIVE(String wITHHOLD_TAX_INCENTIVE) {
        WITHHOLD_TAX_INCENTIVE = wITHHOLD_TAX_INCENTIVE;
    }
    public String getCASH_BONUS() {
        return CASH_BONUS;
    }
    public void setCASH_BONUS(String cASH_BONUS) {
        CASH_BONUS = cASH_BONUS;
    }
    public String getREISSUE_CASH_INTEREST() {
        return REISSUE_CASH_INTEREST;
    }
    public void setREISSUE_CASH_INTEREST(String rEISSUE_CASH_INTEREST) {
        REISSUE_CASH_INTEREST = rEISSUE_CASH_INTEREST;
    }
    public String getFLAT_COST() {
        return FLAT_COST;
    }
    public void setFLAT_COST(String fLAT_COST) {
        FLAT_COST = fLAT_COST;
    }
    public String getPREM_INCOM_TAX() {
        return PREM_INCOM_TAX;
    }
    public void setPREM_INCOM_TAX(String pREM_INCOM_TAX) {
        PREM_INCOM_TAX = pREM_INCOM_TAX;
    }
    public String getTRANSFER_OUT_FEE() {
        return TRANSFER_OUT_FEE;
    }
    public void setTRANSFER_OUT_FEE(String tRANSFER_OUT_FEE) {
        TRANSFER_OUT_FEE = tRANSFER_OUT_FEE;
    }
    public String getTRANSFER_IN_FEE() {
        return TRANSFER_IN_FEE;
    }
    public void setTRANSFER_IN_FEE(String tRANSFER_IN_FEE) {
        TRANSFER_IN_FEE = tRANSFER_IN_FEE;
    }
    public String getLOAN_AMOUNT() {
        return LOAN_AMOUNT;
    }
    public void setLOAN_AMOUNT(String lOAN_AMOUNT) {
        LOAN_AMOUNT = lOAN_AMOUNT;
    }
    public String getHEALTH_SERVICE_FEE() {
        return HEALTH_SERVICE_FEE;
    }
    public void setHEALTH_SERVICE_FEE(String hEALTH_SERVICE_FEE) {
        HEALTH_SERVICE_FEE = hEALTH_SERVICE_FEE;
    }
    public String getAPPLY_CODE() {
        return APPLY_CODE;
    }
    public void setAPPLY_CODE(String aPPLY_CODE) {
        APPLY_CODE = aPPLY_CODE;
    }
    public String getREFUND_TYPE() {
        return REFUND_TYPE;
    }
    public void setREFUND_TYPE(String rEFUND_TYPE) {
        REFUND_TYPE = rEFUND_TYPE;
    }
    public String getFEE_TYPE() {
        return FEE_TYPE;
    }
    public void setFEE_TYPE(String fEE_TYPE) {
        FEE_TYPE = fEE_TYPE;
    }
    public String getINSURANCE_CONTRACT() {
        return INSURANCE_CONTRACT;
    }
    public void setINSURANCE_CONTRACT(String iNSURANCE_CONTRACT) {
        INSURANCE_CONTRACT = iNSURANCE_CONTRACT;
    }
    public String getINVEST_CONTRACT() {
        return INVEST_CONTRACT;
    }
    public void setINVEST_CONTRACT(String iNVEST_CONTRACT) {
        INVEST_CONTRACT = iNVEST_CONTRACT;
    }
    public String getDAY_TOTAL() {
        return DAY_TOTAL;
    }
    public void setDAY_TOTAL(String dAY_TOTAL) {
        DAY_TOTAL = dAY_TOTAL;
    }
    public String getPAY_MODE() {
        return PAY_MODE;
    }
    public void setPAY_MODE(String pAY_MODE) {
        PAY_MODE = pAY_MODE;
    }
    public String getCAP_BANK() {
        return CAP_BANK;
    }
    public void setCAP_BANK(String cAP_BANK) {
        CAP_BANK = cAP_BANK;
    }
    public String getPREM_PAY_MODE() {
        return PREM_PAY_MODE;
    }
    public void setPREM_PAY_MODE(String pREM_PAY_MODE) {
        PREM_PAY_MODE = pREM_PAY_MODE;
    }
    public String getINTERNAL_TYPE() {
        return INTERNAL_TYPE;
    }
    public void setINTERNAL_TYPE(String iNTERNAL_TYPE) {
        INTERNAL_TYPE = iNTERNAL_TYPE;
    }
    public String getINTERNAL_PAY_MODE() {
        return INTERNAL_PAY_MODE;
    }
    public void setINTERNAL_PAY_MODE(String iNTERNAL_PAY_MODE) {
        INTERNAL_PAY_MODE = iNTERNAL_PAY_MODE;
    }
    public String getCLAIM_ITEM() {
        return CLAIM_ITEM;
    }
    public void setCLAIM_ITEM(String cLAIM_ITEM) {
        CLAIM_ITEM = cLAIM_ITEM;
    }
    public String getCLAIM_PAY_TYPE() {
        return CLAIM_PAY_TYPE;
    }
    public void setCLAIM_PAY_TYPE(String cLAIM_PAY_TYPE) {
        CLAIM_PAY_TYPE = cLAIM_PAY_TYPE;
    }
    public String getDEDUCT_ITEM() {
        return DEDUCT_ITEM;
    }
    public void setDEDUCT_ITEM(String dEDUCT_ITEM) {
        DEDUCT_ITEM = dEDUCT_ITEM;
    }
    public String getOVER_ACCOUNT_BALANCE() {
        return OVER_ACCOUNT_BALANCE;
    }
    public void setOVER_ACCOUNT_BALANCE(String oVER_ACCOUNT_BALANCE) {
        OVER_ACCOUNT_BALANCE = oVER_ACCOUNT_BALANCE;
    }
    public String getADVANCE_ITEM() {
        return ADVANCE_ITEM;
    }
    public void setADVANCE_ITEM(String aDVANCE_ITEM) {
        ADVANCE_ITEM = aDVANCE_ITEM;
    }
    public String getOVER_COMP_PAY() {
        return OVER_COMP_PAY;
    }
    public void setOVER_COMP_PAY(String oVER_COMP_PAY) {
        OVER_COMP_PAY = oVER_COMP_PAY;
    }
    public String getCLAIM_BUSI_PROD_CODE() {
        return CLAIM_BUSI_PROD_CODE;
    }
    public void setCLAIM_BUSI_PROD_CODE(String cLAIM_BUSI_PROD_CODE) {
        CLAIM_BUSI_PROD_CODE = cLAIM_BUSI_PROD_CODE;
    }
    public String getCLAIM_BUSI_PROD_NAME() {
        return CLAIM_BUSI_PROD_NAME;
    }
    public void setCLAIM_BUSI_PROD_NAME(String cLAIM_BUSI_PROD_NAME) {
        CLAIM_BUSI_PROD_NAME = cLAIM_BUSI_PROD_NAME;
    }
    public String getWAIVE_BUSI_PROD_CODE() {
        return WAIVE_BUSI_PROD_CODE;
    }
    public void setWAIVE_BUSI_PROD_CODE(String wAIVE_BUSI_PROD_CODE) {
        WAIVE_BUSI_PROD_CODE = wAIVE_BUSI_PROD_CODE;
    }
    public String getWAIVE_BUSI_PROD_NAME() {
        return WAIVE_BUSI_PROD_NAME;
    }
    public void setWAIVE_BUSI_PROD_NAME(String wAIVE_BUSI_PROD_NAME) {
        WAIVE_BUSI_PROD_NAME = wAIVE_BUSI_PROD_NAME;
    }

    

}
