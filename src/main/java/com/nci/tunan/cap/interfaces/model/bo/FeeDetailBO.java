package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description FeeDetailBO对象
 * @<NAME_EMAIL>
 * @date 2016-2-1 下午3:05:18
 * @.belongToModule 收付费-转实收
 */
public class FeeDetailBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 转实收
     */

    private static final long serialVersionUID = 1L;

    /**
     * @Fields policyOrganCode : 保单直属机构
     */
    private String policyOrganCode;
    /**
     * @Fields isItemMain : 主组合产品标识
     */
    private BigDecimal isItemMain;
    /**
     * @Fields holderName : 投保人姓名
     */
    private String holderName;
    /**
     * @Fields payrefno : 一次实收产生的收付号
     */
    private String payrefno;
    /**
     * @Fields bookkeepingId : 记账中间表主键ID
     */
    private BigDecimal bookkeepingId;
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields paidCount : 缴费次数
     */
    private BigDecimal paidCount;
    /**
     * @Fields customerAccountFlag : 是否使用客户账户标识
     */
    private BigDecimal customerAccountFlag;
    /**
     * @Fields feeType : 费用业务类型
     */
    private String feeType;
    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;
    /**
     * @Fields organCode : 系统内往来对方机构 管理机构
     */
    private String organCode;
    /**
     * @Fields channelType : 渠道类型
     */
    private String channelType;
    /**
     * @Fields chargeYear : 缴费年期
     */
    private BigDecimal chargeYear;
    /**
     * @Fields isRiskMain : 主险附加险标识
     */
    private BigDecimal isRiskMain;

    /**
     * @Fields insuredName : 被保人姓名
     */
    private String insuredName;

    /**
     * @Fields insuredId : 被保人ID
     */
    private BigDecimal insuredId;
    /**
     * @Fields productChannel : 产品渠道
     */
    private String productChannel;
    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;

    /**
     * @Fields operatorBy : 业务操作员（出单人员)
     */
    private BigDecimal operatorBy;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;
    /**
     * @Fields businessType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String businessType;

    /**
     * @Fields groupCode : 产品组合代码
     */
    private String groupCode;

    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;

    /**
     * @Fields finishTime : 费用到帐时间
     */
    private Date finishTime;

    /**
     * @Fields groupName : 产品组合名称
     */
    private String groupName;

    /**
     * @Fields feeAmount : 收付金额
     */
    private BigDecimal feeAmount;

    /**
     * @Fields serviceCode : 付款项目
     */
    private String serviceCode;
    /**
     * @Fields holderId : 投保人编码
     */
    private BigDecimal holderId;

    /**
     * @Fields withdrawType : 记账业务类型
     */
    private String withdrawType;
    /**
     * @Fields rollbackUnitNumber : 回退应收付业务流水标识
     */
    private String rollbackUnitNumber;
    /**
     * @Fields policyYear : 年度当前年度 待确认，次数？年？
     */
    private BigDecimal policyYear;

    /**
     * @Fields capOrganCode : 收付费机构
     */
    private String capOrganCode;
    /**
     * @Fields feeStatus : 收付状态 收付完成
     */
    private String feeStatus;
    /**
     * @Fields derivType : 业务来源 001;新契约 003;续保 004;保全 005;理赔
     */
    private String derivType;

    /**
     * @Fields bookkeepingBy : 记账操作人
     */
    private BigDecimal bookkeepingBy;

    /**
     * @Fields arapFlag : 收付类型
     */
    private String arapFlag;
    /**
     * @Fields refeflag : 参考项
     */
    private String refeflag;
    /**
     * @Fields bookkeepingTime : 记录记账时间
     */
    private Date bookkeepingTime;
    /**
     * @Fields agentCode : 业务员CODE
     */
    private String agentCode;
    /**
     * @Fields premFreq : 年缴、月缴、趸交等等 1;趸缴 2;月缴 3;季缴 4;半年缴 5;年缴 6;不定期缴 9;其他
     */
    private BigDecimal premFreq;

    /**
     * @Fields insertendTime : 插入截止时间
     */
    private String insertendTime;

    /**
     * @Fields specialAccountFlag : 特殊账户标记 1-中银保信个人养老金账户
     */
    private String specialAccountFlag;

    /**
     * @Fields busiItemId : 险种ID
     */
    private BigDecimal busiItemId;

    public BigDecimal getBusiItemId() {
        return busiItemId;
    }

    public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
    }

    public String getSpecialAccountFlag() {
        return specialAccountFlag;
    }

    public void setSpecialAccountFlag(String specialAccountFlag) {
        this.specialAccountFlag = specialAccountFlag;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setIsItemMain(BigDecimal isItemMain) {
        this.isItemMain = isItemMain;
    }

    public BigDecimal getIsItemMain() {
        return isItemMain;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setPayrefno(String payrefno) {
        this.payrefno = payrefno;
    }

    public String getPayrefno() {
        return payrefno;
    }

    public void setBookkeepingId(BigDecimal bookkeepingId) {
        this.bookkeepingId = bookkeepingId;
    }

    public BigDecimal getBookkeepingId() {
        return bookkeepingId;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public void setCustomerAccountFlag(BigDecimal customerAccountFlag) {
        this.customerAccountFlag = customerAccountFlag;
    }

    public BigDecimal getCustomerAccountFlag() {
        return customerAccountFlag;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public void setIsRiskMain(BigDecimal isRiskMain) {
        this.isRiskMain = isRiskMain;
    }

    public BigDecimal getIsRiskMain() {
        return isRiskMain;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setOperatorBy(BigDecimal operatorBy) {
        this.operatorBy = operatorBy;
    }

    public BigDecimal getOperatorBy() {
        return operatorBy;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setHolderId(BigDecimal holderId) {
        this.holderId = holderId;
    }

    public BigDecimal getHolderId() {
        return holderId;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getRollbackUnitNumber() {
        return rollbackUnitNumber;
    }

    public void setRollbackUnitNumber(String rollbackUnitNumber) {
        this.rollbackUnitNumber = rollbackUnitNumber;
    }

    public BigDecimal getPolicyYear() {
        return policyYear;
    }

    public void setPolicyYear(BigDecimal policyYear) {
        this.policyYear = policyYear;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public BigDecimal getBookkeepingBy() {
        return bookkeepingBy;
    }

    public void setBookkeepingBy(BigDecimal bookkeepingBy) {
        this.bookkeepingBy = bookkeepingBy;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getRefeflag() {
        return refeflag;
    }

    public void setRefeflag(String refeflag) {
        this.refeflag = refeflag;
    }

    public Date getBookkeepingTime() {
        return bookkeepingTime;
    }

    public void setBookkeepingTime(Date bookkeepingTime) {
        this.bookkeepingTime = bookkeepingTime;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public String getInsertendTime() {
        return insertendTime;
    }

    public void setInsertendTime(String insertendTime) {
        this.insertendTime = insertendTime;
    }

    @Override
    public String getBizId() {
        return null;
    }

}
