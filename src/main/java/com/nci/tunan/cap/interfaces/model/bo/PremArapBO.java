package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.nci.tunan.cap.util.CodeCst;
import com.nci.udmp.framework.model.BaseBO;

/**
 * @description PremArapBO对象
 * @<NAME_EMAIL>
 * @date 2015-11-9 上午10:12:31
 * @.belongToModule 收付费-制返盘
 */
public class PremArapBO extends BaseBO {
    /**
     * @Fields serialVersionUID : SUID
     */
    private static final long serialVersionUID = 7586170246112696895L;
    /** 
     * 是否合同成立前撤保：1-是,0-否
     */ 
    private String isCancellation;
    
    /** 
     * @Fields feeAmountBeforTax : 拆分增值税前金额 
     */ 
    private BigDecimal feeAmountBeforTax;
    
    /** 
     * @Fields feeAmountTax : 增值税金额 
     */ 
    private BigDecimal feeAmountTax;
    
    /**
     * @Fields payeeEmail : 收付款人邮箱
     */
    private String payeeEmail;
    /**
     * @Fields startDate : 收付费起始日期
     */
    private Date startDate;
    /**
     * @Fields endDate : 收付费结束日期
     */
    private Date endDate;
    /**
     * @Fields securityCode : 社保卡号
     */
    private String securityCode;
    /**
     * @Fields certificateNo : POS凭条号
     */
    private String posBelnr;
    /**
     * @Fields productCode : 责任组编码
     */
    private String productCode;
    /**
     * @Fields payLiabCode : 给付责任编码
     */
    private String payLiabCode;
    /**
     * @Fields sourceTable : 数据来源表
     */
    private String sourceTable;
    /**
     * @Fields sourceTablePk : 数据来源表主键
     */
    private BigDecimal sourceTablePk;
    /**
     * @Fields policy_balance : 保单余额
     */
    private BigDecimal policyBalance;
    /**
     * @Fields taskMark : 制返盘进行时，提高效率 收付费操作，无具体业务意义
     */
    private BigDecimal taskMark;
    /**
     * @Fields holderName : 投保人姓名
     */
    private String holderName;
    /**
     * @Fields policyOrganCode : 保单直属机构
     */
    private String policyOrganCode;
    /**
     * @Fields isItemMain : 主组合产品标识
     */
    private BigDecimal isItemMain;
    /**
     * @Fields bookkeepingId : 记账ID
     */
    private BigDecimal bookkeepingId;
    /**
     * @Fields batchNo : null
     */
    private String batchNo;
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;
    /**
     * @Fields payeePhone : 收付费人手机号
     */
    private String payeePhone;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields paidCount : 缴费次数
     */
    private BigDecimal paidCount;
    /**
     * @Fields feeType : 费用业务类型 32；实付退费 41；保费收入 参考费用类型列表
     */
    private String feeType;
    /**
     * @Fields seqNo : 制盘明细主键
     */
    private BigDecimal seqNo;
    /**
     * @Fields busiItemId : 险种ID
     */
    private BigDecimal busiItemId;
    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;
    /**
     * @Fields applyCode : 投保单号码
     */
    private String applyCode;
    /**
     * @Fields feeStatusDate : 收付状态更新时间
     */
    private Date feeStatusDate;
    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;
    /**
     * @Fields redBookkeepingTime : 红冲记录记账时间
     */
    private Date redBookkeepingTime;
    /**
     * @Fields channelType : 个人、银行、团险等
     */
    private String channelType;
    /**
     * @Fields isBankTextDate : 制盘状态更新时间
     */
    private Date isBankTextDate;

    /**
     * @Fields : billNo 票据号自定义字段
     */

    private String billNo;

    /**
     * @Fields chargeYear : 缴费年期
     */
    private BigDecimal chargeYear;

    /**
     * @Fields isRiskMain : 主险附加险标识
     */
    private BigDecimal isRiskMain;
    /**
     * @Fields isBankAccount : 帐号是否有效
     */
    private BigDecimal isBankAccount;
    /**
     * @Fields frozenStatus : 冻结状态 0：正常 1：已冻结 2：已挂起
     */
    private String frozenStatus;
    /**
     * @Fields posted : 记账状态
     */
    private String posted;
    /**
     * @Fields bookkeepingFlag : 是否记账 01；未记账 02；已记账 03；不可记账
     */
    private BigDecimal bookkeepingFlag;
    /**
     * @Fields groupId : 制返盘记录表主键
     */
    private BigDecimal groupId;
    /**
     * @Fields redBookkeepingBy : 红冲记账操作人
     */
    private BigDecimal redBookkeepingBy;
    /**
     * @Fields frozenStatusDate : 收付状态更新时间
     */
    private Date frozenStatusDate;
    /**
     * @Fields insuredName : 被保人姓名
     */
    private String insuredName;
    /**
     * @Fields insuredId : 被保人编码
     */
    private BigDecimal insuredId;
    /**
     * @Fields policyType : 个单、团单
     */
    private String policyType;
    /**
     * @Fields productChannel : 产品渠道
     */
    private String productChannel;
    /**
     * @Fields isBankAccountDate : 帐号有效状态更新时间
     */
    private Date isBankAccountDate;
    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;
    /**
     * @Fields payMode : 收付方式 10;现金 11;现金送款薄 20;支票 21;现金支票 22;转账支票 30;银行转账
     *         31;银行转账（非制返盘） 32;银行转账（制返盘） 33;网银转账 40;实时收费 41;POS收款 50;内部转账
     *         51;普通内部转账 52;预存内部转账 60;银保通 70;第三方支付 80;客户账户 90;其它
     */
    private String payMode;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;
    /**
     * @Fields validateDate : 业务生效日期
     */
    private Date validateDate;
    /**
     * @Fields businessType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String businessType;
    /**
     * @Fields redBelnr : SAP红冲凭证号
     */
    private String redBelnr;
    /**
     * @Fields bankTextStatus : 制盘状态 1;待制盘 2;制盘中 3;返盘成功 4;返盘失败
     */
    private String bankTextStatus;
    /**
     * @Fields certiType : 收付款人证件类型
     */
    private String certiType;
    /**
     * @Fields isBankTextBy : 制盘状态更新人
     */
    private BigDecimal isBankTextBy;
    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     * @Fields isBankAccountBy : 帐号有效状态更新人
     */
    private BigDecimal isBankAccountBy;
    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;
    /**
     * @Fields bankAccount : 银行账号
     */
    private String bankAccount;
    /**
     * @Fields redBookkeepingFlag : 红冲标识
     */
    private BigDecimal redBookkeepingFlag;
    /**
     * @Fields finishTime : 业务核销时间
     */
    private Date finishTime;
    /**
     * @Fields dueTime : 应缴应付日
     */
    private Date dueTime;
    /**
     * @Fields isBankText : 是否允许制盘
     */
    private BigDecimal isBankText;
    /**
     * @Fields certiCode : 收付款人证件号码
     */
    private String certiCode;
    /**
     * @Fields busiApplyDate : 业务申请日
     */
    private Date busiApplyDate;
    /**
     * @Fields belnr : SAP凭证号
     */
    private String belnr;
    /**
     * @Fields feeAmount : 应收付的金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields listId : 流水号，无业务含义
     */
    private BigDecimal listId;
    /**
     * @Fields holderId : 投保人编码
     */
    private BigDecimal holderId;
    /**
     * @Fields withdrawType : 记账业务类型
     */
    private String withdrawType;
    /**
     * @Fields rollbackUnitNumber : 回退应收付业务流水标识
     */
    private String rollbackUnitNumber;
    /**
     * @Fields failTimes : 制返盘失败次数
     */
    private BigDecimal failTimes;
    /**
     * @Fields customerId : 收付款客户号
     */
    private BigDecimal customerId;
    /**
     * @Fields policyYear : 年度当前年度
     */
    private BigDecimal policyYear;
    /**
     * @Fields frozenStatusBy : 收付状态更新人
     */
    private BigDecimal frozenStatusBy;
    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;
    /**
     * @Fields feeStatus : 收付状态 01；待收付 02；已收付 03；回退 04；中止 05；不可收付
     */
    private String feeStatus;
    /**
     * @Fields feeStatusBy : 收付状态更新人
     */
    private BigDecimal feeStatusBy;

    /**
     * @Fields payEndDate : 宽限期止期
     */
    private Date payEndDate;
    /**
     * @Fields derivType : 业务来源 1;契约 2;保全 3;续保 4;数据迁移 5;其他
     */
    private String derivType;
    /**
     * @Fields redBookkeepingId : 红冲记账中间表主键ID
     */
    private BigDecimal redBookkeepingId;
    /**
     * @Fields bookkeepingBy : 记账操作人
     */
    private BigDecimal bookkeepingBy;

    /**
     * @Fields arapFlag : 应收应付类型
     */
    private String arapFlag;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields refeflag : 参考项
     */
    private String refeflag;
    /**
     * @Fields bookkeepingTime : 记录记账时间
     */
    private Date bookkeepingTime;
    /**
     * @Fields agentCode : 代理人员CODE
     */
    private String agentCode;
    /**
     * @Fields premFreq : 年缴、月缴、趸交等等 1;趸缴 2;月缴 3;季缴 4;半年缴 5;年缴 6;不定期缴 9;其他
     */
    private BigDecimal premFreq;
    /**
     * @Fields serviceCode : 付款项目
     */
    private String serviceCode;
    /**
     * @Fields cusAccFeeAmount : 使用客户账户金额
     */
    private BigDecimal cusAccFeeAmount;
    /**
     * @Fields cusAccDetailsId : 客户账户交易明细流水号
     */
    private BigDecimal cusAccDetailsId;
    /**
     * @Fields cusAccUpdateBy : 客户账户信息修改操作人
     */
    private BigDecimal cusAccUpdateBy;
    /**
     * @Fields cusAccUpdateTime : 客户账户信息修改时间
     */
    private Date cusAccUpdateTime;
    /**
     * @Fields customerAccountFlag : 是否使用客户账户标识
     */
    private BigDecimal customerAccountFlag;
    /**
     * @Fields operatorBy : 业务操作人
     */
    private BigDecimal operatorBy;
    /**
     * @Fields bankTransStartTime : 开始时间
     */
    private String bankTransStartTime;
    /**
     * @Fields bankTransEndTime : 结束时间
     */
    private String bankTransEndTime;

    /**
     * @Fields applyUnitNumberList : 签单成功关联预收的流水号
     */
    private List<String> applyUnitNumberList = new ArrayList<String>();

    /** 
    * @Fields lessNum : 最小数
    */ 
    private String lessNum;
    /** 
    * @Fields greaterNum : 最大数
    */ 
    private String greaterNum;
    /** 
    * @Fields branchCodeList : 分公司机构集合
    */ 
    private List<String> branchCodeList = new ArrayList<String>();
    /** 
    * @Fields businessCodeList : 业务号集合
    */ 
    private List<String> businessCodeList = new ArrayList<String>();
    /** 
    * @Fields applyCodeList : 投保单号集合 
    */ 
    private List<String> applyCodeList = new ArrayList<String>();
    /** 
    * @Fields derivTypeList : 业务来源集合
    */ 
    private List<String> derivTypeList = new ArrayList<String>();
    /** 
    * @Fields channelTypeList : 销售渠道集合
    */ 
    private List<String> channelTypeList = new ArrayList<String>();

    /**
     * @Fields auditDate : 审核日期
     */
    private Date auditDate;
    /**
     * @Fields statisticalDate : 统计日期
     */
    private Date statisticalDate;
    /**
     * @Fields fundsRtnCode : 资金平台返回信息代码
     */
    private String fundsRtnCode;
    /**
     * @Fields groupCode : 产品组合代码
     */
    private String groupCode;

    /**
     * @Fields groupName : 产品组合名称
     */
    private String groupName;

    /**
     * 业务来源编号
     */
    private String bizSourceCode;
    /**
     * 业务来源名称
     */
    private String bizSourceName;
    /**
     * 渠道编号
     */
    private String typeID;
    /**
     * 渠道名称
     */
    private String typeName;
    /**
     * 公司名称
     */
    private String organCodeName;

    /**
     * 是否拆分完成（0 未拆分，1 已拆分）
     */
    private BigDecimal isSplit;

    /**
     * 是否发送（0 未发送，1 已发送）
     */
    private BigDecimal isSend;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 服务人员姓名
     */
    private String agentName;

    /**
     * 区
     */
    private String area;

    /**
     * 部
     */
    private String part;

    /**
     * 组
     */
    private String group;

    /**
     * 险种简称
     */
    private String productAbbrName;

    /**
     * 发送日期
     */
    private Date sendDate;

    /**
     * 出险原因
     */
    private BigDecimal claimNature;
    /**
     * 理赔类型
     */
    private String claimType;
    /**
     * 收费日期
     */
    private Date arapDate;
    /**
     * 实付号
     * 
     * @return
     */
    private String payrefNo;
    /**
     * 返盘时间
     * 
     * @return
     */
    private Date backTextTime;
    /**
     * 保障年期
     */
    private BigDecimal coverageYear;

    /**
     * 保障年期类型
     */
    private String coveragePeriod;

    /**
     * @Fields cipDistrictBankCode : 分行代码
     */
    private String cipDistrictBankCode;
    /**
     * @Fields cipBankCode : 银行代码
     */
    private String cipBankCode;
    /**
     * @Fields cipBranchBankCode : 网点代码
     */
    private String cipBranchBankCode;
    /**
     * @Fields drAccuitem : 借方科目
     */
    private String drAccuitem;
    /**
     * @Fields crAccuitem : 贷方科目
     */
    private String crAccuitem;
    /**
     * 是否补发追缴通知书
     */
    private BigDecimal isRecoverDocument;
    
    /** 
    * @Fields preUnitNumber : 收付费前置unitnumber 
    */ 
    private String preUnitNumber;
    
    /** 
    * @Fields bankDealDate : 支付发送时点
    */ 
    private Date bankDealDate;
    /**
     * @Fields tradeSn : 订单号
     */
    private String tradeSno;  
    /**
     * @Fields payInformation : 支付商信息
     */
    private String payInformation;
    
    /**
     * 增加标志区分是分公司集中制盘 还是总部集中制盘
     * @return
     */
    private String branchflag;
    /**
     * 当日/次日发送日期标识
     * @return
     */
    private String sendDateFlag;

	/**
     * 增加银保通制盘标志
     * @return
     */
    private BigDecimal isYbtBankTaxt;
    /**
     * @Fields endDate : 出险日日期
     */
    private Date dateOfRisk;
    /**
     * @Fields endDate : 出险日对应的生效日期
     */
    private Date effectiveDate;
    /**
     * @Fields isCorporate : 对公标识
     */
    private String isCorporate;
    /**
     * 结算日期
     */
    private Date settlementDate;
    /**
     * 生效日期起期
     */
    private Date startValidateDate;
    /**
     * 生效日期止期
     */
    private Date endValidateDate;
    
    /** 
     * @Fields chargePeriod : 缴费年期类型
     */ 
     private String chargePeriod;
     
     /**
     * @Fields specialAccountFlag : 特殊账户标记  1-中银保信个人养老金账户
     */
     private String specialAccountFlag;
     /**
      * @Fields batchId : 实时收付交易批次号
      */
     private String batchId;
     
     /**
      * @Fields submitChannel : 业务来源平台
      */
     private String submitChannel;
     
     /**
      * @Fields statisticalDate2 : 统计日期2
      */
     private Date statisticalDate2;
     
     /**
      * @Fields validateDate2: 业务生效日期2
      */
     private Date validateDate2;
     
     /**
      * @Fields openId : 客户支付ID
      */
     private String openId;
     
     /**
      * @Fields noteCode : 资金系统返回的交易流水号，用于EAST报送
      */
     private String noteCode;
	 
	  /**
      * @Fields @Fields : 是否再次请求扣费，1-是、0（或空）-否
      */
     private String payAgain;
     
     /**
      * @Fields billMode : 柜台收费：收费方式
      */
     private String billMode;
     
     /**
      * @Fields @Fields : 大额拆分标识，0否，1是
      */
     private BigDecimal largeSplitFlag;
     

    public BigDecimal getLargeSplitFlag() {
        return largeSplitFlag;
    }

    public void setLargeSplitFlag(BigDecimal largeSplitFlag) {
        this.largeSplitFlag = largeSplitFlag;
    }

    public String getBillMode() {
        return billMode;
    }

    public void setBillMode(String billMode) {
        this.billMode = billMode;
    }

    public String getPayAgain() {
		return payAgain;
	}

	public void setPayAgain(String payAgain) {
		this.payAgain = payAgain;
	}
     
     /**
      * @Fields cipFlagUser : 小契约虚拟用户(推送小契约标识)
      */
     private BigDecimal cipFlagUser;
     
     /**
      * @Fields insertName : 插入人名字
      */
     private String insertName;
     
	 /**
	  * @Fields oldFeeStatus : 原收付状态
	  */
	 private String oldFeeStatus;
     
    public String getOldFeeStatus() {
		return oldFeeStatus;
	}

	public void setOldFeeStatus(String oldFeeStatus) {
		this.oldFeeStatus = oldFeeStatus;
	}

    public String getInsertName() {
        return insertName;
    }

    public void setInsertName(String insertName) {
        this.insertName = insertName;
    }

    public BigDecimal getCipFlagUser() {
        return cipFlagUser;
    }

    public void setCipFlagUser(BigDecimal cipFlagUser) {
        this.cipFlagUser = cipFlagUser;
    }

    public String getNoteCode() {
        return noteCode;
    }

    public void setNoteCode(String noteCode) {
        this.noteCode = noteCode;
    }

    private String holderMobile;
     
     public String getHolderMobile() {
        return holderMobile;
    }

    public void setHolderMobile(String holderMobile) {
        this.holderMobile = holderMobile;
    }

    //#161949 关于针对绩优业务员客户优化制返盘功能的需求by fuyh 2024-06 begin
     /**
      * @Fields meritFlag : 绩优等级
      */
     private BigDecimal meritFlag;
     
     public BigDecimal getMeritFlag() {
         return meritFlag;
     }

     public void setMeritFlag(BigDecimal meritFlag) {
         this.meritFlag = meritFlag;
     }
     //#161949 关于针对绩优业务员客户优化制返盘功能的需求by fuyh 2024-06 end
     
     public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Date getStatisticalDate2() {
		return statisticalDate2;
	}

     /**
      * @Fields partiAppSheetNo : 申请单编号
      */
     private String partiAppSheetNo;
     
     /** 
    * @Fields commitNum : 提交数量
    */ 
    private String commitNum;
     
     public String getCommitNum() {
        return commitNum;
    }

    public void setCommitNum(String commitNum) {
        this.commitNum = commitNum;
    }

    public String getPartiAppSheetNo() {
 		return partiAppSheetNo;
 	}

 	public void setPartiAppSheetNo(String partiAppSheetNo) {
 		this.partiAppSheetNo = partiAppSheetNo;
 	}

	public void setStatisticalDate2(Date statisticalDate2) {
		this.statisticalDate2 = statisticalDate2;
	}

	public Date getValidateDate2() {
		return validateDate2;
	}

	public void setValidateDate2(Date validateDate2) {
		this.validateDate2 = validateDate2;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public String getSubmitChannel() {
		return submitChannel;
	}

	public void setSubmitChannel(String submitChannel) {
		this.submitChannel = submitChannel;
	}

	public String getSpecialAccountFlag() {
		return specialAccountFlag;
	}

	public void setSpecialAccountFlag(String specialAccountFlag) {
		this.specialAccountFlag = specialAccountFlag;
	}

	public String getChargePeriod() {
         return chargePeriod;
     }

     public void setChargePeriod(String chargePeriod) {
         this.chargePeriod = chargePeriod;
     }
     
    public Date getSettlementDate() {
		return settlementDate;
	}

	public void setSettlementDate(Date settlementDate) {
		this.settlementDate = settlementDate;
	}

	public Date getStartValidateDate() {
		return startValidateDate;
	}

	public void setStartValidateDate(Date startValidateDate) {
		this.startValidateDate = startValidateDate;
	}

	public Date getEndValidateDate() {
		return endValidateDate;
	}

	public void setEndValidateDate(Date endValidateDate) {
		this.endValidateDate = endValidateDate;
	}
    /** 
     * @Fields medicalInsuranceCard : 医保卡标识,使用码表T_MEDICAL_INSURANCE_CARD
     */ 
    private BigDecimal medicalInsuranceCard;
     
    
    public BigDecimal getMedicalInsuranceCard() {
        return medicalInsuranceCard;
    }

    public void setMedicalInsuranceCard(BigDecimal medicalInsuranceCard) {
        this.medicalInsuranceCard = medicalInsuranceCard;
    }
       
    public String getIsCancellation() {
        return isCancellation;
    }

    public void setIsCancellation(String isCancellation) {
        this.isCancellation = isCancellation;
    }

    public Date getDateOfRisk() {
		return dateOfRisk;
	}

	public void setDateOfRisk(Date dateOfRisk) {
		this.dateOfRisk = dateOfRisk;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getTradeSno() {
		return tradeSno;
	}

	public void setTradeSno(String tradeSno) {
		this.tradeSno = tradeSno;
	}

	public String getPayInformation() {
		return payInformation;
	}

	public void setPayInformation(String payInformation) {
		this.payInformation = payInformation;
	}

	public BigDecimal getFeeAmountBeforTax() {
        return feeAmountBeforTax;
    }
    
    public void setFeeAmountBeforTax(BigDecimal feeAmountBeforTax) {
        this.feeAmountBeforTax = feeAmountBeforTax;
    }
    
    public BigDecimal getFeeAmountTax() {
        return feeAmountTax;
    }
    
    public void setFeeAmountTax(BigDecimal feeAmountTax) {
        this.feeAmountTax = feeAmountTax;
    }

    public String getPayeeEmail() {
        return payeeEmail;
    }

    public void setPayeeEmail(String payeeEmail) {
        this.payeeEmail = payeeEmail;
    }

    public BigDecimal getIsRecoverDocument() {
        return isRecoverDocument;
    }

    public void setIsRecoverDocument(BigDecimal isRecoverDocument) {
        this.isRecoverDocument = isRecoverDocument;
    }
    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getSecurityCode() {
        return securityCode;
    }

    public void setSecurityCode(String securityCode) {
        this.securityCode = securityCode;
    }

    public String getPosBelnr() {
        return posBelnr;
    }

    public void setPosBelnr(String posBelnr) {
        this.posBelnr = posBelnr;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPayLiabCode() {
        return payLiabCode;
    }

    public void setPayLiabCode(String payLiabCode) {
        this.payLiabCode = payLiabCode;
    }

    public String getSourceTable() {
        return sourceTable;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public BigDecimal getSourceTablePk() {
        return sourceTablePk;
    }

    public void setSourceTablePk(BigDecimal sourceTablePk) {
        this.sourceTablePk = sourceTablePk;
    }

    public String getDrAccuitem() {
        return drAccuitem;
    }

    public void setDrAccuitem(String drAccuitem) {
        this.drAccuitem = drAccuitem;
    }

    public String getCrAccuitem() {
        return crAccuitem;
    }

    public void setCrAccuitem(String crAccuitem) {
        this.crAccuitem = crAccuitem;
    }

    public String getCipDistrictBankCode() {
        return cipDistrictBankCode;
    }

    public void setCipDistrictBankCode(String cipDistrictBankCode) {
        this.cipDistrictBankCode = cipDistrictBankCode;
    }

    public String getCipBankCode() {
        return cipBankCode;
    }

    public void setCipBankCode(String cipBankCode) {
        this.cipBankCode = cipBankCode;
    }

    public String getCipBranchBankCode() {
        return cipBranchBankCode;
    }

    public void setCipBranchBankCode(String cipBranchBankCode) {
        this.cipBranchBankCode = cipBranchBankCode;
    }

    public Date getArapDate() {
        return arapDate;
    }

    public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    public BigDecimal getPolicyBalance() {
        return policyBalance;
    }

    public void setPolicyBalance(BigDecimal policyBalance) {
        this.policyBalance = policyBalance;
    }

    public BigDecimal getClaimNature() {
        return claimNature;
    }

    public void setClaimNature(BigDecimal claimNature) {
        this.claimNature = claimNature;
    }

    public String getClaimType() {
        return claimType;
    }

    public void setClaimType(String claimType) {
        this.claimType = claimType;
    }

    public BigDecimal getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(BigDecimal isSplit) {
        this.isSplit = isSplit;
    }

    public BigDecimal getIsSend() {
        return isSend;
    }

    public void setIsSend(BigDecimal isSend) {
        this.isSend = isSend;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getPart() {
        return part;
    }

    public void setPart(String part) {
        this.part = part;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getProductAbbrName() {
        return productAbbrName;
    }

    public void setProductAbbrName(String productAbbrName) {
        this.productAbbrName = productAbbrName;
    }

    public Date getSendDate() {
        return sendDate;
    }

    public void setSendDate(Date sendDate) {
        this.sendDate = sendDate;
    }

    public BigDecimal getTaskMark() {
        return taskMark;
    }

    public void setTaskMark(BigDecimal taskMark) {
        this.taskMark = taskMark;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public BigDecimal getIsItemMain() {
        return isItemMain;
    }

    public void setIsItemMain(BigDecimal isItemMain) {
        this.isItemMain = isItemMain;
    }

    public BigDecimal getBookkeepingId() {
        return bookkeepingId;
    }

    public void setBookkeepingId(BigDecimal bookkeepingId) {
        this.bookkeepingId = bookkeepingId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = payeePhone;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(BigDecimal seqNo) {
        this.seqNo = seqNo;
    }
    
    public BigDecimal getBusiItemId() {
        return busiItemId;
        }

    public void setBusiItemId(BigDecimal busiItemId) {
        this.busiItemId = busiItemId;
        }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public Date getFeeStatusDate() {
        return feeStatusDate;
    }

    public void setFeeStatusDate(Date feeStatusDate) {
        this.feeStatusDate = feeStatusDate;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public Date getRedBookkeepingTime() {
        return redBookkeepingTime;
    }

    public void setRedBookkeepingTime(Date redBookkeepingTime) {
        this.redBookkeepingTime = redBookkeepingTime;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Date getIsBankTextDate() {
        return isBankTextDate;
    }

    public void setIsBankTextDate(Date isBankTextDate) {
        this.isBankTextDate = isBankTextDate;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public BigDecimal getIsRiskMain() {
        return isRiskMain;
    }

    public void setIsRiskMain(BigDecimal isRiskMain) {
        this.isRiskMain = isRiskMain;
    }

    public BigDecimal getIsBankAccount() {
        return isBankAccount;
    }

    public void setIsBankAccount(BigDecimal isBankAccount) {
        this.isBankAccount = isBankAccount;
    }

    public String getFrozenStatus() {
        return frozenStatus;
    }

    public void setFrozenStatus(String frozenStatus) {
        this.frozenStatus = frozenStatus;
    }

    public String getPosted() {
        return posted;
    }

    public void setPosted(String posted) {
        this.posted = posted;
    }

    public BigDecimal getBookkeepingFlag() {
        return bookkeepingFlag;
    }

    public void setBookkeepingFlag(BigDecimal bookkeepingFlag) {
        this.bookkeepingFlag = bookkeepingFlag;
    }

    public BigDecimal getGroupId() {
        return groupId;
    }

    public void setGroupId(BigDecimal groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getRedBookkeepingBy() {
        return redBookkeepingBy;
    }

    public void setRedBookkeepingBy(BigDecimal redBookkeepingBy) {
        this.redBookkeepingBy = redBookkeepingBy;
    }

    public Date getFrozenStatusDate() {
        return frozenStatusDate;
    }

    public void setFrozenStatusDate(Date frozenStatusDate) {
        this.frozenStatusDate = frozenStatusDate;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public Date getIsBankAccountDate() {
        return isBankAccountDate;
    }

    public void setIsBankAccountDate(Date isBankAccountDate) {
        this.isBankAccountDate = isBankAccountDate;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getRedBelnr() {
        return redBelnr;
    }

    public void setRedBelnr(String redBelnr) {
        this.redBelnr = redBelnr;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    public void setBankTextStatus(String bankTextStatus) {
        this.bankTextStatus = bankTextStatus;
    }

    public String getCertiType() {
        return certiType;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public BigDecimal getIsBankTextBy() {
        return isBankTextBy;
    }

    public void setIsBankTextBy(BigDecimal isBankTextBy) {
        this.isBankTextBy = isBankTextBy;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public BigDecimal getIsBankAccountBy() {
        return isBankAccountBy;
    }

    public void setIsBankAccountBy(BigDecimal isBankAccountBy) {
        this.isBankAccountBy = isBankAccountBy;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public BigDecimal getRedBookkeepingFlag() {
        return redBookkeepingFlag;
    }

    public void setRedBookkeepingFlag(BigDecimal redBookkeepingFlag) {
        this.redBookkeepingFlag = redBookkeepingFlag;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public BigDecimal getIsBankText() {
        return isBankText;
    }

    public void setIsBankText(BigDecimal isBankText) {
        this.isBankText = isBankText;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public Date getBusiApplyDate() {
        return busiApplyDate;
    }

    public void setBusiApplyDate(Date busiApplyDate) {
        this.busiApplyDate = busiApplyDate;
    }

    public String getBelnr() {
        return belnr;
    }

    public void setBelnr(String belnr) {
        this.belnr = belnr;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public BigDecimal getHolderId() {
        return holderId;
    }

    public void setHolderId(BigDecimal holderId) {
        this.holderId = holderId;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getRollbackUnitNumber() {
        return rollbackUnitNumber;
    }

    public void setRollbackUnitNumber(String rollbackUnitNumber) {
        this.rollbackUnitNumber = rollbackUnitNumber;
    }

    public BigDecimal getFailTimes() {
        return failTimes;
    }

    public void setFailTimes(BigDecimal failTimes) {
        this.failTimes = failTimes;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getPolicyYear() {
        return policyYear;
    }

    public void setPolicyYear(BigDecimal policyYear) {
        this.policyYear = policyYear;
    }

    public BigDecimal getFrozenStatusBy() {
        return frozenStatusBy;
    }

    public void setFrozenStatusBy(BigDecimal frozenStatusBy) {
        this.frozenStatusBy = frozenStatusBy;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public BigDecimal getFeeStatusBy() {
        return feeStatusBy;
    }

    public void setFeeStatusBy(BigDecimal feeStatusBy) {
        this.feeStatusBy = feeStatusBy;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }

    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public BigDecimal getRedBookkeepingId() {
        return redBookkeepingId;
    }

    public void setRedBookkeepingId(BigDecimal redBookkeepingId) {
        this.redBookkeepingId = redBookkeepingId;
    }

    public BigDecimal getBookkeepingBy() {
        return bookkeepingBy;
    }

    public void setBookkeepingBy(BigDecimal bookkeepingBy) {
        this.bookkeepingBy = bookkeepingBy;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getRefeflag() {
        return refeflag;
    }

    public void setRefeflag(String refeflag) {
        this.refeflag = refeflag;
    }

    public Date getBookkeepingTime() {
        return bookkeepingTime;
    }

    public void setBookkeepingTime(Date bookkeepingTime) {
        this.bookkeepingTime = bookkeepingTime;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public BigDecimal getCusAccFeeAmount() {
        return cusAccFeeAmount;
    }

    public void setCusAccFeeAmount(BigDecimal cusAccFeeAmount) {
        this.cusAccFeeAmount = cusAccFeeAmount;
    }

    public BigDecimal getCusAccDetailsId() {
        return cusAccDetailsId;
    }

    public void setCusAccDetailsId(BigDecimal cusAccDetailsId) {
        this.cusAccDetailsId = cusAccDetailsId;
    }

    public BigDecimal getCusAccUpdateBy() {
        return cusAccUpdateBy;
    }

    public void setCusAccUpdateBy(BigDecimal cusAccUpdateBy) {
        this.cusAccUpdateBy = cusAccUpdateBy;
    }

    public Date getCusAccUpdateTime() {
        return cusAccUpdateTime;
    }

    public void setCusAccUpdateTime(Date cusAccUpdateTime) {
        this.cusAccUpdateTime = cusAccUpdateTime;
    }

    public BigDecimal getCustomerAccountFlag() {
        return customerAccountFlag;
    }

    public void setCustomerAccountFlag(BigDecimal customerAccountFlag) {
        this.customerAccountFlag = customerAccountFlag;
    }

    public BigDecimal getOperatorBy() {
        return operatorBy;
    }

    public void setOperatorBy(BigDecimal operatorBy) {
        this.operatorBy = operatorBy;
    }

    public String getBankTransStartTime() {
        return bankTransStartTime;
    }

    public void setBankTransStartTime(String bankTransStartTime) {
        this.bankTransStartTime = bankTransStartTime;
    }

    public String getBankTransEndTime() {
        return bankTransEndTime;
    }

    public void setBankTransEndTime(String bankTransEndTime) {
        this.bankTransEndTime = bankTransEndTime;
    }

    public String getLessNum() {
        return lessNum;
    }

    public void setLessNum(String lessNum) {
        this.lessNum = lessNum;
    }

    public String getGreaterNum() {
        return greaterNum;
    }

    public void setGreaterNum(String greaterNum) {
        this.greaterNum = greaterNum;
    }

    public List<String> getBranchCodeList() {
        return branchCodeList;
    }

    public void setBranchCodeList(List<String> branchCodeList) {
        this.branchCodeList = branchCodeList;
    }

    public List<String> getBusinessCodeList() {
        return businessCodeList;
    }

    public void setBusinessCodeList(List<String> businessCodeList) {
        this.businessCodeList = businessCodeList;
    }

    public List<String> getApplyCodeList() {
        return applyCodeList;
    }

    public void setApplyCodeList(List<String> applyCodeList) {
        this.applyCodeList = applyCodeList;
    }

    public List<String> getDerivTypeList() {
        return derivTypeList;
    }

    public void setDerivTypeList(List<String> derivTypeList) {
        this.derivTypeList = derivTypeList;
    }

    public List<String> getChannelTypeList() {
        return channelTypeList;
    }

    public void setChannelTypeList(List<String> channelTypeList) {
        this.channelTypeList = channelTypeList;
    }

    public String getBizSourceCode() {
        return bizSourceCode;
    }

    public void setBizSourceCode(String bizSourceCode) {
        this.bizSourceCode = bizSourceCode;
    }

    public String getBizSourceName() {
        return bizSourceName;
    }

    public void setBizSourceName(String bizSourceName) {
        this.bizSourceName = bizSourceName;
    }

    public String getTypeID() {
        return typeID;
    }

    public void setTypeID(String typeID) {
        this.typeID = typeID;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getOrganCodeName() {
        return organCodeName;
    }

    public void setOrganCodeName(String organCodeName) {
        this.organCodeName = organCodeName;
    }

    public List<String> getApplyUnitNumberList() {
        return applyUnitNumberList;
    }

    public void setApplyUnitNumberList(List<String> applyUnitNumberList) {
        this.applyUnitNumberList = applyUnitNumberList;
    }
    public String getIsCorporate() {
		return isCorporate;
	}

	public void setIsCorporate(String isCorporate) {
		this.isCorporate = isCorporate;
	}


    @Override
	public String toString() {
		return "PremArapBO [isCancellation=" + isCancellation
				+ ", feeAmountBeforTax=" + feeAmountBeforTax
				+ ", feeAmountTax=" + feeAmountTax + ", payeeEmail="
				+ payeeEmail + ", startDate=" + startDate + ", endDate="
				+ endDate + ", securityCode=" + securityCode + ", posBelnr="
				+ posBelnr + ", productCode=" + productCode + ", payLiabCode="
				+ payLiabCode + ", sourceTable=" + sourceTable
				+ ", sourceTablePk=" + sourceTablePk + ", policyBalance="
				+ policyBalance + ", taskMark=" + taskMark + ", holderName="
				+ holderName + ", policyOrganCode=" + policyOrganCode
				+ ", isItemMain=" + isItemMain + ", bookkeepingId="
				+ bookkeepingId + ", batchNo=" + batchNo + ", busiProdName="
				+ busiProdName + ", payeePhone=" + payeePhone + ", unitNumber="
				+ unitNumber + ", paidCount=" + paidCount + ", feeType="
				+ feeType + ", seqNo=" + seqNo + ", busiItemId=" + busiItemId
				+ ", busiProdCode=" + busiProdCode + ", applyCode=" + applyCode
				+ ", feeStatusDate=" + feeStatusDate + ", organCode="
				+ organCode + ", redBookkeepingTime=" + redBookkeepingTime
				+ ", channelType=" + channelType + ", isBankTextDate="
				+ isBankTextDate + ", billNo=" + billNo + ", chargeYear="
				+ chargeYear + ", isRiskMain=" + isRiskMain
				+ ", isBankAccount=" + isBankAccount + ", frozenStatus="
				+ frozenStatus + ", posted=" + posted + ", bookkeepingFlag="
				+ bookkeepingFlag + ", groupId=" + groupId
				+ ", redBookkeepingBy=" + redBookkeepingBy
				+ ", frozenStatusDate=" + frozenStatusDate + ", insuredName="
				+ insuredName + ", insuredId=" + insuredId + ", policyType="
				+ policyType + ", productChannel=" + productChannel
				+ ", isBankAccountDate=" + isBankAccountDate + ", policyCode="
				+ policyCode + ", payMode=" + payMode + ", branchCode="
				+ branchCode + ", validateDate=" + validateDate
				+ ", businessType=" + businessType + ", redBelnr=" + redBelnr
				+ ", bankTextStatus=" + bankTextStatus + ", certiType="
				+ certiType + ", isBankTextBy=" + isBankTextBy + ", moneyCode="
				+ moneyCode + ", businessCode=" + businessCode
				+ ", isBankAccountBy=" + isBankAccountBy + ", payeeName="
				+ payeeName + ", bankAccount=" + bankAccount
				+ ", redBookkeepingFlag=" + redBookkeepingFlag
				+ ", finishTime=" + finishTime + ", dueTime=" + dueTime
				+ ", isBankText=" + isBankText + ", certiCode=" + certiCode
				+ ", busiApplyDate=" + busiApplyDate + ", belnr=" + belnr
				+ ", feeAmount=" + feeAmount + ", listId=" + listId
				+ ", holderId=" + holderId + ", withdrawType=" + withdrawType
				+ ", rollbackUnitNumber=" + rollbackUnitNumber + ", failTimes="
				+ failTimes + ", customerId=" + customerId + ", policyYear="
				+ policyYear + ", frozenStatusBy=" + frozenStatusBy
				+ ", bankUserName=" + bankUserName + ", feeStatus=" + feeStatus
				+ ", feeStatusBy=" + feeStatusBy + ", payEndDate=" + payEndDate
				+ ", derivType=" + derivType + ", redBookkeepingId="
				+ redBookkeepingId + ", bookkeepingBy=" + bookkeepingBy
				+ ", arapFlag=" + arapFlag + ", bankCode=" + bankCode
				+ ", refeflag=" + refeflag + ", bookkeepingTime="
				+ bookkeepingTime + ", agentCode=" + agentCode + ", premFreq="
				+ premFreq + ", serviceCode=" + serviceCode
				+ ", cusAccFeeAmount=" + cusAccFeeAmount + ", cusAccDetailsId="
				+ cusAccDetailsId + ", cusAccUpdateBy=" + cusAccUpdateBy
				+ ", cusAccUpdateTime=" + cusAccUpdateTime
				+ ", customerAccountFlag=" + customerAccountFlag
				+ ", operatorBy=" + operatorBy + ", bankTransStartTime="
				+ bankTransStartTime + ", bankTransEndTime=" + bankTransEndTime
				+ ", applyUnitNumberList=" + applyUnitNumberList + ", lessNum="
				+ lessNum + ", greaterNum=" + greaterNum + ", branchCodeList="
				+ branchCodeList + ", businessCodeList=" + businessCodeList
				+ ", applyCodeList=" + applyCodeList + ", derivTypeList="
				+ derivTypeList + ", channelTypeList=" + channelTypeList
				+ ", auditDate=" + auditDate + ", statisticalDate="
				+ statisticalDate + ", fundsRtnCode=" + fundsRtnCode
				+ ", groupCode=" + groupCode + ", groupName=" + groupName
				+ ", bizSourceCode=" + bizSourceCode + ", bizSourceName="
				+ bizSourceName + ", typeID=" + typeID + ", typeName="
				+ typeName + ", organCodeName=" + organCodeName + ", isSplit="
				+ isSplit + ", isSend=" + isSend + ", taxRate=" + taxRate
				+ ", agentName=" + agentName + ", area=" + area + ", part="
				+ part + ", group=" + group + ", productAbbrName="
				+ productAbbrName + ", sendDate=" + sendDate + ", claimNature="
				+ claimNature + ", claimType=" + claimType + ", arapDate="
				+ arapDate + ", payrefNo=" + payrefNo + ", backTextTime="
				+ backTextTime + ", coverageYear=" + coverageYear
				+ ", coveragePeriod=" + coveragePeriod
				+ ", cipDistrictBankCode=" + cipDistrictBankCode
				+ ", cipBankCode=" + cipBankCode + ", cipBranchBankCode="
				+ cipBranchBankCode + ", drAccuitem=" + drAccuitem
				+ ", crAccuitem=" + crAccuitem + ", isRecoverDocument="
				+ isRecoverDocument + ", preUnitNumber=" + preUnitNumber
				+ ", bankDealDate=" + bankDealDate + ", tradeSno=" + tradeSno
				+ ", payInformation=" + payInformation + ", branchflag="
				+ branchflag + ", sendDateFlag=" + sendDateFlag
				+ ", isYbtBankTaxt=" + isYbtBankTaxt + ", dateOfRisk="
				+ dateOfRisk + ", effectiveDate=" + effectiveDate
				+ ", isCorporate=" + isCorporate + ", settlementDate="
				+ settlementDate + ", startValidateDate=" + startValidateDate
				+ ", endValidateDate=" + endValidateDate + ", chargePeriod="
				+ chargePeriod + ", specialAccountFlag=" + specialAccountFlag
				+ ", batchId=" + batchId + ", submitChannel=" + submitChannel
				+ ", statisticalDate2=" + statisticalDate2 + ", validateDate2="
				+ validateDate2 + ", partiAppSheetNo=" + partiAppSheetNo
				+ ", medicalInsuranceCard=" + medicalInsuranceCard + "]";
	}

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public int hashCode() {
        int result = CodeCst.NUMBER_SEVENTEEN;
        result = CodeCst.NUMBER_THIRTH_SEVEN * result + getUnitNumber().hashCode();
        return result;

    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof PremArapBO)) {
            return false;
        }
        final PremArapBO premArapBO = (PremArapBO) obj;
        if (!getUnitNumber().equals(premArapBO.getUnitNumber())) {
            return false;
        }
        return true;
    }

    public String getFundsRtnCode() {
        return fundsRtnCode;
    }

    public void setFundsRtnCode(String fundsRtnCode) {
        this.fundsRtnCode = fundsRtnCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    /**
     * 
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return BigDecimal
     */
    public BigDecimal getSignFeeAmount() {
        if (CodeCst.AR_AP__AP.equals(arapFlag)) {
            return feeAmount.multiply(new BigDecimal(-1));
        } else {
            return feeAmount;
        }
    }
   /**
    * 
    * @description
    * @version
    * @title
    * @<NAME_EMAIL>
    * @return BigDecimal
    */
    public BigDecimal getFeeAmountWithOutCust() {
        if (cusAccFeeAmount != null && cusAccFeeAmount.compareTo(new BigDecimal(0)) > 0) {
            return feeAmount.subtract(cusAccFeeAmount);
        } else {
            return feeAmount;
        }
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Date getStatisticalDate() {
        return statisticalDate;
    }

    public void setStatisticalDate(Date statisticalDate) {
        this.statisticalDate = statisticalDate;
    }

    public String getPayrefNo() {
        return payrefNo;
    }

    public void setPayrefNo(String payrefNo) {
        this.payrefNo = payrefNo;
    }

    public Date getBackTextTime() {
        return backTextTime;
    }

    public void setBackTextTime(Date backTextTime) {
        this.backTextTime = backTextTime;
    }

    public BigDecimal getCoverageYear() {
        return coverageYear;
    }

    public void setCoverageYear(BigDecimal coverageYear) {
        this.coverageYear = coverageYear;
    }

    public String getCoveragePeriod() {
        return coveragePeriod;
    }

    public void setCoveragePeriod(String coveragePeriod) {
        this.coveragePeriod = coveragePeriod;
    }

    public String getPreUnitNumber() {
        return preUnitNumber;
    }

    public void setPreUnitNumber(String preUnitNumber) {
        this.preUnitNumber = preUnitNumber;
    }

    public Date getBankDealDate() {
        return bankDealDate;
    }

    public void setBankDealDate(Date bankDealDate) {
        this.bankDealDate = bankDealDate;
    }

	public String getBranchflag() {
		return branchflag;
	}

	public void setBranchflag(String branchflag) {
		this.branchflag = branchflag;
	}

    public BigDecimal getIsYbtBankTaxt() {
        return isYbtBankTaxt;
    }

    public void setIsYbtBankTaxt(BigDecimal isYbtBankTaxt) {
        this.isYbtBankTaxt = isYbtBankTaxt;
    }
    
    public String getSendDateFlag() {
		return sendDateFlag;
	}

	public void setSendDateFlag(String sendDateFlag) {
		this.sendDateFlag = sendDateFlag;
	}

	

}
