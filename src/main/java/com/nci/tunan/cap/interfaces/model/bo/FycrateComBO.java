package com.nci.tunan.cap.interfaces.model.bo;

import com.nci.udmp.framework.model.*;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description FycrateComVO对象
 * <AUTHOR>
 * @date 2024-11-18 14:51:09
 */
public class FycrateComBO extends BaseBO {
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields year : 取T
	 */
	private BigDecimal year;
	/**
	 * @Fields riskcode : 险种编码
	 */
	private String riskcode;
	/**
	 * @Fields curyear : 保单年度
	 */
	private BigDecimal curyear;
	/**
	 * @Fields bankBranchCode : 银行渠道
	 */
	private String bankBranchCode;
	/**
	 * @Fields branchRate : 机构佣金率
	 */
	private BigDecimal branchRate;
	/**
	 * @Fields organCode : 管理机构
	 */
	private String organCode;
	/**
	 * @Fields startdate : 佣金率起期
	 */
	private Date startdate;
	/**
	 * @Fields listId : 主键ID，无业务含义
	 */
	private BigDecimal listId;
	/**
	 * @Fields payintv : 0趸交、12期交、88（长期月交）或99（不定期交）
	 */
	private String payintv;
	/**
	 * @Fields enddate : 佣金率止期
	 */
	private Date enddate;

	public void setYear(BigDecimal year) {
		this.year = year;
	}

	public BigDecimal getYear() {
		return year;
	}

	public void setRiskcode(String riskcode) {
		this.riskcode = riskcode;
	}

	public String getRiskcode() {
		return riskcode;
	}

	public void setCuryear(BigDecimal curyear) {
		this.curyear = curyear;
	}

	public BigDecimal getCuryear() {
		return curyear;
	}

	public void setBankBranchCode(String bankBranchCode) {
		this.bankBranchCode = bankBranchCode;
	}

	public String getBankBranchCode() {
		return bankBranchCode;
	}

	public void setBranchRate(BigDecimal branchRate) {
		this.branchRate = branchRate;
	}

	public BigDecimal getBranchRate() {
		return branchRate;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setStartdate(Date startdate) {
		this.startdate = startdate;
	}

	public Date getStartdate() {
		return startdate;
	}

	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}

	public BigDecimal getListId() {
		return listId;
	}

	public void setPayintv(String payintv) {
		this.payintv = payintv;
	}

	public String getPayintv() {
		return payintv;
	}

	public void setEnddate(Date enddate) {
		this.enddate = enddate;
	}

	public Date getEnddate() {
		return enddate;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "FycrateComVO [" + "year=" + year + "," + "riskcode=" + riskcode
				+ "," + "curyear=" + curyear + "," + "bankBranchCode="
				+ bankBranchCode + "," + "branchRate=" + branchRate + ","
				+ "organCode=" + organCode + "," + "startdate=" + startdate
				+ "," + "listId=" + listId + "," + "payintv=" + payintv + ","
				+ "enddate=" + enddate + "]";
	}
}
