package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description BankDiskTrailVO对象
 * <AUTHOR> 
 * @date 2015-11-12 18:30:10  
 * @.belongToModule 收付费-执行进度查询
 */
public class BankDiskTrailBO extends BaseBO {	
	 /** 
    * @Fields serialVersionUID : 序列化版本号
    */ 
    
    private static final long serialVersionUID = -1553242757989980664L;
    /** 
	* @Fields status :  执行状态
 	*/ 
	private String status;
	 /** 
	* @Fields organCode :  管理机构代码
 	*/ 
	private String organCode;
		 /** 
	* @Fields channelType :  渠道类型
 	*/ 
	private String channelType;
	 /** 
	* @Fields beginTime :  开始时间
 	*/ 
	private Date beginTime;
	 /** 
	* @Fields dueEndTime :  应缴应付结束时间
 	*/ 
	private Date dueEndTime;
	 /** 
	* @Fields diskConfigId :  制盘参数配置ID
 	*/ 
	private BigDecimal diskConfigId;
		 /** 
	* @Fields batchTask :  批任务
 	*/ 
	private BigDecimal batchTask;
	 /** 
	* @Fields batchTaskNum :  批次号
 	*/ 
	private BigDecimal batchTaskNum;
	 /** 
	* @Fields rangeValue :  值
 	*/ 
	private BigDecimal rangeValue;
	 /** 
	* @Fields dueBeginTime :  应缴应付开始时间
 	*/ 
	private Date dueBeginTime;
	 /** 
	* @Fields diskTrailId :  制盘任务详细轨迹ID
 	*/ 
	private BigDecimal diskTrailId;
	 /** 
	* @Fields unit :  取值单位
 	*/ 
	private BigDecimal unit;
	/** 
	* @Fields bankName :  银行名称
 	*/ 
	private String bankName;
	 /** 
	* @Fields derivType :  业务来源
001;新契约
002;核保
003;续保
004;保全
005;理赔
 	*/ 
	private String derivType;
	 /** 
	* @Fields mainId :  制盘任务明细信息ID
 	*/ 
	private BigDecimal mainId;
	 /** 
	* @Fields organName :  管理机构名称
 	*/ 
	private String organName;
	 /** 
	* @Fields arapFlag :  收付标识
 	*/ 
	private String arapFlag;
	 /** 
	* @Fields bankCode :  银行编码
 	*/ 
	private String bankCode;
		 /** 
	* @Fields bankDiskType :  处理内容
1 预制盘
2 制盘
3 返盘
 	*/ 
	private BigDecimal bankDiskType;
	 /** 
	* @Fields endTime :  结束时间
 	*/ 
	private Date endTime;
			
	 public void setStatus(String status) {
		this.status = status;
	}
	
	public String getStatus() {
		return status;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
		 public void setChannelType(String channelType) {
		this.channelType = channelType;
	}
	
	public String getChannelType() {
		return channelType;
	}
	 public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}
	
	public Date getBeginTime() {
		return beginTime;
	}
	 public void setDueEndTime(Date dueEndTime) {
		this.dueEndTime = dueEndTime;
	}
	
	public Date getDueEndTime() {
		return dueEndTime;
	}
	 public void setDiskConfigId(BigDecimal diskConfigId) {
		this.diskConfigId = diskConfigId;
	}
	
	public BigDecimal getDiskConfigId() {
		return diskConfigId;
	}
		 public void setBatchTask(BigDecimal batchTask) {
		this.batchTask = batchTask;
	}
	
	public BigDecimal getBatchTask() {
		return batchTask;
	}
	 public void setBatchTaskNum(BigDecimal batchTaskNum) {
		this.batchTaskNum = batchTaskNum;
	}
	
	public BigDecimal getBatchTaskNum() {
		return batchTaskNum;
	}
	 public void setRangeValue(BigDecimal rangeValue) {
		this.rangeValue = rangeValue;
	}
	
	public BigDecimal getRangeValue() {
		return rangeValue;
	}
	 public void setDueBeginTime(Date dueBeginTime) {
		this.dueBeginTime = dueBeginTime;
	}
	
	public Date getDueBeginTime() {
		return dueBeginTime;
	}
	 public void setDiskTrailId(BigDecimal diskTrailId) {
		this.diskTrailId = diskTrailId;
	}
	
	public BigDecimal getDiskTrailId() {
		return diskTrailId;
	}
	 public void setUnit(BigDecimal unit) {
		this.unit = unit;
	}
	
	public BigDecimal getUnit() {
		return unit;
	}
			 public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	
	public String getBankName() {
		return bankName;
	}
	 public void setDerivType(String derivType) {
		this.derivType = derivType;
	}
	
	public String getDerivType() {
		return derivType;
	}
	 public void setMainId(BigDecimal mainId) {
		this.mainId = mainId;
	}
	
	public BigDecimal getMainId() {
		return mainId;
	}
	 public void setOrganName(String organName) {
		this.organName = organName;
	}
	
	public String getOrganName() {
		return organName;
	}
	 public void setArapFlag(String arapFlag) {
		this.arapFlag = arapFlag;
	}
	
	public String getArapFlag() {
		return arapFlag;
	}
	 public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	
	public String getBankCode() {
		return bankCode;
	}
		 public void setBankDiskType(BigDecimal bankDiskType) {
		this.bankDiskType = bankDiskType;
	}
	
	public BigDecimal getBankDiskType() {
		return bankDiskType;
	}
	 public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public Date getEndTime() {
		return endTime;
	}
			
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "BankDiskTrailVO [" +
				"status="+status+","+
"organCode="+organCode+","+
"channelType="+channelType+","+
"beginTime="+beginTime+","+
"dueEndTime="+dueEndTime+","+
"diskConfigId="+diskConfigId+","+
"batchTask="+batchTask+","+
"batchTaskNum="+batchTaskNum+","+
"rangeValue="+rangeValue+","+
"dueBeginTime="+dueBeginTime+","+
"diskTrailId="+diskTrailId+","+
"unit="+unit+","+
"bankName="+bankName+","+
"derivType="+derivType+","+
"mainId="+mainId+","+
"organName="+organName+","+
"arapFlag="+arapFlag+","+
"bankCode="+bankCode+","+
"bankDiskType="+bankDiskType+","+
"endTime="+endTime+"]";
    }
}
