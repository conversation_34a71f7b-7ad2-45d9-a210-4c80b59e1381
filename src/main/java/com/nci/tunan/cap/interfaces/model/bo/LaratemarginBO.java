package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 扎帐毛价值率aratemarginBO对象
 * <AUTHOR>
 * @date 2018-09-21 09:43:08
 * @.belongToModule 收付费-扎帐
 */
public class LaratemarginBO extends BaseBO {

	/**
	 * @Fields serialVersionUID : 序列化版本号
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields rate : 毛价值率
	 */
	private BigDecimal rate;
	/**
	 * @Fields modifytime : 最后一次修改时间
	 */
	private String modifytime;
	/**
	 * @Fields riskcode : 险种代码
	 */
	private String riskcode;
	/**
	 * @Fields salechnl : --含义描述：渠道代码 --20121127 sushi 新增 begin 1 -- 个人营销， 2 --
	 *         团险， 3 -- 银行保险， 4 -- 收费员渠道， 5 -- 电话直销， 6 -- 银代续期， 7 -- 续期督导， 8 --
	 *         个银渠道， 9 -- 至尊理财 --20121127 sushi 新增 end
	 */
	private String salechnl;
	/**
	 * @Fields makedate : 入机日期
	 */
	private Date makedate;
	/**
	 * @Fields operator : 操作员代码
	 */
	private String operator;
	/**
	 * @Fields payplancode : 缴费计划编码
	 */
	private String payplancode;
	/**
	 * @Fields serialno : 流水号
	 */
	private String serialno;
	/**
	 * @Fields maketime : 入机时间
	 */
	private String maketime;
	/**
	 * @Fields startdate : 有效期开始时间
	 */
	private Date startdate;
	/**
	 * @Fields payyears :缴别代码（缴费年期） --含义描述：0至1000年
	 */
	private BigDecimal payyears;
	/**
	 * @Fields payintv : --含义描述：缴费方式 --20121127 sushi 新增 begin 0 -- 趸交， 1 -- 月交，
	 *         3 -- 季交， 6 -- 半年交， 12 -- 年交 --20121127 sushi 新增 end
	 */
	private BigDecimal payintv;
	/**
	 * @Fields modifydate : 最后一次修改日期
	 */
	private Date modifydate;
	/**
	 * @Fields subsalechnl : --含义描述：子渠道代码，与渠道代码配合使用，可以为空 --20121218 sushi 新增
	 *         begin 渠道代码为5时， 01 -- 电销（话务） 03 -- 网销 --20121218 sushi 新增 end
	 */
	private String subsalechnl;
	/**
	 * @Fields enddate : 有效期结束时间
	 */
	private Date enddate;
	/**
	 * @Fields validate : 生效日期
	 */
	private Date validate;
	/**
	 * @Fields f01 : 保额区间下限
	 */
	private BigDecimal f01;

	/**
	 * @Fields f02 : 保额区间上限
	 */
	private BigDecimal f02;

	/**
	 * @Fields f03 : 按保额分档标识
	 */
	private String f03;

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setModifytime(String modifytime) {
		this.modifytime = modifytime;
	}

	public String getModifytime() {
		return modifytime;
	}

	public void setRiskcode(String riskcode) {
		this.riskcode = riskcode;
	}

	public String getRiskcode() {
		return riskcode;
	}

	public void setSalechnl(String salechnl) {
		this.salechnl = salechnl;
	}

	public String getSalechnl() {
		return salechnl;
	}

	public void setMakedate(Date makedate) {
		this.makedate = makedate;
	}

	public Date getMakedate() {
		return makedate;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getOperator() {
		return operator;
	}

	public void setPayplancode(String payplancode) {
		this.payplancode = payplancode;
	}

	public String getPayplancode() {
		return payplancode;
	}

	public void setSerialno(String serialno) {
		this.serialno = serialno;
	}

	public String getSerialno() {
		return serialno;
	}

	public void setMaketime(String maketime) {
		this.maketime = maketime;
	}

	public String getMaketime() {
		return maketime;
	}

	public void setStartdate(Date startdate) {
		this.startdate = startdate;
	}

	public Date getStartdate() {
		return startdate;
	}

	public void setPayyears(BigDecimal payyears) {
		this.payyears = payyears;
	}

	public BigDecimal getPayyears() {
		return payyears;
	}

	public void setPayintv(BigDecimal payintv) {
		this.payintv = payintv;
	}

	public BigDecimal getPayintv() {
		return payintv;
	}

	public void setModifydate(Date modifydate) {
		this.modifydate = modifydate;
	}

	public Date getModifydate() {
		return modifydate;
	}

	public void setSubsalechnl(String subsalechnl) {
		this.subsalechnl = subsalechnl;
	}

	public String getSubsalechnl() {
		return subsalechnl;
	}

	public void setEnddate(Date enddate) {
		this.enddate = enddate;
	}

	public Date getEnddate() {
		return enddate;
	}

	public Date getValidate() {
		return validate;
	}

	public void setValidate(Date validate) {
		this.validate = validate;
	}

	public void setF01(BigDecimal f01) {
		this.f01 = f01;
	}

	public BigDecimal getF01() {
		return f01;
	}

	public void setF02(BigDecimal f02) {
		this.f02 = f02;
	}

	public BigDecimal getF02() {
		return f02;
	}

	public void setF03(String f03) {
		this.f03 = f03;
	}

	public String getF03() {
		return f03;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "LaratemarginVO [" + "rate=" + rate + "," + "modifytime="
				+ modifytime + "," + "riskcode=" + riskcode + "," + "salechnl="
				+ salechnl + "," + "makedate=" + makedate + "," + "operator="
				+ operator + "," + "payplancode=" + payplancode + ","
				+ "serialno=" + serialno + "," + "maketime=" + maketime + ","
				+ "startdate=" + startdate + "," + "payyears=" + payyears + ","
				+ "payintv=" + payintv + "," + "modifydate=" + modifydate + ","
				+ "subsalechnl=" + subsalechnl + "," + "enddate=" + enddate
				+ "f01=" + f01 + "," + "f02=" + f02 + "," + "f03=" + f03 + "]";
	}
}
