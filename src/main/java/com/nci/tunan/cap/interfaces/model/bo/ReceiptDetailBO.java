package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 发票费用明细表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-11-23 16:33:41
 */
public class ReceiptDetailBO extends BaseBO {
    /** 
    * @Fields serialVersionUID : 序列化 
    */ 
    
    private static final long serialVersionUID = -7394082650062800331L;
    /**
     * @Fields isRiskMain : 是否主险
     */
    private BigDecimal isRiskMain;
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields detailId : 序列号
     */
    private BigDecimal detailId;
    /**
     * @Fields relationId : 发票表主键
     */
    private BigDecimal relationId;
    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;

    public void setIsRiskMain(BigDecimal isRiskMain) {
        this.isRiskMain = isRiskMain;
    }

    public BigDecimal getIsRiskMain() {
        return isRiskMain;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setDetailId(BigDecimal detailId) {
        this.detailId = detailId;
    }

    public BigDecimal getDetailId() {
        return detailId;
    }

    public void setRelationId(BigDecimal relationId) {
        this.relationId = relationId;
    }

    public BigDecimal getRelationId() {
        return relationId;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ReceiptDetailVO [" + "isRiskMain=" + isRiskMain + "," + "busiProdName=" + busiProdName + ","
                + "feeAmount=" + feeAmount + "," + "detailId=" + detailId + "," + "relationId=" + relationId + ","
                + "busiProdCode=" + busiProdCode + "]";
    }
}
