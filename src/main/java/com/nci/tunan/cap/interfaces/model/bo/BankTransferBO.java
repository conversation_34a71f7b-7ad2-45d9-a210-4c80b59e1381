package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * @description BankTransferBO对象
 * <AUTHOR>
 * @date 2015-08-05 15:24:17
 * @.belongToModule 收付费-银行转账
 */
public class BankTransferBO extends BaseVO {

    /**
     * @Fields serialVersionUID : 序列化版本号
     */

    private static final long serialVersionUID = 4456757966531538377L;
    /**
     * @Fields bankAccount1 : 收款银行账户
     */
    private String bankAccount1;
    /**
     * @Fields bankAccount : 银行账号
     */
    private String bankAccount;
    /**
     * @Fields remarks : 备注
     */
    private String remarks;
    /**
     * @Fields btrId : 流水号
     */
    private BigDecimal btrId;
    /**
     * @Fields btrNo : 票据号码
     */
    private String btrNo;
    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;

    /**
     * @Fields arapDate : 收付费日期
     */
    private Date arapDate;

    /**
     * @Fields comfirmDate : 确认日期
     */
    private Date comfirmDate;
    /**
     * @Fields status : 状态
     */
    private String status;
    /**
     * @Fields invoiceDate : 开票日期
     */
    private Date invoiceDate;
    /**
     * @Fields arapFlag : 应收应付类型
     */
    private String arapFlag;
    /**
     * @Fields comfirmBy : 确认人
     */
    private BigDecimal comfirmBy;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;
    
    /** 
     * @Fields corpAction : 企业方账户
     */ 
     private String corpAction;
     
     /** 
     * @Fields companyBankCode : 企业方开户行编码
     */ 
     private String companyBankCode;
     
     /**
      * @Fields ybtOfflineFlag : 银保通脱机单标识  是:1
      */
     private BigDecimal ybtOfflineFlag;

     public BigDecimal getYbtOfflineFlag() {
		return ybtOfflineFlag;
	}

	public void setYbtOfflineFlag(BigDecimal ybtOfflineFlag) {
		this.ybtOfflineFlag = ybtOfflineFlag;
	}

	public String getCorpAction() {
         return corpAction;
     }

     public void setCorpAction(String corpAction) {
         this.corpAction = corpAction;
     }

     public String getCompanyBankCode() {
         return companyBankCode;
     }

     public void setCompanyBankCode(String companyBankCode) {
         this.companyBankCode = companyBankCode;
     }

    public void setBankAccount1(String bankAccount1) {
        this.bankAccount1 = bankAccount1;
    }

    public String getBankAccount1() {
        return bankAccount1;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setBtrId(BigDecimal btrId) {
        this.btrId = btrId;
    }

    public BigDecimal getBtrId() {
        return btrId;
    }

    public void setBtrNo(String btrNo) {
        this.btrNo = btrNo;
    }

    public String getBtrNo() {
        return btrNo;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setComfirmDate(Date comfirmDate) {
        this.comfirmDate = comfirmDate;
    }

    public Date getComfirmDate() {
        return comfirmDate;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setComfirmBy(BigDecimal comfirmBy) {
        this.comfirmBy = comfirmBy;
    }

    public BigDecimal getComfirmBy() {
        return comfirmBy;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public Date getArapDate() {
        return arapDate;
    }

    public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "BankTransferBO [bankAccount1=" + bankAccount1 + ", bankAccount=" + bankAccount + ", remarks=" + remarks
                + ", btrId=" + btrId + ", btrNo=" + btrNo + ", bankUserName=" + bankUserName + ", comfirmDate="
                + comfirmDate + ", status=" + status + ", invoiceDate=" + invoiceDate + ", arapFlag=" + arapFlag
                + ", comfirmBy=" + comfirmBy + ", bankCode=" + bankCode + ", feeAmount=" + feeAmount + ", arapDate="
                + arapDate + "]";
    }

}
