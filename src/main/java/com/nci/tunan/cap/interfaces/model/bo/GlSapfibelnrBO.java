package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 凭证回写GlSapfibelnrBO对象
 * <AUTHOR>
 * @date 2015-07-22 11:54:41
 * @.belongToModule 收付费-凭证回写
 */
public class GlSapfibelnrBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化版本号
     */

    private static final long serialVersionUID = -3426201756713049930L;

    /**
     * @Fields fserialno : 分录数据流水号
     */
    private String fserialno;
    /**
     * @Fields accsum : 金额
     */
    private BigDecimal accsum;
    /**
     * @Fields accdate : 记账日期
     */
    private Date accdate;
    /**
     * @Fields makedate : 入机日期
     */
    private Date makedate;
    /**
     * @Fields sapcomcode : 四级机构
     */
    private String sapcomcode;
    /**
     * @Fields accuitem : 科目代码
     */
    private String accuitem;
    /**
     * @Fields gjahr : 会计年度
     */
    private String gjahr;
    /**
     * @Fields bukrs : 公司代码
     */
    private String bukrs;
    /**
     * @Fields payway : 借贷标志
     */
    private String payway;
    /**
     * @Fields sapdate : 提取日期
     */
    private Date sapdate;
    /**
     * @Fields tqflag : 读取标识
     */
    private String tqflag;
    /**
     * @Fields maketime : 入机时间
     */
    private String maketime;
    /**
     * @Fields batchno : 批次号
     */
    private String batchno;
    /**
     * @Fields currency : 货币
     */
    private String currency;
    /**
     * @Fields belnr : 凭证号
     */
    private String belnr;

    public void setAccsum(BigDecimal accsum) {
        this.accsum = accsum;
    }

    public BigDecimal getAccsum() {
        return accsum;
    }

    public void setFserialno(String fserialno) {
        this.fserialno = fserialno;
    }

    public String getFserialno() {
        return fserialno;
    }

    public void setAccdate(Date accdate) {
        this.accdate = accdate;
    }

    public Date getAccdate() {
        return accdate;
    }

    public void setMakedate(Date makedate) {
        this.makedate = makedate;
    }

    public Date getMakedate() {
        return makedate;
    }

    public void setSapcomcode(String sapcomcode) {
        this.sapcomcode = sapcomcode;
    }

    public String getSapcomcode() {
        return sapcomcode;
    }

    public void setAccuitem(String accuitem) {
        this.accuitem = accuitem;
    }

    public String getAccuitem() {
        return accuitem;
    }

    public void setGjahr(String gjahr) {
        this.gjahr = gjahr;
    }

    public String getGjahr() {
        return gjahr;
    }

    public void setBukrs(String bukrs) {
        this.bukrs = bukrs;
    }

    public String getBukrs() {
        return bukrs;
    }

    public void setPayway(String payway) {
        this.payway = payway;
    }

    public String getPayway() {
        return payway;
    }

    public void setSapdate(Date sapdate) {
        this.sapdate = sapdate;
    }

    public Date getSapdate() {
        return sapdate;
    }

    public void setTqflag(String tqflag) {
        this.tqflag = tqflag;
    }

    public String getTqflag() {
        return tqflag;
    }

    public void setMaketime(String maketime) {
        this.maketime = maketime;
    }

    public String getMaketime() {
        return maketime;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public String getBatchno() {
        return batchno;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }

    public void setBelnr(String belnr) {
        this.belnr = belnr;
    }

    public String getBelnr() {
        return belnr;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "GlSapfibelnrBO [accsum=" + accsum + ", fserialno=" + fserialno + ", accdate=" + accdate + ", makedate="
                + makedate + ", sapcomcode=" + sapcomcode + ", accuitem=" + accuitem + ", gjahr=" + gjahr + ", bukrs="
                + bukrs + ", payway=" + payway + ", sapdate=" + sapdate + ", tqflag=" + tqflag + ", maketime="
                + maketime + ", batchno=" + batchno + ", currency=" + currency + ", belnr=" + belnr + "]";
    }

}
