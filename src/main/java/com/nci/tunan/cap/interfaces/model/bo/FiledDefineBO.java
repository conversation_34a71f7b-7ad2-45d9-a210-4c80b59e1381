package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 字段定义BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class FiledDefineBO extends BaseBO {
    
    /** 
    * @Fields serialVersionUID : 序列化 
    */ 
    
    private static final long serialVersionUID = -1716902719286809533L;
    /**
     * @Fields filedSize : 长度
     */
    private BigDecimal filedSize;
    /**
     * @Fields rowAliasName : 列别名
     */
    private String rowAliasName;
    /**
     * @Fields fixedColumn : 固定列内容
     */
    private String fixedColumn;
    /**
     * @Fields orderNum : 序号
     */
    private BigDecimal orderNum;
    /**
     * @Fields alignment : 对齐方式
     */
    private String alignment;
    /**
     * @Fields filedDefineId : 序列号
     */
    private BigDecimal filedDefineId;
    /**
     * @Fields fillCharacter : 填充字符
     */
    private String fillCharacter;
    /**
     * @Fields rowName : 列名
     */
    private String rowName;

    public void setFiledSize(BigDecimal filedSize) {
        this.filedSize = filedSize;
    }

    public BigDecimal getFiledSize() {
        return filedSize;
    }

    public void setRowAliasName(String rowAliasName) {
        this.rowAliasName = rowAliasName;
    }

    public String getRowAliasName() {
        return rowAliasName;
    }

    public void setFixedColumn(String fixedColumn) {
        this.fixedColumn = fixedColumn;
    }

    public String getFixedColumn() {
        return fixedColumn;
    }

    public void setOrderNum(BigDecimal orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getOrderNum() {
        return orderNum;
    }

    public void setAlignment(String alignment) {
        this.alignment = alignment;
    }

    public String getAlignment() {
        return alignment;
    }

    public void setFiledDefineId(BigDecimal filedDefineId) {
        this.filedDefineId = filedDefineId;
    }

    public BigDecimal getFiledDefineId() {
        return filedDefineId;
    }

    public void setFillCharacter(String fillCharacter) {
        this.fillCharacter = fillCharacter;
    }

    public String getFillCharacter() {
        return fillCharacter;
    }

    public void setRowName(String rowName) {
        this.rowName = rowName;
    }

    public String getRowName() {
        return rowName;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "FiledDefineBO [filedSize=" + filedSize + ", rowAliasName=" + rowAliasName + ", fixedColumn="
                + fixedColumn + ", orderNum=" + orderNum + ", alignment=" + alignment + ", filedDefineId="
                + filedDefineId + ", fillCharacter=" + fillCharacter + ", rowName=" + rowName + "]";
    }

}
