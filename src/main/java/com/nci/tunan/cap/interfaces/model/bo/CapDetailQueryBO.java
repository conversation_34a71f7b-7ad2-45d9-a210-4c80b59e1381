package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 付款信息CapDetailQueryBO查询对象
 * <AUTHOR>
 * @date 2015-1-21 
 * @.belongToModule 收付费-实收实付信息查询
 */
public class CapDetailQueryBO extends BaseBO {
    
    /** 
    * @Fields serialVersionUID : 序列化版本号
    */ 
    
    private static final long serialVersionUID = -6991113467647792977L;
  
    /** 
    * @Fields unitNumber : 收付唯一号
    */ 
    private String unitNumber;

    /**
     * @Fields arapFlag : 应收应付类型(对应 应收应付表)
     */
    private String arapFlag;
    /**
     * @Fields bankCode : 银行代码
     */
    private String actualBankcode;
    /** 
    * @Fields actualBankAccount : 银行账号
    */ 
    private String actualBankAccount; 
    /**
     * @Fields policyCode : 保单号
     */
    private String policyCode;
    /**
     * @Fields payMode : 收付费方式
     */
    private String payMode;
    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;
    /**
     * @Fields feeStartTime : 实际收付起始日
     */
    private Date feeStartTime;
    /**
     * @Fields feeEndTime : 实际收付结束日
     */
    private Date feeEndTime;
    /**
     * @Fields dueStartTime : 应收付起始日
     */
    private Date dueStartTime;
    /**
     * @Fields dueEndTime : 应收付结束日
     */
    private Date dueEndTime;
    /**
     * @Fields certiCode : 收付款人证件号码
     */
    private String certiCode;

    /**
     * @Fields businessCode : 业务号
     */
    private String businessCode;
    /**
     * @Fields feeStatus : (收付状态)费用状态01；待收付 02；已收付 03；回退 04；中止 05；不可收付
     */
    private String feeStatus;

    /** 
    * @Fields feeAmount : 显示数据时需要用的真正数据库中的字段
    */ 
    private BigDecimal feeAmount; 

    /** 
    * @Fields dueTime : 应缴应付日
    */ 
    private Date dueTime;

    /** 
    * @Fields finishTime : 实收实付日期
    */ 
    private Date finishTime;
    
    /** 
    * @Fields busiProdName : 险种名称
    */ 
    private String busiProdName;
    
    /** 
    * @Fields busiProdCode : 险种代码
    */ 
    private String busiProdCode;
    
    /** 
    * @Fields premFreq : 缴费频率 
    */ 
    private BigDecimal premFreq;
    
    /** 
    * @Fields premFreqName : 缴费频率名称
    */ 
    private String premFreqName;
    
    /** 
    * @Fields businessTypeName : 业务类型名称 
    */ 
    private String businessTypeName;
    
    /** 
    * @Fields feeAmountTotal : 收付笔数 
    */ 
    private BigDecimal feeAmountTotal;
    
	/** 
	* @Fields feeToAccountTime : 实收实付到账日期
	*/ 
	private Date feeToAccountTime; 
    
    /** 
    * @Fields businessType : 业务类型
    */ 
    private String businessType;
    
    /** 
    * @Fields arapBankAccount : 收付银行账号
    */ 
    private String arapBankAccount;
    
    /** 
    * @Fields arapBankCode : 收付银行代码 
    */ 
    private String arapBankCode;
    
    /** 
    * @Fields certiType : 证件类型
    */ 
    private String certiType;
    
    /** 
    * @Fields feeAmountSum : 交费总额
    */ 
    private BigDecimal feeAmountSum;
      
    /** 
    * @Fields startPayDate : 付款起期
    */ 
    private Date  startPayDate;
    
    /** 
    * @Fields endPayDate : 付款止期 
    */ 
    private Date  endPayDate;
    
    /** 
    * @Fields payrefno : 实收实付号
    */ 
    private String payrefno;
    
    /** 
    * @Fields payModeName : 支付方式名称 
    */ 
    private String payModeName;
    
    /** 
    * @Fields makeDate : 入机时间
    */ 
    private Date  makeDate;
    
    
    

    public String getUnitNumber() {
		return unitNumber;
	}

	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public String getArapFlag() {
		return arapFlag;
	}

	public void setArapFlag(String arapFlag) {
		this.arapFlag = arapFlag;
	}

	public String getActualBankcode() {
		return actualBankcode;
	}

	public void setActualBankcode(String actualBankcode) {
		this.actualBankcode = actualBankcode;
	}

	public String getActualBankAccount() {
		return actualBankAccount;
	}

	public void setActualBankAccount(String actualBankAccount) {
		this.actualBankAccount = actualBankAccount;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getPayMode() {
		return payMode;
	}

	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}

	public String getPayeeName() {
		return payeeName;
	}

	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}

	public Date getFeeStartTime() {
		return feeStartTime;
	}

	public void setFeeStartTime(Date feeStartTime) {
		this.feeStartTime = feeStartTime;
	}

	public Date getFeeEndTime() {
		return feeEndTime;
	}

	public void setFeeEndTime(Date feeEndTime) {
		this.feeEndTime = feeEndTime;
	}

	public Date getDueStartTime() {
		return dueStartTime;
	}

	public void setDueStartTime(Date dueStartTime) {
		this.dueStartTime = dueStartTime;
	}

	public Date getDueEndTime() {
		return dueEndTime;
	}

	public void setDueEndTime(Date dueEndTime) {
		this.dueEndTime = dueEndTime;
	}

	public String getCertiCode() {
		return certiCode;
	}

	public void setCertiCode(String certiCode) {
		this.certiCode = certiCode;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public Date getDueTime() {
		return dueTime;
	}

	public void setDueTime(Date dueTime) {
		this.dueTime = dueTime;
	}

	public Date getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}

	public Date getFeeToAccountTime() {
		return feeToAccountTime;
	}

	public void setFeeToAccountTime(Date feeToAccountTime) {
		this.feeToAccountTime = feeToAccountTime;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
   
 	public String getBusiProdName() {
		return busiProdName;
	}

	public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public BigDecimal getPremFreq() {
		return premFreq;
	}

	public void setPremFreq(BigDecimal premFreq) {
		this.premFreq = premFreq;
	}

	public String getPremFreqName() {
		return premFreqName;
	}

	public void setPremFreqName(String premFreqName) {
		this.premFreqName = premFreqName;
	}

	public BigDecimal getFeeAmountTotal() {
		return feeAmountTotal;
	}

	public void setFeeAmountTotal(BigDecimal feeAmountTotal) {
		this.feeAmountTotal = feeAmountTotal;
	}
	

	public String getBusinessTypeName() {
		return businessTypeName;
	}

	public void setBusinessTypeName(String businessTypeName) {
		this.businessTypeName = businessTypeName;
	}
	
	public String getArapBankAccount() {
		return arapBankAccount;
	}

	public void setArapBankAccount(String arapBankAccount) {
		this.arapBankAccount = arapBankAccount;
	}

	public String getArapBankCode() {
		return arapBankCode;
	}

	public void setArapBankCode(String arapBankCode) {
		this.arapBankCode = arapBankCode;
	}

	public String getCertiType() {
		return certiType;
	}

	public void setCertiType(String certiType) {
		this.certiType = certiType;
	}
	
	public BigDecimal getFeeAmountSum() {
		return feeAmountSum;
	}

	public void setFeeAmountSum(BigDecimal feeAmountSum) {
		this.feeAmountSum = feeAmountSum;
	}

	public Date getStartPayDate() {
		return startPayDate;
	}

	public void setStartPayDate(Date startPayDate) {
		this.startPayDate = startPayDate;
	}

	public Date getEndPayDate() {
		return endPayDate;
	}

	public void setEndPayDate(Date endPayDate) {
		this.endPayDate = endPayDate;
	}

	public String getPayrefno() {
		return payrefno;
	}

	public void setPayrefno(String payrefno) {
		this.payrefno = payrefno;
	}
	
	public String getPayModeName() {
		return payModeName;
	}

	public void setPayModeName(String payModeName) {
		this.payModeName = payModeName;
	}
	
	public Date getMakeDate() {
		return makeDate;
	}

	public void setMakeDate(Date makeDate) {
		this.makeDate = makeDate;
	}

	@Override
 	public String getBizId() {
 		return null;
 	}

	@Override
	public String toString() {
		return "CapDetailQueryBO [unitNumber=" + unitNumber + ", arapFlag="
				+ arapFlag + ", actualBankcode=" + actualBankcode
				+ ", actualBankAccount=" + actualBankAccount + ", policyCode="
				+ policyCode + ", payMode=" + payMode + ", payeeName="
				+ payeeName + ", feeStartTime=" + feeStartTime
				+ ", feeEndTime=" + feeEndTime + ", dueStartTime="
				+ dueStartTime + ", dueEndTime=" + dueEndTime + ", certiCode="
				+ certiCode + ", businessCode=" + businessCode + ", feeStatus="
				+ feeStatus + ", feeAmount=" + feeAmount + ", dueTime="
				+ dueTime + ", finishTime=" + finishTime + ", busiProdName="
				+ busiProdName + ", busiProdCode=" + busiProdCode
				+ ", premFreq=" + premFreq + ", premFreqName=" + premFreqName
				+ ", businessTypeName=" + businessTypeName
				+ ", feeAmountTotal=" + feeAmountTotal + ", feeToAccountTime="
				+ feeToAccountTime + ", businessType=" + businessType
				+ ", arapBankAccount=" + arapBankAccount + ", arapBankCode="
				+ arapBankCode + ", certiType=" + certiType + ", feeAmountSum="
				+ feeAmountSum + ", startPayDate=" + startPayDate
				+ ", endPayDate=" + endPayDate + ", payrefno=" + payrefno
				+ ", payModeName=" + payModeName + ", makeDate=" + makeDate
				+ "]";
	}
   
}
