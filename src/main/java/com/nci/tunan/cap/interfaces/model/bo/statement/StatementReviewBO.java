package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 日结复核BO类
 * @description 
 * @<NAME_EMAIL> 
 * @date 2024-1-8 下午2:00:09
 */
public class StatementReviewBO extends BaseBO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */
    private static final long serialVersionUID = 1L;
    /**
     * @Fields listId : 日结id
     */
    private BigDecimal listId;
    /**
     * @Fields lineNo : 序号
     */
    private BigDecimal lineNo;
    /**
     * @Fields errorFlag : 是否有差异说明
     */
    private String errorFlag;
    /**
     * @Fields startDate : 查询开始日期
     */
    private Date startDate;
    /**
     * @Fields endDate : 查询结束日期
     */
    private Date endDate;
    /**
     * @Fields reportType : 日结类型，NB、XQ、CS、CLM、BCP、JZ
     */
    private String reportType;
    /**
     * @Fields statementPeriod : D-日;M-月
     */
    private String statementPeriod;
    /**
     * @Fields statementType : 日结类型代码，如： CS001
     */
    private String statementType;
    /**
     * @Fields statementType : 日结类型代码集合，如： CS001
     */
    private List<String> statementTypeList;
    /**
     * @Fields statementType : 日结名称，如： 新契约日结
     */
    private String statementName;
    /**
     * @Fields organCode : 机构代码
     */
    private String organCode;
    /**
     * @Fields organType : 机构类型，ManageOrg-管理机构;CapOrg-收付费机构
     */
    private String organType;
    /**
     * @Fields sapOrganCode : SAP机构代码
     */
    private String sapOrganCode;
    /**
     * @Fields statementDate : 日结日期/月份
     */
    private Date statementDate;
    /**
     * @Fields fileName : 日结文件名称
     */
    private String fileName;
    /**
     * @Fields fileType : 日结文件名称后缀
     */
    private String fileType;
    /**
     * @Fields statementStatus : 归档状态，代码表T_STATEMENT_STATUS
     */
    private String statementStatus;
    /**
     * @Fields createOperator : 日结文件创建人
     */
    private BigDecimal createOperator;
    /**
     * @Fields createTime : 日结文件创建时间
     */
    private Date createTime;
    /**
     * @Fields tabulateOperator : 制表人
     */
    private BigDecimal tabulateOperator;
    /**
     * @Fields tabulateTime : 制表时间
     */
    private Date tabulateTime;
    /**
     * @Fields reviewOperator : 复核人
     */
    private BigDecimal reviewOperator;
    /**
     * @Fields reviewTime : 复核时间
     */
    private Date reviewTime;
    /**
     * @Fields reviewResult : 复核结论，1-通过;0-不通过
     */
    private BigDecimal reviewResult;
    /**
     * @Fields reviewCasue : 复核不通过原因
     */
    private String reviewCasue;
    /**
     * @Fields isFiling : 是否推送电子档案系统，1-是；0-否。
     */
    private BigDecimal isFiling;
    /**
     * @Fields filingOperator : 归档人/归档驳回人
     */
    private BigDecimal filingOperator;
    /**
     * @Fields filingTime : 归档/归档驳回时间
     */
    private Date filingTime;
    /**
     * @Fields filingRefuseCasue : 归档驳回原因
     */
    private String filingRefuseCasue;
    /**
     * @Fields filingProcess : 日结文件推送进度：1-上传FTP成功；2-上传FTP失败；3-通知成功；4-通知失败
     */
    private String filingProcess;
    /**
     * @Fields filingPath : 日结文件归档路径
     */
    private String filingPath;
    /**
     * @Fields taskId : 日结文件推送任务ID
     */
    private String taskId;
    /**
     * @Fields filePushTime : 日结文件推送时间
     */
    private Date filePushTime;
    /**
     * @Fields fileInfoPushTime : 归档文件信息通知时间
     */
    private Date fileInfoPushTime;
    /**
     * @Fields fileInfoPushMsg : 归档通知返回失败原因
     */
    private String fileInfoPushMsg;
    /**
     * @Fields filingResultMsg : 归档查询返回失败原因
     */
    private String filingResultMsg;
    /**
     * @Fields statementClobId : 日结报文ID，T_STATEMENT_TO_SAP表ID
     */
    private BigDecimal statementClobId;

    /** 
     * @Fields onlyBranchOrgan :  是否仅查询分公司本级数据 1-是 0-否
     */ 
    private BigDecimal onlyBranchOrgan;
    
    /** 
     * @Fields existReportData :  日结是否有数据 1-是 0-否
     */ 
    private BigDecimal existReportData;
    

    public BigDecimal getOnlyBranchOrgan() {
        return onlyBranchOrgan;
    }

    public void setOnlyBranchOrgan(BigDecimal onlyBranchOrgan) {
        this.onlyBranchOrgan = onlyBranchOrgan;
    }

    public BigDecimal getExistReportData() {
        return existReportData;
    }

    public void setExistReportData(BigDecimal existReportData) {
        this.existReportData = existReportData;
    }
    
    public String getErrorFlag() {
        return errorFlag;
    }

    public void setErrorFlag(String errorFlag) {
        this.errorFlag = errorFlag;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getStatementPeriod() {
        return statementPeriod;
    }

    public void setStatementPeriod(String statementPeriod) {
        this.statementPeriod = statementPeriod;
    }

    public String getStatementType() {
        return statementType;
    }

    public void setStatementType(String statementType) {
        this.statementType = statementType;
    }

    public String getStatementName() {
        return statementName;
    }

    public void setStatementName(String statementName) {
        this.statementName = statementName;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganType() {
        return organType;
    }

    public void setOrganType(String organType) {
        this.organType = organType;
    }

    public String getSapOrganCode() {
        return sapOrganCode;
    }

    public void setSapOrganCode(String sapOrganCode) {
        this.sapOrganCode = sapOrganCode;
    }

    public Date getStatementDate() {
        return statementDate;
    }

    public void setStatementDate(Date statementDate) {
        this.statementDate = statementDate;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(String statementStatus) {
        this.statementStatus = statementStatus;
    }

    public BigDecimal getCreateOperator() {
        return createOperator;
    }

    public void setCreateOperator(BigDecimal createOperator) {
        this.createOperator = createOperator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public BigDecimal getTabulateOperator() {
        return tabulateOperator;
    }

    public void setTabulateOperator(BigDecimal tabulateOperator) {
        this.tabulateOperator = tabulateOperator;
    }

    public Date getTabulateTime() {
        return tabulateTime;
    }

    public void setTabulateTime(Date tabulateTime) {
        this.tabulateTime = tabulateTime;
    }

    public BigDecimal getReviewOperator() {
        return reviewOperator;
    }

    public void setReviewOperator(BigDecimal reviewOperator) {
        this.reviewOperator = reviewOperator;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public BigDecimal getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(BigDecimal reviewResult) {
        this.reviewResult = reviewResult;
    }

    public String getReviewCasue() {
        return reviewCasue;
    }

    public void setReviewCasue(String reviewCasue) {
        this.reviewCasue = reviewCasue;
    }

    public BigDecimal getIsFiling() {
        return isFiling;
    }

    public void setIsFiling(BigDecimal isFiling) {
        this.isFiling = isFiling;
    }

    public BigDecimal getFilingOperator() {
        return filingOperator;
    }

    public void setFilingOperator(BigDecimal filingOperator) {
        this.filingOperator = filingOperator;
    }

    public Date getFilingTime() {
        return filingTime;
    }

    public void setFilingTime(Date filingTime) {
        this.filingTime = filingTime;
    }

    public String getFilingRefuseCasue() {
        return filingRefuseCasue;
    }

    public void setFilingRefuseCasue(String filingRefuseCasue) {
        this.filingRefuseCasue = filingRefuseCasue;
    }

    public String getFilingProcess() {
        return filingProcess;
    }

    public void setFilingProcess(String filingProcess) {
        this.filingProcess = filingProcess;
    }

    public String getFilingPath() {
        return filingPath;
    }

    public void setFilingPath(String filingPath) {
        this.filingPath = filingPath;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Date getFilePushTime() {
        return filePushTime;
    }

    public void setFilePushTime(Date filePushTime) {
        this.filePushTime = filePushTime;
    }

    public Date getFileInfoPushTime() {
        return fileInfoPushTime;
    }

    public void setFileInfoPushTime(Date fileInfoPushTime) {
        this.fileInfoPushTime = fileInfoPushTime;
    }

    public String getFileInfoPushMsg() {
        return fileInfoPushMsg;
    }

    public void setFileInfoPushMsg(String fileInfoPushMsg) {
        this.fileInfoPushMsg = fileInfoPushMsg;
    }

    public String getFilingResultMsg() {
        return filingResultMsg;
    }

    public void setFilingResultMsg(String filingResultMsg) {
        this.filingResultMsg = filingResultMsg;
    }

    public BigDecimal getStatementClobId() {
        return statementClobId;
    }

    public void setStatementClobId(BigDecimal statementClobId) {
        this.statementClobId = statementClobId;
    }
    
    public List<String> getStatementTypeList() {
        return statementTypeList;
    }

    public void setStatementTypeList(List<String> statementTypeList) {
        this.statementTypeList = statementTypeList;
    }
    
    public BigDecimal getLineNo() {
        return lineNo;
    }

    public void setLineNo(BigDecimal lineNo) {
        this.lineNo = lineNo;
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    @Override
    public String getBizId() {
        // TODO Auto-generated method stub
        return null;
    }

}
