package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description BankDiskConfigVO对象
 * <AUTHOR>
 * @date 2015-08-20 18:21:36
 * @.belongToModule 收付费-制返盘
 */
public class BankDiskConfigBO extends BaseBO {

    /**
     * @Fields serialVersionUID : SUID
     */

    private static final long serialVersionUID = -1328303336673760741L;
    /**
     * @Fields rangeValue : 值
     */
    private BigDecimal rangeValue;
    /**
     * @Fields unit : 取值单位
     */
    private BigDecimal unit;

    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;
    /**
     * @Fields bankName : 银行名称
     */
    private String bankName;
    /**
     * @Fields organName : 管理机构名称
     */
    private String organName;
    /**
     * @Fields organCode : 管理机构代码
     */
    private String organCode;
    /**
     * @Fields channelType : 渠道类型
     */
    private String channelType;
    /**
     * @Fields diskConfigId : 参数ID
     */
    private BigDecimal diskConfigId;
    /**
     * @Fields arapFlag : 收付标识
     */
    private String arapFlag;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields bankDiskType : 处理内容 1 预制盘 2 制盘 3 返盘
     */
    private BigDecimal bankDiskType;

    /** 
    * @Fields dataScope : 数据处理范围（年/月/日）
    */ 
    private int dataScope;
    /** 
    * @Fields dataScopeBy : 数据处理范围取值
    */ 
    private int dataScopeBy;
    /** 
    * @Fields isYbtBankTaxt : 是否银保通标志
    */ 
    private String  isYbtBankTaxt;
    /**
     * @Fields specialAccountFlag : 特殊账户标记  1-中银保信个人养老金账户
     */
     private String specialAccountFlag;
     
     /** 
    * @Fields commitNum : 一次提交数量
    */ 
    private String commitNum;
    
    //#161949 关于针对绩优业务员客户优化制返盘功能的需求 by fuyh begin
    /**
     * @Fields meritFlag : 绩优等级
     */
    private BigDecimal meritFlag;
    
    /**
     * @Fields @Fields : 大额拆分标识，0否，1是
     */
    private BigDecimal largeSplitFlag;
    

   public BigDecimal getLargeSplitFlag() {
       return largeSplitFlag;
   }

   public void setLargeSplitFlag(BigDecimal largeSplitFlag) {
       this.largeSplitFlag = largeSplitFlag;
   }

    public BigDecimal getMeritFlag() {
        return meritFlag;
    }

    public void setMeritFlag(BigDecimal meritFlag) {
        this.meritFlag = meritFlag;
    }
    //#161949 关于针对绩优业务员客户优化制返盘功能的需求 by fuyh end
     

    public String getCommitNum() {
        return commitNum;
    }

    public void setCommitNum(String commitNum) {
        this.commitNum = commitNum;
    }

    public String getSpecialAccountFlag() {
		return specialAccountFlag;
	}

	public void setSpecialAccountFlag(String specialAccountFlag) {
		this.specialAccountFlag = specialAccountFlag;
	}
    /** 
    * @Fields branchCodeList : 分公司代码集合
    */ 
    private List<String> branchCodeList = new ArrayList<String>();
    /** 
    * @Fields channelTypeList : 渠道集合
    */ 
    private List<String> channelTypeList =  new ArrayList<String>();
    /** 
    * @Fields ybtList : 银保通集合
    */ 
    private List<String> ybtList =  new ArrayList<String>();

    public List<String> getChannelTypeList() {
        return channelTypeList;
    }

    public void setChannelTypeList(List<String> channelTypeList) {
        this.channelTypeList = channelTypeList;
    }

    public List<String> getBranchCodeList() {
        return branchCodeList;
    }

    public void setBranchCodeList(List<String> branchCodeList) {
        this.branchCodeList = branchCodeList;
    }

    public void setRangeValue(BigDecimal rangeValue) {
        this.rangeValue = rangeValue;
    }

    public BigDecimal getRangeValue() {
        return rangeValue;
    }

    public void setUnit(BigDecimal unit) {
        this.unit = unit;
    }

    public BigDecimal getUnit() {
        return unit;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setDiskConfigId(BigDecimal diskConfigId) {
        this.diskConfigId = diskConfigId;
    }

    public BigDecimal getDiskConfigId() {
        return diskConfigId;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankDiskType(BigDecimal bankDiskType) {
        this.bankDiskType = bankDiskType;
    }

    public BigDecimal getBankDiskType() {
        return bankDiskType;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public int getDataScope() {
        return dataScope;
    }

    public void setDataScope(int dataScope) {
        this.dataScope = dataScope;
    }

    public int getDataScopeBy() {
        return dataScopeBy;
    }

    public void setDataScopeBy(int dataScopeBy) {
        this.dataScopeBy = dataScopeBy;
    }

    @Override
    public String toString() {
        return "BankDiskConfigBO [meritFlag=" + meritFlag + ", rangeValue=" + rangeValue + ", unit=" + unit + ", bankName=" + bankName
                + ", derivType=" + derivType + ", organName=" + organName + ", organCode=" + organCode
                + ", channelType=" + channelType + ", diskConfigId=" + diskConfigId + ", arapFlag=" + arapFlag
                + ", bankCode=" + bankCode + ", bankDiskType=" + bankDiskType + ", dataScope=" + dataScope
                + ", dataScopeBy=" + dataScopeBy + ", branchCodeList=" + branchCodeList + "]";
    }

	public List<String> getYbtList() {
		return ybtList;
	}

	public void setYbtList(List<String> ybtList) {
		this.ybtList = ybtList;
	}

	public String getIsYbtBankTaxt() {
		return isYbtBankTaxt;
	}

	public void setIsYbtBankTaxt(String isYbtBankTaxt) {
		this.isYbtBankTaxt = isYbtBankTaxt;
	}

}
