package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description LacommisionBO对象
 * <AUTHOR>
 * @date 2018-08-27 11:32:52
 * @.belongToModule 收付费-佣金扎账
 */
public class LacommisionBO extends BaseBO {

	/**
	 * @Fields serialVersionUID : SUID
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields commisionsn : 系列号
	 */
	private String commisionsn;
	/**
	 * @Fields unitNumber : 收付费唯一号
	 */
	private String unitNumber;
	/**
	 * @Fields appntno : 投保人代码
	 */
	private String appntno;
	/**
	 * @Fields salechnlcode : 渠道码
	 */
	private String salechnlcode;
	/**
	 * @Fields feeType : 费用类型
	 */
	private String feeType;
	/**
	 * @Fields confdate : 确认日期
	 */
	private Date confdate;
	/**
	 * @Fields dateid : 数据日期ID
	 */
	private BigDecimal dateid;
	/**
	 * @Fields proposalno : 投保单号
	 */
	private String proposalno;
	/**
	 * @Fields agentcode : 代理人编码
	 */
	private String agentcode;
	/**
	 * @Fields serviceCodeFrom : null
	 */
	private String serviceCodeFrom;
	/**
	 * @Fields bookkeepingFlag : 是否可记账标志
	 */
	private BigDecimal bookkeepingFlag;
	/**
	 * @Fields custgetpoldate : 客户签收日期
	 */
	private Date custgetpoldate;
	/**
	 * @Fields agentcomcode : 代理机构代码
	 */
	private String agentcomcode;
	/**
	 * @Fields standfycrate : 标准提佣比例
	 */
	private BigDecimal standfycrate;
	/**
	 * @Fields prtno : 保单印刷号
	 */
	private String prtno;
	/**
	 * @Fields riskcode : 险种编码
	 */
	private String riskcode;
	/**
	 * @Fields amount : 金额
	 */
	private BigDecimal amount;
	/**
	 * @Fields payyear : 交费年度
	 */
	private BigDecimal payyear;
	/**
	 * @Fields payplancode : 交费计划编码
	 */
	private String payplancode;
	/**
	 * @Fields ybtflag : 银保通标志
	 */
	private String ybtflag;
	/**
	 * @Fields standprem : 折标比例
	 */
	private BigDecimal standprem;
	/**
	 * @Fields subinputtype : E保单出单标识
	 */
	private String subinputtype;
	/**
	 * @Fields directwage : 直接佣金
	 */
	private BigDecimal directwage;
	/**
	 * @Fields mainpayterm : 期缴主险缴费年期
	 */
	private BigDecimal mainpayterm;
	/**
	 * @Fields feeopertypecode : 业务类型代码
	 */
	private String feeopertypecode;
	/**
	 * @Fields premArapListId : 应收应付ID
	 */
	private BigDecimal premArapListId;
	/**
	 * @Fields unitNumberFrom : 收付唯一号来源
	 */
	private String unitNumberFrom;
	/**
	 * @Fields lastpaytodate : 原交至日期
	 */
	private Date lastpaytodate;
	/**
	 * @Fields scTab : scTab
	 */
	private String scTab;
	/**
	 * @Fields signdate : 签单日期
	 */
	private Date signdate;
	/**
	 * @Fields managecomcode : 管理机构代码
	 */
	private String managecomcode;
	/**
	 * @Fields contno : 保单号
	 */
	private String contno;
	/**
	 * @Fields policyId : 保单ID
	 */
	private BigDecimal policyId;
	/**
	 * @Fields serviceCode : 保全项简写码
	 */
	private String serviceCode;
	/**
	 * @Fields fycrate : 标准提佣比例
	 */
	private BigDecimal fycrate;
	/**
	 * @Fields mainriskflag : 主附险标识
	 */
	private String mainriskflag;
	/**
	 * @Fields withdrawType : 记账业务类型
	 */
	private String withdrawType;
	/**
	 * @Fields payintvcode : 缴费间隔代码
	 */
	private BigDecimal payintvcode;
	/**
	 * @Fields getpoldate : 客户签收日期
	 */
	private Date getpoldate;
	/**
	 * @Fields polno : 保单险种号
	 */
	private String polno;
	/**
	 * @Fields BigDecimal : null
	 */
	private BigDecimal coverageyear;
	/**
	 * @Fields rate : 标准提佣比例
	 */
	private BigDecimal rate;
	/**
	 * @Fields insuredno : 被保人代码
	 */
	private String insuredno;
	/**
	 * @Fields premium : 规模保费
	 */
	private BigDecimal premium;
	/**
	 * @Fields standpremrate : 折标比例
	 */
	private BigDecimal standpremrate;
	/**
	 * @Fields premArapListIdFrom : 应收应付ID来源
	 */
	private BigDecimal premArapListIdFrom;
	/**
	 * @Fields feeStatus : 收付状态
	 */
	private String feeStatus;
	/**
	 * @Fields dutycode : 可选责任代码
	 */
	private String dutycode;
	/**
	 * @Fields valueprem : 价值保费
	 */
	private BigDecimal valueprem;
	/**
	 * @Fields rbflag : 回退标识
	 */
	private String rbflag;
	/**
	 * @Fields paytermcode : 交费年期代码
	 */
	private BigDecimal paytermcode;
	/**
	 * @Fields paycount : 缴费次数
	 */
	private BigDecimal paycount;
	/**
	 * @Fields policyYear : 保单年度
	 */
	private BigDecimal policyYear;
	/**
	 * @Fields mainpayintv : 期缴主险缴费方式
	 */
	private BigDecimal mainpayintv;
	/**
	 * @Fields paydate : 缴费日期
	 */
	private Date paydate;
	/**
	 * @Fields cvalidate : 险种生效日期
	 */
	private Date cvalidate;
	/**
	 * @Fields enteraccdate : 到账日期
	 */
	private Date enteraccdate;
	/**
	 * @Fields fyc : fyc
	 */
	private BigDecimal fyc;
	/**
	 * @Fields businessCode : 业务号
	 */
	private String businessCode;
	/**
	 * @Fields hesitateFlag : 犹豫期标识
	 */
	private BigDecimal hesitateFlag;
	/**
	 * @Fields formerId : 转投前保单id
	 */
	private BigDecimal formerId;
	/**
	 * @Fields subsalechnl : 销售渠道
	 */
	private String subsalechnl;
	/**
	 * laratecommisionBO ： 佣金bo
	 */
	private LaratecommisionBO laratecommisionBO;
	/**
	 * @Fields batchdate : 批处理指定执行日
	 */
	private String batchdate;
	/**
	 * @Fields policyCodeList : 批处理指定保单号
	 */
	private List<String> policyCodeList;
	/**
	 * @Fields perFlag : 开门红标识
	 */
	private BigDecimal perFlag;
	/**
	 * @Fields tmakedate : 交易日期
	 */
	private Date tmakedate;
	/**
	 * @Fields cal_commision : 扎帐计算标识
	 */
	private BigDecimal calCommision;
	/**
	 * @Fields newAgentcode : 新代理人号
	 */
	private String newAgentcode;
	/**
	 * @Fields changedate : 变更日期
	 */
	private Date changedate;
	/**
	 * @Fields agentrate : 银代代理人分配比例
	 */
	private BigDecimal agentrate;
	/**
	 * @Fields channeltype : 保单销售渠道
	 */
	private String channeltype;
	/**
	 * @Fields calcount : 件数
	 */
	private BigDecimal calcount;
	/**
	 * @Fields calcount : 收付费记录插入时间
	 */
	private Date inserttime;
	/**
	 * @Fields refeflag : 参考项
	 */
	private String refeflag;
	/**
	 * @Fields policyFlag : 保单标识
	 */
	private String policyFlag;
	/**
	 * @Fields branchCode : 保单管理机构
	 */
	private String branchCode;
	/**
	 * @Fields saleagentcode : 保单销售代理人
	 */
	private String saleagentcode;
	/**
	 * @Fields agentStatus : 代理人状态
	 */
	private BigDecimal agentStatus;
	/**
	 * @Fields agentgrade : 代理人职级
	 */
	private String agentGrade;
	/**
	 * @Fields extraFycrate : 额外佣金率
	 */
	private BigDecimal extraFycrate;
	/**
	 * @Fields extraRate : 额外佣金
	 */
	private BigDecimal extraFyc;
	/**
	 * @Fields derivType : 业务来源
	 */
	private String derivType;
	/**
	 * @Fields emakedate : E保通出单电子投保书的上传日期
	 */
	private Date emakedate;
	/**
	 * @Fields customercaldate : 电话回访日期
	 */
	private Date customercaldate;
	/**
	 * @Fields caldate : 佣金计算年月
	 */
	private Date caldate;
	/**
	 * @Fields wageno : 佣金年月
	 */
	private String wageno;
	/**
	 * @Fields applyDate : 投保日期
	 */
	private Date applyDate;
	/**
	 * @Fields applyTime : 投保时间
	 */
	private String applyTime;
	/**
	 * @Fields coverperiodtype : 保障期间类型
	 */
	private BigDecimal coverperiodtype;
	/**
	 * @Fields renewflag : 险种转换标识
	 */
	private String renewflag;
	/**
	 * @Fields maturitydate : 满期日
	 */
	private Date maturitydate;
	/**
	 * @Fields chargePeriod : 缴费年期类型
	 */
	private String chargePeriod;
	/**
	 * @Fields initialValidateDate : 续保保单的原始生效日期
	 */
	private Date initialValidateDate;
	/**
	 * @Fields busiApplyDate : 续保保单的原始生效日期
	 */
	private Date busiApplyDate;
	/**
	 * @Fields renewOption : 险种定义表保证续保标识
	 */
	private String renewOption;
	/**
	 * @Fields policyReinsureFlag : 保单重投标识
	 */
	private String policyReinsureFlag;
	/**
	 * @Fields processTime : 处理时间
	 */
	private Date processTime;
	/**
	 * @Fields oldcontno : 旧保单号
	 */
	private String oldcontno;
	/**
	 * @Fields commisionFlag : 扎帐标识
	 */
	private String commisionFlag;
	/**
	 * @Fields issueFee : 出单费用
	 */
	private BigDecimal issueFee;
	/**
	 * @Fields issueFee : 直接手续费
	 */
	private BigDecimal directFee;
	/**
	 * @Fields banknrtFalg : 银代非实时标识
	 */
	private BigDecimal banknrtFalg;
	/**
	 * @Fields directFeeRate : 直接手续费率
	 */
	private BigDecimal directFeeRate;
	/**
	 * @Fields extraPremium : 加费
	 */
	private BigDecimal extraPremium;
	/**
	 * @Fields cooperationProtocolId : 合作中介机构协议号
	 */
	private String cooperationProtocolId;
	/**
	 * @Fields oldriskcode : 险种转换旧险种号
	 */
	private String oldriskcode;
	/**
	 * @Fields fycCom : 机构佣金
	 */
	private BigDecimal fycCom;
	/**
	 * @Fields fycrateCom : 机构佣金率
	 */
	private BigDecimal fycrateCom;
	/**
	 * @Fields discountPrem : 考核折标保费
	 */
	private BigDecimal discountPrem;
	/**
	 * @Fields discountPremRate : 考核折标比例
	 */
	private BigDecimal discountPremRate;

	/**
	 * @Fields policyCodeList : 批处理指定分公司
	 */
	private List<String> branchCodeList;

	public void setCommisionsn(String commisionsn) {
		this.commisionsn = commisionsn;
	}

	public String getCommisionsn() {
		return commisionsn;
	}

	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public String getUnitNumber() {
		return unitNumber;
	}

	public void setAppntno(String appntno) {
		this.appntno = appntno;
	}

	public String getAppntno() {
		return appntno;
	}

	public void setSalechnlcode(String salechnlcode) {
		this.salechnlcode = salechnlcode;
	}

	public String getSalechnlcode() {
		return salechnlcode;
	}

	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}

	public String getFeeType() {
		return feeType;
	}

	public void setConfdate(Date confdate) {
		this.confdate = confdate;
	}

	public Date getConfdate() {
		return confdate;
	}

	public void setDateid(BigDecimal dateid) {
		this.dateid = dateid;
	}

	public BigDecimal getDateid() {
		return dateid;
	}

	public void setProposalno(String proposalno) {
		this.proposalno = proposalno;
	}

	public String getProposalno() {
		return proposalno;
	}

	public void setAgentcode(String agentcode) {
		this.agentcode = agentcode;
	}

	public String getAgentcode() {
		return agentcode;
	}

	public void setServiceCodeFrom(String serviceCodeFrom) {
		this.serviceCodeFrom = serviceCodeFrom;
	}

	public String getServiceCodeFrom() {
		return serviceCodeFrom;
	}

	public void setBookkeepingFlag(BigDecimal bookkeepingFlag) {
		this.bookkeepingFlag = bookkeepingFlag;
	}

	public BigDecimal getBookkeepingFlag() {
		return bookkeepingFlag;
	}

	public void setCustgetpoldate(Date custgetpoldate) {
		this.custgetpoldate = custgetpoldate;
	}

	public Date getCustgetpoldate() {
		return custgetpoldate;
	}

	public void setAgentcomcode(String agentcomcode) {
		this.agentcomcode = agentcomcode;
	}

	public String getAgentcomcode() {
		return agentcomcode;
	}

	public void setStandfycrate(BigDecimal standfycrate) {
		this.standfycrate = standfycrate;
	}

	public BigDecimal getStandfycrate() {
		return standfycrate;
	}

	public void setPrtno(String prtno) {
		this.prtno = prtno;
	}

	public String getPrtno() {
		return prtno;
	}

	public void setRiskcode(String riskcode) {
		this.riskcode = riskcode;
	}

	public String getRiskcode() {
		return riskcode;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setPayyear(BigDecimal payyear) {
		this.payyear = payyear;
	}

	public BigDecimal getPayyear() {
		return payyear;
	}

	public void setPayplancode(String payplancode) {
		this.payplancode = payplancode;
	}

	public String getPayplancode() {
		return payplancode;
	}

	public void setYbtflag(String ybtflag) {
		this.ybtflag = ybtflag;
	}

	public String getYbtflag() {
		return ybtflag;
	}

	public void setStandprem(BigDecimal standprem) {
		this.standprem = standprem;
	}

	public BigDecimal getStandprem() {
		return standprem;
	}

	public void setSubinputtype(String subinputtype) {
		this.subinputtype = subinputtype;
	}

	public String getSubinputtype() {
		return subinputtype;
	}

	public void setDirectwage(BigDecimal directwage) {
		this.directwage = directwage;
	}

	public BigDecimal getDirectwage() {
		return directwage;
	}

	public void setMainpayterm(BigDecimal mainpayterm) {
		this.mainpayterm = mainpayterm;
	}

	public BigDecimal getMainpayterm() {
		return mainpayterm;
	}

	public void setFeeopertypecode(String feeopertypecode) {
		this.feeopertypecode = feeopertypecode;
	}

	public String getFeeopertypecode() {
		return feeopertypecode;
	}

	public void setPremArapListId(BigDecimal premArapListId) {
		this.premArapListId = premArapListId;
	}

	public BigDecimal getPremArapListId() {
		return premArapListId;
	}

	public void setUnitNumberFrom(String unitNumberFrom) {
		this.unitNumberFrom = unitNumberFrom;
	}

	public String getUnitNumberFrom() {
		return unitNumberFrom;
	}

	public void setLastpaytodate(Date lastpaytodate) {
		this.lastpaytodate = lastpaytodate;
	}

	public Date getLastpaytodate() {
		return lastpaytodate;
	}

	public void setScTab(String scTab) {
		this.scTab = scTab;
	}

	public String getScTab() {
		return scTab;
	}

	public void setSigndate(Date signdate) {
		this.signdate = signdate;
	}

	public Date getSigndate() {
		return signdate;
	}

	public void setManagecomcode(String managecomcode) {
		this.managecomcode = managecomcode;
	}

	public String getManagecomcode() {
		return managecomcode;
	}

	public void setContno(String contno) {
		this.contno = contno;
	}

	public String getContno() {
		return contno;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getServiceCode() {
		return serviceCode;
	}

	public void setFycrate(BigDecimal fycrate) {
		this.fycrate = fycrate;
	}

	public BigDecimal getFycrate() {
		return fycrate;
	}

	public void setMainriskflag(String mainriskflag) {
		this.mainriskflag = mainriskflag;
	}

	public String getMainriskflag() {
		return mainriskflag;
	}

	public void setWithdrawType(String withdrawType) {
		this.withdrawType = withdrawType;
	}

	public String getWithdrawType() {
		return withdrawType;
	}

	public void setPayintvcode(BigDecimal payintvcode) {
		this.payintvcode = payintvcode;
	}

	public BigDecimal getPayintvcode() {
		return payintvcode;
	}

	public void setGetpoldate(Date getpoldate) {
		this.getpoldate = getpoldate;
	}

	public Date getGetpoldate() {
		return getpoldate;
	}

	public void setPolno(String polno) {
		this.polno = polno;
	}

	public String getPolno() {
		return polno;
	}

	public BigDecimal getCoverageyear() {
		return coverageyear;
	}

	public void setCoverageyear(BigDecimal coverageyear) {
		this.coverageyear = coverageyear;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setInsuredno(String insuredno) {
		this.insuredno = insuredno;
	}

	public String getInsuredno() {
		return insuredno;
	}

	public void setPremium(BigDecimal premium) {
		this.premium = premium;
	}

	public BigDecimal getPremium() {
		return premium;
	}

	public void setStandpremrate(BigDecimal standpremrate) {
		this.standpremrate = standpremrate;
	}

	public BigDecimal getStandpremrate() {
		return standpremrate;
	}

	public void setPremArapListIdFrom(BigDecimal premArapListIdFrom) {
		this.premArapListIdFrom = premArapListIdFrom;
	}

	public BigDecimal getPremArapListIdFrom() {
		return premArapListIdFrom;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setDutycode(String dutycode) {
		this.dutycode = dutycode;
	}

	public String getDutycode() {
		return dutycode;
	}

	public void setValueprem(BigDecimal valueprem) {
		this.valueprem = valueprem;
	}

	public BigDecimal getValueprem() {
		return valueprem;
	}

	public void setRbflag(String rbflag) {
		this.rbflag = rbflag;
	}

	public String getRbflag() {
		return rbflag;
	}

	public void setPaycount(BigDecimal paycount) {
		this.paycount = paycount;
	}

	public BigDecimal getPaycount() {
		return paycount;
	}

	public void setPolicyYear(BigDecimal policyYear) {
		this.policyYear = policyYear;
	}

	public BigDecimal getPolicyYear() {
		return policyYear;
	}

	public void setMainpayintv(BigDecimal mainpayintv) {
		this.mainpayintv = mainpayintv;
	}

	public BigDecimal getMainpayintv() {
		return mainpayintv;
	}

	public void setPaydate(Date paydate) {
		this.paydate = paydate;
	}

	public Date getPaydate() {
		return paydate;
	}

	public void setCvalidate(Date cvalidate) {
		this.cvalidate = cvalidate;
	}

	public Date getCvalidate() {
		return cvalidate;
	}

	public void setEnteraccdate(Date enteraccdate) {
		this.enteraccdate = enteraccdate;
	}

	public Date getEnteraccdate() {
		return enteraccdate;
	}

	public void setFyc(BigDecimal fyc) {
		this.fyc = fyc;
	}

	public BigDecimal getFyc() {
		return fyc;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public BigDecimal getHesitateFlag() {
		return hesitateFlag;
	}

	public void setHesitateFlag(BigDecimal hesitateFlag) {
		this.hesitateFlag = hesitateFlag;
	}

	public BigDecimal getFormerId() {
		return formerId;
	}

	public void setFormerId(BigDecimal formerId) {
		this.formerId = formerId;
	}

	public String getSubsalechnl() {
		return subsalechnl;
	}

	public void setSubsalechnl(String subsalechnl) {
		this.subsalechnl = subsalechnl;
	}

	public List<String> getPolicyCodeList() {
		return policyCodeList;
	}

	public void setPolicyCodeList(List<String> policyCodeList) {
		this.policyCodeList = policyCodeList;
	}

	public String getBatchdate() {
		return batchdate;
	}

	public void setBatchdate(String batchdate) {
		this.batchdate = batchdate;
	}

	public void setPaytermcode(BigDecimal paytermcode) {
		this.paytermcode = paytermcode;
	}

	public BigDecimal getPaytermcode() {
		return paytermcode;
	}

	public LaratecommisionBO getLaratecommisionBO() {
		return laratecommisionBO;
	}

	public void setLaratecommisionBO(LaratecommisionBO laratecommisionBO) {
		this.laratecommisionBO = laratecommisionBO;
	}

	public BigDecimal getPerFlag() {
		return perFlag;
	}

	public void setPerFlag(BigDecimal perFlag) {
		this.perFlag = perFlag;
	}

	public Date getTmakedate() {
		return tmakedate;
	}

	public void setTmakedate(Date tmakedate) {
		this.tmakedate = tmakedate;
	}

	public BigDecimal getCalCommision() {
		return calCommision;
	}

	public void setCalCommision(BigDecimal calCommision) {
		this.calCommision = calCommision;
	}

	public String getNewAgentcode() {
		return newAgentcode;
	}

	public void setNewAgentcode(String newAgentcode) {
		this.newAgentcode = newAgentcode;
	}

	public BigDecimal getAgentrate() {
		return agentrate;
	}

	public void setAgentrate(BigDecimal agentrate) {
		this.agentrate = agentrate;
	}

	public String getChanneltype() {
		return channeltype;
	}

	public void setChanneltype(String channeltype) {
		this.channeltype = channeltype;
	}

	public BigDecimal getCalcount() {
		return calcount;
	}

	public void setCalcount(BigDecimal calcount) {
		this.calcount = calcount;
	}

	public Date getInserttime() {
		return inserttime;
	}

	public void setInserttime(Date inserttime) {
		this.inserttime = inserttime;
	}

	public String getRefeflag() {
		return refeflag;
	}

	public void setRefeflag(String refeflag) {
		this.refeflag = refeflag;
	}

	public String getPolicyFlag() {
		return policyFlag;
	}

	public void setPolicyFlag(String policyFlag) {
		this.policyFlag = policyFlag;
	}

	public Date getChangedate() {
		return changedate;
	}

	public void setChangedate(Date changedate) {
		this.changedate = changedate;
	}

	public String getBranchCode() {
		return branchCode;
	}

	public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}

	public String getSaleagentcode() {
		return saleagentcode;
	}

	public void setSaleagentcode(String saleagentcode) {
		this.saleagentcode = saleagentcode;
	}

	public BigDecimal getAgentStatus() {
		return agentStatus;
	}

	public void setAgentStatus(BigDecimal agentStatus) {
		this.agentStatus = agentStatus;
	}

	public String getAgentGrade() {
		return agentGrade;
	}

	public void setAgentGrade(String agentGrade) {
		this.agentGrade = agentGrade;
	}

	public BigDecimal getExtraFycrate() {
		return extraFycrate;
	}

	public void setExtraFycrate(BigDecimal extraFycrate) {
		this.extraFycrate = extraFycrate;
	}

	public BigDecimal getExtraFyc() {
		return extraFyc;
	}

	public void setExtraFyc(BigDecimal extraFyc) {
		this.extraFyc = extraFyc;
	}

	public String getDerivType() {
		return derivType;
	}

	public void setDerivType(String derivType) {
		this.derivType = derivType;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public String getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(String applyTime) {
		this.applyTime = applyTime;
	}

	public BigDecimal getCoverperiodtype() {
		return coverperiodtype;
	}

	public void setCoverperiodtype(BigDecimal coverperiodtype) {
		this.coverperiodtype = coverperiodtype;
	}

	public List<String> getBranchCodeList() {
		return branchCodeList;
	}

	public void setBranchCodeList(List<String> branchCodeList) {
		this.branchCodeList = branchCodeList;
	}

	public Date getEmakedate() {
		return emakedate;
	}

	public void setEmakedate(Date emakedate) {
		this.emakedate = emakedate;
	}

	public Date getCustomercaldate() {
		return customercaldate;
	}

	public void setCustomercaldate(Date customercaldate) {
		this.customercaldate = customercaldate;
	}

	public Date getCaldate() {
		return caldate;
	}

	public void setCaldate(Date caldate) {
		this.caldate = caldate;
	}

	public String getWageno() {
		return wageno;
	}

	public void setWageno(String wageno) {
		this.wageno = wageno;
	}

	public String getRenewflag() {
		return renewflag;
	}

	public void setRenewflag(String renewflag) {
		this.renewflag = renewflag;
	}

	public Date getMaturitydate() {
		return maturitydate;
	}

	public void setMaturitydate(Date maturitydate) {
		this.maturitydate = maturitydate;
	}

	public String getChargePeriod() {
		return chargePeriod;
	}

	public void setChargePeriod(String chargePeriod) {
		this.chargePeriod = chargePeriod;
	}

	public Date getInitialValidateDate() {
		return initialValidateDate;
	}

	public void setInitialValidateDate(Date initialValidateDate) {
		this.initialValidateDate = initialValidateDate;
	}

	public Date getBusiApplyDate() {
		return busiApplyDate;
	}

	public void setBusiApplyDate(Date busiApplyDate) {
		this.busiApplyDate = busiApplyDate;
	}

	public String getCommisionFlag() {
		return commisionFlag;
	}

	public void setCommisionFlag(String commisionFlag) {
		this.commisionFlag = commisionFlag;
	}

	public String getPolicyReinsureFlag() {
		return policyReinsureFlag;
	}

	public void setPolicyReinsureFlag(String policyReinsureFlag) {
		this.policyReinsureFlag = policyReinsureFlag;
	}

	public Date getProcessTime() {
		return processTime;
	}

	public void setProcessTime(Date processTime) {
		this.processTime = processTime;
	}

	public String getOldcontno() {
		return oldcontno;
	}

	public void setOldcontno(String oldcontno) {
		this.oldcontno = oldcontno;
	}

	public BigDecimal getBanknrtFalg() {
		return banknrtFalg;
	}

	public void setBanknrtFalg(BigDecimal banknrtFalg) {
		this.banknrtFalg = banknrtFalg;
	}

	public BigDecimal getIssueFee() {
		return issueFee;
	}

	public void setIssueFee(BigDecimal issueFee) {
		this.issueFee = issueFee;
	}

	public BigDecimal getDirectFee() {
		return directFee;
	}

	public void setDirectFee(BigDecimal directFee) {
		this.directFee = directFee;
	}

	public String getRenewOption() {
		return renewOption;
	}

	public void setRenewOption(String renewOption) {
		this.renewOption = renewOption;
	}

	public BigDecimal getDirectFeeRate() {
		return directFeeRate;
	}

	public void setDirectFeeRate(BigDecimal directFeeRate) {
		this.directFeeRate = directFeeRate;
	}

	public BigDecimal getExtraPremium() {
		return extraPremium;
	}

	public void setExtraPremium(BigDecimal extraPremium) {
		this.extraPremium = extraPremium;
	}

	public String getCooperationProtocolId() {
		return cooperationProtocolId;
	}

	public void setCooperationProtocolId(String cooperationProtocolId) {
		this.cooperationProtocolId = cooperationProtocolId;
	}

	public String getOldriskcode() {
		return oldriskcode;
	}

	public void setOldriskcode(String oldriskcode) {
		this.oldriskcode = oldriskcode;
	}

	public BigDecimal getFycCom() {
		return fycCom;
	}

	public void setFycCom(BigDecimal fycCom) {
		this.fycCom = fycCom;
	}

	public BigDecimal getFycrateCom() {
		return fycrateCom;
	}

	public void setFycrateCom(BigDecimal fycrateCom) {
		this.fycrateCom = fycrateCom;
	}

	public BigDecimal getDiscountPrem() {
		return discountPrem;
	}

	public void setDiscountPrem(BigDecimal discountPrem) {
		this.discountPrem = discountPrem;
	}

	public BigDecimal getDiscountPremRate() {
		return discountPremRate;
	}

	public void setDiscountPremRate(BigDecimal discountPremRate) {
		this.discountPremRate = discountPremRate;
	}

	@Override
	public String getBizId() {
		return null;
	}
}
