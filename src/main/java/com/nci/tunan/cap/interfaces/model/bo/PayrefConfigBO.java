package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 公用信息配置BO对象
 * <AUTHOR>
 * @date 2015-05-21 18:06:25
 * @.belongToModule 收付费-公用信息配置
 */
public class PayrefConfigBO extends BaseBO {

    /**
     * 序列号
     */

    private static final long serialVersionUID = -6193404156892254805L;
    /**
     * 代码名称
     */
    private String configName;
    /**
     *  类型
     */
    private String configType;
    /**
     *  代码描述
     */
    private String configDesc;

    /**
     *  值
     */
    private String configTypeNum;
    /**
     *  流水号
     */
    private BigDecimal configId;
    /**
     *  代码
     */
    private String configCode;

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigDesc(String configDesc) {
        this.configDesc = configDesc;
    }

    public String getConfigDesc() {
        return configDesc;
    }

    public void setConfigId(BigDecimal configId) {
        this.configId = configId;
    }

    public BigDecimal getConfigId() {
        return configId;
    }

    public void setConfigTypeNum(String configTypeNum) {
        this.configTypeNum = configTypeNum;
    }

    public String getConfigTypeNum() {
        return configTypeNum;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getConfigCode() {
        return configCode;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "PayrefConfigBO [" + "configName=" + configName + ", configType=" + configType + ", configDesc="
                + configDesc + ", configId=" + configId + ", configTypeNum=" + configTypeNum + ", configCode="
                + configCode + "]";
    }
}
