package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description LacommsionAFycVO对象
 * <AUTHOR>
 * @date 2020-11-06 10:47:03
 */
public class LacommsionAFycBO extends BaseBO {
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields standfycrate : null
	 */
	private BigDecimal standfycrate;
	/**
	 * @Fields fycB : null
	 */
	private BigDecimal fycB;
	/**
	 * @Fields commisionsn : null
	 */
	private String commisionsn;
	/**
	 * @Fields directwageB : null
	 */
	private BigDecimal directwageB;
	/**
	 * @Fields standfycrateB : null
	 */
	private BigDecimal standfycrateB;
	/**
	 * @Fields fycrateB : null
	 */
	private BigDecimal fycrateB;
	/**
	 * @Fields fyc : null
	 */
	private BigDecimal fyc;
	/**
	 * @Fields fycrate : null
	 */
	private BigDecimal fycrate;
	/**
	 * @Fields directwage : null
	 */
	private BigDecimal directwage;
	/**
	 * @Fields extraFycrate : 额外佣金率
	 */
	private BigDecimal extraFycrate;	
	/**
	 * @Fields extraRate : 额外佣金
	 */
	private BigDecimal extraFyc;
	/**
	 * @Fields extraFycrate : 额外佣金率
	 */
	private BigDecimal extraFycrateB;	
	/**
	 * @Fields extraRate : 额外佣金
	 */
	private BigDecimal extraFycB;

	public void setStandfycrate(BigDecimal standfycrate) {
		this.standfycrate = standfycrate;
	}

	public BigDecimal getStandfycrate() {
		return standfycrate;
	}

	public void setFycB(BigDecimal fycB) {
		this.fycB = fycB;
	}

	public BigDecimal getFycB() {
		return fycB;
	}

	public void setCommisionsn(String commisionsn) {
		this.commisionsn = commisionsn;
	}

	public String getCommisionsn() {
		return commisionsn;
	}

	public void setDirectwageB(BigDecimal directwageB) {
		this.directwageB = directwageB;
	}

	public BigDecimal getDirectwageB() {
		return directwageB;
	}

	public void setStandfycrateB(BigDecimal standfycrateB) {
		this.standfycrateB = standfycrateB;
	}

	public BigDecimal getStandfycrateB() {
		return standfycrateB;
	}

	public void setFycrateB(BigDecimal fycrateB) {
		this.fycrateB = fycrateB;
	}

	public BigDecimal getFycrateB() {
		return fycrateB;
	}

	public void setFyc(BigDecimal fyc) {
		this.fyc = fyc;
	}

	public BigDecimal getFyc() {
		return fyc;
	}

	public void setFycrate(BigDecimal fycrate) {
		this.fycrate = fycrate;
	}

	public BigDecimal getFycrate() {
		return fycrate;
	}

	public void setDirectwage(BigDecimal directwage) {
		this.directwage = directwage;
	}

	public BigDecimal getDirectwage() {
		return directwage;
	}
	
	public BigDecimal getExtraFycrate() {
		return extraFycrate;
	}
	
	public void setExtraFycrate(BigDecimal extraFycrate) {
		this.extraFycrate = extraFycrate;
	}

	public BigDecimal getExtraFyc() {
		return extraFyc;
	}

	public void setExtraFyc(BigDecimal extraFyc) {
		this.extraFyc = extraFyc;
	}

	public BigDecimal getExtraFycrateB() {
		return extraFycrateB;
	}

	public void setExtraFycrateB(BigDecimal extraFycrateB) {
		this.extraFycrateB = extraFycrateB;
	}

	public BigDecimal getExtraFycB() {
		return extraFycB;
	}

	public void setExtraFycB(BigDecimal extraFycB) {
		this.extraFycB = extraFycB;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "LacommsionAFycVO [" + "standfycrate=" + standfycrate + ","
				+ "fycB=" + fycB + "," + "commisionsn=" + commisionsn + ","
				+ "directwageB=" + directwageB + "," + "standfycrateB="
				+ standfycrateB + "," + "fycrateB=" + fycrateB + "," + "fyc="
				+ fyc + "," + "fycrate=" + fycrate + "," + "directwage="
				+ directwage + "extraFycrate=" + extraFycrate + "extraFyc=" + extraFyc 
				+ "extraFycrateB=" + extraFycrateB + "extraFycB=" + extraFycB + "]";
	}
}
