package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description sap记账BO对象
 * <AUTHOR>
 * @date 2015-05-21 18:06:25
 * @.belongToModule 收付费-sap记账
 */
public class GlInterfaceBO extends BaseBO {

    /**
     * 序列号
     */

    private static final long serialVersionUID = 7462767917230486985L;

    /**
     * 备用字符串十三
     */
    private String standbystring13;
    /**
     * 备用字符串十四
     */
    private String standbystring14;
    /**
     *  备用字符串十六
     */
    private String standbystring16;
    /**
     *  备用字符串十五
     */
    private String standbystring15;
    /**
     * 备用字符串十八
     */
    private String standbystring18;
    /**
     * 备用字符串十七
     */
    private String standbystring17;
    /**
     *  备用字符串十九
     */
    private String standbystring19;
    /**
     *  批次号
     */
    private String batchNo;
    /**
     *  记账日期
     */
    private Date accdate;
    /**
     *  备用数字三
     */
    private BigDecimal standbynum3;
    /**
     * 备用数字四
     */
    private BigDecimal standbynum4;
    /**
     *  科目代码
     */
    private String accuitem;
    /**
     *  备用数字一
     */
    private BigDecimal standbynum1;
    /**
     * 提数模块
     */
    private String opermodel;
    /**
     * 备用数字二
     */
    private BigDecimal standbynum2;
    /**
     *  备用数字七
     */
    private BigDecimal standbynum7;
    /**
     *  备用数字八
     */
    private BigDecimal standbynum8;
    /**
     *  备用数字五
     */
    private BigDecimal standbynum5;
    /**
     * 币别
     */
    private String head;
    /**
     *  备用数字六
     */
    private BigDecimal standbynum6;
    /**
     *  客户编号
     */
    private String customerid;
    /**
     *  参照文本
     */
    private String billrefe;
    /**
     * 凭证头
     */
    private String riskType1;
    /**
     *  01-团险直销,02-个人营销,03-??行代理
     */
    private String salechnll;
    /**
     * 首续期
     */
    private String payprd;
    /**
     *备用字符串二十
     */
    private String standbystring20;
    /**
     *凭证类型
     */
    private String billtype;
    /**
     *  险种性质一
     */
    private String riskType2;
    /**
     *  管理机构
     */
    private String managecom;
    /**
     *  保单号
     */
    private String bankcode;
    /**
     * 险种编码
     */
    private String riskcode;
    /**
     *  备用数字九
     */
    private BigDecimal standbynum9;
    /**
     * 标准保费
     */
    private BigDecimal standprem;
    /**
     * 单证保证金收据号
     */
    private String getnoticeno;
    /**
     * 摘要
     */
    private String billadst;
    /**
     * 回退业务标志
     */
    private String rollbackflag;
    /**
     *  支票号
     */
    private String checkno;
    /**
     * 签报号
     */
    private String grpname;
    /**
     *  险种性质二
     */
    private String paykind;
    /**
     *  业务交易行数
     */
    private String busyNum;
    /**
     *  分录数据流水号
     */
    private String fserialNo;
    /**
     *  业务交易编号
     */
    private String busytransNo;
    /**
     *  提取机构
     */
    private String sapcom;
    /**
     *  银行
     */
    private String agentcom;
    /**
     *  SAP回写标志
     */
    private String returnflag;
    /**
     * 业务发生日期
     */
    private Date billdate;
    /**
     *  成本中心
     */
    private String kostcode;
    /**
     * D 借方 C 贷方
     */
    private String payway;
    /**
     * 公司代码
     */
    private String comcode;
    /**
     *  业务数据流水号
     */
    private String aserialNo;
    /**
     * 备用数字十
     */
    private BigDecimal standbynum10;
    /**
     * 代理机构
     */
    private String amneno;
    /**
     *  缴费期限
     */
    private String contno;
    /**
     *  费用金额
     */
    private BigDecimal accsum;
    /**
     * 预算信息
     */
    private String budgetitem;
    /**
     *  对方机构
     */
    private String executecom;
    /**
     *  SAP机构代码
     */
    private String sapcomcode;
    /**
     * 现金流量
     */
    private String accuse;
    /**
     *  备用字符串十二
     */
    private String standbystring12;
    /**
     *  备用字符串十一
     */
    private String standbystring11;
    /**
     *  业务编号
     */
    private String busyNo;
    /**
     * 提取日期
     */
    private Date sapdate;
    /**
     *备用字符串十
     */
    private String standbystring10;
    /**
     *  支付方式
     */
    private String paymode;
    /**
     * @Fields standbystring1 : 备用字符串一
     */
    private String standbystring1;
    /**
     *  默认为 CNY
     */
    private String currency;
    /**
     * 备用字符串二
     */
    private String standbystring2;
    /**
     *  备用字符串三
     */
    private String standbystring3;
    /**
     *  备用字符串四
     */
    private String standbystring4;
    /**
     *  备用字符串五
     */
    private String standbystring5;
    /**
     *  备用字符串六
     */
    private String standbystring6;
    /**
     *  备用字符串七
     */
    private String standbystring7;
    /**
     *  备用字符串八
     */
    private String standbystring8;
    /**
     *  备用字符串九
     */
    private String standbystring9;
    
    /**
     * 流水号，无业务含义
     */
    private BigDecimal listId;

    public void setStandbystring14(String standbystring14) {
        this.standbystring14 = standbystring14;
    }

    public String getStandbystring14() {
        return standbystring14;
    }

    public void setStandbystring13(String standbystring13) {
        this.standbystring13 = standbystring13;
    }

    public String getStandbystring13() {
        return standbystring13;
    }

    public void setStandbystring16(String standbystring16) {
        this.standbystring16 = standbystring16;
    }

    public String getStandbystring16() {
        return standbystring16;
    }

    public void setStandbystring15(String standbystring15) {
        this.standbystring15 = standbystring15;
    }

    public String getStandbystring15() {
        return standbystring15;
    }

    public void setStandbystring18(String standbystring18) {
        this.standbystring18 = standbystring18;
    }

    public String getStandbystring18() {
        return standbystring18;
    }

    public void setStandbystring17(String standbystring17) {
        this.standbystring17 = standbystring17;
    }

    public String getStandbystring17() {
        return standbystring17;
    }

    public void setStandbystring19(String standbystring19) {
        this.standbystring19 = standbystring19;
    }

    public String getStandbystring19() {
        return standbystring19;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setAccdate(Date accdate) {
        this.accdate = accdate;
    }

    public Date getAccdate() {
        return accdate;
    }

    public void setStandbynum3(BigDecimal standbynum3) {
        this.standbynum3 = standbynum3;
    }

    public BigDecimal getStandbynum3() {
        return standbynum3;
    }

    public void setStandbynum4(BigDecimal standbynum4) {
        this.standbynum4 = standbynum4;
    }

    public BigDecimal getStandbynum4() {
        return standbynum4;
    }

    public void setAccuitem(String accuitem) {
        this.accuitem = accuitem;
    }

    public String getAccuitem() {
        return accuitem;
    }

    public void setStandbynum1(BigDecimal standbynum1) {
        this.standbynum1 = standbynum1;
    }

    public BigDecimal getStandbynum1() {
        return standbynum1;
    }

    public void setOpermodel(String opermodel) {
        this.opermodel = opermodel;
    }

    public String getOpermodel() {
        return opermodel;
    }

    public void setStandbynum2(BigDecimal standbynum2) {
        this.standbynum2 = standbynum2;
    }

    public BigDecimal getStandbynum2() {
        return standbynum2;
    }

    public void setStandbynum7(BigDecimal standbynum7) {
        this.standbynum7 = standbynum7;
    }

    public BigDecimal getStandbynum7() {
        return standbynum7;
    }

    public void setStandbynum8(BigDecimal standbynum8) {
        this.standbynum8 = standbynum8;
    }

    public BigDecimal getStandbynum8() {
        return standbynum8;
    }

    public void setStandbynum5(BigDecimal standbynum5) {
        this.standbynum5 = standbynum5;
    }

    public BigDecimal getStandbynum5() {
        return standbynum5;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getHead() {
        return head;
    }

    public void setStandbynum6(BigDecimal standbynum6) {
        this.standbynum6 = standbynum6;
    }

    public BigDecimal getStandbynum6() {
        return standbynum6;
    }

    public void setCustomerid(String customerid) {
        this.customerid = customerid;
    }

    public String getCustomerid() {
        return customerid;
    }

    public void setBillrefe(String billrefe) {
        this.billrefe = billrefe;
    }

    public String getBillrefe() {
        return billrefe;
    }

    public void setRiskType1(String riskType1) {
        this.riskType1 = riskType1;
    }

    public String getRiskType1() {
        return riskType1;
    }

    public void setSalechnll(String salechnll) {
        this.salechnll = salechnll;
    }

    public String getSalechnll() {
        return salechnll;
    }

    public void setPayprd(String payprd) {
        this.payprd = payprd;
    }

    public String getPayprd() {
        return payprd;
    }

    public void setStandbystring20(String standbystring20) {
        this.standbystring20 = standbystring20;
    }

    public String getStandbystring20() {
        return standbystring20;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    public String getBilltype() {
        return billtype;
    }

    public void setRiskType2(String riskType2) {
        this.riskType2 = riskType2;
    }

    public String getRiskType2() {
        return riskType2;
    }

    public void setManagecom(String managecom) {
        this.managecom = managecom;
    }

    public String getManagecom() {
        return managecom;
    }

    public void setBankcode(String bankcode) {
        this.bankcode = bankcode;
    }

    public String getBankcode() {
        return bankcode;
    }

    public void setRiskcode(String riskcode) {
        this.riskcode = riskcode;
    }

    public String getRiskcode() {
        return riskcode;
    }

    public void setStandbynum9(BigDecimal standbynum9) {
        this.standbynum9 = standbynum9;
    }

    public BigDecimal getStandbynum9() {
        return standbynum9;
    }

    public void setStandprem(BigDecimal standprem) {
        this.standprem = standprem;
    }

    public BigDecimal getStandprem() {
        return standprem;
    }

    public void setGetnoticeno(String getnoticeno) {
        this.getnoticeno = getnoticeno;
    }

    public String getGetnoticeno() {
        return getnoticeno;
    }

    public void setBilladst(String billadst) {
        this.billadst = billadst;
    }

    public String getBilladst() {
        return billadst;
    }

    public void setRollbackflag(String rollbackflag) {
        this.rollbackflag = rollbackflag;
    }

    public String getRollbackflag() {
        return rollbackflag;
    }

    public void setCheckno(String checkno) {
        this.checkno = checkno;
    }

    public String getCheckno() {
        return checkno;
    }

    public void setGrpname(String grpname) {
        this.grpname = grpname;
    }

    public String getGrpname() {
        return grpname;
    }

    public void setPaykind(String paykind) {
        this.paykind = paykind;
    }

    public String getPaykind() {
        return paykind;
    }

    public void setBusyNum(String busyNum) {
        this.busyNum = busyNum;
    }

    public String getBusyNum() {
        return busyNum;
    }

    public void setFserialNo(String fserialNo) {
        this.fserialNo = fserialNo;
    }

    public String getFserialNo() {
        return fserialNo;
    }

    public void setBusytransNo(String busytransNo) {
        this.busytransNo = busytransNo;
    }

    public String getBusytransNo() {
        return busytransNo;
    }

    public void setSapcom(String sapcom) {
        this.sapcom = sapcom;
    }

    public String getSapcom() {
        return sapcom;
    }

    public void setAgentcom(String agentcom) {
        this.agentcom = agentcom;
    }

    public String getAgentcom() {
        return agentcom;
    }

    public void setReturnflag(String returnflag) {
        this.returnflag = returnflag;
    }

    public String getReturnflag() {
        return returnflag;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    public Date getBilldate() {
        return billdate;
    }

    public void setKostcode(String kostcode) {
        this.kostcode = kostcode;
    }

    public String getKostcode() {
        return kostcode;
    }

    public void setPayway(String payway) {
        this.payway = payway;
    }

    public String getPayway() {
        return payway;
    }

    public void setComcode(String comcode) {
        this.comcode = comcode;
    }

    public String getComcode() {
        return comcode;
    }

    public void setAserialNo(String aserialNo) {
        this.aserialNo = aserialNo;
    }

    public String getAserialNo() {
        return aserialNo;
    }

    public void setStandbynum10(BigDecimal standbynum10) {
        this.standbynum10 = standbynum10;
    }

    public BigDecimal getStandbynum10() {
        return standbynum10;
    }

    public void setAmneno(String amneno) {
        this.amneno = amneno;
    }

    public String getAmneno() {
        return amneno;
    }

    public void setContno(String contno) {
        this.contno = contno;
    }

    public String getContno() {
        return contno;
    }

    public void setAccsum(BigDecimal accsum) {
        this.accsum = accsum;
    }

    public BigDecimal getAccsum() {
        return accsum;
    }

    public void setBudgetitem(String budgetitem) {
        this.budgetitem = budgetitem;
    }

    public String getBudgetitem() {
        return budgetitem;
    }

    public void setExecutecom(String executecom) {
        this.executecom = executecom;
    }

    public String getExecutecom() {
        return executecom;
    }

    public void setSapcomcode(String sapcomcode) {
        this.sapcomcode = sapcomcode;
    }

    public String getSapcomcode() {
        return sapcomcode;
    }

    public void setAccuse(String accuse) {
        this.accuse = accuse;
    }

    public String getAccuse() {
        return accuse;
    }

    public void setStandbystring12(String standbystring12) {
        this.standbystring12 = standbystring12;
    }

    public String getStandbystring12() {
        return standbystring12;
    }

    public void setStandbystring11(String standbystring11) {
        this.standbystring11 = standbystring11;
    }

    public String getStandbystring11() {
        return standbystring11;
    }

    public void setBusyNo(String busyNo) {
        this.busyNo = busyNo;
    }

    public String getBusyNo() {
        return busyNo;
    }

    public void setSapdate(Date sapdate) {
        this.sapdate = sapdate;
    }

    public Date getSapdate() {
        return sapdate;
    }

    public void setStandbystring10(String standbystring10) {
        this.standbystring10 = standbystring10;
    }

    public String getStandbystring10() {
        return standbystring10;
    }

    public void setPaymode(String paymode) {
        this.paymode = paymode;
    }

    public String getPaymode() {
        return paymode;
    }

    public void setStandbystring1(String standbystring1) {
        this.standbystring1 = standbystring1;
    }

    public String getStandbystring1() {
        return standbystring1;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }

    public void setStandbystring2(String standbystring2) {
        this.standbystring2 = standbystring2;
    }

    public String getStandbystring2() {
        return standbystring2;
    }

    public void setStandbystring3(String standbystring3) {
        this.standbystring3 = standbystring3;
    }

    public String getStandbystring3() {
        return standbystring3;
    }

    public void setStandbystring4(String standbystring4) {
        this.standbystring4 = standbystring4;
    }

    public String getStandbystring4() {
        return standbystring4;
    }

    public void setStandbystring5(String standbystring5) {
        this.standbystring5 = standbystring5;
    }

    public String getStandbystring5() {
        return standbystring5;
    }

    public void setStandbystring6(String standbystring6) {
        this.standbystring6 = standbystring6;
    }

    public String getStandbystring6() {
        return standbystring6;
    }

    public void setStandbystring7(String standbystring7) {
        this.standbystring7 = standbystring7;
    }

    public String getStandbystring7() {
        return standbystring7;
    }

    public void setStandbystring8(String standbystring8) {
        this.standbystring8 = standbystring8;
    }

    public String getStandbystring8() {
        return standbystring8;
    }

    public void setStandbystring9(String standbystring9) {
        this.standbystring9 = standbystring9;
    }

    public String getStandbystring9() {
        return standbystring9;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "GlInterfaceBO [standbystring14=" + standbystring14 + ", standbystring13=" + standbystring13
                + ", standbystring16=" + standbystring16 + ", standbystring15=" + standbystring15
                + ", standbystring18=" + standbystring18 + ", standbystring17=" + standbystring17
                + ", standbystring19=" + standbystring19 + ", batchNo=" + batchNo + ", accdate=" + accdate
                + ", standbynum3=" + standbynum3 + ", standbynum4=" + standbynum4 + ", accuitem=" + accuitem
                + ", standbynum1=" + standbynum1 + ", opermodel=" + opermodel + ", standbynum2=" + standbynum2
                + ", standbynum7=" + standbynum7 + ", standbynum8=" + standbynum8 + ", standbynum5=" + standbynum5
                + ", head=" + head + ", standbynum6=" + standbynum6 + ", customerid=" + customerid + ", billrefe="
                + billrefe + ", riskType1=" + riskType1 + ", salechnll=" + salechnll + ", payprd=" + payprd
                + ", standbystring20=" + standbystring20 + ", billtype=" + billtype + ", riskType2=" + riskType2
                + ", managecom=" + managecom + ", bankcode=" + bankcode + ", riskcode=" + riskcode + ", standbynum9="
                + standbynum9 + ", standprem=" + standprem + ", getnoticeno=" + getnoticeno + ", billadst=" + billadst
                + ", rollbackflag=" + rollbackflag + ", checkno=" + checkno + ", grpname=" + grpname + ", paykind="
                + paykind + ", busyNum=" + busyNum + ", fserialNo=" + fserialNo + ", busytransNo=" + busytransNo
                + ", sapcom=" + sapcom + ", agentcom=" + agentcom + ", returnflag=" + returnflag + ", billdate="
                + billdate + ", kostcode=" + kostcode + ", payway=" + payway + ", comcode=" + comcode + ", aserialNo="
                + aserialNo + ", standbynum10=" + standbynum10 + ", amneno=" + amneno + ", contno=" + contno
                + ", accsum=" + accsum + ", budgetitem=" + budgetitem + ", executecom=" + executecom + ", sapcomcode="
                + sapcomcode + ", accuse=" + accuse + ", standbystring12=" + standbystring12 + ", standbystring11="
                + standbystring11 + ", busyNo=" + busyNo + ", sapdate=" + sapdate + ", standbystring10="
                + standbystring10 + ", paymode=" + paymode + ", standbystring1=" + standbystring1 + ", currency="
                + currency + ", standbystring2=" + standbystring2 + ", standbystring3=" + standbystring3
                + ", standbystring4=" + standbystring4 + ", standbystring5=" + standbystring5 + ", standbystring6="
                + standbystring6 + ", standbystring7=" + standbystring7 + ", standbystring8=" + standbystring8
                + ", standbystring9=" + standbystring9 + "]";
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

}
