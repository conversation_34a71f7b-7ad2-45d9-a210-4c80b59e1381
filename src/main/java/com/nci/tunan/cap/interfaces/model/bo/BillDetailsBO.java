package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 柜台收费
 * @<NAME_EMAIL>
 * @date 2015-12-10 下午6:48:25
 * @.belongToModule 收付费-柜台收费
 */
public class BillDetailsBO extends BaseBO {
    /**
     * @Fields serialVersionUID : SUID
     */
    private static final long serialVersionUID = 1L;
    /**
     * @Fields arapDate : 收付费日期
     */
    private Date arapDate;
    /**
     * @Fields arapBelnr : 付费凭证号/暂收据号
     */
    private String arapBelnr;
    /**
     * @Fields arapBankCode : 收/付款方银行
     */
    private String arapBankCode;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields billMode : 对应票据类型
     */
    private String billMode;
    /**
     * @Fields capOrganCode : 收付费机构
     */
    private String capOrganCode;
    /**
     * @Fields billMainId : 对应具体票据表里主键
     */
    private BigDecimal billMainId;
    /**
     * @Fields cusAccDetailsId : 客户账户交易明细ID
     */
    private BigDecimal cusAccDetailsId;
    /**
     * @Fields handlerCode : 经办人证件号码
     */
    private String handlerCode;
    /**
     * @Fields tempFeeAmount : 暂收费金额
     */
    private BigDecimal tempFeeAmount;
    /**
     * @Fields handlerName : 经办人姓名
     */
    private String handlerName;
    /**
     * @Fields arapBankAccount : 收/付款方银行账号
     */
    private String arapBankAccount;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields handlerType : 经办人证件类型
     */
    private String handlerType;
    /**
     * @Fields billId : 票据流水号
     */
    private BigDecimal billId;
    /**
     * @Fields payrefno : 实付号
     */
    private String payrefno;
    /**
     * @Fields arapBankUserName : 户名 
     */
    private String arapBankUserName;
    
    private Date insertTime;
    
    

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public String getArapBankUserName() {
		return arapBankUserName;
	}

	public void setArapBankUserName(String arapBankUserName) {
		this.arapBankUserName = arapBankUserName;
	}

	public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    public Date getArapDate() {
        return arapDate;
    }

    public void setArapBelnr(String arapBelnr) {
        this.arapBelnr = arapBelnr;
    }

    public String getArapBelnr() {
        return arapBelnr;
    }

    public void setArapBankCode(String arapBankCode) {
        this.arapBankCode = arapBankCode;
    }

    public String getArapBankCode() {
        return arapBankCode;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setBillMode(String billMode) {
        this.billMode = billMode;
    }

    public String getBillMode() {
        return billMode;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setBillMainId(BigDecimal billMainId) {
        this.billMainId = billMainId;
    }

    public BigDecimal getBillMainId() {
        return billMainId;
    }

    public void setCusAccDetailsId(BigDecimal cusAccDetailsId) {
        this.cusAccDetailsId = cusAccDetailsId;
    }

    public BigDecimal getCusAccDetailsId() {
        return cusAccDetailsId;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    public String getHandlerCode() {
        return handlerCode;
    }

    public void setTempFeeAmount(BigDecimal tempFeeAmount) {
        this.tempFeeAmount = tempFeeAmount;
    }

    public BigDecimal getTempFeeAmount() {
        return tempFeeAmount;
    }

    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }

    public String getHandlerName() {
        return handlerName;
    }

    public void setArapBankAccount(String arapBankAccount) {
        this.arapBankAccount = arapBankAccount;
    }

    public String getArapBankAccount() {
        return arapBankAccount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setHandlerType(String handlerType) {
        this.handlerType = handlerType;
    }

    public String getHandlerType() {
        return handlerType;
    }

    public void setBillId(BigDecimal billId) {
        this.billId = billId;
    }

    public BigDecimal getBillId() {
        return billId;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public String getPayrefno() {
        return payrefno;
    }

    public void setPayrefno(String payrefno) {
        this.payrefno = payrefno;
    }

    @Override
    public String toString() {
        return "BillDetailsBO [arapDate=" + arapDate + ", arapBelnr=" + arapBelnr + ", arapBankCode=" + arapBankCode
                + ", unitNumber=" + unitNumber + ", billMode=" + billMode + ", capOrganCode=" + capOrganCode
                + ", billMainId=" + billMainId + ", cusAccDetailsId=" + cusAccDetailsId + ", handlerCode="
                + handlerCode + ", tempFeeAmount=" + tempFeeAmount + ", handlerName=" + handlerName
                + ", arapBankAccount=" + arapBankAccount + ", feeAmount=" + feeAmount + ", handlerType=" + handlerType
                + ", billId=" + billId + ", payrefno=" + payrefno + "]";
    }

}
