package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;
/**
 * 
 * @className  RealTimeBO 
 * @description  实时业务配置BO
 * <AUTHOR>
 * @date 2017年12月1日 下午6:17:24 
 * @.belongToModule 收付费-实时业务配置
 *
 */
public class RealTimeBO extends BaseBO {
    
    /** 
     * @Fields serialVersionUID : 序列化版本号
     */ 
    private static final long serialVersionUID = 2300366204295804051L;
    
    /**
     * @Fields listId : 主键
     */
    private BigDecimal listId;
    
    /**
     * @Fields status : 启动状态
     */
    private String status;
    
    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;

    /**
     * @Fields arapFlag : 收付标志
     */
    private String arap;

    /**
     * @Fields operatorBy : 操作人
     */
    private BigDecimal operatorBy;

    /**
     * @Fields operatorOrgan : 操作机构
     */
    private String operatorOrgan;

    /**
     * @Fields operatorTime : 操作日期
     */
    private Date operatorTime;

    /**
     * @Fields bankName : 银行名称
     */
    private String bankName;

    /**
     * @Fields bankCode : 银行代码
     */
    private String bankCode;
    
    /**
     * @Fields limitAmount : 限额
     */
    private BigDecimal limitAmount;
    
    /**
     * @Fields limitAmount : 限额
     */
    private String specialAccountFlag;
    
    /**
     *  绩优标识
     */
    private BigDecimal meritFlag;
    
    /**
     *  理赔赔案层绩优等级码值
     */
    private BigDecimal greenFlagCode;

    public BigDecimal getMeritFlag() {
        return meritFlag;
    }

    public void setMeritFlag(BigDecimal meritFlag) {
        this.meritFlag = meritFlag;
    }

    public BigDecimal getGreenFlagCode() {
        return greenFlagCode;
    }

    public void setGreenFlagCode(BigDecimal greenFlagCode) {
        this.greenFlagCode = greenFlagCode;
    }

    public BigDecimal getListId() {
        return listId;
    }
    
    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getArap() {
        return arap;
    }

    public void setArap(String arap) {
        this.arap = arap;
    }

    public BigDecimal getOperatorBy() {
        return operatorBy;
    }
    
    public void setOperatorBy(BigDecimal operatorBy) {
        this.operatorBy = operatorBy;
    }

    public String getOperatorOrgan() {
        return operatorOrgan;
    }

    public void setOperatorOrgan(String operatorOrgan) {
        this.operatorOrgan = operatorOrgan;
    }

    public Date getOperatorTime() {
        return operatorTime;
    }

    public void setOperatorTime(Date operatorTime) {
        this.operatorTime = operatorTime;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }
    
    public BigDecimal getLimitAmount() {
        return limitAmount;
    }

    public void setLimitAmount(BigDecimal limitAmount) {
        this.limitAmount = limitAmount;
    }
    
    public String getSpecialAccountFlag() {
 		return specialAccountFlag;
 	}

 	public void setSpecialAccountFlag(String specialAccountFlag) {
 		this.specialAccountFlag = specialAccountFlag;
 	}

    @Override
	public String toString() {
		return "RealTimeBO [listId=" + listId + ", status=" + status
				+ ", derivType=" + derivType + ", arap=" + arap
				+ ", operatorBy=" + operatorBy + ", operatorOrgan="
				+ operatorOrgan + ", operatorTime=" + operatorTime
				+ ", bankName=" + bankName + ", bankCode=" + bankCode
				+ ", limitAmount=" + limitAmount + ", specialAccountFlag="
				+ specialAccountFlag + "]";
	}

    @Override
    public String getBizId() {
        return null;
    }
}
