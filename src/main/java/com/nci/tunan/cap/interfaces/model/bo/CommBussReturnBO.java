/**
 * @Title: CommBussReturnVO.java
 * @Package com.nci.tunan.cap.interfaces.model.bo
 * @Description: 公共UCC返回类
 * Copyright: Copyright (c) 2015
 * Company: 高伟达软件股份有限公司
 * 
 * <AUTHOR>
 * @date 2015-5-18 下午4:27:39
 * @version V1.0
 */

package com.nci.tunan.cap.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 公共UCC返回BO类
 * <AUTHOR>
 * @version 0.0.1 2015-5-18
 * @see <EMAIL> 2015-5-18 创建
 * @date 2017-12-20 下午2:01:55 
 * @.belongToModule 收付费-通知业务公共接口
 */
public class CommBussReturnBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化版本号
     */
    private static final long serialVersionUID = -3445156203896935803L;

    /**
     * @Fields isFlag : 是否成功标志位 true 成功、false 失败
     */
    private boolean isFlag;

    /**
     * @Fields errorMsg : 失败原因
     */
    private String errorCode;

    /**
     * @Fields errorDesc : 错误信息描述
     */
    private String errorDesc;

    @Override
    public String getBizId() {
        return null;
    }

    /**
     * getter 方法
     * 
     * @return isFlag
     */
    public boolean isFlag() {
        return isFlag;
    }

    public void setFlag(boolean flag) {
        this.isFlag = flag;
    }

    /**
     * getter 方法
     * 
     * @return errorCode
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * setter 方法
     * 
     * @param errorCode
     *            , 设置errorCode
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * getter 方法
     * 
     * @return errorDesc
     */
    public String getErrorDesc() {
        return errorDesc;
    }

    /**
     * setter 方法
     * 
     * @param errorDesc
     *            , 设置errorDesc
     */
    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

}
