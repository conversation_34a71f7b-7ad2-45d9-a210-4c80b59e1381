package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description AgentHandrateVO对象
 * <AUTHOR>
 * @date 2024-05-28 10:40:24
 */
public class AgentHandrateBO extends BaseBO {
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields modifyTime : 修改时间(渠道)
	 */
	private String modifyTime;
	/**
	 * @Fields productName : 险种名称
	 */
	private String productName;
	/**
	 * @Fields endDate : 有效止期
	 */
	private Date endDate;
	/**
	 * @Fields productCode : 险种代码
	 */
	private String productCode;
	/**
	 * @Fields operator : 操作人
	 */
	private String operator;
	/**
	 * @Fields modifyDate : 修改日期(渠道)
	 */
	private Date modifyDate;
	/**
	 * @Fields opFlag : 操作标记 1:新增、2:修改、3:删除
	 */
	private BigDecimal opFlag;
	/**
	 * @Fields protocolId : 关联协议编码
	 */
	private String protocolId;
	/**
	 * @Fields flag : 是否合同有效期：1-是、2-否
	 */
	private String flag;
	/**
	 * @Fields salesChannel : 销售渠道 代码表：t
	 */
	private String salesChannel;
	/**
	 * @Fields paymentMethod : 交费方式：0-月交、1-年交、2-一次交清、3-不定期交;
	 */
	private String paymentMethod;
	/**
	 * @Fields managecom : 销售/保单管理机构代码
	 */
	private String managecom;
	/**
	 * @Fields policyYear : 保单年度
	 */
	private String policyYear;
	/**
	 * @Fields spare3 : 备用字段3
	 */
	private Date spare3;
	/**
	 * @Fields spare2 : 备用字段2
	 */
	private String spare2;
	/**
	 * @Fields spare1 : 备用字段1
	 */
	private String spare1;
	/**
	 * @Fields payyear : 交费年期
	 */
	private String payyear;
	/**
	 * @Fields startDate : 有效起期
	 */
	private Date startDate;
	/**
	 * @Fields makeTime : 入机时间(渠道)
	 */
	private String makeTime;
	/**
	 * @Fields payType : 费用类型：01-手续费
	 */
	private String payType;
	/**
	 * @Fields serialNo : 主键流水号
	 */
	private BigDecimal serialNo;
	/**
	 * @Fields makeDate : 入机日期(渠道)
	 */
	private Date makeDate;
	/**
	 * @Fields agentCom : 中介机构代码
	 */
	private String agentCom;
	/**
	 * @Fields handlingRate : 手续费率
	 */
	private BigDecimal handlingRate;

	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getModifyTime() {
		return modifyTime;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getProductName() {
		return productName;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getOperator() {
		return operator;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	public Date getModifyDate() {
		return modifyDate;
	}

	public void setOpFlag(BigDecimal opFlag) {
		this.opFlag = opFlag;
	}

	public BigDecimal getOpFlag() {
		return opFlag;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getFlag() {
		return flag;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}

	public String getPaymentMethod() {
		return paymentMethod;
	}

	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}

	public String getManagecom() {
		return managecom;
	}

	public void setPolicyYear(String policyYear) {
		this.policyYear = policyYear;
	}

	public String getPolicyYear() {
		return policyYear;
	}

	public void setSpare3(Date spare3) {
		this.spare3 = spare3;
	}

	public Date getSpare3() {
		return spare3;
	}

	public void setSpare2(String spare2) {
		this.spare2 = spare2;
	}

	public String getSpare2() {
		return spare2;
	}

	public void setSpare1(String spare1) {
		this.spare1 = spare1;
	}

	public String getSpare1() {
		return spare1;
	}

	public void setPayyear(String payyear) {
		this.payyear = payyear;
	}

	public String getPayyear() {
		return payyear;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setMakeTime(String makeTime) {
		this.makeTime = makeTime;
	}

	public String getMakeTime() {
		return makeTime;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getPayType() {
		return payType;
	}

	public void setSerialNo(BigDecimal serialNo) {
		this.serialNo = serialNo;
	}

	public BigDecimal getSerialNo() {
		return serialNo;
	}

	public void setMakeDate(Date makeDate) {
		this.makeDate = makeDate;
	}

	public Date getMakeDate() {
		return makeDate;
	}

	public void setAgentCom(String agentCom) {
		this.agentCom = agentCom;
	}

	public String getAgentCom() {
		return agentCom;
	}

	public void setHandlingRate(BigDecimal handlingRate) {
		this.handlingRate = handlingRate;
	}

	public BigDecimal getHandlingRate() {
		return handlingRate;
	}

	@Override
	public String getBizId() {
		return null;
	}
}
