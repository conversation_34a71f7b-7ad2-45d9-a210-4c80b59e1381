package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description ReceiptBatchBO对象
 * <AUTHOR> 
 * @date 2016-02-22 10:26:58  
 * @.belongToModule 收付费-发票批打
 */
public class ReceiptBatchBO extends BaseBO {	
    /** 
    * @Fields serialVersionUID : 序列化版本号
    */ 
    
    private static final long serialVersionUID = -1106952821341761137L;
    /** 
	* @Fields taskMark :  票据任务标识
 	*/ 
	private BigDecimal taskMark;
	 /** 
	* @Fields signDate :  签收日期
 	*/ 
	private Date signDate;
	 /** 
	* @Fields qualityCheckBy :  质检人
 	*/ 
	private BigDecimal qualityCheckBy;
	 /** 
	* @Fields batchPrintDate :  发票批打日期
 	*/ 
	private Date batchPrintDate;
	 /** 
	* @Fields receiptBatchStatus :  批打发票状态
 	*/ 
	private String receiptBatchStatus;
		 /** 
	* @Fields batchNo :  物流批次号
 	*/ 
	private String batchNo;
	 /** 
	* @Fields realPrintDate :  实际打印日期
 	*/ 
	private Date realPrintDate;
	 /** 
	* @Fields expressCode :  快递单号
 	*/ 
	private String expressCode;
	 /** 
	* @Fields expressCompanyCode :  快递公司编号
 	*/ 
	private String expressCompanyCode;
		 /** 
	* @Fields realReceiptNumber :  实际发票数量
 	*/ 
	private BigDecimal realReceiptNumber;
	 /** 
	* @Fields arReceiptNumber :  应收发票数量
 	*/ 
	private BigDecimal arReceiptNumber;
		 /** 
	* @Fields organCode :  管理机构
 	*/ 
	private String organCode;
	 /** 
	* @Fields signBy :  签收人
 	*/ 
	private BigDecimal signBy;
				 /** 
	* @Fields receiptBatchId :  批打发票流水号
 	*/ 
	private BigDecimal receiptBatchId;
	 /** 
	* @Fields qualityCheckDate :  质检日期
 	*/ 
	private Date qualityCheckDate;
	/**
     * @Fields printStartTime :  打印起始日期
     */
    private Date printStartTime;
    /**
     * @Fields printStartTime :  打印结束日期
     */ 
    private Date printEndTime;
    /**
     * @Fields printEndTime :  机构名称
     */ 
    private String organName;
    
    
    public String getOrganName() {
        return organName;
    }
    
    public void setOrganName(String organName) {
        this.organName = organName;
    }
	
    public Date getPrintStartTime() {
        return printStartTime;
    }

    public void setPrintStartTime(Date printStartTime) {
        this.printStartTime = printStartTime;
    }

    public Date getPrintEndTime() {
        return printEndTime;
    }

    public void setPrintEndTime(Date printEndTime) {
        this.printEndTime = printEndTime;
    }

    public void setTaskMark(BigDecimal taskMark) {
		this.taskMark = taskMark;
	}
	
	public BigDecimal getTaskMark() {
		return taskMark;
	}
	 public void setSignDate(Date signDate) {
		this.signDate = signDate;
	}
	
	public Date getSignDate() {
		return signDate;
	}
	 public void setQualityCheckBy(BigDecimal qualityCheckBy) {
		this.qualityCheckBy = qualityCheckBy;
	}
	
	public BigDecimal getQualityCheckBy() {
		return qualityCheckBy;
	}
	 public void setBatchPrintDate(Date batchPrintDate) {
		this.batchPrintDate = batchPrintDate;
	}
	
	public Date getBatchPrintDate() {
		return batchPrintDate;
	}
	 public void setReceiptBatchStatus(String receiptBatchStatus) {
		this.receiptBatchStatus = receiptBatchStatus;
	}
	
	public String getReceiptBatchStatus() {
		return receiptBatchStatus;
	}
		 public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}
	
	public String getBatchNo() {
		return batchNo;
	}
	 public void setRealPrintDate(Date realPrintDate) {
		this.realPrintDate = realPrintDate;
	}
	
	public Date getRealPrintDate() {
		return realPrintDate;
	}
	 public void setExpressCode(String expressCode) {
		this.expressCode = expressCode;
	}
	
	public String getExpressCode() {
		return expressCode;
	}
	 public void setExpressCompanyCode(String expressCompanyCode) {
		this.expressCompanyCode = expressCompanyCode;
	}
	
	public String getExpressCompanyCode() {
		return expressCompanyCode;
	}
		 public void setRealReceiptNumber(BigDecimal realReceiptNumber) {
		this.realReceiptNumber = realReceiptNumber;
	}
	
	public BigDecimal getRealReceiptNumber() {
		return realReceiptNumber;
	}
	 public void setArReceiptNumber(BigDecimal arReceiptNumber) {
		this.arReceiptNumber = arReceiptNumber;
	}
	
	public BigDecimal getArReceiptNumber() {
		return arReceiptNumber;
	}
		 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
	 public void setSignBy(BigDecimal signBy) {
		this.signBy = signBy;
	}
	
	public BigDecimal getSignBy() {
		return signBy;
	}
				 public void setReceiptBatchId(BigDecimal receiptBatchId) {
		this.receiptBatchId = receiptBatchId;
	}
	
	public BigDecimal getReceiptBatchId() {
		return receiptBatchId;
	}
	 public void setQualityCheckDate(Date qualityCheckDate) {
		this.qualityCheckDate = qualityCheckDate;
	}
	
	public Date getQualityCheckDate() {
		return qualityCheckDate;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ReceiptBatchVO [" +
				"taskMark="+taskMark+","+
"signDate="+signDate+","+
"qualityCheckBy="+qualityCheckBy+","+
"batchPrintDate="+batchPrintDate+","+
"receiptBatchStatus="+receiptBatchStatus+","+
"batchNo="+batchNo+","+
"realPrintDate="+realPrintDate+","+
"expressCode="+expressCode+","+
"expressCompanyCode="+expressCompanyCode+","+
"realReceiptNumber="+realReceiptNumber+","+
"arReceiptNumber="+arReceiptNumber+","+
"organCode="+organCode+","+
"signBy="+signBy+","+
"receiptBatchId="+receiptBatchId+","+
"qualityCheckDate="+qualityCheckDate+"]";
    }
}
