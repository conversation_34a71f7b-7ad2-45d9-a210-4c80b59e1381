package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description SaveCashVO
 * @<NAME_EMAIL>
 * @date 2015-11-9 下午4:56:16
 * @.belongToModule 收付费-柜台收付费
 */
public class SaveCashBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 序列化版本号
     */

    private static final long serialVersionUID = 1497049460391421811L;

    /**
     * @Fields feeAmount : 收/付金额
     */
    private BigDecimal feeAmount;

    /**
     * @Fields payMode : 收/付费方式
     */
    private String payMode;

    /**
     * @Fields arapDate : 收/付日期
     */
    private Date arapDate;

    /**
     * @Fields arapBelnr: 付费凭证号/暂收据号
     */
    private String arapBelnr;

    /**
     * @Fields issuanceDate : 开票日期
     */
    private Date invoiceDate;

    /**
     * @Fields chequeNo :支票号码
     */
    private String chequeNo;
    /**
     * @Fields cabNo : 现金送款簿号码
     */
    private String cabNo;
    /**
     * @Fields cabNo : 银行转账单号码
     */
    private String btrNo;
    /**
     * @Fields billCompany :开票单位
     */
    private String chequeCompany;
    /**
     * @Fields expiryDate : 支票有效期/止期
     */
    private Date chequeEndDate;
    /**
     * @Fields chequeAmount :票据金额
     */
    private BigDecimal chequeAmount;
    /**
     * @Fields balanceOfBill : 票据未用余额
     */
    private BigDecimal balanceOfBill;
    /**
     * @Fields chequeBank : 开票银行
     */
    private String chequeBankName;
    /**
     * @Fields chequeBank : 开票银行
     */
    private String chequeBankCode;
    /**
     * @Fields arprBank: 收/付银行
     */
    private String arapBankCode;
    /**
     * @Fields arprBank: 收/付银行名称
     */
    private String arapBankName;
    /**
     * @Fields arprBankAccount: 付款银行账号
     */
    private String arprBankAccount;

    /**
     * @Fields handlerType : 经办人证件类型
     */
    private String handlerType;
    /**
     * @Fields handlerCode : 经办人证件号码
     */
    private String handlerCode;
    /**
     * @Fields handlerName : 经办人姓名
     */
    private String handlerName;
    /**
     * @Fields sapBelnr: 凭证号
     */
    private String sapBelnr;
    /**
     * @Fields sapBelnr: 收付标志
     */
    private String arapFlag;
    /**
     * @Fields customerBankCode: 客户银行号码
     */
    private String customerBankCode;
    /**
     * @Fields customerBankName: 客户银行
     */
    private String customerBankName;
    /**
     * @Fields customerName: 客户账户名
     */
    private String customerName;
    /**
     * @Fields customerAccount: 客户账号
     */
    private String customerAccount;
    /**
     * @Fields bankAccount: 银行账号
     */
    private String bankAccount;
    /**
     * @Fields bankCode: 银行编码
     */
    private String bankCode;
    /**
     * @Fields bankUserName: 银行户名
     */
    private String bankUserName;

    /**
     * @Fields billMainId : 票据表主键
     */
    private BigDecimal billMainId;
    /**
     * @Fields status : 票据状态
     */
    private String status;
    /**
     * @Fields tempFeeAmout :暂收费金额
     */
    private BigDecimal tempFeeAmount;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /** 
    * @Fields balanceTypeExist : 支票余额类型
    */ 
    private String balanceTypeExist;
    /** 
    * @Fields chequeBanlance : 支票余额
    */ 
    private BigDecimal chequeBanlance;
    /** 
    * @Fields balanceCheck :是否是已经存在的支票 :1:0 存在：不存在
    */ 
    private String balanceCheck;
    
    

    public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getBalanceCheck() {
		return balanceCheck;
	}

	public void setBalanceCheck(String balanceCheck) {
		this.balanceCheck = balanceCheck;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public BigDecimal getChequeBanlance() {
		return chequeBanlance;
	}

	public void setChequeBanlance(BigDecimal chequeBanlance) {
		this.chequeBanlance = chequeBanlance;
	}

	public String getBalanceTypeExist() {
		return balanceTypeExist;
	}

	public void setBalanceTypeExist(String balanceTypeExist) {
		this.balanceTypeExist = balanceTypeExist;
	}

	@Override
    public String getBizId() {
        return null;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public Date getArapDate() {
        return arapDate;
    }

    public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    public String getArapBelnr() {
        return arapBelnr;
    }

    public void setArapBelnr(String arapBelnr) {
        this.arapBelnr = arapBelnr;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getCabNo() {
        return cabNo;
    }

    public void setCabNo(String cabNo) {
        this.cabNo = cabNo;
    }

    public String getBtrNo() {
        return btrNo;
    }

    public void setBtrNo(String btrNo) {
        this.btrNo = btrNo;
    }

    public String getChequeCompany() {
        return chequeCompany;
    }

    public void setChequeCompany(String chequeCompany) {
        this.chequeCompany = chequeCompany;
    }

    public Date getChequeEndDate() {
        return chequeEndDate;
    }

    public void setChequeEndDate(Date chequeEndDate) {
        this.chequeEndDate = chequeEndDate;
    }

    public BigDecimal getChequeAmount() {
        return chequeAmount;
    }

    public void setChequeAmount(BigDecimal chequeAmount) {
        this.chequeAmount = chequeAmount;
    }

    public BigDecimal getBalanceOfBill() {
        return balanceOfBill;
    }

    public void setBalanceOfBill(BigDecimal balanceOfBill) {
        this.balanceOfBill = balanceOfBill;
    }

    public String getChequeBankName() {
        return chequeBankName;
    }

    public void setChequeBankName(String chequeBankName) {
        this.chequeBankName = chequeBankName;
    }

    public String getChequeBankCode() {
        return chequeBankCode;
    }

    public void setChequeBankCode(String chequeBankCode) {
        this.chequeBankCode = chequeBankCode;
    }

    public String getArapBankCode() {
        return arapBankCode;
    }

    public void setArapBankCode(String arapBankCode) {
        this.arapBankCode = arapBankCode;
    }

    public String getArapBankName() {
        return arapBankName;
    }

    public void setArapBankName(String arapBankName) {
        this.arapBankName = arapBankName;
    }

    public String getArprBankAccount() {
        return arprBankAccount;
    }

    public void setArprBankAccount(String arprBankAccount) {
        this.arprBankAccount = arprBankAccount;
    }

    public String getHandlerCode() {
        return handlerCode;
    }

    public void setHandlerCode(String handlerCode) {
        this.handlerCode = handlerCode;
    }

    public String getHandlerName() {
        return handlerName;
    }

    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }

    public String getSapBelnr() {
        return sapBelnr;
    }

    public void setSapBelnr(String sapBelnr) {
        this.sapBelnr = sapBelnr;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getCustomerBankCode() {
        return customerBankCode;
    }

    public void setCustomerBankCode(String customerBankCode) {
        this.customerBankCode = customerBankCode;
    }

    public String getCustomerBankName() {
        return customerBankName;
    }

    public void setCustomerBankName(String customerBankName) {
        this.customerBankName = customerBankName;
    }

    public String getCustomerAccount() {
        return customerAccount;
    }

    public void setCustomerAccount(String customerAccount) {
        this.customerAccount = customerAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getHandlerType() {
        return handlerType;
    }

    public void setHandlerType(String handlerType) {
        this.handlerType = handlerType;
    }

    public BigDecimal getBillMainId() {
        return billMainId;
    }

    public void setBillMainId(BigDecimal billMainId) {
        this.billMainId = billMainId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getTempFeeAmount() {
        return tempFeeAmount;
    }

    public void setTempFeeAmount(BigDecimal tempFeeAmount) {
        this.tempFeeAmount = tempFeeAmount;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

}
