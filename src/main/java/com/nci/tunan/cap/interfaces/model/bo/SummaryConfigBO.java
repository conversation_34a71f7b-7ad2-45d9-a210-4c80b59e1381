package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description 记账会计科目BO对象
 * <AUTHOR> 
 * @date 2016-12-19 19:40:03  
 * @.belongToModule 收付费-sap记账
 */
public class SummaryConfigBO extends BaseBO {	
	 /**
	 * 序列号
	 */
	private static final long serialVersionUID = -7243846239806430997L;
	/** 
	*  摘要规范
 	*/ 
	private String summary;
	 /** 
	* 会计科目
 	*/ 
	private String accuitem;
	 /** 
	*  凭证类别，SF收费，FF付费，XX其它
 	*/ 
	private String billkind;
	 /** 
	*   序列id
 	*/ 
	private BigDecimal listId;
	 /** 
	* 借/贷方
 	*/ 
	private String payway;
		
	 public void setSummary(String summary) {
		this.summary = summary;
	}
	
	public String getSummary() {
		return summary;
	}
	 public void setAccuitem(String accuitem) {
		this.accuitem = accuitem;
	}
	
	public String getAccuitem() {
		return accuitem;
	}
	 public void setBillkind(String billkind) {
		this.billkind = billkind;
	}
	
	public String getBillkind() {
		return billkind;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setPayway(String payway) {
		this.payway = payway;
	}
	
	public String getPayway() {
		return payway;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "SummaryConfigVO [" +
				"summary="+summary+","+
"accuitem="+accuitem+","+
"billkind="+billkind+","+
"listId="+listId+","+
"payway="+payway+"]";
    }
}
