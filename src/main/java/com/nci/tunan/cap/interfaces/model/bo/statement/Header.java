package com.nci.tunan.cap.interfaces.model.bo.statement;

/** 
 * @description 表头信息
 * <AUTHOR> <EMAIL> 
 * @date 2024-1-11 上午9:58:33  
*/
/** 
 * @description 
 * <AUTHOR> <EMAIL> 
 * @date 2024-1-11 上午10:06:25  
*/
public class Header {
    /** 
    * @Fields REPTDT : 统计日期
    */ 
    private String REPTDT;
    
    /** 
    * @Fields TABULATION_DATE : 制表日期
    */ 
    private String TABULATION_DATE;
    
    /** 
    * @Fields TABULATION_TIME : 制表时间
    */ 
    private String TABULATION_TIME;
    
    /** 
    * @Fields TABULATION_ORGAN_NAME : 填报单位/填报机构
    */ 
    private String TABULATION_ORGAN_NAME;
    
    /** 
    * @Fields ORGAN_CODE : 管理机构编码/管理机构代码
    */ 
    private String ORGAN_CODE;
    
    /** 
    * @Fields ORGAN_NAME : 管理机构名称
    */ 
    private String ORGAN_NAME;
    
    /** 
    * @Fields CAP_ORGAN_NAME : 收付机构/收费机构   
    */ 
    private String CAP_ORGAN_NAME;
    
    /** 
     * @Fields CAP_ORGAN_CODE : 收付机构代码/收费机构代码
     */ 
     private String CAP_ORGAN_CODE;
    
    /** 
    * @Fields CURRENCY : 单位/金额单位: 元
    */ 
    private String CURRENCY = "元";

    public String getREPTDT() {
        return REPTDT;
    }

    public void setREPTDT(String rEPTDT) {
        REPTDT = rEPTDT;
    }

    public String getTABULATION_DATE() {
        return TABULATION_DATE;
    }

    public void setTABULATION_DATE(String tABULATION_DATE) {
        TABULATION_DATE = tABULATION_DATE;
    }

    public String getTABULATION_TIME() {
        return TABULATION_TIME;
    }

    public void setTABULATION_TIME(String tABULATION_TIME) {
        TABULATION_TIME = tABULATION_TIME;
    }

    public String getTABULATION_ORGAN_NAME() {
        return TABULATION_ORGAN_NAME;
    }

    public void setTABULATION_ORGAN_NAME(String tABULATION_ORGAN_NAME) {
        TABULATION_ORGAN_NAME = tABULATION_ORGAN_NAME;
    }

    public String getORGAN_CODE() {
        return ORGAN_CODE;
    }

    public void setORGAN_CODE(String oRGAN_CODE) {
        ORGAN_CODE = oRGAN_CODE;
    }

    public String getORGAN_NAME() {
        return ORGAN_NAME;
    }

    public void setORGAN_NAME(String oRGAN_NAME) {
        ORGAN_NAME = oRGAN_NAME;
    }

    public String getCURRENCY() {
        return CURRENCY;
    }

    public void setCURRENCY(String cURRENCY) {
        CURRENCY = cURRENCY;
    }

    public String getCAP_ORGAN_NAME() {
        return CAP_ORGAN_NAME;
    }

    public void setCAP_ORGAN_NAME(String cAP_ORGAN_NAME) {
        CAP_ORGAN_NAME = cAP_ORGAN_NAME;
    }

    public String getCAP_ORGAN_CODE() {
        return CAP_ORGAN_CODE;
    }

    public void setCAP_ORGAN_CODE(String cAP_ORGAN_CODE) {
        CAP_ORGAN_CODE = cAP_ORGAN_CODE;
    }
    
    

}
