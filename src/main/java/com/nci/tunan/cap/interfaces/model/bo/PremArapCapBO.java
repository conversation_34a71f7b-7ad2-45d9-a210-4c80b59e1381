package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description PremArapVO对象
 * <AUTHOR> <EMAIL>
 * @date 2015-05-28 16:43:52
 * @.belongToModule CAP-收付费系统
 */
public class PremArapCapBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 序列化
     */
    private static final long serialVersionUID = 8537072217550681435L;
    /**
     * @Fields policyOrganCode : 保单直属机构
     */
    private String policyOrganCode;
    /**
     * @Fields holderName : 投保人姓名
     */
    private String holderName;
    /**
     * @Fields isItemMain : 主组合产品标识
     */
    private String isItemMain;
    /**
     * @Fields accountId : 账户id
     */
    private BigDecimal accountId;
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;
    /**
     * @Fields payeePhone : 收付费人手机号
     */
    private String payeePhone;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields paidCount : 数量
     */
    private BigDecimal paidCount;
    /**
     * @Fields feeType : 费用业务类型
     */
    private BigDecimal feeType;
    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;
    /**
     * @Fields applyCode : 投保单号码
     */
    private String applyCode;
    /**
     * @Fields organCode : 投保单号码
     */
    private String organCode;
    /**
     * @Fields channelType : 渠道
     */
    private String channelType;
    /**
     * @Fields chargeYear : 保障年期
     */
    private BigDecimal chargeYear;
    /**
     * @Fields busiProdPackName : 险种名称
     */
    private String busiProdPackName;
    /**
     * @Fields isRiskMain : 主险附加险标识
     */
    private String isRiskMain;
    /**
     * @Fields posted : 记账状态
     */
    private String posted;
    /**
     * @Fields insuredName : 被保人姓名
     */
    private String insuredName;
    /**
     * @Fields insuredId : 被保人编码
     */
    private BigDecimal insuredId;
    /**
     * @Fields policyType : 个单、团单
     */
    private String policyType;
    /**
     * @Fields productChannel : 产品渠道
     */
    private String productChannel;
    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;
    /**
     * @Fields payMode : 收付方式
     */
    private String payMode;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;
    /**
     * @Fields validateDate : 业务生效日期
     */
    private Date validateDate;
    /**
     * @Fields businessType : 业务类型
     */
    private BigDecimal businessType;
    /**
     * @Fields arapFeeId : 应收id
     */
    private BigDecimal arapFeeId;
    /**
     * @Fields certiType : 证件类型
     */
    private String certiType;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。
     */
    private String businessCode;
    /**
     * @Fields moneyCode : 金额代码
     */
    private String moneyCode;
    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;
    /**
     * @Fields bankAccount : 银行账号
     */
    private String bankAccount;
    /**
     * @Fields checkEnterTime : 插入时间
     */
    private Date checkEnterTime;
    /**
     * @Fields finishTime : 费用到帐时间
     */
    private Date finishTime;
    /**
     * @Fields dueTime : 应缴应付日
     */
    private Date dueTime;
    /**
     * @Fields certiCode : 收付款人证件号码
     */
    private String certiCode;
    /**
     * @Fields busiApplyDate : 业务申请日
     */
    private Date busiApplyDate;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields holderId : 投保人编码
     */
    private BigDecimal holderId;
    /**
     * @Fields withdrawType : 记账业务类型
     */
    private String withdrawType;
    /**
     * @Fields rollbackUnitNumber : 回退应收付业务流水标识
     */
    private String rollbackUnitNumber;
    /**
     * @Fields customerId : 收付款客户号
     */
    private BigDecimal customerId;
    /**
     * @Fields policyYear : 年度当前年度
     */
    private BigDecimal policyYear;
    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;
    /**
     * @Fields feeStatus : 费用状态
     */
    private String feeStatus;
    /**
     * @Fields syncFlag : 是否同步到新核心标识
     */
    private BigDecimal syncFlag;
    /**
     * @Fields busiProdPackCode : 险种代码
     */
    private String busiProdPackCode;
    /**
     * @Fields payEndDate : 宽限期止期
     */
    private Date payEndDate;
    /**
     * @Fields derivType : 系统来源
     */
    private String derivType;
    /**
     * @Fields eventCode : 业务操作事件代码
     */
    private String eventCode;
    /**
     * @Fields arapFlag : 收付类型
     */
    private String arapFlag;
    /**
     * @Fields bankCode : 银行代码
     */
    private String bankCode;
    /**
     * @Fields agentCode : 代理人员CODE
     */
    private String agentCode;
    /**
     * @Fields premFreq : 缴费频率。码值参考码表T_CHARGE_MODE
     */
    private String premFreq;

    /**
     * @Fields batchNo : 批次号
     */
    private String batchNo;

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setIsItemMain(String isItemMain) {
        this.isItemMain = isItemMain;
    }

    public String getIsItemMain() {
        return isItemMain;
    }

    public void setAccountId(BigDecimal accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getAccountId() {
        return accountId;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = payeePhone;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public void setFeeType(BigDecimal feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getFeeType() {
        return feeType;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public void setBusiProdPackName(String busiProdPackName) {
        this.busiProdPackName = busiProdPackName;
    }

    public String getBusiProdPackName() {
        return busiProdPackName;
    }

    public void setIsRiskMain(String isRiskMain) {
        this.isRiskMain = isRiskMain;
    }

    public String getIsRiskMain() {
        return isRiskMain;
    }

    public void setPosted(String posted) {
        this.posted = posted;
    }

    public String getPosted() {
        return posted;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setBusinessType(BigDecimal businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getBusinessType() {
        return businessType;
    }

    public void setArapFeeId(BigDecimal arapFeeId) {
        this.arapFeeId = arapFeeId;
    }

    public BigDecimal getArapFeeId() {
        return arapFeeId;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setCheckEnterTime(Date checkEnterTime) {
        this.checkEnterTime = checkEnterTime;
    }

    public Date getCheckEnterTime() {
        return checkEnterTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setBusiApplyDate(Date busiApplyDate) {
        this.busiApplyDate = busiApplyDate;
    }

    public Date getBusiApplyDate() {
        return busiApplyDate;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setHolderId(BigDecimal holderId) {
        this.holderId = holderId;
    }

    public BigDecimal getHolderId() {
        return holderId;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setRollbackUnitNumber(String rollbackUnitNumber) {
        this.rollbackUnitNumber = rollbackUnitNumber;
    }

    public String getRollbackUnitNumber() {
        return rollbackUnitNumber;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public void setPolicyYear(BigDecimal policyYear) {
        this.policyYear = policyYear;
    }

    public BigDecimal getPolicyYear() {
        return policyYear;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setSyncFlag(BigDecimal syncFlag) {
        this.syncFlag = syncFlag;
    }

    public BigDecimal getSyncFlag() {
        return syncFlag;
    }

    public void setBusiProdPackCode(String busiProdPackCode) {
        this.busiProdPackCode = busiProdPackCode;
    }

    public String getBusiProdPackCode() {
        return busiProdPackCode;
    }

    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setPremFreq(String premFreq) {
        this.premFreq = premFreq;
    }

    public String getPremFreq() {
        return premFreq;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "PremArapVO [" + "policyOrganCode=" + policyOrganCode + ", holderName=" + holderName + ","
                + "isItemMain=" + isItemMain + ", accountId=" + accountId + ", busiProdName=" + busiProdName
                + ", payeePhone=" + payeePhone + ", unitNumber=" + unitNumber + ", paidCount=" + paidCount
                + ", feeType=" + feeType + ", busiProdCode=" + busiProdCode + ", applyCode=" + applyCode
                + ", organCode=" + organCode + ", channelType=" + channelType + ", chargeYear=" + chargeYear
                + ", busiProdPackName=" + busiProdPackName + ", isRiskMain=" + isRiskMain + ", posted=" + posted
                + ", insuredName=" + insuredName + ", insuredId=" + insuredId + ", policyType=" + policyType
                + ", productChannel=" + productChannel + ", policyCode=" + policyCode + ", payMode=" + payMode
                + ", branchCode=" + branchCode + ", validateDate=" + validateDate + ", businessType=" + businessType
                + ", arapFeeId=" + arapFeeId + ", certiType=" + certiType + ", businessCode=" + businessCode
                + ", moneyCode=" + moneyCode + ", payeeName=" + payeeName + ", bankAccount=" + bankAccount
                + ", checkEnterTime=" + checkEnterTime + ", finishTime=" + finishTime + ", dueTime=" + dueTime
                + ", certiCode=" + certiCode + ", busiApplyDate=" + busiApplyDate + ", feeAmount=" + feeAmount + ","
                + "holderId=" + holderId + ", withdrawType=" + withdrawType + ", rollbackUnitNumber="
                + rollbackUnitNumber + ", customerId=" + customerId + ", policyYear=" + policyYear + ", bankUserName="
                + bankUserName + ", feeStatus=" + feeStatus + ", syncFlag=" + syncFlag + ", busiProdPackCode="
                + busiProdPackCode + ", payEndDate=" + payEndDate + ", derivType=" + derivType + ", eventCode="
                + eventCode + ", arapFlag=" + arapFlag + ", bankCode=" + bankCode + ", agentCode=" + agentCode
                + ", premFreq=" + premFreq + "]";
    }
}
