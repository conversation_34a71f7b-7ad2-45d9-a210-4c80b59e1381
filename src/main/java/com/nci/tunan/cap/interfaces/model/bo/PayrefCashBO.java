package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description 实收付交易表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL> 
 * @date 2017-03-13 14:01:35  
 */
public class PayrefCashBO extends BaseBO {	

	/** 
	* @Fields serialVersionUID : 序列化 
	*/ 
	private static final long serialVersionUID = -6357774693121205693L;
	/** 
	* @Fields activePayAmount :  移动支付金额
 	*/ 
	private BigDecimal activePayAmount;
	 /** 
	* @Fields moneyCode :  币种代码
 	*/ 
	private String moneyCode;
	 /** 
	* @Fields cabAmount :  现金送款薄金额
 	*/ 
	private BigDecimal cabAmount;
	 /** 
	* @Fields payrefno :  实付号
 	*/ 
	private String payrefno;
	 /** 
	* @Fields bankInsuranceAmount :  银保通金额
 	*/ 
	private BigDecimal bankInsuranceAmount;
	 /** 
	* @Fields mobilePayAmount :  移动支付金额
 	*/ 
	private BigDecimal mobilePayAmount;
		 /** 
	* @Fields cusAccFeeAmount :  使用客户账户金额
 	*/ 
	private BigDecimal cusAccFeeAmount;
		 /** 
	* @Fields sumAmount :  本次收付金额
 	*/ 
	private BigDecimal sumAmount;
		 /** 
	* @Fields cashAmount :  现金金额
 	*/ 
	private BigDecimal cashAmount;
		 /** 
	* @Fields arapFlag :  应收付标识
 	*/ 
	private String arapFlag;
	 /** 
	* @Fields bankTextAmount :  制返盘金额
 	*/ 
	private BigDecimal bankTextAmount;
	 /** 
	* @Fields payrefId :  交易流水号
 	*/ 
	private BigDecimal payrefId;
	 /** 
	* @Fields chequeAmount :  支票金额
 	*/ 
	private BigDecimal chequeAmount;
	 /** 
	* @Fields btrAmount :  银行转账单金额
 	*/ 
	private BigDecimal btrAmount;
	/** 
	* @Fields otherAmount :  第三方支付金额
 	*/ 
	private BigDecimal otherAmount;
	 /** 
	* @Fields posAmount :  POS刷卡金额
 	*/ 
	private BigDecimal posAmount;
		
	 public void setActivePayAmount(BigDecimal activePayAmount) {
		this.activePayAmount = activePayAmount;
	}
	
	public BigDecimal getActivePayAmount() {
		return activePayAmount;
	}
	 public void setMoneyCode(String moneyCode) {
		this.moneyCode = moneyCode;
	}
	
	public String getMoneyCode() {
		return moneyCode;
	}
	 public void setCabAmount(BigDecimal cabAmount) {
		this.cabAmount = cabAmount;
	}
	
	public BigDecimal getCabAmount() {
		return cabAmount;
	}
	 public void setPayrefno(String payrefno) {
		this.payrefno = payrefno;
	}
	
	public String getPayrefno() {
		return payrefno;
	}
	 public void setBankInsuranceAmount(BigDecimal bankInsuranceAmount) {
		this.bankInsuranceAmount = bankInsuranceAmount;
	}
	
	public BigDecimal getBankInsuranceAmount() {
		return bankInsuranceAmount;
	}
	 public void setMobilePayAmount(BigDecimal mobilePayAmount) {
		this.mobilePayAmount = mobilePayAmount;
	}
	
	public BigDecimal getMobilePayAmount() {
		return mobilePayAmount;
	}
		 public void setCusAccFeeAmount(BigDecimal cusAccFeeAmount) {
		this.cusAccFeeAmount = cusAccFeeAmount;
	}
	
	public BigDecimal getCusAccFeeAmount() {
		return cusAccFeeAmount;
	}
		 public void setSumAmount(BigDecimal sumAmount) {
		this.sumAmount = sumAmount;
	}
	
	public BigDecimal getSumAmount() {
		return sumAmount;
	}
		 public void setCashAmount(BigDecimal cashAmount) {
		this.cashAmount = cashAmount;
	}
	
	public BigDecimal getCashAmount() {
		return cashAmount;
	}
		 public void setArapFlag(String arapFlag) {
		this.arapFlag = arapFlag;
	}
	
	public String getArapFlag() {
		return arapFlag;
	}
	 public void setBankTextAmount(BigDecimal bankTextAmount) {
		this.bankTextAmount = bankTextAmount;
	}
	
	public BigDecimal getBankTextAmount() {
		return bankTextAmount;
	}
	 public void setPayrefId(BigDecimal payrefId) {
		this.payrefId = payrefId;
	}
	
	public BigDecimal getPayrefId() {
		return payrefId;
	}
	 public void setChequeAmount(BigDecimal chequeAmount) {
		this.chequeAmount = chequeAmount;
	}
	
	public BigDecimal getChequeAmount() {
		return chequeAmount;
	}
	 public void setBtrAmount(BigDecimal btrAmount) {
		this.btrAmount = btrAmount;
	}
	
	public BigDecimal getBtrAmount() {
		return btrAmount;
	}
			 public void setOtherAmount(BigDecimal otherAmount) {
		this.otherAmount = otherAmount;
	}
	
	public BigDecimal getOtherAmount() {
		return otherAmount;
	}
	 public void setPosAmount(BigDecimal posAmount) {
		this.posAmount = posAmount;
	}
	
	public BigDecimal getPosAmount() {
		return posAmount;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "PayrefCashBO [" +
				"activePayAmount="+activePayAmount+","+
"moneyCode="+moneyCode+","+
"cabAmount="+cabAmount+","+
"payrefno="+payrefno+","+
"bankInsuranceAmount="+bankInsuranceAmount+","+
"mobilePayAmount="+mobilePayAmount+","+
"cusAccFeeAmount="+cusAccFeeAmount+","+
"sumAmount="+sumAmount+","+
"cashAmount="+cashAmount+","+
"arapFlag="+arapFlag+","+
"bankTextAmount="+bankTextAmount+","+
"payrefId="+payrefId+","+
"chequeAmount="+chequeAmount+","+
"btrAmount="+btrAmount+","+
"otherAmount="+otherAmount+","+
"posAmount="+posAmount+"]";
    }
}
