package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;
/**
 * @description DailyJZReportBO对象
 * <AUTHOR>
 * @date 2019-02-28 14:06:25
 * @.belongToModule 收付费-日结报表
 */
public class DailyJZReportBO extends BaseBO{


	/** 
	* @Fields serialVersionUID : SUID
	*/ 
	private static final long serialVersionUID = 1L;
    /** 
    * @Fields money : 金额
    */ 
    private BigDecimal money;
    /** 
    * @Fields numbers : 数量
    */ 
    private BigDecimal numbers;
    
	public DailyJZReportBO(BigDecimal money, BigDecimal numbers) {
		super();
		this.money = money;
		this.numbers = numbers;
	}
	@Override
	public String getBizId() {
		
		return null;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public BigDecimal getNumbers() {
		return numbers;
	}
	public void setNumbers(BigDecimal numbers) {
		this.numbers = numbers;
	}

}
