package com.nci.tunan.cap.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 开户行码表BO对象
 * <AUTHOR>
 * @date 2020-11-06 11:10:21
 */
public class BankOfDepositBO extends BaseBO {
	private static final long serialVersionUID = 1L;
	
	/**
     * 联行号
     */
    private String correspondentNo;

    /**
     *  支行银行
     */
    private String correspondentName;

    /**
     *  银行代码
     */
    private String bankCode;

    /**
     *  银行简称
     */
    private String bankAbbname;

    /**
     *  银行名称
     */
    private String bankName;

    public String getCorrespondentNo() {
        return correspondentNo;
    }

    public void setCorrespondentNo(String correspondentNo) {
        this.correspondentNo = correspondentNo;
    }

    public String getCorrespondentName() {
        return correspondentName;
    }

    public void setCorrespondentName(String correspondentName) {
        this.correspondentName = correspondentName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAbbname() {
        return bankAbbname;
    }

    public void setBankAbbname(String bankAbbname) {
        this.bankAbbname = bankAbbname;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    @Override
    public String toString() {
        return "BankOfDepositBO [correspondentNo=" + correspondentNo + ", correspondentName=" + correspondentName
                + ", bankCode=" + bankCode + ", bankAbbname=" + bankAbbname + ", bankName=" + bankName + "]";
    }

    @Override
    public String getBizId() {
        return "开户行码表BO";
    }
    
}
