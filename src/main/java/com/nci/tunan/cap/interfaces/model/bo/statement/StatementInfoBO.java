package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;
 
/** 
 * @description StatementInfoVO对象
 * <AUTHOR> 
 * @date 2023-12-27 14:20:40  
 */
public class StatementInfoBO extends BaseBO {	
	 /** 
    * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
    */ 
    
    private static final long serialVersionUID = 1L;
    /** 
	* @Fields createTime :  日结文件创建时间
 	*/ 
	private Date createTime;
	 /** 
	* @Fields statementStatus :  归档状态，代码表
 	*/ 
	private String statementStatus;
	 /** 
	* @Fields tabulateTime :  制表时间
 	*/ 
	private Date tabulateTime;
	 /** 
	* @Fields taskId :  日结文件推送任务ID
 	*/ 
	private String taskId;
	 /** 
	* @Fields statementPeriod :  D-日;M-月
 	*/ 
	private String statementPeriod;
		 /** 
	* @Fields organCode :  机构代码
 	*/ 
	private String organCode;
	
	   
    /** 
    * @Fields organName : 机构名称
    */ 
    private String organName;
    
	 /** 
	* @Fields filingPath :  日结文件归档路径
 	*/ 
	private String filingPath;
	 /** 
	* @Fields reviewTime :  复核时间
 	*/ 
	private Date reviewTime;
	 /** 
	* @Fields reviewCasue :  复核不通过原因
 	*/ 
	private String reviewCasue;
		 /** 
	* @Fields filePushTime :  日结文件推送时间
 	*/ 
	private Date filePushTime;
	 /** 
	* @Fields createOperator :  日结文件创建人
 	*/ 
	private BigDecimal createOperator;
	 /** 
	* @Fields fileInfoPushMsg :  归档通知返回失败原因
 	*/ 
	private String fileInfoPushMsg;
	 /** 
	* @Fields organType :  机构类型，ManageOrg-管理机构;CapOrg-收付费机构
 	*/ 
	private String organType;
	 /** 
	* @Fields listId :  主键序列
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields filingRefuseCasue :  归档驳回原因
 	*/ 
	private String filingRefuseCasue;
	 /** 
	* @Fields statementDate :  日结日期/月份
 	*/ 
	private Date statementDate;
	   
    /** 
     * @Fields statementDate :  截止日期/月份
     */ 
    private Date statementEndDate;
    
	 /** 
	* @Fields tabulateOperator :  制表人
 	*/ 
	private BigDecimal tabulateOperator;
	 /** 
	* @Fields filingTime :  归档/归档驳回时间
 	*/ 
	private Date filingTime;
	 /** 
	* @Fields sapOrganCode :  SAP机构代码
 	*/ 
	private String sapOrganCode;
		 /** 
	* @Fields filingProcess :  日结文件推送进度：1-上传FTP成功；2-上传FTP失败；3-通知成功；4-通知失败
 	*/ 
	private String filingProcess;
	 /** 
	* @Fields isFiling :  是否推送电子档案系统，1-是；0-否。
 	*/ 
	private BigDecimal isFiling;
	 /** 
	* @Fields reviewOperator :  复核人
 	*/ 
	private BigDecimal reviewOperator;
		 /** 
	* @Fields filingOperator :  归档人/归档驳回人
 	*/ 
	private BigDecimal filingOperator;
	 /** 
	* @Fields statementClobId :  日结报文ID，T
 	*/ 
	private BigDecimal statementClobId;
	 /** 
	* @Fields statementType :  日结类型代码，如： CS001
 	*/ 
	private String statementType;
	 /**
     * @Fields statementType : 日结名称，如： 新契约日结
     */
    private String statementName;
	 /** 
	* @Fields reviewResult :  复核结论，1-通过;0-不通过
 	*/ 
	private BigDecimal reviewResult;
	 /** 
	* @Fields filingResultMsg :  归档查询返回失败原因
 	*/ 
	private String filingResultMsg;
	 /** 
	* @Fields fileName :  日结文件名称
 	*/ 
	private String fileName;
	 /** 
	* @Fields fileInfoPushTime :  归档文件信息通知时间
 	*/ 
	private Date fileInfoPushTime;
	
	/** 
     * @Fields rowNumber : 序号
    */ 
    private BigDecimal listStr;
    
    /** 
     * @Fields statementTypes : 日结类型 集合
     */ 
     private List<String> statementTypes;
     
     /** 
      * @Fields statementTypes : 归档状态集合
     */ 
     private List<String> statusList;
     
     /**
      * @Fields errorFlag : 是否有差异说明
      */
     private String errorFlag;

     /**
      * 标记：初始化查询 0   有条件查询1
      */
     private String flag;
     
     /** 
      * @Fields onlyBranchOrgan : 仅查询分公司本级数据，T_YES_NO
      */ 
     private BigDecimal onlyBranchOrgan;
     
     /** 
      * @Fields existReportData : 日结是否有数据，T_YES_NO
      */ 
     private BigDecimal existReportData;
     
     public BigDecimal getOnlyBranchOrgan() {
         return onlyBranchOrgan;
     }

     public void setOnlyBranchOrgan(BigDecimal onlyBranchOrgan) {
         this.onlyBranchOrgan = onlyBranchOrgan;
     }

     public BigDecimal getExistReportData() {
         return existReportData;
     }

     public void setExistReportData(BigDecimal existReportData) {
         this.existReportData = existReportData;
     }

     public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getErrorFlag() {
        return errorFlag;
    }

    public void setErrorFlag(String errorFlag) {
        this.errorFlag = errorFlag;
    }

    public List<String> getStatusList() {
         return statusList;
     }

     public void setStatusList(List<String> statusList) {
         this.statusList = statusList;
     }
     
                    
     public List<String> getStatementTypes() {
        return statementTypes;
    }
    
    public void setStatementTypes(List<String> statementTypes) {
        this.statementTypes = statementTypes;
    }

    public BigDecimal getListStr() {
        return listStr;
    }

    public void setListStr(BigDecimal listStr) {
        this.listStr = listStr;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public Date getStatementEndDate() {
        return statementEndDate;
    }

    public void setStatementEndDate(Date statementEndDate) {
        this.statementEndDate = statementEndDate;
    }

    public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return createTime;
	}
	 public void setStatementStatus(String statementStatus) {
		this.statementStatus = statementStatus;
	}
	
	public String getStatementStatus() {
		return statementStatus;
	}
	 public void setTabulateTime(Date tabulateTime) {
		this.tabulateTime = tabulateTime;
	}
	
	public Date getTabulateTime() {
		return tabulateTime;
	}
	 public void setTaskId(String taskId) {
		this.taskId = taskId;
	}
	
	public String getTaskId() {
		return taskId;
	}
	 public void setStatementPeriod(String statementPeriod) {
		this.statementPeriod = statementPeriod;
	}
	
	public String getStatementPeriod() {
		return statementPeriod;
	}
		 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
	 public void setFilingPath(String filingPath) {
		this.filingPath = filingPath;
	}
	
	public String getFilingPath() {
		return filingPath;
	}
	 public void setReviewTime(Date reviewTime) {
		this.reviewTime = reviewTime;
	}
	
	public Date getReviewTime() {
		return reviewTime;
	}
	 public void setReviewCasue(String reviewCasue) {
		this.reviewCasue = reviewCasue;
	}
	
	public String getReviewCasue() {
		return reviewCasue;
	}
		 public void setFilePushTime(Date filePushTime) {
		this.filePushTime = filePushTime;
	}
	
	public Date getFilePushTime() {
		return filePushTime;
	}
	 public void setCreateOperator(BigDecimal createOperator) {
		this.createOperator = createOperator;
	}
	
	public BigDecimal getCreateOperator() {
		return createOperator;
	}
	 public void setFileInfoPushMsg(String fileInfoPushMsg) {
		this.fileInfoPushMsg = fileInfoPushMsg;
	}
	
	public String getFileInfoPushMsg() {
		return fileInfoPushMsg;
	}
	 public void setOrganType(String organType) {
		this.organType = organType;
	}
	
	public String getOrganType() {
		return organType;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setFilingRefuseCasue(String filingRefuseCasue) {
		this.filingRefuseCasue = filingRefuseCasue;
	}
	
	public String getFilingRefuseCasue() {
		return filingRefuseCasue;
	}
	 public void setStatementDate(Date statementDate) {
		this.statementDate = statementDate;
	}
	
	public Date getStatementDate() {
		return statementDate;
	}
	 public void setTabulateOperator(BigDecimal tabulateOperator) {
		this.tabulateOperator = tabulateOperator;
	}
	
	public BigDecimal getTabulateOperator() {
		return tabulateOperator;
	}
	 public void setFilingTime(Date filingTime) {
		this.filingTime = filingTime;
	}
	
	public Date getFilingTime() {
		return filingTime;
	}
	 public void setSapOrganCode(String sapOrganCode) {
		this.sapOrganCode = sapOrganCode;
	}
	
	public String getSapOrganCode() {
		return sapOrganCode;
	}
		 public void setFilingProcess(String filingProcess) {
		this.filingProcess = filingProcess;
	}
	
	public String getFilingProcess() {
		return filingProcess;
	}
	 public void setIsFiling(BigDecimal isFiling) {
		this.isFiling = isFiling;
	}
	
	public BigDecimal getIsFiling() {
		return isFiling;
	}
	 public void setReviewOperator(BigDecimal reviewOperator) {
		this.reviewOperator = reviewOperator;
	}
	
	public BigDecimal getReviewOperator() {
		return reviewOperator;
	}
		 public void setFilingOperator(BigDecimal filingOperator) {
		this.filingOperator = filingOperator;
	}
	
	public BigDecimal getFilingOperator() {
		return filingOperator;
	}
	 public void setStatementClobId(BigDecimal statementClobId) {
		this.statementClobId = statementClobId;
	}
	
	public BigDecimal getStatementClobId() {
		return statementClobId;
	}
	 public void setStatementType(String statementType) {
		this.statementType = statementType;
	}
	
	public String getStatementType() {
		return statementType;
	}
	public String getStatementName() {
        return statementName;
    }

    public void setStatementName(String statementName) {
        this.statementName = statementName;
    }

    public void setReviewResult(BigDecimal reviewResult) {
		this.reviewResult = reviewResult;
	}
	
	public BigDecimal getReviewResult() {
		return reviewResult;
	}
	 public void setFilingResultMsg(String filingResultMsg) {
		this.filingResultMsg = filingResultMsg;
	}
	
	public String getFilingResultMsg() {
		return filingResultMsg;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	public String getFileName() {
		return fileName;
	}
	
	public void setFileInfoPushTime(Date fileInfoPushTime) {
		this.fileInfoPushTime = fileInfoPushTime;
	}
	
	public Date getFileInfoPushTime() {
		return fileInfoPushTime;
	}
				
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "StatementInfoVO [" +
				"createTime="+createTime+","+
"statementStatus="+statementStatus+","+
"tabulateTime="+tabulateTime+","+
"taskId="+taskId+","+
"statementPeriod="+statementPeriod+","+
"organCode="+organCode+","+
"filingPath="+filingPath+","+
"reviewTime="+reviewTime+","+
"reviewCasue="+reviewCasue+","+
"filePushTime="+filePushTime+","+
"createOperator="+createOperator+","+
"fileInfoPushMsg="+fileInfoPushMsg+","+
"organType="+organType+","+
"listId="+listId+","+
"filingRefuseCasue="+filingRefuseCasue+","+
"statementDate="+statementDate+","+
"tabulateOperator="+tabulateOperator+","+
"filingTime="+filingTime+","+
"sapOrganCode="+sapOrganCode+","+
"filingProcess="+filingProcess+","+
"isFiling="+isFiling+","+
"reviewOperator="+reviewOperator+","+
"filingOperator="+filingOperator+","+
"statementClobId="+statementClobId+","+
"statementType="+statementType+","+
"reviewResult="+reviewResult+","+
"filingResultMsg="+filingResultMsg+","+
"fileName="+fileName+","+
"fileInfoPushTime="+fileInfoPushTime+"]";
    }
}
