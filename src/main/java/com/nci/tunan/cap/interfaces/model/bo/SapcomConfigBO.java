package com.nci.tunan.cap.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description SAP机构配置表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class SapcomConfigBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 6324774434235632188L;
    /**
     * @Fields sysname : 业务机构名称
     */
    private String sysname;
    /**
     * @Fields syscode : 业务机构代码
     */
    private String syscode;
    /**
     * @Fields sapcomcode : SAP机构代码
     */
    private String sapcomcode;

    public void setSysname(String sysname) {
        this.sysname = sysname;
    }

    public String getSysname() {
        return sysname;
    }

    public void setSapcomcode(String sapcomcode) {
        this.sapcomcode = sapcomcode;
    }

    public String getSapcomcode() {
        return sapcomcode;
    }

    public void setSyscode(String syscode) {
        this.syscode = syscode;
    }

    public String getSyscode() {
        return syscode;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "SapcomConfigBO [" + "sysname=" + sysname + ", sapcomcode=" + sapcomcode + ", syscode=" + syscode + "]";
    }
}
