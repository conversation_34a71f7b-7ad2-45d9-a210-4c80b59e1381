package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * <AUTHOR>
 * @.belongToModule 收付费—应收应付
 * @date 2015年5月21日 下午2:01:04
 * @description 应收应付授权表
 */
public class PayeeBO extends BaseBO {

    /** 
    * @Fields serialVersionUID : * 序列号
     */ 
    private static final long serialVersionUID = 1L;

    


    @Override
    public String getBizId() {
        return "com.nci.tunan.cap.interfaces.model.bo.PayeeBO";
    }
    
    /** 
    * @Fields unitNumber : 应收付业务流水标识 
    */ 
    private String unitNumber;
    /** 
    * @Fields payeeName : 姓名
    */ 
    private String payeeName;
    /** 
    * @Fields payeeGender : 性别
    */ 
    private int payeeGender;
    /** 
    * @Fields payeeCertType : 证件类型 关联到证件类型表
    */ 
    private String payeeCertType;
    /** 
    * @Fields payeeCertiCode : 证件号码
    */ 
    private String payeeCertiCode;
    /** 
    * @Fields payeeCertStarDate : 证件有效期起期
    */ 
    private Date payeeCertStarDate;
    /** 
    * @Fields payeeCertEndDate : 证件有效期止期
    */ 
    private Date payeeCertEndDate;
    /** 
    * @Fields countryCode : 国籍 关联到国家代码
    */ 
    private String countryCode;
    /** 
    * @Fields jobCode : 职业代码，关联到职业代码表
    */ 
    private String jobCode;
    /** 
    * @Fields phone : 联系电话
    */ 
    private String phone;
    /** 
    * @Fields state : 州、省
    */ 
    private String state;
    /** 
    * @Fields city : 城市名称
    */ 
    private String city;
    /** 
    * @Fields district : 区县 
    */ 
    private String district;
    /** 
    * @Fields address : 地址 
    */ 
    private String address;
    
    /** 
     * @Fields payeeCertEndDate : 出生日期
     */ 
    private Date payeeBirthday;
    
    /**
     * @Fields billId : 票据流水号
     */
    private BigDecimal billId;

    public BigDecimal getBillId() {
		return billId;
	}
	public void setBillId(BigDecimal billId) {
		this.billId = billId;
	}
	public Date getPayeeBirthday() {
        return payeeBirthday;
    }
    public void setPayeeBirthday(Date payeeBirthday) {
        this.payeeBirthday = payeeBirthday;
    }
    public String getUnitNumber() {
        return unitNumber;
    }
    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }
    public String getPayeeName() {
        return payeeName;
    }
    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }
    public int getPayeeGender() {
        return payeeGender;
    }
    public void setPayeeGender(int payeeGender) {
        this.payeeGender = payeeGender;
    }
    public String getPayeeCertType() {
        return payeeCertType;
    }
    public void setPayeeCertType(String payeeCertType) {
        this.payeeCertType = payeeCertType;
    }
    public String getPayeeCertiCode() {
        return payeeCertiCode;
    }
    public void setPayeeCertiCode(String payeeCertiCode) {
        this.payeeCertiCode = payeeCertiCode;
    }
    public Date getPayeeCertStarDate() {
        return payeeCertStarDate;
    }
    public void setPayeeCertStarDate(Date payeeCertStarDate) {
        this.payeeCertStarDate = payeeCertStarDate;
    }
    public Date getPayeeCertEndDate() {
        return payeeCertEndDate;
    }
    public void setPayeeCertEndDate(Date payeeCertEndDate) {
        this.payeeCertEndDate = payeeCertEndDate;
    }
    public String getCountryCode() {
        return countryCode;
    }
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    public String getJobCode() {
        return jobCode;
    }
    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }
    public String getPhone() {
        return phone;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getState() {
        return state;
    }
    public void setState(String state) {
        this.state = state;
    }
    public String getCity() {
        return city;
    }
    public void setCity(String city) {
        this.city = city;
    }
    public String getDistrict() {
        return district;
    }
    public void setDistrict(String district) {
        this.district = district;
    }
    public String getAddress() {
        return address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    @Override
    public String toString() {
        return "PayeeBO [unitNumber=" + unitNumber + ", payeeName=" + payeeName + ", payeeGender=" + payeeGender
                + ", payeeCertType=" + payeeCertType + ", payeeCertiCode=" + payeeCertiCode + ", payeeCertStarDate="
                + payeeCertStarDate + ", payeeCertEndDate=" + payeeCertEndDate + ", countryCode=" + countryCode
                + ", jobCode=" + jobCode + ", phone=" + phone + ", state=" + state + ", city=" + city + ", district="
                + district + ", address=" + address + "]";
    }
    
}
