package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 日志bo对象
 * <AUTHOR>
 * @date 2015-05-21 18:06:25
 * @.belongToModule 收付费-日志
 */
public class GlInfErrorLogBO extends BaseBO {
    
    /** 
    * 序列号
    */ 
    
    private static final long serialVersionUID = -4717583947691480302L;
    /**
     * 错误日志流水号
     */
    private BigDecimal logId;
    /**
     *  汇总ID
     */
    private BigDecimal groupId;
    /**
     * 数据ID
     */
    private BigDecimal sourceId;
    /**
     * 错误描述
     */
    private String description;
    /**
     * 数据表
     */
    private String sourceTable;

    public void setLogId(BigDecimal logId) {
        this.logId = logId;
    }

    public BigDecimal getLogId() {
        return logId;
    }

    public void setGroupId(BigDecimal groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getGroupId() {
        return groupId;
    }

    public void setSourceId(BigDecimal sourceId) {
        this.sourceId = sourceId;
    }

    public BigDecimal getSourceId() {
        return sourceId;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public String getSourceTable() {
        return sourceTable;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "GlInfErrorLogBO [" + "logId=" + logId + ", groupId=" + groupId + ", sourceId=" + sourceId + ","
                + "description=" + description + ", sourceTable=" + sourceTable + "]";
    }
}
