package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * @description 分公司盘文件OrganBankTextBO对象
 * <AUTHOR>
 * @date 2015-10-14 16:36:47
 * @.belongToModule 收付费-分公司制返盘
 */
public class OrganBankTextBO extends BaseVO {
    /** 
    * @Fields serialVersionUID : 序列化版本号
    */ 
    
    private static final long serialVersionUID = 3592457776499277819L;
    /**
     * @Fields backTextSuccAcc : 返盘成功金额
     */
    private BigDecimal backTextSuccAcc;
    /**
     * @Fields createTime : 报盘文档生成时间
     */
    private Date createTime;
    /**
     * @Fields discNo : 流水号 收付费生成，唯一，区分老核心
     */
    private String discNo;
    /**
     * @Fields batchTaskTime : 批任务日期
     */
    private Date batchTaskTime;
    /**
     * @Fields pcrtEnd : 预制盘结束时间
     */
    private Date pcrtEnd;
    /**
     * @Fields textPath : 制盘文件路径
     */
    private String textPath;
    /**
     * @Fields sendId : 流水号，无业务含义
     */
    private BigDecimal sendId;
    /**
     * @Fields backTextFailAcc : 返盘失败金额
     */
    private BigDecimal backTextFailAcc;
    /**
     * @Fields channelType : 销售渠道类型
     */
    private String channelType;
    /**
     * @Fields sendCount : 报盘总记录数
     */
    private BigDecimal sendCount;
    /**
     * @Fields batchTask : 批处理任务配置
     */
    private BigDecimal batchTask;
    /**
     * @Fields backTextTime : 返盘时间
     */
    private Date backTextTime;
    /**
     * @Fields batchTaskNum : 批处理任务号
     */
    private BigDecimal batchTaskNum;
    /**
     * @Fields backText : 返盘电子文档
     */
    private BigDecimal backText;
    /**
     * @Fields backTextFailNum : 返盘失败条数
     */
    private BigDecimal backTextFailNum;
    /**
     * @Fields downloads : 报盘下载次数
     */
    private BigDecimal downloads;
    /**
     * @Fields backerId : 返盘人员ID
     */
    private BigDecimal backerId;
    /**
     * @Fields groupNum : 盘文件组号
     */
    private String groupNum;
    /**
     * @Fields textStatus : 盘文件状态 2 预制盘成功 3 预制盘回退 5 制盘失败 6 制盘成功 7 返盘成功 8 返盘失败 9
     *         制盘下载成功 10 返盘上传成功
     */
    private String bankTextStatus;
    /**
     * @Fields sendAmount : 报盘总金额
     */
    private BigDecimal sendAmount;
    /**
     * @Fields uploadTime : 返盘上载时间
     */
    private Date uploadTime;
    /**
     * @Fields backTextSuccNum : 返盘成功条数
     */
    private BigDecimal backTextSuccNum;
    /**
     * @Fields backFileName : 返盘文件名
     */
    private String backFileName;
    /**
     * @Fields bussinessType : 业务类型
     */
    private String businessType;
    /**
     * @Fields mainId : 序列号
     */
    private BigDecimal mainId;
    /**
     * @Fields fileName : 唯一，作为资金系统的来源批号。
     */
    private String fileName;
    /**
     * @Fields sendTextMd : MD5摘要
     */
    private String sendTextMd;
    /**
     * @Fields actualCapOrgId : 实际收付费机构
     */
    private String actualCapOrgId;
    /**
     * @Fields arapFlag : 应收应付类型 0;非应收/应付 1;应收 2;应付
     */
    private String arapFlag;
    /**
     * @Fields branchCode : 分公司
     */
    private String branchCode;
    /**
     * @Fields backTextPath : 返盘文件路径
     */
    private String backTextPath;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields senderId : 报盘人员ID
     */
    private BigDecimal senderId;
    /**
     * @Fields insertTime : 插入时间
     */
    private Date  insertTime;

    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;

    /** 
    * @Fields organName : 管理机构名称
    */ 
    private String organName;
    /** 
    * @Fields bankName : 银行名称
    */ 
    private String bankName;
    
    /**
     * @Field crtstart 制盘开始日期
     */
    private Date crtstart;
    /**
     * @Field crtstart 制盘结束日期
     */
    private Date crtend;
    
    public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public void setBackTextSuccAcc(BigDecimal backTextSuccAcc) {
        this.backTextSuccAcc = backTextSuccAcc;
    }

    public BigDecimal getBackTextSuccAcc() {
        return backTextSuccAcc;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setDiscNo(String discNo) {
        this.discNo = discNo;
    }

    public String getDiscNo() {
        return discNo;
    }

    public void setBatchTaskTime(Date batchTaskTime) {
        this.batchTaskTime = batchTaskTime;
    }

    public Date getBatchTaskTime() {
        return batchTaskTime;
    }

    public void setPcrtEnd(Date pcrtEnd) {
        this.pcrtEnd = pcrtEnd;
    }

    public Date getPcrtEnd() {
        return pcrtEnd;
    }

    public void setTextPath(String textPath) {
        this.textPath = textPath;
    }

    public String getTextPath() {
        return textPath;
    }

    public void setSendId(BigDecimal sendId) {
        this.sendId = sendId;
    }

    public BigDecimal getSendId() {
        return sendId;
    }

    public void setBackTextFailAcc(BigDecimal backTextFailAcc) {
        this.backTextFailAcc = backTextFailAcc;
    }

    public BigDecimal getBackTextFailAcc() {
        return backTextFailAcc;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setSendCount(BigDecimal sendCount) {
        this.sendCount = sendCount;
    }

    public BigDecimal getSendCount() {
        return sendCount;
    }

    public void setBatchTask(BigDecimal batchTask) {
        this.batchTask = batchTask;
    }

    public BigDecimal getBatchTask() {
        return batchTask;
    }

    public void setBackTextTime(Date backTextTime) {
        this.backTextTime = backTextTime;
    }

    public Date getBackTextTime() {
        return backTextTime;
    }

    public void setBatchTaskNum(BigDecimal batchTaskNum) {
        this.batchTaskNum = batchTaskNum;
    }

    public BigDecimal getBatchTaskNum() {
        return batchTaskNum;
    }

    public void setBackText(BigDecimal backText) {
        this.backText = backText;
    }

    public BigDecimal getBackText() {
        return backText;
    }

    public void setBackTextFailNum(BigDecimal backTextFailNum) {
        this.backTextFailNum = backTextFailNum;
    }

    public BigDecimal getBackTextFailNum() {
        return backTextFailNum;
    }

    public void setDownloads(BigDecimal downloads) {
        this.downloads = downloads;
    }

    public BigDecimal getDownloads() {
        return downloads;
    }

    public void setBackerId(BigDecimal backerId) {
        this.backerId = backerId;
    }

    public BigDecimal getBackerId() {
        return backerId;
    }

    public void setGroupNum(String groupNum) {
        this.groupNum = groupNum;
    }

    public String getGroupNum() {
        return groupNum;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    public void setBankTextStatus(String bankTextStatus) {
        this.bankTextStatus = bankTextStatus;
    }

    public void setSendAmount(BigDecimal sendAmount) {
        this.sendAmount = sendAmount;
    }

    public BigDecimal getSendAmount() {
        return sendAmount;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setBackTextSuccNum(BigDecimal backTextSuccNum) {
        this.backTextSuccNum = backTextSuccNum;
    }

    public BigDecimal getBackTextSuccNum() {
        return backTextSuccNum;
    }

    public void setBackFileName(String backFileName) {
        this.backFileName = backFileName;
    }

    public String getBackFileName() {
        return backFileName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public void setMainId(BigDecimal mainId) {
        this.mainId = mainId;
    }

    public BigDecimal getMainId() {
        return mainId;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setSendTextMd(String sendTextMd) {
        this.sendTextMd = sendTextMd;
    }

    public String getSendTextMd() {
        return sendTextMd;
    }

    public void setActualCapOrgId(String actualCapOrgId) {
        this.actualCapOrgId = actualCapOrgId;
    }

    public String getActualCapOrgId() {
        return actualCapOrgId;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBackTextPath(String backTextPath) {
        this.backTextPath = backTextPath;
    }

    public String getBackTextPath() {
        return backTextPath;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setSenderId(BigDecimal senderId) {
        this.senderId = senderId;
    }

    public BigDecimal getSenderId() {
        return senderId;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }
    
    public Date getCrtstart() {
		return crtstart;
	}

	public void setCrtstart(Date crtstart) {
		this.crtstart = crtstart;
	}

	public Date getCrtend() {
		return crtend;
	}

	public void setCrtend(Date crtend) {
		this.crtend = crtend;
	}

	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "OrganBankTextBO [backTextSuccAcc=" + backTextSuccAcc
				+ ", createTime=" + createTime + ", discNo=" + discNo
				+ ", batchTaskTime=" + batchTaskTime + ", pcrtEnd=" + pcrtEnd
				+ ", textPath=" + textPath + ", sendId=" + sendId
				+ ", backTextFailAcc=" + backTextFailAcc + ", channelType="
				+ channelType + ", sendCount=" + sendCount + ", batchTask="
				+ batchTask + ", backTextTime=" + backTextTime
				+ ", batchTaskNum=" + batchTaskNum + ", backText=" + backText
				+ ", backTextFailNum=" + backTextFailNum + ", downloads="
				+ downloads + ", backerId=" + backerId + ", groupNum="
				+ groupNum + ", bankTextStatus=" + bankTextStatus
				+ ", sendAmount=" + sendAmount + ", uploadTime=" + uploadTime
				+ ", backTextSuccNum=" + backTextSuccNum + ", backFileName="
				+ backFileName + ", businessType=" + businessType + ", mainId="
				+ mainId + ", fileName=" + fileName + ", sendTextMd="
				+ sendTextMd + ", actualCapOrgId=" + actualCapOrgId
				+ ", arapFlag=" + arapFlag + ", branchCode=" + branchCode
				+ ", backTextPath=" + backTextPath + ", bankCode=" + bankCode
				+ ", senderId=" + senderId + ", insertTime=" + insertTime
				+ ", organCode=" + organCode + ", organName=" + organName
				+ ", bankName=" + bankName + ", crtstart=" + crtstart
				+ ", crtend=" + crtend + "]";
	}

}
