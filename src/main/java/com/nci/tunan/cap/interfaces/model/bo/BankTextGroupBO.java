package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 制返盘记录表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-06-24 20:32:22
 */
public class BankTextGroupBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 7985277111324930719L;
    /**
     * @Fields taskMark : 制返盘进行时，提高效率 收付费操作，无具体业务意义
     */
    private BigDecimal taskMark;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;
    /**
     * @Fields payeeName : 收付款人姓名
     */
    private String payeeName;
    /**
     * @Fields bankAccount : 银行账号
     */
    private String bankAccount;
    /**
     * @Fields accountId : 帐号ID
     */
    private BigDecimal accountId;
    /**
     * @Fields isWriteCash : 是否转实收实付标志
     */
    private BigDecimal isWriteCash;
    /**
     * @Fields payeePhone : 收付费人手机号
     */
    private String payeePhone;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields seqNo : 制盘明细主键
     */
    private BigDecimal seqNo;
    /**
     * @Fields channelType : 个人、银行、团险等
     */
    private String channelType;
    /**
     * @Fields isBankText : 是否允许制盘
     */
    private BigDecimal isBankText;
    /**
     * @Fields certiCode : 收付款人证件号码
     */
    private String certiCode;
    /**
     * @Fields tempFeeAmount : 暂收费金额
     */
    private BigDecimal tempFeeAmount;
    /**
     * @Fields feeAmount : 应收付金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields bankFeeAmount : 报盘金额
     */
    private BigDecimal bankFeeAmount;
    /**
     * @Fields groupId : 流水号，无业务规则
     */
    private BigDecimal groupId;
    /**
     * @Fields customerId : 收付款人 ID
     */
    private BigDecimal customerId;
    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;
    /**
     * @Fields derivType : 业务来源 1;契约 2;保全 3;续保 4;数据迁移 5;其他
     */
    private String derivType;
    /**
     * @Fields completionDate : 交易完成日期
     */
    private Date completionDate;
    /**
     * @Fields bankFlag : 制盘重复数据标识
     */
    private String bankFlag;
    /**
     * @Fields arapFlag : 应收付标识
     */
    private String arapFlag;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields businessType : 业务类型 1;新单暂收费退费 2;溢交退费 3;出单前退费 4;续期暂收费退费 5;续期回退
     *         参考列表
     */
    private String businessType;
    /**
     * @Fields bankTextStatus : 制盘状态 1;预制盘 2;预制盘回退 3;制盘 4;返盘成功 5;返盘失败
     */
    private String bankTextStatus;
    /**
     * @Fields certiType : 收付款人证件类型
     */
    private String certiType;

    /** 
    * @Fields bankTextType : 制盘类型
    */ 
    private String bankTextType;

    public void setTaskMark(BigDecimal taskMark) {
        this.taskMark = taskMark;
    }

    public BigDecimal getTaskMark() {
        return taskMark;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setAccountId(BigDecimal accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getAccountId() {
        return accountId;
    }

    public void setIsWriteCash(BigDecimal isWriteCash) {
        this.isWriteCash = isWriteCash;
    }

    public BigDecimal getIsWriteCash() {
        return isWriteCash;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = payeePhone;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setSeqNo(BigDecimal seqNo) {
        this.seqNo = seqNo;
    }

    public BigDecimal getSeqNo() {
        return seqNo;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setIsBankText(BigDecimal isBankText) {
        this.isBankText = isBankText;
    }

    public BigDecimal getIsBankText() {
        return isBankText;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setTempFeeAmount(BigDecimal tempFeeAmount) {
        this.tempFeeAmount = tempFeeAmount;
    }

    public BigDecimal getTempFeeAmount() {
        return tempFeeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setBankFeeAmount(BigDecimal bankFeeAmount) {
        this.bankFeeAmount = bankFeeAmount;
    }

    public BigDecimal getBankFeeAmount() {
        return bankFeeAmount;
    }

    public void setGroupId(BigDecimal groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getGroupId() {
        return groupId;
    }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setCompletionDate(Date completionDate) {
        this.completionDate = completionDate;
    }

    public Date getCompletionDate() {
        return completionDate;
    }

    public void setBankFlag(String bankFlag) {
        this.bankFlag = bankFlag;
    }

    public String getBankFlag() {
        return bankFlag;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankTextStatus(String bankTextStatus) {
        this.bankTextStatus = bankTextStatus;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public String getBankTextType() {
        return bankTextType;
    }

    public void setBankTextType(String bankTextType) {
        this.bankTextType = bankTextType;
    }

    @Override
    public String toString() {
        return "BankTextGroupBO [taskMark=" + taskMark + ", businessCode=" + businessCode + ", moneyCode=" + moneyCode
                + ", payeeName=" + payeeName + ", bankAccount=" + bankAccount + ", accountId=" + accountId
                + ", isWriteCash=" + isWriteCash + ", payeePhone=" + payeePhone + ", unitNumber=" + unitNumber
                + ", seqNo=" + seqNo + ", channelType=" + channelType + ", isBankText=" + isBankText + ", certiCode="
                + certiCode + ", tempFeeAmount=" + tempFeeAmount + ", feeAmount=" + feeAmount + ", bankFeeAmount="
                + bankFeeAmount + ", groupId=" + groupId + ", customerId=" + customerId + ", bankUserName="
                + bankUserName + ", derivType=" + derivType + ", completionDate=" + completionDate + ", bankFlag="
                + bankFlag + ", arapFlag=" + arapFlag + ", branchCode=" + branchCode + ", bankCode=" + bankCode
                + ", businessType=" + businessType + ", bankTextStatus=" + bankTextStatus + ", certiType=" + certiType
                + "]";
    }
}
