package com.nci.tunan.cap.interfaces.model.bo;

import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description ReportQueryBO对象
 * @<NAME_EMAIL>
 * @date 2016-2-22 下午3:11:11
 * @.belongToModule 收付费-日结报表
 */
public class ReportQueryBO extends BaseBO {
    /**
     * @Fields serialVersionUID : SUID
     */
    private static final long serialVersionUID = 0L;
    
    /** 
    * @Fields reportNo : 报表编号
    */ 
    private String reportNo;
    /** 
     * @Fields reportNo : 报表名称
     */ 
    private String reportName;

    /**
     * @Fields startDate : 查询开始日期
     */
    private Date startDate;
    /**
     * @Fields endDate : 查询结束日期
     */
    private Date endDate;
    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;
    /**
     * @Fields organName : 管理机构名称
     */
    private String organName;
    /**
     * @Fields capOrganCode : 收付机构
     */
    private String capOrganCode;
    /**
     * @Fields capOrganName : 收付机构名称
     */
    private String capOrganName;
    /**
     * @Fields servicerName : 支付商名称     
     */    
    private String servicerName;
    /**
     * @Fields servicerName : 网络销售
     */
    private String networkSales;
    
    /**
     * @Fields servicerName : 裕福卡
     */
    private String yfCard;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * 
     * bankName 银行名称
     */
    private String bankName;
    /**
     * 明细标志
     */
    private String detailflag;
	/**
     * 渠道标志
     */
    private String channelFlag;
    
    /** 
    * @Fields isNewFlag : 多借多贷日结查询标记，1：多借多贷查询逻辑，0：原查询逻辑
    */ 
    private String isNewFlag;
    
    
    /** 
    * @Fields xmlFlag : 转换xml标识 0不需要转换，1需要转换
    */ 
    private String xmlFlag;
    
    /** 
     * @Fields fileName : 制表时日结文件名
     */ 
    private String fileName;
      
  /** 
   * @Fields statementPeriod : D-日;M-月
   */ 
    private String statementPeriod;
    
    /** 
    * @Fields sapOrganCode : sap机构码，四位
    */ 
    private String sapOrganCode;
 
    /** 
     * @Fields orgNullFlag : 页面机构是否为空标志
     */ 
      private String orgNullFlag;
   
    
    
    public String getOrgNullFlag() {
       return orgNullFlag;
   }

   public void setOrgNullFlag(String orgNullFlag) {
       this.orgNullFlag = orgNullFlag;
   }   
    public String getSapOrganCode() {
        return sapOrganCode;
    }

    public void setSapOrganCode(String sapOrganCode) {
        this.sapOrganCode = sapOrganCode;
    }

    public String getStatementPeriod() {
        return statementPeriod;
    }

    public void setStatementPeriod(String statementPeriod) {
        this.statementPeriod = statementPeriod;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getXmlFlag() {
        return xmlFlag;
    }

    public void setXmlFlag(String xmlFlag) {
        this.xmlFlag = xmlFlag;
    }

    public String getIsNewFlag() {
        return isNewFlag;
    }

    public void setIsNewFlag(String isNewFlag) {
        this.isNewFlag = isNewFlag;
    }

    public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	@Override
    public String getBizId() {
        return null;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getCapOrganName() {
        return capOrganName;
    }

    public void setCapOrganName(String capOrganName) {
        this.capOrganName = capOrganName;
    }

    public String getServicerName() {
        return servicerName;
    }

    public void setServicerName(String servicerName) {
        this.servicerName = servicerName;
    }

	public String getNetworkSales() {
		return networkSales;
	}

	public void setNetworkSales(String networkSales) {
		this.networkSales = networkSales;
	}

	public String getYfCard() {
		return yfCard;
	}

	public void setYfCard(String yfCard) {
		this.yfCard = yfCard;
	}

	public String getChannelFlag() {
		return channelFlag;
	}

	public void setChannelFlag(String channelFlag) {
		this.channelFlag = channelFlag;
	}
	public String getDetailflag() {
		return detailflag;
	}

	public void setDetailflag(String detailflag) {
		this.detailflag = detailflag;
	}
    
}
