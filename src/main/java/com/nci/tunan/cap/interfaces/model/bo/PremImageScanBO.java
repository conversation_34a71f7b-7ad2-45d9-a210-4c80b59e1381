/**
 * @Fields field:field:{todo}
 */
package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * <AUTHOR>
 * @.belongToModule 收付费 -影像对象VO类
 * @date 2020年7月17日下午3:24:25
 * @description 影像VO类
 */
public class PremImageScanBO extends BaseBO {

	private static final long serialVersionUID = 1L;

	
	/**
	 * 应收付业务流水标识
	 */
	private String unitNumber;
	/**
	 * 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
	 */
	private String businessCode;
	/**
	 * 影像单证分类
	 */
	private String tPremImgType;
	/** 
     * 操作项,补扫或新扫描 0-新扫描； 1-补扫（影像系统传入，核心保存）
     */ 
    private BigDecimal scanOperationType;
    /**
     * 收付标识
     */
    private String payMode;
    
    /** 
    * @Fields scanUserCode : 扫描操作人
    */ 
    private String scanUserCode;
    
    /** 
    * @Fields 扫描时间
    */ 
    private Date scanTime;
    /** 
    * @Fields feeStatus : 收付状态
    */ 
    private String feeStatus;
    
    /** 
    * @Fields applyCode : 投保单号
    */ 
    private String applyCode;
    
    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    
    public String gettPremImgType() {
        return tPremImgType;
    }

    public void settPremImgType(String tPremImgType) {
        this.tPremImgType = tPremImgType;
    }

    public BigDecimal getScanOperationType() {
        return scanOperationType;
    }

    public void setScanOperationType(BigDecimal scanOperationType) {
        this.scanOperationType = scanOperationType;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getScanUserCode() {
        return scanUserCode;
    }

    public void setScanUserCode(String scanUserCode) {
        this.scanUserCode = scanUserCode;
    }

    public Date getScanTime() {
        return scanTime;
    }

    public void setScanTime(Date scanTime) {
        this.scanTime = scanTime;
    }

    @Override
    public String toString() {
        return "PremImageScanVO [unitNumber=" + unitNumber + ", businessCode=" + businessCode + ", tPremImgType="
                + tPremImgType + ", scanOperationType=" + scanOperationType + ", payMode=" + payMode + ", scanUserCode="
                + scanUserCode + ", scanTime=" + scanTime + "]";
    }

    @Override
    public String getBizId() {
        return null;
    }

	
}
