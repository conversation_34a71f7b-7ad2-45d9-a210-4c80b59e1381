package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 资金交易表
 * @<NAME_EMAIL>
 * @date 2016-2-25 上午10:15:23
 * @.belongToModule CAP-收付费系统
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
public class FmsInterfaceBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 1L;
    /** 
     * @Fields paySendTime : 收付标发起时间
     */ 
    private Date paySendTime;
    /** 
     * @Fields arapFlag : 收付标志 
     */ 
    private String arapFlag;
    private String feeStatus;
    private String businessCode;
    /**
     * @Fields manageCom : 管理费区间
     */
    private String manageCom;
    /**
     * @Fields corpBankCode : 银行编码
     */
    private String corpBankCode;
    /**
     * @Fields userCode : 操作员代码
     */
    private String userCode;
    /**
     * @Fields corpBankName : 银行名称
     */
    private String corpBankName;
    /**
     * @Fields inReserved1 : 保留字段
     */
    private String inReserved1;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields idType : 证件类型
     */
    private String idType;
    /**
     * @Fields remark : 备注
     */
    private String remark;
    /**
     * @Fields status : 盘文件状态2 预制盘成功3 预制盘回退5 制盘失败6 制盘成功7 返盘成功8 返盘失败9 制盘下载成功10 返盘上传成功
     */
    private String status;
    /**
     * @Fields accountType : 账号类型
     */
    private String accountType;
    /**
     * @Fields certifyNo : 单证号码，投保单号/保单号/通知书号/保全受理号等
     */
    private String certifyNo;
    /**
     * @Fields enterAccDate : 到账日期，扣款成功的日期
     */
    private Date enterAccDate;
    /**
     * @Fields returnMsg : 扣款结果描述，收费结果描述: 成功-收费成功,失败-具体失败原因
     */
    private String returnMsg;
    /**
     * @Fields outReserved1 : 预留字段
     */
    private String outReserved1;
    /**
     * @Fields inReserved3 : 预留字段3
     */
    private String inReserved3;
    /**
     * @Fields returnCode : 扣款结果，银行扣款失败,银行扣款成功
     */
    private String returnCode;
    /**
     * @Fields outReserved2 : 预留字段2
     */
    private String outReserved2;
    /**
     * @Fields listId : 序号
     */
    private BigDecimal listId;
    /**
     * @Fields inReserved2 : 预留字段
     */
    private String inReserved2;
    /**
     * @Fields outReserved3 : 预留字段
     */
    private String outReserved3;
    /**
     * @Fields accountNo : 账户
     */
    private String accountNo;
    /**
     * @Fields certifyType : 单证类型，信用卡，借记卡
     */
    private String certifyType;
    /**
     * @Fields premium : 金额
     */
    private BigDecimal premium;
    /**
     * @Fields accountName : 账户名称
     */
    private String accountName;
    /**
     * @Fields custUserId : 用户编码
     */
    private String custUserId;
    /**
     * @Fields servicerCode : 服务商代码
     */
    private String servicerCode;
    /**
     * @Fields idNo : 证件号码
     */
    private String idNo;
    /**
     * @Fields currency : 货币
     */
    private String currency;
    /**
     * @Fields payMode : 支付方式
     */
    private String payMode;
    /**
     * @Fields tradeSn : 流水号，批次内流水号，服务商自定义
     */
    private String tradeSn;
    /**
     * @Fields batchId : 唯一批次号，每笔交易的唯一批次号
     */
    private String batchId;
    /**
     * @Fields mobilePhone : 手机号
     */
    private String mobilePhone;
    /**
     * @Fields corpAccount : 账户
     */
    private String corpAccount;
    /**
     * @Fields verifyFlag : 证明类型
     */
    private String verifyFlag;
    /**
     * @Fields bankCode : 银行代码
     */
    private String bankCode;
    /**
     * @Fields enterAccTime : 到账时间，扣款成功的时间
     */
    private Date enterAccTime;
    /**
     * @Fields transSource : 结论
     */
    private String transSource;
    /**
     * @Fields merchantId : 商户代码，服务商分配给新华的商户代码
     */
    private String merchantId;
	/** 
	* @Fields startDate : 对账起始日期（实际： 到账时间，扣款成功的时间）
	*/ 
	private String startDate;
	/** 
	* @Fields endDate : 对账截止日期（实际： 到账时间，扣款成功的时间）
	*/ 
	private String endDate;
	/** 
	* @Fields chargeTypeList :  收费类型集合
	*/ 
	private List<String> chargeTypeList;
	/** 
	* @Fields servicerCodeList : 支付商类型集合 
	*/ 
	private List<String> servicerCodeList;
	/** 
	* @Fields remarks : 备注
	*/ 
	private String remarks;
	/** 
	* @Fields organName : 管理机构 
	*/ 
	private String organName;

	/** 
	* @Fields flag : 标识表值  
	*/ 
	private String flag;
	/** 
	* @Fields chargeType : 收费类型 
	*/ 
	private String chargeType;
	/** 
     * @Fields chargeType :  pos终端号码
	 */
	private String terminalCode;
	/** 
	  * @Fields platform : 平台来源
	  */ 
	private String submitChannel;
	
	/**
     * @Fields payAgain : 是否再次请求扣费，1-是、0（或空）-否
     */
    private String payAgain;
    
    /**
     * @Fields Abstract : 对账码（申请单编号）
     */
    private String Abstract;
    
    /**
     * @Fields failPayAgin : 付费失败再次支付标识
     */
    private String failPayAgin;
    
    /**
     * @Fields openId : 客户支付ID
     */
    private String openId;
    
    /**
     * @Fields noteCode : 交易流水号
     */
    private String noteCode;
    
    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;

	public String getDerivType() {
		return derivType;
	}

	public void setDerivType(String derivType) {
		this.derivType = derivType;
	}

	public String getNoteCode() {
        return noteCode;
    }

    public void setNoteCode(String noteCode) {
        this.noteCode = noteCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getAbstract() {
		return Abstract;
	}

	public void setAbstract(String Abstract) {
		this.Abstract = Abstract;
	}

	public String getPayAgain() {
        return payAgain;
    }

    public void setPayAgain(String payAgain) {
        this.payAgain = payAgain;
    }

    public String getSubmitChannel() {
        return submitChannel;
    }

    public void setSubmitChannel(String submitChannel) {
        this.submitChannel = submitChannel;
    }

    public Date getPaySendTime() {
		return paySendTime;
	}

	public void setPaySendTime(Date paySendTime) {
		this.paySendTime = paySendTime;
	}

	public String getArapFlag() {
        return arapFlag;
    }
    
    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }
    
    public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getTerminalCode() {
		return terminalCode;
	}

	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	public void setManageCom(String manageCom) {
        this.manageCom = manageCom;
    }

    public String getManageCom() {
        return manageCom;
    }

    public void setCorpBankCode(String corpBankCode) {
        this.corpBankCode = corpBankCode;
    }

    public String getCorpBankCode() {
        return corpBankCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setCorpBankName(String corpBankName) {
        this.corpBankName = corpBankName;
    }

    public String getCorpBankName() {
        return corpBankName;
    }

    public void setInReserved1(String inReserved1) {
        this.inReserved1 = inReserved1;
    }

    public String getInReserved1() {
        return inReserved1;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdType() {
        return idType;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setCertifyNo(String certifyNo) {
        this.certifyNo = certifyNo;
    }

    public String getCertifyNo() {
        return certifyNo;
    }

    public void setEnterAccDate(Date enterAccDate) {
        this.enterAccDate = enterAccDate;
    }

    public Date getEnterAccDate() {
        return enterAccDate;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setOutReserved1(String outReserved1) {
        this.outReserved1 = outReserved1;
    }

    public String getOutReserved1() {
        return outReserved1;
    }

    public void setInReserved3(String inReserved3) {
        this.inReserved3 = inReserved3;
    }

    public String getInReserved3() {
        return inReserved3;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setOutReserved2(String outReserved2) {
        this.outReserved2 = outReserved2;
    }

    public String getOutReserved2() {
        return outReserved2;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setInReserved2(String inReserved2) {
        this.inReserved2 = inReserved2;
    }

    public String getInReserved2() {
        return inReserved2;
    }

    public void setOutReserved3(String outReserved3) {
        this.outReserved3 = outReserved3;
    }

    public String getOutReserved3() {
        return outReserved3;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setCertifyType(String certifyType) {
        this.certifyType = certifyType;
    }

    public String getCertifyType() {
        return certifyType;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setCustUserId(String custUserId) {
        this.custUserId = custUserId;
    }

    public String getCustUserId() {
        return custUserId;
    }

    public void setServicerCode(String servicerCode) {
        this.servicerCode = servicerCode;
    }

    public String getServicerCode() {
        return servicerCode;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public String getTradeSn() {
        return tradeSn;
    }

    public void setTradeSn(String tradeSn) {
        this.tradeSn = tradeSn;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setCorpAccount(String corpAccount) {
        this.corpAccount = corpAccount;
    }

    public String getCorpAccount() {
        return corpAccount;
    }

    public void setVerifyFlag(String verifyFlag) {
        this.verifyFlag = verifyFlag;
    }

    public String getVerifyFlag() {
        return verifyFlag;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setEnterAccTime(Date enterAccTime) {
        this.enterAccTime = enterAccTime;
    }

    public Date getEnterAccTime() {
        return enterAccTime;
    }

    public void setTransSource(String transSource) {
        this.transSource = transSource;
    }

    public String getTransSource() {
        return transSource;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    @Override
    public String getBizId() {
        return null;
    }
    
    public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public List<String> getChargeTypeList() {
		return chargeTypeList;
	}

	public void setChargeTypeList(List<String> chargeTypeList) {
		this.chargeTypeList = chargeTypeList;
	}

	public List<String> getServicerCodeList() {
		return servicerCodeList;
	}

	public void setServicerCodeList(List<String> servicerCodeList) {
		this.servicerCodeList = servicerCodeList;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}
	
	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}
	
	public String getChargeType() {
		return chargeType;
	}

	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}
    
	public String getFailPayAgin() {
		return failPayAgin;
	}

	public void setFailPayAgin(String failPayAgin) {
		this.failPayAgin = failPayAgin;
	}
	
	@Override
	public String toString() {
		return "FmsInterfaceBO [paySendTime=" + paySendTime + ", arapFlag="
				+ arapFlag + ", feeStatus=" + feeStatus + ", businessCode="
				+ businessCode + ", manageCom=" + manageCom + ", corpBankCode="
				+ corpBankCode + ", userCode=" + userCode + ", corpBankName="
				+ corpBankName + ", inReserved1=" + inReserved1
				+ ", unitNumber=" + unitNumber + ", idType=" + idType
				+ ", remark=" + remark + ", status=" + status
				+ ", accountType=" + accountType + ", certifyNo=" + certifyNo
				+ ", enterAccDate=" + enterAccDate + ", returnMsg=" + returnMsg
				+ ", outReserved1=" + outReserved1 + ", inReserved3="
				+ inReserved3 + ", returnCode=" + returnCode
				+ ", outReserved2=" + outReserved2 + ", listId=" + listId
				+ ", inReserved2=" + inReserved2 + ", outReserved3="
				+ outReserved3 + ", accountNo=" + accountNo + ", certifyType="
				+ certifyType + ", premium=" + premium + ", accountName="
				+ accountName + ", custUserId=" + custUserId
				+ ", servicerCode=" + servicerCode + ", idNo=" + idNo
				+ ", currency=" + currency + ", payMode=" + payMode
				+ ", tradeSn=" + tradeSn + ", batchId=" + batchId
				+ ", mobilePhone=" + mobilePhone + ", corpAccount="
				+ corpAccount + ", verifyFlag=" + verifyFlag + ", bankCode="
				+ bankCode + ", enterAccTime=" + enterAccTime
				+ ", transSource=" + transSource + ", merchantId=" + merchantId
				+ ", startDate=" + startDate + ", endDate=" + endDate
				+ ", chargeTypeList=" + chargeTypeList + ", servicerCodeList="
				+ servicerCodeList + ", remarks=" + remarks + ", organName="
				+ organName + ", flag=" + flag + ", chargeType=" + chargeType
				+ ", terminalCode=" + terminalCode + ", submitChannel="
				+ submitChannel + ", payAgain=" + payAgain + ", Abstract="
				+ Abstract + ", failPayAgin=" + failPayAgin + "]";
	}

}
