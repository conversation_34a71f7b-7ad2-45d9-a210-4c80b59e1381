package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 客户账户退款信息BO
 * @<NAME_EMAIL>
 * @date 2015-11-6 下午2:16:58
 * @.belongToModule 收付费-客户暂存
 */
public class CusAccRefundBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 序列化版本号
     */
    private static final long serialVersionUID = 1L;
    /**
     * @Fields moneyCode : 币种代码
     */
    private String moneyCode;
    /**
     * @Fields cusAccRefund : 客户账户退费流水号
     */
    private BigDecimal cusAccRefund;
    /**
     * @Fields bankAccount : 银行账号
     */
    private String bankAccount;
    /**
     * @Fields seqNo : 盘记录流水号
     */
    private BigDecimal seqNo;
    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;
    /**
     * @Fields dueTime : 应缴应付日
     */
    private Date dueTime;
    /**
     * @Fields feeAmount : 客户账户取款金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields cusAccUnitNo : 客户账户应收付流水标识
     */
    private String cusAccUnitNo;
    /**
     * @Fields groupId : 制返盘记录表ID
     */
    private BigDecimal groupId;
    /**
     * @Fields failTimes : 失败次数
     */
    private BigDecimal failTimes;
    /**
     * @Fields bankUserName : 户名
     */
    private String bankUserName;
    /**
     * @Fields rtnCode : 银行返回代码
     */
    private String rtnCode;
    /**
     * @Fields cusAccDetailsId : 客户账户交易明细ID
     */
    private BigDecimal cusAccDetailsId;
    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;
    /**
     * @Fields payMode : 收付方式
     */
    private String payMode;
    /**
     * @Fields arapFlag : 应收付标识
     */
    private String arapFlag;
    /**
     * @Fields customerAccountId : 客户账户ID
     */
    private BigDecimal customerAccountId;
    /**
     * @Fields bankCode : 银行编码
     */
    private String bankCode;
    /**
     * @Fields branchCode : 分公司代码
     */
    private String branchCode;
    /**
     * @Fields bankTextStatus : 制盘状态
     */
    private String bankTextStatus;

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setCusAccRefund(BigDecimal cusAccRefund) {
        this.cusAccRefund = cusAccRefund;
    }

    public BigDecimal getCusAccRefund() {
        return cusAccRefund;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setSeqNo(BigDecimal seqNo) {
        this.seqNo = seqNo;
    }

    public BigDecimal getSeqNo() {
        return seqNo;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setCusAccUnitNo(String cusAccUnitNo) {
        this.cusAccUnitNo = cusAccUnitNo;
    }

    public String getCusAccUnitNo() {
        return cusAccUnitNo;
    }

    public void setGroupId(BigDecimal groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getGroupId() {
        return groupId;
    }

    public void setFailTimes(BigDecimal failTimes) {
        this.failTimes = failTimes;
    }

    public BigDecimal getFailTimes() {
        return failTimes;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    public String getBankUserName() {
        return bankUserName;
    }

    public void setRtnCode(String rtnCode) {
        this.rtnCode = rtnCode;
    }

    public String getRtnCode() {
        return rtnCode;
    }

    public void setCusAccDetailsId(BigDecimal cusAccDetailsId) {
        this.cusAccDetailsId = cusAccDetailsId;
    }

    public BigDecimal getCusAccDetailsId() {
        return cusAccDetailsId;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setCustomerAccountId(BigDecimal customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public BigDecimal getCustomerAccountId() {
        return customerAccountId;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBankTextStatus(String bankTextStatus) {
        this.bankTextStatus = bankTextStatus;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "CusAccRefundBO [" + "moneyCode=" + moneyCode + "," + "cusAccRefund=" + cusAccRefund + ","
                + "bankAccount=" + bankAccount + "," + "seqNo=" + seqNo + "," + "organCode=" + organCode + ","
                + "dueTime=" + dueTime + "," + "feeAmount=" + feeAmount + "," + "cusAccUnitNo=" + cusAccUnitNo + ","
                + "groupId=" + groupId + "," + "failTimes=" + failTimes + "," + "bankUserName=" + bankUserName + ","
                + "rtnCode=" + rtnCode + "," + "cusAccDetailsId=" + cusAccDetailsId + "," + "derivType=" + derivType
                + "," + "payMode=" + payMode + "," + "arapFlag=" + arapFlag + "," + "customerAccountId="
                + customerAccountId + "," + "bankCode=" + bankCode + "," + "branchCode=" + branchCode + ","
                + "bankTextStatus=" + bankTextStatus + "]";
    }
}
