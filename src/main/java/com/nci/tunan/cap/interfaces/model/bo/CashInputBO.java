package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class CashInputBO extends BaseBO{
    @Override
    public String getBizId() {
        return null;
    }
    
    /** 
     * @Fields serialVersionUID : 序列化
     */ 
    private static final long serialVersionUID = 2014847328626558888L;
    /** 
     * @Fields serialVersionUID : 流水号
     */ 
    private BigDecimal listId;
    /**
     * @Fields tempNo : 暂收据号码TEMP_NO
     */
    private String tempNo;
    /**
     * @Fields status : 现金状态:00-待复核、01-待推送确认、02-已推送确认STATUS
     */
    private String status;
    /**
     * @Fields reviewUser : 复核人REVIEW_USER
     */
    private BigDecimal reviewUser;
    /**
     * @Fields reviewTime : 复核时间REVIEW_TIME
     */
    private Date reviewTime;
    /**
     * @Fields comfirmBy : 确认人COMFIRM_BY
     */
    private BigDecimal comfirmBy;
    /**
     * @Fields comfirmDate : 确认日期COMFIRM_DATE
     */
    private Date comfirmDate;
    /**
     * @Fields arapFlag : 收付标识ARAP_FLAG
     */
    private String arapFlag;
    /**
     * @Fields cashDate : 现金存现日期CASH_DATE
     */
    private Date cashDate;
    /**
     * @Fields finComfirmDate : 确认日期FIN_COMFIRM_DATE
     */
    private Date finComfirmDate;
    /**
     * @Fields insertBy : 记录插入人INSERT_BY
     */
    private BigDecimal insertBy;
    /**
     * @Fields insertBy : 记录更新人UPDATE_BY
     */
    private BigDecimal updateBy;
    /**
     * @Fields insertTime : 记录插入时间INSERT_TIME
     */
    private Date insertTime;
    /**
     * @Fields insertTime : 记录更新时间UPDATE_TIME
     */
    private Date updateTime;
    /**
     * @Fields insertTimestamp : 记录插入时间戳INSERT_TIMESTAMP，默认值SYSDATE
     */
    private Date insertTimestamp;
    /**
     * @Fields updateTimestamp : 记录更新时间戳UPDATE_TIMESTAMP，默认值SYSDATE
     */
    private Date updateTimestamp;
    
    
    public BigDecimal getListId() {
        return listId;
    }
    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }
    public String getTempNo() {
        return tempNo;
    }
    public void setTempNo(String tempNo) {
        this.tempNo = tempNo;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public BigDecimal getReviewUser() {
        return reviewUser;
    }
    public void setReviewUser(BigDecimal reviewUser) {
        this.reviewUser = reviewUser;
    }
    public Date getReviewTime() {
        return reviewTime;
    }
    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }
    public BigDecimal getComfirmBy() {
        return comfirmBy;
    }
    public void setComfirmBy(BigDecimal comfirmBy) {
        this.comfirmBy = comfirmBy;
    }
    public Date getComfirmDate() {
        return comfirmDate;
    }
    public void setComfirmDate(Date comfirmDate) {
        this.comfirmDate = comfirmDate;
    }
    public String getArapFlag() {
        return arapFlag;
    }
    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }
    public Date getCashDate() {
        return cashDate;
    }
    public void setCashDate(Date cashDate) {
        this.cashDate = cashDate;
    }
    public Date getFinComfirmDate() {
        return finComfirmDate;
    }
    public void setFinComfirmDate(Date finComfirmDate) {
        this.finComfirmDate = finComfirmDate;
    }
    public BigDecimal getInsertBy() {
        return insertBy;
    }
    public void setInsertBy(BigDecimal insertBy) {
        this.insertBy = insertBy;
    }
    public BigDecimal getUpdateBy() {
        return updateBy;
    }
    public void setUpdateBy(BigDecimal updateBy) {
        this.updateBy = updateBy;
    }
    public Date getInsertTime() {
        return insertTime;
    }
    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public Date getInsertTimestamp() {
        return insertTimestamp;
    }
    public void setInsertTimestamp(Date insertTimestamp) {
        this.insertTimestamp = insertTimestamp;
    }
    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }
    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
    
    @Override
    public String toString() {
        return "CashInputBO [listId=" + listId + ", tempNo=" + tempNo + ", status=" + status + ", reviewUser="
                + reviewUser + ", reviewTime=" + reviewTime + ", comfirmBy=" + comfirmBy + ", comfirmDate="
                + comfirmDate + ", arapFlag=" + arapFlag + ", cashDate=" + cashDate + ", finComfirmDate="
                + finComfirmDate + ", insertBy=" + insertBy + ", updateBy=" + updateBy + ", insertTime=" + insertTime
                + ", updateTime=" + updateTime + ", insertTimestamp=" + insertTimestamp + ", updateTimestamp="
                + updateTimestamp + "]";
    }
}
