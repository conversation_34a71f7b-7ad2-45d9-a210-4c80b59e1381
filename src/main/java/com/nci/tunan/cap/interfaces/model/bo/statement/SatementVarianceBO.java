package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.math.BigDecimal;
import java.util.List;

import com.nci.udmp.framework.model.BaseVO;

/** 
 * @description SatementVarianceVO对象
 * <AUTHOR> 
 * @date 2023-12-28 14:55:04  
 */
public class SatementVarianceBO extends BaseVO {	
	 /** 
    * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
    */ 
    
    private static final long serialVersionUID = 1L;
    /** 
	* @Fields businessCode :  涉及业务号
 	*/ 
	private String businessCode;
	 /** 
	* @Fields errDesc :  差错详细描述
 	*/ 
	private String errDesc;
			 /** 
	* @Fields busiProdCode :  涉及险种
 	*/ 
	private String busiProdCode;
			 /** 
	* @Fields serialNum :  序号
 	*/ 
	private BigDecimal serialNum;
	 /** 
	* @Fields listId :  主键序列
 	*/ 
	private BigDecimal listId;
    /** 
	* @Fields feeAmount :  日结金额
 	*/ 
	private BigDecimal feeAmount;
			 /** 
	* @Fields statementId :  日结ID
 	*/ 
	private BigDecimal statementId;
	 /** 
	* @Fields actAmount :  实际业务金额
 	*/ 
	private BigDecimal actAmount;
	/** 
     * @Fields actAmount :  操作标识
    */ 
    private String operateFlag;

    /** 
     * @Fields actAmount :  下载标识
    */ 
    private String downloadFlag;
    
    /** 
     * @Fields fileName :  日结文件名称
     */ 
     private String fileName;
	
     /**
      *  差异说明集合
      */
    private List<SatementVarianceBO> satementVarianceVOList;


    public List<SatementVarianceBO> getSatementVarianceVOList() {
        return satementVarianceVOList;
    }

    public void setSatementVarianceVOList(List<SatementVarianceBO> satementVarianceVOList) {
        this.satementVarianceVOList = satementVarianceVOList;
    }

    public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	
	public String getBusinessCode() {
		return businessCode;
	}
	 public void setErrDesc(String errDesc) {
		this.errDesc = errDesc;
	}
	
	public String getErrDesc() {
		return errDesc;
	}
			 public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}
	
	public String getBusiProdCode() {
		return busiProdCode;
	}
			 public void setSerialNum(BigDecimal serialNum) {
		this.serialNum = serialNum;
	}
	
	public BigDecimal getSerialNum() {
		return serialNum;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}
	
	public BigDecimal getFeeAmount() {
		return feeAmount;
	}
			 public void setStatementId(BigDecimal statementId) {
		this.statementId = statementId;
	}
	
	public BigDecimal getStatementId() {
		return statementId;
	}
	 public void setActAmount(BigDecimal actAmount) {
		this.actAmount = actAmount;
	}
	
	public BigDecimal getActAmount() {
		return actAmount;
	}
	
    public String getOperateFlag() {
        return operateFlag;
    }

    public void setOperateFlag(String operateFlag) {
        this.operateFlag = operateFlag;
    }
    
    public String getDownloadFlag() {
        return downloadFlag;
    }

    public void setDownloadFlag(String downloadFlag) {
        this.downloadFlag = downloadFlag;
    }
    
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "SatementVarianceBO [businessCode=" + businessCode + ", errDesc=" + errDesc + ", busiProdCode="
                + busiProdCode + ", serialNum=" + serialNum + ", listId=" + listId + ", feeAmount=" + feeAmount
                + ", statementId=" + statementId + ", actAmount=" + actAmount + ", operateFlag=" + operateFlag
                + ", downloadFlag=" + downloadFlag + ", fileName=" + fileName + "]";
    }
}
