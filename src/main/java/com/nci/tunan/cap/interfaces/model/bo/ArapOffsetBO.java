package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 冲销明细表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-06-27 15:09:33
 */
public class ArapOffsetBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = -5313967454051997071L;
    /**
     * @Fields offsetAmount : 冲销金额
     */
    private BigDecimal offsetAmount;
    /**
     * @Fields offsetSeqno : 冲销流水号
     */
    private BigDecimal offsetSeqno;

    /**
     * @Fields splitUnitNumber : 拆分应收应付流水标识号
     */
    private String splitUnitNumber;
    /**
     * @Fields offsetUnitNumber : 冲销应收应付流水标识号
     */
    private String offsetUnitNumber;
    /**
     * @Fields offsetStatus : 1;未冲销 2;已冲销 3;取消 4;预先冲销
     */
    private String offsetStatus;

    public void setOffsetSeqno(BigDecimal offsetSeqno) {
        this.offsetSeqno = offsetSeqno;
    }

    public BigDecimal getOffsetSeqno() {
        return offsetSeqno;
    }

    public void setOffsetAmount(BigDecimal offsetAmount) {
        this.offsetAmount = offsetAmount;
    }

    public BigDecimal getOffsetAmount() {
        return offsetAmount;
    }

    public void setSplitUnitNumber(String splitUnitNumber) {
        this.splitUnitNumber = splitUnitNumber;
    }

    public String getSplitUnitNumber() {
        return splitUnitNumber;
    }

    public void setOffsetUnitNumber(String offsetUnitNumber) {
        this.offsetUnitNumber = offsetUnitNumber;
    }

    public String getOffsetUnitNumber() {
        return offsetUnitNumber;
    }

    public void setOffsetStatus(String offsetStatus) {
        this.offsetStatus = offsetStatus;
    }

    public String getOffsetStatus() {
        return offsetStatus;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ArapOffsetVO [" + "offsetSeqno=" + offsetSeqno + ", offsetAmount=" + offsetAmount
                + ", splitUnitNumber=" + splitUnitNumber + ", offsetUnitNumber=" + offsetUnitNumber + ","
                + "offsetStatus=" + offsetStatus + "]";
    }
}
