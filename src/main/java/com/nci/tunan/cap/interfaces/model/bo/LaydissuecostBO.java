package com.nci.tunan.cap.interfaces.model.bo;

import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description LaydissuecostBO对象
 * <AUTHOR>
 * @date 2022-08-26 15:44:56
 */
public class LaydissuecostBO extends BaseBO {
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields jbdel : 加保是否包含非实时业务
	 */
	private String jbdel;
	/**
	 * @Fields modifytime : 修改时间
	 */
	private String modifytime;
	/**
	 * @Fields agreementcode : 协议编码
	 */
	private String agreementcode;
	/**
	 * @Fields exaninestate : 审核状态
	 */
	private String exaninestate;
	/**
	 * @Fields operator : 操作员
	 */
	private String operator;
	/**
	 * @Fields maketime : 入机时间
	 */
	private String maketime;
	/**
	 * @Fields startdate : 协议有效起期
	 */
	private Date startdate;
	/**
	 * @Fields yyqtbissuecost : 犹豫期退保出单费用
	 */
	private String yyqtbissuecost;
	/**
	 * @Fields xqdel : 续期是否包含非实时业务
	 */
	private String xqdel;
	/**
	 * @Fields enddate : 协议有效止期
	 */
	private Date enddate;
	/**
	 * @Fields xqyissuecos : 新契约出单费用
	 */
	private String xqyissuecost;
	/**
	 * @Fields xqmaxyear : 续期最大保单年度
	 */
	private String xqmaxyear;
	/**
	 * @Fields yyqtbdel : 犹豫期退保是否包含非实时业务
	 */
	private String yyqtbdel;
	/**
	 * @Fields managecom : 管理机构
	 */
	private String managecom;
	/**
	 * @Fields bankcode : 银行渠道
	 */
	private String bankcode;
	/**
	 * @Fields applydate : 用印申请日期
	 */
	private Date applydate;
	/**
	 * @Fields zbfdel : 追加保费是否包含非实时业务
	 */
	private String zbfdel;
	/**
	 * @Fields makedate : 入机日期
	 */
	private Date makedate;
	/**
	 * @Fields f1 : 备用字段1
	 */
	private String f1;
	/**
	 * @Fields f5 : 备用字段5
	 */
	private Date f5;
	/**
	 * @Fields f4 : 备用字段4
	 */
	private Date f4;
	/**
	 * @Fields f3 : 备用字段3
	 */
	private String f3;
	/**
	 * @Fields f2 : 备用字段2
	 */
	private String f2;
	/**
	 * @Fields xqissuecost : 续期出单费用
	 */
	private String xqissuecost;
	/**
	 * @Fields yyqnjbissuecost : 犹豫期内减保出单费用
	 */
	private String yyqnjbissuecost;
	/**
	 * @Fields xqydel : 新契约是否包含非实时业务
	 */
	private String xqydel;
	/**
	 * @Fields jbissuecost : 加保出单费用
	 */
	private String jbissuecost;
	/**
	 * @Fields yyqnjbdel : 犹豫期内减保是否包含非实时业务
	 */
	private String yyqnjbdel;
	/**
	 * @Fields zjbfissuecost : 追加保费出单费用
	 */
	private String zjbfissuecost;
	/**
	 * @Fields modifydate : 修改日期
	 */
	private Date modifydate;

	public void setJbdel(String jbdel) {
		this.jbdel = jbdel;
	}

	public String getJbdel() {
		return jbdel;
	}

	public void setModifytime(String modifytime) {
		this.modifytime = modifytime;
	}

	public String getModifytime() {
		return modifytime;
	}

	public void setAgreementcode(String agreementcode) {
		this.agreementcode = agreementcode;
	}

	public String getAgreementcode() {
		return agreementcode;
	}

	public void setExaninestate(String exaninestate) {
		this.exaninestate = exaninestate;
	}

	public String getExaninestate() {
		return exaninestate;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getOperator() {
		return operator;
	}

	public void setMaketime(String maketime) {
		this.maketime = maketime;
	}

	public String getMaketime() {
		return maketime;
	}

	public void setStartdate(Date startdate) {
		this.startdate = startdate;
	}

	public Date getStartdate() {
		return startdate;
	}

	public void setYyqtbissuecost(String yyqtbissuecost) {
		this.yyqtbissuecost = yyqtbissuecost;
	}

	public String getYyqtbissuecost() {
		return yyqtbissuecost;
	}

	public void setXqdel(String xqdel) {
		this.xqdel = xqdel;
	}

	public String getXqdel() {
		return xqdel;
	}

	public void setEnddate(Date enddate) {
		this.enddate = enddate;
	}

	public Date getEnddate() {
		return enddate;
	}

	public void setXqyissuecost(String xqyissuecost) {
		this.xqyissuecost = xqyissuecost;
	}

	public String getXqyissuecost() {
		return xqyissuecost;
	}

	public void setXqmaxyear(String xqmaxyear) {
		this.xqmaxyear = xqmaxyear;
	}

	public String getXqmaxyear() {
		return xqmaxyear;
	}

	public void setYyqtbdel(String yyqtbdel) {
		this.yyqtbdel = yyqtbdel;
	}

	public String getYyqtbdel() {
		return yyqtbdel;
	}

	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}

	public String getManagecom() {
		return managecom;
	}

	public void setBankcode(String bankcode) {
		this.bankcode = bankcode;
	}

	public String getBankcode() {
		return bankcode;
	}

	public void setApplydate(Date applydate) {
		this.applydate = applydate;
	}

	public Date getApplydate() {
		return applydate;
	}

	public void setZbfdel(String zbfdel) {
		this.zbfdel = zbfdel;
	}

	public String getZbfdel() {
		return zbfdel;
	}

	public void setMakedate(Date makedate) {
		this.makedate = makedate;
	}

	public Date getMakedate() {
		return makedate;
	}

	public void setF1(String f1) {
		this.f1 = f1;
	}

	public String getF1() {
		return f1;
	}

	public void setF5(Date f5) {
		this.f5 = f5;
	}

	public Date getF5() {
		return f5;
	}

	public void setF4(Date f4) {
		this.f4 = f4;
	}

	public Date getF4() {
		return f4;
	}

	public void setF3(String f3) {
		this.f3 = f3;
	}

	public String getF3() {
		return f3;
	}

	public void setF2(String f2) {
		this.f2 = f2;
	}

	public String getF2() {
		return f2;
	}

	public void setXqissuecost(String xqissuecost) {
		this.xqissuecost = xqissuecost;
	}

	public String getXqissuecost() {
		return xqissuecost;
	}

	public void setYyqnjbissuecost(String yyqnjbissuecost) {
		this.yyqnjbissuecost = yyqnjbissuecost;
	}

	public String getYyqnjbissuecost() {
		return yyqnjbissuecost;
	}

	public void setXqydel(String xqydel) {
		this.xqydel = xqydel;
	}

	public String getXqydel() {
		return xqydel;
	}

	public void setJbissuecost(String jbissuecost) {
		this.jbissuecost = jbissuecost;
	}

	public String getJbissuecost() {
		return jbissuecost;
	}

	public void setYyqnjbdel(String yyqnjbdel) {
		this.yyqnjbdel = yyqnjbdel;
	}

	public String getYyqnjbdel() {
		return yyqnjbdel;
	}

	public void setZjbfissuecost(String zjbfissuecost) {
		this.zjbfissuecost = zjbfissuecost;
	}

	public String getZjbfissuecost() {
		return zjbfissuecost;
	}

	public void setModifydate(Date modifydate) {
		this.modifydate = modifydate;
	}

	public Date getModifydate() {
		return modifydate;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "LaydissuecostBO [" + "jbdel=" + jbdel + "," + "modifytime="
				+ modifytime + "," + "agreementcode=" + agreementcode + ","
				+ "exaninestate=" + exaninestate + "," + "operator=" + operator
				+ "," + "maketime=" + maketime + "," + "startdate=" + startdate
				+ "," + "yyqtbissuecost=" + yyqtbissuecost + "," + "xqdel="
				+ xqdel + "," + "enddate=" + enddate + "," + "xqyissuecost="
				+ xqyissuecost + "," + "xqmaxyear=" + xqmaxyear + ","
				+ "yyqtbdel=" + yyqtbdel + "," + "managecom=" + managecom + ","
				+ "bankcode=" + bankcode + "," + "applydate=" + applydate + ","
				+ "zbfdel=" + zbfdel + "," + "makedate=" + makedate + ","
				+ "f1=" + f1 + "," + "f5=" + f5 + "," + "f4=" + f4 + ","
				+ "f3=" + f3 + "," + "f2=" + f2 + "," + "xqissuecost="
				+ xqissuecost + "," + "yyqnjbissuecost=" + yyqnjbissuecost
				+ "," + "xqydel=" + xqydel + "," + "jbissuecost=" + jbissuecost
				+ "," + "yyqnjbdel=" + yyqnjbdel + "," + "zjbfissuecost="
				+ zjbfissuecost + "," + "modifydate=" + modifydate + "]";
	}
}
