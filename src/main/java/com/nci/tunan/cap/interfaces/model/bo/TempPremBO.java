package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class TempPremBO extends BaseBO {

	/**
	 * 序列
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 流水号
	 */
	private BigDecimal tempfeeLogID;
	public BigDecimal getTempfeeLogID() {
		return tempfeeLogID;
	}

	public void setTempfeeLogID(BigDecimal tempfeeLogID) {
		this.tempfeeLogID = tempfeeLogID;
	}

	/**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     * @Fields businessCode : 契约投保单号
     */
    private String applyCode;
    /**
     * @Fields applyType : 申请类型
     */
    private String applyType;
    /**
     * @Fields businessCode :收费方式
     */
    private String payMode;
    /**
     * @Fields arapDate 收付费日期
     */
    private Date arapDate;
    /**
     * @Fields feeAmount 收费金额
     */
    private BigDecimal feeAmount;
    /** 
     * @Fields bankName : 银行名称 
     */ 
    private String bankName;
    /**
     * @Fields payMode : 票据类型
     */
    private String billMode;

    /**
     * @Fields payMode : 支付类型
     */
    private String oldPayMode;

    /**
     * @Fields feeStatus : 费用状态
     */
    private String feeStatus;
    /**
     * @Fields chequeNoOld : 变更前支票号
     */
    private String chequeNoOld;
    /**
     * @Fields chequeNoOld : 变更后支票号
     */
    private String chequeNoNew;
    /**
     * @Fields invoiceDateOld : 变更前支票日期
     */
    private Date invoiceDateOld;
    /**
     * @Fields invoiceDateOld : 变更后支票日期
     */
    private Date invoiceDateNew;
    /**
     * @Fields cheque_company_old : 变更前开票单位
     */
    private String chequeCompanyOld;
    /**
     * @Fields cheque_company_old : 变更后开票单位
     */
    private String chequeCompanyNew;
    /**
     * @Fields chequeEndDateOld : 变更前支票有效期
     */
    private Date chequeEndDateOld;
    /**
     * @Fields chequeEndDateNew : 变更后支票有效期
     */
    private Date chequeEndDateNew;
    /**
     * @Fields  bankCodeOld ： 变更前银行编码
     */
    private String bankCodeOld;
    /**
     * @Fields  bankCodeOld ： 变更后银行编码
     */
    private String bankCodeNew;
    /**
     * @Fields  bankCodeOld ： 变更后银行名称
     */
    private String bankNameOld;
    /**
     * @Fields  bankCodeOld ： 变更后银行名称
     */
    private String bankNameNew;
    /**
    * @Fields  bankUserNameOld ： 变更前户名
    */
    private String bankUserNameOld;
    /**
     * @Fields  bankUserNameNew ： 变更后户名
     */
    private String bankUserNameNew;
    /**
     * @Fields bankAccountOld : 变更前客户账号
     */
    private String bankAccountOld;
    /**
     * @Fields bankAccountNew : 变更后客户账号
     */
    private String bankAccountNew;
    /**
     * @Fields applyUser : 申请人
     */
    private BigDecimal applyUser;
    /**
     * @Fields applyDate : 申请日期
     */
    private Date applyDate;
    /**
     * @Fields checkUser : 审核人
     */
    private BigDecimal checkUser;
    /**
     * @Fields checkDate : 审核日期
     */
    private Date checkDate;
    /**
     * @Fields checkStatus : 审核状态
     */
    private String checkStatus;
    /**
     * @Fields checkResult : 审核结论
     */
    private String checkResult;
    /**
     * @Fields rejectReason : 不通过原因
     */
    private String rejectReason;
    /**
     * @Fields applyReason : 变更申请原因代码
     */
    private String applyReason;
    /**
     * @Fields scanFlag : 是否上传影响记录 0—未上传，1—已上传
     */
    private BigDecimal scanFlag;
    /**
     * @Fields scanFlag : 是否上传影响记录 0—未上传，1—已上传
     */
    private String remark;
    /**
     * @Fields chequeNo : 票据号
     */
    private String chequeNo;
    
    /**
     * @Fields bankcode : 开户银行
     */
    private String bankCode;
    /**
     * @Fields bankAccount : 银行账号
     */
    private String bankAccount;
    /**
     * @Fields bankUserName : 银行账号
     */
    private String bankUserName;
    /**
     * @Fields arStartDate : 收費錄入起始日期
     */
    private Date arStartDate;
    /**
     * @Fields arStartDate : 收費錄入結束日期
     */
    private Date arEndDate;
    
    /** 
     * @Fields corpActionOld :  原企业方账户
     */ 
     private String corpActionOld;
     
     /** 
      * @Fields corpActionNew :  修改后企业方账户
      */ 
     private String corpActionNew;
     
     /** 
     * @Fields companyBankCodeOld : 原企业方开户行编码
     */ 
     private String companyBankCodeOld;
     /** 
     * @Fields companyBankCodeNew : 修改后企业方开户行编码
     */ 
     private String companyBankCodeNew;
     /**
      * @Fields scanFlag : 银保通脱机单标识  是:1
      */
     private BigDecimal ybtOfflineFlag;
     /**
      * @Fields scanFlag : 银保通脱机单标识  新
      */
     private BigDecimal ybtOfflineFlagNew;
     /**
      * @Fields scanFlag : 银保通脱机单标识  旧
      */
     private BigDecimal ybtOfflineFlagOld;
     /**
      * @Fields billId : 票据流水号
      */
     private BigDecimal billId;
     
     public BigDecimal getBillId() {
		return billId;
	}

	public void setBillId(BigDecimal billId) {
		this.billId = billId;
	}

	public BigDecimal getYbtOfflineFlagNew() {
		return ybtOfflineFlagNew;
	}

	public void setYbtOfflineFlagNew(BigDecimal ybtOfflineFlagNew) {
		this.ybtOfflineFlagNew = ybtOfflineFlagNew;
	}

	public BigDecimal getYbtOfflineFlagOld() {
		return ybtOfflineFlagOld;
	}

	public void setYbtOfflineFlagOld(BigDecimal ybtOfflineFlagOld) {
		this.ybtOfflineFlagOld = ybtOfflineFlagOld;
	}

	public BigDecimal getYbtOfflineFlag() {
 		return ybtOfflineFlag;
 	}


 	public void setYbtOfflineFlag(BigDecimal ybtOfflineFlag) {
 		this.ybtOfflineFlag = ybtOfflineFlag;
 	}
     

	public String getCorpActionOld() {
        return corpActionOld;
    }

    public void setCorpActionOld(String corpActionOld) {
        this.corpActionOld = corpActionOld;
    }

    public String getCorpActionNew() {
        return corpActionNew;
    }

    public void setCorpActionNew(String corpActionNew) {
        this.corpActionNew = corpActionNew;
    }

    public String getCompanyBankCodeOld() {
        return companyBankCodeOld;
    }

    public void setCompanyBankCodeOld(String companyBankCodeOld) {
        this.companyBankCodeOld = companyBankCodeOld;
    }

    public String getCompanyBankCodeNew() {
        return companyBankCodeNew;
    }

    public void setCompanyBankCodeNew(String companyBankCodeNew) {
        this.companyBankCodeNew = companyBankCodeNew;
    }

    public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getApplyType() {
		return applyType;
	}

	public void setApplyType(String applyType) {
		this.applyType = applyType;
	}

	public String getPayMode() {
		return payMode;
	}

	public void setPayMode(String payMode) {
		this.payMode = payMode;
	}

	public Date getArapDate() {
		return arapDate;
	}

	public void setArapDate(Date arapDate) {
		this.arapDate = arapDate;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBillMode() {
		return billMode;
	}

	public void setBillMode(String billMode) {
		this.billMode = billMode;
	}

	public String getOldPayMode() {
		return oldPayMode;
	}

	public void setOldPayMode(String oldPayMode) {
		this.oldPayMode = oldPayMode;
	}

	public String getFeeStatus() {
		return feeStatus;
	}

	public void setFeeStatus(String feeStatus) {
		this.feeStatus = feeStatus;
	}

	public String getChequeNoOld() {
		return chequeNoOld;
	}

	public void setChequeNoOld(String chequeNoOld) {
		this.chequeNoOld = chequeNoOld;
	}

	public String getChequeNoNew() {
		return chequeNoNew;
	}

	public void setChequeNoNew(String chequeNoNew) {
		this.chequeNoNew = chequeNoNew;
	}

	public Date getInvoiceDateOld() {
		return invoiceDateOld;
	}

	public void setInvoiceDateOld(Date invoiceDateOld) {
		this.invoiceDateOld = invoiceDateOld;
	}

	public Date getInvoiceDateNew() {
		return invoiceDateNew;
	}

	public void setInvoiceDateNew(Date invoiceDateNew) {
		this.invoiceDateNew = invoiceDateNew;
	}

	public String getChequeCompanyOld() {
		return chequeCompanyOld;
	}

	public void setChequeCompanyOld(String chequeCompanyOld) {
		this.chequeCompanyOld = chequeCompanyOld;
	}

	public String getChequeCompanyNew() {
		return chequeCompanyNew;
	}

	public void setChequeCompanyNew(String chequeCompanyNew) {
		this.chequeCompanyNew = chequeCompanyNew;
	}

	public Date getChequeEndDateOld() {
		return chequeEndDateOld;
	}

	public void setChequeEndDateOld(Date chequeEndDateOld) {
		this.chequeEndDateOld = chequeEndDateOld;
	}

	public Date getChequeEndDateNew() {
		return chequeEndDateNew;
	}

	public void setChequeEndDateNew(Date chequeEndDateNew) {
		this.chequeEndDateNew = chequeEndDateNew;
	}

	public String getBankCodeOld() {
		return bankCodeOld;
	}

	public void setBankCodeOld(String bankCodeOld) {
		this.bankCodeOld = bankCodeOld;
	}

	public String getBankCodeNew() {
		return bankCodeNew;
	}

	public void setBankCodeNew(String bankCodeNew) {
		this.bankCodeNew = bankCodeNew;
	}

	public String getBankNameOld() {
		return bankNameOld;
	}

	public void setBankNameOld(String bankNameOld) {
		this.bankNameOld = bankNameOld;
	}

	public String getBankNameNew() {
		return bankNameNew;
	}

	public void setBankNameNew(String bankNameNew) {
		this.bankNameNew = bankNameNew;
	}

	public String getBankUserNameOld() {
		return bankUserNameOld;
	}

	public void setBankUserNameOld(String bankUserNameOld) {
		this.bankUserNameOld = bankUserNameOld;
	}

	public String getBankUserNameNew() {
		return bankUserNameNew;
	}

	public void setBankUserNameNew(String bankUserNameNew) {
		this.bankUserNameNew = bankUserNameNew;
	}

	public String getBankAccountOld() {
		return bankAccountOld;
	}

	public void setBankAccountOld(String bankAccountOld) {
		this.bankAccountOld = bankAccountOld;
	}

	public String getBankAccountNew() {
		return bankAccountNew;
	}

	public void setBankAccountNew(String bankAccountNew) {
		this.bankAccountNew = bankAccountNew;
	}

	public BigDecimal getApplyUser() {
		return applyUser;
	}

	public void setApplyUser(BigDecimal applyUser) {
		this.applyUser = applyUser;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public BigDecimal getCheckUser() {
		return checkUser;
	}

	public void setCheckUser(BigDecimal checkUser) {
		this.checkUser = checkUser;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public String getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(String checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getCheckResult() {
		return checkResult;
	}

	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}

	public String getRejectReason() {
		return rejectReason;
	}

	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}

	public String getApplyReason() {
		return applyReason;
	}

	public void setApplyReason(String applyReason) {
		this.applyReason = applyReason;
	}

	public BigDecimal getScanFlag() {
		return scanFlag;
	}

	public void setScanFlag(BigDecimal scanFlag) {
		this.scanFlag = scanFlag;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getChequeNo() {
		return chequeNo;
	}

	public void setChequeNo(String chequeNo) {
		this.chequeNo = chequeNo;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}

	public String getBankUserName() {
		return bankUserName;
	}

	public void setBankUserName(String bankUserName) {
		this.bankUserName = bankUserName;
	}
	public String getUnitNumber() {
		return unitNumber;
	}

	public Date getArStartDate() {
		return arStartDate;
	}

	public void setArStartDate(Date arStartDate) {
		this.arStartDate = arStartDate;
	}

	public Date getArEndDate() {
		return arEndDate;
	}

	public void setArEndDate(Date arEndDate) {
		this.arEndDate = arEndDate;
	}
	@Override
	public String toString() {
		return "TempPremBO [tempfeeLogID=" + tempfeeLogID + ", unitNumber="
				+ unitNumber + ", businessCode=" + businessCode
				+ ", applyCode=" + applyCode + ", applyType=" + applyType
				+ ", payMode=" + payMode + ", arapDate=" + arapDate
				+ ", feeAmount=" + feeAmount + ", bankName=" + bankName
				+ ", billMode=" + billMode + ", oldPayMode=" + oldPayMode
				+ ", feeStatus=" + feeStatus + ", chequeNoOld=" + chequeNoOld
				+ ", chequeNoNew=" + chequeNoNew + ", invoiceDateOld="
				+ invoiceDateOld + ", invoiceDateNew=" + invoiceDateNew
				+ ", chequeCompanyOld=" + chequeCompanyOld
				+ ", chequeCompanyNew=" + chequeCompanyNew
				+ ", chequeEndDateOld=" + chequeEndDateOld
				+ ", chequeEndDateNew=" + chequeEndDateNew + ", bankCodeOld="
				+ bankCodeOld + ", bankCodeNew=" + bankCodeNew
				+ ", bankNameOld=" + bankNameOld + ", bankNameNew="
				+ bankNameNew + ", bankUserNameOld=" + bankUserNameOld
				+ ", bankUserNameNew=" + bankUserNameNew + ", bankAccountOld="
				+ bankAccountOld + ", bankAccountNew=" + bankAccountNew
				+ ", applyUser=" + applyUser + ", applyDate=" + applyDate
				+ ", checkUser=" + checkUser + ", checkDate=" + checkDate
				+ ", checkStatus=" + checkStatus + ", checkResult="
				+ checkResult + ", rejectReason=" + rejectReason
				+ ", applyReason=" + applyReason + ", scanFlag=" + scanFlag
				+ ", remark=" + remark + ", chequeNo=" + chequeNo
				+ ", bankCode=" + bankCode + ", bankAccount=" + bankAccount
				+ ", bankUserName=" + bankUserName + ", arStartDate="
				+ arStartDate + ", arEndDate=" + arEndDate + "]";
	}

	@Override
	public String getBizId() {
		return null;
	}

}
