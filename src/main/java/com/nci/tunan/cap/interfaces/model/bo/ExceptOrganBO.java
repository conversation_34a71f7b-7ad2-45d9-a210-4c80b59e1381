package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 例外机构BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class ExceptOrganBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 2554183397326770695L;
    /**
     * @Fields organCode : 管理机构代码
     */
    private String organCode;

    /**
     * @Fields diskConfigId : 参数ID
     */
    private BigDecimal diskConfigId;
    /**
     * @Fields organName : 管理机构名称
     */
    private String organName;
    /**
     * @Fields exceptOrganId : 例外ID
     */
    private BigDecimal exceptOrganId;

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getOrganName() {
        return organName;
    }

    public void setDiskConfigId(BigDecimal diskConfigId) {
        this.diskConfigId = diskConfigId;
    }

    public BigDecimal getDiskConfigId() {
        return diskConfigId;
    }

    public void setExceptOrganId(BigDecimal exceptOrganId) {
        this.exceptOrganId = exceptOrganId;
    }

    public BigDecimal getExceptOrganId() {
        return exceptOrganId;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ExceptOrganBO [" + "organCode=" + organCode + ", organName=" + organName + ", diskConfigId="
                + diskConfigId + ", exceptOrganId=" + exceptOrganId + "]";
    }
}
