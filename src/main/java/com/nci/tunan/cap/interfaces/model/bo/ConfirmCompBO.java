package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 到账确认BO
 * @<NAME_EMAIL>
 * @date 2015-10-27 上午10:38:54
 * @.belongToModule 收付费-到账确认
 */
public class ConfirmCompBO extends BaseBO {

    /**
     * @Fields serialVersionUID : SUID
     */

    private static final long serialVersionUID = 8025681841866178211L;
    
    /** 
     * @Fields billId : 主键Id 
     */ 
    private BigDecimal billId;
    
    /** 
     * @Fields payEndDate : 逾期日期
     */ 
    private Date payEndDate;

    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;

    /**
     * @Fields applyCode : 投保单号码
     */
    private String applyCode;
    /**
     * @Fields businessCode : 业务编码
     */
    private String businessCode;

    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;

    /**
     * @Fields payMode : 支付方式
     */
    private String payMode;

    /**
     * @Fields billMode : 支付方式
     */
    private String billMode;

    /**
     * @Fields arapFlag : 收付标志
     */
    private String arapFlag;

    /**
     * @Fields feeStatus : 收付状态
     */
    private String feeStatus;

    /**
     * @Fields startDate : 收付费起始日期
     */
    private Date startDate;

    /**
     * @Fields endDate : 收付费截止日期
     */
    private Date endDate;

    /**
     * @Fields bankCode : 开票银行代码
     */
    private String bankCode;

    /**
     * @Fields bankName : 开票银行名称
     */
    private String bankName;

    /**
     * @Fields chequeNo : 支票号码
     */
    private String chequeNo;

    /**
     * @Fields chequeCompany : 开票单位
     */
    private String chequeCompany;

    /**
     * @Fields feeAmount : 应收付金额
     */
    private BigDecimal feeAmount;

    /**
     * @Fields billCode : 票据号码
     */
    private String billCode;

    /**
     * @Fields capOrganCode : 收付费机构代码
     */
    private String capOrganCode;

    /**
     * @Fields capOrganName : 收付费机构名称
     */
    private String capOrganName;

    /**
     * @Fields arapFlagAp : 收付费标志为付费
     */
    private String arapFlagAp;

    /**
     * @Fields insertTime : 票据登记时间
     */
    private Date insertTime;

    /**
     * @Fields withdrawType : 业务类型
     */
    private String withdrawType;

    /**
     * @Fields comfirmDate : 确认日期
     */
    private Date comfirmDate;

    /**
     * @Fields tempFeeAmount : 票据明细暂收费金额
     */
    private BigDecimal tempFeeAmount;

    /**
     * @Fields tempFeeAmount : 票据明细金额
     */
    private BigDecimal billAmount;

    /**
     * @Fields returnChequeCode : 退票原因代码
     */
    private BigDecimal returnChequeCode;
    /**
     * @Fields returnChequeReason : 退票原因
     */
    private String returnChequeReason;
    /**
     * @Fields json : 到账确认信息
     */
    private String json;

    /**
     * @Fields payeeName : 收款人/领款人
     */
    private String payeeName;
    
    /** 
    * @Fields handlerName : 经办人姓名
    */ 
    private String handlerName;
    /**
     * @Fields confirmFlag : 到账确认标志
     */
    private String confirmFlag;
    /**
     * @Fields dueTime : 应缴日期
     */
    private Date dueTime;
    /**
     * @Fields businessType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String businessType;
    /**
     * @Fields sumAmout : 合计金额
     */
    private BigDecimal sumAmout;
    /**
     * @Fields cusAccDetailsId : 客户账户交易明细ID
     */
    private BigDecimal cusAccDetailsId;
    /**
     * @Fields payrefno : 实付号
     */
    private String payrefno;
    /** 
    * @Fields status : 票据状态
    */ 
    private String status;
    
    /** 
    * @Fields chequeBanlance : 支票余额
    */ 
    private BigDecimal chequeBanlance;
    
    /** 
     * @Fields  insertBy: 操作员代码
     */ 
    private BigDecimal insertBy;
    
    /** 
     * @Fields  updateBy: 投保人
     */
    private String holderName;
    /**
     * 银行账户
     */
    private String bankAccount;
    /**
     * 账户名
     */
    private String bankUserName;
    
    /** 
     * @Fields companyBankCode : 企业方开户行
     */ 
     private String companyBankCode;
     
     /** 
      * @Fields corpAction : 企业方账号
     */ 
     private String corpAction;
     /** 
      * @Fields payeeName : 姓名
      */ 
    //private String payeeName;
      /** 
      * @Fields payeeGender : 性别
      */ 
      private BigDecimal payeeGender;
      /** 
      * @Fields payeeCertType : 证件类型 关联到证件类型表
      */ 
      private String payeeCertType;
      /** 
      * @Fields payeeCertiCode : 证件号码
      */ 
      private String payeeCertiCode;
      /** 
      * @Fields payeeCertStarDate : 证件有效期起期
      */ 
      private Date payeeCertStarDate;
      /** 
      * @Fields payeeCertEndDate : 证件有效期止期
      */ 
      private Date payeeCertEndDate;
      /** 
      * @Fields countryCode : 国籍 关联到国家代码
      */ 
      private String countryCode;
      /** 
      * @Fields jobCode : 职业代码，关联到职业代码表
      */ 
      private String jobCode;
      /** 
      * @Fields phone : 联系电话
      */ 
      private String phone;
      /** 
      * @Fields state : 州、省
      */ 
      private String state;
      /** 
      * @Fields city : 城市名称
      */ 
      private String city;
      /** 
      * @Fields district : 区县 
      */ 
      private String district;
      /** 
      * @Fields address : 地址 
      */ 
      private String address;
      
      /** 
       * @Fields payeeCertEndDate : 出生日期
       */ 
      private Date payeeBirthday;
      
      /** 
       * @Fields companyName : 法人/单位名称
       */ 
       private String companyName;
       
       /** 
       * @Fields companyAddress : 法人/单位注册地址
       */ 
       private String companyAddress;
       
       /** 
       * @Fields companyBusiScope : 法人/单位经营范围
       */ 
       private String companyBusiScope;
       
       /** 
       * @Fields companyCertiType : 法人/单位证件类型
       */ 
       private String companyCertiType;
       
       /** 
       * @Fields companyCertiCode : 法人/单位证件号码
       */ 
       private String companyCertiCode;
       
       /** 
       * @Fields certiStartDate : 法人/单位证件有效起期
       */ 
       private Date certiStartDate;
       /** 
       * @Fields certiEndDate : 法人/单位证件有效止期
       */ 
       private Date certiEndDate;
       
       /**
        * @Fields payeeType : 交费类型 0:个人 1:法人/单位
       */
       private String payeeType;
       
       /**
        * @Fields btrId : 银行转账单流水号
        */
       private BigDecimal btrId;
       
     
     public String getPayeeType() {
		return payeeType;
	}

	public void setPayeeType(String payeeType) {
		this.payeeType = payeeType;
	}

	public Date getPayeeBirthday() {
		return payeeBirthday;
	}

	public void setPayeeBirthday(Date payeeBirthday) {
		this.payeeBirthday = payeeBirthday;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCompanyAddress() {
		return companyAddress;
	}

	public void setCompanyAddress(String companyAddress) {
		this.companyAddress = companyAddress;
	}

	public String getCompanyBusiScope() {
		return companyBusiScope;
	}

	public void setCompanyBusiScope(String companyBusiScope) {
		this.companyBusiScope = companyBusiScope;
	}

	public String getCompanyCertiType() {
		return companyCertiType;
	}

	public void setCompanyCertiType(String companyCertiType) {
		this.companyCertiType = companyCertiType;
	}

	public String getCompanyCertiCode() {
		return companyCertiCode;
	}

	public void setCompanyCertiCode(String companyCertiCode) {
		this.companyCertiCode = companyCertiCode;
	}

	public Date getCertiStartDate() {
		return certiStartDate;
	}

	public void setCertiStartDate(Date certiStartDate) {
		this.certiStartDate = certiStartDate;
	}

	public Date getCertiEndDate() {
		return certiEndDate;
	}

	public void setCertiEndDate(Date certiEndDate) {
		this.certiEndDate = certiEndDate;
	}

	public String getCorpAction() {
        return corpAction;
    }

    public void setCorpAction(String corpAction) {
        this.corpAction = corpAction;
    }

    public String getCompanyBankCode() {
         return companyBankCode;
     }

     public void setCompanyBankCode(String companyBankCode) {
         this.companyBankCode = companyBankCode;
     }
  
	public String getBankAccount() {
		return bankAccount;
	}

	
	public void setBankAccount(String bankaccount) {
		this.bankAccount = bankaccount;
	}

	public String getBankUserName() {
		return bankUserName;
	}

	public void setBankUserName(String bankUserName) {
		this.bankUserName = bankUserName;
	}

	public BigDecimal getInsertBy() {
		return insertBy;
	}

	public void setInsertBy(BigDecimal insertBy) {
		this.insertBy = insertBy;
	}

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public BigDecimal getBillId() {
        return billId;
    }
    
    public void setBillId(BigDecimal billId) {
        this.billId = billId;
    }

    public Date getPayEndDate() {
        return payEndDate;
    }
    
    public void setPayEndDate(Date payEndDate) {
        this.payEndDate = payEndDate;
    }

    public BigDecimal getChequeBanlance() {
		return chequeBanlance;
	}

	public void setChequeBanlance(BigDecimal chequeBanlance) {
		this.chequeBanlance = chequeBanlance;
	}

	@Override
    public String getBizId() {
        return null;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getBillMode() {
        return billMode;
    }

    public void setBillMode(String billMode) {
        this.billMode = billMode;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(String feeStatus) {
        this.feeStatus = feeStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getChequeCompany() {
        return chequeCompany;
    }

    public void setChequeCompany(String chequeCompany) {
        this.chequeCompany = chequeCompany;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public String getCapOrganName() {
        return capOrganName;
    }

    public void setCapOrganName(String capOrganName) {
        this.capOrganName = capOrganName;
    }

    public String getArapFlagAp() {
        return arapFlagAp;
    }

    public void setArapFlagAp(String arapFlagAp) {
        this.arapFlagAp = arapFlagAp;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    public Date getComfirmDate() {
        return comfirmDate;
    }

    public void setComfirmDate(Date comfirmDate) {
        this.comfirmDate = comfirmDate;
    }

    public BigDecimal getTempFeeAmount() {
        return tempFeeAmount;
    }

    public void setTempFeeAmount(BigDecimal tempFeeAmount) {
        this.tempFeeAmount = tempFeeAmount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getConfirmFlag() {
        return confirmFlag;
    }

    public void setConfirmFlag(String confirmFlag) {
        this.confirmFlag = confirmFlag;
    }

    public BigDecimal getReturnChequeCode() {
        return returnChequeCode;
    }

    public void setReturnChequeCode(BigDecimal returnChequeCode) {
        this.returnChequeCode = returnChequeCode;
    }

    public Date getDueTime() {
        return dueTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public String getReturnChequeReason() {
        return returnChequeReason;
    }

    public void setReturnChequeReason(String returnChequeReason) {
        this.returnChequeReason = returnChequeReason;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getSumAmout() {
        return sumAmout;
    }

    public void setSumAmout(BigDecimal sumAmout) {
        this.sumAmout = sumAmout;
    }

    public BigDecimal getCusAccDetailsId() {
        return cusAccDetailsId;
    }

    public void setCusAccDetailsId(BigDecimal cusAccDetailsId) {
        this.cusAccDetailsId = cusAccDetailsId;
    }

    public String getPayrefno() {
        return payrefno;
    }

    public void setPayrefno(String payrefno) {
        this.payrefno = payrefno;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }


    public String getHandlerName() {
        return handlerName;
    }


    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }
    
    public BigDecimal getPayeeGender() {
		return payeeGender;
	}

	public void setPayeeGender(BigDecimal payeeGender) {
		this.payeeGender = payeeGender;
	}

	public String getPayeeCertType() {
        return payeeCertType;
    }
    public void setPayeeCertType(String payeeCertType) {
        this.payeeCertType = payeeCertType;
    }
    public String getPayeeCertiCode() {
        return payeeCertiCode;
    }
    public void setPayeeCertiCode(String payeeCertiCode) {
        this.payeeCertiCode = payeeCertiCode;
    }
    public Date getPayeeCertStarDate() {
        return payeeCertStarDate;
    }
    public void setPayeeCertStarDate(Date payeeCertStarDate) {
        this.payeeCertStarDate = payeeCertStarDate;
    }
    public Date getPayeeCertEndDate() {
        return payeeCertEndDate;
    }
    public void setPayeeCertEndDate(Date payeeCertEndDate) {
        this.payeeCertEndDate = payeeCertEndDate;
    }
    public String getCountryCode() {
        return countryCode;
    }
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    public String getJobCode() {
        return jobCode;
    }
    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }
    public String getPhone() {
        return phone;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getState() {
        return state;
    }
    public void setState(String state) {
        this.state = state;
    }
    public String getCity() {
        return city;
    }
    public void setCity(String city) {
        this.city = city;
    }
    public String getDistrict() {
        return district;
    }
    public void setDistrict(String district) {
        this.district = district;
    }
    public String getAddress() {
        return address;
    }
    public void setAddress(String address) {
        this.address = address;
    }

	public BigDecimal getBtrId() {
		return btrId;
	}

	public void setBtrId(BigDecimal btrId) {
		this.btrId = btrId;
	}
}
