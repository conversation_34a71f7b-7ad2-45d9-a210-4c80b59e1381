package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 服务回调表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class RecallServiceBO extends BaseBO {
   
    /** 
    * @Fields serialVersionUID : 序列化
    */ 
    
    private static final long serialVersionUID = 1129524663872583331L;
    /**
     * @Fields recallType : 回调类型 01：业务生效 02：业务失效
     */
    private String recallType;
    /**
     * @Fields lastRecallDesc : 最近一次处理结果描述
     */
    private String lastRecallDesc;
    /**
     * @Fields recallTimes : 回调次数
     */
    private BigDecimal recallTimes;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields finishTime : 收付成功时间
     */
    private Date finishTime;
    /**
     * @Fields serviceId : 回调服务ID
     */
    private BigDecimal serviceId;
    /**
     * @Fields status : 0－待处理 1－回调成功 2－回调失败
     */
    private String status;
    /**
     * @Fields seqNo : 制盘明细主键
     */
    private BigDecimal seqNo;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;

    public void setRecallType(String recallType) {
        this.recallType = recallType;
    }

    public String getRecallType() {
        return recallType;
    }

    public void setLastRecallDesc(String lastRecallDesc) {
        this.lastRecallDesc = lastRecallDesc;
    }

    public String getLastRecallDesc() {
        return lastRecallDesc;
    }

    public void setRecallTimes(BigDecimal recallTimes) {
        this.recallTimes = recallTimes;
    }

    public BigDecimal getRecallTimes() {
        return recallTimes;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setServiceId(BigDecimal serviceId) {
        this.serviceId = serviceId;
    }

    public BigDecimal getServiceId() {
        return serviceId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setSeqNo(BigDecimal seqNo) {
        this.seqNo = seqNo;
    }

    public BigDecimal getSeqNo() {
        return seqNo;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "RecallServiceBO [" + "recallType=" + recallType + ", lastRecallDesc=" + lastRecallDesc + ","
                + "recallTimes=" + recallTimes + ", unitNumber=" + unitNumber + ", finishTime=" + finishTime
                + ", serviceId=" + serviceId + ", status=" + status + ", seqNo=" + seqNo + ", feeAmount=" + feeAmount
                + "]";
    }
}
