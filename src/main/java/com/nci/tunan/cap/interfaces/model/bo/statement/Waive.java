package com.nci.tunan.cap.interfaces.model.bo.statement;

/** 
 * @description 理赔豁免保费日结豁免险种信息
 * <AUTHOR> <EMAIL> 
 * @date 2024-2-19 下午1:16:26  
*/
public class Waive {
    
    /**
     * @Fields SERIAL_NUM : 序号
     */
    private String SERIAL_NUM;

    /**
     * @Fields WAIVE_BUSI_PROD_CODE : 转换后险种编码/权益转移后险种代码
     */
    private String WAIVE_BUSI_PROD_CODE;
    /**
     * @Fields WAIVE_BUSI_PROD_NAME : 转换后险种/权益转移后险种
     */
    private String WAIVE_BUSI_PROD_NAME;

    /**
     * @Fields CHANNEL_TYPE : 销售渠道
     */
    private String CHANNEL_TYPE;

    /**
     * @Fields AGENTCOM : 代理机构
     */
    private String AMOUNT;
    
    /** 
     * @Fields PIECES : 件数
     */ 
     private String PIECES;

    public String getSERIAL_NUM() {
        return SERIAL_NUM;
    }

    public void setSERIAL_NUM(String sERIAL_NUM) {
        SERIAL_NUM = sERIAL_NUM;
    }

    public String getWAIVE_BUSI_PROD_CODE() {
        return WAIVE_BUSI_PROD_CODE;
    }

    public void setWAIVE_BUSI_PROD_CODE(String wAIVE_BUSI_PROD_CODE) {
        WAIVE_BUSI_PROD_CODE = wAIVE_BUSI_PROD_CODE;
    }

    public String getWAIVE_BUSI_PROD_NAME() {
        return WAIVE_BUSI_PROD_NAME;
    }

    public void setWAIVE_BUSI_PROD_NAME(String wAIVE_BUSI_PROD_NAME) {
        WAIVE_BUSI_PROD_NAME = wAIVE_BUSI_PROD_NAME;
    }

    public String getCHANNEL_TYPE() {
        return CHANNEL_TYPE;
    }

    public void setCHANNEL_TYPE(String cHANNEL_TYPE) {
        CHANNEL_TYPE = cHANNEL_TYPE;
    }

    public String getAMOUNT() {
        return AMOUNT;
    }

    public void setAMOUNT(String aMOUNT) {
        AMOUNT = aMOUNT;
    }

    public String getPIECES() {
        return PIECES;
    }

    public void setPIECES(String pIECES) {
        PIECES = pIECES;
    }
     
     

}
