package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseVO;
/**
 * 
 * @description 票据BO对象
 * @<NAME_EMAIL> 
 * @date 2019-12-20 下午2:53:20 
 * @.belongToModule 收付费-票据
 */
public class ChequeReceiptBO extends BaseVO{
	/** 
	 * 序列号
	 */ 
	private static final long serialVersionUID = 1L;
	/**
     * 开票日期
     */
    private Date invoiceDate;
	/**
	 *  支票Id
	 */
	private BigDecimal chequeId;
	/**
	 *付款单位/个人
	 */
	private String handlerName;
	/**
	 *  应收付业务流水标示
	 */
	private String unitNumber;
	/**
     *业务号
     */
    private String businessCode;
	/**
     * 收付费日期
     */
    private Date arapDate;
    /**
     * 金额
     */
    private BigDecimal feeAmount;
    /**
     *  票据余额
     */
    private BigDecimal chequeBanlance;
    /**
     * 支票号码
     */
    private String chequeNo;
    /**
     *  打印状态
     */
    private String receiptStatus;
    /**
     * 票据类型
     */
    private String receiptType;
    /**
     * 打印次数
     */
    private BigDecimal receiptCount;
    /**
     *  打印操作人
     */
    private String receiptBy;
    /**
     * 记录插入人
     */
    private String insertBy;
    /**
     *  记录更新人
     */
    private String updateBy;
    /**
     *  支票对应的应收记录
     */
    private List<PremArapBO> lsPremArapBO;
    
	public BigDecimal getChequeId() {
		return chequeId;
	}

	public void setChequeId(BigDecimal chequeId) {
		this.chequeId = chequeId;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public Date getArapDate() {
		return arapDate;
	}

	public void setArapDate(Date arapDate) {
		this.arapDate = arapDate;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getChequeNo() {
		return chequeNo;
	}

	public void setChequeNo(String chequeNo) {
		this.chequeNo = chequeNo;
	}

	public BigDecimal getChequeBanlance() {
		return chequeBanlance;
	}

	public void setChequeBanlance(BigDecimal chequeBanlance) {
		this.chequeBanlance = chequeBanlance;
	}

	public String getReceiptStatus() {
		return receiptStatus;
	}

	public void setReceiptStatus(String receiptStatus) {
		this.receiptStatus = receiptStatus;
	}

	public String getReceiptType() {
		return receiptType;
	}

	public void setReceiptType(String receiptType) {
		this.receiptType = receiptType;
	}

	public BigDecimal getReceiptCount() {
		return receiptCount;
	}

	public void setReceiptCount(BigDecimal receiptCount) {
		this.receiptCount = receiptCount;
	}

	public String getReceiptBy() {
		return receiptBy;
	}

	public void setReceiptBy(String receiptBy) {
		this.receiptBy = receiptBy;
	}

	public String getInsertBy() {
		return insertBy;
	}

	public void setInsertBy(String insertBy) {
		this.insertBy = insertBy;
	}

	public String getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public List<PremArapBO> getLsPremArapBO() {
		return lsPremArapBO;
	}

	public void setLsPremArapBO(List<PremArapBO> lsPremArapBO) {
		this.lsPremArapBO = lsPremArapBO;
	}

	public String getUnitNumber() {
		return unitNumber;
	}

	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public String getHandlerName() {
		return handlerName;
	}

	public void setHandlerName(String handlerName) {
		this.handlerName = handlerName;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	@Override
	public String getBizId() {
		return null;
	}
}
