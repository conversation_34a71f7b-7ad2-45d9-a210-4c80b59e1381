package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

public class OrgQryCfgBO extends BaseBO{

    /** 
    * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么) 
    */ 
    private static final long serialVersionUID = 1L;
    /** 
     * @Fields organCode : 机构代码
     */ 
    private String organCode;
    /** 
     * @Fields qryOrganCode : 查询机构范围
     */ 
    private String qryOrganCode;
    /** 
     * @Fields isIncludeSub : 是否查询下级机构
     */ 
    private BigDecimal isIncludeSub;
    
    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getQryOrganCode() {
        return qryOrganCode;
    }

    public void setQryOrganCode(String qryOrganCode) {
        this.qryOrganCode = qryOrganCode;
    }

    public BigDecimal getIsIncludeSub() {
        return isIncludeSub;
    }

    public void setIsIncludeSub(BigDecimal isIncludeSub) {
        this.isIncludeSub = isIncludeSub;
    }

    @Override
    public String getBizId() {
        return null;
    }

}
