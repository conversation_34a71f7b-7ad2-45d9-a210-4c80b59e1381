package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

public class MedicareInfoBO extends BaseBO {


    /** 
    * @Fields serialVersionUID : 序列号
    */ 
    
    private static final long serialVersionUID = 1L;
    /** 
    * @Fields listId : 主键
    */ 
    private BigDecimal listId;
    /** 
    * @Fields medicalInsuranceCard : 医保卡标识,使用码表T_MEDICAL_INSURANCE_CARD
    */ 
    private BigDecimal medicalInsuranceCard;
    /** 
    * @Fields businessCode : 业务号
    */ 
    private String businessCode;
    /** 
    * @Fields feeAmount : 应收付的金额
    */ 
    private BigDecimal feeAmount;
    /** 
    * @Fields finishTime : 对账日期（收费时间）
    */ 
    private Date finishTime;
    /** 
    * @Fields busiSerialNum : 结算业务流水号
    */ 
    private String busiSerialNum;
    /** 
    * @Fields busiProdCode : 险种代码
    */ 
    private String busiProdCode;
    /** 
    * @Fields productNumber : 保险产品编号(01-深圳医保专属医疗险一年期02-深圳医保专属医疗险六年期03-深圳医保专属重疾险一年期04-深圳医保专属重疾险五年期05-深圳医保专属重疾险十年期)
    */ 
    private String productNumber;
    /** 
    * @Fields insertBy : 记录插入人
    */ 
    private BigDecimal insertBy;
    /** 
    * @Fields updateBy : 记录更新人
    */ 
    private BigDecimal updateBy;
    
    /** 
    * @Fields arapFlag : 应收应付类型 
    */ 
    private String arapFlag = "1";
    /** 
    * @Fields payMode : 收付方式。码值参考码表T_PAY_MODE
    */ 
    private String payMode = "18";

    /** 
    * @Fields submitChannel : 业务来源平台 
    */ 
    private String submitChannel;
    /** 
    * @Fields organCode : 管理机构 
    */ 
    private String organCode;
    
    /** 
    * @Fields itsmNo : 数补工单编号
    */ 
    private String itsmNo;
    
    /** 
     * @Fields policyCode : 保单号 
     */ 
     private String policyCode;
     
     /** 
     * @Fields transSerialNum : 交易流水号
     */ 
     private String transSerialNum;
     
     
    
    public String getPolicyCode() {
        return policyCode;
    }


    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }


    public String getTransSerialNum() {
        return transSerialNum;
    }


    public void setTransSerialNum(String transSerialNum) {
        this.transSerialNum = transSerialNum;
    }


    public String getSubmitChannel() {
        return submitChannel;
    }


    public void setSubmitChannel(String submitChannel) {
        this.submitChannel = submitChannel;
    }


    public String getOrganCode() {
        return organCode;
    }


    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }


    public String getItsmNo() {
        return itsmNo;
    }


    public void setItsmNo(String itsmNo) {
        this.itsmNo = itsmNo;
    }

    public BigDecimal getListId() {
        return listId;
    }


    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }


    public BigDecimal getMedicalInsuranceCard() {
        return medicalInsuranceCard;
    }


    public void setMedicalInsuranceCard(BigDecimal medicalInsuranceCard) {
        this.medicalInsuranceCard = medicalInsuranceCard;
    }


    public String getBusinessCode() {
        return businessCode;
    }


    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }


    public BigDecimal getFeeAmount() {
        return feeAmount;
    }


    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }


    public Date getFinishTime() {
        return finishTime;
    }


    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }


    public String getBusiSerialNum() {
        return busiSerialNum;
    }


    public void setBusiSerialNum(String busiSerialNum) {
        this.busiSerialNum = busiSerialNum;
    }


    public String getBusiProdCode() {
        return busiProdCode;
    }


    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }


    public String getProductNumber() {
        return productNumber;
    }


    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }


    public BigDecimal getInsertBy() {
        return insertBy;
    }


    public void setInsertBy(BigDecimal insertBy) {
        this.insertBy = insertBy;
    }


    public BigDecimal getUpdateBy() {
        return updateBy;
    }


    public void setUpdateBy(BigDecimal updateBy) {
        this.updateBy = updateBy;
    }


    public String getArapFlag() {
        return arapFlag;
    }


    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }


    public String getPayMode() {
        return payMode;
    }


    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    @Override
    public String toString() {
        return "MedicalInfoBO [listId=" + listId + ", medicalInsuranceCard=" + medicalInsuranceCard + ", businessCode="
                + businessCode + ", feeAmount=" + feeAmount + ", finishTime=" + finishTime + ", busiSerialNum="
                + busiSerialNum + ", busiProdCode=" + busiProdCode + ", productNumber=" + productNumber + ", insertBy="
                + insertBy + ", updateBy=" + updateBy + ", arapFlag=" + arapFlag + ", payMode=" + payMode
                + ", submitChannel=" + submitChannel + ", organCode=" + organCode + ", itsmNo=" + itsmNo
                + ", policyCode=" + policyCode + ", transSerialNum=" + transSerialNum + "]";
    }


    @Override
    public String getBizId() {
        return null;
    }


   
}
