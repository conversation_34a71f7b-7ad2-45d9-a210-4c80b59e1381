package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description 制盘任务明细信息表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL> 
 * @date 2015-11-12 18:30:10  
 */
public class BankTextMainBO extends BaseBO {	
	 /** 
    * @Fields serialVersionUID : 序列化
    */ 
    
    private static final long serialVersionUID = -6101688449347363305L;
    /** 
	* @Fields preTextPackNum :  预制盘包数
 	*/ 
	private BigDecimal preTextPackNum;
	 /** 
	* @Fields bankTextPackNum :  制盘包数
 	*/ 
	private BigDecimal bankTextPackNum;
	 /** 
	* @Fields backTextSuccAcc :  返盘成功金额
 	*/ 
	private BigDecimal backTextSuccAcc;
	 /** 
	* @Fields backTextBy :  最新返盘操作人
 	*/ 
	private BigDecimal backTextBy;
	 /** 
	* @Fields preTextBackBy :  预制盘回退操作人
 	*/ 
	private BigDecimal preTextBackBy;
	 /** 
	* @Fields batchTaskTime :  日期
 	*/ 
	private Date batchTaskTime;
	 /** 
	* @Fields preTextTime :  预制盘时间
 	*/ 
	private Date preTextTime;
	 /** 
	* @Fields preTextBackPackNum :  预制盘回退包数
 	*/ 
	private BigDecimal preTextBackPackNum;
	 /** 
	* @Fields preTextBackTime :  预制盘回退时间
 	*/ 
	private Date preTextBackTime;
	 /** 
	* @Fields preTextBackNum :  预制盘回退数量
 	*/ 
	private BigDecimal preTextBackNum;
	 /** 
	* @Fields backTextFailAcc :  返盘失败金额
 	*/ 
	private BigDecimal backTextFailAcc;
	 /** 
	* @Fields organCode :  管理机构代码
 	*/ 
	private String organCodeList;
		 /** 
	* @Fields channelType :  渠道类型
 	*/ 
	private String channelTypeList;
		 /** 
	* @Fields backTextTime :  最新返盘时间
 	*/ 
	private Date backTextTime;
	 /** 
	* @Fields batchTask :  批任务
 	*/ 
	private BigDecimal batchTask;
	 /** 
	* @Fields batchTaskNum :  批次号
 	*/ 
	private BigDecimal batchTaskNum;
	 /** 
	* @Fields bankTextBy :  制盘操作人
 	*/ 
	private BigDecimal bankTextBy;
	 /** 
	* @Fields rangeValue :  值
 	*/ 
	private BigDecimal rangeValue;
	 /** 
	* @Fields unit :  取值单位
 	*/ 
	private BigDecimal unit;
		 /** 
	* @Fields preTextSumAcc :  预制盘合计金额
 	*/ 
	private BigDecimal preTextSumAcc;
	 /** 
	* @Fields bankTextSumAcc :  制盘合计金额
 	*/ 
	private BigDecimal bankTextSumAcc;
		 /** 
	* @Fields preTextNum :  预制盘数量
 	*/ 
	private BigDecimal preTextNum;
	 /** 
	* @Fields preTextBy :  预制盘操作人
 	*/ 
	private BigDecimal preTextBy;
	 /** 
	* @Fields bankName :  银行名称
 	*/ 
	private String bankName;
	 /** 
	* @Fields derivType :  业务来源
 	*/ 
	private String derivTypeList;
	 /** 
	* @Fields mainId :  序列号
 	*/ 
	private BigDecimal mainId;

	 /** 
	* @Fields backTextPackSuccNum :  返盘成功数量
 	*/ 
	private BigDecimal backTextPackSuccNum;
	 /** 
	* @Fields bankTextTime :  制盘时间
 	*/ 
	private Date bankTextTime;
	 /** 
	* @Fields backTextPackFailNum :  返盘失败数量
 	*/ 
	private BigDecimal backTextPackFailNum;
	 /** 
	* @Fields arapFlag :  收付标识
 	*/ 
	private String arapFlagList;
	 /** 
	* @Fields bankCode :  银行编码
 	*/ 
	private String bankCode;
	 /** 
	* @Fields bankDiskType :  处理内容
 	*/ 
	private BigDecimal bankDiskType;
			 /** 
	* @Fields bankTextNum :  制盘数量
 	*/ 
	private BigDecimal bankTextNum;
	 /** 
	* @Fields preTextBackSumAcc :  预制盘回退合计金额
 	*/ 
	private BigDecimal preTextBackSumAcc;
	 /** 
	* @Fields backTextPackNum :  返盘包数
 	*/ 
	private BigDecimal backTextPackNum;
	
	/**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
	
	/**
     * @Fields @Fields : 大额拆分标识，0否，1是
     */
    private BigDecimal largeSplitFlag;
		
	 public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public BigDecimal getLargeSplitFlag() {
        return largeSplitFlag;
    }

    public void setLargeSplitFlag(BigDecimal largeSplitFlag) {
        this.largeSplitFlag = largeSplitFlag;
    }

    public void setPreTextPackNum(BigDecimal preTextPackNum) {
		this.preTextPackNum = preTextPackNum;
	}
	
	public BigDecimal getPreTextPackNum() {
		return preTextPackNum;
	}
	 public void setBankTextPackNum(BigDecimal bankTextPackNum) {
		this.bankTextPackNum = bankTextPackNum;
	}
	
	public BigDecimal getBankTextPackNum() {
		return bankTextPackNum;
	}
	 public void setBackTextSuccAcc(BigDecimal backTextSuccAcc) {
		this.backTextSuccAcc = backTextSuccAcc;
	}
	
	public BigDecimal getBackTextSuccAcc() {
		return backTextSuccAcc;
	}
	 public void setBackTextBy(BigDecimal backTextBy) {
		this.backTextBy = backTextBy;
	}
	
	public BigDecimal getBackTextBy() {
		return backTextBy;
	}
	 public void setPreTextBackBy(BigDecimal preTextBackBy) {
		this.preTextBackBy = preTextBackBy;
	}
	
	public BigDecimal getPreTextBackBy() {
		return preTextBackBy;
	}
	 public void setBatchTaskTime(Date batchTaskTime) {
		this.batchTaskTime = batchTaskTime;
	}
	
	public Date getBatchTaskTime() {
		return batchTaskTime;
	}
	 public void setPreTextTime(Date preTextTime) {
		this.preTextTime = preTextTime;
	}
	
	public Date getPreTextTime() {
		return preTextTime;
	}
	 public void setPreTextBackPackNum(BigDecimal preTextBackPackNum) {
		this.preTextBackPackNum = preTextBackPackNum;
	}
	
	public BigDecimal getPreTextBackPackNum() {
		return preTextBackPackNum;
	}
	 public void setPreTextBackTime(Date preTextBackTime) {
		this.preTextBackTime = preTextBackTime;
	}
	
	public Date getPreTextBackTime() {
		return preTextBackTime;
	}
	 public void setPreTextBackNum(BigDecimal preTextBackNum) {
		this.preTextBackNum = preTextBackNum;
	}
	
	public BigDecimal getPreTextBackNum() {
		return preTextBackNum;
	}
	 public void setBackTextFailAcc(BigDecimal backTextFailAcc) {
		this.backTextFailAcc = backTextFailAcc;
	}
	
	public BigDecimal getBackTextFailAcc() {
		return backTextFailAcc;
	}
	
		 public void setBackTextTime(Date backTextTime) {
		this.backTextTime = backTextTime;
	}
	
	public Date getBackTextTime() {
		return backTextTime;
	}
	 public void setBatchTask(BigDecimal batchTask) {
		this.batchTask = batchTask;
	}
	
	public BigDecimal getBatchTask() {
		return batchTask;
	}
	 public void setBatchTaskNum(BigDecimal batchTaskNum) {
		this.batchTaskNum = batchTaskNum;
	}
	
	public BigDecimal getBatchTaskNum() {
		return batchTaskNum;
	}
	 public void setBankTextBy(BigDecimal bankTextBy) {
		this.bankTextBy = bankTextBy;
	}
	
	public BigDecimal getBankTextBy() {
		return bankTextBy;
	}
	 public void setRangeValue(BigDecimal rangeValue) {
		this.rangeValue = rangeValue;
	}
	
	public BigDecimal getRangeValue() {
		return rangeValue;
	}
	 public void setUnit(BigDecimal unit) {
		this.unit = unit;
	}
	
	public BigDecimal getUnit() {
		return unit;
	}
		 public void setPreTextSumAcc(BigDecimal preTextSumAcc) {
		this.preTextSumAcc = preTextSumAcc;
	}
	
	public BigDecimal getPreTextSumAcc() {
		return preTextSumAcc;
	}
	 public void setBankTextSumAcc(BigDecimal bankTextSumAcc) {
		this.bankTextSumAcc = bankTextSumAcc;
	}
	
	public BigDecimal getBankTextSumAcc() {
		return bankTextSumAcc;
	}
		 public void setPreTextNum(BigDecimal preTextNum) {
		this.preTextNum = preTextNum;
	}
	
	public BigDecimal getPreTextNum() {
		return preTextNum;
	}
	 public void setPreTextBy(BigDecimal preTextBy) {
		this.preTextBy = preTextBy;
	}
	
	public BigDecimal getPreTextBy() {
		return preTextBy;
	}
	 public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	
	public String getBankName() {
		return bankName;
	}

	 public void setMainId(BigDecimal mainId) {
		this.mainId = mainId;
	}
	
	public BigDecimal getMainId() {
		return mainId;
	}

	 public void setBackTextPackSuccNum(BigDecimal backTextPackSuccNum) {
		this.backTextPackSuccNum = backTextPackSuccNum;
	}
	
	public BigDecimal getBackTextPackSuccNum() {
		return backTextPackSuccNum;
	}
	 public void setBankTextTime(Date bankTextTime) {
		this.bankTextTime = bankTextTime;
	}
	
	public Date getBankTextTime() {
		return bankTextTime;
	}
	 public void setBackTextPackFailNum(BigDecimal backTextPackFailNum) {
		this.backTextPackFailNum = backTextPackFailNum;
	}
	
	public BigDecimal getBackTextPackFailNum() {
		return backTextPackFailNum;
	}

	 public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	
	public String getBankCode() {
		return bankCode;
	}
	 public void setBankDiskType(BigDecimal bankDiskType) {
		this.bankDiskType = bankDiskType;
	}
	
	public BigDecimal getBankDiskType() {
		return bankDiskType;
	}
			 public void setBankTextNum(BigDecimal bankTextNum) {
		this.bankTextNum = bankTextNum;
	}
	
	public BigDecimal getBankTextNum() {
		return bankTextNum;
	}
	 public void setPreTextBackSumAcc(BigDecimal preTextBackSumAcc) {
		this.preTextBackSumAcc = preTextBackSumAcc;
	}
	
	public BigDecimal getPreTextBackSumAcc() {
		return preTextBackSumAcc;
	}
	 public void setBackTextPackNum(BigDecimal backTextPackNum) {
		this.backTextPackNum = backTextPackNum;
	}
	
	public BigDecimal getBackTextPackNum() {
		return backTextPackNum;
	}
		
	public String getOrganCodeList() {
        return organCodeList;
    }

    public void setOrganCodeList(String organCodeList) {
        this.organCodeList = organCodeList;
    }

    public String getChannelTypeList() {
        return channelTypeList;
    }

    public void setChannelTypeList(String channelTypeList) {
        this.channelTypeList = channelTypeList;
    }

    public String getDerivTypeList() {
        return derivTypeList;
    }

    public void setDerivTypeList(String derivTypeList) {
        this.derivTypeList = derivTypeList;
    }

    public String getArapFlagList() {
        return arapFlagList;
    }

    public void setArapFlagList(String arapFlagList) {
        this.arapFlagList = arapFlagList;
    }

    @Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "BankTextMainVO [" +
				"preTextPackNum="+preTextPackNum+","+
"bankTextPackNum="+bankTextPackNum+","+
"backTextSuccAcc="+backTextSuccAcc+","+
"backTextBy="+backTextBy+","+
"preTextBackBy="+preTextBackBy+","+
"batchTaskTime="+batchTaskTime+","+
"preTextTime="+preTextTime+","+
"preTextBackPackNum="+preTextBackPackNum+","+
"preTextBackTime="+preTextBackTime+","+
"preTextBackNum="+preTextBackNum+","+
"backTextFailAcc="+backTextFailAcc+","+
"organCodeList="+organCodeList+","+
"channelTypeList="+channelTypeList+","+
"backTextTime="+backTextTime+","+
"batchTask="+batchTask+","+
"batchTaskNum="+batchTaskNum+","+
"bankTextBy="+bankTextBy+","+
"rangeValue="+rangeValue+","+
"unit="+unit+","+
"preTextSumAcc="+preTextSumAcc+","+
"bankTextSumAcc="+bankTextSumAcc+","+
"preTextNum="+preTextNum+","+
"preTextBy="+preTextBy+","+
"bankName="+bankName+","+
"derivTypeList="+derivTypeList+","+
"mainId="+mainId+","+
"backTextPackSuccNum="+backTextPackSuccNum+","+
"bankTextTime="+bankTextTime+","+
"backTextPackFailNum="+backTextPackFailNum+","+
"arapFlagList="+arapFlagList+","+
"bankCode="+bankCode+","+
"bankDiskType="+bankDiskType+","+
"bankTextNum="+bankTextNum+","+
"preTextBackSumAcc="+preTextBackSumAcc+","+
"backTextPackNum="+backTextPackNum+"]";
    }
}
