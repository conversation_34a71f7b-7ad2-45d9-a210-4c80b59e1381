package com.nci.tunan.cap.interfaces.model.bo;

import com.nci.tunan.cap.interfaces.model.vo.ChequePrintClientVO;
import com.nci.tunan.cap.interfaces.model.vo.DocumentPrintClientVO;
import com.nci.udmp.framework.model.BaseBO;


/** 
 * @description 票据打印PrintReturnBO
 * @<NAME_EMAIL> 
 * @date 2017-12-20 下午2:05:05 
 * @.belongToModule 收付费-票据打印
*/
public class PrintReturnBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化版本号
     */
    private static final long serialVersionUID = -3445156203896935803L;

    /**
     * @Fields isFlag : 是否成功标志位 true 成功、false 失败
     */
    private boolean isFlag;

    /**
     * @Fields errorMsg : 失败原因
     */
    private String errorCode;

    /**
     * @Fields errorDesc : 失败信息描述
     */
    private String errorDesc;
    
    /** 
    * @Fields chequePrintClientVO : 柜台收付费支票收款打印VO
    */ 
    private ChequePrintClientVO chequePrintClientVO;
    /** 
    * @Fields documentPrintClientVO : 票据打印接口交互VO
    */ 
    private DocumentPrintClientVO documentPrintClientVO;

    @Override
    public String getBizId() {
        return null;
    }

    /**
     * getter 方法
     * 
     * @return isFlag
     */
    public boolean isFlag() {
        return isFlag;
    }

    public void setFlag(boolean flag) {
        this.isFlag = flag;
    }

    /**
     * getter 方法
     * 
     * @return errorCode
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * setter 方法
     * 
     * @param errorCode
     *            , 设置errorCode
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * getter 方法
     * 
     * @return errorDesc
     */
    public String getErrorDesc() {
        return errorDesc;
    }

    /**
     * setter 方法
     * 
     * @param errorDesc
     *            , 设置errorDesc
     */
    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }

	public ChequePrintClientVO getChequePrintClientVO() {
		return chequePrintClientVO;
	}

	public void setChequePrintClientVO(ChequePrintClientVO chequePrintClientVO) {
		this.chequePrintClientVO = chequePrintClientVO;
	}

	public DocumentPrintClientVO getDocumentPrintClientVO() {
		return documentPrintClientVO;
	}

	public void setDocumentPrintClientVO(DocumentPrintClientVO documentPrintClientVO) {
		this.documentPrintClientVO = documentPrintClientVO;
	}

}
