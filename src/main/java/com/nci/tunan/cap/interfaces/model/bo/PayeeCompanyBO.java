package com.nci.tunan.cap.interfaces.model.bo;

import java.util.Date;
import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * <AUTHOR>
 * @.belongToModule 收付费—应收应付
 * @date 2015年5月21日 下午2:01:04
 * @description 应收应付授权表
 */
public class PayeeCompanyBO extends BaseBO {

    /** 
    * @Fields serialVersionUID : * 序列号
     */ 
    private static final long serialVersionUID = 1L;

    @Override
    public String getBizId() {
        return "com.nci.tunan.cap.interfaces.model.bo.PayeeCompanyBO";
    }
    
    /** 
     * @Fields unitNumber : 应收付业务流水标识 
     */ 
     private String unitNumber;
     
     /** 
     * @Fields companyName : 法人/单位名称
     */ 
     private String companyName;
     
     /** 
     * @Fields companyAddress : 法人/单位注册地址
     */ 
     private String companyAddress;
     
     /** 
     * @Fields companyBusiScope : 法人/单位经营范围
     */ 
     private String companyBusiScope;
     
     /** 
     * @Fields companyCertiType : 法人/单位证件类型
     */ 
     private String companyCertiType;
     
     /** 
     * @Fields companyCertiCode : 法人/单位证件号码
     */ 
     private String companyCertiCode;
     
     /** 
     * @Fields certiStartDate : 法人/单位证件有效起期
     */ 
     private Date certiStartDate;
     /** 
     * @Fields certiEndDate : 法人/单位证件有效止期
     */ 
     private Date certiEndDate;
     
     /**
      * @Fields billId : 票据流水号
      */
     private BigDecimal billId;

    public BigDecimal getBillId() {
		return billId;
	}
	public void setBillId(BigDecimal billId) {
		this.billId = billId;
	}
	public String getUnitNumber() {
        return unitNumber;
    }
    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }
    public String getCompanyName() {
        return companyName;
    }
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public String getCompanyAddress() {
        return companyAddress;
    }
    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }
    public String getCompanyBusiScope() {
        return companyBusiScope;
    }
    public void setCompanyBusiScope(String companyBusiScope) {
        this.companyBusiScope = companyBusiScope;
    }
    public String getCompanyCertiType() {
        return companyCertiType;
    }
    public void setCompanyCertiType(String companyCertiType) {
        this.companyCertiType = companyCertiType;
    }
    public String getCompanyCertiCode() {
        return companyCertiCode;
    }
    public void setCompanyCertiCode(String companyCertiCode) {
        this.companyCertiCode = companyCertiCode;
    }
    public Date getCertiStartDate() {
        return certiStartDate;
    }
    public void setCertiStartDate(Date certiStartDate) {
        this.certiStartDate = certiStartDate;
    }
    public Date getCertiEndDate() {
        return certiEndDate;
    }
    public void setCertiEndDate(Date certiEndDate) {
        this.certiEndDate = certiEndDate;
    }
    @Override
    public String toString() {
        return "PayeeCompanyBO [unitNumber=" + unitNumber + ", companyName=" + companyName + ", companyAddress="
                + companyAddress + ", companyBusiScope=" + companyBusiScope + ", companyCertiType=" + companyCertiType
                + ", companyCertiCode=" + companyCertiCode + ", certiStartDate=" + certiStartDate + ", certiEndDate="
                + certiEndDate + "]";
    }
     
     
    
}
