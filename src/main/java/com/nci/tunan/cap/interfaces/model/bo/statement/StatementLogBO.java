package com.nci.tunan.cap.interfaces.model.bo.statement;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description StatementLogVO对象
 * <AUTHOR>
 * @date 2023-12-27 14:51:20
 */
public class StatementLogBO extends BaseBO {

    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */

    private static final long serialVersionUID = 1L;
    /**
     * @Fields statementStatus : 归档状态，码表T
     */
    private String statementStatus;
    /**
     * @Fields operTime : 操作时间
     */
    private Date operTime;

    /**
     * @Fields startDate : 开始时间
     */
    private Date startDate;
    /**
     * @Fields endDate : 结束时间
     */
    private Date endDate;
    /**
     * @Fields rollbackCause : 回退原因
     */
    private String rollbackCause;
    /**
     * @Fields fileName : 日结文件名称
     */
    private String fileName;
    /**
     * @Fields isVariance : 是否有差异说明：T
     */
    private BigDecimal isVariance;
    /**
     * @Fields isRollback : 是否回退：T
     */
    private BigDecimal isRollback;
    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;

    /**
     * @Fields organCode : 收付费机构代码
     */
    private String capOrganCode;

    /**
     * @Fields organName : 管理机构名称
     */
    private String organName;

    /**
     * @Fields statementType : 日结类型
     */
    private String statementType;
    /**
     * @Fields statementType : 日结名称
     */
    private String statementName;

    /**
     * @Fields listStr : 序号
     */
    private BigDecimal listStr;
    /**
     * @Fields statementDate : 日结日期
     */
    private Date statementDate;
    /**
     * @Fields statementTypes : 日结类型 集合
     */
    private List<String> statementTypes;
    /**
     * @Fields operUser : 操作人
     */
    private BigDecimal operUser;
    /**
     * @Fields listId : 主键序列
     */
    private BigDecimal listId;
    /**
     * @Fields statementId : 日结ID
     */
    private BigDecimal statementId;
    /**
     * @Fields errorFlag : 是否有差异说明
     */
    private String errorFlag;
    /**
     * @Fields statementPeriod : 日结、月度标志 D、M
     */
    private String statementPeriod;
    /**
     * @Fields operatorBy : 操作员姓名(对应 日结表中的OPER_USER字段)
     */
    private String operatorBy;
    
    public String getOperatorBy() {
        return operatorBy;
    }

    public void setOperatorBy(String operatorBy) {
        this.operatorBy = operatorBy;
    }

    public String getStatementPeriod() {
        return statementPeriod;
    }

    public void setStatementPeriod(String statementPeriod) {
        this.statementPeriod = statementPeriod;
    }

    public String getErrorFlag() {
        return errorFlag;
    }

    public void setErrorFlag(String errorFlag) {
        this.errorFlag = errorFlag;
    }

    public void setStatementStatus(String statementStatus) {
        this.statementStatus = statementStatus;
    }

    public String getStatementStatus() {
        return statementStatus;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setRollbackCause(String rollbackCause) {
        this.rollbackCause = rollbackCause;
    }

    public String getRollbackCause() {
        return rollbackCause;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setIsVariance(BigDecimal isVariance) {
        this.isVariance = isVariance;
    }

    public BigDecimal getIsVariance() {
        return isVariance;
    }

    public void setIsRollback(BigDecimal isRollback) {
        this.isRollback = isRollback;
    }

    public BigDecimal getIsRollback() {
        return isRollback;
    }

    public void setOperUser(BigDecimal operUser) {
        this.operUser = operUser;
    }

    public BigDecimal getOperUser() {
        return operUser;
    }

    public void setListId(BigDecimal listId) {
        this.listId = listId;
    }

    public BigDecimal getListId() {
        return listId;
    }

    public void setStatementId(BigDecimal statementId) {
        this.statementId = statementId;
    }

    public BigDecimal getStatementId() {
        return statementId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getStatementType() {
        return statementType;
    }

    public void setStatementType(String statementType) {
        this.statementType = statementType;
    }

    public List<String> getStatementTypes() {
        return statementTypes;
    }

    public void setStatementTypes(List<String> statementTypes) {
        this.statementTypes = statementTypes;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public Date getStatementDate() {
        return statementDate;
    }

    public void setStatementDate(Date statementDate) {
        this.statementDate = statementDate;
    }

    public BigDecimal getListStr() {
        return listStr;
    }

    public void setListStr(BigDecimal listStr) {
        this.listStr = listStr;
    }

    public String getStatementName() {
        return statementName;
    }

    public void setStatementName(String statementName) {
        this.statementName = statementName;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    @Override
    public String toString() {
        return "StatementLogBO [statementStatus=" + statementStatus + ", operTime=" + operTime + ", startDate="
                + startDate + ", endDate=" + endDate + ", rollbackCause=" + rollbackCause + ", fileName=" + fileName
                + ", isVariance=" + isVariance + ", isRollback=" + isRollback + ", organCode=" + organCode
                + ", capOrganCode=" + capOrganCode + ", organName=" + organName + ", statementType=" + statementType
                + ", statementName=" + statementName + ", listStr=" + listStr + ", statementDate=" + statementDate
                + ", statementTypes=" + statementTypes + ", operUser=" + operUser + ", listId=" + listId
                + ", statementId=" + statementId + ", errorFlag=" + errorFlag + "]";
    }
}
