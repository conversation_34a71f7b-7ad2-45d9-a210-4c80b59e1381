package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description 发票查询ReceiptQueryBO
 * @<NAME_EMAIL> 
 * @date 2017-12-20 上午10:23:01 
 * @.belongToModule 收付费-发票查询
*/
public class ReceiptQueryBO extends BaseBO {
    /**
     * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
     */

    private static final long serialVersionUID = 1L;
    
    /**
     * @Fields transactionId : 交易逻辑ID
     */
    private BigDecimal transactionId;
    /**
     * @Fields customerId : 应收付业务流水标识
     */
    private BigDecimal customerId;
    /**
     * @Fields unitNumbers : 应收付业务流水标识String集合
     */
    private String unitNumbers;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * 代理人代码
     */
    private String agentCode;
    /**
     * 服务人员姓名
     */
    private String agentName;
    /**
     * 区
     */
    private String area;
    /**
     * 部
     */
    private String part;
    /**
     * 组
     */
    private String group;
    /**
     * @Fields paidCount : 缴费次数
     */
    private BigDecimal paidCount;
    /**
     * @Fields premFreq : 年缴、月缴、趸交等等 1;趸缴 2;月缴 3;季缴 4;半年缴 5;年缴 6;不定期缴 9;其他
     */
    private BigDecimal premFreq;
    /**
     * @Fields organCode : 系统内往来对方机构 管理机构
     */
    private String organCode;
    /**
     * @Fields policyOrganCode : 管理机构
     */
    private String policyOrganCode;
    /** 
    * @Fields capOrganCode : 收付费机构
    */ 
    private String capOrganCode;
    /**
     * @Fields businessCode : 业务号。
     */
    private String businessCode;
    /**
     * @Fields businessType : 业务类型代码
     */
    private String businessType;
    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;
    /**
     * @Fields policyCode : 保单号码
     */
    
    private String busiProdName;
    /**
     * @Fields dueTime : 应缴应付日
     */
    private Date dueTime;
    
    /**
     * @Fields StartDate : 收费开始日期
     */
    private Date startDate;

    /**
     * @Fields EndDate : 收费结束日期
     */
    private Date endDate;
    /**
     * @Fields channelType : 渠道类型
     */
    private String channelType;
    
    /** 
    * @Fields arapDate : 收付日期
    */ 
    private Date arapDate;
    /**
     * @Fields derivType : 业务来源 001;新契约 003;续保 004;保全 005;理赔
     */
    private String derivType;
    /**
     * @Fields feeAmount : 收付金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields holderName : 投保人姓名
     */
    private String holderName;
    /**
     * @Fields receiptStatus : 投保人姓名
     */
    private String receiptStatus;
    
    /**
     * @Fields receiptCount : 收据打印次数
     */
    private BigDecimal receiptCount;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;  
 
    /** 
    * @Fields organName : 分公司名称
    */ 
    private String organName;
    
    /**
     * @Fields finishTime :  业务核销时间
     */
    private Date finishTime;
    /** 
    * @Fields serviceCode : 服务编码（保全为保全项简称）
    */ 
    private String serviceCode;

	public BigDecimal getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(BigDecimal transactionId) {
		this.transactionId = transactionId;
	}

	public String getUnitNumbers() {
		return unitNumbers;
	}

	public void setUnitNumbers(String unitNumbers) {
		this.unitNumbers = unitNumbers;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public String getUnitNumber() {
		return unitNumber;
	}

	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public BigDecimal getPaidCount() {
		return paidCount;
	}

	public void setPaidCount(BigDecimal paidCount) {
		this.paidCount = paidCount;
	}

	public BigDecimal getPremFreq() {
		return premFreq;
	}

	public void setPremFreq(BigDecimal premFreq) {
		this.premFreq = premFreq;
	}

	public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }
    
    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public String getCapOrganCode() {
        return capOrganCode;
    }

    public void setCapOrganCode(String capOrganCode) {
        this.capOrganCode = capOrganCode;
    }

    public Date getArapDate() {
        return arapDate;
    }

    public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }


    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }


    public Date getDueTime() {
        return dueTime;
    }

    public void setDueTime(Date dueTime) {
        this.dueTime = dueTime;
    }

    public String getPolicyCode() {
        return policyCode;
    }


    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

   


    public String getChannelType() {
        return channelType;
    }


    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }


    public String getDerivType() {
        return derivType;
    }


    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }


    public String getBranchCode() {
        return branchCode;
    }


    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }


    public BigDecimal getFeeAmount() {
        return feeAmount;
    }


    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }


    public String getOrganName() {
        return organName;
    }


    public void setOrganName(String organName) {
        this.organName = organName;
    }


    public static long getSerialversionuid() {
        return serialVersionUID;
    }


    public String getHolderName() {
        return holderName;
    }


    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }


    public void setReceiptStatus(String receiptStatus) {
        this.receiptStatus = receiptStatus;
    }

    public String getReceiptStatus() {
        return receiptStatus;
    }


    public BigDecimal getReceiptCount() {
        return receiptCount;
    }


    public void setReceiptCount(BigDecimal receiptCount) {
        this.receiptCount = receiptCount;
    }


  

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
    
    public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getPart() {
		return part;
	}

	public void setPart(String part) {
		this.part = part;
	}

	public String getGroup() {
		return group;
	}

	public void setGroup(String group) {
		this.group = group;
	}
	
	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	@Override
    public String toString() {
        return "ReceiptQueryBO [organCode=" + organCode + ", policyOrganCode=" + policyOrganCode + ", capOrganCode="
                + capOrganCode + ", businessCode=" + businessCode + ", policyCode=" + policyCode + ", busiProdName="
                + busiProdName + ", dueTime=" + dueTime + ", startDate=" + startDate + ", endDate=" + endDate
                + ", arapDate=" + arapDate + ", channelType=" + channelType + ", derivType=" + derivType
                + ", feeAmount=" + feeAmount + ", holderName=" + holderName + ", receiptStatus=" + receiptStatus
                + ", receiptCount=" + receiptCount + ", branchCode=" + branchCode + ", organName=" + organName + "]";
    }

    @Override
    public String getBizId() {
        return null;
    }

   

}
