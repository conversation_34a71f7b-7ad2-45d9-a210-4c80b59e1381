package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description ArapAuthBO对象
 * <AUTHOR>
 * @date 2015-05-21 18:06:25
 * @.belongToModule 收付费-收付费查询
 */
public class ArapAuthBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化版本号
     */

    private static final long serialVersionUID = 1L;
    /**
     * @Fields organCode : 管理机构
     */
    private String organCode;
    /**
     * @Fields payMode : 收付方式
     */
    private String payMode;
    /**
     * @Fields arAp : 收付标识
     */
    private String arAp;
    /**
     * @Fields authId : 序列号
     */
    private BigDecimal authId;
    /**
     * @Fields amount : 金额
     */
    private BigDecimal amount;

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setArAp(String arAp) {
        this.arAp = arAp;
    }

    public String getArAp() {
        return arAp;
    }

    public void setAuthId(BigDecimal authId) {
        this.authId = authId;
    }

    public BigDecimal getAuthId() {
        return authId;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ArapAuthBO [" + "organCode=" + organCode + ", payMode=" + payMode + ", arAp=" + arAp + ", authId="
                + authId + ", amount=" + amount + "]";
    }
}
