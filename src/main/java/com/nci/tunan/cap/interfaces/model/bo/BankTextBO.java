package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 盘文件BO对象
 * <AUTHOR>
 * @date 2015-05-21 18:06:25
 * @.belongToModule 收付费-集中制返盘
 */
public class BankTextBO extends BaseBO {

    /**
     * 序列号
     */

    private static final long serialVersionUID = -1581169574746759819L;
    /**
     *  业务来源的多选设置
     */
    private String chksNames;

    /**
     * 报盘文档生成时间
     */
    private Date createTime;
    /**
     * 返盘成功金额
     */
    private BigDecimal backTextSuccAcc;
    /**
     *  流水号 收付费生成，唯一，区分老核心
     */
    private String discNo;
    /**
     *  批任务日期
     */
    private Date batchTaskTime;
    /**
     * 预制盘结束时间
     */
    private Date pcrtEnd;
    /**
     * 制盘文件路径
     */
    private String textPath;
    /**
     *  流水号，无业务含义
     */
    private BigDecimal sendId;
    /**
     *  返盘失败金额
     */
    private BigDecimal backTextFailAcc;
    /**
     *  销售渠道类型
     */
    private String channelType;
    /**
     *  报盘总记录数
     */
    private BigDecimal sendCount;
    /**
     * 批处理任务配置
     */
    private BigDecimal batchTask;
    /**
     *  返盘时间
     */
    private Date backTextTime;
    /**
     *  批处理任务号
     */
    private BigDecimal batchTaskNum;
    /**
     * 返盘电子文档
     */
    private String backText;
    /**
     *  返盘失败条数
     */
    private BigDecimal backTextFailNum;
    /**
     * 报盘下载次数
     */
    private BigDecimal downloads;
    /**
     * 返盘人员ID
     */
    private BigDecimal backerId;
    /**
     *盘文件组号
     */
    private String groupNum;
    /**
     *  盘文件状态 2 预制盘成功 3 预制盘回退 5 制盘失败 6 制盘成功 7 返盘成功 8 返盘失败 9
     *         制盘下载成功 10 返盘上传成功
     */
    private String bankTextStatus;
    /**
     *  报盘总金额
     */
    private BigDecimal sendAmount;
    /**
     *  返盘上载时间
     */
    private Date uploadTime;
    /**
     *  返盘成功条数
     */
    private BigDecimal backTextSuccNum;
    /**
     * 返盘文件名
     */
    private String backFileName;
    /**
     *  业务来源
     */
    /**
     * 序列号
     */
    private BigDecimal diskTrailId;
    /**
     *  唯一，作为资金系统的来源批号。
     */
    private String fileName;
    /**
     *  MD5摘要
     */
    private String sendTextMd;
    /**
     *  实际收付费机构
     */
    private String actualCapOrgId;
    /**
     * 应收应付类型 0;非应收/应付 1;应收 2;应付
     */
    private String arapFlag;
    /**
     *  返盘文件路径
     */
    private String backTextPath;
    /**
     *银行编码
     */
    private String bankCode;
    /**
     * 报盘人员ID
     */
    private BigDecimal senderId;
    /**
     *  成功率
     */
    private String successrate;
    /**
     *  处理条数
     */
    private BigDecimal fatlsucc;

    /**
     *  分公司
     */
    private String branchCode;
    /**
     * 实际收付费机构名称
     */
    private String actualCapOrgName;
    /**
     * 收付标识名称
     */
    private String arapFlagName;
    /**
     * 渠道名称
     */
    private String channelTypeName;
    /**
     * 业务来源名称
     */
    private String derivTypeName;
    /**
     * 制盘状态名称
     */
    private String banktextStatusName;
    /**
     * 制盘开始日期
     */
    private Date crtstart;
    /**
     *  制盘结束日期
     */
    private Date crtend;
    /**
     *  返盘开始日期
     */
    private Date backstart;
    /**
     *  返盘结束日期
     */
    private Date backend;
    /**
     *  分公司
     */
    private String derivType;
    /**
     * 开始查询日期
     */
    private String startDate;
    /**
     * 结束查询日期
     */
    private String endDate;
    /**
     * 预制盘
     */
    private String yzp;
    /**
     * 录入日期
     */
    private Date insertTime;
    /**
     * 机构集合
     */
    private List<String> capOrganCodeList;
    /**
     * 分公司集合
     */
    private List<String> branchCodeCodeList;
    /**
     * 制返盘成功短信集合
     */
    private List<BankTextPremCompBO> sucSMSList;//制返盘成功短信集合
    /**
     * 制返盘失败短信集合
     */
    private List<BankTextPremCompBO> failSMSList;//制返盘失败短信集合
    /**
     * 通知前端业务集合
     */
    private List<UnifiedCallbackBranchesBO> unifiedCallLists;//通知前端业务集合
    
    /** 
    * 销售渠道分组 1.银代 2非银代  3中银保通渠道
    */ 
    private String channelGroup;
    /**
     * @Fields specialAccountFlag : 特殊账户标记  1-中银保信个人养老金账户
     */
     private String specialAccountFlag;
    
   //#161949 关于针对绩优业务员客户优化制返盘功能的需求by fuyh 2024-06 begin
     /**
      * @Fields meritFlag : 绩优等级
      */
     private BigDecimal meritFlag;
     
     public BigDecimal getMeritFlag() {
         return meritFlag;
     }

     public void setMeritFlag(BigDecimal meritFlag) {
         this.meritFlag = meritFlag;
     }
     //#161949 关于针对绩优业务员客户优化制返盘功能的需求by fuyh 2024-06 end
     
    /**
     * @Fields @Fields : 大额拆分标识，0否，1是
     */
    private BigDecimal largeSplitFlag; 
    
    public BigDecimal getLargeSplitFlag() {
        return largeSplitFlag;
    }

    public void setLargeSplitFlag(BigDecimal largeSplitFlag) {
        this.largeSplitFlag = largeSplitFlag;
    }

    public String getSpecialAccountFlag() {
		return specialAccountFlag;
	}

	public void setSpecialAccountFlag(String specialAccountFlag) {
		this.specialAccountFlag = specialAccountFlag;
	}

	public String getChannelGroup() {
        return channelGroup;
    }

    public void setChannelGroup(String channelGroup) {
        this.channelGroup = channelGroup;
    }

    public String getBanktextStatusName() {
		return banktextStatusName;
	}

	public List<UnifiedCallbackBranchesBO> getUnifiedCallLists() {
		return unifiedCallLists;
	}

	public void setUnifiedCallLists(List<UnifiedCallbackBranchesBO> unifiedCallLists) {
		this.unifiedCallLists = unifiedCallLists;
	}

	public void setBanktextStatusName(String banktextStatusName) {
		this.banktextStatusName = banktextStatusName;
	}

	public List<BankTextPremCompBO> getSucSMSList() {
		return sucSMSList;
	}

	public void setSucSMSList(List<BankTextPremCompBO> sucSMSList) {
		this.sucSMSList = sucSMSList;
	}

	public List<BankTextPremCompBO> getFailSMSList() {
		return failSMSList;
	}

	public void setFailSMSList(List<BankTextPremCompBO> failSMSList) {
		this.failSMSList = failSMSList;
	}

	public String getYzp() {
        return yzp;
    }

    public void setYzp(String yzp) {
        this.yzp = yzp;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    // private String branchCode;
    public Date getCrtstart() {
        return crtstart;
    }

    public void setCrtstart(Date crtstart) {
        this.crtstart = crtstart;
    }

    public Date getCrtend() {
        return crtend;
    }

    public void setCrtend(Date crtend) {
        this.crtend = crtend;
    }

    public Date getBackstart() {
        return backstart;
    }

    public void setBackstart(Date backstart) {
        this.backstart = backstart;
    }

    public Date getBackend() {
        return backend;
    }

    public void setBackend(Date backend) {
        this.backend = backend;
    }

    // public String getBranchCode() {
    // return branchCode;
    // }
    //
    // public void setBranchCode(String branchCode) {
    // this.branchCode = branchCode;
    // }
    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public BigDecimal getFatlsucc() {
        return fatlsucc;
    }

    public void setFatlsucc(BigDecimal fatlsucc) {
        this.fatlsucc = fatlsucc;
    }

    public String getSuccessrate() {
        return successrate;
    }

    public void setSuccessrate(String successrate) {
        this.successrate = successrate;
    }

    public void setBackTextSuccAcc(BigDecimal backTextSuccAcc) {
        this.backTextSuccAcc = backTextSuccAcc;
    }

    public BigDecimal getBackTextSuccAcc() {
        return backTextSuccAcc;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setDiscNo(String discNo) {
        this.discNo = discNo;
    }

    public String getDiscNo() {
        return discNo;
    }

    public void setBatchTaskTime(Date batchTaskTime) {
        this.batchTaskTime = batchTaskTime;
    }

    public Date getBatchTaskTime() {
        return batchTaskTime;
    }

    public void setPcrtEnd(Date pcrtEnd) {
        this.pcrtEnd = pcrtEnd;
    }

    public Date getPcrtEnd() {
        return pcrtEnd;
    }

    public void setTextPath(String textPath) {
        this.textPath = textPath;
    }

    public String getTextPath() {
        return textPath;
    }

    public void setSendId(BigDecimal sendId) {
        this.sendId = sendId;
    }

    public BigDecimal getSendId() {
        return sendId;
    }

    public void setBackTextFailAcc(BigDecimal backTextFailAcc) {
        this.backTextFailAcc = backTextFailAcc;
    }

    public BigDecimal getBackTextFailAcc() {
        return backTextFailAcc;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setSendCount(BigDecimal sendCount) {
        this.sendCount = sendCount;
    }

    public BigDecimal getSendCount() {
        return sendCount;
    }

    public void setBatchTask(BigDecimal batchTask) {
        this.batchTask = batchTask;
    }

    public BigDecimal getBatchTask() {
        return batchTask;
    }

    public void setBackTextTime(Date backTextTime) {
        this.backTextTime = backTextTime;
    }

    public Date getBackTextTime() {
        return backTextTime;
    }

    public void setBatchTaskNum(BigDecimal batchTaskNum) {
        this.batchTaskNum = batchTaskNum;
    }

    public BigDecimal getBatchTaskNum() {
        return batchTaskNum;
    }

    public String getBackText() {
        return backText;
    }

    public void setBackText(String backText) {
        this.backText = backText;
    }

    public void setBackTextFailNum(BigDecimal backTextFailNum) {
        this.backTextFailNum = backTextFailNum;
    }

    public BigDecimal getBackTextFailNum() {
        return backTextFailNum;
    }

    public void setDownloads(BigDecimal downloads) {
        this.downloads = downloads;
    }

    public BigDecimal getDownloads() {
        return downloads;
    }

    public void setBackerId(BigDecimal backerId) {
        this.backerId = backerId;
    }

    public BigDecimal getBackerId() {
        return backerId;
    }

    public void setGroupNum(String groupNum) {
        this.groupNum = groupNum;
    }

    public String getGroupNum() {
        return groupNum;
    }

    public void setBankTextStatus(String bankTextStatus) {
        this.bankTextStatus = bankTextStatus;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    public void setSendAmount(BigDecimal sendAmount) {
        this.sendAmount = sendAmount;
    }

    public BigDecimal getSendAmount() {
        return sendAmount;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setBackTextSuccNum(BigDecimal backTextSuccNum) {
        this.backTextSuccNum = backTextSuccNum;
    }

    public BigDecimal getBackTextSuccNum() {
        return backTextSuccNum;
    }

    public void setBackFileName(String backFileName) {
        this.backFileName = backFileName;
    }

    public String getBackFileName() {
        return backFileName;
    }

    public void setDiskTrailId(BigDecimal diskTrailId) {
        this.diskTrailId = diskTrailId;
    }

    public BigDecimal getDiskTrailId() {
        return diskTrailId;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setSendTextMd(String sendTextMd) {
        this.sendTextMd = sendTextMd;
    }

    public String getSendTextMd() {
        return sendTextMd;
    }

    public String getActualCapOrgId() {
        return actualCapOrgId;
    }

    public void setActualCapOrgId(String actualCapOrgId) {
        this.actualCapOrgId = actualCapOrgId;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setBackTextPath(String backTextPath) {
        this.backTextPath = backTextPath;
    }

    public String getBackTextPath() {
        return backTextPath;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setSenderId(BigDecimal senderId) {
        this.senderId = senderId;
    }

    public BigDecimal getSenderId() {
        return senderId;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public String getChksNames() {
        return chksNames;
    }

    public void setChksNames(String chksNames) {
        this.chksNames = chksNames;
    }

    public String getActualCapOrgName() {
        return actualCapOrgName;
    }

    public void setActualCapOrgName(String actualCapOrgName) {
        this.actualCapOrgName = actualCapOrgName;
    }

    public String getArapFlagName() {
        return arapFlagName;
    }

    public void setArapFlagName(String arapFlagName) {
        this.arapFlagName = arapFlagName;
    }

    public String getChannelTypeName() {
        return channelTypeName;
    }

    public void setChannelTypeName(String channelTypeName) {
        this.channelTypeName = channelTypeName;
    }

    public String getDerivTypeName() {
        return derivTypeName;
    }

    public void setDerivTypeName(String derivTypeName) {
        this.derivTypeName = derivTypeName;
    }

    public String getBankTextStatusName() {
        return banktextStatusName;
    }

    public void setBankTextStatusName(String banktextStatusName) {
        this.banktextStatusName = banktextStatusName;
    }
    
    public List<String> getCapOrganCodeList() {
        return capOrganCodeList;
    }

    public void setCapOrganCodeList(List<String> capOrganCodeList) {
        this.capOrganCodeList = capOrganCodeList;
    }

    public List<String> getBranchCodeCodeList() {
        return branchCodeCodeList;
    }

    public void setBranchCodeCodeList(List<String> branchCodeCodeList) {
        this.branchCodeCodeList = branchCodeCodeList;
    }

    @Override
    public String toString() {

        return "BankTextBO [backTextSuccAcc=" + backTextSuccAcc + ", createTime=" + createTime + ", discNo=" + discNo
                + ", batchTaskTime=" + batchTaskTime + ", pcrtEnd=" + pcrtEnd + ", textPath=" + textPath + ", sendId="
                + sendId + ", backTextFailAcc=" + backTextFailAcc + ", channelType=" + channelType + ", sendCount="
                + sendCount + ", batchTask=" + batchTask + ", backTextTime=" + backTextTime + ", batchTaskNum="
                + batchTaskNum + ", backText=" + backText + ", backTextFailNum=" + backTextFailNum + ", downloads="
                + downloads + ", backerId=" + backerId + ", groupNum=" + groupNum + ", bankTextStatus=" + bankTextStatus
                + ", sendAmount=" + sendAmount + ", uploadTime=" + uploadTime + ", backTextSuccNum=" + backTextSuccNum
                + ", backFileName=" + backFileName + ", derivType=" + derivType + ", insertTime=" + insertTime
                + ", diskTrailId=" + diskTrailId + ", fileName=" + fileName + ", sendTextMd=" + sendTextMd + ", actualCapOrgId="
                + actualCapOrgId + ", arapFlag=" + arapFlag + ", backTextPath=" + backTextPath + ", bankCode="
                + bankCode + ", senderId=" + senderId + ", successrate=" + successrate + ", fatlsucc=" + fatlsucc
                + ", branchCode=" + branchCode + ", crtstart=" + crtstart + ", crtend=" + crtend + ", backstart="
                + backstart + ", backend=" + backend + ", chksNames=" + chksNames + "]";

    }

}
