package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description  付费凭证展示BO对象
 * <AUTHOR>
 * @date 2015-05-21 18:06:25
 * @.belongToModule 收付费-付费凭证打印
 */
public class VoucherinfoBO extends BaseBO {
    
    /** 
    * 序列号
    */ 
    
    private static final long serialVersionUID = -2332053476558177620L;
    /**
     *  投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     *  收付款人姓名
     */
    private String payeeName;
    /**
     *  一次实收产生的收付号
     */
    private BigDecimal payrefno;
    /**
     *  付费凭证号
     */
    private String voucherId;
    /**
     * 保单号码
     */
    private String policyCode;
    /**
     * 收付款人证件号码
     */
    private String certiCode;
    /**
     *  合计金额
     */
    private BigDecimal feeAmount;
    /**
     * 收付款人证件类型
     */
    private String certiType;

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayrefno(BigDecimal payrefno) {
        this.payrefno = payrefno;
    }

    public BigDecimal getPayrefno() {
        return payrefno;
    }

    public void setVoucherId(String voucherId) {
        this.voucherId = voucherId;
    }

    public String getVoucherId() {
        return voucherId;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "VoucherinfoBO [" + "businessCode=" + businessCode + ", payeeName=" + payeeName + ", payrefno="
                + payrefno + ", voucherId=" + voucherId + ", policyCode=" + policyCode + ", certiCode=" + certiCode
                + ", feeAmount=" + feeAmount + ", certiType=" + certiType + "]";
    }
}
