package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 成本中心配置表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-05-21 18:06:25
 */
public class KostConfigBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 9139218528783186112L;

    /**
     * @Fields paykind : 首续期
     */
    private String paykind;
    /**
     * @Fields salechnl : 01-团险直销,02-个人营销,03-??行代理
     */
    private String salechnl;
    /**
     * @Fields kostId : 流水号，无实际意义
     */
    private BigDecimal kostId;
    /**
     * @Fields kostcode : 成本中心
     */
    private String kostcode;

    public void setSalechnl(String salechnl) {
        this.salechnl = salechnl;
    }

    public String getSalechnl() {
        return salechnl;
    }

    public void setPaykind(String paykind) {
        this.paykind = paykind;
    }

    public String getPaykind() {
        return paykind;
    }

    public void setKostId(BigDecimal kostId) {
        this.kostId = kostId;
    }

    public BigDecimal getKostId() {
        return kostId;
    }

    public void setKostcode(String kostcode) {
        this.kostcode = kostcode;
    }

    public String getKostcode() {
        return kostcode;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "KostConfigBO [" + "salechnl=" + salechnl + ", paykind=" + paykind + ", kostId=" + kostId
                + ", kostcode=" + kostcode + "]";
    }
}
