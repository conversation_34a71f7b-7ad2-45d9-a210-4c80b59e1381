package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 支票信息表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-07-28 17:08:37
 */
public class ChequeBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = 7128841580763503851L;
    /**
     * @Fields receiptBy : 打印操作人
     */
    private String receiptBy;
    /**
     * @Fields receiptTime : 打印操作时间
     */
    private Date receiptTime;
    /**
     * @Fields receiptCount : 打印次数
     */
    private BigDecimal receiptCount;
    /**
     * @Fields chequeBanlance : 支票余额
     */
    private BigDecimal chequeBanlance;
    /**
     * @Fields chequeCompany : 支票单位名称
     */
    private String chequeCompany;
    /**
     * @Fields comfirmDate : 支票确认日期
     */
    private Date comfirmDate;
    /**
     * @Fields invoiceDate : 开票日期
     */
    private Date invoiceDate;
    /**
     * @Fields returnChequeCode : 退票原因代码
     */
    private BigDecimal returnChequeCode;
    /**
     * @Fields returnChequeRsn : 退票原因
     */
    private String returnChequeRsn;
    /**
     * @Fields chequeId : 流水号
     */
    private BigDecimal chequeId;
    /**
     * @Fields arapFlag : 收付类型
     */
    private String arapFlag;
    /**
     * @Fields comfirmBy : 支票确认人
     */
    private BigDecimal comfirmBy;
    /**
     * @Fields bankCode : 支票银行编码
     */
    private String bankCode;

    /**
     * @Fields chequeStatus : 支票状态 01:已登记 02:已确认 03:已退票
     */
    private String chequeStatus;
    /**
     * @Fields chequeNo : 支票号码
     */
    private String chequeNo;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields chequeStartDate : 起期
     */
    private Date chequeStartDate;
    /**
     * @Fields chequeEndDate : 止期
     */
    private Date chequeEndDate;

    /**
     * @Fields arapDate : 收付费日期
     */
    private Date arapDate;
    
    /**
     *  @Fields balanceCheck :是否是已经存在的支票 :1:0  存在：不存在
     */
    private String balanceCheck;
    /**
     * @Fields 支票余额类型
     */
    private String  balanceTypeExist;
    
    /** 
     * @Fields corpAction : 企业方账户
     */ 
     private String corpAction;
     
     /** 
     * @Fields companyBankCode : 企业方开户行编码
     */ 
     private String companyBankCode;

     public String getCorpAction() {
         return corpAction;
     }

     public void setCorpAction(String corpAction) {
         this.corpAction = corpAction;
     }

     public String getCompanyBankCode() {
         return companyBankCode;
     }

     public void setCompanyBankCode(String companyBankCode) {
         this.companyBankCode = companyBankCode;
     }

    public String getBalanceTypeExist() {
		return balanceTypeExist;
	}

	public void setBalanceTypeExist(String balanceTypeExist) {
		this.balanceTypeExist = balanceTypeExist;
	}

	public String getBalanceCheck() {
		return balanceCheck;
	}

	public void setBalanceCheck(String balanceCheck) {
		this.balanceCheck = balanceCheck;
	}

	public void setReceiptCount(BigDecimal receiptCount) {
		this.receiptCount = receiptCount;
	}

	public BigDecimal getChequeBanlance() {
		return chequeBanlance;
	}

	public void setChequeBanlance(BigDecimal chequeBanlance) {
		this.chequeBanlance = chequeBanlance;
	}

	public void setChequeCompany(String chequeCompany) {
        this.chequeCompany = chequeCompany;
    }

    public String getReceiptBy() {
		return receiptBy;
	}

	public void setReceiptBy(String receiptBy) {
		this.receiptBy = receiptBy;
	}

	public Date getReceiptTime() {
		return receiptTime;
	}

	public void setReceiptTime(Date receiptTime) {
		this.receiptTime = receiptTime;
	}

	public BigDecimal getReceiptCount() {
		return receiptCount;
	}


    public String getChequeCompany() {
        return chequeCompany;
    }

    public void setComfirmDate(Date comfirmDate) {
        this.comfirmDate = comfirmDate;
    }

    public Date getComfirmDate() {
        return comfirmDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setReturnChequeCode(BigDecimal returnChequeCode) {
        this.returnChequeCode = returnChequeCode;
    }

    public BigDecimal getReturnChequeCode() {
        return returnChequeCode;
    }

    public void setReturnChequeRsn(String returnChequeRsn) {
        this.returnChequeRsn = returnChequeRsn;
    }

    public String getReturnChequeRsn() {
        return returnChequeRsn;
    }

    public void setChequeId(BigDecimal chequeId) {
        this.chequeId = chequeId;
    }

    public BigDecimal getChequeId() {
        return chequeId;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setComfirmBy(BigDecimal comfirmBy) {
        this.comfirmBy = comfirmBy;
    }

    public BigDecimal getComfirmBy() {
        return comfirmBy;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setChequeStatus(String chequeStatus) {
        this.chequeStatus = chequeStatus;
    }

    public String getChequeStatus() {
        return chequeStatus;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeStartDate(Date chequeStartDate) {
        this.chequeStartDate = chequeStartDate;
    }

    public Date getChequeStartDate() {
        return chequeStartDate;
    }

    public void setChequeEndDate(Date chequeEndDate) {
        this.chequeEndDate = chequeEndDate;
    }

    public Date getChequeEndDate() {
        return chequeEndDate;
    }

    public Date getArapDate() {
        return arapDate;
    }

    public void setArapDate(Date arapDate) {
        this.arapDate = arapDate;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ChequeBO [chequeCompany=" + chequeCompany + ", comfirmDate=" + comfirmDate + ", invoiceDate="
                + invoiceDate + ", returnChequeCode=" + returnChequeCode + ", returnChequeRsn=" + returnChequeRsn
                + ", chequeId=" + chequeId + ", arapFlag=" + arapFlag + ", comfirmBy=" + comfirmBy + ", bankCode="
                + bankCode + ", feeAmount=" + feeAmount + ", chequeStatus=" + chequeStatus + ", chequeNo=" + chequeNo
                + ", chequeStartDate=" + chequeStartDate + ", chequeEndDate=" + chequeEndDate + ", arapDate="
                + arapDate + "]";
    }

}
