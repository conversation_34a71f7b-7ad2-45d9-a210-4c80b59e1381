/**
 * @Fields field:field:{todo}
 */
package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;
/**
 * <AUTHOR>
 * @.belongToModule 收付费
 * @date 2020年7月17日下午3:21:56
 * @description 
 */
public class PremScanLogBO extends BaseBO {

	private static final long serialVersionUID = 1L;
	/**
	 * 应收付业务流水标识
	 */
	private String unitNumber;
	/**
	 * 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
	 */
	private String businessCode;
	/**
	 * 影像业务类型
	 */
	private BigDecimal scanType;
	/** 
     * 是否上传影像标识
     */ 
    private BigDecimal scanFlag;
	

	public String getUnitNumber() {
		return unitNumber;
	}

	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public BigDecimal getScanType() {
		return scanType;
	}

	public void setScanType(BigDecimal scanType) {
		this.scanType = scanType;
	}

	public BigDecimal getScanFlag() {
		return scanFlag;
	}

	public void setScanFlag(BigDecimal scanFlag) {
		this.scanFlag = scanFlag;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
