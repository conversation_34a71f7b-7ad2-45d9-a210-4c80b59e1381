package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description LaspaypersonVO对象
 * <AUTHOR>
 * @date 2018-11-14 14:48:52
 */
public class LaspaypersonBO extends BaseBO {
	private static final long serialVersionUID = 1L;
	/**
	 * @Fields scDate : 数据生成日期
	 */
	private Date scDate;
	/**
	 * @Fields lastpaytodate : 原交至日期
	 */
	private Date lastpaytodate;
	/**
	 * @Fields distict : 区
	 */
	private String distict;
	/**
	 * @Fields udflag : 更新/删除标识
	 */
	private String udflag;
	/**
	 * @Fields agentcode : 代理人编码
	 */
	private String agentcode;
	/**
	 * @Fields contno : 合同号码
	 */
	private String contno;
	/**
	 * @Fields poltype : 保单类型
	 */
	private String poltype;
	/**
	 * @Fields flag : 理赔挂起标记
	 */
	private String flag;
	/**
	 * @Fields department : 部
	 */
	private String department;
	/**
	 * @Fields polno : 保单号码
	 */
	private String polno;
	/**
	 * @Fields managecom : 管理机构
	 */
	private String managecom;
	/**
	 * @Fields branchseries : 展业机构序列编码
	 */
	private String branchseries;
	/**
	 * @Fields sumactupaymoney : 总实交金额
	 */
	private BigDecimal sumactupaymoney;
	/**
	 * @Fields riskcode : 险种编码
	 */
	private String riskcode;
	/**
	 * @Fields flag1 : 理赔终止标记
	 */
	private String flag1;
	/**
	 * @Fields dutycode : 责任编码
	 */
	private String dutycode;
	/**
	 * @Fields payaimclass : 交费目的分类
	 */
	private String payaimclass;
	/**
	 * @Fields approvedate : 复核日期
	 */
	private Date approvedate;
	/**
	 * @Fields paytype : 交费类型
	 */
	private String paytype;
	/**
	 * @Fields flag2 : 减额缴清保单标记
	 */
	private String flag2;
	/**
	 * @Fields payplancode : 交费计划编码
	 */
	private String payplancode;
	/**
	 * @Fields listid : 流水号
	 */
	private BigDecimal listid;
	/**
	 * @Fields curpaytodate : 现交至日期
	 */
	private Date curpaytodate;
	/**
	 * @Fields actupayflag : 实收标记
	 */
	private String actupayflag;
	/**
	 * @Fields getnoticeno : 通知书号码
	 */
	private String getnoticeno;
	/**
	 * @Fields paycount : 第几次交费
	 */
	private BigDecimal paycount;
	/**
	 * @Fields mainpolyear : 主险保单年度
	 */
	private BigDecimal mainpolyear;
	/**
	 * @Fields riskchnl : 险种渠道
	 */
	private String riskchnl;
	/**
	 * @Fields paytypeflag : 续保收费标记
	 */
	private String paytypeflag;
	/**
	 * @Fields paydate : 交费日期
	 */
	private Date paydate;
	/**
	 * @Fields actupaydate : 实收日期
	 */
	private Date actupaydate;
	/**
	 * @Fields agentgroup : 代理人组别
	 */
	private String agentgroup;
	/**
	 * @Fields sumduepaymoney : 总应交金额
	 */
	private BigDecimal sumduepaymoney;
	/**
	 * @Fields payintv : 交费间隔
	 */
	private String payintv;
	/**
	 * @Fields scTab : null
	 */
	private String scTab;
	/**
	 * @Fields changedate : 变更日期
	 */
	private Date changedate;
	/**
	 * @Fields liabilitystate : 保单状态
	 */
	private BigDecimal liabilitystate;
	/**
	 * @Fields endcause : 终止原因
	 */
	private String endcause;
	/**
	 * @Fields currentAgent : 当前代理人
	 */
	private String currentAgent;
	/**
	 * @Fields channelType : 销售渠道
	 */
	private String channelType;
	/**
	 * @Fields type : 数据类型
	 */
	private String type;
	/**
	 * @Fields BigDecimal : 保障期间类型
	 */
	private BigDecimal coverperiodtype;
	/**
	 * @Fields String : 险种转换标识
	 */
	private String renewflag;
	/**
	 * @Fields String : 满期日
	 */
	private Date maturitydate;
	/**
	 * @Fields salechnlcode : 渠道码
	 */
	private String salechnlcode;
	/**
	 * @Fields signdate : 签单日期
	 */
	private Date signdate;
	/**
	 * @Fields batchdate : 批处理指定执行日
	 */
	private String batchdate;
	/**
	 * @Fields policyCodeList : 批处理指定保单号
	 */
	private List<String> policyCodeList;
	/**
	 * @Fields policyCodeList : 批处理指定分公司
	 */
	private List<String> branchCodeList;

	public void setScDate(Date scDate) {
		this.scDate = scDate;
	}

	public Date getScDate() {
		return scDate;
	}

	public void setLastpaytodate(Date lastpaytodate) {
		this.lastpaytodate = lastpaytodate;
	}

	public Date getLastpaytodate() {
		return lastpaytodate;
	}

	public void setDistict(String distict) {
		this.distict = distict;
	}

	public String getDistict() {
		return distict;
	}

	public void setUdflag(String udflag) {
		this.udflag = udflag;
	}

	public String getUdflag() {
		return udflag;
	}

	public void setAgentcode(String agentcode) {
		this.agentcode = agentcode;
	}

	public String getAgentcode() {
		return agentcode;
	}

	public void setContno(String contno) {
		this.contno = contno;
	}

	public String getContno() {
		return contno;
	}

	public void setPoltype(String poltype) {
		this.poltype = poltype;
	}

	public String getPoltype() {
		return poltype;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getFlag() {
		return flag;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getDepartment() {
		return department;
	}

	public void setPolno(String polno) {
		this.polno = polno;
	}

	public String getPolno() {
		return polno;
	}

	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}

	public String getManagecom() {
		return managecom;
	}

	public String getBranchseries() {
		return branchseries;
	}

	public void setBranchseries(String branchseries) {
		this.branchseries = branchseries;
	}

	public void setSumactupaymoney(BigDecimal sumactupaymoney) {
		this.sumactupaymoney = sumactupaymoney;
	}

	public BigDecimal getSumactupaymoney() {
		return sumactupaymoney;
	}

	public void setRiskcode(String riskcode) {
		this.riskcode = riskcode;
	}

	public String getRiskcode() {
		return riskcode;
	}

	public void setFlag1(String flag1) {
		this.flag1 = flag1;
	}

	public String getFlag1() {
		return flag1;
	}

	public void setDutycode(String dutycode) {
		this.dutycode = dutycode;
	}

	public String getDutycode() {
		return dutycode;
	}

	public void setPayaimclass(String payaimclass) {
		this.payaimclass = payaimclass;
	}

	public String getPayaimclass() {
		return payaimclass;
	}

	public void setApprovedate(Date approvedate) {
		this.approvedate = approvedate;
	}

	public Date getApprovedate() {
		return approvedate;
	}

	public void setPaytype(String paytype) {
		this.paytype = paytype;
	}

	public String getPaytype() {
		return paytype;
	}

	public void setFlag2(String flag2) {
		this.flag2 = flag2;
	}

	public String getFlag2() {
		return flag2;
	}

	public void setPayplancode(String payplancode) {
		this.payplancode = payplancode;
	}

	public String getPayplancode() {
		return payplancode;
	}

	public void setListid(BigDecimal listid) {
		this.listid = listid;
	}

	public BigDecimal getListid() {
		return listid;
	}

	public void setCurpaytodate(Date curpaytodate) {
		this.curpaytodate = curpaytodate;
	}

	public Date getCurpaytodate() {
		return curpaytodate;
	}

	public void setActupayflag(String actupayflag) {
		this.actupayflag = actupayflag;
	}

	public String getActupayflag() {
		return actupayflag;
	}

	public void setGetnoticeno(String getnoticeno) {
		this.getnoticeno = getnoticeno;
	}

	public String getGetnoticeno() {
		return getnoticeno;
	}

	public void setPaycount(BigDecimal paycount) {
		this.paycount = paycount;
	}

	public BigDecimal getPaycount() {
		return paycount;
	}

	public void setMainpolyear(BigDecimal mainpolyear) {
		this.mainpolyear = mainpolyear;
	}

	public BigDecimal getMainpolyear() {
		return mainpolyear;
	}

	public void setRiskchnl(String riskchnl) {
		this.riskchnl = riskchnl;
	}

	public String getRiskchnl() {
		return riskchnl;
	}

	public void setPaytypeflag(String paytypeflag) {
		this.paytypeflag = paytypeflag;
	}

	public String getPaytypeflag() {
		return paytypeflag;
	}

	public void setPaydate(Date paydate) {
		this.paydate = paydate;
	}

	public Date getPaydate() {
		return paydate;
	}

	public Date getActupaydate() {
		return actupaydate;
	}

	public void setActupaydate(Date actupaydate) {
		this.actupaydate = actupaydate;
	}

	public void setAgentgroup(String agentgroup) {
		this.agentgroup = agentgroup;
	}

	public String getAgentgroup() {
		return agentgroup;
	}

	public void setSumduepaymoney(BigDecimal sumduepaymoney) {
		this.sumduepaymoney = sumduepaymoney;
	}

	public BigDecimal getSumduepaymoney() {
		return sumduepaymoney;
	}

	public void setPayintv(String payintv) {
		this.payintv = payintv;
	}

	public String getPayintv() {
		return payintv;
	}

	public String getScTab() {
		return scTab;
	}

	public void setScTab(String scTab) {
		this.scTab = scTab;
	}

	public Date getChangedate() {
		return changedate;
	}

	public void setChangedate(Date changedate) {
		this.changedate = changedate;
	}

	public BigDecimal getLiabilitystate() {
		return liabilitystate;
	}

	public void setLiabilitystate(BigDecimal liabilitystate) {
		this.liabilitystate = liabilitystate;
	}

	public String getEndcause() {
		return endcause;
	}

	public void setEndcause(String endcause) {
		this.endcause = endcause;
	}

	public String getCurrentAgent() {
		return currentAgent;
	}

	public void setCurrentAgent(String currentAgent) {
		this.currentAgent = currentAgent;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public BigDecimal getCoverperiodtype() {
		return coverperiodtype;
	}

	public void setCoverperiodtype(BigDecimal coverperiodtype) {
		this.coverperiodtype = coverperiodtype;
	}

	public String getRenewflag() {
		return renewflag;
	}

	public void setRenewflag(String renewflag) {
		this.renewflag = renewflag;
	}

	public Date getMaturitydate() {
		return maturitydate;
	}

	public void setMaturitydate(Date maturitydate) {
		this.maturitydate = maturitydate;
	}

	public String getSalechnlcode() {
		return salechnlcode;
	}

	public void setSalechnlcode(String salechnlcode) {
		this.salechnlcode = salechnlcode;
	}

	public Date getSigndate() {
		return signdate;
	}

	public void setSigndate(Date signdate) {
		this.signdate = signdate;
	}

	public List<String> getPolicyCodeList() {
		return policyCodeList;
	}

	public void setPolicyCodeList(List<String> policyCodeList) {
		this.policyCodeList = policyCodeList;
	}

	public String getBatchdate() {
		return batchdate;
	}

	public void setBatchdate(String batchdate) {
		this.batchdate = batchdate;
	}

	public List<String> getBranchCodeList() {
		return branchCodeList;
	}

	public void setBranchCodeList(List<String> branchCodeList) {
		this.branchCodeList = branchCodeList;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "LaspaypersonBO [" + "scDate=" + scDate + "," + "lastpaytodate="
				+ lastpaytodate + "," + "distict=" + distict + "," + "udflag="
				+ udflag + "," + "agentcode=" + agentcode + "," + "contno="
				+ contno + "," + "poltype=" + poltype + "," + "flag=" + flag
				+ "," + "department=" + department + "," + "polno=" + polno
				+ "," + "managecom=" + managecom + "," + "branchseries="
				+ branchseries + "," + "sumactupaymoney=" + sumactupaymoney
				+ "," + "riskcode=" + riskcode + "," + "flag1=" + flag1 + ","
				+ "dutycode=" + dutycode + "," + "payaimclass=" + payaimclass
				+ "," + "approvedate=" + approvedate + "," + "paytype="
				+ paytype + "," + "flag2=" + flag2 + "," + "payplancode="
				+ payplancode + "," + "listid=" + listid + ","
				+ "curpaytodate=" + curpaytodate + "," + "actupayflag="
				+ actupayflag + "," + "getnoticeno=" + getnoticeno + ","
				+ "paycount=" + paycount + "," + "mainpolyear=" + mainpolyear
				+ "," + "riskchnl=" + riskchnl + "," + "paytypeflag="
				+ paytypeflag + "," + "paydate=" + paydate + ","
				+ "actupaydate=" + actupaydate + "," + "agentgroup="
				+ agentgroup + "," + "sumduepaymoney=" + sumduepaymoney + ","
				+ "payintv=" + payintv + "scTab=" + scTab + "changedate="
				+ changedate + "liabilitystate=" + liabilitystate + "endcause="
				+ endcause + "currentAgent=" + currentAgent + "channelType="
				+ channelType + "type=" + type + "]";
	}

}
