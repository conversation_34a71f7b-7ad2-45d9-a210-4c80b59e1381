package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description ReceiptBO对象
 * <AUTHOR>
 * @date 2015-11-23 16:33:41
 * @.belongToModule 收付费-发票打印
 */
public class ReceiptBO extends BaseBO {
    /** 
    * @Fields serialVersionUID : 序列化版本号
    */ 
    
    private static final long serialVersionUID = 1870072185845442981L;
    /**
     * @Fields businessCode : 业务代码
     */
    private String businessCode;
    /**
     * @Fields payeeName : 缴费人姓名
     */
    private String payeeName;
    /**
     * @Fields policyOrganCode : 保单所属机构
     */
    private String policyOrganCode;
    /**
     * @Fields drawDate : 开票日期
     */
    private Date drawDate;
    /**
     * @Fields holderName : 投保人名称
     */
    private String holderName;
    /**
     * @Fields batchNo : 物流批次号
     */
    private String batchNo;
    /**
     * @Fields detailId : 费用明细
     */
    private BigDecimal detailId;
    /**
     * @Fields receiptId : 主键
     */
    private BigDecimal receiptId;
    /**
     * @Fields verifyCode : 验证码
     */
    private String verifyCode;
    /**
     * @Fields finishTime : 交费日期
     */
    private Date finishTime;
    /**
     * @Fields paidCount : 交费次数
     */
    private BigDecimal paidCount;
    /**
     * @Fields feeOrgCode : 收款单位
     */
    private String feeOrgCode;
    /**
     * @Fields mainPremium : 主险金额
     */
    private BigDecimal mainPremium;
    /**
     * @Fields organCode : 机构号
     */
    private String organCode;
    /**
     * @Fields searchCode : 查询码
     */
    private String searchCode;
    /**
     * @Fields isReversal : 是否冲正发票
     */
    private BigDecimal isReversal;
    /**
     * @Fields groupName : 产品组合名称
     */
    private String groupName;
    /**
     * @Fields invoiceCode : 机打发票代码
     */
    private String invoiceCode;
    /**
     * @Fields additionPremium : 附加险金额
     */
    private BigDecimal additionPremium;
    /**
     * @Fields feeAmount : 金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields serviceCode : 付款项目
     */
    private String serviceCode;
    /**
     * @Fields printTimes : 补打次数
     */
    private BigDecimal printTimes;
    /**
     * @Fields industryCategory : 行业分类
     */
    private String industryCategory;
    /**
     * @Fields invoiceNo : 机打发票号
     */
    private String invoiceNo;
    /**
     * @Fields docId : 单证ID
     */
    private String docId;
    /**
     * @Fields payeeId : 收付款人 ID
     */
    private BigDecimal payeeId;
    /**
     * @Fields isBatch : 是否外包打印
     */
    private BigDecimal isBatch;
    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;
    /**
     * @Fields checkCode : 校验码
     */
    private String checkCode;
    /**
     * @Fields csName : 保全项目名称
     */
    private String csName;
    /**
     * @Fields policyCode : 保单号
     */
    private String policyCode;
    /**
     * @Fields receiptStatus : 打印状态 01;可打印 02;已打印 03;打印中 04;已作废 05;已红冲
     */
    private String receiptStatus;
    /**
     * @Fields businessType : 业务类型
     */
    private String businessType;
    /**
     * @Fields agentCode : 代理人CODE
     */
    private String agentCode;
    /**
     * @Fields groupCode : 产品组合代码
     */
    private String groupCode;
    /**
     * @Fields premFreq : 交费方式
     */
    private BigDecimal premFreq;
    /**
     * @Fields taskMark : 票据任务标示
     */
    private BigDecimal taskMark;
    /**
     * @Fields express_code : 快递单号
     */
    private String expressCode;
    /**
     * @Fields express_code : 实收发票号
     */
    private String realReceiptNo;
    /**
     *  @Fields express_code : 质检结果
     */
    private BigDecimal qcStatus;
    
    public BigDecimal getQcStatus() {
        return qcStatus;
    }

    public void setQcStatus(BigDecimal qcStatus) {
        this.qcStatus = qcStatus;
    }

    public String getRealReceiptNo() {
        return realReceiptNo;
    }

    public void setRealReceiptNo(String realReceiptNo) {
        this.realReceiptNo = realReceiptNo;
    }

    public String getExpress_code() {
        return expressCode;
    }

    public void setExpress_code(String express_code) {
        this.expressCode = express_code;
    }

    public BigDecimal getTaskMark() {
        return taskMark;
    }

    public void setTaskMark(BigDecimal taskMark) {
        this.taskMark = taskMark;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public void setDrawDate(Date drawDate) {
        this.drawDate = drawDate;
    }

    public Date getDrawDate() {
        return drawDate;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setDetailId(BigDecimal detailId) {
        this.detailId = detailId;
    }

    public BigDecimal getDetailId() {
        return detailId;
    }

    public void setReceiptId(BigDecimal receiptId) {
        this.receiptId = receiptId;
    }

    public BigDecimal getReceiptId() {
        return receiptId;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public void setFeeOrgCode(String feeOrgCode) {
        this.feeOrgCode = feeOrgCode;
    }

    public String getFeeOrgCode() {
        return feeOrgCode;
    }

    public void setMainPremium(BigDecimal mainPremium) {
        this.mainPremium = mainPremium;
    }

    public BigDecimal getMainPremium() {
        return mainPremium;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    public String getSearchCode() {
        return searchCode;
    }

    public void setIsReversal(BigDecimal isReversal) {
        this.isReversal = isReversal;
    }

    public BigDecimal getIsReversal() {
        return isReversal;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setAdditionPremium(BigDecimal additionPremium) {
        this.additionPremium = additionPremium;
    }

    public BigDecimal getAdditionPremium() {
        return additionPremium;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setPrintTimes(BigDecimal printTimes) {
        this.printTimes = printTimes;
    }

    public BigDecimal getPrintTimes() {
        return printTimes;
    }

    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    public String getIndustryCategory() {
        return industryCategory;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setDocId(String docId) {
        this.docId = docId;
    }

    public String getDocId() {
        return docId;
    }

    public void setPayeeId(BigDecimal payeeId) {
        this.payeeId = payeeId;
    }

    public BigDecimal getPayeeId() {
        return payeeId;
    }

    public void setIsBatch(BigDecimal isBatch) {
        this.isBatch = isBatch;
    }

    public BigDecimal getIsBatch() {
        return isBatch;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public String getCsName() {
        return csName;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setReceiptStatus(String receiptStatus) {
        this.receiptStatus = receiptStatus;
    }

    public String getReceiptStatus() {
        return receiptStatus;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ReceiptVO [" + "businessCode=" + businessCode + "," + "payeeName=" + payeeName + ","
                + "policyOrganCode=" + policyOrganCode + "," + "drawDate=" + drawDate + "," + "holderName="
                + holderName + "," + "batchNo=" + batchNo + "," + "detailId=" + detailId + "," + "receiptId="
                + receiptId + "," + "verifyCode=" + verifyCode + "," + "finishTime=" + finishTime + "," + "paidCount="
                + paidCount + "," + "feeOrgCode=" + feeOrgCode + "," + "mainPremium=" + mainPremium + ","
                + "organCode=" + organCode + "," + "searchCode=" + searchCode + "," + "isReversal=" + isReversal + ","
                + "groupName=" + groupName + "," + "invoiceCode=" + invoiceCode + "," + "additionPremium="
                + additionPremium + "," + "feeAmount=" + feeAmount + "," + "serviceCode=" + serviceCode + ","
                + "printTimes=" + printTimes + "," + "industryCategory=" + industryCategory + "," + "invoiceNo="
                + invoiceNo + "," + "docId=" + docId + "," + "payeeId=" + payeeId + "," + "isBatch=" + isBatch + ","
                + "derivType=" + derivType + "," + "checkCode=" + checkCode + "," + "csName=" + csName + ","
                + "policyCode=" + policyCode + "," + "receiptStatus=" + receiptStatus + "," + "businessType="
                + businessType + "," + "agentCode=" + agentCode + "," + "groupCode=" + groupCode + "," + "premFreq="
                + premFreq + "]";
    }
}
