package com.nci.tunan.cap.interfaces.model.bo.statement;

/** 
 * @description 理赔豁免保费日结出险险种信息
 * <AUTHOR> <EMAIL> 
 * @date 2024-2-19 下午1:15:43  
*/
public class Claim {
    
    /**
     * @Fields SERIAL_NUM : 序号
     */
    private String SERIAL_NUM;

    /**
     * @Fields CLAIM_BUSI_PROD_CODE : 出险险种编码
     */
    private String CLAIM_BUSI_PROD_CODE;
    /**
     * @Fields CLAIM_BUSI_PROD_NAME : 出险险种
     */
    private String CLAIM_BUSI_PROD_NAME;

    /**
     * @Fields CHANNEL_TYPE : 销售渠道
     */
    private String CHANNEL_TYPE;

    /**
     * @Fields AGENTCOM : 代理机构
     */
    private String AMOUNT;
    
    /** 
     * @Fields PIECES : 件数
     */ 
     private String PIECES;
    
    public String getSERIAL_NUM() {
        return SERIAL_NUM;
    }

    public void setSERIAL_NUM(String sERIAL_NUM) {
        SERIAL_NUM = sERIAL_NUM;
    }

    public String getCLAIM_BUSI_PROD_CODE() {
        return CLAIM_BUSI_PROD_CODE;
    }

    public void setCLAIM_BUSI_PROD_CODE(String cLAIM_BUSI_PROD_CODE) {
        CLAIM_BUSI_PROD_CODE = cLAIM_BUSI_PROD_CODE;
    }

    public String getCLAIM_BUSI_PROD_NAME() {
        return CLAIM_BUSI_PROD_NAME;
    }

    public void setCLAIM_BUSI_PROD_NAME(String cLAIM_BUSI_PROD_NAME) {
        CLAIM_BUSI_PROD_NAME = cLAIM_BUSI_PROD_NAME;
    }

    public String getCHANNEL_TYPE() {
        return CHANNEL_TYPE;
    }

    public void setCHANNEL_TYPE(String cHANNEL_TYPE) {
        CHANNEL_TYPE = cHANNEL_TYPE;
    }

    public String getAMOUNT() {
        return AMOUNT;
    }

    public void setAMOUNT(String aMOUNT) {
        AMOUNT = aMOUNT;
    }

    public String getPIECES() {
        return PIECES;
    }

    public void setPIECES(String pIECES) {
        PIECES = pIECES;
    }
    
    

}
