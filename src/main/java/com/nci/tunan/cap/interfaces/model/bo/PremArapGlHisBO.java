package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 应收应付记账转储表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR> <EMAIL>
 * @date 2015-07-24 15:57:11
 */
public class PremArapGlHisBO extends BaseBO {
    /**
     * @Fields serialVersionUID : 序列化
     */
    private static final long serialVersionUID = -3879226592761636517L;
    /**
     * @Fields accountGlDate : 记账状态 01：已记账
     */
    private Date accountGlDate;
    /**
     * @Fields arapFlag : 应收应付类型
     */
    private String arapFlag;
    /**
     * @Fields base1 : 基本信息1
     */
    private String base1;
    /**
     * @Fields base10 : 基本信息10
     */
    private String base10;
    /**
     * @Fields base2 : 基本信息2
     */
    private String base2;

    /**
     * @Fields base4 : 基本信息4
     */
    private String base4;
    /**
     * @Fields base5 : 基本信息5
     */
    private String base5;
    /**
     * @Fields base3 : 基本信息3
     */
    private String base3;
    /**
     * @Fields base6 : 基本信息6
     */
    private String base6;
    /**
     * @Fields base7 : 基本信息7
     */
    private String base7;
    /**
     * @Fields base8 : 基本信息8
     */
    private String base8;
    /**
     * @Fields base9 : 基本信息9
     */
    private String base9;
    /**
     * @Fields batchno : 记账批次号
     */
    private String batchno;
    /**
     * @Fields belnr : SAP回写凭证号信息
     */
    private String belnr;
    /**
     * @Fields bookkeepingId : 业务流水号
     */
    private BigDecimal bookkeepingId;
    /**
     * @Fields branchCode : 保单所属分公司
     */
    private String branchCode;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     * @Fields busiProdCode : 险种代码
     */
    private String busiProdCode;
    /**
     * @Fields busiProdName : 险种名称
     */
    private String busiProdName;
    /**
     * @Fields channelType : 个人、银行、团险等
     */
    private String channelType;
    /**
     * @Fields chargeYear : 缴费年期
     */
    private BigDecimal chargeYear;
    /**
     * @Fields crSeg1 : 贷方分析点字段1
     */
    private String crSeg1;
    /**
     * @Fields crSeg10 : 贷方分析点字段10
     */
    private String crSeg10;
    /**
     * @Fields crSeg11 : 贷方分析点字段11
     */
    private String crSeg11;
    /**
     * @Fields crSeg12 : 贷方分析点字段12
     */
    private String crSeg12;
    /**
     * @Fields crSeg13 : 贷方分析点字段13
     */
    private String crSeg13;
    /**
     * @Fields crSeg14 : 贷方分析点字段14
     */
    private String crSeg14;
    /**
     * @Fields crSeg15 : 贷方分析点字段15
     */
    private String crSeg15;
    /**
     * @Fields crSeg16 : 贷方分析点字段16
     */
    private String crSeg16;
    /**
     * @Fields crSeg17 : 贷方分析点字段17
     */
    private String crSeg17;
    /**
     * @Fields crSeg18 : 贷方分析点字段18
     */
    private String crSeg18;
    /**
     * @Fields crSeg19 : 贷方分析点字段19
     */
    private String crSeg19;
    /**
     * @Fields crSeg2 : 贷方分析点字段2
     */
    private String crSeg2;
    /**
     * @Fields crSeg20 : 贷方分析点字段20
     */
    private String crSeg20;
    /**
     * @Fields crSeg3 : 贷方分析点字段3
     */
    private String crSeg3;
    /**
     * @Fields crSeg4 : 贷方分析点字段4
     */
    private String crSeg4;
    /**
     * @Fields crSeg5 : 贷方分析点字段5
     */
    private String crSeg5;
    /**
     * @Fields crSeg6 : 贷方分析点字段6
     */
    private String crSeg6;
    /**
     * @Fields crSeg7 : 贷方分析点字段7
     */
    private String crSeg7;
    /**
     * @Fields crSeg8 : 贷方分析点字段8
     */
    private String crSeg8;
    /**
     * @Fields crSeg9 : 贷方分析点字段9
     */
    private String crSeg9;
    /**
     * @Fields cSerialno : 一个批次号下多条分录数据流水号，全数字组成的流水号不重复?，且同批次号组成唯一标识
     */
    private String cSerialno;
    /**
     * @Fields derivType : 业务来源 001;新契约 003;续保 004;保全 005;理赔
     */
    private String derivType;
    /**
     * @Fields drSeg1 : 借方分析点字段1
     */
    private String drSeg1;
    /**
     * @Fields drSeg10 : 借方分析点字段10
     */
    private String drSeg10;
    /**
     * @Fields drSeg11 : 借方分析点字段11
     */
    private String drSeg11;
    /**
     * @Fields drSeg12 : 借方分析点字段12
     */
    private String drSeg12;
    /**
     * @Fields drSeg13 : 借方分析点字段13
     */
    private String drSeg13;
    /**
     * @Fields drSeg14 : 借方分析点字段14
     */
    private String drSeg14;
    /**
     * @Fields drSeg15 : 借方分析点字段15
     */
    private String drSeg15;
    /**
     * @Fields drSeg16 : 借方分析点字段16
     */
    private String drSeg16;
    /**
     * @Fields drSeg17 : 借方分析点字段17
     */
    private String drSeg17;
    /**
     * @Fields drSeg18 : 借方分析点字段18
     */
    private String drSeg18;
    /**
     * @Fields drSeg19 : 借方分析点字段19
     */
    private String drSeg19;
    /**
     * @Fields drSeg2 : 借方分析点字段2
     */
    private String drSeg2;
    /**
     * @Fields drSeg20 : 借方分析点字段20
     */
    private String drSeg20;
    /**
     * @Fields drSeg3 : 借方分析点字段3
     */
    private String drSeg3;
    /**
     * @Fields drSeg4 : 借方分析点字段4
     */
    private String drSeg4;
    /**
     * @Fields drSeg5 : 借方分析点字段5
     */
    private String drSeg5;
    /**
     * @Fields drSeg6 : 借方分析点字段6
     */
    private String drSeg6;
    /**
     * @Fields drSeg7 : 借方分析点字段7
     */
    private String drSeg7;
    /**
     * @Fields drSeg8 : 借方分析点字段8
     */
    private String drSeg8;
    /**
     * @Fields drSeg9 : 借方分析点字段9
     */
    private String drSeg9;
    /**
     * @Fields dSerialno : 一个批次号下多条分录数据流水号，全数字组成的流水号不重复?，且同批次号组成唯一标识
     */
    private String dSerialno;
    /**
     * @Fields feeAmount : 收付金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields feeType : 费用业务类型 32；实付退费 41；保费收入 参考费用类型列表
     */
    private String feeType;
    /**
     * @Fields filter1 : 过滤1
     */
    private String filter1;
    /**
     * @Fields filter10 : 过滤10
     */
    private String filter10;
    /**
     * @Fields filter2 : 过滤2
     */
    private String filter2;
    /**
     * @Fields filter3 : 过滤3
     */
    private String filter3;
    /**
     * @Fields filter4 : 过滤4
     */
    private String filter4;
    /**
     * @Fields filter5 : 过滤5
     */
    private String filter5;
    /**
     * @Fields filter6 : 过滤6
     */
    private String filter6;
    /**
     * @Fields filter7 : 过滤7
     */
    private String filter7;
    /**
     * @Fields filter8 : 过滤8
     */
    private String filter8;
    /**
     * @Fields filter9 : 过滤9
     */
    private String filter9;
    /**
     * @Fields gjahr : SAP会写会计年度
     */
    private String gjahr;
    /**
     * @Fields grp1 : 分组条件1
     */
    private String grp1;
    /**
     * @Fields grp10 : 分组条件10
     */
    private String grp10;
    /**
     * @Fields grp2 : 分组条件2
     */
    private String grp2;
    /**
     * @Fields grp3 : 分组条件3
     */
    private String grp3;
    /**
     * @Fields grp4 : 分组条件4
     */
    private String grp4;
    /**
     * @Fields grp5 : 分组条件5
     */
    private String grp5;
    /**
     * @Fields grp6 : 分组条件6
     */
    private String grp6;
    /**
     * @Fields grp7 : 分组条件7
     */
    private String grp7;
    /**
     * @Fields grp8 : 分组条件8
     */
    private String grp8;
    /**
     * @Fields grp9 : 分组条件9
     */
    private String grp9;
    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;
    /**
     * @Fields organCode : 系统内往来对方机构 管理机构
     */
    private String organCode;
    /**
     * @Fields paidCount : 缴费次数
     */
    private BigDecimal paidCount;
    /**
     * @Fields policyOrganCode : 保单直属机构
     */
    private String policyOrganCode;
    /**
     * @Fields policyType : 个单、团单
     */
    private String policyType;
    /**
     * @Fields policyYear : 年度当前年度
     */
    private BigDecimal policyYear;
    /**
     * @Fields posted : Y－数据已经到SAP中间表 N－
     */
    private String posted;
    /**
     * @Fields premFreq : 年缴、月缴、趸交等等 1;趸缴 2;月缴 3;季缴 4;半年缴 5;年缴 6;不定期缴 9;其他
     */
    private BigDecimal premFreq;
    /**
     * @Fields productChannel : 产品渠道
     */
    private String productChannel;
    /**
     * @Fields refeflag : 参考项
     */
    private String refeflag;
    /**
     * @Fields serviceCode : 付款项目
     */
    private String serviceCode;
    /**
     * @Fields validateDate : 业务生效日期
     */
    private Date validateDate;

    /**
     * @Fields withdrawType : 收付费类型 01、补费、 02、退费、 03、退保、 04、工本费、 05、利息、 06、满期金、
     *         07、年金、 08、复效、 09、还款、 10、贷款、
     */
    private String withdrawType;
    
    /**
     * @Fields policyCode : 保单号码
     */
    private String policyCode;

    public Date getAccountGlDate() {
        return accountGlDate;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public String getBase1() {
        return base1;
    }

    public String getBase10() {
        return base10;
    }

    public String getBase2() {
        return base2;
    }

    public String getBase3() {
        return base3;
    }

    public String getBase4() {
        return base4;
    }

    public String getBase5() {
        return base5;
    }

    public String getBase6() {
        return base6;
    }

    public String getBase7() {
        return base7;
    }

    public String getBase8() {
        return base8;
    }

    public String getBase9() {
        return base9;
    }

    public String getBatchno() {
        return batchno;
    }

    public String getBelnr() {
        return belnr;
    }

    @Override
    public String getBizId() {
        return null;
    }

    public BigDecimal getBookkeepingId() {
        return bookkeepingId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public String getBusiProdCode() {
        return busiProdCode;
    }

    public String getBusiProdName() {
        return busiProdName;
    }

    public String getChannelType() {
        return channelType;
    }

    public BigDecimal getChargeYear() {
        return chargeYear;
    }

    public String getCrSeg1() {
        return crSeg1;
    }

    public String getCrSeg10() {
        return crSeg10;
    }

    public String getCrSeg11() {
        return crSeg11;
    }

    public String getCrSeg12() {
        return crSeg12;
    }

    public String getCrSeg13() {
        return crSeg13;
    }

    public String getCrSeg14() {
        return crSeg14;
    }

    public String getCrSeg15() {
        return crSeg15;
    }

    public String getCrSeg16() {
        return crSeg16;
    }

    public String getCrSeg17() {
        return crSeg17;
    }

    public String getCrSeg18() {
        return crSeg18;
    }

    public String getCrSeg19() {
        return crSeg19;
    }

    public String getCrSeg2() {
        return crSeg2;
    }

    public String getCrSeg20() {
        return crSeg20;
    }

    public String getCrSeg3() {
        return crSeg3;
    }

    public String getCrSeg4() {
        return crSeg4;
    }

    public String getCrSeg5() {
        return crSeg5;
    }

    public String getCrSeg6() {
        return crSeg6;
    }

    public String getCrSeg7() {
        return crSeg7;
    }

    public String getCrSeg8() {
        return crSeg8;
    }

    public String getCrSeg9() {
        return crSeg9;
    }

    public String getCSerialno() {
        return cSerialno;
    }

    public String getDerivType() {
        return derivType;
    }

    public String getDrSeg1() {
        return drSeg1;
    }

    public String getDrSeg10() {
        return drSeg10;
    }

    public String getDrSeg11() {
        return drSeg11;
    }

    public String getDrSeg12() {
        return drSeg12;
    }

    public String getDrSeg13() {
        return drSeg13;
    }

    public String getDrSeg14() {
        return drSeg14;
    }

    public String getDrSeg15() {
        return drSeg15;
    }

    public String getDrSeg16() {
        return drSeg16;
    }

    public String getDrSeg17() {
        return drSeg17;
    }

    public String getDrSeg18() {
        return drSeg18;
    }

    public String getDrSeg19() {
        return drSeg19;
    }

    public String getDrSeg2() {
        return drSeg2;
    }

    public String getDrSeg20() {
        return drSeg20;
    }

    public String getDrSeg3() {
        return drSeg3;
    }

    public String getDrSeg4() {
        return drSeg4;
    }

    public String getDrSeg5() {
        return drSeg5;
    }

    public String getDrSeg6() {
        return drSeg6;
    }

    public String getDrSeg7() {
        return drSeg7;
    }

    public String getDrSeg8() {
        return drSeg8;
    }

    public String getDrSeg9() {
        return drSeg9;
    }

    public String getDSerialno() {
        return dSerialno;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public String getFeeType() {
        return feeType;
    }

    public String getFilter1() {
        return filter1;
    }

    public String getFilter10() {
        return filter10;
    }

    public String getFilter2() {
        return filter2;
    }

    public String getFilter3() {
        return filter3;
    }

    public String getFilter4() {
        return filter4;
    }

    public String getFilter5() {
        return filter5;
    }

    public String getFilter6() {
        return filter6;
    }

    public String getFilter7() {
        return filter7;
    }

    public String getFilter8() {
        return filter8;
    }

    public String getFilter9() {
        return filter9;
    }

    public String getGjahr() {
        return gjahr;
    }

    public String getGrp1() {
        return grp1;
    }

    public String getGrp10() {
        return grp10;
    }

    public String getGrp2() {
        return grp2;
    }

    public String getGrp3() {
        return grp3;
    }

    public String getGrp4() {
        return grp4;
    }

    public String getGrp5() {
        return grp5;
    }

    public String getGrp6() {
        return grp6;
    }

    public String getGrp7() {
        return grp7;
    }

    public String getGrp8() {
        return grp8;
    }

    public String getGrp9() {
        return grp9;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public BigDecimal getPaidCount() {
        return paidCount;
    }

    public String getPolicyOrganCode() {
        return policyOrganCode;
    }

    public String getPolicyType() {
        return policyType;
    }

    public BigDecimal getPolicyYear() {
        return policyYear;
    }

    public String getPosted() {
        return posted;
    }

    public BigDecimal getPremFreq() {
        return premFreq;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public String getRefeflag() {
        return refeflag;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public String getWithdrawType() {
        return withdrawType;
    }

    public void setAccountGlDate(Date accountGlDate) {
        this.accountGlDate = accountGlDate;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public void setBase1(String base1) {
        this.base1 = base1;
    }

    public void setBase10(String base10) {
        this.base10 = base10;
    }

    public void setBase2(String base2) {
        this.base2 = base2;
    }

    public void setBase3(String base3) {
        this.base3 = base3;
    }

    public void setBase4(String base4) {
        this.base4 = base4;
    }

    public void setBase5(String base5) {
        this.base5 = base5;
    }

    public void setBase6(String base6) {
        this.base6 = base6;
    }

    public void setBase7(String base7) {
        this.base7 = base7;
    }

    public void setBase8(String base8) {
        this.base8 = base8;
    }

    public void setBase9(String base9) {
        this.base9 = base9;
    }

    public void setBatchno(String batchno) {
        this.batchno = batchno;
    }

    public void setBelnr(String belnr) {
        this.belnr = belnr;
    }

    public void setBookkeepingId(BigDecimal bookkeepingId) {
        this.bookkeepingId = bookkeepingId;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public void setBusiProdCode(String busiProdCode) {
        this.busiProdCode = busiProdCode;
    }

    public void setBusiProdName(String busiProdName) {
        this.busiProdName = busiProdName;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public void setChargeYear(BigDecimal chargeYear) {
        this.chargeYear = chargeYear;
    }

    public void setCrSeg1(String crSeg1) {
        this.crSeg1 = crSeg1;
    }

    public void setCrSeg10(String crSeg10) {
        this.crSeg10 = crSeg10;
    }

    public void setCrSeg11(String crSeg11) {
        this.crSeg11 = crSeg11;
    }

    public void setCrSeg12(String crSeg12) {
        this.crSeg12 = crSeg12;
    }

    public void setCrSeg13(String crSeg13) {
        this.crSeg13 = crSeg13;
    }

    public void setCrSeg14(String crSeg14) {
        this.crSeg14 = crSeg14;
    }

    public void setCrSeg15(String crSeg15) {
        this.crSeg15 = crSeg15;
    }

    public void setCrSeg16(String crSeg16) {
        this.crSeg16 = crSeg16;
    }

    public void setCrSeg17(String crSeg17) {
        this.crSeg17 = crSeg17;
    }

    public void setCrSeg18(String crSeg18) {
        this.crSeg18 = crSeg18;
    }

    public void setCrSeg19(String crSeg19) {
        this.crSeg19 = crSeg19;
    }

    public void setCrSeg2(String crSeg2) {
        this.crSeg2 = crSeg2;
    }

    public void setCrSeg20(String crSeg20) {
        this.crSeg20 = crSeg20;
    }

    public void setCrSeg3(String crSeg3) {
        this.crSeg3 = crSeg3;
    }

    public void setCrSeg4(String crSeg4) {
        this.crSeg4 = crSeg4;
    }

    public void setCrSeg5(String crSeg5) {
        this.crSeg5 = crSeg5;
    }

    public void setCrSeg6(String crSeg6) {
        this.crSeg6 = crSeg6;
    }

    public void setCrSeg7(String crSeg7) {
        this.crSeg7 = crSeg7;
    }

    public void setCrSeg8(String crSeg8) {
        this.crSeg8 = crSeg8;
    }

    public void setCrSeg9(String crSeg9) {
        this.crSeg9 = crSeg9;
    }

    public void setCSerialno(String no) {
        this.cSerialno = no;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public void setDrSeg1(String drSeg1) {
        this.drSeg1 = drSeg1;
    }

    public void setDrSeg10(String drSeg10) {
        this.drSeg10 = drSeg10;
    }

    public void setDrSeg11(String drSeg11) {
        this.drSeg11 = drSeg11;
    }

    public void setDrSeg12(String drSeg12) {
        this.drSeg12 = drSeg12;
    }

    public void setDrSeg13(String drSeg13) {
        this.drSeg13 = drSeg13;
    }

    public void setDrSeg14(String drSeg14) {
        this.drSeg14 = drSeg14;
    }

    public void setDrSeg15(String drSeg15) {
        this.drSeg15 = drSeg15;
    }

    public void setDrSeg16(String drSeg16) {
        this.drSeg16 = drSeg16;
    }

    public void setDrSeg17(String drSeg17) {
        this.drSeg17 = drSeg17;
    }

    public void setDrSeg18(String drSeg18) {
        this.drSeg18 = drSeg18;
    }

    public void setDrSeg19(String drSeg19) {
        this.drSeg19 = drSeg19;
    }

    public void setDrSeg2(String drSeg2) {
        this.drSeg2 = drSeg2;
    }

    public void setDrSeg20(String drSeg20) {
        this.drSeg20 = drSeg20;
    }

    public void setDrSeg3(String drSeg3) {
        this.drSeg3 = drSeg3;
    }

    public void setDrSeg4(String drSeg4) {
        this.drSeg4 = drSeg4;
    }

    public void setDrSeg5(String drSeg5) {
        this.drSeg5 = drSeg5;
    }

    public void setDrSeg6(String drSeg6) {
        this.drSeg6 = drSeg6;
    }

    public void setDrSeg7(String drSeg7) {
        this.drSeg7 = drSeg7;
    }

    public void setDrSeg8(String drSeg8) {
        this.drSeg8 = drSeg8;
    }

    public void setDrSeg9(String drSeg9) {
        this.drSeg9 = drSeg9;
    }

    public void setDSerialno(String no) {
        this.dSerialno = no;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public void setFilter1(String filter1) {
        this.filter1 = filter1;
    }

    public void setFilter10(String filter10) {
        this.filter10 = filter10;
    }

    public void setFilter2(String filter2) {
        this.filter2 = filter2;
    }

    public void setFilter3(String filter3) {
        this.filter3 = filter3;
    }

    public void setFilter4(String filter4) {
        this.filter4 = filter4;
    }

    public void setFilter5(String filter5) {
        this.filter5 = filter5;
    }

    public void setFilter6(String filter6) {
        this.filter6 = filter6;
    }

    public void setFilter7(String filter7) {
        this.filter7 = filter7;
    }

    public void setFilter8(String filter8) {
        this.filter8 = filter8;
    }

    public void setFilter9(String filter9) {
        this.filter9 = filter9;
    }

    public void setGjahr(String gjahr) {
        this.gjahr = gjahr;
    }

    public void setGrp1(String grp1) {
        this.grp1 = grp1;
    }

    public void setGrp10(String grp10) {
        this.grp10 = grp10;
    }

    public void setGrp2(String grp2) {
        this.grp2 = grp2;
    }

    public void setGrp3(String grp3) {
        this.grp3 = grp3;
    }

    public void setGrp4(String grp4) {
        this.grp4 = grp4;
    }

    public void setGrp5(String grp5) {
        this.grp5 = grp5;
    }

    public void setGrp6(String grp6) {
        this.grp6 = grp6;
    }

    public void setGrp7(String grp7) {
        this.grp7 = grp7;
    }

    public void setGrp8(String grp8) {
        this.grp8 = grp8;
    }

    public void setGrp9(String grp9) {
        this.grp9 = grp9;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public void setPaidCount(BigDecimal paidCount) {
        this.paidCount = paidCount;
    }

    public void setPolicyOrganCode(String policyOrganCode) {
        this.policyOrganCode = policyOrganCode;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public void setPolicyYear(BigDecimal policyYear) {
        this.policyYear = policyYear;
    }

    public void setPosted(String posted) {
        this.posted = posted;
    }

    public void setPremFreq(BigDecimal premFreq) {
        this.premFreq = premFreq;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public void setRefeflag(String refeflag) {
        this.refeflag = refeflag;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public void setWithdrawType(String withdrawType) {
        this.withdrawType = withdrawType;
    }

    @Override
    public String toString() {
        return "PremArapGlHisBO [" + "base6=" + base6 + ", base5=" + base5 + ", base8=" + base8 + ", base7=" + base7
                + ", base2=" + base2 + ", base1=" + base1 + ", base4=" + base4 + ", base3=" + base3 + ", paidCount="
                + paidCount + ", base9=" + base9 + ", organCode=" + organCode + ", filter8=" + filter8 + ", filter9="
                + filter9 + ", filter10=" + filter10 + ", chargeYear=" + chargeYear + ", posted=" + posted
                + ", dSerialno=" + dSerialno + ", gjahr=" + gjahr + ", productChannel=" + productChannel + ", batchno="
                + batchno + ", branchCode=" + branchCode + ", validateDate=" + validateDate + ", drSeg20=" + drSeg20
                + ", crSeg20=" + crSeg20 + ", cSerialno=" + cSerialno + ", drSeg19=" + drSeg19 + ", drSeg17=" + drSeg17
                + ", drSeg18=" + drSeg18 + ", drSeg15=" + drSeg15 + ", drSeg16=" + drSeg16 + ", belnr=" + belnr
                + ", drSeg13=" + drSeg13 + ", drSeg14=" + drSeg14 + ", drSeg11=" + drSeg11 + ", drSeg12=" + drSeg12
                + ", drSeg10=" + drSeg10 + ", grp9=" + grp9 + ", crSeg11=" + crSeg11 + ", grp7=" + grp7 + ", crSeg10="
                + crSeg10 + ", grp8=" + grp8 + ", crSeg13=" + crSeg13 + ", grp5=" + grp5 + ", policyYear=" + policyYear
                + ", crSeg12=" + crSeg12 + ", grp6=" + grp6 + ", filter5=" + filter5 + ", crSeg15=" + crSeg15
                + ", filter4=" + filter4 + ", crSeg14=" + crSeg14 + ", filter7=" + filter7 + ", crSeg17=" + crSeg17
                + ", filter6=" + filter6 + ", crSeg16=" + crSeg16 + ", filter1=" + filter1 + ", crSeg19=" + crSeg19
                + ", crSeg18=" + crSeg18 + ", filter3=" + filter3 + ", filter2=" + filter2 + ", grp4=" + grp4
                + ", grp3=" + grp3 + ", grp2=" + grp2 + ", grp1=" + grp1 + ", refeflag=" + refeflag + ", premFreq="
                + premFreq + ", policyOrganCode=" + policyOrganCode + ", bookkeepingId=" + bookkeepingId
                + ", busiProdName=" + busiProdName + ", feeType=" + feeType + ", busiProdCode=" + busiProdCode
                + ", channelType=" + channelType + ", base10=" + base10 + ", grp10=" + grp10 + ", crSeg9=" + crSeg9
                + ", crSeg7=" + crSeg7 + ", crSeg8=" + crSeg8 + ", crSeg5=" + crSeg5 + ", crSeg6=" + crSeg6
                + ", accountGlDate=" + accountGlDate + ", policyType=" + policyType + ", crSeg4=" + crSeg4
                + ", crSeg3=" + crSeg3 + ", crSeg2=" + crSeg2 + ", crSeg1=" + crSeg1 + ", businessCode=" + businessCode
                + ", moneyCode=" + moneyCode + ", drSeg9=" + drSeg9 + ", drSeg8=" + drSeg8 + ", drSeg7=" + drSeg7
                + ", drSeg6=" + drSeg6 + ", drSeg5=" + drSeg5 + ", drSeg4=" + drSeg4 + ", drSeg2=" + drSeg2
                + ", drSeg3=" + drSeg3 + ", drSeg1=" + drSeg1 + ", feeAmount=" + feeAmount + ", withdrawType="
                + withdrawType + ", derivType=" + derivType + "]";
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }
}
