package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description 变更轨迹表BO
 * @.belongToModule CAP-收付费系统
 * <AUTHOR>
 * @date 2015-07-15 10:05:46
 */
public class ChangeLogBO extends BaseBO {

    /**
     * @Fields serialVersionUID : 序列化
     */

    private static final long serialVersionUID = -5457991989536068367L;
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    /**
     * @Fields changeType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String changeType;
    /**
     * @Fields changeInfoOld : 变更前信息
     */
    private String changeInfoOld;
    /**
     * @Fields unitNumber : 应收付业务流水标识
     */
    private String unitNumber;
    /**
     * @Fields abortReason : 中止原因
     */
    private String abortReason;

    /**
     * @Fields changeId : 业务流水号，无实际含义
     */
    private BigDecimal changeId;
    /**
     * @Fields changeTime : 变更操作时间
     */
    private Date changeTime;
    /**
     * @Fields chequeNo : 支票号码
     */
    private String chequeNo;
    /**
     * @Fields changeInfoNew : 变更后信息
     */
    private String changeInfoNew;
    /**
     * @Fields changeBy : 变更操作人
     */
    private BigDecimal changeBy;
    /**
     * @Fields abortType : 中止原因类型代码
     */
    private String abortType;

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeInfoOld(String changeInfoOld) {
        this.changeInfoOld = changeInfoOld;
    }

    public String getChangeInfoOld() {
        return changeInfoOld;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getUnitNumber() {
        return unitNumber;
    }

    public void setAbortReason(String abortReason) {
        this.abortReason = abortReason;
    }

    public String getAbortReason() {
        return abortReason;
    }

    public void setAbortType(String abortType) {
        this.abortType = abortType;
    }

    public String getAbortType() {
        return abortType;
    }

    public void setChangeId(BigDecimal changeId) {
        this.changeId = changeId;
    }

    public BigDecimal getChangeId() {
        return changeId;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChangeInfoNew(String changeInfoNew) {
        this.changeInfoNew = changeInfoNew;
    }

    public String getChangeInfoNew() {
        return changeInfoNew;
    }

    public void setChangeBy(BigDecimal changeBy) {
        this.changeBy = changeBy;
    }

    public BigDecimal getChangeBy() {
        return changeBy;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ChangeLogBO [businessCode=" + businessCode + ", changeType=" + changeType + ", changeInfoOld="
                + changeInfoOld + ", unitNumber=" + unitNumber + ", abortReason=" + abortReason + ", abortType="
                + abortType + ", changeId=" + changeId + ", changeTime=" + changeTime + ", chequeNo=" + chequeNo
                + ", changeInfoNew=" + changeInfoNew + ", changeBy=" + changeBy + "]";
    }

}
