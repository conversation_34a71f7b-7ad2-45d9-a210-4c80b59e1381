package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
/**
 * 
 * @description 付款凭证BO对象
 * <AUTHOR> 
 * @date 2019-12-20 上午11:28:25 
 * @.belongToModule 收付费-付费凭证打印
 */
public class TVoucherInfoBO {
	/**
	 * 付款凭证号
	 */
	private String voucherId;// 付款凭证号
	/**
	 * 一次实收产生的收付号
	 */
	private String payrefno;// 一次实收产生的收付号
	/**
	 * 业务号
	 */
	private String businessCode;// 投保单号、保单号、保全受理号、赔案号等。如果支持代理人佣金，该字段还可以保存代理人id等等。
	/**
	 * 保单号码
	 */
	private String policyCode;// 保单号码
	/**
	 * 收付款人姓名
	 */
	private String payeeName;// 收付款人姓名
	/**
	 * 收付款人证件类型
	 */
	private String certiType;// 收付款人证件类型
	/**
	 * 收付款人证件号码
	 */
	private String certiCode;// 收付款人证件号码
	/**
	 * 合计金额
	 */
	private BigDecimal feeAmount;// 合计金额
	/**
	 * 记录插入人
	 */
	private BigDecimal insertBy;// 记录插入人
	/**
	 * 记录更新人
	 */	
	private BigDecimal updateBy;// 记录更新人
	/**
	 * 记录插入时间
	 */
	private String insertTime;// 记录插入时间
	/**
	 * 记录更新时间
	 */
	private String updateTime;// 记录更新时间
	/**
	 * 记录插入时间戳
	 */
	private String insertTimestamp;// 记录插入时间戳
	/**
	 * 记录更新时间戳
	 */
	private String updateTimestamp;// 记录更新时间戳
	public String getVoucherId() {
		return voucherId;
	}
	public void setVoucherId(String voucherId) {
		this.voucherId = voucherId;
	}
	public String getPayrefno() {
		return payrefno;
	}
	public void setPayrefno(String payrefno) {
		this.payrefno = payrefno;
	}
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}
	public String getPolicyCode() {
		return policyCode;
	}
	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	public String getPayeeName() {
		return payeeName;
	}
	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}
	public String getCertiType() {
		return certiType;
	}
	public void setCertiType(String certiType) {
		this.certiType = certiType;
	}
	public String getCertiCode() {
		return certiCode;
	}
	public void setCertiCode(String certiCode) {
		this.certiCode = certiCode;
	}
	public BigDecimal getFeeAmount() {
		return feeAmount;
	}
	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}
	public BigDecimal getInsertBy() {
		return insertBy;
	}
	public void setInsertBy(BigDecimal insertBy) {
		this.insertBy = insertBy;
	}
	public BigDecimal getUpdateBy() {
		return updateBy;
	}
	public void setUpdateBy(BigDecimal updateBy) {
		this.updateBy = updateBy;
	}
	public String getInsertTime() {
		return insertTime;
	}
	public void setInsertTime(String insertTime) {
		this.insertTime = insertTime;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getInsertTimestamp() {
		return insertTimestamp;
	}
	public void setInsertTimestamp(String insertTimestamp) {
		this.insertTimestamp = insertTimestamp;
	}
	public String getUpdateTimestamp() {
		return updateTimestamp;
	}
	public void setUpdateTimestamp(String updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}
	@Override
	public String toString() {
		return "TVoucherInfoBO [voucherId=" + voucherId + ", payrefno="
				+ payrefno + ", businessCode=" + businessCode + ", policyCode="
				+ policyCode + ", payeeName=" + payeeName + ", certiType="
				+ certiType + ", certiCode=" + certiCode + ", feeAmount="
				+ feeAmount + ", insertBy=" + insertBy + ", updateBy="
				+ updateBy + ", insertTime=" + insertTime + ", updateTime="
				+ updateTime + ", insertTimestamp=" + insertTimestamp
				+ ", updateTimestamp=" + updateTimestamp + "]";
	}
	
	
	
	
	
	

}
