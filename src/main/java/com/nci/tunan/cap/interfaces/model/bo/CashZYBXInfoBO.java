package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description CashZYBXInfoBO对象
 * <AUTHOR>
 * @.belongToModule 收付费-中银保信申请单编号查询
 * 
 */
public class CashZYBXInfoBO extends BaseBO {

  
    /**
	 * 序列化版本号
	 */
	private static final long serialVersionUID = 1L;
	/**
     * @Fields listId : 流水号
     */
    private BigDecimal listId;

    /**
     * @Fields unitNumber : 收付唯一号
     */
    private String unitNumber;
    
    
    /**
     * @Fields partiAppSheetNo : 申请单编号
     */
    private String partiAppSheetNo;
    
    /**
     * @Fields businessCode : 投保单号、保单号、保全受理号、赔案号等。 如果支持代理人佣金，该字段还可以保存代理人ID等等。
     */
    private String businessCode;
    
	public String getBusinessCode() {
		return businessCode;
	}
	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}


	public BigDecimal getListId() {
		return listId;
	}


	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}


	public String getUnitNumber() {
		return unitNumber;
	}


	public void setUnitNumber(String unitNumber) {
		this.unitNumber = unitNumber;
	}


	public String getPartiAppSheetNo() {
		return partiAppSheetNo;
	}


	public void setPartiAppSheetNo(String partiAppSheetNo) {
		this.partiAppSheetNo = partiAppSheetNo;
	}

	@Override
	public String getBizId() {
		return null;
	}


	@Override
	public String toString() {
		return "CashZYBXInfoBO [listId=" + listId + ", unitNumber="
				+ unitNumber + ", partiAppSheetNo=" + partiAppSheetNo + "]";
	}
    
    
}
