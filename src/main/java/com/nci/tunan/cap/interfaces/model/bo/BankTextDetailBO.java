package com.nci.tunan.cap.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseBO;

/**
 * @description BankTextDetailVO对象
 * <AUTHOR>
 * @date 2015-07-31 17:54:24
 * @.belongToModule 收付费-制返盘
 */
public class BankTextDetailBO extends BaseBO {

    /**
     * @Fields serialVersionUID : SUID
     */

    private static final long serialVersionUID = 592540828367673707L;
    /**
     * @Fields moneyCode : 币种代码 CNY；人民币元
     */
    private String moneyCode;
    /**
     * @Fields bankAccount : 帐号
     */
    private String bankAccount;

    /**
     * @Fields payeePhone : 手机号
     */
    private String payeePhone;
    /**
     * @Fields diskMode : 资金使用，默认值：1 1：代收付
     */
    private String diskMode;
    /**
     * @Fields uuid : 唯一标识码
     */
    private String uuid;
    /**
     * @Fields sendId : 盘文件ID
     */
    private BigDecimal sendId;
    /**
     * @Fields status : 返盘状态 1;返盘成功 2;返盘失败
     */
    private String bankTextStatus;
    /**
     * @Fields seqNo : 流水号，无业务含义
     */
    private BigDecimal seqNo;
    /**
     * @Fields certiCode : 证件号码
     */
    private String certiCode;
    /**
     * @Fields feeAmount : 制盘金额
     */
    private BigDecimal feeAmount;
    /**
     * @Fields cashDesc : 标记现金流量，是会计入账科目的CODE，可不填
     */
    private String cashDesc;
    /**
     * @Fields dueDate : 应收应付日期
     */
    private Date dueDate;
    /**
     * @Fields rtnCode : 银行返回代码 E1409；付款行未开通业务 E1503；客户信息存在非法字符
     */
    private String rtnCode;
    /**
     * @Fields zone : 对方账号的所属区域 3201；江苏省
     */
    private String zone;
    /**
     * @Fields derivType : 业务来源
     */
    private String derivType;
    /**
     * @Fields arapFlag : 应收付标识 S；收费 F；付费
     */
    private String arapFlag;
    /**
     * @Fields lhh : 对方银行的联行号
     */
    private String lhh;
    /**
     * @Fields bankCode : 分组标记+银行代码
     */
    private String bankCode;
    /**
     * @Fields accoName : 账户所有人姓名
     */
    private String accoName;
    /**
     * @Fields certiType : 证件类型
     */
    private String certiType;

    /**
     * 制盘类型
     */
    private String bankTextType;
    /**
     * 交易时间
     */
    private Date bankDealDate;
    /**
     * 流水号集合
     */
    private List<BigDecimal> seqnoList;
    /**
     * 分公司代码(生成制盘文件时使用，用于复制group表对应的值)
     */
    private String branchCode;
    /**
     * 保单号
     */
    private String policyCode;
    /**
     * 代扣\发类型
     */
    private String premAgentType;
    /**
     * @Fields applyCode : 投保单号码
     */
    private String applyCode;
    /**
     * 对公标识
     */
    private String isCorporate;
    /** 
    * @Fields unitNumber : 收付唯一号
    */ 
    private String unitNumber;
    /** 
     * @Fields busiType : 业务类型 "C201（续期保费）、C202：保全补费、C203：保全退费、C204 退保保费、C301年金领取"
     */
    private String busiType;
    
    /** 
	* @Fields applyDate : 购买日期
	*/ 
    private Date applyDate;
    
    /**
	 * @Fields premFreq : 扣款期数
	 */
	private String premNumber;
	
	/** 
	* @Fields expiryDate : 保单终止日期    授权到期日
	*/ 
    private Date expiryDate;
    
    /**
	 * @Fields productNameSys : 主险险种代码(需连码表取出中文)产品名称
	 */
	private String productNameSys;
	
	/**
	 * @Fields feeAmount : 主险保额  保单与契约传回
	 */
	private BigDecimal returnAmount;
	
	/**
	 * @Fields serviceCode : 付款项目  (用来判断保单状态的)
	 */
	private String serviceCode;
	/**
     * @Fields businessType : 业务类型 新契约类型（新单、退单）、保全（保全项）、理赔（受理）
     *         参照总账汇总——FEEOPERATIONTYPE，如理赔业务、贷款清偿。。。
     */
    private String businessType;
    
    /**
     * @Fields rtnTransstatusDes : 交易状态描述
     */
    private String rtnTransstatusDes;
    

	public String getRtnTransstatusDes() {
		return rtnTransstatusDes;
	}

	public void setRtnTransstatusDes(String rtnTransstatusDes) {
		this.rtnTransstatusDes = rtnTransstatusDes;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getPremNumber() {
		return premNumber;
	}

	public void setPremNumber(String premNumber) {
		this.premNumber = premNumber;
	}

	public String getProductNameSys() {
		return productNameSys;
	}

	public void setProductNameSys(String productNameSys) {
		this.productNameSys = productNameSys;
	}

	public BigDecimal getReturnAmount() {
		return returnAmount;
	}

	public void setReturnAmount(BigDecimal returnAmount) {
		this.returnAmount = returnAmount;
	}

	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}

	public String getUnitNumber() {
        return unitNumber;
    }

    public void setUnitNumber(String unitNumber) {
        this.unitNumber = unitNumber;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getPremAgentType() {
        return premAgentType;
    }

    public void setPremAgentType(String premAgentType) {
        this.premAgentType = premAgentType;
    }

    public void setMoneyCode(String moneyCode) {
        this.moneyCode = moneyCode;
    }

    public String getMoneyCode() {
        return moneyCode;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setDiskMode(String diskMode) {
        this.diskMode = diskMode;
    }

    public String getDiskMode() {
        return diskMode;
    }

    public void setPayeePhone(String payeePhone) {
        this.payeePhone = payeePhone;
    }

    public String getPayeePhone() {
        return payeePhone;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setSendId(BigDecimal sendId) {
        this.sendId = sendId;
    }

    public BigDecimal getSendId() {
        return sendId;
    }

    public void setBankTextStatus(String status) {
        this.bankTextStatus = status;
    }

    public String getBankTextStatus() {
        return bankTextStatus;
    }

    public void setSeqNo(BigDecimal seqNo) {
        this.seqNo = seqNo;
    }

    public BigDecimal getSeqNo() {
        return seqNo;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setCashDesc(String cashDesc) {
        this.cashDesc = cashDesc;
    }

    public String getCashDesc() {
        return cashDesc;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setRtnCode(String rtnCode) {
        this.rtnCode = rtnCode;
    }

    public String getRtnCode() {
        return rtnCode;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getZone() {
        return zone;
    }

    public void setDerivType(String derivType) {
        this.derivType = derivType;
    }

    public String getDerivType() {
        return derivType;
    }

    public void setArapFlag(String arapFlag) {
        this.arapFlag = arapFlag;
    }

    public String getArapFlag() {
        return arapFlag;
    }

    public void setLhh(String lhh) {
        this.lhh = lhh;
    }

    public String getLhh() {
        return lhh;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setAccoName(String accoName) {
        this.accoName = accoName;
    }

    public String getAccoName() {
        return accoName;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    public String getBankTextType() {
        return bankTextType;
    }

    public Date getBankDealDate() {
        return bankDealDate;
    }

    public void setBankDealDate(Date bankDealDate) {
        this.bankDealDate = bankDealDate;
    }

    /**
     * @description 制盘类型
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param bankTextType 
    */
    public void setBankTextType(String bankTextType) {
        this.bankTextType = bankTextType;
    }
    
	public List<BigDecimal> getSeqnoList() {
		return seqnoList;
	}

	public void setSeqnoList(List<BigDecimal> seqnoList) {
		this.seqnoList = seqnoList;
	}

	@Override
    public String getBizId() {
        return null;
    }

    public String getBranchCode() {
		return branchCode;
	}

	public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}

	@Override
    public String toString() {
        return "BankTextDetailBO [moneyCode=" + moneyCode + ", bankAccount=" + bankAccount + ", diskMode=" + diskMode
                + ", payeePhone=" + payeePhone + ", uuid=" + uuid + ", sendId=" + sendId + ", bankTextStatus=" + bankTextStatus
                + ", seqNo=" + seqNo + ", certiCode=" + certiCode + ", feeAmount=" + feeAmount + ", cashDesc="
                + cashDesc + ", dueDate=" + dueDate + ", rtnCode=" + rtnCode + ", zone=" + zone + ", derivType="
                + derivType + ", arapFlag=" + arapFlag + ", lhh=" + lhh + ", bankCode=" + bankCode + ", accoName="
                + accoName + ", certiType=" + certiType + ",bankDealDate ="+bankDealDate+ ", branchCode=" + branchCode
                + ", isCorporate="+isCorporate+"]";
    }

	public String getApplyCode() {
		return applyCode;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getIsCorporate() {
		return isCorporate;
	}

	public void setIsCorporate(String isCorporate) {
		this.isCorporate = isCorporate;
	}
	
}
