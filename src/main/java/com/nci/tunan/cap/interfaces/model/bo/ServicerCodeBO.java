package com.nci.tunan.cap.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description 支付商ServicerCodeBO
 * @<NAME_EMAIL> 
 * @date 2017-02-14 上午10:15:15 
 * @.belongToModule 收付费-查询支付商
*/
public class ServicerCodeBO extends BaseBO{


	/** 
	* @Fields serialVersionUID : 序列化版本号
	*/ 
	private static final long serialVersionUID = 3323401733315090691L;
	/** 
	* @Fields code : 支付商代码
	*/ 
	private String code;
	/** 
	* @Fields name : 支付商名称 
	*/ 
	private String name;
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
