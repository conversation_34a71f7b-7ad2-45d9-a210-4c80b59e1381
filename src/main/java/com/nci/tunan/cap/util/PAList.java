package com.nci.tunan.cap.util;

import java.util.List;

import com.nci.tunan.cap.interfaces.model.bo.PremArapVMSBO;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;

/** 
 * @ClassName: PAList 
 * @description 推送VMS数据报文内容 
 * <AUTHOR>
 * @date 2017年12月18日 上午11:27:55 
 * @.belongToModule 收付费-推送VMS数据
 */ 
public class PAList{
   
    /** 
     * 业务数据报文
     */ 
    @XStreamImplicit
    private List<PremArapVMSBO> vms;
    
    /** 
     * 报文文件个数
     */ 
    @XStreamAlias("RowCount")
    private String  rowCount;
    
    /** 
     * 报文文件中第几个业务数据计数
     */ 
    @XStreamAlias("ColCount")
    private String  colCount;
    
    public String getRowCount() {
        return rowCount;
    }
    public void setRowCount(String rowCount) {
        this.rowCount = rowCount;
    }
    public String getColCount() {
        return colCount;
    }
    public void setColCount(String colCount) {
        this.colCount = colCount;
    }
    public List<PremArapVMSBO> getVms() {
        return vms;
    }
    public void setVms(List<PremArapVMSBO> vms) {
        this.vms = vms;
    }
}
