package com.nci.tunan.cap.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import jcifs.util.Base64;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;

import com.nci.tunan.cap.interfaces.model.vo.szyb.MedicareInfoVO;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.framework.util.UtilCoding;
import com.nci.udmp.framework.util.idea.LisIDEA;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

public class SZYLMD5Util {
    /**
     * 日志工具
     */
    protected static Logger logger = LoggerFactory.getLogger();
    /**
     * @Fields TOKEN_YFWX : 获取MD5参数固定值
     */
    public static final String TOKEN_YFWX = "yfwx";

    /**
     * @Fields TOKEN_YFWX : 公司代码
     */
    public static final String SZYB_GSDM = "XHLC";
    /**
     * @Fields TOKEN_SYSCODE : 系统标记
     */
    public static final String TOKEN_SYSCODE = "A002";

    public static List<String> proNum = Arrays.asList("03", "04", "05");

    public static String changeBusi(String productNum) {
        String busiProduct = "";
        switch (productNum) {
        case "03":
            busiProduct = "00851000";
            break;
        case "04":
            busiProduct = "00865000";
            break;
        case "05":
            busiProduct = "00865000";
            break;
        default:
            break;
        }
        return busiProduct;
    }

    public static String md5(String passwd) {
        String encryptPassword = null;
        passwd = TOKEN_SYSCODE + passwd + TOKEN_YFWX;
        try {
            MessageDigest alga = java.security.MessageDigest.getInstance("MD5");
            alga.update(passwd.getBytes());
            byte[] digesta = alga.digest();
            encryptPassword = byte2String(digesta);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encryptPassword;
    }

    public static String byte2String(byte[] b) {
        return new String(Base64.encode(b));
    }

    /**
     * @description http,json格式，post请求
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param @param URL
     * @param jsonstr
     * @return String
     */
    public static String jsondoPost(String URL, String jsonstr) {
        OutputStreamWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        HttpURLConnection conn = null;
        try {
            URL url = new URL(URL);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            // 发送POST请求必须设置为true
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 设置连接超时时间和读取超时时间
            conn.setConnectTimeout(CodeCst.NUMBER_THIRTY_THOUSAND);
            conn.setReadTimeout(CodeCst.NUMBER_THIRTY_THOUSAND);
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            // 获取输出流
            out = new OutputStreamWriter(conn.getOutputStream(), "utf-8");
            out.write(jsonstr);
            out.flush();
            out.close();
            // 取得输入流，并使用Reader读取
            if (CodeCst.NUMBER_TWO_HUNDRED == conn.getResponseCode()) {
                in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                    logger.info(line);
                }
            } else {
                logger.error("ResponseCode is an error code:" + conn.getResponseCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ioe) {
                ioe.printStackTrace();
            }
        }
        return result.toString();
    }

    /**
     * @description 获取8位随机数
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return String
     */
    public static String randomStr() {
        StringBuilder str = new StringBuilder();// 定义变长字符串
        Random random = new Random();
        // 随机生成数字，并添加到字符串
        for (int i = 0; i < CodeCst.NUMBER_EIGHT; i++) {
            str.append(random.nextInt(CodeCst.NUMBER_TEN));
        }
        // 将字符串转换为数字并输出
        String randomStr = str.toString();
        logger.info(randomStr);
        return randomStr;
    }

    /**
     * @description 解析深圳医保返回报文
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param resultXml 返回报文
     * @param date 对账日期
     * @param transSerialNum 交易流水号
     * @return Map<String,List<MedicalInfoVO>>
     */
    public static Map<String, List<MedicareInfoVO>> parseStr(String resultXml, String date, String transSerialNum) {
        try {
            Map<String, List<MedicareInfoVO>> mediMap = new HashMap<String, List<MedicareInfoVO>>();
            Document document;
            document = DocumentHelper.parseText(resultXml);
            Element root = document.getRootElement();
            Iterator<?> iter = root.elementIterator();
            List<MedicareInfoVO> medicareList = new ArrayList<MedicareInfoVO>();
            String endpage = "1";
            while (iter.hasNext()) {
                Element ele = (Element) iter.next();
                Attribute attribute = ele.attribute(0);
                String data = attribute.getStringValue();
                if ("totalsize".equals(attribute.getName())) {
                    if (null != data && "0".equals(data)) {
                        mediMap.put("1", medicareList);
                        return mediMap;
                    }
                }
                /* endpage 尾页标志 0=否1=是如果为0，请发起下一次查询，将尾页标记封装到map里返回，判断是否发起下一次请求 */
                if ("endpage".equals(attribute.getName())) {
                    if (null != data && "0".equals(data)) {
                        endpage = data;
                    }
                }
                Iterator<?> childTterator = ele.elementIterator();
                while (childTterator.hasNext()) {
                    Element stuChild = (Element) childTterator.next();
                    String productNum = stuChild.attribute("ckc729").getStringValue(); // 产品编号
                    if (null != productNum && proNum.contains(productNum)) {
                        List<Attribute> list = stuChild.attributes();
                        MedicareInfoVO medicare = new MedicareInfoVO();
                        medicare.setTransSerialNum(transSerialNum);
                        medicare.setMedicalInsuranceCard(BigDecimal.ONE);
                        medicare.setFinishTime(DateUtilsEx.formatToDate(date, "yyyy-MM-dd")); // TODO
                                                                                              // 上线时根据入参赋值
                        medicare.setBusiProdCode(changeBusi(productNum)); // 险种
                        medicare.setPayMode(CodeCst.PAY_MODE__SOCIALSECURITY);
                        for (Attribute attr : list) {
                            String name = attr.getName();
                            switch (name) {
                            case "ake038": // 保单金额
                                medicare.setFeeAmount(new BigDecimal(attr.getStringValue()));
                                break;
                            case "ckc618": // 结算业务流水号
                                medicare.setBusiSerialNum(attr.getStringValue());
                                break;
                            case "ckc729": // 保险产品编号
                                medicare.setProductNumber(attr.getStringValue());
                                break;
                            case "ckc746": // 投保单号
                                medicare.setBusinessCode(attr.getStringValue());
                                break;
                            default:
                                break;
                            }
                        }
                        medicareList.add(medicare);
                    }
                }
            }
            mediMap.put(endpage, medicareList);
            return mediMap;
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String loginPWD(String pwd) {
        ScriptEngineManager maneger = new ScriptEngineManager();
        String md5str = "";
        ScriptEngine engine = maneger.getEngineByName("JavaScript");
        InputStream resourceAsStream = SZYLMD5Util.class.getResourceAsStream("/META-INF/cap/properties/idea.js");
        Reader scriptReader = null;
        try {
            scriptReader = new InputStreamReader(resourceAsStream, "utf-8");
            engine.eval(scriptReader);
            if (engine instanceof Invocable) {
                // 调用JS方法
                Invocable invocable = (Invocable) engine;
                String result = (String) invocable.invokeFunction("encryptString", new Object[] { pwd });
                LisIDEA lisIdea = new LisIDEA();
                // 将前端接收到的密文解密成明文
                result = lisIdea.decryptString(result);
                md5str = UtilCoding.md5Encrypt(result, Constants.CODE_TYPE);
                return md5str;
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            try {
                if (null != scriptReader) {
                    scriptReader.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return md5str;
    }
}
