package com.nci.tunan.cap.util;


/**
 * @description 针对收付费的常见验证扩展
 * @<NAME_EMAIL>
 * @date 2015-9-18 上午11:07:52
 * @.belongToModule 收付费-常见验证扩展
 */
public class CapVerify {

    /**
     * @description 检查dataObject对象是否空
     * @param param dataObject对象
     * @param strict 是否启用严格检查
     * @return 是否空
     * <AUTHOR>
     */
    public static boolean dataObjectNotNull(Object param, boolean strict) {
//        boolean flag = false;
        if (null == param) {
            return false;
        }

/*@invalid  try {
            BeanMap bean = BeanMap.create(param);
            for (Object attrCode : bean.values()) {
                flag = flag || objIsNotNull(attrCode, strict);
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }*/
        return true;
    }

    /**
     * @description 检查对象是否空
     * @param param 被检查的对象
     * @param <T> 被检查对象的类型
     * <AUTHOR>
     * @return 是否空
     * @since 2014-10-13
     */
    public static <T> boolean objIsNotNull(T param) {
        return objIsNotNull(param, false);
    }

    /**
     * @description 检查对象是否空
     * @param param 被检查的对象
     * @param strict 如果strict为true，对部分类型进行进一步检查，可扩展
     * @param <T> 被检查对象的类型
     * @return 是否空
     * <AUTHOR>
     */
    public static <T> boolean objIsNotNull(T param, boolean strict) {
        boolean flag = true;
        if (null == param) {
            flag = false;
        } else if (strict) {
            //@invalid 校验字符串
            if (param.getClass() == String.class && ((String) param).trim().equals("")) {
                flag = false;
            }
        }
        return flag;
    }
    
    /**
     * 
     * @Title: phTeZhi 
     * @description 判断strict为true 返回param1 ,否则返回param2
     * @param strict 判断条件
     * @param param1 返回值1
     * @param param2 返回值2
     * @return    设定文件 
     * T    返回类型 
     * @throws
     */
    public static <T> T ternaryOperator (boolean strict,T param1,T param2){
    	if(strict){
    		return param1;
    	}else{
    		return param2;
    	}
    }
    
}
