package com.nci.tunan.cap.util;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

import javax.net.ssl.HttpsURLConnection;

import org.slf4j.Logger;

import com.nci.tunan.cap.impl.report.service.impl.StatementFilingServiceImpl;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 基于HttpsURLConnection实现客户端https请求
 *
 */
public class HTTPSUtil {
    private static final String METHOD_GET = "GET";
    private static final String METHOD_POST = "POST";
    private static Logger logger = LoggerFactory.getLogger();

    /**
     * doGet
     */
    private static void httpsGet(String url) throws NoSuchAlgorithmException, KeyManagementException, IOException {
        // 创建连接对象
        HttpsURLConnection connection = getHttpsURLConnection(url, METHOD_GET);
        connection.connect();
        logger.info("cipersuit used:" + connection.getCipherSuite());

        // 读取连接响应内容
        getresponse(connection);
    }

    /**
     * doPost
     */
    private static void httpsPost(String url, String params) throws NoSuchAlgorithmException, IOException, KeyManagementException {
        // 创建连接对象
        HttpsURLConnection connection = getHttpsURLConnection(url, METHOD_POST);
        // 发送POST请求必须设置如下两行
        connection.setDoOutput(true);
        connection.setDoInput(true);
        connection.connect();

        // 获取URLConnection对象对应的输出流
        PrintWriter out = null;
        try {
            out = new PrintWriter(connection.getOutputStream());
            // 发送请求参数
            out.print(params);
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
        }
        getresponse(connection);
    }

    /**
     * 读取连接响应内容
     */
    private static void getresponse(HttpsURLConnection connection) throws IOException {

        BufferedReader br = null;
        try {
            br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String inputLine;
            StringBuilder sb = new StringBuilder();
            while ((inputLine = br.readLine()) != null) {
                sb.append(inputLine);
            }
            logger.info(sb.toString());
            logger.info("responseMsg:" + connection.getResponseMessage() + "; responseCode:" + connection.getResponseCode());
        } finally {
            if (br != null) {
                br.close();
            }
        }
    }

    /**
     * 获取连接对象
     */
    public static HttpsURLConnection getHttpsURLConnection(String url, String method) throws IOException, NoSuchAlgorithmException, KeyManagementException {
        URL myUrl = new URL(url);
        // 创建连接对象
        HttpsURLConnection connection = (HttpsURLConnection) myUrl.openConnection();
        connection.setRequestMethod(method);
        // 设置SSLSocketFactory对象（若不指定算法套，getSslContext().getSocketFactory()即可）
        connection.setSSLSocketFactory(new MySSLSocketFactory());
//        // 验证hostname,全部允许
//        connection.setHostnameVerifier((hostname, sslSession) -> true);

        // 设置通用请求属性
//        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.84 Safari/537.36");
//        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setRequestProperty("Charset", "UTF-8");

        return connection;
    }

    public static void main(String[] args) throws Exception {
        String url = "https://eaftest.newchinalife.com/manage/getToken";

//        httpsGet(url);
        StatementFilingServiceImpl a = new StatementFilingServiceImpl();
        String parameter = a.getTokenReqParm();
        httpsPost(url, parameter);
    }
}



