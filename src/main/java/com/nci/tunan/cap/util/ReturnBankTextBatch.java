package com.nci.tunan.cap.util;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import com.nci.tunan.cap.dao.IArApDao;
import com.nci.tunan.cap.dao.IBankTextMainDao;
import com.nci.tunan.cap.dao.IBizSourceDao;
import com.nci.tunan.cap.dao.ISalesChannelDao;
import com.nci.tunan.cap.impl.counterchagre.service.ICapArapEnterService;
import com.nci.tunan.cap.impl.counterchagre.service.IReturnBankService;
import com.nci.tunan.cap.impl.counterchagre.service.impl.MailServiceUtil;
import com.nci.tunan.cap.interfaces.model.bo.BankTextBO;
import com.nci.tunan.cap.interfaces.model.bo.BankTextMainBO;
import com.nci.tunan.cap.interfaces.model.bo.UnifiedCallbackBranchesBO;
import com.nci.tunan.cap.interfaces.model.po.BankTextMainPO;
import com.nci.tunan.cap.interfaces.model.vo.BankTextVO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.core.transaction.TransactionUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.cache.CacheUtil;
import com.nci.udmp.util.file.FileUtil;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * @description 制返盘返盘处理
 * @<NAME_EMAIL>
 * @date 2015-9-17 下午7:55:10
 * @.belongToModule 收付费-集中制返盘
 */
public class ReturnBankTextBatch extends AbstractBatchJobForMod {
	/**
	 * 返盘service
	 */
    private static IReturnBankService returnBankService;
    /**
     * 制盘任务明细信息IBankTextMainDao
     */
    private IBankTextMainDao iBankTextMainDao;
    /**
     * 制盘任务明细信息表BO
     */
    private BankTextMainBO mainBO;
    /**
     * 应收应付dao
     */
    private IArApDao iArApDao;
    /**
     * 业务来源到
     */
    private IBizSourceDao iBizSourceDao;
    /**
     * 销售渠道dao
     */
    private ISalesChannelDao iSalesChannelDao;

    /**
     * 转实收service
     */

    private ICapArapEnterService capArapEnterService;
    /**
     * @description 批处理执行
     * @param jobSessionContext 任务会话上下文
     * @param resultDatas 参数列表
     * @param modNum 取模数值
     */
    @Override
    public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> resultDatas, int modNum) {
        return resultDatas;
    }
    /**
     * @description 批处理异常处理
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void jobErrorAudit(JobSessionContext jobSessionContext, JobData jobData) {
        
    }
    /**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {

    }
    /**
     * @description 批处理业务处理
     * @param jobSessionContext 任务会话上下文
     * @param start 起始位置
     * @param counts 查询数量
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
        Date sapDate = DateUtilsEx.getTodayDate();
        logger.info("返盘======>开始:ReturnBankTextBatch--query");
        File[] texttsAR=new File[]{};
        //1 下载收款返盘文件
        File[] bankTextsAR = returnBankService.downLoadRemoteFiles(CodeCst.ARAP_FLAG__AR,null);
       //@invalid  返盘文件存放目录/temp8/new/rec/return/return_process_b
    	String branchRemoteRec=PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.FPRANCH_PATH,
                CodeCst.ZFPRANCH_REC_CODE);
    	//@invalid 返盘文件备份目录/temp8/new/rec/return/return_bak	
        String branchRemoteRecBak= PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.FPRANCH_PATH,
                CodeCst.ZFPRANCH_REC_BAK_CODE);
        if(StringUtils.isNotEmpty(branchRemoteRec)&&StringUtils.isNotEmpty(branchRemoteRecBak)){
        	 //@invalid 分公司下载收款返盘文件
        	 File[] branchbankTextsAR =returnBankService.downLoadRemoteFiles(CodeCst.ARAP_FLAG__AR,"1");
        	  logger.info("返盘======>ReturnBankTextBatch--下载收款返盘文件分公司集中个数：{}",branchbankTextsAR.length);
        	 if(null!=branchbankTextsAR&&branchbankTextsAR.length>0) {
        	 texttsAR= (File[]) ArrayUtils.addAll(bankTextsAR, branchbankTextsAR);
        	 }
        }
        logger.info("返盘======>ReturnBankTextBatch--下载收款返盘文件总公司集中个数：{}",bankTextsAR.length);
        if (null!=texttsAR&&texttsAR.length > 0) {
        	 logger.info("返盘收费。分公司集中和总公司集中返盘处理逻辑开始======>");
        	 //1.1 依据收费返盘文件进行返盘逻辑处理
            this.process(jobSessionContext, texttsAR);
        }else{
        	 if (bankTextsAR.length > 0) {
        		 logger.info("返盘收费 。总公司集中返盘处理逻辑开始======>");
               	 this.process(jobSessionContext, bankTextsAR); 
        	  }
        }
        File[] textsAP=new File[]{};
        //2 下载付款返盘文件
        File[] bankTextsAP = returnBankService.downLoadRemoteFiles(CodeCst.ARAP_FLAG__AP,null);
        
       String remotePay=PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.FPRANCH_PATH,
                CodeCst.ZFPRANCH_PAY_CODE);
       String remotePayBak=PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.FPRANCH_PATH,
                CodeCst.ZFPRANCH_PAY_BAK_CODE);
       if(StringUtils.isNotEmpty(remotePay)&&StringUtils.isNotEmpty(remotePayBak)){
    	   //@invalid 下载付款分公司集中文件
    	   File[] branchbankTextsAP = returnBankService.downLoadRemoteFiles(CodeCst.ARAP_FLAG__AP,"1");
    	   logger.info("返盘======>ReturnBankTextBatch--下载付款返盘文件分公司集中个数：{}",branchbankTextsAP.length);
    	   if(null!=branchbankTextsAP&&branchbankTextsAP.length>0) {
    	     textsAP= (File[]) ArrayUtils.addAll(bankTextsAP, branchbankTextsAP);
    	   }
       }
        logger.info("返盘======>ReturnBankTextBatch--下载付款返盘文件总公司集中个数：{}",bankTextsAP.length);
        if (null!=textsAP&&textsAP.length > 0) {
        	 logger.info("返盘付费。分公司集中和总公司集中返盘处理逻辑开始======>");
        	//2.1 依据付费返盘文件进行返盘逻辑处理
            this.process(jobSessionContext, textsAP);
        }else{
        	  if (bankTextsAP.length > 0) {
        		  logger.info("返盘付费。总公司集中返盘处理逻辑开始======>");
              	this.process(jobSessionContext, bankTextsAP);
        	  }
        	
        }
        List<JobData> jobDataList = new ArrayList<JobData>();
        JobData jobData = new JobData();
        jobData.set("test1", "test1");
        jobDataList.add(jobData);
        Date end = new Date();
        long s = (end.getTime() - sapDate.getTime()) / CodeCst.NUMBER_THOUSAND;
        logger.info("返盘======>批处理总耗时:" + s / CodeCst.NUMBER_THREE_THOUSAND_AND_SIX_HUNDRED + "时" + s
                / CodeCst.NUMBER_SIXTY % CodeCst.NUMBER_SIXTY + "分" + s % CodeCst.NUMBER_SIXTY + "秒");
        return jobDataList;
    }

    public ICapArapEnterService getCapArapEnterService() {
        return capArapEnterService;
    }

    public void setCapArapEnterService(ICapArapEnterService capArapEnterService) {
        this.capArapEnterService = capArapEnterService;
    }
    /**
     * 任务关闭
     */
    @Override
    public boolean isCanBeRun() {
        return true;
    }
    /**
     * 停止任务
     */
    @Override
    public void jobStop() {

    }
    /**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        jobSessionContext.setStartNum(1);
        jobSessionContext.setEndNum(1);
        jobSessionContext.setModNum(1);//@invalid 批处理逻辑依赖关系需要
        return jobSessionContext;
    }

    public IReturnBankService getReturnBankService() {
        return returnBankService;
    }

    public void setReturnBankService(IReturnBankService returnBankService) {
        this.returnBankService = returnBankService;
    }

    public BankTextMainBO getMainBO() {
        return mainBO;
    }

    public void setMainBO(BankTextMainBO mainBO) {
        this.mainBO = mainBO;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return IBankTextMainDao
    */
    public IBankTextMainDao getiBankTextMainDao() {
        return iBankTextMainDao;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param iBankTextMainDao 
    */
    public void setiBankTextMainDao(IBankTextMainDao iBankTextMainDao) {
        this.iBankTextMainDao = iBankTextMainDao;
    }
    /**
     * 获取id名字
     */
    @Override
    public String getIdName() {
        return null;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return IArApDao
    */
    public IArApDao getiArApDao() {
        return iArApDao;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param iArApDao 
    */
    public void setiArApDao(IArApDao iArApDao) {
        this.iArApDao = iArApDao;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return IBizSourceDao
    */
    public IBizSourceDao getiBizSourceDao() {
        return iBizSourceDao;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param iBizSourceDao 
    */
    public void setiBizSourceDao(IBizSourceDao iBizSourceDao) {
        this.iBizSourceDao = iBizSourceDao;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return ISalesChannelDao
    */
    public ISalesChannelDao getiSalesChannelDao() {
        return iSalesChannelDao;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param iSalesChannelDao 
    */
    public void setiSalesChannelDao(ISalesChannelDao iSalesChannelDao) {
        this.iSalesChannelDao = iSalesChannelDao;
    }

    /**
     * @description 查询制盘任务明细信息表主键序列
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return Integer
     */
    private Integer findBankTextMainId() {
        final TransactionTemplate transactionTemplate = TransactionUtil.getTransactionTemplate();
        //@invalid  事务控制
        return transactionTemplate.execute(new TransactionCallback<Integer>() {

            public Integer doInTransaction(TransactionStatus status) {
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
                //@invalid  如果status不重新赋值的话，就会报异常
                //@invalid  因为transactionManager是改变了status的状态的！
                TransactionStatus newStatus = transactionTemplate.getTransactionManager().getTransaction(def);
                Integer mainId = iBankTextMainDao.findMainId();
                //@invalid  提交事物
                if (!newStatus.isCompleted()) {
                    transactionTemplate.getTransactionManager().commit(newStatus);
                }
                return mainId;
            }
        });
    }

    /** 
     * @description 线程处理返盘
     * @<NAME_EMAIL>
     * @date 2017-11-29 下午6:52:44  
     *  @.belongToModule 收付费-集中制返盘
    */
    public class ReturnBankThread implements Callable<BankTextBO> {
    	/**
    	 * 文件
    	 */
        private File file;

        /** 
         * <p>Title: </p> 
         * <p>Description: </p> 
         * @param file 
        */
        public ReturnBankThread(File file) {
            this.file = file;
        }
        /**
         * 线程处理返盘
         */
        @Override
        public BankTextBO call() throws Exception {
            String absoluteFileName = FileUtil.getFileName(file.getAbsolutePath());
            String[] absoluteFileNames = absoluteFileName.split("\\.");
            final List<BankTextBO> boList = new ArrayList<BankTextBO>();
            BankTextBO result = null;
            if (absoluteFileNames.length == 2) {
                final TransactionTemplate transactionTemplate = TransactionUtil.getTransactionTemplate();
                //@invalid  事务控制
                transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                    protected void doInTransactionWithoutResult(TransactionStatus status) {
                        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                        //@invalid  如果status不重新赋值的话，就会报异常
                        //@invalid  因为transactionManager是改变了status的状态的！
                        TransactionStatus newStatus = transactionTemplate.getTransactionManager().getTransaction(def);
                        //@invalid  设为false
                        try {
                            BankTextBO bo = returnBankService.downLoadBankText(file);
                            boList.add(bo);
                            if (!newStatus.isCompleted()) {
                                transactionTemplate.getTransactionManager().commit(newStatus);
                            }

                        } catch (Exception e) {
                            logger.error("返盘======>ReturnBankTextBatchError返盘处理异常======>",e);
                            logger.warn("返盘======>ReturnBankTextBatch======>" + e.getMessage());
                            if (!newStatus.isCompleted()) {
                                transactionTemplate.getTransactionManager().rollback(newStatus);
                            }
                        }
                    }
                });

            }
            result = boList.get(0);
            return result;
        }

    }

    /**
     * @description 返盘处理逻辑
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param jobSessionContext 批处理参数类
     * @param bankTexts 盘文件
    */
    @SuppressWarnings("unchecked")
    private void process(JobSessionContext jobSessionContext, File[] bankTexts) {
        mainBO = new BankTextMainBO();
        final int mainId = findBankTextMainId();
        mainBO.setBatchTask(new BigDecimal(jobSessionContext.getJobId()));
        mainBO.setBatchTaskNum(new BigDecimal(jobSessionContext.getJobRunId()));
        mainBO.setBankDiskType(new BigDecimal(CodeCst.BANK_DISK_TYPE__BACK));
        mainBO.setMainId(new BigDecimal(mainId));
        mainBO.setBatchTaskTime(DateUtilsEx.getTodayDate());

        mainBO.setBackTextPackNum(new BigDecimal(bankTexts.length / 2));
        mainBO.setBackTextPackSuccNum(new BigDecimal(0));
        mainBO.setBackTextSuccAcc(new BigDecimal(0));
        mainBO.setBackTextPackFailNum(new BigDecimal(0));
        mainBO.setBackTextFailAcc(new BigDecimal(0));

        final List<BankTextBO> returnSuccBankTextBOList = new ArrayList<BankTextBO>();
        List<Future<BankTextBO>> returnBankTextBoList = new ArrayList<Future<BankTextBO>>();
        ExecutorService pool = Executors.newFixedThreadPool(CodeCst.NUMBER_TEN); //@invalid  创建线程池-初始化10
        //@invalid  成功，失败的应收应付对象、盘文件明细对象、盘文件对象组合封装对象的集合
        //@invalid         List<BankTextPremCompBO> successList = new ArrayList<BankTextPremCompBO>(); //收费成功由前端业务发短信，先注释  wugz_wb
        //@invalid  List<BankTextPremCompBO> failList = new ArrayList<BankTextPremCompBO>(); //返盘失败短信数据
        List<UnifiedCallbackBranchesBO> unifiedCallList = new ArrayList<UnifiedCallbackBranchesBO>();
        //1 循环返盘文件，依次处理
        for (final File file : bankTexts) {
            long statTM = System.currentTimeMillis();
            String absoluteFileName = FileUtil.getFileName(file.getAbsolutePath());
            String[] absoluteFileNames = absoluteFileName.split("\\.");
            if (absoluteFileNames.length == 2) { //@invalid  过滤md5校验文件
                logger.info("返盘======>" + file.getName() + "开始处理:" + statTM);
                Callable<BankTextBO> task = new ReturnBankThread(file);
                //2 返盘逻辑处理
                Future<BankTextBO> future = (Future<BankTextBO>) pool.submit(task);
                returnBankTextBoList.add(future);
            }
        }

        for (Future<BankTextBO> bo : returnBankTextBoList) {
            try {
                BankTextBO returnBankTextBO = bo.get();
                if (returnBankTextBO != null) {
                	//@invalid 返盘成功处理，返回状态12
                    if (CodeCst.BANK_TEXT_STATUS__BACK_UPLOADED.equals(returnBankTextBO.getBankTextStatus())) {
                        returnSuccBankTextBOList.add(returnBankTextBO);
                    }

                    if (returnBankTextBO.getBackTextSuccNum() != null) {
                        mainBO.setBackTextPackSuccNum(new BigDecimal(returnBankTextBO.getBackTextSuccNum()
                                .doubleValue() + mainBO.getBackTextPackSuccNum().doubleValue()));
                    }
                    if (returnBankTextBO.getBackTextSuccAcc() != null) {
                        mainBO.setBackTextSuccAcc(new BigDecimal(returnBankTextBO.getBackTextSuccAcc().doubleValue()
                                + mainBO.getBackTextSuccAcc().doubleValue()));
                    }
                    if (returnBankTextBO.getBackTextFailNum() != null) {
                        mainBO.setBackTextPackFailNum(new BigDecimal(returnBankTextBO.getBackTextFailNum()
                                .doubleValue() + mainBO.getBackTextPackFailNum().doubleValue()));
                    }
                    if (returnBankTextBO.getBackTextFailAcc() != null) {
                        mainBO.setBackTextFailAcc(new BigDecimal(returnBankTextBO.getBackTextFailAcc().doubleValue()
                                + mainBO.getBackTextFailAcc().doubleValue()));
                    }
                    if (returnBankTextBO.getArapFlag() != null) {
                        String arap = returnBankTextBO.getArapFlag();
                        String arapStr = CapVerify.ternaryOperator(CodeCst.AR_AP__AR.equals(arap),
                                CodeCst.AR_AP__AR_NAME,
                                CapVerify.ternaryOperator(CodeCst.AR_AP__AP.equals(arap), CodeCst.AR_AP__AP_NAME,
                                        CapVerify.ternaryOperator(CodeCst.AR_AP__NON_ARAP.equals(arap),
                                                CodeCst.AR_AP__NON_ARAP_NAME, "")));
                        String arapFlagStr = mainBO.getArapFlagList();
                        if ("".equals(arapFlagStr) || arapFlagStr == null) {
                            arapFlagStr = arapStr;
                        } else {
                            if (!arapFlagStr.contains(arapStr)) {
                                arapFlagStr = arapFlagStr + "," + arapStr;
                            }
                        }
                        mainBO.setArapFlagList(arapFlagStr);
                    }
                    if (returnBankTextBO.getDerivType() != null) { //@invalid  修改
                        String derivType = returnBankTextBO.getDerivType();
                        String derivTypeName = CapVerify.ternaryOperator(CodeCst.DERIV_TYPE_NBS.equals(derivType),
                                CodeCst.DERIV_TYPE_NBS_NAME, CapVerify.ternaryOperator(CodeCst.DERIV_TYPE_UW
                                        .equals(derivType), CodeCst.DERIV_TYPE_UW_NAME, CapVerify.ternaryOperator(
                                        CodeCst.DERIV_TYPE_PA.equals(derivType), CodeCst.DERIV_TYPE_PA_NAME, CapVerify
                                                .ternaryOperator(CodeCst.DERIV_TYPE_CS.equals(derivType),
                                                        CodeCst.DERIV_TYPE_CS_NAME, CapVerify.ternaryOperator(
                                                                CodeCst.DERIV_TYPE_CLM.equals(derivType),
                                                                CodeCst.DERIV_TYPE_CLM_NAME, CapVerify.ternaryOperator(
                                                                        CodeCst.DERIV_TYPE_CAP.equals(derivType),
                                                                        CodeCst.DERIV_TYPE_CAP_NAME, ""))))));
                        String derivTypeStr = mainBO.getDerivTypeList();
                        if ("".equals(derivTypeStr) || derivTypeStr == null) {
                            derivTypeStr = derivTypeName;
                        } else {
                            if (!derivTypeStr.contains(derivTypeName)) {
                                derivTypeStr = derivTypeStr + "," + derivTypeName;
                            }
                        }
                        mainBO.setDerivTypeList(derivTypeStr);
                    }
                    if (mainBO.getBankCode() == null) {
                        mainBO.setBankCode(returnBankTextBO.getBankCode());
                    }
                    if (CacheUtil
                            .containsKey(CacheRegionName.CAP_MidtableCache.toString(), CodeCst.BANK_TEXT_PROGRESS)) {
                        Map<String, BankTextVO> map = (Map<String, BankTextVO>) CacheUtil.get(
                                CacheRegionName.CAP_MidtableCache.toString(), CodeCst.BANK_TEXT_PROGRESS);
                        BankTextVO bankTextVO = map.get(String.valueOf(returnBankTextBO.getSendId()));
                        if (null != bankTextVO) {
                            map.remove(String.valueOf(returnBankTextBO.getSendId()));
                        }

                    }
                }
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
                logger.info("返盘Error:",e);
            }
        }
        pool.shutdown();
        if (mainBO.getArapFlagList() != null || mainBO.getBankCode() != null || mainBO.getDerivTypeList() != null
                || mainBO.getChannelTypeList() != null) {
            logger.info("返盘======>mainBO--ArapFlag:" + mainBO.getArapFlagList() + "--BankCode:" + mainBO.getBankCode()
                    + "--DerivType:" + mainBO.getDerivTypeList() + "--ChannelType:" + mainBO.getChannelTypeList());
          //3 制盘任务明细信息表增加返盘成功或者失败记录
            iBankTextMainDao.addBankTextMain(BeanUtils.copyProperties(BankTextMainPO.class, mainBO));

        }
        //4 返盘完成邮件通知
        if (null != returnSuccBankTextBOList && !returnSuccBankTextBOList.isEmpty()) {

            MailServiceUtil.execute(CodeCst.MAIL_NOTICE_CODE_5, returnSuccBankTextBOList, null);
            logger.info("返盘======>------开始发送返盘完成邮件通知------");
        }
    }
}
