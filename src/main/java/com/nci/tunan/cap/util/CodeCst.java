package com.nci.tunan.cap.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * @description 常量类
 * @<NAME_EMAIL>
 * @date 2015-11-19 下午3:30:32
 * @.belongToModule 收付费-公共常量
 */
public class CodeCst {
    
    /** 
     * 业务类型--新单暂收费退费
     */
    public static final String BUSINESS_TYPE_1001 = "1001";
    /**
     * 业务类型--溢交退费
     */
    public static final String BUSINESS_TYPE_1002 = "1002";
    /**
     * 业务类型--出单前退费
     */
    public static final String BUSINESS_TYPE_1003 = "1003";
    /**
     * 业务类型--预收
     */
    public static final String BUSINESS_TYPE_1004 = "1004";
    /**
     * 业务类型--核保加费
     */
    public static final String BUSINESS_TYPE_1006 = "1006";
    /**
     * @Fields BUSINESS_TYPE_7001:业务类型--社保结算
     */
    public static final String BUSINESS_TYPE_7001 = "7001";
    /**
     * @Fields BUSINESS_TYPE_1007:业务类型--续保转投
     */
    public static final String BUSINESS_TYPE_1007 = "1007";
    
    /**
     * @Fields WITHDRAW_TYPE_00602:社保-净额结算
     */
    public static final String WITHDRAW_TYPE_00602 = "0060200000";
    
    /** 
    * @Fields FEE_TYPE_0060201 : 社保实际收费
    */ 
    public static final String FEE_TYPE_0060201 = "G006020100";
    /** 
    * @Fields FEE_TYPE_0060202 : 社保应收/应付抵扣
    */ 
    public static final String FEE_TYPE_0060202 = "G006020200";
    /** 
     * @Fields outlier_check_status : 异常数据记录表状态 ： 异常
     */ 
    public static final String OUTLIER_CHECK_STATUS="1";
	/** 
     * @Fields check_status : 暂收费变更状态：通过
     */ 
    public static final String CHECK_STATUS_YES="1";
    /** 
     * @Fields check_status : 暂收费变更状态：不通过
     */
    public static final String CHECK_STATUS_NO="2";
    /** 
     * @Fields check_status : 暂收费申请结论：审核通过
     */
    public static final String CHECK_RESULT_YES="1";
    /** 
     * @Fields check_status : 暂收费申请结论：审核不通过
     */
    public static final String CHECK_RESULT_NO="2";
    /** 
     * @Fields check_status : 暂收费申请类型：暂收费信息变更
     */ 
    public static final String APPLY_TYPE_UPDATE="1";
    /** 
     * @Fields check_status : 暂收费申请类型：暂收费信息删除
     */ 
    public static final String APPLY_TYPE_DELETE="2";
    /** 
     * @Fields check_status : 暂收费申请类型：暂收费信息删除
     */ 
    public static final String DELETE_FLAG="1";
    /** 
     * @Fields check_status : 契约付费变更状态：申请中
     */ 
    public static final String CHECK_STATUS = "3";
    
    /** 
     * @Fields WITHDRAW_TYPE_00108 : 新契约承保-主险加费，附加险拒保
     */ 
    public static final String WITHDRAW_TYPE_00108 = "0010800000";
    
    /** 
     * @Fields WITHDRAW_TYPE_00444 : 新增长期附加险记账场景
     */ 
    public static final String WITHDRAW_TYPE_00444 = "0044400000";
    
    /** 
     * @Fields WITHDRAW_TYPE_00444 : 暂收费退费
     */ 
    public static final String WITHDRAW_TYPE_00499 = "0049900000";
    
    /** 
     * @Fields REALTIME_USERTYPE_INDEMNITY : 资金对账文件(操作员类型:4：单笔实时代付（无磁无密）)
     */ 
    public static final String REALTIME_USERTYPE_INDEMNITY = "4";
    /** 
     * @Fields REALTIME_CHARGETYPE_INDEMNITY : 资金对账文件(收付费类型:07-单笔实时代付)
     */ 
    public static final String REALTIME_CHARGETYPE_INDEMNITY = "07";
	/**
	 * 实时业务交易配置接口查询类型 : 1-同时校验限额和银行配置 
	 */
	public static final String REALTIME_CHECKCODE__ALL = "1";
	/**
	 * 实时业务交易配置接口查询类型 : 2-只校验银行配置
	 */
	public static final String REALTIME_CHECKCODE__BANK = "2";
	/**
	 * 实时业务交易配置接口查询类型 : 3-只校验限额 配置
	 */
	public static final String REALTIME_CHECKCODE__LIMIT = "3";
	/**
	 * 社保到账确认页面设置 : 南京
	 */
	public static final String SOCIAL_CONFIRM__ORGAN__NJ = "865100";
	/**
	 * 社保到账确认页面设置 : 江阴
	 */
	public static final String SOCIAL_CONFIRM__ORGAN__JY = "********";
	/**
	 * 社保到账确认页面设置 : 苏州
	 */
	public static final String SOCIAL_CONFIRM__ORGAN__SZ = "865101";
	/**
	 * 实时业务银行配置 : 删除
	 */
	public static final String BANK_STATUS__DELECT = "0";
	/**
	 * 实时业务银行配置 : 启动
	 */
	public static final String BANK_STATUS__START = "1";
	
	/**
	 * 费用类型 : 二核加费非保险合同产品
	 */
	public static final String FEE_TYPE__CLM50802 ="**********";
	/**
	 * 费用类型 : 二核加费保险合同产品
	 */
	public static final String FEE_TYPE__CLM50801 ="**********";
	/**
	 * 客户账户明细收付情形 : 1、续期预存
	 */
	public static final String ARAP_TYPE_ONE ="1";
	
	/**
	 * 客户账户明细收付情形 : 2、支票余额
	 */
	public static final String ARAP_TYPE_TWO ="2";
	
	/**
	 * 客户账户明细收付情形 : 3、账户收费
	 */
	public static final String ARAP_TYPE_THREE ="3";
	
	/**
	 * 客户账户明细收付情形 : 4、账户付费
	 */
	public static final String ARAP_TYPE_FOUR ="4";
	
	/**
	 * POS代理人状态：离职
	 */
	public static final String POS_AGENT_STATUS_LEAVE ="4";
	/**
	 * 用户禁用状态YES：
	 */
	public static final String USER_DISABLE_YES ="Y";
	/**
	 * 用户禁用状态NO：
	 */
	public static final String USER_DISABLE_NO = "N";
	/**
	 * @Fields POS启用状态POS_INITIATE_ON : 启用
	 */
	public static final String POS_INITIATE_ON ="1";
	/**
	 * @Fields POS启用状态POS_INITIATE_OFF : 关闭
	 */
	public static final String POS_INITIATE_OFF ="0";
	/**
	 * @Fields POS_USERTYPE_USER POS用户类型：内勤
	 */
	public static final String POS_USERTYPE_USER = "1";
	/**
	 * @Fields POS_USERTYPE_AGENT POS用户类型：代理人
	 */
	public static final String POS_USERTYPE_AGENT = "2";
	/**
     * @Fields CERTI_TYPE 证件类型 ： 无证件
     */
	public static final String CERTI_TYPE__UNDOCUMENTED = "83";
	/**
	 * @Fields PRINT_RECEIPT_STATUS__PRINTED 打印状态 ： 已打印
	 */
	public static final String PRINT_RECEIPT_STATUS__PRINTED = "01";
	/**
     * @Fields PRINT_RECEIPT_STATUS__READY : 已核销
     */
    public static final String PRINT_RECEIPT_STATUS__READY = "02";
	/**
     * @Fields RECEIPT_STATUS__CANCELLATION : 已作废
     */
    public static final String PRINT_RECEIPT_STATUS__CANCELLATION = "03";
	/**
     * @Fields 票据类型 ：1 支票收据
     */
	public static final String RECEIPT_TYPE__CHEQUE = "01";
	/**
	 * @Fields 票据类型 ：2 实收收据
	 */
	public static final String RECEIPT_TYPE__CASH = "02";
    /**
     * @Fields 销售渠道：01 个人渠道
     */
    public static final String SALES_CHANNEL_PERSONAL = "01";
    /**
     * @Fields SALES_CHANNEL_TTQD : 02 团体渠道
     */
    public static final String SALES_CHANNEL_TTQD = "02";
    /**
     * @Fields 销售渠道：03 银行代理
     */
    public static final String SALES_CHANNEL_BANKMAID = "03";
    /**
     * @Fields SALES_CHANNEL_SFYQD : 04 收费员渠道
     */
    public static final String SALES_CHANNEL_SFYQD = "04";
    /**
     * @Fields SALES_CHANNEL_DHZX : 05 电话直销
     */
    public static final String SALES_CHANNEL_DHZX = "05";
    /**
     * @Fields SALES_CHANNEL_BANKXQ : 06 银代续期
     */
    public static final String SALES_CHANNEL_BANKXQ = "06";
    /**
     * @Fields SALES_CHANNEL_XQDD : 07 续期督导
     */
    public static final String SALES_CHANNEL_XQDD = "07";
    /**
     * @Fields SALES_CHANNEL_CFGL : 08 财富管理
     */
    public static final String SALES_CHANNEL_CFGL = "08";
    /**
     * @Fields SALES_CHANNEL_ZZLC : 09 至尊理财
     */
    public static final String SALES_CHANNEL_ZZLC = "09";
    /**
     * @Fields SALES_CHANNEL_WZXS : 10 网站销售
     */
    public static final String SALES_CHANNEL_WZXS = "10";
    
    /** 
    * @Fields PAYREF_CONFIG_TYPE_CHANNELGROUP : CHANNELGROUP 配置类型：渠道分组
    */ 
    public static final String PAYREF_CONFIG_TYPE_CHANNELGROUP = "CHANNELGROUP";
    /** 
    * @Fields CHANNELGROUP_YBX : 银保信渠道
    */ 
    public static final String CHANNELGROUP_YBX = "3";
    /** 
    * @Fields CHANNELGROUP_QT : 其它渠道
    */ 
    public static final String CHANNELGROUP_QT = "2";
    /** 
     * @Fields CHANNELGROUP_YBT : 银保通渠道
     */ 
     public static final String CHANNELGROUP_YBT = "1";
     
     /** 
      * @Fields SPECIAL_ACCOUNT_FLAG_YBX : 银保信账户标记
      */ 
      public static final String  SPECIAL_ACCOUNT_FLAG_YBX = "1";
      
      /** 
       * @Fields SPECIAL_ACCOUNT_FLAG_NO : 非特殊账户标记
       */ 
       public static final String  SPECIAL_ACCOUNT_FLAG_NO = "0";
    
    /** 
     * @Fields CHANNEL_GROUP_BANK_AGENT : 1 销售渠道分组 银行代理组
     */ 
    public static final String CHANNEL_GROUP_BANK_AGENT = "1";
    /**
     * @Fields 格式定义左右：01left(左)/02right(右)
     */
    public static final String STRJUS_CODE_LEFT ="01";
    /**
     * @Fields 格式定义左右：01left(左)/02right(右)
     */
    public static final String STRJUS_CODE_RIGHT ="02";
    /**
     * @Fields SYSTEM_CODE__NB : 系统编号——新契约
     */
    public static final String SYSTEM_CODE__NB = "065";
    /**
     * 非应收应付 
     * 
     */
    public static final String AR_AP__NON_ARAP = "0";
    /**
     * 非应收应付描述
     * 
     */
    public static final String AR_AP__NON_ARAP_NAME = "非应收应付";
    /** 
     * 应收
     * 
     */
    public static final String AR_AP__AR = "1";
    /** 
     * 应收描述
     * 
     */
    public static final String AR_AP__AR_NAME = "应收";
    /** 
     * 应付
     * 
     */
    public static final String AR_AP__AP = "2";
    /** 
     * 应付描述
     * 
     */
    public static final String AR_AP__AP_NAME = "应付";
    
    /**
     * 流水编号前3个字段
     */
    public static final String UNIT_NUMBER="075";

    /** 应收 */
    public static final String ARAP_FLAG__AR = "1";
    /** 应付 */
    public static final String ARAP_FLAG__AP = "2";

    /** 是否为暂收---否 */
    public static final String TEMP_FLAG__NO = "0";
    /** 是否为暂收---是 */
    public static final String TEMP_FLAG__YES = "1";

    /** 记账状态---未记账 */
    /** 记账状态---已记账 */
    /** 记账状态---不可记账 */
    /*
     * public static final BigDecimal BOOK_FLAG__NO = new BigDecimal(1); public
     * static final BigDecimal BOOK_FLAG__YES = new BigDecimal(2); public static
     * final BigDecimal BOOK_FLAG__DISABLE = new BigDecimal(3);
     */
    /** 记账状态---未记账 */
    public static final String POSTED_FLAG__NO = "01";
    /** 记账状态---已抽取 */
    public static final String POSTED_FLAG__GET = "02";
    /** 记账状态---已记账 */
    public static final String POSTED_FLAG__YES = "03";
    /** 记账状态---月末计提类 */
    public static final String POSTED_FLAG__YMJT = "04";
    // 业务来源
    /** 新契约 */
    public static final String DERIV_TYPE_NBS = "001";
    public static final String DERIV_TYPE_NBS_NAME = "新契约";
    /** 核保 */
    public static final String DERIV_TYPE_UW = "002";
    public static final String DERIV_TYPE_UW_NAME = "核保";
    /** 续期 */
    public static final String DERIV_TYPE_PA = "003";
    public static final String DERIV_TYPE_PA_NAME = "续期";
    /** 保全 */
    public static final String DERIV_TYPE_CS = "004";
    public static final String DERIV_TYPE_CS_NAME = "保全";
    /** 理赔 */
    public static final String DERIV_TYPE_CLM = "005";
    public static final String DERIV_TYPE_CLM_NAME = "理赔";
    /** 收付费 */
    public static final String DERIV_TYPE_CAP = "006";
    public static final String DERIV_TYPE_CAP_NAME = "收付费 ";

    /** 是否有暂收--有 */
    public static final String HASCOLLECTION__YES = "Y";
    /** 是否有暂收--无 */
    public static final String HASCOLLECTION__NO = "N";

    /** 内扣标识--不是内扣 */
    public static final String DEDUCTIONFLAG__N = "N";
    /** 内扣标识--是内扣 */
    public static final String DEDUCTIONFLAG__Y = "Y";

    /** 费用状态-- 01；待收付 */

    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 00；待收付
     */
    public static final String FEE_STATUS__READY = "00";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 01；已收付
     */
    public static final String FEE_STATUS__FINISH = "01";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 02；回退
     */
    public static final String FEE_STATUS__BACK = "02";
    /**
     * @Fields FEE_STATUS__FAIL : 费用状态-- 03；转账失败
     */
    public static final String FEE_STATUS__FAIL = "03";
    /**
     * @Fields FEE_STATUS__WAY : 费用状态-- 04；转账途中
     */
    public static final String FEE_STATUS__WAY = "04";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 14；中止
     */
    public static final String FEE_STATUS__CANCELLED = "14";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 16；不可收付
     */
    public static final String FEE_STATUS__DISABLE = "16";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 15；票据未确认
     */
    public static final String FEE_STATUS__UNCONFIRMED = "15";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 17；锁定客户账户
     */
    public static final String FEE_STATUS__CUST_LOCK = "17";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 18；冻结客户账户
     */
    public static final String FEE_STATUS__CUST_BLOCK = "18";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 19；核销
     */
    public static final String FEE_STATUS__WRITE_OFF = "19";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 20；依赖收付费未完成
     */
    public static final String FEE_STATUS__DEPEND_UNDONE = "20";
    /**
     * @Fields FEE_STATUS__READY : 费用状态-- 21；退票
     */
    public static final String FEE_STATUS__BILL_REFUND = "21";
    
    /**
     * @Fields FMS_TIME_OUT : 资金系统超时异常
     */
    public static final String FMS_TIME_OUT = "08";

    /**
     * @Fields FMS_FEE_STATUS__FINISH : 资金系统通知状态-成功
     */
    public static final String FMS_TZ__FINISH = "0";

    /**
     * @Fields FMS_FEE_STATUS__FAIL : 资金系统通知状态-失败
     */
    public static final String FMS_TZ__FAIL = "1";
    /**
     * @Fields FMS_FEE_STATUS__FINISH : 资金系统状态-成功
     */
    public static final String FMS_FEE_STATUS__FINISH = "0000";

    /**
     * @Fields FMS_FEE_STATUS__FAIL : 资金系统状态-失败
     */
    public static final String FMS_FEE_STATUS__FAIL = "0001";

    /**
     * @Fields FMS_FEE_STATUS__WAIT : 资金系统状态-中间状态
     */
    public static final String FMS_FEE_STATUS__WAIT = "2000";
    
    /** 
     * @Fields FMS_AP_FEE_STATUS__FINISH : 资金系统状态-实时付款成功
     */ 
    public static final String FMS_AP_FEE_STATUS__FINISH = "S0000";
    
    /** 
     * @Fields FMS_AP_FEE_STATUS__WAIT : 资金系统状态-实时付款处理中
     */ 
    public static final String FMS_AP_FEE_STATUS__WAIT = "I4001";
    
    /** 
     * @Fields FMS_AP_FEE_STATUS__WAIT : 资金通知接口-付费-成功状态
     */ 
    public static final String FMS_AP_SUCCESS = "success";
    
    /** 
     * @Fields FMS_AP_FEE_STATUS__WAIT : 资金通知接口-付费-异常状态
     */ 
    public static final String FMS_AP_EXCEPTION = "exception";
    
    /** 
     * @Fields FMS_AP_FEE_STATUS__WAIT : 资金通知接口-付费-失败状态
     */ 
    public static final String FMS_AP_FAIL = "fail";

    /***/
    public static final String OFFSET_TRANSFER__Y = "Y";
    /***/
    public static final String OFFSET_TRANSFER__N = "N";

    /**
     * @Fields ARAP_OFFSET_STATUS__READY : 1;未冲销
     */
    public static final String ARAP_OFFSET_STATUS__READY = "1";

    /**
     * @Fields ARAP_OFFSET_STATUS__FINISH : 2;已冲销
     */
    public static final String ARAP_OFFSET_STATUS__FINISH = "2";

    /**
     * @Fields ARAP_OFFSET_STATUS__CANCELLED : 3;取消
     */
    public static final String ARAP_OFFSET_STATUS__CANCELLED = "3";

    /**
     * @Fields ARAP_OFFSET_STATUS__PREFINISH : 4;预先冲销
     */
    public static final String ARAP_OFFSET_STATUS__PREFINISH = "4";

    /**
     * @Fields SAP_REPLY_FLAG_READ : SAP回写表读取状态：1 已经被读取回写
     */
    public static final String SAP_REPLY_FLAG_READ = "1";

    /**
     * @Fields SAP_REPLY_FLAG_UNREAD : SAP回写表读取状态：0 未被读取回写
     */
    public static final String SAP_REPLY_FLAG_UNREAD = "0";

    /**
     * @Fields SAP_PAYWAY_DEBIT : SAP借贷标记：借：40
     */
    public static final String SAP_PAYWAY_DEBIT = "40";

    /**
     * @Fields SAP_PAYWAY_CREDIT : SAP借贷标记：贷：50
     */
    public static final String SAP_PAYWAY_CREDIT = "50";

    /**
     * @Fields SAP_FSERIAL_NO_PATTERN : 分录流水号格式
     */
    public static final String SAP_FSERIAL_NO_PATTERN = "0000000000000000000000000";
    /**
     * @Fields SAP_BATCH_NO_PATTERN : 批次号格式
     */
    public static final String SAP_BATCH_NO_PATTERN = "00000000000000000000";

    /**
     * @Fields CHANGE_TYPE__PAYMODE_CHG : 收付方式变更
     */
    public static final String CHANGE_TYPE__PAYMODE_CHG = "1";
    /**
     * @Fields CHANGE_TYPE__BILL_CHG : 票据信息变更
     */
    public static final String CHANGE_TYPE__BILL_CHG = "2";
    /**
     * @Fields  : 票据状态 ：票据待确认
     */
    public static final String CHEQUE_STATUS_ONE="1";
    /**
     * @Fields  : 票据状态 ：已确认
     */
    public static final String CHEQUE_STATUS_TWO="2";
    /**
     * @Fields  : 票据状态 ：退票
     */
    public static final String CHEQUE_STATUS_THREE="3";
    /**
     * @Fields CHANGE_TYPE__ABORT_CHG : 中止/恢复状态变更
     */
    public static final String CHANGE_TYPE__ABORT_CHG = "3";
    /**
     * @Fields ABORT_TYPE__OTHER : 中止原因类型
     */
    public static final String ABORT_TYPE__OTHER = "3";

    // 收付费配置
    /**
     * @Fields CONFIG_TYPE_CODE : 立即到账的支付方式
     */
    public static final String CONFIG_TYPE_CODE = "20";
    /** 收付方式---Not Relevant */
    public static final String PAY_MODE__NOT_RELEVANT = "0";
    /** 网络销售---Not Relevant */
    public static final String PAY_MODE__NET_SALES = "71";
    /** 微信支付---Not Relevant */
    public static final String PAY_MODE__WE_CHAT = "72";
    /** 中介网销(蚂蚁保)---Not Relevant */
    public static final String PAY_MODE__ZJWX = "73";
    /** 收付方式---现金 */
    public static final String PAY_MODE__CASH = "10";
    /** 收付方式---现金送款簿 */
    public static final String PAY_MODE__CASH_BOOK = "11";
    /** 收付方式---支票 CHEQUE */
    public static final String PAY_MODE__CHEQUE = "20";
    /** 现金支票 Cash Pay Invoices */
    public static final String PAY_MODE__CASH_PAY_INVOICE = "21";
    /** 转账支票 */
    public static final String PAY_MODE__TRANS_CHEQUE = "22";
    /** 收付方式---银行转账GIRO */
    public static final String PAY_MODE__GIRO = "30";
    /** 收付方式---网银转账(非制返盘) */
    public static final String PAY_MODE__GIRO_NOT_BACKDISK = "31";
    
    /** 收付方式---银行转账(制返盘) Bank transfer is made back to disk */
    /**
     * @Fields PAY_MODE__GIRO_BANKBACK : 收付方式---银行转账(制返盘)
     */
    public static final String PAY_MODE__GIRO_BANKBACK = "32";
    /** 收付方式---网银转账 internet/WIRELESS */
    
    public static final String PAY_MODE__INTERNET_TRANS = "33";
    /** 收付方式---网上银行(制返盘) Bank transfer is made back to disk */
    /**
     * @Fields PAY_MODE__ONLINE_BANK : 收付方式---网上银行
     */
    public static final String PAY_MODE__REALTIME_CHARGE = "34";
    /** 收付方式---实时收费 REAL TIME charge */
    public static final String PAY_MODE__ONLINE_BANK = "40";
    /** 收付方式---实时转账-数字人民币  */
    public static final String PAY_MODE__ONLINE_BANK_DCEP = "45";
    /** 收付方式---POS */
    public static final String PAY_MODE__POS = "41";
    /** 收付方式---内部转账 Internal Transfer */
    public static final String PAY_MODE__INTER_TRANS = "50";
    /** 收付方式---普通内部转账 */
    public static final String PAY_MODE__COMMON_INTER_TRANS = "51";
    /** 收付方式---预存内部转账 */
    public static final String PAY_MODE__PRE_INTER_TRANS = "52";
    /** 收付方式---内部转账-万能险扣费 */
    public static final String PAY_MODE__UNIVERSAL_INTER_TRANS = "53";
    /** 收付方式---内部转账-内部划转 */
    public static final String PAY_MODE__INTER_TRANSFER = "55";
    /** 收付方式---银保通 bank brokage business system */
    public static final String PAY_MODE__BBS = "60";
    /** 收付方式---第三方支付 Third-party payment */
    public static final String PAY_MODE__THIRD_PARTY_PAY = "70";
    /** 收付方式---客户暂存 */
    public static final String PAY_MODE__CUSTOMER_ACCOUNT = "80";
    /** 收付方式---其他 */
    public static final String PAY_MODE__INVEST_OTHERS = "90";
    /** 收付方式---内部抵扣 */
    public static final String PAY_MODE__DEDUCTION = "99";
    /**收付方式---社保缴纳*/
    public static final String PAY_MODE__SOCIALSECURITY = "18";
    /**收付方式---自行缴纳*/
    public static final String PAY_MODE__SELF_PAYMENT = "98";
    /** 
    * @Fields PAY_MODE_MOBILEPAYMENT : 收付方式---移动支付
    */ 
    public static final String PAY_MODE_MOBILEPAYMENT ="43";
    
    /** 
     * @Fields PAY_MODE_MOBILEPAYMENT : 收付方式---微信支付
     */ 
     public static final String PAY_MODE_WECHAT ="72";
    
    /** CPF */
    public static final String PAY_MODE__CPF = "4";
    /** 收付方式---客户账户（移动支付） */
    public static final String PAY_MODE__MOBLIEPAY = "42";

    // 银行制返盘状态 制盘状态要统一，同一个状态在不同表使用也要定义一个码值 ，需要重新整理
    /**
     * 1:待制盘,2:制盘中,3:返盘成功,4:返盘失败
     */
    /** 待制盘 */
    public static final String BANK_TEXT_STATUS__INIT = "1";
    /** 预制盘成功 */
    public static final String BANK_TEXT_STATUS__READY_SUCC = "2";
    /** 预制盘回退 */
    public static final String BANK_TEXT_STATUS__READY_ROLLBACK = "3";
    /** 制盘中 */
    public static final String BANK_TEXT_STATUS__TRANSFERING = "4";
    /** 制盘失败 */
    public static final String BANK_TEXT_STATUS__FAIL = "5";
    /** 制盘成功 */
    public static final String BANK_TEXT_STATUS__SUCC = "6";
    /** 返盘成功 */
    public static final String BANK_TEXT_STATUS__BACK_SUCC = "7";
    /** 返盘失败 */
    public static final String BANK_TEXT_STATUS__BACK_FAIL = "8";
    /** 下载 */
    public static final String BANK_TEXT_STATUS__DOWNLOADED = "9";
    /** 上传 */
    public static final String BANK_TEXT_STATUS__UPLOADED = "10";
    /** 返盘上传成功 */
    public static final String BANK_TEXT_STATUS__BACK_UPLOADED = "12";
    /** 制盘 */
    public static final String BANK_DISK_TYPE__TOTRANS = "1";
    /** 返盘 */
    public static final String BANK_DISK_TYPE__REBACK = "2";
    
    /* *//** BANK_TEXT_GROUP 预制盘 */
    /*
     * public static final String BANK_TEXT_GROUP__BEFOREHAND = "2";
     *//** BANK_TEXT_GROUP 预制盘回退 */
    /*
     * public static final String BANK_TEXT_GROUP__BACK_ROLLBACK = "3";
     *//**
     * T_BANK_TEXT 盘文件状态 2 预制盘成功 3 预制盘回退 5 制盘失败 6 制盘成功 7 返盘成功 8 返盘失败 9 制盘下载成功
     * 10 返盘上传成功
     **/
    /*
    
    *//** 预制盘回退 */
    /*
     * public static final String BANK_TEXT_STATUS__CANCEL = "3";
     *//** 盘文件信息表中返盘失败的状态代码 */
    /*
     * public static final String BANK_TEXT_FAIL = "8";
     * 
     * public static final String BANK_TEXT_DETAIL_FAIL = "2";
     * 
     * public static final String BANK_TEXT_GROUP_FAIL = "5";
     * 
     * public static final String BANK_TEXT_PREM_ARAP_FAIL = "8";
     *//** 盘文件信息表中返盘成功的状态代码 */
    /*
     * public static final String BANK_TEXT_SUC = "7";
     * 
     * public static final String BANK_TEXT_DETAIL_SUC = "1";
     * 
     * public static final String BANK_TEXT_GROUP_SUC = "4";
     * 
     * public static final String BANK_TEXT_PREM_ARAP_SUC = "7";
     */

    public static final String BANK_TEXT_YES = "1";

    public static final String BANK_TEXT_NO = "0";

    /**
     * @Fields SAP_RULE_TYPE__Y : SAP规则类型 Y
     */
    public static final String SAP_RULE_TYPE__Y = "Y";
    /**
     * @Fields SAP_RULE_TYPE__N : SAP规则类型 N
     */
    public static final String SAP_RULE_TYPE__N = "N";
    /**
     * @Fields SAP_RULE_TYPE__F : SAP规则类型 F
     */
    public static final String SAP_RULE_TYPE__F = "F";

    /**
     * @Fields SAP_FEE_TABLE__CASH : SAP记账来源表--实收实付
     */
    public static final String SAP_FEE_TABLE__CASH = "1";
    /**
     * @Fields SAP_FEE_TABLE__ARAP : SAP记账来源表--应收应付
     */
    public static final String SAP_FEE_TABLE__ARAP = "2";
    /**
     * @Fields SAP_FEE_TABLE__YSWS : SAP记账来源表--应收未收
     */
    public static final String SAP_FEE_TABLE__YSWS = "3";

    /**
     * @Fields CHEQUE_STATUS__TO_BE_CONFIRMED : 支票状态---已登记
     */
    public static final String CHEQUE_STATUS__TO_BE_CONFIRMED = "1";
    /**
     * @Fields CHEQUE_STATUS__CONFIRMED : 支票状态---已确认
     */
    public static final String CHEQUE_STATUS__CONFIRMED = "2";
    /**
     * @Fields CHEQUE_STATUS__CANCELLED : 支票状态---已退票
     */
    public static final String CHEQUE_STATUS__CANCELLED = "3";
    /** 发票打印状态标识 */
    /**
     * @Fields RECEIPT_STATUS_UNPRINTED 未打印:
     */
    public static final String RECEIPT_STATUS_UNPRINTED = "1";
    /**
     * @Fields RECEIPT_STATUS_PRINTED : 已打印
     */
    public static final String RECEIPT_STATUS_PRINTED = "2";
    /**
     * @Fields RECEIPT_STATUS_PRINTING : 打印中
     */
    public static final String RECEIPT_STATUS_PRINTING = "3";
    /**
     * @Fields RECEIPT_STATUS_CANCELLATION : 已作废
     */
    public static final String RECEIPT_STATUS_CANCELLATION = "4";
    /**
     * @Fields RECEIPT_STATUS_REDBOOK : 已红冲
     */
    public static final String RECEIPT_STATUS_REDBOOK = "5";
    /**
     * @Fields RECEIPT_STATUS_REDBOOKING : 冲正中
     */
    public static final String RECEIPT_STATUS_REDBOOKING = "6";

    /** 发票签收质检状态 begin */
    /**
     * @Fields RECEIPT_BATCH_STATUS_SEND : 已发送
     */
    public static final String RECEIPT_BATCH_STATUS_SEND = "01";
    /**
     * @Fields RECEIPT_BATCH_STATUS_PRINT : 已打印
     */
    public static final String RECEIPT_BATCH_STATUS_PRINT = "02";
    /**
     * @Fields RECEIPT_BATCH_STATUS_SIGN : 已签收未质检
     */
    public static final String RECEIPT_BATCH_STATUS_SIGN = "03";
    /**
     * @Fields RECEIPT_BATCH_STATUS_SIGNQC : 已签收已质检
     */
    public static final String RECEIPT_BATCH_STATUS_SIGNQC = "04";
    /** 发票签收质检状态 end */

    /** 支票退票原因 */

    /**
     * @Fields CHEQUE_REFUND_REASON_CANCEL : 撤销
     */
    public static final String CHEQUE_REFUND_REASON_CANCEL = "5";
    /**
     * @Fields CHEQUE_REFUND_REASON_OTHER : 其他
     */
    public static final String CHEQUE_REFUND_REASON_OTHER = "6";
    /**
     * @Fields BANK_TEXT_PATH : 盘文件路径
     */
    public static final String BANK_TEXT_PATH = "e:\\cap\\cap-impl\\src\\main\\java\\com\\nci\\tunan\\cap\\util";
    
    /**
     * @Fields SERVICE_CODE_RB 保全回退保全项
     */
    public static final String SERVICE_CODE_RB ="RB";

    /** 其他 */
    public static final int DERIVATION_SYSTEM__OTHERS_BIZ = 0;
    /** 新契约 */
    public static final int DERIVATION_SYSTEM__NBS_BIZ = 1;
    /** 保全 */
    public static final int DERIVATION_SYSTEM__CUS_BIZ = 2;
    /** 续期 */
    public static final int DERIVATION_SYSTEM__PA_BIZ = 3;
    /** 理赔 */
    public static final int DERIVATION_SYSTEM__CLM_BIZ = 4;
    /** 单证 */
    public static final int DERIVATION_SYSTEM__BMS_BIZ = 5;
    /** 收付费自动产生 */
    public static final int DERIVATION_SYSTEM__CAP_BIZ = 6;
    /** 银行账户状态 */
    /** 银行账户状态 ：1 有效 **/
    public static final int BANK_ACCOUNT_YES = 1;
    /** 银行账户状态 ：0 无效 **/
    public static final int BANK_ACCOUNT_NO = 0;
    /** 是否使用客户账户标识 **/
    /** 是否使用客户账户标识 1:使用 **/
    public static final int CUSTOMER_ACCOUNT_FLAG_YES = 1;
    /** 是否使用客户账户标识 0:不使用 **/
    public static final int CUSTOMER_ACCOUNT_FLAG_NO = 1;

    /** 允许制盘 */
    public static final int IS_BANK_TEXT_YES = 1;
    /** 不允许制盘 */
    public static final int IS_BANK_TEXT_NO = 0;
    /**
     * 分隔符
     */
    public static final String TRIM = "-";
    /**
     * 邮件主题
     */
    public static final String MAIL_SUBJECT = "-回盘失败";
    /**
     * 邮件内容
     */
    public static final String MAIL_CONTENT = "-回盘失败，请核实回盘文件信息的正确性";
    /**
     * 校验失败主题
     */
    public static final String VALIDATE_FAIL_MAIL_SUBJECT = "-回盘成功-规则校验失败";
    /**
     *校验失败内容
     */
    public static final String VALIDATE_FAIL_MAIL_CONTENT = "-回盘成功-规则校验失败，请核实回盘文件信息的正确性";
    /**
     * 校验成功主题
     */
    public static final String VALIDATE_SUC_MAIL_SUBJECT = "-回盘成功";
    /**
     * 校验成功内容
     */
    public static final String VALIDATE_SUC_MAIL_CONTENT = "-回盘成功";
    /**
     * 扣款失败主题
     */
    public static final String DETAIL_FAIL_MAIL_SUBJECT = "-扣款失败";
    /**
     * 扣款失败内容
     */
    public static final String DETAIL_FAIL_MAIL_CONTENT = "-扣款失败，请核实账号信息的正确性";

    /** 邮件发送人的配置代码 */
    public static final String MAIL_ADDR_FROM_CODE = "01";
    /** 邮件接受人的配置代码 */
    public static final String MAIL_ADDR_TO_CODE = "02";
    /** 邮件notice_code **/
    /** 邮件notice_code 1：Michael测试邮件 **/
    public static final String MAIL_NOTICE_CODE_1 = "1";
    /** 邮件notice_code 2：回盘成功-规则校验失败，请核实回盘文件信息的正确性 **/
    public static final String MAIL_NOTICE_CODE_2 = "2";
    /** 邮件notice_code 3：制盘文件传输完成提醒 **/
    public static final String MAIL_NOTICE_CODE_3 = "3";
    /** 邮件notice_code 4：返盘文件错误报告提醒 **/
    public static final String MAIL_NOTICE_CODE_4 = "4";
    /** 邮件notice_code 5：返盘文件处理完成通知 **/
    public static final String MAIL_NOTICE_CODE_5 = "5";
    /** 邮件notice_code 6：制返盘失败数据发送通知 **/
    public static final String MAIL_NOTICE_CODE_6 = "6";
    /** 邮件notice_code 7：回盘成功 **/
    public static final String MAIL_NOTICE_CODE_7 = "7";
    /** 邮件notice_code 8：回盘失败，请核实回盘文件信息的正确性 **/
    public static final String MAIL_NOTICE_CODE_8 = "8";
    /** 邮件notice_code 9：疑似重复收付数据提醒 **/
    public static final String MAIL_NOTICE_CODE_9 = "9";
    /** 邮件notice_code 12：预制盘完成提醒 **/
    public static final String MAIL_NOTICE_CODE_12 = "12";
    /** 邮件notice_code 17：月结清单报表统计 **/
    public static final String MAIL_NOTICE_CODE_17 = "17";
    // 银行回盘信息描述码表 t_bank_ret_conf
    /** E1409 付款行未开通业务 */
    public static final String BANK_RET_CONF__E1409 = "E1409";
    /** E1503 客户信息存在非法字符 */
    public static final String BANK_RET_CONF__E1503 = "E1503";
    /** E1003 账户冻结 */
    public static final String BANK_RET_CONF__E1003 = "E1003";
    /** E1004 公司账户余额不足 */
    public static final String BANK_RET_CONF__E1004 = "E1004";
    /** E1004 客户账户余额不足 */
    public static final String BANK_RET_CONF__CUSTOMER_E1004 = "E1004";
    /** E1005 账户已销户 */
    public static final String BANK_RET_CONF__E1005 = "E1005";
    /** E1006 账户已挂失 */
    public static final String BANK_RET_CONF__E1006 = "E1006";
    /** E1007 账户状态异常 */
    public static final String BANK_RET_CONF__E1007 = "E1007";
    /** E1008 账户类型有误 */
    public static final String BANK_RET_CONF__E1008 = "E1008";
    /** E1009 账户不存在或状态异常 */
    public static final String BANK_RET_CONF__E1009 = "E1009";
    /** E1010 账号有误 */
    public static final String BANK_RET_CONF__E1010 = "E1010";
    /** E1011 户名有误 */
    public static final String BANK_RET_CONF__E1011 = "E1011";
    /** E1012 账户非法 */
    public static final String BANK_RET_CONF__E1012 = "E1012";
    /** E1013 账号或户名有误 */
    public static final String BANK_RET_CONF__E1013 = "E1013";
    /** E1014 银行信息有误 */
    public static final String BANK_RET_CONF__E1014 = "E1014";
    /** E1015 开户行信息有误 */
    public static final String BANK_RET_CONF__E1015 = "E1015";
    /** E1016 过期卡 */
    public static final String BANK_RET_CONF__E1016 = "E1016";
    /** E1017 不支持旧账号 */
    public static final String BANK_RET_CONF__E1017 = "E1017";
    /** E1018 不支持存折账号 */
    public static final String BANK_RET_CONF__E1018 = "E1018";
    /** E1019 证件号码或姓名与账户不符 */
    public static final String BANK_RET_CONF__E1019 = "E1019";
    /** E1020 不能对此类账户做此类交易 */
    public static final String BANK_RET_CONF__E1020 = "E1020";
    /** E1021 卡号不合法或不存在 */
    public static final String BANK_RET_CONF__E1021 = "E1021";
    /** E1022 不允许跨省市交易或不支持存折 */
    public static final String BANK_RET_CONF__E1022 = "E1022";
    /** E1023 客户姓名不符 */
    public static final String BANK_RET_CONF__E1023 = "E1023";
    /** E1024 因帐户长期不动帐，请到柜台办理业务 */
    public static final String BANK_RET_CONF__E1024 = "E1024";
    /** E1025 输入帐号卡号错 */
    public static final String BANK_RET_CONF__E1025 = "E1025";
    /** E1026 非结算账户无法入账 */
    public static final String BANK_RET_CONF__E1026 = "E1026";
    /** E1027 系统开户行信息不正确 */
    public static final String BANK_RET_CONF__E1027 = "E1027";
    /** E1051 发起方账户未签约 */
    public static final String BANK_RET_CONF__E1051 = "E1051";
    /** E1052 发起方账户未激活 */
    public static final String BANK_RET_CONF__E1052 = "E1052";
    /** E1053 发起方账户无权限 */
    public static final String BANK_RET_CONF__E1053 = "E1053";
    /** E1054 发起方账户不存在或状态异常 */
    public static final String BANK_RET_CONF__E1054 = "E1054";
    /** E1055 未设置清算户 */
    public static final String BANK_RET_CONF__E1055 = "E1055";
    /** E1056 未设置业务额度 */
    public static final String BANK_RET_CONF__E1056 = "E1056";
    /** E1057 同批次中不能有相同卡折号的记录存在 */
    public static final String BANK_RET_CONF__E1057 = "E1057";
    /** E1119 柜员无此类凭证 */
    public static final String BANK_RET_CONF__E1119 = "E1119";
    /** E1201 金额超限 */
    public static final String BANK_RET_CONF__E1201 = "E1201";
    /** E1202 金额有误 */
    public static final String BANK_RET_CONF__E1202 = "E1202";
    /** E1203 币种有误 */
    public static final String BANK_RET_CONF__E1203 = "E1203";
    /** E1204 无效业务 */
    public static final String BANK_RET_CONF__E1204 = "E1204";
    /** E1205 超过额度限制 */
    public static final String BANK_RET_CONF__E1205 = "E1205";
    /** E1206 银行区域有误 */
    public static final String BANK_RET_CONF__E1206 = "E1206";
    /** E1207 处理活期产品主档错误 */
    public static final String BANK_RET_CONF__E1207 = "E1207";
    /** E1208 无一笔入帐成功 */
    public static final String BANK_RET_CONF__E1208 = "E1208";
    /** E1301 提交失败 */
    public static final String BANK_RET_CONF__E1301 = "E1301";
    /** E1302 记录不存在 */
    public static final String BANK_RET_CONF__E1302 = "E1302";
    /** E3001 支付包错误 */
    public static final String BANK_RET_CONF__E3001 = "E3001";
    /** E9111 提取数据异常 */
    public static final String BANK_RET_CONF__E9111 = "E9111";
    /** E9777 报文异常 */
    public static final String BANK_RET_CONF__E9777 = "E9777";
    /** E9981 通讯参数配置错误 */
    public static final String BANK_RET_CONF__E9981 = "E9981";
    /** E9999 通讯失败 */
    public static final String BANK_RET_CONF__E9999 = "E9999";
    /** E4001 外部系统错误 */
    public static final String BANK_RET_CONF__E4001 = "E4001";
    /** E4002 外部系统通讯失败 */
    public static final String BANK_RET_CONF__E4002 = "E4002";
    /** E8001 其它错误 */
    public static final String BANK_RET_CONF__E8001 = "E8001";
    /** E1001 账户不存在 */
    public static final String BANK_RET_CONF__E1001 = "E1001";
    /** E1002 账户关闭 */
    public static final String BANK_RET_CONF__E1002 = "E1002";
    /** S0000 成功 */
    public static final String BANK_RET_CONF__S000 = "S000";
    /** E120E 没有符合条件的记录 */
    public static final String BANK_RET_CONF__E120E = "E120E";
    /** E1041 银行卡省份不符合要求 */
    public static final String BANK_RET_CONF__E1041 = "E1041";
    /** E1042 文件中记录条数超过限制 */
    public static final String BANK_RET_CONF__E1042 = "E1042";
    /** E1043 总笔数非法 */
    public static final String BANK_RET_CONF__E1043 = "E1043";
    /** E1044 已超过此交易当天允许执行最大次数 */
    public static final String BANK_RET_CONF__E1044 = "E1044";
    /** E1045 用途信息不符合企业财务室要求 */
    public static final String BANK_RET_CONF__E1045 = "E1045";
    /** E1046 收费金额超过单笔最大金额 */
    public static final String BANK_RET_CONF__E1046 = "E1046";
    /** E1047 未注册企业财务室 */
    public static final String BANK_RET_CONF__E1047 = "E1047";
    /** E1048 指令处理失败 */
    public static final String BANK_RET_CONF__E1048 = "E1048";
    /** E1049 错误代码获取有误 */
    public static final String BANK_RET_CONF__E1049 = "E1049";
    /** E1401 此账户未缴纳小额账户管理费 */
    public static final String BANK_RET_CONF__E1401 = "E1401";
    /** E1402 柜员无此类凭证 */
    public static final String BANK_RET_CONF__E1402 = "E1402";
    /** E1008 储蓄类账户不支持代付交易 */
    public static final String BANK_RET_CONF__E1408 = "E1008";
    /** E1502 账号未授权 */
    public static final String BANK_RET_CONF__E1502 = "E1502";
    /** E8001 客户信息不完整 */
    public static final String BANK_RET_CONF__E8009 = "E8009";
    /** E1459 密码挂失 */
    public static final String BANK_RET_CONF__E1459 = "E1459";
    /** E1443 合同（协议）号在协议库里不存在 */
    public static final String BANK_RET_CONF__E1443 = "E1443";
    public static final String BANK_TRANS_SUC = "1";

    /** 配置信息类型 t_payref_config.config_type */
    public static final String PAYREF_CONFIG_TYPE_FTP = "2";
    /** ftp文件服务器的Ip地址的配置代码 */
    public static final String FTP_SERVER_IP = "11";
    /** ftp文件服务器的端口的配置代码 */
    public static final String FTP_SERVER_PORT = "12";
    /** ftp文件服务器的登陆用户名的配置代码 */
    public static final String FTP_USER_NAME = "13";
    /** ftp文件服务器的登陆用户名的配置代码 */
    public static final String FTP_PASSWORD = "14";
    /** FTP服务器存放集中制返盘-制盘文件路径的配置代码 */
    public static final String REMOTE_CATALOG_CODE = "15";
    /**windows共享文件服务器存放集中制返盘-新核心产生的收款文件上传目录 new*/
    public static final String REMOTE_CATALOG_REC_CODE = "26";
    /**windows共享文件服务器存放集中制返盘-新核心产生的付款文件上传目录new*/
    public static final String REMOTE_CATALOG_PAY_CODE = "27";
    /** FTP服务器存放集中制返盘-返盘文件路径的配置代码 */
    public static final String BACK_REMOTE_CATALOG_CODE = "19";
    
    /** 移动支付对账ftp文件服务器的登陆用户名配置代码 */
    public static final String YDZF_USER_NAME = "40";
    /** 移动支付对账ftp文件服务器的登陆用户名的配置代码 */
    public static final String YDZF_PASSWORD = "41";
    /** 移动支付对账ftp文件服务器的登陆用户名配置代码 */
    public static final String YDZF_READ = "42";
    /** 移动支付对账ftp文件服务器的登陆用户名的配置代码 */
    public static final String YDZF_BACKUPS = "43";
    /********************VMS*****************************/
    /** 配置信息类型 t_payref_config.config_type */
    public static final String PAYREF_CONFIG_TYPE_FTP_VMS = "4";
    /** ftp文件服务器的Ip地址的配置代码 */
    public static final String FTP_SERVER_IP_VMS = "01";
    /** ftp文件服务器的端口的配置代码 */
    public static final String FTP_SERVER_PORT_VMS = "02";
    /** ftp文件服务器的登陆用户名的配置代码 */
    public static final String FTP_USER_NAME_VMS = "03";
    /** ftp文件服务器的登陆密码的配置代码 */
    public static final String FTP_PASSWORD_VMS = "04";
    /** FTP服务器存放VMS发票信息文件路径的配置代码 */
    public static final String REMOTE_CATALOG_CODE_VMS = "05";
    /** ftp文件服务器的数据条数 */
    public static final String FTP_FILE_SIZE_VMS = "06";
    /**windows共享文件服务器存放VMS发票信息文件上传目录*/
    public static final String REMOTE_CATALOG_REC_CODE_VMS = "26";
    
    /*******************实收实付日结传输************************/
    /** 配置信息类型 t_payref_config.config_type */
    public static final String PAYREF_CONFIG_TYPE_FTP_DS = "4";
    /** ftp文件服务器的Ip地址的配置代码 */
    public static final String FTP_SERVER_IP_DS = "01";
    /** ftp文件服务器的端口的配置代码 */
    public static final String FTP_SERVER_PORT_DS = "02";
    /** ftp文件服务器的登陆用户名的配置代码 */
    public static final String FTP_USER_NAME_DS = "03";
    /** ftp文件服务器的登陆密码的配置代码 */
    public static final String FTP_PASSWORD_DS = "04";
    /** FTP服务器日结发票信息文件路径的配置代码 */
    public static final String REMOTE_CATALOG_CODE_DS = "05";
    /** ftp文件服务器的数据条数 */
    public static final String FTP_FILE_SIZE_DS = "06";
    /** 本地存放日结信息文件路径的配置代码 */
    public static final String LOCAL_CATALOG_CODE_DS = "07";
    /** AES加密密钥 */
    public static final String AES_KEY = "08";
    
    /**E保通超时时间设置*/
    public static final String EBT_TIMEOUT = "01";
    /**E保通设置调用资金挡板 */
    public static final String EBT_AR_BAFFLE = "02";
    /**实时付款设置调用资金挡板 */
    public static final String EBT_AP_BAFFLE = "03";
    
    
    /**windows共享文件服务器的Ip地址的配置代码 */
    public static final String SMBFILE_SERVER_IP = "11" ;
    /**windows共享文件服务器的的登陆用户名的配置代码 */
    public static final String SMBFILE_USER_NAME = "13";
    /**windows共享文件服务器的登陆密码的配置代码 */
    public static final String SMBFILE_PASSWORD = "14";
    /**windows共享文件服务器存放集中制返盘-资金系统收款返盘文件存放目录（供新核心系统下载）new*/
    public static final String BACK_REMOTE_CATALOG_REC_CODE = "28";
    /**windows共享文件服务器存放集中制返盘-资金系统付款返盘文件存放目录（供新核心系统下载）new */
    public static final String BACK_REMOTE_CATALOG_PAY_CODE = "29";
    /**windows共享文件服务器存放集中制返盘-资金系统收款返盘文件存放目录（供新核心系统下载完成后的备份目录）new*/
    public static final String BACK_REMOTE_REC_BAK_CODE = "30";
    /**windows共享文件服务器存放集中制返盘-资金系统付款返盘文件存放目录（供新核心系统下载完成后的备份目录）new */
    public static final String BACK_REMOTE_PAY_BAK_CODE = "31";
    /** 本地存放制盘文件的位置的配置代码 */
    public static final String LOCAL_CATALOG_CODE = "16";
    /** 本地存放下载后的返盘文件后续解析使用 */
    public static final String LOCAL_CONFIG_CODE = "21";
    /** 超过三次不允许制盘 */
    public static final String FAIL_TIMES_CODE = "24";
    /** 新契约一轮最大制盘次数 */
    public static final String FAIL_TIMES_NB = "2";
    /** 新契约二轮最大制盘次数 */
    public static final String FAIL_TIMES_NB_SEC = "6";
    /** 银代电子渠道数据最大制盘次数 */
    public static final String FAIL_TIMES_NB_YDDZQD = "35";
    /** 预制盘处理日 = 续期宽限期终止日– D **/
    public static final String D_END_DAYS_CODE = "25";
    /** 是否转实收付 标志 **/
    /** 是否转是否标志 0 未转实收付 **/
    public static final String NO_WRITE_CASH = "0";
    /** 是否转是否标志 1 已转实收付 **/
    public static final String IS_WRITE_CASH = "1";
    /** 收付费系统编号 */
    public static final String SYSTEM_CODE__CAP = "075";
    /** 收付费系统收编号 */
    public static final String SYSTEM_CODE__CAP_S = "SQT";
    /** 收付费系统付编号 */
    public static final String SYSTEM_CODE__CAP_F = "FQT";
    /**
     * 否定标志:"0"
     */
    public static final String YES_NO__NO = "0";
    /**
     * 确认标志:"1"
     */
    public static final String YES_NO__YES = "1";
    /**
     * 数值:0
     */
    public static final BigDecimal YES_NO__NUMNO = new BigDecimal(0);
    /**
     * 数值:1
     */
    public static final BigDecimal YES_NO__NUMYES = new BigDecimal(1);

    /** 集中制返盘每个盘记录扣款信息的条数分包数10000 */
    public static final Integer BANK_TEXT_FILE__DETAIL_COUNT = 10000;
    /** 盘文件组号前置字符G */
    public static final String BANK_TEXT_FILE__GROUP_PREFIX_CHAR = "G";
    /** 盘文件最大组号G1G2G3 */
    public static final Integer BANK_TEXT_FILE__GROUP_NUM = 3;
    /** 处理内容 */
    /** 处理内容 1 集中预制盘 */
    public static final Integer BANK_DISK_TYPE__PRE = 1;
    /** 处理内容 2 集中制盘 */
    public static final Integer BANK_DISK_TYPE__BANK = 2;
    /** 处理内容 3 集中返盘 */
    public static final Integer BANK_DISK_TYPE__BACK = 3;
    /** 处理内容 4 分公司预制盘 */
    public static final Integer BANK_DISK_TYPE__ORAGN_PRE = 4;
    /** 处理内容 5 分公司制盘 */
    public static final Integer BANK_DISK_TYPE__ORGAN_BANK = 5;
    /** 处理内容 6 分公司返盘 */
    public static final Integer BANK_DISK_TYPE__ORAGN_BACK = 6;

    /**
     * FUNCTIONFLAG_PAYMENT 交易码 42--续期缴费
     */
    public static final String FUNCTIONFLAG_PAYMENT = "42";

    /**
     * @Fields FUNCTIONFLAG_RENEWALQUERY : 交易码-- 41；续期缴费查询
     */
    public static final String FUNCTIONFLAG_RENEWALQUERY = "41";

    /**
     * 续期缴费冲正 交易码43 FUNCTIONFLAG_RENEWALAMEND
     */
    public static final String FUNCTIONFLAG_RENEWALAMEND = "43";

    /**
     * FunctionFlag 交易码44重打
     */
    public static final String FUNCTIONFLAG_TOPRINT = "44";

//    /**
//     * 挂起标识01 operateType
//     */
//    public static final String OPERATE_TYPE_SUSPEND = "01";
//
//    /**
//     * 解挂标识02
//     */
//    public static final String OPERATE_TYPE_SOLUTION = "02";

    /**
     * 数据库状态 正常01
     */
    public static final String FROZEN_STATUS_SOLUTION = "01";

    /**
     * 数据库状态 挂起03
     */
    public static final String FROZEN_STATUS_SUSPEND = "03";
    /*
     * 智能POS收费专属用户
     */
    public static final BigDecimal FROZEN_STATUS_BY_POS = new BigDecimal(2098020980);
    /*
     * 小契约虚拟用户，此用户数据推送小契约
     */
    public static final String USER_NAME_BY_CIP = "xuniSSF001";
    // --------- 交易类型------------
    /**
     * @Fields TRANSACTION_TYPE__DEPOSIT : 存款:10
     */
    public static final String TRANSACTION_TYPE__DEPOSIT = "10";
    /**
     * @Fields TRANSACTION_TYPE__DRAW : 取款:20
     */
    public static final String TRANSACTION_TYPE__DRAW = "20";
    /**
     * @Fields TRANSACTION_TYPE__BIZ_DEPOSIT : 业务存款 :11
     */
    public static final String TRANSACTION_TYPE__BIZ_DEPOSIT = "11";
    /**
     * @Fields TRANSACTION_TYPE__BIZ_DRAW : 业务取款 :21
     */
    public static final String TRANSACTION_TYPE__BIZ_DRAW = "21";

    // -----------交易状态-------------
    /**
     * @Fields TEMP_CASH_STATUS__COLLING : 收款中 10
     */
    public static final String TEMP_CASH_STATUS__COLLING = "10";
    /**
     * @Fields TEMP_CASH_STATUS__COLLED : 已收款 11
     */
    public static final String TEMP_CASH_STATUS__COLLED = "11";
    /**
     * @Fields TEMP_CASH_STATUS__COLLCAL : 收账取消 12
     */
    public static final String TEMP_CASH_STATUS__COLLCAL = "12";
    /**
     * @Fields TEMP_CASH_STATUS__PAYING : 付款中 20
     */
    public static final String TEMP_CASH_STATUS__PAYING = "20";
    /**
     * @Fields TEMP_CASH_STATUS__PAYDE : 已付款 21
     */
    public static final String TEMP_CASH_STATUS__PAYED = "21";
    /**
     * @Fields TEMP_CASH_STATUS__PAYCAL : 付款取消 22
     */
    public static final String TEMP_CASH_STATUS__PAYCAL = "22";

    // ---------------冻结锁定状态-----------
    /**
     * @Fields TEMP_CASH_STATUS__COLLING : 10：已冻结
     */
    public static final String BLOCK_STATUS__BLOCK = "10";
    /**
     * @Fields BLOCK_STATUS__UNBLOCK : 11：已解冻
     */
    public static final String BLOCK_STATUS__UNBLOCK = "11";
    /**
     * @Fields BLOCK_STATUS__LOCK : 20：已锁定
     */
    public static final String BLOCK_STATUS__LOCK = "20";
    /**
     * @Fields BLOCK_STATUS__UNLOCK : 21：已解锁
     */
    public static final String BLOCK_STATUS__UNLOCK = "21";

    /** 分公司配置 */
    /**
     * 盘文件类型txt
     */
    public static final String DISK_FILE_TYPE_TXT = "6";

    /**
     * 盘文件类型txt
     */
    public static final String DISK_FILE_TYPE_TXT_SUFFIX = ".txt";
    /**
     * 盘文件类型xls
     */
    public static final String DISK_FILE_TYPE_XLS = "4";
    /**
     * 盘文件类型xls
     */
    public static final String DISK_FILE_TYPE_EXCEL = "9";
    /**
     * 盘文件类型xls
     */
    public static final String DISK_FILE_TYPE_XLS_SUFFIX = ".xls";
    /**
     * 盘文件类型ISF
     */
    public static final String DISK_FILE_TYPE_ISF = "11";
    /**
     * 盘文件类型ISF
     */
    public static final String DISK_FILE_TYPE_ISF_SUFFIX = ".ISF";
    /** 汇总行类型表T_SUM_LINE_TYPE */
    /** 无 */
    public static final String SUM_LINE_TYPE__NO = "0";
    /** 文件前部 */
    public static final String SUM_LINE_TYPE__BEFORE = "1";
    /** 文件后部 */
    public static final String SUM_LINE_TYPE__AFTER = "2";

    /** T_ORGAN_BANK_DISK_CONFIG Validity 分公司制盘配置是否有效 1有 0 无 */
    public static final String ORGAN_CONFIG_VALIDITY_YES = "1";
    /** T_ORGAN_BANK_DISK_LINECONFIG lineType行配置匹配 判断行类型是汇总 还是明细 1汇总 2明细 */
    public static final String ORGAN_LINECONFIG_LINETYPE_SUM = "1";
    /** T_ORGAN_BANK_DISK_LINECONFIG lineType行配置匹配 判断行类型是汇总 还是明细 1汇总 2明细 */
    public static final String ORGAN_LINECONFIG_LINETYPE_INFO = "2";
    /** T_ORGAN_BANK_DISK_LINECONFIG SEPARATOR_FLAG 是否间隔 1是 0否 */
    public static final String ORGAN_LINECONFIG_SEPARATOR_FLAG_YES = "1";

    /** T_DISK_TYPE 分公司格式配置盘类型 1:制盘 2:返盘 */
    public static final String ORGAN_DISK_TYPE__TEXT = "1";
    /** T_DISK_TYPE 分公司格式配置盘类型 1:制盘 2:返盘 */
    public static final String ORGAN_DISK_TYPE__BACK_TEXT = "2";

    /**
     * t_BANK_TEXT_TYPE 制盘类型 (制盘来源) 1:应收应付 2:客户账户
     */
    public static final String FMS_BANK_TEXT_TYPE__ARAP = "1";
    /**
     * t_BANK_TEXT_TYPE 制盘类型 (制盘来源) 1:应收应付 2:客户账户
     */
    public static final String FMS_BANK_TEXT_TYPE__CUS_ACC = "2";

    /** 密文 用作存放集中制返盘的3des加密文件夹名称 */
    public static final String BANK_TEXT_CIPHERTEXT = "Ciphertext";
    /** 明文 用作存放集中制返盘的3des解密文件夹名称 */
    public static final String BANK_TEXT_PLAINTEXT = "Plaintext";
    /**新核心产生的收款文件上传目录*/
    public static final String BANK_TEXT__REC = "rec";
    /**新核心产生的付款文件上传目录*/
    public static final String BANK_TEXT__PAY = "pay";
    /**资金系统收款返盘文件存放目录（供新核心系统下载）*/
    public static final String bank_text__send = "send";  
    /**资金系统付款返盘文件存放目录（供新核心系统下载 */
    public static final String bank_text__return = "return";

    public static final String SAP_CONTROL__STATUS_INIT = "0";
    public static final String SAP_CONTROL__STATUS_READY = "1";
    /** 渠道类型为其他 code值99 */
    public static final String CHANNEL_TYPE_OTHER = "99";
    /**
     * @Fields keystr : 密钥 SUVyaGVgT2hccV9TaFtWUGZjcnVhQ0RV
     *         需要把密钥base64解码之后，得到24字节的3DES密钥 :IErhe`Oh\\q_Sh[VPfcruaCDU
     */
    public static final String KEYSTR1 = "SUVyaGVgT2hccV9TaFtWUGZjcnVhQ0RV";//
    /**
     * @Fields ivstr : 向量 YVJ3S3ZpdnQ= 把向量base64解码之后，得到8字节的3DES向量:aRwKvivt
     */
    public static final String IVSTR1 = "YVJ3S3ZpdnQ";//

    public static final String SECURITY_CODE = "7YUUeo30dvmn@v9meeBNME";

    /**
     * @Fields FMS_REALTIME_UPDATE : 单笔实时收付 提交
     */
    public static final String FMS_REALTIME_UPDATE = "********";
    /**
     * @Fields FMS_REALTIME_QUERY : 单笔实时收付 查询
     */
    public static final String FMS_REALTIME_QUERY = "********";

    /**
     * @Fields FMS_REALTIME_NOTICE : 单笔实时收付 通知
     */
    public static final String FMS_REALTIME_NOTICE = "********";
    /**
     * @Fields IS_BOOKKEEPING_YES : 是否记账1是
     */
    public static final String IS_BOOKKEEPING_YES = "1";
    /**
     * @Fields IS_BOOKKEEPING_NO : 是否记账0否
     */
    public static final String IS_BOOKKEEPING_NO = "0";
    
    
    /** --------------以上为截止目前编码中实际用到，且与码表一致的code常量----------------- */
    public static final String BANK_TEXT_PROGRESS = "bank_text_progress";
    
    /** 短信notice_code 10：银行转账制返盘交易成功 **/
    public static final String SMS_NOTICE_CODE_10 = "10";
    /** 短信notice_code 11：银行转账制返盘交易失败 **/
    public static final String SMS_NOTICE_CODE_11 = "11";
    /** 860000 默认收付费机构 **/
    public static final String DEFAULT_CAP_ORGAN = "860000";
    
    /** 8600001：信用卡银行代码 **/
    public static final String BANK_CODE_CREDIT_CARD = "8600001";
    /** 9：银行代码尾号 **/
    public static final String BANK_CODE_END = "9";
    
    /** 收付费查询页面的收付费方式代码表名 **/
    public static final String CAPDETAIL_PAYMODE_DEFAULT = "APP___CAP__DBUSER.T_PAY_MODE";
    /** 收费方式代码表名 **/
    public static final String CAPDETAIL_PAYMODE_AR = "APP___CAP__DBUSER.T_PAY_MODE_CAPDETAIL_AR";
    /** 付费方式代码表名 **/
    public static final String CAPDETAIL_PAYMODE_AP = "APP___CAP__DBUSER.T_PAY_MODE_CAPDETAIL_AP";
    /** 8600799：裕福卡代码，只允许进行保全付费制盘**/
    public static final String BANK_CODE_YUFU = "8600799";
    /** 8600010：工商银行银行代码 **/
    public static final String BANK_CODE_GS = "8600010";
    /** 8600020：建设银行银行代码 **/
    public static final String BANK_CODE_JS = "8600020";
    /** 8600110：农村信用社银行代码 **/
    public static final String BANK_CODE_NXS = "8600110";
    /** 8600160：华夏银行银行代码 **/
    public static final String BANK_CODE_HX = "8600160";
    /** 8600480：杭州银行银行代码 **/
    public static final String BANK_CODE_HZ = "8600480";
    /** 510047：江苏分公司银行代码 **/
    public static final String ORGAN_BANK_CODE_JS = "5100470";
    
    /** 支票余额处理类型：支票余额退回原单位 */
    public static final String CB_OPERATE_TYPE_TH = "0";
    /** 支票余额处理类型：保留支票余额 */
    public static final String CB_OPERATE_TYPE_CC = "1";
    /** 支票余额处理类型：支票余额进入客户账号 */
    public static final String CB_OPERATE_TYPE_CA = "2";
    /** 预制盘批次号*/
    public static final String BANK_TEXT_DiscNo = "000000";
    
    /**---------------------------------智能POS收费通知常量---------------------------------**/
    /**
     * @Fields POS_TZ__FINISH : 资金系统收费结果-成功
     */
    public static final String POS_CHARGE__FINISH = "1";
    /**
     * @Fields POS_CHARGE__FAIL : 资金系统收费结果-失败
     */
    public static final String POS_CHARGE__FAIL = "0";
    
    /**---------------------------------智能POS应收查询常量---------------------------------**/
    /**
     * @Fields POS_QUERY_SQJF : 首期交费
     */
    public static final String POS_QUERY_SQJF = "01";
    /**
     * @Fields POS_QUERY_ESF : E行销收费
     */
    public static final String POS_QUERY_ESF = "02";
    /**
     * @Fields POS_QUERY_GMSF : 柜面收费
     */
    public static final String POS_QUERY_GMSF = "03";
    /**
     * @Fields POS_QUERY_XQJF : 续期交费
     */
    public static final String POS_QUERY_XQJF = "04";
    /**
     * @Fields POS_QUERY_SQJF_A : 首期加费
     */
    public static final String POS_QUERY_SQJF_A = "05";
    /**
     * @Fields POS_QUERY_BQJF : 保全交费
     */
    public static final String POS_QUERY_BQJF = "06";
    /**
     * @Fields POS_BUSI_TYPE_YDXS : 移动销售
     */
    public static final String POS_BUSI_TYPE_YDXS = "1";
    /**
     * @Fields POS_BUSI_TYPE_ZJSF : 直接收费
     */
    public static final String POS_BUSI_TYPE_ZJSF = "2";
    
    //实时对账相关配置
    /**
     * 实时对账收费状态为：成功
     */
    public static final String REALTIME_RETURN_CODE = "1";
    /**
     * 实时对账收付方式：实时收费，POS收费
     */
    public static final String REALTIME_CHARGETYPE = "40,41";
    
     /**
     * 读取对账数据的ftp服务器的对账目录地址的配置代码
     */
    public static final String RECONCILIATION_READ = "30";
    /**
     * 备份对账数据的ftp服务器的对账目录地址的配置代码
     */
    public static final String RECONCILIATION_BACKUPS = "31";
    
    /**
     * 生成对账表格的FTP服务的目录地址的配置
     */
    public static final String RECONCILIATION_EXCEL = "32";
    /**
     * ---------------------------------------微信支付对账FTP配置-----------------------------------------**/
    /**
     * 读取对账数据的ftp服务器地址IP
     */
    public static final String  IP ="IP";
    /**
     * 读取对账数据的ftp服务器地址端口
     */
    public static final String PORT="PORT";
    /**
     * 读取对账数据的ftp服务器用户
     */
    public static final String USESR="USESR";
    /**
     * 读取对账数据的ftp服务器密码
     */
    public static final String PASSWORD="PASSWORD";
    /**
     * 新微店网销对账文件读取下载文件地址
     */
    public static final String TXT_FILE_P="TXT_FILE_P";
    /**
     * 新微店网销对账表格生成存放目录
     */
    public static final String EXCEL_FILE_P="EXCEL_FILE_P";
    
    /** 
    * @Fields EXCEL_FOLDER : 深圳医保对账表格存放目录
    */ 
    public static final String SZYB_EXCEL_FOLDER = "excelFolder";
    /**
     * 新微店网销对账文件备份文件地址
     */
    public static final String FILE_P_BAK="FILE_P_BAK";
    /**---------------------------------------分公司制返盘格式定义常量---------------------------------------**/
    /**
     * 银行转账制返盘上传/下载文件时每次读取/写入流中的字节数
     */
    public static final int READ_WRITE_SIZE = 1024;
    
    /**
     * 分公司制返盘格式定义，默认列宽
     */
    public static final BigDecimal LENGTH = new BigDecimal(20);
    /**
     * 分公司制返盘格式定义，列名下标
     */
    public static final int NAME_INDEX = 0;
    /**
     * 分公司制返盘格式定义，列名显示名下标
     */
    public static final int DISPLAY_NAME_INDEX = 1;
    /**
     * 分公司制返盘格式定义，列序号
     */
    public static final BigDecimal ORDER_INDEX = new BigDecimal(1);
    /**
     * 分公司制返盘格式定义，列样式（yyyyMMdd）
     */
    public static final String FORMAT_STYLE_NONE = "01";
    /**
     * 分公司制返盘格式定义，列样式（yyyy.MM.dd）
     */
    public static final String FORMAT_STYLE_POINT = "02";
    /**
     * 分公司制返盘格式定义，列样式（yyyy-MM-dd）
     */
    public static final String FORMAT_STYLE_LINE = "03";
    /**
     * 分公司制返盘格式定义，回盘截取开始字符位置
     */
    public static final BigDecimal SPLIT_CHAR_INDEX = new BigDecimal(1);
    /**
     * 分公司制返盘格式定义，回盘截取开始字符位数
     */
    public static final BigDecimal SPLIT_CHAR_INDEX_LENTH = new BigDecimal(1);
    /**---------------------------------------分公司制返盘格式定义常量---------------------------------------**/
    
    /**
     * 单证ID首位编码
     */
    public static final String DOC_HEAD = "CAP002_";
    
    /**
     * 数值0
     */
    public static final int NUMBER_ZERO = 0;
    /**
     * 数值1
     */
    public static final int NUMBER_ONE = 1;
    /**
     * 数值2
     */
    public static final int NUMBER_TWO = 2;
    /**
     * 数值3
     */
    public static final int NUMBER_THREE = 3;
    /**
     * 数值4
     */
    public static final int NUMBER_FOUR = 4;
    /**
     * 数值5
     */
    public static final int NUMBER_FIVE = 5;
    /**
     * 数值6
     */
    public static final int NUMBER_SIX = 6;
    /**
     * 数值7
     */
    public static final int NUMBER_SEVEN = 7;
    /**
     * 数值8
     */
    public static final int NUMBER_EIGHT = 8;
    /**
     * 数值9
     */
    public static final int NUMBER_NINE = 9;
    /**
     * 数值10
     */
    public static final int NUMBER_TEN = 10;
    /**
     * 数值11
     */
    public static final int NUMBER_ELEVEN = 11;
    /**
     * 数值12
     */
    public static final int NUMBER_TWELVE = 12;
    /**
     * 数值13
     */
    public static final int NUMBER_THIRTEEN = 13;
    /**
     * 数值14
     */
    public static final int NUMBER_FOURTEEN = 14;
    /**
     * 数值15
     */
    public static final int NUMBER_FIFTEEN = 15;
    /**
     * 数值16
     */
    public static final int NUMBER_SIXTEEN = 16;
    /**
     * 数值17
     */
    public static final int NUMBER_SEVENTEEN = 17;
    /**
     * 数值18
     */
    public static final int NUMBER_EIGHTEEN = 18;
    /**
     * 数值19
     */
    public static final int NUMBER_NINETEEN = 19;
    /**
     * 数值20
     */
    public static final int NUMBER_TWENTY = 20;
    /**
     * 数值21
     */
    public static final int NUMBER_TWENTY_ONE = 21;
    /**
     * 数值22
     */
    public static final int NUMBER_TWENTY_TWO = 22;
    /**
     * 数值23
     */
    public static final int NUMBER_TWENTY_THREE = 23;
    /**
     * 数值24
     */
    public static final int NUMBER_TWENTY_FOUR = 24;
    /**
     * 数值25
     */
    public static final int NUMBER_TWENTY_FIVE = 25;
    /**
     * 数值26
     */
    public static final int NUMBER_TWENTY_SIX = 26;
    /**
     * 数值27
     */
    public static final int NUMBER_TWENTY_SEVEN = 27;
    /**
     * 数值28
     */
    public static final int NUMBER_TWENTY_EIGHT = 28;
    /**
     * 数值29
     */
    public static final int NUMBER_TWENTY_NINE = 29;
    /**
     * 数值30
     */
    public static final int NUMBER_THIRTH = 30;
    /**
     * 数值31
     */
    public static final int NUMBER_THIRTH_ONE = 31;
    /**
     * 数值32
     */
    public static final int NUMBER_THIRTH_TWO = 32;
    /**
     * 数值33
     */
    public static final int NUMBER_THIRTH_THREE = 33;
    /**
     * 数值34
     */
    public static final int NUMBER_THIRTH_FOUR = 34;
    /**
     * 数值35
     */
    public static final int NUMBER_THIRTH_FIVE = 35;
    /**
     * 数值36
     */
    public static final int NUMBER_THIRTH_SIX = 36;
    /**
     * 数值37
     */
    public static final int NUMBER_THIRTH_SEVEN = 37;
    /**
     * 数值38
     */
    public static final int NUMBER_THIRTH_EIGHT = 38;
    /**
     * 数值39
     */
    public static final int NUMBER_THIRTH_NINE = 39;
    /**
     * 数值40
     */
    public static final int NUMBER_FORTY = 40;
    /**
     * 数值41
     */
    public static final int NUMBER_FORTY_ONE = 41;
    /**
     * 数值42
     */
    public static final int NUMBER_FORTY_TWO = 42;
    /**
     * 数值43
     */
    public static final int NUMBER_FORTY_THREE = 43;
    /**
     * 数值44
     */
    public static final int NUMBER_FORTY_FOUR = 44;
    /**
     * 数值45
     */
    public static final int NUMBER_FORTY_FIVE = 45;
    /**
     * 数值46
     */
    public static final int NUMBER_FORTY_SIX = 46;
    /**
     * 数值47
     */
    public static final int NUMBER_FORTY_SEVEN = 47;
    /**
     * 数值48
     */
    public static final int NUMBER_FORTY_EIGHT = 48;
    /**
     * 数值49
     */
    public static final int NUMBER_FORTY_NINE = 49;
    /**
     * 数值50
     */
    public static final int NUMBER_FIFTY = 50;
    /**
     * 数值59
     */
    public static final int NUMBER_FIVETY_NINE= 59;
    /**
     * 数值60
     */
    public static final int NUMBER_SIXTY = 60;
    /**
     * 数值99
     */
    public static final int NUMBER_NINETY_NINE = 99;
    /**
     * 数值100
     */
    public static final int NUMBER_ONE_HUNDRED = 100;
    /**
     * 数值200
     */
    public static final int NUMBER_TWO_HUNDRED = 200;
    /**
     * 数值255
     */
    public static final int NUMBER_TWO_HUNDRED_FIVETY_FIVE = 255;
    /**
     * 数值256
     */
    public static final int NUMBER_TWO_HUNDRED_FIVETY_SIX = 256;
    /**
     * 数值290
     */
    public static final int NUMBER_TWO_HUNDRED_NINETY = 290;
    /**
     * 数值365
     */
    public static final int NUMBER_THREE_HUNDRED_SIXTY_FIVE = 365;
    /**
     * 数值480
     */
    public static final int NUMBER_FOUR_HUNDRED_EIGHTY = 480;
    /**
     * 数值500
     */
    public static final int NUMBER_FIVE_HUNDRED = 500;
    /**
     * 数值700
     */
    public static final int NUMBER_SEVEN_HUNDRED = 700;
    /**
     * 数值1024
     */
    public static final int NUMBER_ONE_THOUSAND_AND_TWENTY_FOUR = 1024;
    /**
     * 数值3600
     */
    public static final int NUMBER_THREE_THOUSAND_AND_SIX_HUNDRED = 3600;
    /**
     * 数值4096
     */
    public static final int NUMBER_FOUR_THOUSAND_AND_NINETY_SIX = 4096;
    /**
     * 数值10000
     */
    public static final int NUMBER_TEN_THOUSAND = 10000;
    /**
     * 数值20000
     */
    public static final int NUMBER_TWENTY_THOUSAND = 20000;
    /**
     * 数值30000
     */
    public static final int NUMBER_THIRTY_THOUSAND = 30000;
    /**
     * 数值100000
     */
    public static final int NUMBER_ONE_HUNDRED_THOUSAND = 100000;
    /**
     * 数值11111111
     */
    public static final int NUMBER_TEN_11111111 = 11111111;
    /**
     * 数值60000
     */
    public static final int NUMBER_SIXTY_THOUSAND = 60000;
    /**
     * 数值21292
     */
    public static final int NUMBER_TWENTY_ONE_THOUSAND_TWO_HUNDRED_NINETY_TWO = 21292;
    /**
     * 数值1000
     */
    public static final int NUMBER_THOUSAND = 1000;
    /**
     * 数值3000
     */
    public static final int NUMBER_THREE_THOUSAND = 3000;
    /**
     * 十六进制数值0xEF
     */
    public static final int HEXADECIMAL_0XEF = 0xEF;
    /**
     * 十六进制数值0xFF
     */
    public static final int HEXADECIMAL_0XFF = 0xFF;
    
    /**
     * 十六进制数值0xBB
     */
    public static final int HEXADECIMAL_0XBB = 0xBB;
    
    /**
     * 十六进制数值0xBF
     */
    public static final int HEXADECIMAL_0XBF = 0xBF;
    
    /**
     * 十六进制数值0xFE
     */
    public static final int HEXADECIMAL_0XFE = 0xFE;
    
    /**
     * 十六进制数值0x80
     */
    public static final int HEXADECIMAL_0X80 = 0x80;
    
    /**
     * 十六进制数值0xF0
     */
    public static final int HEXADECIMAL_0XF0 = 0xF0;
    
    /**
     * 十六进制数值0xC0
     */
    public static final int HEXADECIMAL_0XC0 = 0xC0;
    
    /**
     * 十六进制数值0xD0
     */
    public static final int HEXADECIMAL_0XE0 = 0xE0;
    
    /**
     * 十六进制数值0xDf
     */
    public static final int HEXADECIMAL_0XDF = 0xDF;
    
    /**
     * 十六进制数值0xefbb
     */
    public static final int HEXADECIMAL_0XEFBB = 0xefbb;
    
    /**
     * 十六进制数值0xfffe
     */
    public static final int HEXADECIMAL_0XFFFE = 0xfffe;
    
    /**
     * 十六进制数值0xfeff
     */
    public static final int HEXADECIMAL_0XFEFF = 0xfeff;
    
    /**
     * 字符编码ISO8859-1
     */
    public static final String ENCODE_ISO8859_1 = "ISO8859-1";
    /**
     * 字符编码UTF-8
     */
    public static final String ENCODE_UTF8 = "UTF-8";
    /**
     * 字符编码GB2312
     */
    public static final String ENCODE_GB2312 = "GB2312";
    /**
     * 字符编码GBK
     */
    public static final String ENCODE_GBK = "GBK";
    
    /**
     * 盘文件明细表临时表APP___CAP__DBUSER.T_BANK_TEXT_DETAIL_TEMP缩写
     */
    public static final String BANK_TEXT_DETAIL_TEMP = "T_BTD";//APP___CAP__DBUSER
    /**
     * 盘文件表临时表APP___CAP__DBUSER.T_BANK_TEXT_TEMP缩写
     */
    public static final String BANK_TEXT_TEMP = "T_BT"; //APP___CAP__DBUSER
    /**
     * 首期交费--预收
     */
    public static final String BUSINESS_TYPE_YS = "1004";
    /**
     * 首期加费--加补费
     */
    public static final String BUSINESS_TYPE_JBF = "1006";
    
    /**---------------------------------------月结清单批处理---------------------------------------**/
    /** 
    * @Fields PAYREF_CONFIG_TYPE_YJQD : 配置表月结清单类型
    */ 
    public static final String PAYREF_CONFIG_TYPE_YJQD = "MONTHLY_STAT";
    /** 
    * @Fields PAYREF_CONFIG_CODE_YJQD_PATH :  配置表月结清单路径
    */ 
    public static final String PAYREF_CONFIG_CODE_YJQD_PATH = "PATH";
    /** 
    * @Fields SFTP_YJQD_SERVER_IP :  配置表月结清单SFTP服务器IP地址
    */ 
    public static final String SFTP_YJQD_SERVER_IP = "01";
    /** 
    * @Fields SFTP_YJQD_SERVER_PORT :  配置表月结清单SFTP服务器端口号
    */ 
    public static final String SFTP_YJQD_SERVER_PORT = "02";
    /** 
    * @Fields SFTP_YJQD_USER_NAME :  配置表月结清单SFTP服务器登录用户名
    */ 
    public static final String SFTP_YJQD_USER_NAME = "03";
    /** 
    * @Fields SFTP_YJQD_PASSWORD :  配置表月结清单SFTP服务器登录密码
    */ 
    public static final String SFTP_YJQD_PASSWORD = "04";
    /** 
    * @Fields SFTP_YJQD_PATH :  配置表月结清单SFTP服务器存放路径
    */ 
    public static final String SFTP_YJQD_PATH = "05";
    /**---------------------------------------月结清单批处理---------------------------------------**/
    
    public static final String ZFPRANCH_REC_CODE = "1";//分公司收费目录
    /**
     * 收费备份目录
     */
    public static final String ZFPRANCH_REC_BAK_CODE="3";//收费备份目录
    /**
     * 分公司付费目录
     */
    public static final String ZFPRANCH_PAY_CODE = "2";//分公司付费目录
    /**
     * 付费备份目录
     */
    public static final String ZFPRANCH_PAY_BAK_CODE = "4";//付费备份目录
    /**
     * 数值
     */
    public static final int excelMax=65536;
    /**
     * 数值
     */
    public static final int ZIP_NUMBER=8192;
    /**
     * 数值
     */
    public static final int NUMBER_140=140;
    /**
     * 数值
     */
    public static final double NUMBER_ZEROONE=0.1;
    /**
     * 数值
     */
    public static final double NUMBER_ZEROTHREE=0.3;
    /**
     * 数值
     */
    public static final double NUMBER_ZEROFIVE=0.5;
  
    /** 
    * @Fields FROZEN_SOURCE_HX : 挂起/解挂源核心
    */ 
    public static final String FROZEN_SOURCE_HX = "00";
    
    /** 
    * @Fields FROZEN_SITUATION_CAP : 掌上新华续期交费回写并实时核销解挂(00601)
    */ 
    public static final String FROZEN_SITUATION_CAP = "00601";
    /**
     * @Fields BUSI_PROD_CODE__00557000 : 557上海医保账户重大疾病保险
     */
	public static final String BUSI_PROD_CODE__00557000 = new String("00557000");
	/**
	 * @Fields BUSI_PROD_CODE__00558000 : 558上海医保账户住院自费医疗保险
	 */
	public static final String BUSI_PROD_CODE__00558000 = new String("00558000");
    /**
     * @Fields BUSI_PROD_CODE__******** : 上海医保账户医疗保险
     */
	public static final String BUSI_PROD_CODE__******** = new String("********");
	/**
     * @Fields BUSI_PROD_CODE__******** : 上海医保账户意外伤害医疗保险
     */
	public static final String BUSI_PROD_CODE__******** = new String("********");
	
	/**
     * @Fields SHYB : 上海医保相关产品
     */
	public static final ArrayList<String> SHYB = new ArrayList<String>(
            Arrays.asList("00557000", "00558000", "********", "********", "********"));
	
	/** 
	* @Fields SZYB_TOKEN : 深圳医保获取令牌地址
	*/ 
	public static final String SZYB_TOKEN="szybtoken";
	
	/** 
	* @Fields SZYB_YB119 : 深圳医保日终对账地址配置
	*/ 
	public static final String SZYB_YB119="szyb119";
	
	/** 
	* @Fields SZYB_YB120 : 深圳医保对账明细清单地址配置
	*/ 
	public static final String SZYB_YB120="szyb120";
	
	/** 
	* @Fields PAY_MODE_BANKCODES : 需要存储银行代码、银行账号信息支付方式
	*/ 
	public static final  String PAY_MODE_BANKCODES ="31,32,34,40,41,43,60,72,71,45";
	/**   
	* @Fields PAY_MODE_CHEQUES : 支票类支付方式
	*/ 
	public static final String PAY_MODE_CHEQUES = "20,21,22";
	
	/** 
	* @Fields TRANS_STATE_SUSPEND : 5:资金非实时交易状态-暂停支付
	*/ 
	public static final String TRANS_STATE_SUSPEND = "5";
	
	/** 
	* @Fields TRANS_STATE_SOLUTION : 资金非实时交易状态-取消暂停支付
	*/ 
	public static final String TRANS_STATE_SOLUTION = "7";
	/** 
	    * @Fields TRANS_STATE_SOLUTION : 资金非实时交易状态-支付中
	    */ 
	public static final String TRANS_STATE_TRANSFERING = "4";
	
	/** 
	    * @Fields WECHAT_PAYSUBMIT : 资金微信支付退款提交地址
	    */ 
	public static final String WECHAT_PAYSUBMIT="paysubmit";
	/** 
     * @Fields WECHAT_PAYSUBMIT : 资金微信支付加密密钥
     */ 
	public static final String WECHAT_ENCRYPTION="encryption";
	
	/** 
     * @Fields WECHAT_PAYSUBMIT : 微信支付结果通知回调地址
     */ 
    public static final String NOTIFY_URL="notifyurl";
	
    public static final String SUBMIT_CHANNEL_WX = "11";
    
    /** 
     * @Fields SUBMIT_CHANNEL_ZFB : 蚂蚁保
     */ 
    public static final String SUBMIT_CHANNEL_ZFB = "24"; 
    
    /** 
     * @Fields SUBMIT_CHANNEL_ZFB : 惠泽平台
     */ 
    public static final String SUBMIT_CHANNEL_HZ = "26";
    
    /** 
     * @Fields SUBMIT_CHANNEL_WTS : 梧桐树平台
     */ 
    public static final String SUBMIT_CHANNEL_WTS = "27";
    
    /** 
     * @Fields SUBMIT_CHANNEL_BT : 保通保险代理自营平台
     */ 
    public static final String SUBMIT_CHANNEL_BT = "28";
    
    /** 
     * @Fields SUBMIT_CHANNEL_CX : 创信中介业务平台
     */ 
    public static final String SUBMIT_CHANNEL_CX = "29";
    
    /** 
     * @Fields SUBMIT_CHANNEL_WY : 微易平台
     */ 
    public static final String SUBMIT_CHANNEL_WY = "30";
    
    /**---------------------------------------分公司集中制盘（农商行）---------------------------------------**/
    /**
     * 北京农商行
     */
    public static final String BANK_RCB_69 = "69";
    /**
     * 北京农村商业银行
     */
    public static final String BANK_RCB_7008 = "7008";
    /**
     * 农信社北京丰台联合社(制返盘银行)
     */
    public static final String BANK_RCB_2100110 = "2100110";
    /**
     * 集中制盘分公司标识
     */
    public static final String BRANCH_FLAG_YES = "1";
    /**
     * 交费年期类型:年交
     */
    public static final String CHARGE_PERIOD_YEAR = "2";
    /**
     * 交费年期类型:月交
     */
    public static final String CHARGE_PERIOD_MONTH = "6";
    /**
     * 交费年期类型:趸交
     */
    public static final String CHARGE_PERIOD_SINGLE = "1";
    /**
     * 交费年期类型:交至某确定年龄
     */
    public static final String CHARGE_PERIOD_TO_AGE = "3";
    /**
     * 交费年期类型:其他
     */
    public static final int CHARGE_PERIOD_OTHER = 99;
    /**
     * 缴费频率:年交
     */
    public static final String PREM_FREQ_YEAR = "5";
    /**
     * 缴费频率:月交
     */
    public static final String PREM_FREQ_MONTH = "2";
    /**
     * 首次/非首次记录标示  1-首次(写入分公司制盘文件)
     */
    public static final String FIRST_YES = "1";
    /**
     * 首次/非首次记录标示  2-非首次(写入分公司制盘文件)
     */
    public static final String FIRST_NO = "2";
    /**
     * 非农商行银保通(写入分公司制盘文件)
     */
    public static final String RCB_YBT_NO = "2";
    /**
     * 交费年期类型:趸交 时，默认传值：90(写入分公司制盘文件)
     */
    public static final int CHARGE_PERIOD_SINGLE_90 = 90;
    /**
     * 不为北京农商行银保通保单，且业务来源为“新契约”(写入分公司制盘文件)
     */
    public static final String DERIV_TYPE_NBS_YBT_NO = "99";
    /**
     * 业务类型其他(写入分公司制盘文件)
     */
    public static final String BUSINESS_TYPE_OTHER = "8";
    
    /**---------------------------------------公司集中制盘（农商行）---------------------------------------**/
    
    
    /** 新老日结下载时间节点配置代码 */
    public static final String SPLIT_TIME = "1";
	
    public static final String CASH_STATUS_00 = "00";//现金待复核
    public static final String CASH_STATUS_01 = "01";//现金待推送
    public static final String CASH_STATUS_02 = "02";//现金已推送
    
    public static final String REL_TYPE_01 = "01";//关联表关联类型-01重复支付
    public static final String REL_TYPE_02 = "02";//关联表关联类型-02内部转账-保全
    
    /**
     * 解挂/挂起操作人(续期交费回写并实时核销接口)
     */
    public static final int FROZEN_STATUS_BY_70010 = 70010;
    
    /** 
    * @Fields REPORT_URL : 日结传送ftp存放路径
    */ 
    public static final String REPORT_URL = "34";
    
    /** 
    * @Fields REPORT_NAME : 日结文件夹名称
    */ 
    public static final String REPORT_NAME = "report";
    /** 
     * @Fields FILING_FTP_IP : 日结归档-个险日结同步接口-ftp信息
     */ 
    public static final String FILING_FTP_IP = "1";
    public static final String FILING_FTP_PORT = "2";
    public static final String FILING_FTP_USER = "3";
    public static final String FILING_FTP_PASSWORD = "4";
    public static final String FILING_FTP_TARGETPATH = "5";
    public static final String FILING_HTTP_IP = "6";
    public static final String FILING_HTTP_TOKEN_PATH = "7";
    public static final String FILING_HTTP_INFO_PATH = "8";
    public static final String FILING_HTTP_GETRESULT_PATH = "9";
    public static final String FILING_HTTP_TOKEN_USER = "10";
    public static final String FILING_HTTP_TOKEN_PWD = "11";
    public static final String FILING_SOURCESYSTEM = "GXHX";//系统来源
    public static final String FILING_BUSINESSTYPE = "IND_01";//文件信息推送接口 业务类型
    
    //泰隆银行编码bankcode
    public static final String ZJTLCB_BANKCODE = "2400008";
    /**
     * 资金实时交易状态-收费成功
     */
    public static final String STATUS_AR__FINISH = "1";
    /**
     * 资金实时交易状态-付费成功
     */
    public static final String STATUS_AP__FINISH = "2";
    /**
     * 资金实时交易状态-支付中
     */
    public static final String STATUS__WAY = "4";
    
    /**---------------------------------------交易流水编号(批处理)开始---------------------------------------**/
    public static final String SFTP_SERVER_IP_NC = "01";//IP地址
    public static final String SFTP_SERVER_PORT_NC = "02";//端口号
    public static final String SFTP_USER_NAME_NC = "03";//用户名
    public static final String SFTP_PASSWORD_NC = "04";//密码
    public static final String SFTP_SEND_NC = "05";//推送-交易流水编号文件上传路径
    public static final String SFTP_RETURN_NC = "06";//返回-交易流水编号文件资金返回存放路径
    public static final String SFTP_RETURN_BAK_NC = "07";//返回备份-交易流水编号文件资金返回备份存放路径
    public static final String SFTP_RETURN_ERROR_NC = "08";//处理数据错误-交易流水编号文件资金返回数据处理错误存放路径
    public static final String LOCAL_CATALOG_NC = "09";//本地路径
    public static final String AES_KEY_NC = "10";//AES加密密钥
    public static final String OLD_NEW_DATE_NC = "11";//交易流水编号推送批处理新老数据时间分割点
    public static final String HIS_START_DATE_NC = "12";//交易流水编号推送批处理历史数据起始日期
    public static final String CASH_END_DATE_NC = "13";//现金-交易流水编号推送批处理历史数据终止日期
    public static final String NET_SALES_END_DATE_NC = "14";//网络销售-交易流水编号推送批处理历史数据终止日期
    /**---------------------------------------交易流水编号(批处理)结束---------------------------------------**/
    
    /** 
     * @Fields SUBMIT_CHANNEL_XSD : 新时代平台  码值5或OSP-*
     */ 
    public static final String SUBMIT_CHANNEL_XSD = "5";
    /** 
     * @Fields SUBMIT_CHANNEL_XSD1 : 新时代平台  码值5或OSP-*
     */ 
    public static final String SUBMIT_CHANNEL_XSD1 = "OSP";
    /** 
     * @Fields FROZEN_SITUATION__XSD_SUSPEND : 新时代投保发起重复支付挂起
     */ 
    public static final String FROZEN_SITUATION__XSD_SUSPEND = "00602";
    /** 
     * @Fields FROZEN_SITUATION__XSD_SUSPEND : 新时代投保发起重复支付支付成功解挂
     */ 
    public static final String FROZEN_SITUATION__XSD_SOLUTION_SUCC = "00603";
    /** 
     * @Fields FROZEN_SITUATION__XSD_SUSPEND : 新时代投保发起重复支付支付失败解挂
     */ 
    public static final String FROZEN_SITUATION__XSD_SOLUTION_FAIL = "00604";
    /** 
     * @Fields FROZEN_SOURCE__XZSXH : 挂起/解挂源 掌上新华
     */ 
    public static final String FROZEN_SOURCE__XZSXH = "24";
    /** 
     * @Fields FROZEN_SITUATION__ZSXH_SOLUTION_FAIL : 掌上新华续期收费失败解挂接口
     */ 
    public static final String FROZEN_SITUATION__ZSXH_SOLUTION_FAIL = "00302";
}
