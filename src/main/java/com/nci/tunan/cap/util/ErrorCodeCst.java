/**
 * @Title: ErrorCodeCst.java
 * @Package com.nci.tunan.cap.util
 * @Description: 公共模块错误返回代码
 * Copyright: Copyright (c) 2015
 * Company: 高伟达软件股份有限公司
 * 
 * <AUTHOR>
 * @date 2015-6-9 下午4:34:09
 * @version 0.0.1
 */

package com.nci.tunan.cap.util;

/**
 * 类说明: 公共模块错误返回代码
 * 
 * <AUTHOR>
 * @version 0.0.1 2015-6-9
 * @see <EMAIL> 2015-6-9 创建
 * @since cap-impl 0.0.1
 * @date 2015-6-9
 * @.belongToModule 收付费-公共模块错误返回代码
 */

public class ErrorCodeCst {
    /**
     * 保存失败
     */
    public static final String ERR_SAVE_FAILED = "00";
    /**
     * 成功
     */
    public static final String SUCCESS = "01";

    /**
     * 参数为空
     */
    public static final String ERR_PRAM_NULL = "02";

    /**
     * 应收应付金额为零
     */
    public static final String ERR_ARAP_NULL = "03";

    /**
     * 码值不存在
     */
    public static final String ERR_CODE_NOT_EXIST = "04";

    /**
     * 记录已存在
     */
    public static final String ERR_RECODE_IS_EXIST = "05";

    /**
     * 取消记录不存在
     */
    public static final String ERR_CANCEL_RECODE_NOT_EXIST = "06";

    /**
     * 记录不存在
     */
    public static final String ERR_RECODE_NOT_EXIST = "07";

    /**
     * 应收付总金额与实收付总金额不一致，保存终止！
     */
    public static final String ERR_ARAP_NOT_MATCH_CASH = "08";

    /**
     * 应收应付金额为零
     */
    public static final String ERR_CASH_NULL = "09";

    /**
     * 挂起、解挂、方式变更操作类型 异常
     */
    public static final String ERR_OPERATE_TYPE = "10";

    /**
     * 制盘中业务不允许回退
     */
    public static final String ERR_BankTextStatus_TRANSFERING = "11";

    /**
     * 票据未确认状态不允许回退
     */
    public static final String ERR_FeeStatus_UNCONFIRMED = "12";

    /**
     * 状态待处理
     */
    public static final String ERR_FEE_STATUS_READY = "03";

    /**
     * 发票已打印不允许冲正
     */
    public static final String ERR_RECEIPT_STATUS_PRINTED = "14";
    
    /**
     * 文件校验异常
     */
    public static final String ERR_CHECK_FILE_STATUS_ERROR = "15";
    /**
     * 文件校验失败
     */
    public static final String ERR_CHECK_FILE_STATUS_FAILED = "16";
    
    /**
     * 挂起/解挂重复操作
     */
    public static final String ERR_CHECK_FILE_STATUS_REPEAT = "17";
}
