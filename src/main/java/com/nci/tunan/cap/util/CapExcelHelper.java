package com.nci.tunan.cap.util;

import jxl.CellView;
import jxl.SheetSettings;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.PageOrientation;
import jxl.format.PaperSize;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.NumberFormat;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

/**
 * @description excel操作帮助类
 * <AUTHOR>
 * @date 2014年10月16日 上午9:27:04
 * @.belongToModule 收付费-报表
 */
public class CapExcelHelper {
    /**
     * @Fields RPTNAME : 格式化类型
     */
    public static final String RPTNAME = "RptName";

    /**
     * @Fields RPTSUBNAME : 格式化类型
     */
    public static final String RPTSUBNAME = "RptSubName";

    /**
     * @Fields NOTE : 格式化类型
     */
    public static final String NOTE = "Note";

    /**
     * @Fields wk : WritableSheet对象
     */ 
    private WritableSheet wk;

    /**
     * @Fields fmt : WritableCellFormat对象
     */ 
    private WritableCellFormat fmt;

    // format list
    /**
     * @Fields fmtRptName : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtRptName;

    /**
     * @Fields fmtSubRptName : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtSubRptName;

    /**
     * @Fields fmtNote : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtNote;

    // thin & white & left & noframe & string
    /**
     * @Fields fmtTWL : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWL;

    

    // bold & gray & center & frame & string
    /**
     * @Fields fmtBGC1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtBGC1;

    // bold & white & center & no/yes frame & string
    /**
     * @Fields fmtBWC : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtBWC;

    /**
     * @Fields fmtBWC1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtBWC1;

    // cells - string
    /**
     * @Fields fmtTWL1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWL1;

    /**
     * @Fields fmtTWR1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWR1;

    /**
     * @Fields fmtTWC1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWC1;

    /**
     * @Fields fmtTWR : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWR;

    /**
     * @Fields fmtTWC : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWC;

    // cells - integer
    /**
     * @Fields fmtTWLi1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWLi1;

    /**
     * @Fields fmtTWRi1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWRi1;

    /**
     * @Fields fmtTWCi1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWCi1;

    /**
     * @Fields fmtTWLi : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWLi;

    /**
     * @Fields fmtTWRi : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWRi;

    /**
     * @Fields fmtTWCi : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWCi;

    //cells double
    /**
     * @Fields fmtTWLd1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWLd1;

    /**
     * @Fields fmtTWRd1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWRd1;

    /**
     * @Fields fmtTWCd1 : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWCd1;

    /**
     * @Fields fmtTWLd : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWLd;

    /**
     * @Fields fmtTWRd : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWRd;

    /**
     * @Fields fmtTWCd : WritableCellFormat对象
     */ 
    private WritableCellFormat fmtTWCd;

    /**
     * <p>Title: 构造函数</p> 
     * <p>Description: 构造函数</p> 
     * @param wb WritableWorkbook 书写工具
     * @param wsName sheet页名字
     * @throws WriteException 无法写入异常
     */
    public CapExcelHelper(WritableWorkbook wb, String wsName) throws WriteException {
        this.wk = wb.createSheet(wsName, wb.getNumberOfSheets());
        createFormat();
        createPrintSetting();
        this.fmt = fmtTWL1;
    }

    /**
     * @description 向指定的sheet页cell中写入标签 write a table cell into the sheetbook
     * @version 1.0
     * @title 向指定的sheet页cell中写入标签 write a table cell into the sheetbook
     * @<NAME_EMAIL>
     * @param labelName
     *            the value of the cell,cell 的标签名
     * @param x
     *            the row number of the cell in the booksheet,cell在sheet页中,所在的列号
     * @param y
     *            the column number of the cell in the
     *            booksheet,cell在sheet页中,所在的行号
     * @param width
     *            how many rows to merge if merge itself use 1,cell将会合并几行
     * @param height
     *            how many columns to merge if merge itself use 1 ,cell将会合并几列
     * @throws WriteException
     *             无法写入异常
     */
    public void writeLabel(String labelName, int x, int y, int width, int height) throws WriteException {
        if ((x >= 0) && (y >= 0) && (width > 0) && (height > 0)) {
            Label lblColumn = null;

            if ((width != 1) || (height != 1)) {
                wk.mergeCells(x, y, (x + width) - 1, (y + height) - 1);
            }

            lblColumn = new Label(x, y, labelName, fmt);
            wk.addCell(lblColumn);
        }
    }

    /**
     * @description 向指定的sheet页cell中写入标签 write a table cell into the sheetbook
     * @version 1.0
     * @title 向指定的sheet页cell中写入标签 write a table cell into the sheetbook
     * @<NAME_EMAIL>
     * @param labelName
     *            the value of the cell,cell 的标签名
     * @param x
     *            the row number of the cell in the booksheet,cell在sheet页中,所在的列号
     * @param y
     *            the column number of the cell in the
     *            booksheet,cell在sheet页中,所在的行号
     * @param width
     *            how many rows to merge if merge itself use 1,cell将会合并几行
     * @param height
     *            how many columns to merge if merge itself use 1 ,cell将会合并几列
     * @throws WriteException
     *             无法写入异常
     */
    public void writeLabel(String labelName, int x, int y, int width, int height, WritableCellFormat _fmt) throws WriteException {
        if ((x >= 0) && (y >= 0) && (width > 0) && (height > 0)) {
            Label lblColumn = null;
            
            if ((width != 1) || (height != 1)) {
                wk.mergeCells(x, y, (x + width) - 1, (y + height) - 1);
            }
            
            lblColumn = new Label(x, y, labelName, _fmt);
            wk.addCell(lblColumn);
        }
    }

    /**
     * @description 向指定的cell 中写入double类型的数据
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param numberData
     *            cell中值为double类型的cell
     * @param x
     *            cell在sheet页中,所在的行号
     * @param y
     *            cell在sheet页中,所在的列号
     * @param width
     *            cell将会合并几行
     * @param height
     *            cell将会合并几列
     * @throws WriteException
     *             无法写入异常
     */
    public void writeNumber(double numberData, int x, int y, int width, int height) throws WriteException {
        if ((x >= 0) && (y >= 0) && (width > 0) && (height > 0)) {
            jxl.write.Number numColumn = null;

            if ((width != 1) || (height != 1)) {
                wk.mergeCells(x, y, (x + width) - 1, (y + height) - 1);
            }

            numColumn = new jxl.write.Number(x, y, numberData, fmt);
            wk.addCell(numColumn);
        }
    }
    /**
     * @description 向指定的cell 中写入double类型的数据
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param numberData
     *            cell中值为double类型的cell
     * @param x
     *            cell在sheet页中,所在的行号
     * @param y
     *            cell在sheet页中,所在的列号
     * @param width
     *            cell将会合并几行
     * @param height
     *            cell将会合并几列
     * @throws WriteException
     *             无法写入异常
     */
    @SuppressWarnings("deprecation")
    public void writeNumber(double numberData, int x, int y, int width, int height, WritableCellFormat _fmt) throws WriteException {
        if ((x >= 0) && (y >= 0) && (width > 0) && (height > 0)) {
            jxl.write.Number numColumn = null;
            
            if ((width != 1) || (height != 1)) {
                wk.mergeCells(x, y, (x + width) - 1, (y + height) - 1);
            }
            
            numColumn = new jxl.write.Number(x, y, numberData, _fmt);
            int intVal = (int)numberData;
            int zeroCount = String.valueOf(intVal).length() - CodeCst.NUMBER_FOUR;
            if(zeroCount >0){
                if(wk.getColumnWidth(x)<(int)(CodeCst.NUMBER_EIGHT + (zeroCount/CodeCst.NUMBER_THREE+CodeCst.NUMBER_ZEROFIVE)*CodeCst.NUMBER_THREE)){
                    wk.setColumnView(x, (int)(CodeCst.NUMBER_EIGHT + (zeroCount/CodeCst.NUMBER_THREE+CodeCst.NUMBER_ZEROFIVE)*CodeCst.NUMBER_THREE));
                }
            }
            wk.addCell(numColumn);
        }
    }

    /**
     * @description 设置列宽
     * @version 1.0
     * @title 设置列宽
     * @<NAME_EMAIL>
     * @param num
     *            列在sheet页中的索引
     * @param width
     *            ,列的宽度
     * @throws WriteException
     *             无法写入异常
     */
    public void setColumnWidth(int num, int width) throws WriteException {
        if ((num >= 0) && (width > 0)) {
            wk.setColumnView(num, width);
        }
    }
    /**
     * 设置自动列宽
     * @param num 列在sheet页中的索引
     * @throws WriteException
     */
    public void setColumnWidthAuto(int num) throws WriteException {
        if ((num >= 0)) {
            CellView cellView = new CellView();  
            cellView.setAutosize(true);
            wk.setColumnView(num, cellView);
        }
    }

    /**
     * @description 设置默认的格式化方式
     * @version 1.0
     * @title 设置默认的格式化方式
     * @<NAME_EMAIL>
     * @param type 格式化类型包括"RptName","RptSubName","Note"
     */
    public void setFmt(String type) {
        if (RPTNAME.equals(type)) {
            this.fmt = fmtRptName;
        } else if (RPTSUBNAME.equals(type)) {
            this.fmt = fmtSubRptName;
        } else if (NOTE.equals(type)) {
            this.fmt = fmtNote;
        }
    }

    /**
     * @description 创建默认打印格式
     * @version V1.0.0
     * @title
     * @<NAME_EMAIL> 
    */
    private void createPrintSetting(){
        SheetSettings ss = this.wk.getSettings();
        ss.setOrientation(PageOrientation.LANDSCAPE);
        ss.setPaperSize(PaperSize.A4);
        ss.setHorizontalCentre(false);
        ss.setHeaderMargin(CodeCst.NUMBER_ZEROONE);
        ss.setFooterMargin(CodeCst.NUMBER_ZEROONE);
        ss.setVerticalCentre(false);
        ss.setTopMargin(CodeCst.NUMBER_ZEROTHREE);
        ss.setLeftMargin(CodeCst.NUMBER_ZEROTHREE);
        ss.setRightMargin(CodeCst.NUMBER_ZEROTHREE);
        ss.setBottomMargin(CodeCst.NUMBER_ZEROTHREE);
    }
    
    /**
     * @description 创建格式化
     * @version 1.0
     * @title 创建格式化
     * <AUTHOR> <EMAIL>
     * @throws WriteException 无法写入异常
     */
    private void createFormat() throws WriteException {
        WritableFont rptNameFont = null;
        WritableFont rptSubNameFont = null;
        WritableFont thinFont = null;
        WritableFont boldFont = null;
        // Font : Arial, Bold, 14/12 size
        rptNameFont = new WritableFont(WritableFont.ARIAL);
        rptNameFont.setBoldStyle(WritableFont.BOLD);
        rptNameFont.setPointSize(CodeCst.NUMBER_FOURTEEN);
        rptSubNameFont = new WritableFont(WritableFont.ARIAL);
        rptSubNameFont.setBoldStyle(WritableFont.BOLD);
        rptSubNameFont.setPointSize(CodeCst.NUMBER_TWELVE);
        // Font: Arial, Bold, 10size
        boldFont = new WritableFont(WritableFont.ARIAL);
        boldFont.setBoldStyle(WritableFont.BOLD);
        boldFont.setPointSize(CodeCst.NUMBER_TEN);
        // Font: Arial, 10size
        thinFont = new WritableFont(WritableFont.ARIAL);
        thinFont.setPointSize(CodeCst.NUMBER_TEN);
        // RptName Format
        fmtRptName = new WritableCellFormat(rptNameFont);
        fmtRptName.setAlignment(Alignment.CENTRE);
//        fmtRptName.setBackground(Colour.WHITE);
        fmtRptName.setWrap(false);
        fmtRptName.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtRptName.setBorder(Border.NONE, BorderLineStyle.THIN);
        fmtSubRptName = new WritableCellFormat(rptSubNameFont);
        fmtSubRptName.setAlignment(Alignment.LEFT);
//        fmtSubRptName.setBackground(Colour.WHITE);
        fmtSubRptName.setWrap(true);
        fmtSubRptName.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtSubRptName.setBorder(Border.NONE, BorderLineStyle.THIN);
        // setting
        fmtBGC1 = new WritableCellFormat(boldFont);
        fmtBGC1.setAlignment(Alignment.CENTRE);
//        fmtBGC1.setBackground(Colour.GRAY_25);
        fmtBGC1.setWrap(true);
        fmtBGC1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtBGC1.setBorder(Border.ALL, BorderLineStyle.THIN);
        fmtBWC1 = new WritableCellFormat(boldFont);
        fmtBWC1.setAlignment(Alignment.CENTRE);
//        fmtBWC1.setBackground(Colour.WHITE);
        fmtBWC1.setWrap(true);
        fmtBWC1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtBWC1.setBorder(Border.ALL, BorderLineStyle.THIN);
        fmtBWC = new WritableCellFormat(boldFont);
        fmtBWC.setAlignment(Alignment.CENTRE);
//        fmtBWC.setBackground(Colour.WHITE);
        fmtBWC.setWrap(true);
        fmtBWC.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtBWC.setBorder(Border.NONE, BorderLineStyle.THIN);
        // string
        fmtTWL1 = new WritableCellFormat(thinFont);
        fmtTWL1.setAlignment(Alignment.LEFT);
//        fmtTWL1.setBackground(Colour.WHITE);
        fmtTWL1.setWrap(true);
        fmtTWL1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWL1.setBorder(Border.ALL, BorderLineStyle.THIN);
        fmtTWR1 = new WritableCellFormat(thinFont);
        fmtTWR1.setAlignment(Alignment.RIGHT);
//        fmtTWR1.setBackground(Colour.WHITE);
        fmtTWR1.setWrap(true);
        fmtTWR1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWR1.setBorder(Border.ALL, BorderLineStyle.THIN);
        fmtTWC1 = new WritableCellFormat(thinFont);
        fmtTWC1.setAlignment(Alignment.CENTRE);
//        fmtTWC1.setBackground(Colour.WHITE);
        fmtTWC1.setWrap(true);
        fmtTWC1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWC1.setBorder(Border.ALL, BorderLineStyle.THIN);
        fmtTWL = new WritableCellFormat(thinFont);
        fmtTWL.setAlignment(Alignment.LEFT);
//        fmtTWL.setBackground(Colour.WHITE);
        fmtTWL.setWrap(false);
        fmtTWL.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWL.setBorder(Border.NONE, BorderLineStyle.THIN);
        fmtNote = fmtTWL;
        fmtTWR = new WritableCellFormat(thinFont);
        fmtTWR.setAlignment(Alignment.RIGHT);
//        fmtTWR.setBackground(Colour.WHITE);
        fmtTWR.setWrap(true);
        fmtTWR.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWR.setBorder(Border.NONE, BorderLineStyle.THIN);

        fmtTWC = new WritableCellFormat(thinFont);
        fmtTWC.setAlignment(Alignment.CENTRE);
//        fmtTWC.setBackground(Colour.WHITE);
        fmtTWC.setWrap(true);
        fmtTWC.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWC.setBorder(Border.NONE, BorderLineStyle.THIN);

        // integer
        NumberFormat nft = new NumberFormat("#,##0");
        fmtTWLi1 = new WritableCellFormat(thinFont, nft);
        fmtTWLi1.setAlignment(Alignment.LEFT);
//        fmtTWLi1.setBackground(Colour.WHITE);
        fmtTWLi1.setWrap(true);
        fmtTWLi1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWLi1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWRi1 = new WritableCellFormat(thinFont, nft);
        fmtTWRi1.setAlignment(Alignment.RIGHT);
//        fmtTWRi1.setBackground(Colour.WHITE);
        fmtTWRi1.setWrap(true);
        fmtTWRi1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWRi1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWCi1 = new WritableCellFormat(thinFont, nft);
        fmtTWCi1.setAlignment(Alignment.CENTRE);
//        fmtTWCi1.setBackground(Colour.WHITE);
        fmtTWCi1.setWrap(true);
        fmtTWCi1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWCi1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWLi = new WritableCellFormat(thinFont, nft);
        fmtTWLi.setAlignment(Alignment.LEFT);
//        fmtTWLi.setBackground(Colour.WHITE);
        fmtTWLi.setWrap(true);
        fmtTWLi.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWLi.setBorder(Border.NONE, BorderLineStyle.THIN);

        fmtTWRi = new WritableCellFormat(thinFont, nft);
        fmtTWRi.setAlignment(Alignment.RIGHT);
//        fmtTWRi.setBackground(Colour.WHITE);
        fmtTWRi.setWrap(true);
        fmtTWRi.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWRi.setBorder(Border.NONE, BorderLineStyle.THIN);

        fmtTWCi = new WritableCellFormat(thinFont, nft);
        fmtTWCi.setAlignment(Alignment.CENTRE);
//        fmtTWCi.setBackground(Colour.WHITE);
        fmtTWCi.setWrap(true);
        fmtTWCi.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWCi.setBorder(Border.NONE, BorderLineStyle.THIN);

        // double
        nft = new NumberFormat("#,##0.00");
        fmtTWLd1 = new WritableCellFormat(thinFont, nft);
        fmtTWLd1.setAlignment(Alignment.LEFT);
//        fmtTWLd1.setBackground(Colour.WHITE);
        fmtTWLd1.setWrap(true);
        fmtTWLd1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWLd1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWRd1 = new WritableCellFormat(thinFont, nft);
        fmtTWRd1.setAlignment(Alignment.RIGHT);
//        fmtTWRd1.setBackground(Colour.WHITE);
        fmtTWRd1.setWrap(true);
        fmtTWRd1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWRd1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWCd1 = new WritableCellFormat(thinFont, nft);
        fmtTWCd1.setAlignment(Alignment.CENTRE);
//        fmtTWCd1.setBackground(Colour.WHITE);
        fmtTWCd1.setWrap(true);
        fmtTWCd1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWCd1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWLd = new WritableCellFormat(thinFont, nft);
        fmtTWLd.setAlignment(Alignment.LEFT);
//        fmtTWLd.setBackground(Colour.WHITE);
        fmtTWLd.setWrap(true);
        fmtTWLd.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWLd.setBorder(Border.NONE, BorderLineStyle.THIN);

        fmtTWRd = new WritableCellFormat(thinFont, nft);
        fmtTWRd.setAlignment(Alignment.RIGHT);
//        fmtTWRd.setBackground(Colour.WHITE);
        fmtTWRd.setWrap(true);
        fmtTWRd.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWRd.setBorder(Border.NONE, BorderLineStyle.THIN);

        fmtTWCd = new WritableCellFormat(thinFont, nft);
        fmtTWCd.setAlignment(Alignment.CENTRE);
//        fmtTWCd.setBackground(Colour.WHITE);
        fmtTWCd.setWrap(true);
        fmtTWCd.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWCd.setBorder(Border.NONE, BorderLineStyle.THIN);
    }
    
    public WritableCellFormat getFmt() {
        return fmt;
    }

    public WritableCellFormat getFmtRptName() {
        return fmtRptName;
    }

    public WritableCellFormat getFmtSubRptName() {
        return fmtSubRptName;
    }

    public WritableCellFormat getFmtNote() {
        return fmtNote;
    }

    /**
     * @description 字符 居中 灰底 有边框 粗
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtBGC1() {
        return fmtBGC1;
    }

    /**
     * @description 字符 居中 白底 有边框 粗
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtBWC1() {
        return fmtBWC1;
    }

    /**
     * @description 字符 居中 白底 无边框 粗
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtBWC() {
        return fmtBWC;
    }

    /**
     * @description 字符 居左 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWL1() {
        return fmtTWL1;
    }

    /**
     * @description 字符 居右 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWR1() {
        return fmtTWR1;
    }

    /**
     * @description 字符 居种 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWC1() {
        return fmtTWC1;
    }
    
    /**
     * @description 字符 居左 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWL() {
        return fmtTWL;
    }

    /**
     * @description 字符 居右 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWR() {
        return fmtTWR;
    }

    /**
     * @description 字符 居中 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWC() {
        return fmtTWC;
    }
    
    /**
     * @description 整数字 居左 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWLi1() {
        return fmtTWLi1;
    }
    /**
     * @description 整数字 居右 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWRi1() {
        return fmtTWRi1;
    }
    /**
     * @description 整数字 居中 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWCi1() {
        return fmtTWCi1;
    }

    /**
     * @description 整数字 居左 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWLi() {
        return fmtTWLi;
    }

    /**
     * @description 整数字 居右 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWRi() {
        return fmtTWRi;
    }

    /**
     * @description 整数字 居中 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWCi() {
        return fmtTWCi;
    }

    /**
     * @description 浮点数字 居左 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWLd1() {
        return fmtTWLd1;
    }

    /**
     * @description 浮点数字 居右 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWRd1() {
        return fmtTWRd1;
    }

    /**
     * @description 浮点数字 居中 白底 有边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWCd1() {
        return fmtTWCd1;
    }

    /**
     * @description 浮点数字 居左 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWLd() {
        return fmtTWLd;
    }

    /**
     * @description 浮点数字 居右 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWRd() {
        return fmtTWRd;
    }

    /**
     * @description 浮点数字 居中 白底 无边框 细
     * @version
     * @title
     * @<NAME_EMAIL>
     * @return 
    */
    public WritableCellFormat getFmtTWCd() {
        return fmtTWCd;
    }
}
