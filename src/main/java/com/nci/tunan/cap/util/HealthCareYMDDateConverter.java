package com.nci.tunan.cap.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import org.apache.xmlbeans.impl.regex.ParseException;
import com.thoughtworks.xstream.converters.ConversionException;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;

/** 
 * @description 日期格式化
 * <AUTHOR> 
 * @date 2020年1月16日 下午3:37:48 
 * @.belongToModule  上海医保收付费 
*/
public class HealthCareYMDDateConverter implements Converter {

	/**
	 * @description 序列化
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.thoughtworks.xstream.converters.Converter#marshal(java.lang.Object, com.thoughtworks.xstream.io.HierarchicalStreamWriter, com.thoughtworks.xstream.converters.MarshallingContext)
	 * @param source 对象
	 * @param writer 写入对象
	 * @param context 上下文
	*/
	@Override
	public void marshal(Object source, HierarchicalStreamWriter writer,
			MarshallingContext context) {
		
	}

	/**
	 * @description 格式化当前系统日期  
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.thoughtworks.xstream.converters.Converter#unmarshal(com.thoughtworks.xstream.io.HierarchicalStreamReader, com.thoughtworks.xstream.converters.UnmarshallingContext)
	 * @param reader 读取对象
	 * @param context 上下文
	 * @return 格式后日期
	*/
	@Override
	public Object unmarshal(HierarchicalStreamReader reader,
			UnmarshallingContext context) {
		 GregorianCalendar calendar = new GregorianCalendar();   
	        SimpleDateFormat dateFm = new SimpleDateFormat("yyyyMMdd"); //格式化当前系统日期  
	        try {  
	                calendar.setTime(dateFm.parse(reader.getValue()));  
	        } catch (ParseException e) {  
	                throw new ConversionException(e.getMessage(), e);  
	        } catch (java.text.ParseException e) {
				e.printStackTrace();
			}   
	        return calendar.getTime();
	}

	/**
	 * @description 日期类型
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.thoughtworks.xstream.converters.ConverterMatcher#canConvert(java.lang.Class)
	 * @param type
	 * @return 成功或失败
	*/
	@Override
	public boolean canConvert(Class type) {
		
		return Date.class == type;
	}



}
