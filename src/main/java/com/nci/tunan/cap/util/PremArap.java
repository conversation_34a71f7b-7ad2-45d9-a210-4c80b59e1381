package com.nci.tunan.cap.util;

import com.thoughtworks.xstream.annotations.XStreamAlias;


/** 
 * @ClassName: PremArap 
 * @description VSM请求报文对象
 * <AUTHOR>
 * @date 2016年6月17日 上午10:22:23
 * @.belongToModule 收付费-推送VMS数据
 */ 
public class PremArap{
    /** 
     * 报文头字段
     */ 
    @XStreamAlias("SID")
    private String sid;
    /** 
     * 报文头字段
     */ 
    @XStreamAlias("SYSCODE")
    private String syscode;
    /** 
     * 报文头字段
     */ 
    @XStreamAlias("RESULT")
    private PAList result;
    public String getSid() {
        return sid;
    }
    public void setSid(String sid) {
        this.sid = sid;
    }
    public String getSyscode() {
        return syscode;
    }
    public void setSyscode(String syscode) {
        this.syscode = syscode;
    }
    public PAList getResult() {
        return result;
    }
    public void setResult(PAList result) {
        this.result = result;
    }
}
