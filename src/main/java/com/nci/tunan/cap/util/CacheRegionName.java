package com.nci.tunan.cap.util;

/**
 * 
 * @description 缓存区域块名字
 * <AUTHOR> <EMAIL> 
 * @date 2015-9-25 下午1:37:57
 * @.belongToModule 收付费-推送VMS数据
 * 
 */
public enum CacheRegionName {

    /**
     * 从不或是很少刷新的缓存域名字
     */
    CAP_InmutableCache("cap-InmutableCache"),
    /**
     * 存放中等频度刷新的缓存域名字
     */
    CAP_MidtableCache("cap-MidtableCache"),
    /**
     * 存放可以随时变化的缓存域名字
     */
    CAP_MutableCache("cap-MutableCache");

    /** 
     * 区域名称
     */ 
    private String regionName;

    /** 
     * @description 获取地区名
     * @param regionName  缓存地址名称
     * @return 返回类型 
     * @throws 
     */
    private CacheRegionName(String regionName) {
        this.regionName = regionName;
    }

    /** 
     * @description 获取地区名
     * @return 返回类型 
     * @throws 
     */
    @Override
    public String toString() {
        return regionName;
    }
}
