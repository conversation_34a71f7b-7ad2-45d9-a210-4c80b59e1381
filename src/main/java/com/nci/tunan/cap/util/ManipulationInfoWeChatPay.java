package com.nci.tunan.cap.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Colour;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationDetailsList;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationFmsinterface;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationInfoBO;
import com.nci.tunan.cap.interfaces.model.vo.ManipulationInfoVO;
import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.ftp.FtpUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

public class ManipulationInfoWeChatPay {
	 
	/**
	 * 实时对账文件读取批处理service
	 */
   private IManipulationInfoService manipulationInfoService;
   /**
    * 日志工具
    */
   protected Logger logger = LoggerFactory.getLogger();
	public IManipulationInfoService getManipulationInfoService() {
		return manipulationInfoService;
	}
	 
	public void setManipulationInfoService(
			IManipulationInfoService manipulationInfoService) {
		this.manipulationInfoService = manipulationInfoService;
	}

	/**
	 * @description 获取FTP客户端；上传微信对账Excel文件
	 * @param @return
	 * @param @throws IOException 参数
	 */
	private FTPClient getFTPExcelClient() throws IOException{
		try{
			String serverIp = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_IP);
	        String ftpServerPort = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_PORT);
	        String ftpUserName = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_USER_NAME);
	        String ftpPassword = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_PASSWORD);		
	        FtpServerConfig ftpServerConfig = new FtpServerConfig();
	        ftpServerConfig.setFtpServerIp(serverIp);
	        ftpServerConfig.setFtpServerPort(Integer.parseInt(ftpServerPort));
	        ftpServerConfig.setFtpUserName(ftpUserName);
	        ftpServerConfig.setFtpPassword(ftpPassword);
	        FTPClient ftpClient = FtpUtil.loginFtpServer(ftpServerConfig);
	        ftpClient.setControlEncoding("GBK"); // 设置格式
	        ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
	        ftpClient.enterLocalPassiveMode();// 设置被动模式
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
			return ftpClient;
		}catch (IOException e) {
			throw e;
		}   
		
	}
	/**
	 * @description 微信支付对账处理
	 * @param infoVO 对账参数
	 */
	public void toExcl(ManipulationInfoVO infoVO){
		
       logger.debug("------------------write------------------------");
     	if(infoVO.getEndDate()== null|| "".equals(infoVO.getEndDate())
     			|| infoVO.getStartDate() == null||"".equals(infoVO.getStartDate())) {
     		Date enterAccDate = DateUtilsEx.getTodayDate();
         	enterAccDate = DateUtilsEx.addDay(enterAccDate, -1);
         	infoVO.setStartDate(DateUtilsEx.formatToString(enterAccDate, "YYYY-MM-dd"));
       	infoVO.setEndDate(DateUtilsEx.formatToString(enterAccDate, "YYYY-MM-dd"));
     	}
     	infoVO.setPayType("72");
   	infoVO.setServicerCode("WXZF");
   	infoVO.setReturnCode("1");
   	
  //读取数据的ftp服务器的对账目录
	String reconciliationRead =PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT,
			CodeCst.TXT_FILE_P);
	 String reconciliationbackups =PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT,
	            CodeCst.FILE_P_BAK);
   	
       int minusDays = CodeCst.NUMBER_THREE;
       for(int i = 1; i <= minusDays; i++){
       	try {
       		ManipulationInfoBO infoARBos = BeanUtils.copyProperties(ManipulationInfoBO.class, infoVO);
       		//1.查询对账文件导入明细表 
       		List<ManipulationInfoBO> arManiInfoBOList = manipulationInfoService.findAllManipulationWeChat(infoARBos);
       	
       		//2.查询实收付表 微信支付日结数据
       		List<ManipulationInfoBO> arCashDetailBOList = manipulationInfoService.findAllCashDetailWeChat(infoARBos);
       		
       		logger.debug("------------------微信支付对账封装日结、对账数据到map集合  ------------------------");
       		//3.组装数据
       		Map<String,ManipulationDetailsList> mmap=this.getCheckList(arCashDetailBOList, arManiInfoBOList,infoVO);
       		 
       		//4 生成文件
       		logger.debug("------------------微信支付对账开始生成对账文件  ------------------------");
       		this.doDownloadManipulationInfoImpl(mmap,infoVO);
       		//付费对账文件
       	} catch (Exception e) {
       		e.printStackTrace();
       	}
       	
       }
       
   }
	
	  /**
	    * @description 获取对账日期
	    * @param startDate 开始日期
	    * @param endDate 结束日期
	    * @param @return 参数
	    */
	  public  Set<String> getAllDate(String startDate, String endDate){
	      // oneDay = 1000 * 60 * 60 * 24l;
	      Set<String> dates = new LinkedHashSet<String>();
	      Calendar dateCal = Calendar.getInstance();  
	      Date stDate =DateUtilsEx.formatToDate(startDate, "");
	      Date enDate =DateUtilsEx.formatToDate(endDate, "");
	      int diffDay = (int) ((enDate.getTime() - stDate.getTime())/(CodeCst.NUMBER_THOUSAND * CodeCst.NUMBER_SIXTY * CodeCst.NUMBER_SIXTY * CodeCst.NUMBER_TWENTY_FOUR));
	      dateCal.setTime(stDate);
	      dates.add(DateUtilsEx.formatToString(dateCal.getTime(), ""));
	      for(int i = 1 ; i <= diffDay ; i++){
	          dateCal.add(Calendar.DAY_OF_MONTH, 1);
	          dates.add(DateUtilsEx.formatToString(dateCal.getTime(), ""));
	      }      
	      return dates;
	  }
	
		/**
		 * @description 生成对账差异清单
		 * @param  mmap 封装微信支付对账差异数据
		 * @param  infoVO 对账文件明细差异
		 * @param @throws Exception 参数
		 */
	private void doDownloadManipulationInfoImpl(
			Map<String,ManipulationDetailsList> mmap, ManipulationInfoVO infoVO) throws IOException {
       String excelName = "Wxpaybill_"+ infoVO.getArapFlag() + "_" + infoVO.getStartDate() + ".xls";
       FTPClient ftpClient = this.getFTPExcelClient();
       InputStream local=null;
       File file = new File(excelName);
       WritableWorkbook wwb = Workbook.createWorkbook(file);
       String arapFlag=infoVO.getArapFlag();
       try {
           // 设置格式字体
           WritableFont wf = new jxl.write.WritableFont(WritableFont.ARIAL, CodeCst.NUMBER_FIFTEEN, WritableFont.BOLD,
                   false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
           WritableCellFormat wcf = new WritableCellFormat(wf);
           wcf.setVerticalAlignment(VerticalAlignment.CENTRE);
           wcf.setAlignment(Alignment.CENTRE);
           //************** 往工作表中添加数据 *****************//*
           int rowNum = 0; // 初始化行数
           int workAreaNum = 0; // 工作区
           WritableSheet ws = null;
           Map<String, Object> maps = new HashMap<String, Object>();
           
           
           workAreaNum = workAreaNum++;
           // 创建Excel工作表 指定名称和位置
           ws = wwb.createSheet("微信支付对账差异明细", workAreaNum);
           workAreaNum = 1;
           maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
           workAreaNum = (Integer) maps.get("workAreaNum");
           rowNum = (Integer) maps.get("rowNum");
           ws = (WritableSheet) maps.get("ws");
           for(String key:mmap.keySet()){
           	//对账日期
           	String enterAccDate = key;
           	//对账明细
           	ManipulationDetailsList list = mmap.get(enterAccDate);

               rowNum = rowNum + 1;
               ws.addCell(new Label(0, rowNum, "日期"));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(1, rowNum, key));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
             
               
               if(arapFlag.equals(CodeCst.AR_AP__AP)){
               	ws.addCell(new Label(2, rowNum, "付费方式:"));
               }else{
               	ws.addCell(new Label(2, rowNum, "收费方式:"));
               }
               
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(CodeCst.NUMBER_THREE, rowNum, "微信支付"));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               
               //差异明细
               BigDecimal endOfDayNum = list.getEndOfDayNum();
               BigDecimal endTotalPremium = list.getEndTotalPremium();
               if (endOfDayNum == null) {
                   endOfDayNum = new BigDecimal(0);
               }
               if (endTotalPremium == null) {
                   endTotalPremium = new BigDecimal(0);
               }
               maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
               workAreaNum = (Integer) maps.get("workAreaNum");
               rowNum = (Integer) maps.get("rowNum");
               ws = (WritableSheet) maps.get("ws");
               rowNum = rowNum + 1;
               ws.addCell(new Label(0, rowNum, "微信"));
               
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(1, rowNum, "笔数"));
               ws.setColumnView(1, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(2, rowNum, "总金额"));
               ws.setColumnView(2, CodeCst.NUMBER_TWENTY_FIVE);
             
               ws.addCell(new Label(CodeCst.NUMBER_THREE, rowNum, "业务号"));
               ws.setColumnView(CodeCst.NUMBER_THREE, CodeCst.NUMBER_TWENTY_FIVE);
              
               	ws.addCell(new Label(CodeCst.NUMBER_FOUR, rowNum, "实收/实付流水号"));
               	ws.setColumnView(CodeCst.NUMBER_FOUR, CodeCst.NUMBER_TWENTY_FIVE);
               
              
               	ws.addCell(new Label(CodeCst.NUMBER_FIVE, rowNum, "批次号"));
                ws.setColumnView(CodeCst.NUMBER_FIVE, CodeCst.NUMBER_TWENTY_FIVE);
               
               
               
               ws.addCell(new Label(CodeCst.NUMBER_SIX, rowNum, "金额"));
               ws.setColumnView(CodeCst.NUMBER_SIX, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(CodeCst.NUMBER_SEVEN, rowNum, "管理机构"));
               ws.setColumnView(CodeCst.NUMBER_SEVEN, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(CodeCst.NUMBER_EIGHT, rowNum, "备注"));
               ws.setColumnView(CodeCst.NUMBER_EIGHT, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new Label(CodeCst.NUMBER_NINE, rowNum, "数补处理"));
               ws.setColumnView(CodeCst.NUMBER_NINE, CodeCst.NUMBER_TWENTY_FIVE);
               
               maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
               workAreaNum = (Integer) maps.get("workAreaNum");
               rowNum = (Integer) maps.get("rowNum");
               ws = (WritableSheet) maps.get("ws");
               rowNum = rowNum + 1;
            
           	ws.addCell(new Label(0, rowNum,  "WXZF日结"));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new jxl.write.Number(1, rowNum, CapVerify.ternaryOperator(null == endOfDayNum,
                       BigDecimal.ZERO, endOfDayNum).doubleValue()));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               if(arapFlag.equals(CodeCst.AR_AP__AP)){
               	ws.addCell(new jxl.write.Number(2, rowNum, CapVerify.ternaryOperator(null == endTotalPremium,
                           BigDecimal.ZERO, endTotalPremium).doubleValue()));
                           //new BigDecimal(0).subtract()
               }else{
               	ws.addCell(new jxl.write.Number(2, rowNum, CapVerify.ternaryOperator(null == endTotalPremium,
                           BigDecimal.ZERO, endTotalPremium).doubleValue()));
               }
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
               workAreaNum = (Integer) maps.get("workAreaNum");
               rowNum = (Integer) maps.get("rowNum");
               ws = (WritableSheet) maps.get("ws");
               rowNum = rowNum + 1;
               ws.addCell(new Label(0, rowNum, "对账文件"));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new jxl.write.Number(1, rowNum, CapVerify.ternaryOperator(
                       null == list.getReconciliationNum(), BigDecimal.ZERO,
                       		list.getReconciliationNum()).doubleValue()));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new jxl.write.Number(2, rowNum, CapVerify.ternaryOperator(
                       null == list.getRecTotalPremium(), BigDecimal.ZERO, list.getRecTotalPremium())
                       .doubleValue()));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
               workAreaNum = (Integer) maps.get("workAreaNum");
               rowNum = (Integer) maps.get("rowNum");
               ws = (WritableSheet) maps.get("ws");
               rowNum = rowNum + 1;
               ws.addCell(new Label(0, rowNum, "差额"));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new jxl.write.Number(1, rowNum, CapVerify.ternaryOperator(
                       null == list.getDifferenceNum(), BigDecimal.ZERO, list.getDifferenceNum())
                       .doubleValue()));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               ws.addCell(new jxl.write.Number(2, rowNum, CapVerify.ternaryOperator(
                       null == list.getDifTotalPremium(), BigDecimal.ZERO, list.getDifTotalPremium())
                       .doubleValue()));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
               List<ManipulationFmsinterface> manFmslist = list.getManFmslist();
               for (int i = 0; i < manFmslist.size(); i++) {
               	 // 集合
                   ManipulationFmsinterface maniFms = manFmslist.get(i);
                   maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
                   workAreaNum = (Integer) maps.get("workAreaNum");
                   rowNum = (Integer) maps.get("rowNum");
                   ws = (WritableSheet) maps.get("ws");
                   rowNum = rowNum + 1;
                   // 业务号
                   ws.addCell(new Label(CodeCst.NUMBER_THREE, rowNum, CapVerify.ternaryOperator(
                           null == maniFms.getCertifyNo(), "", maniFms.getCertifyNo())));
                   ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
                   //实收/实付流水号
                   ws.addCell(new Label(CodeCst.NUMBER_FOUR, rowNum, CapVerify.ternaryOperator(
                           null == maniFms.getUnitNumber(), "", maniFms.getUnitNumber())));
                   ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
                   //批次号
                  
                   	 ws.addCell(new Label(CodeCst.NUMBER_FIVE, rowNum, CapVerify.ternaryOperator(
                                null == maniFms.getBatchId(), "", maniFms.getBatchId())));
                        ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
                   // 金额
                   ws.addCell(new jxl.write.Number(CodeCst.NUMBER_SIX, rowNum, Double.valueOf(CapVerify
                           .ternaryOperator(null == maniFms.getPremium(), "", maniFms.getPremium()).toString())));
                   ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
                   // 管理机构
                   if (null != maniFms.getOrganName() && !"".equals(maniFms.getOrganName())) {
                       ws.addCell(new Label(CodeCst.NUMBER_SEVEN, rowNum, maniFms.getOrganName()));
                   } else {
                       ws.addCell(new Label(CodeCst.NUMBER_SEVEN, rowNum, "86"));
                   }
                   ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
                   // 备注
                   ws.addCell(new Label(CodeCst.NUMBER_EIGHT, rowNum, CapVerify.ternaryOperator(
                           null == maniFms.getRemarks(), "", maniFms.getRemarks())));
                   ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
                  //数补处理
                   ws.addCell(new Label(CodeCst.NUMBER_NINE, rowNum, CapVerify.ternaryOperator(
                           null == maniFms.getItsmNo(), "", maniFms.getItsmNo())));
                   ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				}
               maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
               workAreaNum = (Integer) maps.get("workAreaNum");
               rowNum = (Integer) maps.get("rowNum");
               ws = (WritableSheet) maps.get("ws");
               // 空行
               rowNum = rowNum + 1;
               ws.addCell(new Label(0, rowNum, ""));
               ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
           }
           wwb.write();
           wwb.close();
           local = new FileInputStream(file);
           String reconciliationbackups = "cap/tms/reconciliation/weChatExcel/" + infoVO.getStartDate();
           boolean flag = ftpClient.changeWorkingDirectory(reconciliationbackups);
           if(!flag){
               ftpClient.makeDirectory(reconciliationbackups);
               ftpClient.changeWorkingDirectory(reconciliationbackups);
           }
           ftpClient.storeFile(new String(excelName.getBytes("utf-8"), "iso-8859-1"), local);
           local.close();
       } catch (UnsupportedEncodingException e) {
           e.printStackTrace();
           throw new BizException("微信支付对账差异明细Excel导出失败！");
       } catch (IOException e) {
           e.printStackTrace();
           throw new BizException("微信支付对账差异明细Excel导出失败！");
       } catch (Exception e) {
           e.printStackTrace();
           throw new BizException("微信支付对账差异明细Excel导出失败！");
       } finally {
           FtpUtil.logoutFtpServer(ftpClient);
       }
       
   
	}

	/**
	 * @description 转Map集合
	 * @param arCashDetailBOList List集合
	 * @param @return 参数
	*/
	private Map<String, List<ManipulationInfoBO>> toMap(List<ManipulationInfoBO> arCashDetailBOList) {
		//合并2个结果集 ，封装map集合
		 Map<String, List<ManipulationInfoBO>> treeMap = new TreeMap<String,  List<ManipulationInfoBO>>(
	                new Comparator<String>() {
	                    @Override
	                    public int compare(String o1, String o2) {
	                        return o1.compareTo(o2);
	                    }

	                }); 		 
		for(ManipulationInfoBO cashBO: arCashDetailBOList){
         String enterAccDate = cashBO.getRemarks();
         if (treeMap.containsKey(enterAccDate)) {
      	   treeMap.get(enterAccDate).add(cashBO);
         } else {
      	   List<ManipulationInfoBO> list = new ArrayList<ManipulationInfoBO>();
      	   list.add(cashBO);
      	   treeMap.put(enterAccDate, list);
         }

     }
		
		return treeMap;
	}

	
	/**
	 * @description 组合封装对账、日结差异清单
	 * @param arCashDetailBOList 日结差异对象列表
	 * @param arManiInfoBOList 对账差异对象列表
	 * @param infoVO 对账入参
	 * @param @return 参数
	 */
	public Map<String,ManipulationDetailsList> getCheckList(List<ManipulationInfoBO> arCashDetailBOList, List<ManipulationInfoBO> arManiInfoBOList,ManipulationInfoVO infoVO) {
		Map<String,ManipulationDetailsList>   compmap = new TreeMap<String, ManipulationDetailsList>();
		Map<String,List<ManipulationInfoBO>>   cashDetailmap = this.toMap(arCashDetailBOList);
		Map<String,List<ManipulationInfoBO>>   maniInfomap = this.toMap(arManiInfoBOList);
		 //获取页面选择的日期集合
       Set<String> dates = this.getAllDate(infoVO.getStartDate(), infoVO.getEndDate());
       for (String dateKay : dates){
       	Map<String,ManipulationInfoBO> mapCash = new HashMap<String,ManipulationInfoBO>();
           Map<String,ManipulationInfoBO> mapMain= new HashMap<String,ManipulationInfoBO>();
       	ManipulationDetailsList manipulationDetailsList = new ManipulationDetailsList();
       	List<ManipulationInfoBO> manipulationInfoBOList = new ArrayList<ManipulationInfoBO>();
       	//日结总笔数 
   		BigDecimal totalOfDay = new BigDecimal(0);
   		//日结总金额
   		BigDecimal totalOfDayPrem = new BigDecimal(0);
   		//对账笔数
   		BigDecimal totalofMani = new BigDecimal(0);
   		//对账总金额
   		BigDecimal totalofManiPrem =new BigDecimal(0);
   		// 相差笔数
           BigDecimal diftotal = new BigDecimal(0);
           // 相差总金额
           BigDecimal difTotalOfPrem = new BigDecimal(0);
			if(maniInfomap.containsKey(dateKay)){
				List<ManipulationInfoBO> list = maniInfomap.get(dateKay);
				//处理对账
				if(arManiInfoBOList!=null){
					totalofMani = new BigDecimal(list.size());
		    		for (int j = 0; j < list.size(); j++) {
		    			mapMain.put(list.get(j).getBatchId(), list.get(j));
		    			totalofManiPrem =totalofManiPrem.add(list.get(j).getPremium());
					}
				}
			}
			if(cashDetailmap.containsKey(dateKay)){
				List<ManipulationInfoBO> list = cashDetailmap.get(dateKay);
				//处理日结
				if(list!=null){
		    		//日结笔数
					totalOfDay =  new BigDecimal(list.size());
					for (int j = 0; j < list.size(); j++) {
						list.get(j).getPremium();
						mapCash.put(list.get(j).getBatchId(), list.get(j));
						
						if(list.get(j).getPremium().intValue()<0){
							totalOfDayPrem =  totalOfDayPrem.add(list.get(j).getPremium().abs()); 
						}else{
							totalOfDayPrem =  totalOfDayPrem.subtract(list.get(j).getPremium());
						}
						
					}
				}
			}
			for(String certifyNo : mapMain.keySet()) {
				if(!mapCash.containsKey(certifyNo) || (mapCash.containsKey(certifyNo) && 
				        mapMain.get(certifyNo).getPremium().compareTo(mapCash.get(certifyNo).getPremium().abs()) != 0)) {
					manipulationInfoBOList.add(mapMain.get(certifyNo));
				}
			}
            for (String certifyNo : mapCash.keySet()) {
                if (!mapMain.containsKey(certifyNo) || (mapMain.containsKey(certifyNo) && 
                        mapMain.get(certifyNo).getPremium().compareTo(mapCash.get(certifyNo).getPremium().abs()) != 0)) {
                    manipulationInfoBOList.add(mapCash.get(certifyNo));
                }
            }
			//差异金额
			difTotalOfPrem=totalofManiPrem.subtract(totalOfDayPrem);
			//差异笔数
			diftotal =totalofMani.subtract(totalOfDay);
			//封装差异清单部分数据
			manipulationDetailsList.setDifferenceNum(diftotal);
			manipulationDetailsList.setDifTotalPremium(difTotalOfPrem);
			manipulationDetailsList.setEndOfDayNum(totalOfDay);
			manipulationDetailsList.setEndTotalPremium(totalOfDayPrem);
			manipulationDetailsList.setReconciliationNum(totalofMani);
			manipulationDetailsList.setRecTotalPremium(totalofManiPrem);
			List<ManipulationFmsinterface> copyList = BeanUtils.copyList(ManipulationFmsinterface.class, manipulationInfoBOList);
			manipulationDetailsList.setManFmslist(copyList);
			compmap.put(dateKay, manipulationDetailsList);
		
		}
		return compmap;
}
	/**
	 * @description 创建Excel工作表 指定名称和位置
	 * @param rowNum 行号
	 * @param workAreaNum 工作区
	 * @param wwb Excel创建对象
	 * @param wcf Excel创建对象
	 * @param infoVO 对账入参
	 * @param maps 封装差异数据对象
	 * @param ws 表格对账
	 */
	private Map<String, Object> toExcelSheet(int rowNum, int workAreaNum, WritableWorkbook wwb, WritableCellFormat wcf,
           ManipulationInfoVO infoVO, Map<String, Object> maps, WritableSheet ws) throws Exception, WriteException {
       if (rowNum % CodeCst.excelMax == 0) {
           if (rowNum != 0) {
               workAreaNum = workAreaNum + 1;
               // 创建Excel工作表 指定名称和位置
               ws = wwb.createSheet("微信支付对账差异明细(" + workAreaNum + ")", workAreaNum);
               rowNum = 0;
           }
           // 创建Excel工作表 指定名称和位置
           // 列,行
           // 1最左上角列号 2最左上角行号 3最右上角列号 最右下角行号
           
           if(infoVO.getArapFlag().equals(CodeCst.AR_AP__AP)){
           	ws.addCell(new Label(0, rowNum, "微信支付对账清单-付费", wcf));
           }else{
           	ws.addCell(new Label(0, rowNum, "微信支付对账清单-收费", wcf));
           }
           
           ws.mergeCells(0, rowNum, CodeCst.NUMBER_SEVEN, rowNum);
           ws.setColumnView(0, CodeCst.NUMBER_TWO_HUNDRED);
           rowNum = rowNum + 1;
           ws.addCell(new Label(0, rowNum, "对账起期:" + infoVO.getStartDate()));
           ws.mergeCells(0, rowNum, CodeCst.NUMBER_THREE, rowNum);
           ws.setColumnView(0, CodeCst.NUMBER_ONE_HUNDRED);
           ws.addCell(new Label(CodeCst.NUMBER_FOUR, rowNum, "对账止期:" + infoVO.getEndDate()));
           ws.mergeCells(CodeCst.NUMBER_FOUR, rowNum, CodeCst.NUMBER_SEVEN, rowNum);
           ws.setColumnView(CodeCst.NUMBER_FOUR, CodeCst.NUMBER_ONE_HUNDRED);
       }
       maps.put("workAreaNum", workAreaNum);
       maps.put("rowNum", rowNum);
       maps.put("ws", ws);
       return maps;
   }
}
