package com.nci.tunan.cap.util;

import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;

import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 针对收付费系统常用格式转换，对格式转换进行更高层次的封装
 * 
 * <AUTHOR>
 * @date 2014-10-14
 * @version 0.0.1
 * @.belongToModule 收付费-收付费系统常用格式转换
 * 
 */
public class CapTransform {
    /** 
     * 日志类
     */ 
    private static Logger lOGGER = LoggerFactory.getLogger();

    /**
     * 字符串转换为时间
     * 
     * @param dateToTrans
     *            被转换的字符串
     * @param output
     *            被转换的时间
     * @param strict
     *            严格模式遇到任何问题，直接抛出异常
     * @return 返回转换后的时间
     * <AUTHOR>
     * @since 2014-10-14
     */
    public static java.sql.Date stringToDate(String dateToTrans, java.sql.Date output, boolean strict) {
        Date date = null;
        java.sql.Date sqlDate = null;
        date = stringToDate(dateToTrans, date, strict);
        if (null != date) {
            sqlDate = DateUtilsEx.toSqlDate(date);
        }
        return sqlDate;
    }

    /**
     * 字符串转换为时间
     * 
     * @param dateToTrans
     *            被转换的字符串
     * @param output
     *            被转换的时间
     * @param strict
     *            严格模式遇到任何问题直接抛出异常
     * @return 返回转换后的时间
     * <AUTHOR>
     * @since 2014-10-14
     */
    public static Date stringToDate(String dateToTrans, Date output, boolean strict) {
        boolean success = false;
        Date date = null;
        Pattern p = Pattern.compile("^\\d{4}-\\d{1,2}-\\d{1,2}$");
        Matcher m = p.matcher(dateToTrans);
        if (!success && m.matches()) {
            date = DateUtilsEx.formatToDate(dateToTrans, "yyyy-MM-dd");
            success = true;
        }

        if (!success && m.matches()) {
            p = Pattern.compile("^//d{4}-//d{1,2}-//d{1,2} //d{1,2}://d{1,2}://d{1,2}$");
            m = p.matcher(dateToTrans);
            date = DateUtilsEx.formatToDate(dateToTrans, "yyyy-MM-dd HH-mm-ss");
            success = true;

        }

        if (strict && !success) {
            lOGGER.error("not support string in this form of date!");
            throw new BizException("不支持该格式的时间转换");
        }
        return date;
    }

    /**
     * 通用类型转换
     * 
     * @param obj
     *            进行类型转换的对象
     * @param clazz
     *            类型转换的目标类型的class
     * @param <M>
     *            进行类型转换的对象类型
     * @param <D>
     *            类型转换的目标类型
     * @return 返回转换后的对象，类型不匹配返回null
     */
    public static <M, D> D generalTrans(M obj, Class<D> clazz) {
        return generalTrans(obj, clazz, false);
    }

    /**
     * 修复由于装箱产生的错误
     * 
     * @param clazz
     *            被修复的clazz
     * @return 修复结果
     */
    private static Class<?> replaceClazz(Class<?> clazz) {
        // 修复Integer引发的各类问题
        Class<?> newClazz = clazz;
        if (clazz == Integer.class) {
            newClazz = int.class;
        } else if (clazz == Float.class) {
            newClazz = float.class;
        } else if (clazz == Double.class) {
            newClazz = int.class;
        } else if (clazz == Long.class) {
            newClazz = long.class;
        }

        return newClazz;
    }

    /**
     * 通用类型转换
     * 
     * @param obj
     *            进行类型转换的对象
     * @param clazz
     *            类型转换的目标类型Class
     * @param <M>
     *            进行类型转换的对象类型
     * @param <D>
     *            进行类型转换的目标类型
     * @param strict
     *            严格模式，任何原因导致转换失败直接抛异常
     * @return 返回转换后的对象
     */
    @SuppressWarnings("unchecked")
    public static <M, D> D generalTrans(M obj, Class<D> clazz, boolean strict) {
        D returnValue = null;
        Class<?> originClazz;
        boolean flag = true;
        if (CapVerify.objIsNotNull(obj, true)) {
            originClazz = (Class<M>) obj.getClass();
            if (originClazz == clazz) {
                returnValue = (D) obj;
            } else if (clazz == Date.class && originClazz == String.class) {
                String dateToTrans = ((String) obj).trim();
                returnValue = (D) CapTransform.stringToDate(dateToTrans, (Date) returnValue, true);
            } else if (clazz == java.sql.Date.class && originClazz == String.class) {
                String dateToTrans = ((String) obj).trim();
                returnValue = (D) CapTransform.stringToDate(dateToTrans, (java.sql.Date) returnValue, true);

            } else {
                // 通用转换

                try {
                    originClazz = replaceClazz(originClazz);
                    returnValue = clazz.getConstructor(originClazz).newInstance(obj);
                } catch (Throwable e) {
                    flag = false;
                }
            }
        }
        if (strict && !CapVerify.objIsNotNull(returnValue, true)) {
            flag = false;
        }
        if (!flag) {
            lOGGER.error("get object from json failed!");
            throw new BizException("获取对象失败");
        }
        return returnValue;
    }

    /**
     * @description 根据日历的规则，为给定的日历字段添加或减去制定的时间量，public final static int YEAR =
     *              1;public final static int MONTH = 2;public final static int
     *              DAY_OF_MONTH = 5;
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param date
     *            使用给定的Date设置初始时间
     * @param field
     *            日历字段(Calendar.DAY_OF_MONTH/Calendar.MONTH/Calendar.YEAR)
     * @param amount
     *            为日历字段添加的日期或时间量
     * @return 返回添加或减去时间量后的Data对象
     */
    public static java.util.Date getMaximumDayInMonth(java.util.Date date, int field, int amount) {
        int tempField;
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        if (field == Calendar.DAY_OF_MONTH) {
            tempField = Calendar.DAY_OF_MONTH;
            calendar.add(tempField, amount);
        } else {
            if (field == Calendar.YEAR) {
                tempField = Calendar.YEAR;
            } else if (field == Calendar.MONTH) {
                tempField = Calendar.MONTH;
            } else {
                throw new BizException("超出日历字段处理范围(年:1,月:2,日:5)");
            }

            int currDay = calendar.get(Calendar.DAY_OF_MONTH);
            int currDays = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            if (currDay == currDays) {
                calendar.add(tempField, amount);
                int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                calendar.set(Calendar.DAY_OF_MONTH, lastDay);
            } else {
                calendar.add(tempField, amount);
            }
        }

        return calendar.getTime();
    }
    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param c 补齐字符
     * @param length  补齐后的字符长度
     * @param content 字符
     * @param bool  ture左补字符/false右补字符
     * @return
     */
    public static String flushChar(String c,long length,String content,boolean bool ){
        String str = "";
        for(int i = 0 ; i<length-content.length();i++){
            str+=c;
        }
        if(bool){
            str+=content;
        }else{
            content+=str;
        }
        return str.trim();
    }

}
