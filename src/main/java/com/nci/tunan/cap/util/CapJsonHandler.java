package com.nci.tunan.cap.util;

import java.util.Map;

import org.slf4j.Logger;

import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * json对象处理
 * 
 * <AUTHOR>
 * @version 0.0.1
 * @date 2014-10-13
 * @see CapTransform
 * @.belongToModule 收付费-json对象处理
 */
public class CapJsonHandler {
    /** 
     * 日志类
     */ 
    private static Logger lOGGER = LoggerFactory.getLogger();

    /**
     * 获取json中的特定对象
     * 
     * @param objs json转换而来的Map
     * @param param 键
     * @param clazz 返回类型
     * @param <T> json转换而来的Map的键类型
     * @param <M> json转换而来的Map的值类型
     * @param <D> 希望返回的对象类型
     * @return 返回转换后的对象
     * <AUTHOR>
     * @since 2014-10-13
     * @version 0.0.1
     * @return
     */
    public static <T, M, D> D getJsonObj(Map<T, M> objs, T param, Class<D> clazz) {
        return getJsonObj(objs, param, clazz, false);
    }

    /**
     * 获取json中的特定对象
     * 
     * @param objs
     *            json转换而来的Map
     * @param param
     *            键
     * @param <T>
     *            json转换而来的Map的键类型
     * @param <M>
     *            json转换而来的Map的值类型
     * @return 返回获取的值
     * <AUTHOR>
     * @since 2014-10-13
     * @version 0.0.1
     * @return
     */
    public static <T, M> M getJsonObj(Map<T, M> objs, T param) {
        return getJsonObj(objs, param, false);
    }

    /**
     * 获取json中的特定对象
     * 
     * @param objs
     *            json转换而来的Map
     * @param param
     *            键
     * @param strict
     *            是否开启严格验证，开启严格验证将直接抛出异常，不返回null
     * @param <T>
     *            json转换而来的Map的键类型
     * @param <M>
     *            json转换而来的Map的值类型
     * @return 返回获取的值
     * <AUTHOR>
     * @since 2014-10-13
     * @version 0.0.1
     * @return
     */
    public static <T, M> M getJsonObj(Map<T, M> objs, T param, boolean strict) {
        M obj = objs.get(param);
        if (strict) {
            if (!CapVerify.objIsNotNull(obj, true)) {
                lOGGER.error("get object from json failed!");
                throw new BizException("获取对象失败");
            }
        }
        return obj;
    }

    /**
     * 获取json中的特定对象
     * 
     * @param objs
     *            json转换而来的Map
     * @param param
     *            键
     * @param clazz
     *            返回类型
     * @param strict
     *            是否开启严格验证，开启严格验证将直接抛出异常，不返回null
     * @param <T>
     *            json转换而来的Map的键类型
     * @param <M>
     *            json转换而来的Map的值类型
     * @param <D>
     *            希望返回的对象类型
     * @return 返回获取的值
     * <AUTHOR>
     * @since 2014-10-13
     * @version 0.0.1
     * @return
     */
    public static <T, M, D> D getJsonObj(Map<T, M> objs, T param, Class<D> clazz, boolean strict) {
        M obj = objs.get(param);
        return CapTransform.generalTrans(obj, clazz, strict);
    }

}
