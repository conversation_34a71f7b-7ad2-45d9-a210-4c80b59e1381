package com.nci.tunan.cap.util;

import java.sql.SQLException;
import java.util.Date;
import java.util.UUID;

import com.nci.udmp.component.aop.TablePrevThreadLocal;
import com.nci.udmp.framework.consts.CharConst;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.generatekey.api.GenerateBizKeyUtil;
import com.nci.udmp.util.lang.DateUtilsEx;


/**
 * @description 获取实付号
 * <AUTHOR>
 * @date 2016-4-8 上午10:20:18
 * @.belongToModule 收付费-实收实付
 */
public class CapPayrefNoByTime {
	 /**
     * 数值
     */
    static int num = CodeCst.NUMBER_THOUSAND;
    /**
     * 数值1000
     */
    static int NUM = CodeCst.NUMBER_THOUSAND;
    /**
     * 数值9999
     */
    static final int MAX = 9999;
    /**
     * 数值1000
     */
    static final int MIN = 1000;
    /**
     * 生成实付号
     * @return
     */
    public static String getPayrefNoByTime() {
        String timeStr = DateUtilsEx.formatToString(new Date(), CharConst.FORMAT_STR_DATE_TIME_MILLISEC);
        timeStr = timeStr.substring(CodeCst.NUMBER_TWO, CodeCst.NUMBER_SEVENTEEN);
        if(num < MAX){
            num = num + 1;
        }else if(num == MAX) {
            num = MIN;
        }
        return timeStr + num;
    }

    /**
     * 获取主键值。
     *  
     * @return UUID
     */
    public static String getUUID() {
        String s = UUID.randomUUID().toString();
        return s.substring(0, CodeCst.NUMBER_EIGHT) + s.substring(CodeCst.NUMBER_NINE, CodeCst.NUMBER_THIRTEEN) 
                + s.substring(CodeCst.NUMBER_FOURTEEN, CodeCst.NUMBER_EIGHTEEN) + s.substring(CodeCst.NUMBER_NINETEEN, CodeCst.NUMBER_TWENTY_THREE) + s.substring(CodeCst.NUMBER_TWENTY_FOUR);
    }
    
    /**
     * 获得指定数目的UUID
     * 
     * @param number
     *             需要获得的UUID数量
     * @return String[] UUID数组
     */
    public static String[] getUUID(int number) {
        if (number < 1) {
            return null;
        }
        String[] ss = new String[number];
        for (int i = 0; i < number; i++) {
            ss[i] = getUUID();
        }
        return ss;
    }
    
    /**
     * @description 生成收付唯一号方法
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param  arapFlag 收付标志
     * @throws BizException
     * @throws SQLException 
     * @return String 收付唯一号
    */
    public static String createUnitNumber(String arapFlag) throws BizException, SQLException {
        String unitNumber = "";
        String date = DateUtilsEx.formatToString(WorkDateUtil.getWorkDate(),"yyyyMMdd");
        String renewalno = "CAP_RENEWALNO"+date;
        TablePrevThreadLocal.setTABLEPREV("APP___CAP__DBUSER.");
        if(CodeCst.AR_AP__AR.equals(arapFlag)) {
            unitNumber = CodeCst.SYSTEM_CODE__CAP_S +date+ GenerateBizKeyUtil.generateBizKeyCommon(renewalno);
        } else {
            unitNumber = CodeCst.SYSTEM_CODE__CAP_F +date+ GenerateBizKeyUtil.generateBizKeyCommon(renewalno);
        }
        return unitNumber;
        
    }

}
