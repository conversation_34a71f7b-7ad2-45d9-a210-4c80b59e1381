package com.nci.tunan.cap.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.sf.json.JSONObject;

import org.slf4j.Logger;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.tunan.cap.interfaces.model.bo.MedicareInfoBO;
import com.nci.tunan.cap.interfaces.model.vo.szyb.MedicareInfoVO;
import com.nci.tunan.cap.interfaces.model.vo.szyb.YB119InputVO;
import com.nci.tunan.cap.interfaces.model.vo.szyb.YB120InputVO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
/**
 * @description 医保对账批处理
 * @<NAME_EMAIL>
 * @date 2015-9-17 下午7:55:10
 * @.belongToModule 收付费-社保对账
 */
public class ManiMedicareInfoBatchJob extends AbstractBatchJobForMod {
	
    /**
     * 日志工具
     */
    protected Logger logger = LoggerFactory.getLogger();
    /**
	 * 对账文件读取批处理service
	 */
    private IManipulationInfoService manipulationInfoService;

    public IManipulationInfoService getManipulationInfoService() {
        return manipulationInfoService;
    }

    public void setManipulationInfoService(IManipulationInfoService manipulationInfoService) {
        this.manipulationInfoService = manipulationInfoService;
    }

    /**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {

    }
    /**
     * 获取id名字
     */
    @Override
    public String getIdName() {
        return null;
    }
    /**
     * 判断任务运行
     */
    @Override
    public boolean isCanBeRun() {
        return true;
    }
    /**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        jobSessionContext.setStartNum(1);
        jobSessionContext.setEndNum(1);
        jobSessionContext.setModNum(1);//批处理逻辑依赖关系需要
        return jobSessionContext;
    }


    /**
     1.获取文件(IP地址,端口号,用户名,密码，实时对账信息文件上传路径) ----------数据库t_payref_config配置
     * （单独配置） 2.根据调用接口的日期获取所要解析的文件 3.解析.txt文件（流读取） 4.根据文件内容格式 || 进行拆分 ,
     * 将拆分内容放入list<map>中 5.进行批量插入t_manipulation_info表中（如何预防重复插入）
     * @param jobSessionContext 任务会话上下文
     * @param start 起始位置
     * @param counts 查询数量
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
        
        try {
            List<JobParam> paramList = jobSessionContext.getParams();
            Date finishTime = DateUtilsEx.addDay(DateUtilsEx.getTodayDate(), -1);
            if (null != paramList) {
                String paramName;
                String paramValue;
                for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
                    JobParam param = iter.next();
                    logger.info("param.getParamName()=    " + param.getParamName());
                    logger.info("param.getParamCode()=    " + param.getParamCode());
                    logger.info("param.getParamType()=    " + param.getParamType());
                    logger.info("param.getParamValue()=    "
                            + param.getParamValue());
                    paramName = param.getParamName();
                    paramValue = param.getParamValue();
                    // 批处理启动参数指定日期，则生产文件从指定日期开始
                    if ("finishTime".equals(paramName)) {
                        if (CapVerify.objIsNotNull(paramValue, true)) {
                            finishTime = DateUtilsEx.formatToDate(paramValue, "yyyy-MM-dd");
                        }
                    }
                    
                }
            }
            if(!finishTime.after(DateUtilsEx.addDay(DateUtilsEx.getTodayDate(), -1))){
            toMedicareInfoBatch(finishTime);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<JobData> datas = new ArrayList<JobData>();
        return datas;
    }
    /**
     * 停止任务
     */
    @Override
    public void jobStop() {
        
    }
   
    /**
     * @description 批处理执行
     * @param arg0 任务会话上下文
     * @param arg1 参数列表
     * @param arg2 取模数值
     */
    @Override
    public List<JobData> execute(JobSessionContext arg0, List<JobData> arg1, int arg2) {
        return arg1;
    }
    /**
     * @description 批处理异常处理
     * @param arg0 任务会话上下文
     * @param arg1 任务数据参数
     */
    @Override
    public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
        
    }
    
    public void toMedicareInfoBatch(Date finishTime) {
        logger.info("医保对账文件读取批处理------开始");
        String tokenURL = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__MEDICARE, CodeCst.SZYB_TOKEN);
        String YB119URL = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__MEDICARE, CodeCst.SZYB_YB119);
        String YB120URL = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__MEDICARE, CodeCst.SZYB_YB120);
        String date = DateUtilsEx.formatToString(finishTime, "yyyyMMdd");
        String sysNum = "CAP075" + DateUtilsEx.formatToString(finishTime, "yyyyMMddHHmmss");
        String md5 = SZYLMD5Util.md5(sysNum);
        String jsonStr = "{\"SysCode\":\"A002\", \"SysNumber\":\"" + sysNum + "\", \"SysKey\":\"" + md5 + "\"}";
        logger.info("医保对账文件读取批处理------获取令牌入参：" + jsonStr);
        String resultStr = SZYLMD5Util.jsondoPost(tokenURL, jsonStr);
        logger.info("医保对账文件读取批处理------获取令牌结果：" + resultStr);
        JSONObject jsobj = JSONObject.fromObject(resultStr);
        String accessToken = jsobj.get("accessToken").toString(); // 获取令牌
        String cke100 = SZYLMD5Util.SZYB_GSDM + sysNum + SZYLMD5Util.randomStr();
        YB119InputVO vo = new YB119InputVO();
        vo.setBad766(date);
        vo.setAae011("111111"); // 操作员编码
        vo.setCke100(cke100); // 交易流水号
        vo.setAhb516(1); // 交易笔数
        vo.setAkb065(10.00); // 保单总费用
        JSONObject obj = new JSONObject();
        obj = obj.fromObject(vo);
        String objStr = obj.toString();
        logger.info("医保对账文件读取批处理------日终对账入参：" + objStr);
        String yb119Result = SZYLMD5Util.jsondoPost(YB119URL + accessToken, objStr);
        logger.info("医保对账文件读取批处理------日终对账结果：" + yb119Result);
        int pageno = 0;
        boolean isloop = true;
        while (isloop) {
            YB120InputVO yb120vo = new YB120InputVO();
            yb120vo.setBad766(date);
            yb120vo.setAae011("111111"); // 操作员编码
            yb120vo.setCke100(cke100); // 交易流水号
            yb120vo.setPageno(pageno);
            JSONObject obj1 = new JSONObject();
            obj1 = obj1.fromObject(yb120vo);
            String objStr1 = obj1.toString();
            logger.info("医保对账文件读取批处理------明细清单入参：" + objStr1);
            String yb120Result = SZYLMD5Util.jsondoPost(YB120URL + accessToken, objStr1);
            logger.info("医保对账文件读取批处理------明细清单结果：" + yb120Result);
            Map<String, List<MedicareInfoVO>> mapMedial = SZYLMD5Util.parseStr(yb120Result,
                    DateUtilsEx.formatToString(finishTime, "yyyy-MM-dd"), cke100);
            List<MedicareInfoVO> mediVO = new ArrayList<MedicareInfoVO>();
            Set<String> set = mapMedial.keySet();
            if (set.contains("1")) {
                isloop = false;
                mediVO = mapMedial.get("1");
            }
            if (set.contains("0")) {
                mediVO = mapMedial.get("0");
            }

            if (mediVO.size() > 0) {
                logger.info("医保对账文件读取批处理------明细清单数据保存");
                manipulationInfoService.batchSaveMedicareInfo(BeanUtils.copyList(MedicareInfoBO.class, mediVO));
            }
            pageno++;
        }
    }
}
