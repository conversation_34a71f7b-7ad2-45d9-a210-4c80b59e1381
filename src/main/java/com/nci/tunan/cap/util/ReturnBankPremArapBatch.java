package com.nci.tunan.cap.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import com.nci.tunan.cap.dao.IBankTextDao;
import com.nci.tunan.cap.impl.counterchagre.service.IReturnBankService;
import com.nci.tunan.cap.interfaces.model.po.BankTextPO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.util.lang.DateUtilsEx;

/** 
 * @description 返盘更新应收应付批处理
 * @<NAME_EMAIL> 
 * @date 2021-1-20 下午2:23:04 
 * @.belongToModule 收付费-返盘
 */
public class ReturnBankPremArapBatch extends AbstractBatchJobForMod {
    /** 
     * @Fields iBankTextDao :  盘文件dao
     */ 
    private IBankTextDao bankTextDao;
     
     /**
      * 返盘service
      */
    private static IReturnBankService returnBankService;
     
    public IBankTextDao getBankTextDao() {
        return bankTextDao;
    }
    public void setBankTextDao(IBankTextDao bankTextDao) {
        this.bankTextDao = bankTextDao;
    }

    public static IReturnBankService getReturnBankService() {
        return returnBankService;
    }

    public static void setReturnBankService(IReturnBankService returnBankService) {
        ReturnBankPremArapBatch.returnBankService = returnBankService;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext, java.util.List, int)
     * @param jobSessionContext
     * @param resultDatas
     * @param modNum
     * @return 
     */
    @Override
    public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> resultDatas, int modNum) {
        if(null != resultDatas && resultDatas.size()>0){
        	BankTextPO po = getBankTextPO(jobSessionContext);
            @SuppressWarnings("unchecked")
            List<BankTextPO> backList = (List<BankTextPO>)resultDatas.get(0).get("backTextList");
            for (BankTextPO bankTextPO : backList) {
                logger.info("返盘更新应收应付批处理===单个盘文件处理:{}",bankTextPO.getSendId());
                bankTextPO.setInt("commitNum", po.getInt("commitNum"));
                returnBankService.backTextUpdatePremArAp(bankTextPO);
            }
        
        }
        logger.info("返盘更新应收应付批处理===结束");
        return resultDatas;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
     * @param jobSessionContext
     * @param jobData 
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {

    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
     * @param jobSessionContext
     * @param jobData 
     */
    @Override
    public void jobErrorAudit(JobSessionContext jobSessionContext, JobData jobData) {

    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
     * @return 
     */
    @Override
    public String getIdName() {
        return "返盘更新应收应付批处理";
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
     * @return 
     */
    @Override
    public boolean isCanBeRun() {
        return true;
    }

    /**
     * @description 查询个数
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
     * @param jobSessionContext
     * @return 
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        BankTextPO bankTextPO = getBankTextPO(jobSessionContext);
        int backcount = bankTextDao.queryBackCount(bankTextPO);
        jobSessionContext.setTotalCnt((long)backcount);
        jobSessionContext.setModNum(1);
        return jobSessionContext;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#query(com.nci.udmp.component.batch.context.JobSessionContext, long, int)
     * @param jobSessionContext
     * @param start
     * @param counts
     * @return 
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
        logger.info("返盘更新应收应付批处理===开始");
        BankTextPO bankTextPO = getBankTextPO(jobSessionContext);
        bankTextPO.set("start", start);
        bankTextPO.set("modNum", counts);
        List<BankTextPO> backTextList = bankTextDao.findBankText(bankTextPO);
        List<BankTextPO> backTextListNew = new ArrayList<BankTextPO>();
        int bankTextNum =0;
        for (BankTextPO bankTextNPO : backTextList) {
        	if(bankTextPO.getInt("bankTextNum") > bankTextNum){
        	bankTextNum = bankTextNum + bankTextNPO.getSendCount().intValue();
        	backTextListNew.add(bankTextNPO);
        	} else {
        		break;
        	}
		}
        logger.info("返盘更新应收应付批处理===查询需要更新应收应付表盘文件总数:{}",backTextListNew.size());
        List<JobData> jobDataList = new ArrayList<JobData>();
        if(null != backTextListNew && backTextListNew.size()>0){
            JobData jobdata = new JobData();
            jobdata.set("backTextList", backTextListNew);
            jobDataList.add(jobdata);
            return jobDataList;
        }
        return null;
    }

    /**
     * @description
     * @version
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop() 
     */
    @Override
    public void jobStop() {

    }
    
    /**
     * @description 批处理对象转换盘文件对象
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param jobSessionContext 批处理入参
     * @return BankTextPO
    */
    public BankTextPO getBankTextPO(JobSessionContext jobSessionContext){
        List<JobParam> paramList = jobSessionContext.getParams();
        BankTextPO bankTextPO = new BankTextPO();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date backTextTime = DateUtilsEx.getTodayDate();
        int bankTextNum = CodeCst.NUMBER_ONE_HUNDRED_THOUSAND; //每个线程处理数量
        int commitNum = CodeCst.NUMBER_TWO_HUNDRED;
        Set<String> derivTypes = new HashSet<String>();
        if (null != paramList) {
            for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
                JobParam param = iter.next();
                logger.info("param.getParamName()= " + param.getParamName());
                logger.info("param.getParamCode()= " + param.getParamCode());
                logger.info("param.getParamType()= " + param.getParamType());
                logger.info("param.getParamValue()= " + param.getParamValue());
                String paramName = param.getParamName();
                String paramValue = param.getParamValue();
                if ("backTextTime".equals(paramName) && null!=paramValue && CapVerify.objIsNotNull(paramValue,true)) {
                    try {
                        backTextTime=sdf.parse(paramValue);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                if("commitNum".equals(paramName) && null!=paramValue && CapVerify.objIsNotNull(paramValue,true)){
                	commitNum = Integer.parseInt(paramValue);
                }
                if("bankTextNum".equals(paramName) && null!=paramValue && CapVerify.objIsNotNull(paramValue,true)){
                	bankTextNum = Integer.parseInt(paramValue);
                }
                if ("derivType".equals(paramName) && null!=paramValue && CapVerify.objIsNotNull(paramValue,true)) {
                	derivTypes.add(paramValue);
                   
                }
                bankTextPO.setBackTextTime(backTextTime);
                bankTextPO.setInt("commitNum", commitNum);
                bankTextPO.setInt("bankTextNum", bankTextNum);
                bankTextPO.set("derivTypes", derivTypes);
                bankTextPO.setBankTextStatus(CodeCst.BANK_TEXT_STATUS__BACK_UPLOADED);
            }
        }
        return bankTextPO;
    }

}
