package com.nci.tunan.cap.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.nci.tunan.cap.dao.IConstantsInfoDao;
import com.nci.tunan.cap.interfaces.model.po.ConstantsInfoPO;
import com.nci.udmp.framework.exception.FrameworkRuntimeException;
import com.nci.udmp.framework.spring.SpringContextUtils;
import com.nci.udmp.util.cache.CacheUtil;
/**
 * 
 * @description 收付费配置变量
 * <AUTHOR>
 * @date 2016-1-18 下午2:21:04
 * @.belongToModule 收付费-公共配置
 */
public class ConstantsInfoUtil {
    /**
     * 缓存key
     */
    public  static final String CAP_CONSTANTS_INFO= "cap_constants_info_key";
    /**
     * 支付类型
     */
    public static final String KEY__ACCOUNTING_ROUE = "ACCOUNTING_ROUE_CFG";
    
    
    public static IConstantsInfoDao constantsInfoDao = (IConstantsInfoDao) SpringContextUtils.getBean("CAP_constantsInfoDao");
    /**
     * 清除缓存方法
     */
    public static void clear() {
        CacheUtil.clear(ConstantsInfoUtil.CAP_CONSTANTS_INFO);
    }
    /**
     * 读取配置文件
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Map<String, String>> checkReload() {
        Map<String, Map<String, String>> map = null;
        boolean mark = false;
        try {
            map = (Map<String, Map<String, String>>) CacheUtil.get(CacheRegionName.CAP_MidtableCache.toString(), ConstantsInfoUtil.CAP_CONSTANTS_INFO);
        } catch (FrameworkRuntimeException e) {
            mark = true;
        }
        if (!mark && map != null) {
            return map;
        }
        mark = false;
        synchronized (ConstantsInfoUtil.class) {
            try {
                map = (Map<String, Map<String, String>>) CacheUtil.get(CacheRegionName.CAP_MidtableCache.toString(), ConstantsInfoUtil.CAP_CONSTANTS_INFO);
            } catch (FrameworkRuntimeException e) {
                mark = true;
            }
            if (!mark && map != null) {
                return map;
            }
            try {
                List<ConstantsInfoPO> lst = constantsInfoDao.findConstantsInfoByKeyValue("", "");
                map = new HashMap<String, Map<String, String>>();
                for (ConstantsInfoPO po : lst) {
                    if(map.containsKey(po.getConstantsKey())){
                        map.get(po.getConstantsKey()).put(po.getConstantsValue(),po.getConstantsDesc());
                    }else{
                        Map<String, String> innermap = new HashMap<String, String>();
                        innermap.put(po.getConstantsValue(), po.getConstantsDesc());
                        map.put(po.getConstantsKey(), innermap);
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            CacheUtil.put(CacheRegionName.CAP_MidtableCache.toString(), ConstantsInfoUtil.CAP_CONSTANTS_INFO, map);
        }
        return map;
    }
    /**
     * 重新加载配置文件
     * @return
     */
    public static Map<String, Map<String, String>> reload() {
        Map<String, Map<String, String>> map = null;
        synchronized (ConstantsInfoUtil.class) {
            CacheUtil.clear(ConstantsInfoUtil.CAP_CONSTANTS_INFO);
            try {
                List<ConstantsInfoPO> lst = constantsInfoDao.findConstantsInfoByKeyValue("", "");
                map = new HashMap<String, Map<String, String>>();
                for (ConstantsInfoPO po : lst) {
                    if(map.containsKey(po.getConstantsKey())){
                        map.get(po.getConstantsKey()).put(po.getConstantsValue(), po.getConstantsDesc());
                    }else{
                        Map<String, String> innermap = new HashMap<String, String>();
                        innermap.put(po.getConstantsValue(), po.getConstantsDesc());
                        map.put(po.getConstantsKey(), innermap);
                    }
                }
                CacheUtil.put(CacheRegionName.CAP_MidtableCache.toString(), ConstantsInfoUtil.CAP_CONSTANTS_INFO, map);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return map;
    }
    
   /* *//**
     * @description 根据类型和code查询value值
     * @version 0.0.1
     * @title getValueByTypeCode
     * @<NAME_EMAIL>
     * @param type 类型
     * @param code code 编码
     * @return value
    *//*
    public static String getByKeyValue(String key, String value){
        Map<String, Map<String, String>> map = checkReload();
        if(map.containsKey(type)){
            if(map.get(type).containsKey(code)){
                return map.get(type).get(code);
            }
        }
        return null;
    }*/
    
    /**
     * @description 根据key获取所有value信息
     * @version 0.0.1
     * @title getValueByTypeCode
     * @<NAME_EMAIL>
     * @param type 类型
     * @return map
    */
    public static Map<String, String> getValuesByKey(String key){
        Map<String, Map<String, String>> map = checkReload();
        if(map.containsKey(key)){
            return map.get(key);
        }
        return null;
    }
    
}
