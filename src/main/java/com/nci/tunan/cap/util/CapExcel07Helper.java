package com.nci.tunan.cap.util;

import java.util.ArrayList;
import java.util.List;

import jxl.write.WriteException;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

public class CapExcel07Helper {
    /**
     * 表格样式:字体默认  上下左右边框黑线
     */
    CellStyle textStyle;
    /**
     * 数字样式
     */
    CellStyle numStyle;
    
    CellStyle headerStyle;
    
    CellStyle titleStyle;
    
    /** 
    * @Fields sheet : sheet页
    */ 
    Sheet sheet;
    
    /** 
    * @Fields rows : 已创建行集合
    */ 
    List<Integer> rows;
    /** 
     * <p>Title: 构造函数</p> 
     * <p>Description: 构造函数</p> 
     * @param xlsx excel对象
     * @param wsName sheet页名字
    */
    public CapExcel07Helper(SXSSFWorkbook xlsx,String wsName) {
        this.textStyle= createTextStyle(xlsx);
        this.numStyle = createNumStyle(xlsx);
        this.headerStyle = createHeaderStyle(xlsx);
        this.titleStyle = createTitleStyle(xlsx);
        this.sheet = xlsx.createSheet(wsName);
        this.rows = new ArrayList<Integer>();
    }

    public CellStyle getTextStyle() {
        return textStyle;
    }

    public void setTextStyle(CellStyle textStyle) {
        this.textStyle = textStyle;
    }

    public CellStyle getNumStyle() {
        return numStyle;
    }

    public void setNumStyle(CellStyle numStyle) {
        this.numStyle = numStyle;
    }

    public CellStyle getHeaderStyle() {
        return headerStyle;
    }

    public void setHeaderStyle(CellStyle headerStyle) {
        this.headerStyle = headerStyle;
    }

    public Sheet getSheet() {
        return sheet;
    }

    public void setSheet(Sheet sheet) {
        this.sheet = sheet;
    }

    public CellStyle getTitleStyle() {
        return titleStyle;
    }

    public void setTitleStyle(CellStyle titleStyle) {
        this.titleStyle = titleStyle;
    }

    /**
     * @description 样式:字体默认  上下左右边框黑线
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param xlsx xlsx对象
     * @return CellStyle
    */
    private CellStyle createTextStyle(SXSSFWorkbook xlsx) {
        CellStyle tableStyle = xlsx.createCellStyle(); //@invalid  table框格式
        tableStyle.setBorderBottom(CellStyle.BORDER_THIN);//@invalid  下边框
        tableStyle.setBorderLeft(CellStyle.BORDER_THIN);//@invalid  左边框
        tableStyle.setBorderRight(CellStyle.BORDER_THIN);//@invalid  右边框
        tableStyle.setBorderTop(CellStyle.BORDER_THIN);//@invalid  上边框
        return tableStyle;
    }
    /**
     * @description 样式:金额数字格式化为货币  上下左右边框黑线
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param xlsx xlsx对象
     * @return CellStyle
    */
    private CellStyle createNumStyle(SXSSFWorkbook xlsx) {
        CellStyle numStyle = xlsx.createCellStyle(); //@invalid  table框格式
        numStyle.setBorderBottom(CellStyle.BORDER_THIN);//@invalid  下边框
        numStyle.setBorderLeft(CellStyle.BORDER_THIN);//@invalid  左边框
        numStyle.setBorderRight(CellStyle.BORDER_THIN);//@invalid  右边框
        numStyle.setBorderTop(CellStyle.BORDER_THIN);//@invalid  上边框
        DataFormat dataFormat = xlsx.createDataFormat();//@invalid  数字格式化
        numStyle.setDataFormat(dataFormat.getFormat("#,##0.00;[RED]\\-#,##0.00"));//@invalid  数字格式化
        return numStyle;
    }
    
    /**
     * @description 列表头框样式:上下左右边框,水平居中,字体加粗
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param xlsx xlsx对象
     * @return CellStyle 
    */
    private CellStyle createHeaderStyle(SXSSFWorkbook xlsx){
        CellStyle headerStyle = xlsx.createCellStyle(); //@invalid 表头框格式
        headerStyle.setBorderBottom(CellStyle.BORDER_THIN);//@invalid 下边框
        headerStyle.setBorderLeft(CellStyle.BORDER_THIN);//@invalid 左边框
        headerStyle.setBorderRight(CellStyle.BORDER_THIN);//@invalid 右边框
        headerStyle.setBorderTop(CellStyle.BORDER_THIN);//@invalid 上边框
        headerStyle.setAlignment(CellStyle.ALIGN_CENTER);//@invalid 水平居中
        Font headerFont = xlsx.createFont(); //@invalid 设置字体样式
        headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);//@invalid 设置字体加粗
        headerStyle.setFont(headerFont);
        return headerStyle;
    }
    
    /**
     * @description 大标题样式：上下左右边框加粗,水平居中,字体加粗
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param xlsx xlsx对象
     * @return CellStyle
    */
    private CellStyle createTitleStyle(SXSSFWorkbook xlsx) {
        Font fontTitle = xlsx.createFont(); // @invalid 设置字体样式
        fontTitle.setFontHeightInPoints((short) CodeCst.NUMBER_FOURTEEN);// @invalid 设置字体大小
        fontTitle.setFontName("宋体"); // @invalid 设置字体名称
        fontTitle.setBoldweight(Font.BOLDWEIGHT_BOLD); // @invalid 设置字体加粗
        CellStyle titleStyle = xlsx.createCellStyle();
        titleStyle.setFont(fontTitle);
        titleStyle.setAlignment(CellStyle.ALIGN_CENTER);// @invalid 水平居中
        titleStyle.setVerticalAlignment(CellStyle.ALIGN_CENTER);// @invalid 垂直居中
        return titleStyle;
    }
    
    /**
     * @description 写入文本格式
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param labelName
     * @param x 列号
     * @param y 行号
     * @param width 宽度,最小为1
     * @param height 高度,最小为1
     * @param style 字体样式
    */
    public void writeLabel07(String labelName, int x, int y, int width, int height,CellStyle style) {
        if ((x >= 0) && (y >= 0) && (width > 0) && (height > 0)) {
            Row row;
            if(rows.contains(y)){
              row = sheet.getRow(y);
            }else{
              row = sheet.createRow(y);
              rows.add(y);
            }
            Cell cell = row.createCell(x);
            cell.setCellValue(labelName);
            if (null != style) {
                cell.setCellStyle(style);
            }
            if ((width != 1) || (height != 1)) {
                sheet.addMergedRegion(new CellRangeAddress(y, (y + height) - 1,x, (x + width) - 1));
            }
        }
    }
    
    /**
     * @description 写入数字格式
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param labelName
     * @param x 行
     * @param y 列
     * @param width 宽度,最小为1
     * @param height 高度,最小为1
     * @param style 字体样式
    */
    public void writeNumber07(double numberData, int x, int y, int width, int height,CellStyle style) {
        if ((x >= 0) && (y >= 0) && (width > 0) && (height > 0)) {
            Row row;
            if(rows.contains(y)){
              row = sheet.getRow(y);
            }else{
              row = sheet.createRow(y);
              rows.add(y);
            }
            Cell cell = row.createCell(x);
            cell.setCellValue(numberData);
            cell.setCellStyle(style);
            if ((width != 1) || (height != 1)) {
                sheet.addMergedRegion(new CellRangeAddress(y, (y + height) - 1,x, (x + width) - 1));
            }
        }
    }

    /**
     * @description 不传入字体格式,无格式
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param labelName
     * @param x 行
     * @param y 列
     * @param width 宽度,最小为1
     * @param height 高度,最小为1
    */
    public void writeLabel07(String labelName, int x, int y, int width, int height) {
        writeLabel07(labelName,x,y,width,height,null);
    }
    
    /**
     * @description 设置列宽
     * @version 1.0
     * @title 设置列宽
     * @<NAME_EMAIL>
     * @param num
     *            列在sheet页中的索引
     * @param width
     *            ,列的宽度
     * @throws WriteException
     *             无法写入异常
     */
    public void setColumnWidth(int num, int width) {
        if ((num >= 0) && (width > 0)) {
            sheet.setColumnWidth(num, width * CodeCst.NUMBER_TWO_HUNDRED_NINETY);
        }
    }
    /**
     * 设置自动列宽
     * @param num 列在sheet页中的索引
     * @throws WriteException
     */
    public void setColumnWidthAuto(int num) throws WriteException {
        if ((num >= 0)) {
            sheet.autoSizeColumn(num);
            int width = Math.max(
                    CodeCst.NUMBER_FIFTEEN * CodeCst.NUMBER_TWO_HUNDRED_FIVETY_SIX,
                    Math.min(CodeCst.NUMBER_TWO_HUNDRED_FIVETY_FIVE * CodeCst.NUMBER_TWO_HUNDRED_FIVETY_SIX,
                            sheet.getColumnWidth(num) * CodeCst.NUMBER_TWELVE / CodeCst.NUMBER_TEN));
            sheet.setColumnWidth(num, width);
        }
    }
}
