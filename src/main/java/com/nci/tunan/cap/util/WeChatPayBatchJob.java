package com.nci.tunan.cap.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.slf4j.Logger;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationInfoBO;
import com.nci.tunan.cap.interfaces.model.vo.ManipulationInfoVO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.ftp.FtpUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

public class WeChatPayBatchJob extends AbstractBatchJobForMod {
    /**
     * 日志工具
     */
    protected Logger logger = LoggerFactory.getLogger();
	/**
	 * 实时对账文件读取批处理service
	 */
    private IManipulationInfoService manipulationInfoService;

	public IManipulationInfoService getManipulationInfoService() {
		return manipulationInfoService;
	}
	 
	public void setManipulationInfoService(
			IManipulationInfoService manipulationInfoService) {
		this.manipulationInfoService = manipulationInfoService;
	}

	/**
     * @description 批处理执行
     * @param jobSessionContext 任务会话上下文
     * @param resultDatas 参数列表
     * @param modNum 取模数值
     */
	@Override
	public List<JobData> execute(JobSessionContext jobSessionContext,
			List<JobData> resultDatas, int modNum) {
		return resultDatas;
	}

	/**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData jobData) {
		logger.debug("------------------write------------------------");
		List<JobParam> paramList = jobSessionContext.getParams();
		Date enterAccDate = DateUtilsEx.getTodayDate();
		int minusDays = CodeCst.NUMBER_ZERO;
		if (null != paramList) {
			String paramName;
			String paramValue;
			for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
				JobParam param = iter.next();
				logger.info("param.getParamName()=    " + param.getParamName());
				logger.info("param.getParamCode()=    " + param.getParamCode());
				logger.info("param.getParamType()=    " + param.getParamType());
				logger.info("param.getParamValue()=    "
						+ param.getParamValue());
				paramName = param.getParamName();
				paramValue = param.getParamValue();
				// 批处理启动参数指定日期，则生产文件从指定日期开始
				if ("enterAccDate".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
						enterAccDate = DateUtilsEx.formatToDate(paramValue, "");
						enterAccDate = DateUtilsEx.addDay(enterAccDate, 1);
					}
				}
				if ("minusDays".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
						minusDays = Integer.parseInt(paramValue);
					}
				}
			}
			for (int i = 0; i <= minusDays; i++) {
				String date	=DateUtilsEx.formatToString(enterAccDate,"YYYYMMdd");
				enterAccDate = DateUtilsEx.addDay(enterAccDate, -1);
				this.toReadFile(date);
			}
			
		}
		
	}
	/**
     * @description 批处理异常处理
     * @param arg0 任务会话上下文
     * @param arg1 任务数据参数
     */
	@Override
	public void jobErrorAudit(JobSessionContext jobSessionContext,
			JobData jobData) {
	}
	/**
     * 获取id名字
     */
	@Override
	public String getIdName() {
		return null;
	}
	/**
     * 判断任务运行
     */
	@Override
	public boolean isCanBeRun() {
		 return true;
	}
	/**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		    jobSessionContext.setStartNum(1);
	        jobSessionContext.setEndNum(1);
	        jobSessionContext.setModNum(1);//批处理逻辑依赖关系需要
	    return jobSessionContext;
	}
	  /**
    1.获取文件(IP地址,端口号,用户名,密码，实时对账信息文件上传路径) ----------数据库t_payref_config配置
    * （单独配置） 2.根据调用接口的日期获取所要解析的文件 3.解析.txt文件（流读取） 4.根据文件内容格式 || 进行拆分 ,
    * 将拆分内容放入list<map>中 5.进行批量插入t_manipulation_info表中（如何预防重复插入）
    * @param jobSessionContext 任务会话上下文
    * @param start 起始位置
    * @param counts 查询数量
    */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start,
			int counts) {
		logger.debug("---------------query-------------------------");
		List<JobData> datas = new ArrayList<JobData>();
		JobData data = new JobData();
		datas.add(data);
		return datas;
	}
	/**
     * 停止任务
     */
	@Override
	public void jobStop() {
	}
	/**   
    * @description 1.连接核心服务器，获取微信推送文件。2.解析文件，保存数据到对账表中。3.返回解析文件列表
    * @param Date 对账日期
    * @param minusDays 对账期间
    */
	private void toReadFile(String date){
		logger.debug("------------------toReadFile------------------------");
		try {
		FTPClient ftpClient = this.getFTPClient();
		InputStream inputStream = null;
		BufferedReader reader = null;

		if(ftpClient!=null){
			try {
				//读取数据的ftp服务器的对账目录
				String reconciliationRead ="/cap/tms/reconciliation/weChatRead/";
				 //备份文件袋ftp服务器的对账目录
			    String reconciliationbackups ="/cap/tms/reconciliation/weChatBackups/";
	            
			    ftpClient.changeWorkingDirectory(reconciliationRead);
	            
				FTPFile[] file = ftpClient.listFiles();
				
				List<ManipulationInfoBO>  infoBoList = new ArrayList<ManipulationInfoBO>();
				
				for (int i = 0; i < file.length; i++) {
					String[] str = file[i].getName().split("\\.");
					if("txt".equals(str[str.length - 1])){
						inputStream = ftpClient.retrieveFileStream(file[i].getName());
	                    reader = new BufferedReader(new InputStreamReader(inputStream));

                    	String lineTxt = null;
   	                    //读取每一行数据
   	                    while((lineTxt = reader.readLine())!=null){
   	                    	if("".equals(lineTxt)){
   	                    		break;
   	                    	}
   	                    	//根据 , 进行拆分数据
   	                    	String[] strArr =lineTxt.split("\\|");
   	                    	Map<Integer, String> maps = new HashMap<Integer,String>();
   	                    	for (int s = 0; s < strArr.length; s++) {
   								maps.put(s,strArr[s].toString());
   							}
   	                    	infoBoList.add(getManipulationInfoWX(maps));
   	                    }
   	                 ftpClient.getReply();
					}												
				}
				logger.debug("-----文件备份到核心服务器------"+reconciliationbackups);
				//备份文件到核心服务器	
				Date d = WorkDateUtil.getWorkDate();
				String dateNowStr = DateUtilsEx.formatToString(d, "yyyy-MM-dd");
				String[] dateStr = dateNowStr.split("\\-");
				reconciliationbackups = reconciliationbackups.concat(dateStr[0]);
				ftpClient.makeDirectory(reconciliationbackups);
				reconciliationbackups = reconciliationbackups.concat("/").concat(dateStr[1]);
				ftpClient.makeDirectory(reconciliationbackups);
				reconciliationbackups = reconciliationbackups.concat("/").concat(dateStr[2]);
				ftpClient.makeDirectory(reconciliationbackups);
				ftpClient.makeDirectory(reconciliationbackups);
				
				for (int i = 0; i < file.length; i++) { 
				    //@invalid 在备份文件夹中增加文件，删除原读取文件夹中文件
				    ftpClient.rename(file[i].getName(), reconciliationbackups.concat("/").concat(file[i].getName()));
				    ftpClient.deleteFile(reconciliationRead.concat(file[i].getName()));
				}
				List<ManipulationInfoBO> saveInfoBOList = new ArrayList<ManipulationInfoBO>();
				//因ftp目录中对账文件存在重复，故先判断该笔一笔交易唯一批次号是否存在，不存在才添加
				ManipulationInfoBO batchIDBO = null;
				
				List<String> batchID = new ArrayList<String>();
				for (ManipulationInfoBO bo : infoBoList) {
	                if(!batchID.contains(bo.getBatchId())){
	                    batchIDBO = new ManipulationInfoBO();
	                    batchIDBO.setBatchId(bo.getBatchId());
	                    batchIDBO.setArapFlag(bo.getArapFlag());
	                    int num = manipulationInfoService.findManipulationInfoTotal(batchIDBO);
	                    if(num == 0){
	                    	saveInfoBOList.add(bo);
	                        batchID.add(bo.getBatchId());
	                    }else{
	                        batchID.add(bo.getBatchId());
	                    }
	                }
	            }
				manipulationInfoService.batchSaveManipulationWeChat(saveInfoBOList);
				
			} catch (IOException eIO) {
                eIO.printStackTrace();
                logger.info(eIO.getMessage());
                throw eIO;
            } catch (ParseException ePa) {
                ePa.printStackTrace();
                logger.info(ePa.getMessage());
                throw ePa;
            } catch (Exception e) {
                e.printStackTrace();
                logger.info(e.getMessage());
                throw e;
			}
		}
		if(reader!=null){
		    reader.close();
		}
		if(inputStream!=null){
			inputStream.close();
		}
		FtpUtil.logoutFtpServer(ftpClient);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @description 核心FTP服务器
	 * @param  参数
	 * @return 
	 * @throws IOException 
	*/
	private FTPClient getFTPClient() throws IOException {
		try {
		    String serverIp = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.SMBFILE_SERVER_IP);
	        String ftpServerPort = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP,CodeCst.FTP_SERVER_PORT);
	        String ftpUserName = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_USER_NAME);
	        String ftpPassword = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_PASSWORD);
	        FtpServerConfig ftpServerConfig = new FtpServerConfig();
	        ftpServerConfig.setFtpServerIp(serverIp);
            ftpServerConfig.setFtpServerPort(Integer.parseInt(ftpServerPort));
            ftpServerConfig.setFtpUserName(ftpUserName);
            ftpServerConfig.setFtpPassword(ftpPassword);
            FTPClient ftpClient = FtpUtil.loginFtpServer(ftpServerConfig);
			ftpClient.setControlEncoding("GBK"); // 设置格式
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
			ftpClient.enterLocalPassiveMode();// 设置被动模式
			return ftpClient;
		} catch (IOException e) {
			throw e;
		}
	}
	
	
	/**
	 * @description 微信支付数据封装到BO对象列表中
	 * @param maps 数据集合
	 * @param @return 参数
	*/
	private ManipulationInfoBO getManipulationInfoWX(Map<Integer, String> maps) throws ParseException{
		ManipulationInfoVO infoVo = new ManipulationInfoVO();
		infoVo.setServicerCode(maps.get(CodeCst.NUMBER_ZERO).toString());
		infoVo.setTerminalCode(maps.get(CodeCst.NUMBER_ONE).toString());
		infoVo.setMerchantId(maps.get(CodeCst.NUMBER_THREE).toString());
		Date parse = new SimpleDateFormat("yyyyMMdd").parse(maps.get(CodeCst.NUMBER_FOUR).toString());
		infoVo.setCompareAccDate(parse);
		infoVo.setBatchId(maps.get(CodeCst.NUMBER_FIVE).toString());
		infoVo.setChargeType(maps.get(CodeCst.NUMBER_SIX).toString());
		if("08".equals(infoVo.getChargeType())){ //08-微信收费，09-微信原路退款
		    infoVo.setArapFlag(CodeCst.AR_AP__AR);
		}
		if("09".equals(infoVo.getChargeType())){ //08-微信收费，09-微信原路退款
            infoVo.setArapFlag(CodeCst.AR_AP__AP);
        }
		infoVo.setCertifyType(maps.get(CodeCst.NUMBER_SEVEN).toString());
		infoVo.setPayType(CodeCst.PAY_MODE_WECHAT);
		//保存paymentCode
		infoVo.setTradeSn(maps.get(CodeCst.NUMBER_EIGHT).toString());
		infoVo.setAccountName(maps.get(CodeCst.NUMBER_NINE).toString());
		infoVo.setAccountNo(maps.get(CodeCst.NUMBER_TEN).toString());
		infoVo.setBankCode(maps.get(CodeCst.NUMBER_ELEVEN).toString());
		Date parse1 = new SimpleDateFormat("yyyyMMddHHmmss").parse(maps.get(CodeCst.NUMBER_TWELVE).toString());
		infoVo.setEnterAccDate(parse);
		infoVo.setEnterAccTime(parse1);
		infoVo.setPremium(new BigDecimal(maps.get(CodeCst.NUMBER_THIRTEEN).toString()));
		infoVo.setPoundage(new BigDecimal(maps.get(CodeCst.NUMBER_FOURTEEN).toString()));
		infoVo.setReturnCode(maps.get(CodeCst.NUMBER_FIFTEEN).toString());
		infoVo.setReturnMsg(maps.get(CodeCst.NUMBER_SIXTEEN).toString());
		infoVo.setUserCode("DBDK");
		infoVo.setNoteCode(maps.get(CodeCst.NUMBER_SEVENTEEN).toString());
		ManipulationInfoBO infoBO = BeanUtils.copyProperties(ManipulationInfoBO.class, infoVo);
		return infoBO;
	}
}
