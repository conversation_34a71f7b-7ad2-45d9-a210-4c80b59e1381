package com.nci.tunan.cap.util;

import java.util.List;

import org.aspectj.lang.JoinPoint;

import com.nci.udmp.framework.consts.PubConstants;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.model.BasePO;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * 操作数据库时对insert_by等字段赋值
 * 
 * <AUTHOR>
 * @version 0.0.1
 * @.belongToModule 收付费-更新审计字段
 * @date 2015-12-11 上午10:27:46
 */
public class EnhanceDao {
    /**
     * 更新时插入update_by 等字段
     * 
     * @param jp 要更新的PO
     */
    public void beforUpdate(JoinPoint jp) {
        Object[] args = jp.getArgs();
        if (args != null && args.length > 1 && args[1] instanceof BasePO) {
            BasePO po = (BasePO) args[1];
            if (null != po.getData()) { // 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
            }

        }
    }

    /**
     * 批量更新时为update_by等赋值
     * 
     * @param jp 要更新的PO
     */
    public void beforBatchUpdate(JoinPoint jp) {
        Object[] args = jp.getArgs();
        if (args != null && args.length > 1 && args[1] instanceof List) {
            List<BasePO> poList = (List<BasePO>) args[1];

            for (BasePO po : poList) {
                if (null != po.getData()) { // 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                    po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                    po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                }
            }

        }
    }

    /**
     * 单条插入时为insert_by等字段赋值
     * 
     * @param jp 要插入的PO
     */
    public void beforInsert(JoinPoint jp) {
        Object[] args = jp.getArgs();
        if (args != null && args.length > 1 && args[1] instanceof BasePO) {
            BasePO po = (BasePO) args[1];
            if (null != po.getData()) { // 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                po.getData().put(PubConstants.INSERT_BY, AppUserContext.getCurrentUser().getUserId());
            }
        } else if (args != null && args.length > 0 && args[0] instanceof BasePO) {
            BasePO po = (BasePO) args[1];
            if (null != po.getData()) { //@invalid 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                po.getData().put(PubConstants.INSERT_BY, AppUserContext.getCurrentUser().getUserId());
            }
        }
    }

    /**
     * 批量插入时为insert_by等字段赋值
     * 
     * @param jp 要插入的PO
     */
    public void beforBatchInsert(JoinPoint jp) {
        Object[] args = jp.getArgs();
        if (args != null && args.length > 1 && args[1] instanceof List) {
            List<BasePO> poList = (List<BasePO>) args[1];

            for (BasePO po : poList) {
                if (null != po.getData()) { //@invalid 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                    po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                    po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                    po.getData().put(PubConstants.INSERT_BY, AppUserContext.getCurrentUser().getUserId());

                }
            }

        } else if (args != null && args.length > 0 && args[0] instanceof List) {
            List<BasePO> poList = (List<BasePO>) args[0];

            for (BasePO po : poList) {
                if (null != po.getData()) { //@invalid 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                    po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                    po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                    po.getData().put(PubConstants.INSERT_BY, AppUserContext.getCurrentUser().getUserId());

                }
            }

        }

        if (args != null && args.length > 1 && args[1] instanceof BasePO) {
            BasePO po = (BasePO) args[1];
            if (null != po.getData()) { // 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                po.getData().put(PubConstants.INSERT_BY, AppUserContext.getCurrentUser().getUserId());
            }
        } else if (args != null && args.length > 0 && args[0] instanceof BasePO) {
            BasePO po = (BasePO) args[0];
            if (null != po.getData()) { //@invalid 为查询对象所对应的表中update_time,update_timestamp插入默认的值
                po.getData().put(PubConstants.UPDATE_BY, AppUserContext.getCurrentUser().getUserId());
                po.getData().put(PubConstants.UPDATE_TIME, DateUtilsEx.getTodayTime());
                po.getData().put(PubConstants.INSERT_BY, AppUserContext.getCurrentUser().getUserId());
            }
        }
    }

}
