package com.nci.tunan.cap.util;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.List;

import jxl.Sheet;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.VerticalAlignment;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import com.nci.tunan.cap.interfaces.model.bo.statement.SatementVarianceBO;

/**
 * 
 * @description 日结报表excel文件追加差异说明工具类
 * @<NAME_EMAIL> 
 * @date 2024-10-17 上午10:03:04
 */
public class ExcelAppender {

    /**
     * @Fields fmt : WritableCellFormat对象
     */
    private static WritableCellFormat fmtTWL1;
    private static WritableCellFormat fmtTWL2;
    private static WritableCellFormat fmtTWL3;

    WritableSheet wb;

    /**
     * 
     * <p>Title:excel工具初始化</p> 
     * <p>Description: </p> 
     * @throws WriteException
     */
    public ExcelAppender() throws WriteException {
        WritableFont thinFont = null;
        thinFont = new WritableFont(WritableFont.ARIAL);
        thinFont.setPointSize(CodeCst.NUMBER_TEN);
        fmtTWL1 = new WritableCellFormat(thinFont);
        fmtTWL1.setAlignment(Alignment.CENTRE);
        fmtTWL1.setWrap(true);
        fmtTWL1.setVerticalAlignment(VerticalAlignment.CENTRE);
        fmtTWL1.setBorder(Border.ALL, BorderLineStyle.THIN);

        fmtTWL2 = new WritableCellFormat(thinFont);
        fmtTWL2.setAlignment(Alignment.LEFT);
        fmtTWL2.setWrap(true);
        fmtTWL2.setVerticalAlignment(VerticalAlignment.CENTRE);

        fmtTWL3 = new WritableCellFormat(thinFont);
        fmtTWL3.setAlignment(Alignment.CENTRE);
        fmtTWL3.setWrap(true);
        fmtTWL3.setVerticalAlignment(VerticalAlignment.CENTRE);
    }

    /**
     * 
     * @description 向日结报表文件中追加差异说明部分
     * @version
     * @title
     * <AUTHOR>
     * @param excelPath
     * @param appder
     * @param reviewUser
     * @param tableUser
     * @throws BiffException
     * @throws WriteException
     */
    public void excelAppender(String excelPath, List<SatementVarianceBO> appder, String reviewUser, String tableUser)
            throws BiffException, WriteException {
        try {
            File excel = new File(excelPath);
            Workbook book = Workbook.getWorkbook(excel);
            Sheet sheet = book.getSheet(0);
            int rows = sheet.getRows();
            WritableWorkbook workbook = Workbook.createWorkbook(excel, book);
            wb = workbook.getSheet(0);
            ReportRowColNum index = new ReportRowColNum();
            DecimalFormat df = new DecimalFormat("0.00");
            index.row = rows++;
            index.col = 0;
            writeLabel("制表", 0, index.row, fmtTWL2);
            writeLabel(tableUser, 1, index.row++, fmtTWL2);

            if (appder != null && appder.size() > 0) {
                writeLabel("", index.col, index.row++, fmtTWL3);
                writeHeader("差异说明", index.col, index.row++, CodeCst.NUMBER_SIX, 1, fmtTWL3);
                writeLabel("序号", index.col++, index.row, fmtTWL1);
                writeLabel("日结金额", index.col++, index.row, fmtTWL1);
                writeLabel("实际业务金额", index.col++, index.row, fmtTWL1);
                writeLabel("涉及业务号", index.col++, index.row, fmtTWL1);
                writeLabel("涉及的险种", index.col++, index.row, fmtTWL1);
                writeLabel("差错详细描述", index.col++, index.row++, fmtTWL1);
                index.col = 0;

                for (SatementVarianceBO bo : appder) {
                    writeLabel(bo.getSerialNum().toString(), index.col++, index.row, fmtTWL1);
                    writeLabel(df.format(bo.getFeeAmount()), index.col++, index.row, fmtTWL1);
                    writeLabel(df.format(bo.getActAmount()), index.col++, index.row, fmtTWL1);
                    writeLabel(bo.getBusinessCode(), index.col++, index.row, fmtTWL1);
                    writeLabel(bo.getBusiProdCode(), index.col++, index.row, fmtTWL1);
                    writeLabel(bo.getErrDesc(), index.col++, index.row++, fmtTWL1);
                    index.col = 0;
                }
            }
            writeLabel("复核", 0, index.row, fmtTWL2);
            writeLabel(reviewUser, 1, index.row, fmtTWL2);
            if (workbook != null) {
                workbook.write();
                workbook.close();
            }
            if (book != null) {
                book.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * @description 向指定的sheet页cell中写入标签 write a table cell into the sheetbook
     * @version 1.0
     * @title 向指定的sheet页cell中写入标签 write a table cell into the sheetbook
     * @<NAME_EMAIL>
     * @param labelName the value of the cell,cell 的标签名
     * @param x the row number of the cell in the booksheet,cell在sheet页中,所在的列号
     * @param y the column number of the cell in the
     *            booksheet,cell在sheet页中,所在的行号
     * @param width how many rows to merge if merge itself use 1,cell将会合并几行
     * @param height how many columns to merge if merge itself use 1 ,cell将会合并几列
     * @throws WriteException 无法写入异常
     */
    public void writeLabel(String labelName, int x, int y, WritableCellFormat _fmt) throws WriteException {
        if ((x >= 0) && (y >= 0)) {
            Label lblColumn = null;

            lblColumn = new Label(x, y, labelName, _fmt);
            this.wb.addCell(lblColumn);
        }
    }

    /**
     * 
     * @description 生成表头
     * @version
     * @title
     * <AUTHOR>
     * @param labelName
     * @param x
     * @param y
     * @param width
     * @param height
     * @param _fmt
     * @throws WriteException
     */
    public void writeHeader(String labelName, int x, int y, int width, int height, WritableCellFormat _fmt)
            throws WriteException {
        if ((x >= 0) && (y >= 0)) {
            Label lblColumn = null;

            if ((width != 1) || (height != 1)) {
                this.wb.mergeCells(x, y, (x + width) - 1, (y + height) - 1);
            }

            lblColumn = new Label(x, y, labelName, _fmt);
            this.wb.addCell(lblColumn);
        }
    }

    /**
     * @description 行列及表头对象
     * @<NAME_EMAIL>
     * @.belongToModule 收付费-报表打印
     * @date 2019年3月19日 下午3:25:02
     */
    class ReportRowColNum {
        /**
         * 行号
         */
        public int row;
        /**
         * 列号
         */
        public int col;
        /**
         * 列头
         */
        private String[] colName;
        /**
         * 列合并
         */
        private String[] colMerge = null;
        /**
         * @Fields isNoCode : 表头不显示机构代码，默认显示
         */
        boolean isNoCode = false;

        public boolean isNoCode() {
            return isNoCode;
        }

        public void setNoCode(boolean isNoCode) {
            this.isNoCode = isNoCode;
        }

        public int getRow() {
            return row;
        }

        public void setRow(int row) {
            this.row = row;
        }

        public int getCol() {
            return col;
        }

        public void setCol(int col) {
            this.col = col;
        }

        public String[] getColName() {
            return colName;
        }

        public void setColName(String[] colName) {
            this.colName = colName;
        }

        public String[] getColMerge() {
            return colMerge;
        }

        public void setColMerge(String[] colMerge) {
            this.colMerge = colMerge;
        }
    }
}