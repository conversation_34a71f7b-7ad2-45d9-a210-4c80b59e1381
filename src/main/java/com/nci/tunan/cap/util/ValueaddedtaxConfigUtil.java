package com.nci.tunan.cap.util;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;

import com.nci.tunan.cap.impl.valueaddedtax.service.IValueaddedtaxFTConfService;

/**
 * 
 * @description 增值税拆分配置util
 * @<NAME_EMAIL>
 * @date 2016年4月29日 下午4:18:31
 * @.belongToModule 收付费-增值税拆分配置util
 */
public class ValueaddedtaxConfigUtil implements BeanPostProcessor {

    /**
     * 
     * @description 增值税拆分配置
     * @<NAME_EMAIL>
     * @date 2016年4月29日 下午4:18:31
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    /**
     * 
     * @description 增值税拆分配置
     * @<NAME_EMAIL>
     * @date 2016年4月29日 下午4:18:31
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof IValueaddedtaxFTConfService) {
            ((IValueaddedtaxFTConfService)bean).loadFeetypeConf();
        }
        return bean;
    }

}
