package com.nci.tunan.cap.util;

import java.util.ArrayList;
import java.util.List;

import com.nci.tunan.cap.interfaces.model.vo.InvoiceConfigInfoDetailVO;
import com.nci.tunan.cap.interfaces.model.vo.InvoiceConfigInfoVO;
import com.nci.tunan.cap.interfaces.model.vo.ReceiptInfoAllScheam;
import com.nci.udmp.framework.tag.i18n.CodeTable;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * @description 发票打印报文赋值对象数据组织工具类 针对不同业务与机构，差异化取值
 * @<NAME_EMAIL>
 * @date 2015-11-19 上午11:14:14
 * @.belongToModule 收付费-发票打印
 */
public class InvoiceInfoScheam {

    /**
     * @description 针对不同业务与机构，差异化取值
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param invoiceConfigInfoVO 发票打印配置信息(发票配置主表子表取值)
     * @param receiptCancelSearchVOList 发票业务信息取值对象(实收付明细表、发票主表与发票子表组织的对象)
     * @return invoiceConfigInfoVOList invoiceConfigInfoVO集合
     */
    public List<InvoiceConfigInfoVO> invoiceInfoScheam(InvoiceConfigInfoVO invoiceConfigInfoVO,
            List<ReceiptInfoAllScheam> receiptInfoAllScheamList) {
        List<InvoiceConfigInfoVO> invoiceConfigInfoVOList = new ArrayList();
        for (int i = 0; i < receiptInfoAllScheamList.size(); i++) {
        	String derivType = receiptInfoAllScheamList.get(i).getReceiptVO().getDerivType();
        	if ("001".equals(derivType)) {
            	derivType = "新契约";//@invalid 新契约
            } else if ("003".equals(derivType)) {
            	derivType = "续期";//@invalid 续期
            } else if ("004".equals(derivType)) {
            	derivType = "保全";//@invalid 保全
            }
            invoiceConfigInfoVO.setBusinessSource(derivType);//@invalid 业务来源
            invoiceConfigInfoVO.setOrganizationNo(receiptInfoAllScheamList.get(i).getReceiptVO().getOrganCode());//@invalid 机构号
            invoiceConfigInfoVO.setBatchNo("");//@invalid 批次号（待定）
            invoiceConfigInfoVO.setPayDate(DateUtilsEx.getDateString(receiptInfoAllScheamList.get(i).getReceiptVO()
                    .getFinishTime()));//@invalid 缴费日期
            invoiceConfigInfoVO.setInvoiceDate(DateUtilsEx.getToday());//@invalid 开票日期
            //@invalid 起止日期、至：为配置字段，如果库里存储默认值default，则给其赋值，否则置空
            if ("default".equals(invoiceConfigInfoVO.getStartDate())) {
                invoiceConfigInfoVO.setStartDate(DateUtilsEx.getToday());
            } else {
                invoiceConfigInfoVO.setStartDate("");
            }
            if ("default".equals(invoiceConfigInfoVO.getEndDate())) {
                invoiceConfigInfoVO.setEndDate(DateUtilsEx.getToday());
            } else {
                invoiceConfigInfoVO.setEndDate("");
            }
            //@invalid 行业分类:又差异化的机构单独配置入库，其他的为“保险业”
            if ((!"".equals(invoiceConfigInfoVO.getIndustryType()))  || null != invoiceConfigInfoVO.getIndustryType()) {
                invoiceConfigInfoVO.setIndustryType("保险业");
            }
            //@invalid 机打发票号码
            if ("default".equals(invoiceConfigInfoVO.getPriInvoiceNo())) {
                invoiceConfigInfoVO.setPriInvoiceNo(receiptInfoAllScheamList.get(i).getReceiptVO().getInvoiceNo());
            }
            //@invalid 机打发票代码
            if ("default".equals(invoiceConfigInfoVO.getPriInvoiceCode())) {
                invoiceConfigInfoVO.setPriInvoiceCode(receiptInfoAllScheamList.get(i).getReceiptVO().getInvoiceCode());
            }
            //@invalid 验证码
            if ("default".equals(invoiceConfigInfoVO.getTestNumber())) {
                invoiceConfigInfoVO.setTestNumber("");
            }
            //@invalid 校验码
            if ("default".equals(invoiceConfigInfoVO.getCheckoutCode())) {
                invoiceConfigInfoVO.setCheckoutCode("");
            }
            //@invalid 查询码
            if ("default".equals(invoiceConfigInfoVO.getConsultNumber())) {
                invoiceConfigInfoVO.setConsultNumber("");
            }
            //@invalid 收款单位
            invoiceConfigInfoVO.setReceiveCompany(CodeTable.getCodeDesc("APP___CAP__DBUSER.T_UDMP_ORG",receiptInfoAllScheamList.get(i).getReceiptVO().getOrganCode()));

            //@invalid 付款方名称
            invoiceConfigInfoVO.setPayCompany(receiptInfoAllScheamList.get(i).getReceiptVO().getPayeeName());

            //@invalid 投保人名称
            invoiceConfigInfoVO.setApplicantName(receiptInfoAllScheamList.get(i).getReceiptVO().getHolderName());

            //@invalid 交费方式
            if (null != receiptInfoAllScheamList.get(i).getReceiptVO().getPremFreq()) {
                
                invoiceConfigInfoVO.setPayWay(CodeTable.getCodeDesc("APP___CAP__DBUSER.t_charge_mode", receiptInfoAllScheamList.get(i).getReceiptVO().getPremFreq().toString()));
            }

            //@invalid 交费次数
            if (null != receiptInfoAllScheamList.get(i).getReceiptVO().getPaidCount()) {
                invoiceConfigInfoVO.setPayTimes(receiptInfoAllScheamList.get(i).getReceiptVO().getPaidCount()
                        .toString());
            }

            //@invalid 保单合同号
            invoiceConfigInfoVO.setPolicyNo(receiptInfoAllScheamList.get(i).getReceiptVO().getPolicyCode());

            //@invalid 产品组合名称
            invoiceConfigInfoVO.setGroupBussiness(receiptInfoAllScheamList.get(i).getReceiptVO().getGroupName());

            //@invalid 保全项目
            invoiceConfigInfoVO.setCUSItem(receiptInfoAllScheamList.get(i).getReceiptVO().getCsName());

            //@invalid 是否外包打印
            if(null != receiptInfoAllScheamList.get(i).getReceiptVO().getIsBatch()){
                invoiceConfigInfoVO.setJobType(receiptInfoAllScheamList.get(i).getReceiptVO().getIsBatch().toString());
            }
            
            //@invalid 单证ID
            invoiceConfigInfoVO.setDocumentID(receiptInfoAllScheamList.get(i).getReceiptVO().getDocId());
            
            //@invalid 金额合计
            invoiceConfigInfoVO.setSumMoney(receiptInfoAllScheamList.get(i).getReceiptVO().getFeeAmount().toString());
            
            //@invalid 险种等信息处理
            InvoiceConfigInfoDetailVO invoiceConfigInfoDetailVO;
            List<InvoiceConfigInfoDetailVO> invoiceConfigInfoDetailVOList = new ArrayList();
            if(null != receiptInfoAllScheamList.get(i).getReceiptDetailVOList()){
                for (int j = 0; j < receiptInfoAllScheamList.get(i).getReceiptDetailVOList().size(); j++) {//@invalid 险种等信息
                    invoiceConfigInfoDetailVO = new InvoiceConfigInfoDetailVO();
                    invoiceConfigInfoDetailVO.setPremium(receiptInfoAllScheamList.get(i).getReceiptDetailVOList().get(j)
                            .getFeeAmount().toString());//@invalid 险种金额
                    if(null != receiptInfoAllScheamList.get(i).getReceiptDetailVOList().get(j).getIsRiskMain()){
                        invoiceConfigInfoDetailVO.setProductCategory(receiptInfoAllScheamList.get(i).getReceiptDetailVOList()
                                .get(j).getIsRiskMain().toString());//@invalid 主附险标示符"0"为主险，"1"为附加险
                    }
                    invoiceConfigInfoDetailVO.setProductName(receiptInfoAllScheamList.get(i).getReceiptDetailVOList()
                            .get(j).getBusiProdName());//@invalid 险种名称
                    invoiceConfigInfoDetailVO.setProductNo(receiptInfoAllScheamList.get(i).getReceiptDetailVOList().get(j)
                            .getBusiProdCode());//@invalid 险种代码
                    invoiceConfigInfoDetailVOList.add(invoiceConfigInfoDetailVO);
                }
            }
            invoiceConfigInfoVO.setInvoiceConfigInfoVOList(invoiceConfigInfoDetailVOList);
            invoiceConfigInfoVOList.add(invoiceConfigInfoVO);
        }
        return invoiceConfigInfoVOList;
    }
}
