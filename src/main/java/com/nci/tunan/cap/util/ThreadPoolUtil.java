package com.nci.tunan.cap.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/** 
 * @ClassName: ThreadPoolUtil 
 * @description 创建线程池-初始化类
 * <AUTHOR>
 * @date 2019年12月18日 下午4:16:15 
 * @.belongToModule 收付费-创建线程池
 */ 
public class ThreadPoolUtil {
	/** 
	 * 创建线程池-初始化设置线程个数10
	 */ 
	public static final  ExecutorService POOL = Executors.newFixedThreadPool(10);//创建线程池-初始化10
}
