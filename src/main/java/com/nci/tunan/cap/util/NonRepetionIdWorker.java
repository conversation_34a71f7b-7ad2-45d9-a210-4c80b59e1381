package com.nci.tunan.cap.util;

import java.text.SimpleDateFormat;



public class NonRepetionIdWorker {
    /** 上次生成ID的时间截 */
    private static long lastTimestamp = -1L;
    /**
     * @return 返回非重复单号;之前批次号生成规则保持一致
     */
    public  static synchronized String nextDisNO() {
        long timestamp = System.currentTimeMillis();
        if (lastTimestamp == timestamp) {
                 timestamp =  System.currentTimeMillis();
                while (timestamp <= lastTimestamp) {
                	timestamp = System.currentTimeMillis();
                }
        }
        //上次生成的时间截
        lastTimestamp = timestamp;
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateStr ="075"+dateformat.format(timestamp); 
        return dateStr;
    }
   
}
