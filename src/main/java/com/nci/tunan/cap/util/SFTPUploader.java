package com.nci.tunan.cap.util;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
 
import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SFTP批量上传文件
 * <AUTHOR>
 *
 */
public class SFTPUploader {
    private String host;
    private int port;
    private String username;
    private String password;
    private String remoteDir;
    private String localDir;
    private transient Logger log = LoggerFactory.getLogger(this.getClass());
 
    public SFTPUploader(String host, int port, String username, String password, String remoteDir, String localDir) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.remoteDir = remoteDir;
        this.localDir = localDir;
    }
 
    public void uploadFiles() {
        Session session = null;
        Channel channel = null;
        ChannelSftp channelSftp = null;
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(username, host, port);
            session.setConfig("StrictHostKeyChecking", "no");
            session.setPassword(password);
            session.connect();
            channel = session.openChannel("sftp");
            channel.connect();
            channelSftp = (ChannelSftp) channel;
            try {
				channelSftp.cd(remoteDir);
			} catch (Exception e) {
				// 目录不存在，则创建文件夹
	            String[] dirs = remoteDir.split("/");
	            String tempPath = "";
	            int index = 0;
	            mkdirDir(dirs, tempPath, dirs.length, index, channelSftp);
			} // 切换到远程目录，如果不存在则会自动创建（取决于服务器配置）
            uploadDirectory(new File(localDir), channelSftp); // 开始上传本地目录到远程目录
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.exit(); // 关闭SFTP会话
            }
            if (channel != null) {
                channel.disconnect(); // 关闭通道连接
            }
            if (session != null) {
                session.disconnect(); // 断开与SFTP服务器的连接
            }
        }
    }
 
    private void uploadDirectory(File localDir, ChannelSftp channelSftp) throws SftpException {
        File[] files = localDir.listFiles(); // 获取目录下所有文件和子目录的列表
        if (files != null) { // 确保目录不为空或不是一个文件类型对象（例如符号链接）
            for (File file : files) { // 遍历所有文件和子目录
                if (file.isDirectory()) { // 如果是目录，则递归调用uploadDirectory方法上传子目录中的文件和子目录。
                    uploadDirectory(file, channelSftp); // 注意：这里需要确保远程目录结构与本地一致。如果不一致，可能需要手动创建远程目录结构。或者使用mkdir等命令在远程创建相应目录。
                } else { // 如果是文件，则直接上传文件。
                    String remoteFilePath = remoteDir + File.separator + file.getName(); // 构建远程文件路径。这里假定你想将文件放在与本地目录同名的远程目录下。如果不是这样，请自行调整路径构建逻辑。
                    channelSftp.put(file.getAbsolutePath(), remoteFilePath); // 上传文件到SFTP服务器。注意这里是绝对路径和相对路径的结合使用。根据实际需求调整。例如，如果只想上传到特定子目录，可以只使用文件名而不需要路径。或者使用相对路径（"file.getName()"）。具体取决于你的需求。这里用的是绝对路径（"file.getAbsolutePath()"），以确保文件被上传到正确的位置。如果需要上传到特定子目录，可以考虑使用"remote
                }
            }
        }
    }
    
    /**
     * 递归根据路径创建文件夹
     *
     * @param dirs     根据 / 分隔后的数组文件夹名称
     * @param tempPath 拼接路径
     * @param length   文件夹的格式
     * @param index    数组下标
     * @return
     */
    public void mkdirDir(String[] dirs, String tempPath, int length, int index, ChannelSftp sftp) {
        // 以"/a/b/c/d"为例按"/"分隔后,第0位是"";顾下标从1开始
        index++;
        if (index < length) {
            // 目录不存在，则创建文件夹
            tempPath += "/" + dirs[index];
        }
        try {
            log.info("检测目录[" + tempPath + "]");
            sftp.cd(tempPath);
            if (index < length) {
                mkdirDir(dirs, tempPath, length, index, sftp);
            }
        } catch (SftpException ex) {
            log.warn("创建目录[" + tempPath + "]");
            try {
                sftp.mkdir(tempPath);
                sftp.cd(tempPath);
            } catch (SftpException e) {
                e.printStackTrace();
                log.error("创建目录[" + tempPath + "]失败,异常信息[" + e.getMessage() + "]");

            }
            log.info("进入目录[" + tempPath + "]");
            mkdirDir(dirs, tempPath, length, index, sftp);
        }
    }
}
