package com.nci.tunan.cap.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.nci.tunan.cap.shhealthcare.impl.service.IHealthcareBackboardBatchService;
import com.nci.tunan.cap.shhealthcare.impl.ucc.IBankCardPaymentUpUCC;
import com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.DateUtilsEx;

/** 
 * @description 上海医保银行卡缴费结果接口批处理
 * <AUTHOR> 
 * @date 2020年1月16日 下午2:34:50 
 * @.belongToModule  上海医保收付费 
*/
public class HealthCareBackboardBatch extends AbstractBatchJobForMod {
	/** 
	* @Fields healthcareBackboardBatchService : 银行卡缴费结果接口service
	*/ 
	private IHealthcareBackboardBatchService healthcareBackboardBatchService;
	/** 
	* @Fields bankCardPaymentUpUCC : 银行卡缴费结果接口ucc
	*/ 
	private IBankCardPaymentUpUCC bankCardPaymentUpUCC;
	
	/**
	 * @description 启动批处理
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext, java.util.List, int)
	 * @param arg0 作业中共享的全局配置信息
	 * @param resultDatas 执行结果
	 * @param arg2 批处理逻辑依赖关系
	 * @return 启动结果
	*/
	@Override
	public List<JobData> execute(JobSessionContext arg0, List<JobData> resultDatas,
			int arg2) {
		logger.info("上海医保银行卡缴费接口批处理-execute方法开始");
		try{
			for (JobData jobData : resultDatas) {
				ShHealthcareBatchBO shHealthcareBatchBO = (ShHealthcareBatchBO) jobData.getData().get("healthcareBatchBO");				
				//先判断数据是否有问题，有问题跳过此次循环
				boolean flag = bankCardPaymentUpUCC.checkDataByParam(shHealthcareBatchBO);
				if(!flag){
					continue;
				}
				if (CodeCst.FEE_STATUS__FINISH.equals(shHealthcareBatchBO.getFeeStatus()) || CodeCst.BANK_TEXT_STATUS__BACK_SUCC.equals(shHealthcareBatchBO.getBankTextStatus())) {//制反盘成功
					bankCardPaymentUpUCC.saveBankCardPaymentInfo(shHealthcareBatchBO);				
				}else {
					int result = shHealthcareBatchBO.getFailTimes().compareTo(new BigDecimal(CodeCst.NUMBER_EIGHT));
					if (result >= 0) {//制反盘失败次数大于8次
						bankCardPaymentUpUCC.saveBankCardPaymentInfo(shHealthcareBatchBO);				
					}
				}			
			}
			logger.info("上海医保银行卡缴费接口批处理-execute方法结束");
		} catch (Exception e) {
			logger.info("上海医保银行卡缴费接口异常！"+ "==异常原因是===" + e.getMessage(), e);
			}
	return resultDatas;
	}

	/**
	 * @description 写入操作
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 作业中共享的全局配置信息
	 * @param arg1 作业信息
	*/
	@Override
	public void write(JobSessionContext arg0, JobData arg1) {
		
		
	}

	/**
	 * @description 获取批处理名称
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
	 * @return 批处理名称
	*/
	@Override
	public String getIdName() {
		
		return null;
	}

	/**
	 * @description 是否可执行
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
	 * @return 返回是否可执行
	*/
	@Override
	public boolean isCanBeRun() {
		
		return true;
	}

	/**
	 * @description 启动批处理
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 作业中共享的全局配置信息
	 * @param arg1 作业信息
	*/
	@Override
	public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
		
		
	}

	/**
	 * @description 中止批处理
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop() 
	*/
	@Override
	public void jobStop() {
		
		
	}

	/**
	 * @description 根据批处理入参查询符合条件的银行卡缴费信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#query(com.nci.udmp.component.batch.context.JobSessionContext, long, int)
	 * @param arg0 作业中共享的全局配置信息
	 * @param arg1 定义值
	 * @param arg2 定义除数
	 * @return 查询结果
	*/
	@Override
	public List<JobData> query(JobSessionContext arg0, long arg1, int arg2) {
		logger.info("上海医保银行卡缴费接口批处理-query方法开始");
		List<JobData> jobDataList = new ArrayList<JobData>();
		ShHealthcareBatchBO shhealthcareBatchBO=new ShHealthcareBatchBO();
		List<JobParam> paramlist = arg0.getParams();
		if(CollectionUtils.isNotEmpty(paramlist)){
			for (JobParam jobParam : paramlist) {
				if (jobParam.getParamName().equals("arapflag")&& jobParam.getParamType().equals("string")) {
					String arapflag = jobParam.getParamValue();
					shhealthcareBatchBO.setArapflag(arapflag);
				}
			}
		}
		String applyCode = "";
		List<JobParam> jobParamList = arg0.getParams();
		for(JobParam jobParam : jobParamList){
			if(jobParam.getParamName().trim().equalsIgnoreCase("applyCode") 
					&& jobParam.getParamType().trim().equalsIgnoreCase("string")){
				applyCode = jobParam.getParamValue().trim();
			}
		}
		// 如果输入投保单号为空，则数据只查询近三周内制返盘的数据
		if ("".equals(applyCode) || applyCode == null) {
			Date date = WorkDateUtil.getWorkDate(AppUserContext.getCurrentUser());
			Date date1 = DateUtilsEx.addDay(date, -CodeCst.NUMBER_TWENTY_ONE);
			String dayDate = DateUtilsEx.date2String(date1, "yyyy-MM-dd");
			shhealthcareBatchBO.setTodayDate(dayDate);
		}
		shhealthcareBatchBO.setApplyCode(applyCode);
		if ("xq".equals(shhealthcareBatchBO.getArapflag())) {
			shhealthcareBatchBO.setDerivType(CodeCst.DERIV_TYPE_PA);
		}else{
			shhealthcareBatchBO.setDerivType(CodeCst.DERIV_TYPE_NBS);
		}
		List<ShHealthcareBatchBO> healthcareBatchBOs = healthcareBackboardBatchService.queryBatchService(shhealthcareBatchBO);
		if (healthcareBatchBOs != null && healthcareBatchBOs.size() > 0) {
	        for (ShHealthcareBatchBO healthcareBatchBO : healthcareBatchBOs) {
 				Map<String, Object> data = new HashMap<String, Object>();
 				data.put("healthcareBatchBO", healthcareBatchBO);
 				JobData jobData = new JobData();
 				jobData.setData(data);
 				jobDataList.add(jobData);
	        }
		}
		logger.info("上海医保银行卡缴费接口批处理-query方法结束");
		return jobDataList;		
	}

	/**
	 * @description 查询总数
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
	 * @param arg0 作业中共享的全局配置信息
	 * @return 查询结果
	*/
	@Override
	public JobSessionContext queryCounts(JobSessionContext arg0) {
		logger.debug("上海医保银行卡缴费接口批处理-queryCounts方法开始");
		arg0.setStartNum(0);
		arg0.setEndNum(2);
		return arg0;
	}

	public IHealthcareBackboardBatchService getHealthcareBackboardBatchService() {
		return healthcareBackboardBatchService;
	}

	public void setHealthcareBackboardBatchService(
			IHealthcareBackboardBatchService healthcareBackboardBatchService) {
		this.healthcareBackboardBatchService = healthcareBackboardBatchService;
	}

	public IBankCardPaymentUpUCC getBankCardPaymentUpUCC() {
		return bankCardPaymentUpUCC;
	}

	public void setBankCardPaymentUpUCC(IBankCardPaymentUpUCC bankCardPaymentUpUCC) {
		this.bankCardPaymentUpUCC = bankCardPaymentUpUCC;
	}
	
}