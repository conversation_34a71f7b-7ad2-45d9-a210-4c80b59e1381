package com.nci.tunan.cap.util;

/** 
 * @description 通知书类型常量类工具包
 * @<NAME_EMAIL>
 * @date 2015-9-18 下午6:22:47  
 * @.belongToModule 收付费-发票打印
*/
public class DocumentTypeUtil {
    /**
     * 补费通知书
     */
    public static final String ADDPRE_DOCUMENT = "103";
    /**
     * 00000000
     */
    public static final String ORGANCODE = "00000000";
    /**
     * 生成xml编码格式
     */
    public static final String UTF8 = "UTF-8";
    /**
     * DATASETS
     */
    public static final String DATASETS = "DATASETS";
    /**
     * 通知书状态-待发放
     */
    public static final String NOTICE_STATUS = "1";
    /**
     * 通知书状态-待复核
     */
    public static final String NOTICE_STATUS6 = "6";
    /**
     * 通知书状态-已打印
     */
    public static final String NOTICE_STATUS3 = "3";
    /**
     * 通知书状态-待录入
     */
    public static final String NOTICE_STATS_ENTRY = "5";
    /**
     * 打印系统服务ip配置
     */
    public static final String DOCUMENTIP = "**********";
    /**
     * 打印系统服务ip配置
     */
    public static final String DOCUMENTIP_ESB = "**********";
    /**
     * 地址
     */
    public static final String DOCUMENTSERVICE = "http://**********:5999/?wsdl";
    /**
     * wsdl
     */
    public static final String DOCUMENTSERVICE_ESB = "http://***********:8111/services/P00002001217?wsdl";
    /**
     * 公司内单证打印
     */
    public static final String LTR_SENDTYPE = "Ltr_ORT";
    /**
     * eLetter
     */
    public static final String E_LETTER = "eLetter";
    /**
     * 公司内发票打印 
     */
    public static final String INV_ORT = "Inv_ORT";
    /**
     * 公司内保单打印
     */
    public static final String PIP_SENDTYPE = "Pip_ORT";
    /**
     * 外包单证
     */
    public static final String LTR_BPO = "Ltr_BPO";
    /**
     * Y
     */
    public static final String DOCUMENT_Y = "Y";
    /**
     * _
     */
    public static final String DOCUMENT_ = "_";
    /**
     * 打印标识
     */
    public static final String PRINT_PRINTTYPE = "Print";
    /**
     * 打印预览标识
     */
    public static final String PREVIEW_PRINTTYPE = "Preview";
    /**
     * 打印与打印预览标识
     */
    public static final String PREVIEWANDPRINT_PRINTTYPE = "PreviewAndPrint";
    /**
     * 查询标识
     */
    public static final String QUERY = "query";
    /**
     * 通知书模板主键
     */
    public static final String WITHOUTNOTICENO = "WITHOUTNOTICENO";
    /**
     * RECEIPTNOTICENO
     */
    public static final String RECEIPTNOTICENO = "RECEIPTNOTICENO";
    /**
     * CONTCHNOTICENO
     */
    public static final String CONTCHNOTICENO = "CONTCHNOTICENO";
    /**
     * 初审不通过通知书类型
     */
    public static final String DOCUEMENT03_TYPE = "NBS_00003";
    /**
     * 初审回执通知书类型
     */
    public static final String DOCUEMENT04_TYPE = "NBS_00004";
    /**
     * NBS_00010
     */
    public static final String DOCUEMENT10_TYPE = "NBS_00010";
    /**
     * 新契约内容变更通知书
     */
    public static final String NB_INFO_TEMPLATE_CODE = "nb.info.template.code";
    /**
     * 发票限额
     */
    public static final String FEE_LIMITS = "10";

}
