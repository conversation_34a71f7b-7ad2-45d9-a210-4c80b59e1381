package com.nci.tunan.cap.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationInfoBO;
import com.nci.tunan.cap.interfaces.model.vo.ManipulationInfoVO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.ftp.FtpUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * @description 实时收付对账批处理
 * @<NAME_EMAIL>
 * @date 2015-9-17 下午7:55:10
 * @.belongToModule 收付费-对账清单
 */
public class ManipulationInfoBatchJob extends AbstractBatchJobForMod {
	/**
	 * 实时对账文件读取批处理service
	 */
    private IManipulationInfoService manipulationInfoService;

    public IManipulationInfoService getManipulationInfoService() {
        return manipulationInfoService;
    }

    public void setManipulationInfoService(IManipulationInfoService manipulationInfoService) {
        this.manipulationInfoService = manipulationInfoService;
    }

    /**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {

    }
    /**
     * 获取id名字
     */
    @Override
    public String getIdName() {
        return null;
    }
    /**
     * 判断任务运行
     */
    @Override
    public boolean isCanBeRun() {
        return true;
    }
    /**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        jobSessionContext.setStartNum(1);
        jobSessionContext.setEndNum(1);
        jobSessionContext.setModNum(1);//批处理逻辑依赖关系需要
        return jobSessionContext;
    }


    /**
     1.获取文件(IP地址,端口号,用户名,密码，实时对账信息文件上传路径) ----------数据库t_payref_config配置
     * （单独配置） 2.根据调用接口的日期获取所要解析的文件 3.解析.txt文件（流读取） 4.根据文件内容格式 || 进行拆分 ,
     * 将拆分内容放入list<map>中 5.进行批量插入t_manipulation_info表中（如何预防重复插入）
     * @param jobSessionContext 任务会话上下文
     * @param start 起始位置
     * @param counts 查询数量
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
        try {
            toManipulationInfoBatch();
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<JobData> datas = new ArrayList<JobData>();
        return datas;
    }
    /**
     * 停止任务
     */
    @Override
    public void jobStop() {
        
    }
    /**
     * 读取ftp文件
     * @throws Exception
     */
    private void toManipulationInfoBatch() throws Exception {
        logger.info("实时收付对账文件读取批处理---开始执行");
        Date sapDate = DateUtilsEx.getTodayDate();
        FTPClient ftpClient = this.getFTPClient();
        InputStream inputStream = null;
        BufferedReader reader = null;
        if (null != ftpClient) {
            List<ManipulationInfoBO> infoBOList = new ArrayList<ManipulationInfoBO>();
            try {
                //1 读取数据的ftp服务器的对账目录
                String reconciliationRead = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP,
                        CodeCst.RECONCILIATION_READ);
                //2 备份文件的ftp服务器的对账目录
                String reconciliationbackups = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP,
                        CodeCst.RECONCILIATION_BACKUPS);
                //3 更改当前工作目录
                ftpClient.changeWorkingDirectory(reconciliationRead);
                //4 从ftp上获取目录下的文件
                FTPFile[] file = ftpClient.listFiles();
                logger.info("实时收付对账---待下载文件"+file.length+"个");
                
                //5 遍历所有文件，匹配需要查找的文件
                for (int i = 0; i < file.length; i++) {
                    String[] str = file[i].getName().split("\\.");
                    if ("txt".equals(str[str.length - 1])) {
                        inputStream = ftpClient.retrieveFileStream(file[i].getName());
                        logger.info("实时收付对账---下载第"+i+"个文件,获取InputStream");
                        //5.1 如果text文件中有BOM标识，去除
                        inputStream = JcifsUtil.getTrimBOMInputStream(inputStream);
                        reader = new BufferedReader(new InputStreamReader(inputStream));
                        logger.info("实时收付对账---下载第"+i+"个文件,获取BufferedReader");
                        String lineTxt = null;
                        //5.2 读取每一行数据
                        while ((lineTxt = reader.readLine()) != null) {
                            //5.3 根据｜进行拆分数据
                            logger.info("实时收付对账---文件行内容为：" + lineTxt);
                            String[] strArr = lineTxt.split("\\|");
                            Map maps = new HashMap();
                            for (int s = 0; s < strArr.length; s++) {
                                maps.put(s, strArr[s].toString());
                            }
                            infoBOList.add(getManipulationInfoBO(maps));
                        }
                        ftpClient.getReply();
                    }
                }
                
                logger.info("实时收付对账---转移对账文件到备份目录---开始");
                //@invalid 备份目录地址加工增加年月日
                Date d = WorkDateUtil.getWorkDate();
                String dateNowStr = DateUtilsEx.formatToString(d, "yyyy-MM-dd");
                String[] dateStr = dateNowStr.split("\\-");
                reconciliationbackups = reconciliationbackups.concat(dateStr[0]);
                ftpClient.makeDirectory(reconciliationbackups);
                reconciliationbackups = reconciliationbackups.concat("/").concat(dateStr[1]);
                ftpClient.makeDirectory(reconciliationbackups);
                reconciliationbackups = reconciliationbackups.concat("/").concat(dateStr[2]);
                ftpClient.makeDirectory(reconciliationbackups);
                for (int i = 0; i < file.length; i++) { 
                    //@invalid 在备份文件夹中增加文件，删除原读取文件夹中文件
                    ftpClient.rename(file[i].getName(), reconciliationbackups.concat("/").concat(file[i].getName()));
                    ftpClient.deleteFile(reconciliationRead.concat(file[i].getName()));
                }
                logger.info("实时收付对账---转移对账文件到备份目录---结束");
            } catch (IOException eIO) {
                eIO.printStackTrace();
                logger.info(eIO.getMessage());
                throw eIO;
            } catch (ParseException ePa) {
                ePa.printStackTrace();
                logger.info(ePa.getMessage());
                throw ePa;
            } catch (Exception e) {
                logger.info(e.getMessage());
                e.printStackTrace();
                throw e;
            } finally {
                if (reader != null) {
                    reader.close();
                    logger.info("实时收付对账---关闭BufferedReader流");
                }
                if (inputStream != null) {
                    inputStream.close();
                    logger.info("实时收付对账---关闭InputStream流");
                }
                FtpUtil.logoutFtpServer(ftpClient);
                logger.info("实时收付对账---退出FTP");
            }
            
            
            logger.info("实时收付对账---读取目录中所有文件的数据结束，记录数：" + infoBOList.size());
            List<ManipulationInfoBO> saveInfoBOList = new ArrayList<ManipulationInfoBO>();
            //@invalid 筛选本次需要存入数据库中的数据map集合
            Map<String, ManipulationInfoBO> mapBO = new ConcurrentHashMap<String, ManipulationInfoBO>();
            ManipulationInfoBO batchIDBO;
            List<String> batchIdAllList = new ArrayList<String>();
            //6 因ftp目录中对账不同的文件存在重复记录，故先判断该笔一笔交易唯一批次号是否存在，不存在才添加
            for(ManipulationInfoBO bo : infoBOList){
                if(!batchIdAllList.contains(bo.getBatchId())){
                    batchIdAllList.add(bo.getBatchId());
                    batchIDBO = BeanUtils.copyProperties(ManipulationInfoBO.class, bo);
                    mapBO.put(bo.getBatchId(), batchIDBO);
                }
            }
            logger.info("实时收付对账---文件内容去除重复后的记录数：" + batchIdAllList.size() + ",开始和数据库对比进行去重");
            List<String> batchIdList = new ArrayList<String>();
            int count = 0;//@invalid 一千条记录分一个线程处理。
            for (int i = 0; i < batchIdAllList.size(); i++){
                count++;
                batchIdList.add(batchIdAllList.get(i));
                if(count == CodeCst.NUMBER_THOUSAND || i == batchIdAllList.size()-1){
                    //@invalid 从map中删除数据库中已经存在的记录
                    distinctMap(mapBO, batchIdList);
                    count = 0;
                    batchIdList = new ArrayList<String>();
                }
            }
            //@invalid 去除重复后取出要插入的数据
            Set<String> mapKey = mapBO.keySet();
            for (String batchIdKay : mapKey) {
                saveInfoBOList.add(mapBO.get(batchIdKay));
            }
            logger.info("实时收付对账---本次需存入数据库中的记录数：" + saveInfoBOList.size());
            manipulationInfoService.batchSaveManipulationInfo(saveInfoBOList);
            logger.info("实时收付对账---保存成功");
        }
        Date end = new Date();
        long s = (end.getTime() - sapDate.getTime()) / CodeCst.NUMBER_THOUSAND;
        logger.info("实时收付对账文件读取批处理---执行结束---总耗时：" + s / CodeCst.NUMBER_THREE_THOUSAND_AND_SIX_HUNDRED + "时" + s / CodeCst.NUMBER_SIXTY
                % CodeCst.NUMBER_SIXTY + "分" + s % CodeCst.NUMBER_SIXTY + "秒");
    }
    /**
     * 连接ftp服务器
     * @return
     * @throws IOException
     */
    private FTPClient getFTPClient() throws IOException {
        try {
            String serverIp = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_IP);
            String ftpServerPort = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP,
                    CodeCst.FTP_SERVER_PORT);
            String ftpUserName = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_USER_NAME);
            String ftpPassword = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_PASSWORD);
            FtpServerConfig ftpServerConfig = new FtpServerConfig();
            ftpServerConfig.setFtpServerIp(serverIp);
            ftpServerConfig.setFtpServerPort(Integer.parseInt(ftpServerPort));
            ftpServerConfig.setFtpUserName(ftpUserName);
            ftpServerConfig.setFtpPassword(ftpPassword);
            FTPClient ftpClient = FtpUtil.loginFtpServer(ftpServerConfig);
            logger.info("实时收付对账---登录FTP");
            ftpClient.setControlEncoding("GBK"); //@invalid  设置格式
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);//@invalid  登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
            ftpClient.enterLocalPassiveMode();//@invalid  设置被动模式
            return ftpClient;
        } catch (IOException e) {
            logger.error("实时收付对账---登录FTP异常");
            throw e;
        }
    }
    /**
     * 组装对账内容
     * @param maps map集合
     * @return
     * @throws ParseException
     */
    private ManipulationInfoBO getManipulationInfoBO(Map maps) throws ParseException {
        ManipulationInfoVO infoVo = new ManipulationInfoVO();
        infoVo.setServicerCode(maps.get(0).toString()); //@invalid  服务商代码
        infoVo.setTerminalCode(maps.get(1).toString()); //@invalid  终端代码，pos机的识别码
        infoVo.setMerchantId(maps.get(2).toString()); //@invalid  商户代码，服务商分配给新华的商户代码；总公司一个代码，每个分公司各一个
        infoVo.setCompareAccDate(DateUtilsEx.formatToDate(maps.get(CodeCst.NUMBER_THREE).toString(), "")); //@invalid  对账日期
        infoVo.setBatchId(maps.get(CodeCst.NUMBER_FOUR).toString()); //@invalid  唯一批次号，每笔交易的唯一批次号
        infoVo.setTradeSn(maps.get(CodeCst.NUMBER_FIVE).toString()); //@invalid  流水号，批次内流水号，服务商自定义，如没有默认0
        infoVo.setChargeType(maps.get(CodeCst.NUMBER_SIX).toString()); //@invalid  收费类型:00-单笔实时代扣（无磁无密）；01-首期交费；02-E行销收费；04-续期交费；05-首期加费；06-保全交费
        infoVo.setCertifyType(maps.get(CodeCst.NUMBER_SEVEN).toString()); //@invalid  单证类型，信用卡，借记卡
        infoVo.setCertifyNo(maps.get(CodeCst.NUMBER_EIGHT).toString()); //@invalid  单证号码，投保单号/保单号/通知书号/保全受理号等
        infoVo.setAccountName(maps.get(CodeCst.NUMBER_NINE).toString()); //@invalid  账户名称
        infoVo.setAccountNo(maps.get(CodeCst.NUMBER_TEN).toString()); //@invalid  账户
        infoVo.setBankCode(maps.get(CodeCst.NUMBER_ELEVEN).toString()); //@invalid  扣款银行代码
        infoVo.setEnterAccDate(DateUtilsEx.formatToDate(maps.get(CodeCst.NUMBER_TWELVE).toString(), "")); //@invalid  到账日期，扣款成功的日期
        infoVo.setEnterAccTime(DateUtilsEx.formatToDate(maps.get(CodeCst.NUMBER_TWELVE).toString() + " " + maps.get(CodeCst.NUMBER_THIRTEEN).toString(),
                "yyyy-MM-dd HH:mm:ss")); //@invalid  到账时间，扣款成功的时间
        infoVo.setPremium(new BigDecimal(maps.get(CodeCst.NUMBER_FOURTEEN).toString())); //@invalid  扣款金额
        infoVo.setPoundage(new BigDecimal(maps.get(CodeCst.NUMBER_FIFTEEN).toString())); //@invalid  手续费，代扣的手续费，保留两位小数，如没默认为0.00
        infoVo.setPayType(CapVerify.ternaryOperator("2".equals(maps.get(CodeCst.NUMBER_SIXTEEN).toString()), CodeCst.PAY_MODE__ONLINE_BANK,
                (CapVerify.ternaryOperator("1".equals(maps.get(CodeCst.NUMBER_SIXTEEN).toString()), CodeCst.PAY_MODE__POS, maps.get(CodeCst.NUMBER_SIXTEEN)
                        .toString())))); //@invalid  扣款类型，有磁有密(pos)，无磁无密(单笔实时)
        infoVo.setUserType(maps.get(CodeCst.NUMBER_SEVENTEEN).toString()); //@invalid  操作员类型，内勤,代理人,单笔实时代扣（无磁无密）
        infoVo.setUserCode(maps.get(CodeCst.NUMBER_EIGHTEEN).toString()); //@invalid  操作员代码，内勤或代理人的代码(不能为空)，无磁无密默认为：DBDK
        infoVo.setReturnCode(maps.get(CodeCst.NUMBER_NINETEEN).toString()); //@invalid  扣款结果，银行扣款失败,银行扣款成功
        if(maps.size() > CodeCst.NUMBER_TWENTY) {
        	infoVo.setReturnMsg(maps.get(CodeCst.NUMBER_TWENTY).toString()); //@invalid  扣款结果描述，收费结果描述:
        																	 //@invalid  成功-收费成功,失败-具体失败原因
        }
        //@invalid 收费类型:07-单笔实时代付 或者 操作员类型:4：单笔实时代付（无磁无密） 均为付费，其余为收
        String arapFlag = CapVerify.ternaryOperator(CodeCst.REALTIME_CHARGETYPE_INDEMNITY.equals(maps.get(CodeCst.NUMBER_SIX).toString()) 
                || CodeCst.REALTIME_USERTYPE_INDEMNITY.equals(maps.get(CodeCst.NUMBER_SEVENTEEN).toString())
                , CodeCst.AR_AP__AP, CodeCst.AR_AP__AR);
        // 对账文件里面，付费类的第23位为对账码，收费类的第22位为对账码
        if (CodeCst.AR_AP__AP.equals(arapFlag)){
        	if(maps.size() > CodeCst.NUMBER_TWENTY_ONE){
        		if (!StringUtilsEx.isBlank(maps.get(CodeCst.NUMBER_TWENTY_ONE).toString())){
        			infoVo.setPaySendTime(DateUtilsEx.formatToDate(maps.get(CodeCst.NUMBER_TWENTY_ONE).toString(), "yyyyMMddHHmmss"));
        		}
        	}
        	if(maps.size() > CodeCst.NUMBER_TWENTY_TWO){
        		infoVo.setAbstract(maps.get(CodeCst.NUMBER_TWENTY_TWO).toString());
        	}
        	infoVo.setNoteCode(maps.get(CodeCst.NUMBER_TWENTY_THREE).toString());
        }else {
        	if(maps.size() > CodeCst.NUMBER_TWENTY_ONE){
        		infoVo.setAbstract(maps.get(CodeCst.NUMBER_TWENTY_ONE).toString());
        	}
        	infoVo.setNoteCode(maps.get(CodeCst.NUMBER_TWENTY_TWO).toString());
        }
        infoVo.setArapFlag(arapFlag);
        ManipulationInfoBO infoBos = BeanUtils.copyProperties(ManipulationInfoBO.class, infoVo);
        return infoBos;
    }
    /**
     * @description 批处理执行
     * @param arg0 任务会话上下文
     * @param arg1 参数列表
     * @param arg2 取模数值
     */
    @Override
    public List<JobData> execute(JobSessionContext arg0, List<JobData> arg1, int arg2) {
        return arg1;
    }
    /**
     * @description 批处理异常处理
     * @param arg0 任务会话上下文
     * @param arg1 任务数据参数
     */
    @Override
    public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
        
    }
    
    /** 
     * @ClassName: MainputlationExecutor 
     * @description 处理对账文件记录已存在数据库表中的记录内部类。
     * <AUTHOR>
     * @date 2020年7月23日 下午6:09:13 
     *  
     */ 
    public Map<String, ManipulationInfoBO> distinctMap(Map<String, ManipulationInfoBO> map,List<String> lstBatchID){
        ManipulationInfoBO batchIDBO = new ManipulationInfoBO();
        batchIDBO.setBatchIdList(lstBatchID);
        List<ManipulationInfoBO> lstMani = manipulationInfoService.findAllManipulationByBatchID(batchIDBO);
        logger.info("实时收付对账---查询数据库中已存在的记录数：" + lstMani.size());
        //从map集合中移除数据库中已经存在的记录
        for (ManipulationInfoBO bo : lstMani) {
            map.remove(bo.getBatchId());
        }
        return map;
    }
    
}
