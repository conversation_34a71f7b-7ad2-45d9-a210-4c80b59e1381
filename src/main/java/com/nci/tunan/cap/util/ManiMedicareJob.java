
package com.nci.tunan.cap.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.slf4j.Logger;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * <AUTHOR>
 * @.belongToModule 收付费—医保对账表格生成批处理
 * @date 2015年5月21日 下午2:01:04
 * @description 医保对账表格生成批处理
 */
public class ManiMedicareJob extends AbstractBatchJobForMod {
	/**
	 * 对账文件读取批处理service
	 */
	private IManipulationInfoService manipulationInfoService;
	/**
	 * 日志工具
	 */
	protected Logger logger = LoggerFactory.getLogger();

	public IManipulationInfoService getManipulationInfoService() {
		return manipulationInfoService;
	}

	public void setManipulationInfoService(
			IManipulationInfoService manipulationInfoService) {
		this.manipulationInfoService = manipulationInfoService;
	}

	/**
     * @description 批处理任务的具体操作
     * @<NAME_EMAIL>
     * @param jobSessionContext 上下文参数
     * @param resultDatas 需要处理的结果集
     * @return 处理后的结果集
     */
	@Override
	public List<JobData> execute(JobSessionContext jobSessionContext,
			List<JobData> resultDatas, int modNum) {
		return resultDatas;
	}
	 /**
     * @description 写入操作
     * @<NAME_EMAIL>
     * @param jobSessionContext 上下文对象
     * @param jobData 数据对象
     */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData jobData) {

		logger.debug("------------------write------------------------");
		List<JobParam> paramList = jobSessionContext.getParams();
		Date finishTime = DateUtilsEx.addDay(DateUtilsEx.getTodayDate(), -1);
		if (null != paramList) {
			String paramName;
			String paramValue;
			for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
				JobParam param = iter.next();
				logger.info("param.getParamName()=    " + param.getParamName());
				logger.info("param.getParamCode()=    " + param.getParamCode());
				logger.info("param.getParamType()=    " + param.getParamType());
				logger.info("param.getParamValue()=    "
						+ param.getParamValue());
				paramName = param.getParamName();
				paramValue = param.getParamValue();
				// 批处理启动参数指定日期，则生产文件从指定日期开始
				if ("finishTime".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
					    finishTime = DateUtilsEx.formatToDate(paramValue, "");
					}
				}
				
			}
		}
		if(!finishTime.after(DateUtilsEx.addDay(DateUtilsEx.getTodayDate(), -1))){
			try {
			    manipulationInfoService.doDownloadManipulationInfoImpl(finishTime);
			    
			} catch (Exception e) {
				e.printStackTrace();
			}

		} else {
		    logger.error("------------------医保对账开始生成对账时间有误  ------------------------");
		}

	}
	/**
     * @description 批处理异常处理
     * @param arg0 任务会话上下文
     * @param arg1 任务数据参数
     */
	@Override
	public void jobErrorAudit(JobSessionContext jobSessionContext,
			JobData jobData) {

	}
	/**
     *@description 获取id名字
     */
	@Override
	public String getIdName() {
		return null;
	}
	/**
     *@description 判断任务运行
     */
	@Override
	public boolean isCanBeRun() {
		return true;
	}
	 /**
     * @description 查询任务数量
     * @param jobSessionContext  任务上下文
     */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		logger.debug("--------------queryCounts-------------------");
		jobSessionContext.setStartNum(1);
		jobSessionContext.setEndNum(1);
		jobSessionContext.setModNum(1);// 批处理逻辑依赖关系需要
		return jobSessionContext;
	}
    /** @description 获取文件(IP地址,端口号,用户名,密码，实时对账信息文件上传路径) 
    * @param jobSessionContext 任务会话上下文
    * @param start 起始位置
    * @param counts 查询数量
    */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start,
			int counts) {
		logger.debug("---------------query-------------------------");
		List<JobData> datas = new ArrayList<JobData>();
		JobData data = new JobData();
		datas.add(data);
		return datas;
	}
	/**
     *@description 停止任务
     */
	@Override
	public void jobStop() {

	}
	
}
