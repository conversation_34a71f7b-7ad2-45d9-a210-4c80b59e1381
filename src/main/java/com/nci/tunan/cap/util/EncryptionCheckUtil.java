package com.nci.tunan.cap.util;

import java.io.BufferedInputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;

import org.slf4j.Logger;

import com.nci.udmp.framework.util.CapAPDes3;
import com.nci.udmp.util.logging.LoggerFactory;
/**
 * 
 * @description 加密校验工具类
 * @<NAME_EMAIL>
 * @date 2015-12-11 上午10:27:46
 * @.belongToModule 收付费-制盘、返盘
 */
public class EncryptionCheckUtil {

    /** 
     * 日志工具
     */
     private static Logger logger = LoggerFactory.getLogger();
    /**
     * 
     * @description 生成MD5校验文件
     * @version
     * @title CreatMD5File
     * @<NAME_EMAIL>
     * @param readFilePath 源文件路径+文件名
     * @param writeFilePath 生成的md5校验文件路径+文件名
     */
    public static void creatMD5File(String readFilePath,String writeFilePath,String safeCode){     
        File file = new File(readFilePath);
        File writeFile = new File(writeFilePath);
        CapMD5CipherUtil.getMd5ByFile(file, writeFile,safeCode.getBytes());
    }
    /**
     * @description  通过输入文件生成16进制的md5摘要，再对md5摘要进行3des加密写入文件
     * @version
     * @title
     * <AUTHOR>  <EMAIL> 
     * @param readFilePath 输入文件
     * @param writeFilePath 写入加密后的MD5文件
     * @param safeCode 安全码
     * @param keystr1  密钥
     * @param ivstr1 void  iv向量 
    */
    public static void creatMD5File(String readFilePath,String writeFilePath,String safeCode,String keystr1,String ivstr1){     
        try {
            File file = new File(readFilePath);       
            FileOutputStream fos =  new FileOutputStream(new File(writeFilePath));
            String hexMd5 = CapMD5CipherUtil.getMd5ByFile(file,safeCode.getBytes());
            byte[] bytes = CapAPDes3.encrypt(hexMd5.getBytes(), keystr1, ivstr1);
            fos.write(bytes);
            fos.close();            
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }
    /**
     * 
     * @description 生成Des3加密文件
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param filePath 源文件路径+文件名
     * @param writePath 生成加密文件的路径+文件名
     * @return boolean 生成结果
     */
    public static boolean creatDes3EncryptionFile(String filePath,String writePath,String keystr1,String ivstr1){
        try {

            FileInputStream fin = new FileInputStream(new File(filePath));//@invalid "UTF-8"
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] b = new byte[CodeCst.NUMBER_FOUR_THOUSAND_AND_NINETY_SIX];
            int n;
            while ((n = fin.read(b)) != -1) {
                out.write(b, CodeCst.NUMBER_ZERO, n);
            }
            byte[] des3Str = CapAPDes3.encrypt(out.toByteArray(), keystr1, ivstr1);

            FileOutputStream fos =  new FileOutputStream(new File(writePath),false);
            fos.write(des3Str); 
            fos.close();

            fin.close();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } 
        
        return true;
    }
    /**
     * 
     * @description 解析Des3加密文件
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param filePath 加密文件的路径+文件名
     * @param writePath 解密文件路径+文件名
     * @return boolean 生成结果
     */
    public static boolean creatDes3DecryptionFile(String filePath,String writePath,String keystr1,String ivstr1){
        try {
            BufferedInputStream in = new BufferedInputStream(new FileInputStream(new File(filePath)));//@invalid "UTF-8"
            ByteArrayOutputStream out = new ByteArrayOutputStream(CodeCst.READ_WRITE_SIZE);//@invalid "UTF-8"
            int size = CodeCst.NUMBER_ZERO;
            byte[] temp = new byte[CodeCst.READ_WRITE_SIZE];
            while(( size= in.read(temp)) !=-1 ){
                out.write(temp, CodeCst.NUMBER_ZERO, size);
            }
            in.close();
            byte[] content = out.toByteArray();
            String des3Str = new String(CapAPDes3.decrypt(content, keystr1, ivstr1),CodeCst.ENCODE_GBK);
            OutputStreamWriter writ = new OutputStreamWriter(new FileOutputStream(new File(writePath),false),"GBK");//@invalid "UTF-8"
            BufferedWriter writer = new BufferedWriter(writ);
            writer.append(des3Str);
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } 
        
        return true;     
    }
}
