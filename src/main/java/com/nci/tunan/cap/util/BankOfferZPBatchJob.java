package com.nci.tunan.cap.util;

import java.util.ArrayList;
import java.util.List;

import com.nci.tunan.cap.impl.banktrans.service.IBankOfferZPForService;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;

/**
 * 
 * @description 集中制返盘 制盘处理批处理job
 * @<NAME_EMAIL>
 * @date 2016-1-18 下午2:21:04
 * @.belongToModule 收付费-集中制返盘 
 */
public class BankOfferZPBatchJob extends AbstractBatchJobForMod{
    /** 
     * 制盘处理Service类
     */ 
    private IBankOfferZPForService bankOfferZPForService;
    /**
     * @description 制盘处理批处理-查询数据方法
     * @<NAME_EMAIL>
     * @param jobSessionContext 作业中共享的全局配置信息
     * @param start 从该计数开始
     * @param counts 执行个数
     * 
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
    	long stime =System.currentTimeMillis();
    	logger.info("制盘操作======>开始:"+stime);
        List<JobData> jobDataList = new ArrayList<JobData>();
        bankOfferZPForService.queryHandle(jobSessionContext);
        long etime =System.currentTimeMillis();
        logger.info("制盘操作======>结束,耗时:"+(etime-stime)+"毫秒");
        return jobDataList;
    }

    /**
     * @description 制盘处理批处理-查询个数方法
     * @<NAME_EMAIL>
     * @param jobSessionContext 作业中共享的全局配置信息
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        jobSessionContext.setStartNum(1);
        jobSessionContext.setEndNum(1);
        jobSessionContext.setModNum(1);//@invalid 批处理逻辑依赖关系需要
        return jobSessionContext;
    }
    /**
     * @description 制盘处理批处理-执行方法
     * @<NAME_EMAIL>
     * @param jobSessionContext 作业中共享的全局配置信息
     * @param resultDatas 需要处理的结果集
     * @param modNum 线程阀值
     */
    @Override
    public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> resultDatas, int modNum) {
    	jobSessionContext.setTotalCnt(1);
    	jobSessionContext.setFailCnt(0);
    	jobSessionContext.setSuccessCnt(1);
        return resultDatas;
    }

    /**
     * @description 制盘处理批处理-查询方法
     * @<NAME_EMAIL>
     * @param jobSessionContext 作业中共享的全局配置信息
     * @param jobData 作业信息
     * 
     */
    @Override
    public void jobErrorAudit(JobSessionContext jobSessionContext, JobData jobData) {
        
    }

    /**
     * @description 制盘处理批处理-写入方法
     * @<NAME_EMAIL>
     * @param jobSessionContext 作业中共享的全局配置信息
     * @param jobData 作业信息
     * 
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {
        
    }

    /**
     * @description 制盘处理批处理-获取任务名称
     * @<NAME_EMAIL>
     */
    @Override
    public String getIdName() {
        return null;
    }

    /**
     * @description 制盘处理批处理-是否可以执行判断方法
     * @<NAME_EMAIL>
     */
    @Override
    public boolean isCanBeRun() {
        return true;
    }

    /**
     * @description 制盘处理批处理-停止批处理方法
     * @<NAME_EMAIL>
     */
    @Override
    public void jobStop() {
        
    }

    public IBankOfferZPForService getBankOfferZPForService() {
        return bankOfferZPForService;
    }

    public void setBankOfferZPForService(IBankOfferZPForService bankOfferZPForService) {
        this.bankOfferZPForService = bankOfferZPForService;
    }

    
}
