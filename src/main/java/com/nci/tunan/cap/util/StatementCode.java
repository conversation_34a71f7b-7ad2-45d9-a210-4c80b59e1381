package com.nci.tunan.cap.util;

import java.util.LinkedHashMap;
import java.util.Map;

public class StatementCode {
    
    public static final Map<String, String> REPORT_MAP_NB = new LinkedHashMap<String, String>();
    public static final Map<String, String> REPORT_MAP_XQ = new LinkedHashMap<String, String>();
    public static final Map<String, String> REPORT_MAP_CS = new LinkedHashMap<String, String>();
    public static final Map<String, String> REPORT_MAP_CLM = new LinkedHashMap<String, String>();
    public static final Map<String, String> REPORT_MAP_BCP = new LinkedHashMap<String, String>();
    public static final Map<String, String> REPORT_MAP_JZ = new LinkedHashMap<String, String>();
    
    static {
        //@invalid 新契约
        REPORT_MAP_NB.put("NB001", "承保日结");
        REPORT_MAP_NB.put("PA001", "出单前退费日结");
        REPORT_MAP_NB.put("PA003", "出单后生效前退费日结");
        //续期
        REPORT_MAP_XQ.put("PA002", "续期日结");
        REPORT_MAP_XQ.put("PA004", "续期暂收退费日结");
        //@invalid 保全
        REPORT_MAP_CS.put("CS001", "保户质押贷款日结");
        REPORT_MAP_CS.put("CS002", "满期给付日结");
        REPORT_MAP_CS.put("CS003", "年金给付日结");
        REPORT_MAP_CS.put("CS004", "退保日结");
        REPORT_MAP_CS.put("CS006", "保户质押贷款清偿日结");
        REPORT_MAP_CS.put("CS007", "复效日结");
        REPORT_MAP_CS.put("CS008", "加保日结");
        REPORT_MAP_CS.put("CS009", "加补费日结");
        REPORT_MAP_CS.put("CS010", "退费日结");
        REPORT_MAP_CS.put("CS011", "账户领取日结");
        REPORT_MAP_CS.put("CS012", "复缴日结");
        REPORT_MAP_CS.put("CS013", "保费自垫日结");
        REPORT_MAP_CS.put("CS014", "险种转换日结");
        REPORT_MAP_CS.put("CS015", "现金红利分配日结");
        REPORT_MAP_CS.put("CS016", "投资组合转换手续费日结");
        REPORT_MAP_CS.put("CS017", "新增长期附加险日结");
        REPORT_MAP_CS.put("CS018", "年金险积累期与领取期转换日结");
        REPORT_MAP_CS.put("CS019", "产品转移日结");
        //理赔
        REPORT_MAP_CLM.put("CLM001", "理赔退费日结");
        REPORT_MAP_CLM.put("CLM004", "理赔退保金日结");
        REPORT_MAP_CLM.put("CLM010", "死亡给付日结");
        REPORT_MAP_CLM.put("CLM011", "医疗给付日结");  
        REPORT_MAP_CLM.put("CLM012", "赔款支出日结");
        REPORT_MAP_CLM.put("CLM013", "预付赔款日结");
        REPORT_MAP_CLM.put("CLM014", "二次核保加费日结");
        REPORT_MAP_CLM.put("CLM015", "理赔超期赔偿日结");
        REPORT_MAP_CLM.put("CLM016", "理赔豁免保费日结");
        //@invalid 收付费
        REPORT_MAP_BCP.put("BCP001", "收费日结单-按收费类型");
        REPORT_MAP_BCP.put("BCP002", "收费日结单-按收费方式");
        REPORT_MAP_BCP.put("BCP003", "付费日结单-按付费类型");
        REPORT_MAP_BCP.put("BCP004", "付费日结单-按付费方式");
        REPORT_MAP_BCP.put("BCP007", "收费日结单-内部转账");
        REPORT_MAP_BCP.put("BCP008", "付费日结单-内部转账");
        //invalid 集中代收付
        REPORT_MAP_JZ.put("JZ001", "收费按集中分类日结-银行划款");
        REPORT_MAP_JZ.put("JZ002", "付费按集中分类日结-银行划款");
        REPORT_MAP_JZ.put("JZ003", "收费按集中分类日结-银保通");
        REPORT_MAP_JZ.put("JZ004", "付费按集中分类日结-银保通");
        REPORT_MAP_JZ.put("JZ005", "收费按集中分类日结-实时转账");
        REPORT_MAP_JZ.put("JZ006", "付费按集中分类日结-实时转账");
        REPORT_MAP_JZ.put("JZ007", "收费按集中分类日结-客户账户");
        REPORT_MAP_JZ.put("JZ008", "付费按集中分类日结-客户账户");
        REPORT_MAP_JZ.put("JZ009", "收费按集中分类日结-智能POS");
        REPORT_MAP_JZ.put("JZ010", "付费按集中分类日结-智能POS");
        REPORT_MAP_JZ.put("JZ011", "收费按集中分类日结-网络销售");
        REPORT_MAP_JZ.put("JZ012", "付费按集中分类日结-网络销售");
        REPORT_MAP_JZ.put("JZ014", "收费按集中分类日结-移动支付");
        REPORT_MAP_JZ.put("JZ015", "付费按集中分类日结-移动支付");
        REPORT_MAP_JZ.put("JZ016", "收费按集中分类日结-微信支付");
    }
}
