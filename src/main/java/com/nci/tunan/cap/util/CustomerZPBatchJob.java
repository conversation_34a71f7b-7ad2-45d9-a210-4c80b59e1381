package com.nci.tunan.cap.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.nci.tunan.cap.dao.IBankTextDao;
import com.nci.tunan.cap.impl.banktrans.service.IBankOfferZPForService;
import com.nci.tunan.cap.interfaces.model.bo.BankDiskConfigBO;
import com.nci.tunan.cap.interfaces.model.po.BankTextPO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
/**
 * @description 客户制盘处理类
 * @<NAME_EMAIL>
 * @date 2015-9-17 下午7:55:10
 * @.belongToModule 收付费-制盘
 */
public class CustomerZPBatchJob extends AbstractBatchJobForMod{
	/**
	 * 制盘servcie
	 */
    private IBankOfferZPForService bankOfferZPForService;
    /**
     * 制盘dao
     */
    private IBankTextDao iBankTextDao;
    /**
     * @description 批处理业务处理
     * @param jobSessionContext 任务会话上下文
     * @param start 起始位置
     * @param counts 查询数量
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
        List<JobData> jobDataList = new ArrayList<JobData>();
        bankOfferZPForService.creatBankTextMainBO();
        //@invalid 初始化bankTextMain
        List<BankDiskConfigBO> bankDiskConfigBOList = convertvBankDiskConfigBO(jobSessionContext);
        bankOfferZPForService.bankText(bankDiskConfigBOList,null,null,null);
        
        return jobDataList;
    }
    /**
     * 制盘分包
     * @param jobSessionContext 任务会话上下文
     * @return
     */
    public List<BankDiskConfigBO> convertvBankDiskConfigBO(JobSessionContext jobSessionContext){

        String derivTypeObj = null;
        List<BankDiskConfigBO> bankDiskConfigBOList = new ArrayList<BankDiskConfigBO>();
        List<JobParam> paramList = jobSessionContext.getParams();
        if (null != paramList) {
            for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
                JobParam param = iter.next();

                String paramName = param.getParamName();
                String paramValue = param.getParamValue();
                if ("derivType".equals(paramName)) {
                    derivTypeObj = paramValue;
                }
            }
        }

        //@invalid 创建bankDiskConfig
        BankTextPO bankText = new BankTextPO();
        //@invalid bankText.setChannelType(CodeCst.CHANNEL_TYPE_OTHER);
        bankText.setDerivType(derivTypeObj);
        List<BankTextPO> bankTextPO = iBankTextDao.findCustomerData(bankText);
        BankDiskConfigBO bankDiskConfigBO;
        for(BankTextPO po : bankTextPO){
            bankDiskConfigBO = new BankDiskConfigBO();
            bankDiskConfigBO.setArapFlag(po.getArapFlag());
            bankDiskConfigBO.setBankCode(po.getBankCode());
            //@invalid bankDiskConfigBO.setChannelType(po.getChannelType());
            bankDiskConfigBO.setDerivType(po.getDerivType());
            bankDiskConfigBO.setBankDiskType(new BigDecimal(CodeCst.BANK_DISK_TYPE__BANK));
            bankDiskConfigBOList.add(bankDiskConfigBO);
        }
        logger.info("<==保存 bankDiskConfig 成功===>");
        return bankDiskConfigBOList;
    }
    /**
     * @description 批处理执行
     * @param jobSessionContext 任务会话上下文
     * @param resultDatas 参数列表
     * @param modNum 取模数值
     */
    @Override
    public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> resultDatas, int modNum) {
        
        return null;
    }
    /**
     * @description 批处理异常处理
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void jobErrorAudit(JobSessionContext jobSessionContext, JobData jobData) {
        
        
    }
    /**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {
        
        
    }
    /**
     * 获取id名
     */
    @Override
    public String getIdName() {
        
        return null;
    }
    /**
     * 任务关闭
     */
    @Override
    public boolean isCanBeRun() {
        
        return true;
    }
    /**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        jobSessionContext.setStartNum(1);
        jobSessionContext.setEndNum(1);
        jobSessionContext.setModNum(1);//@invalid 批处理逻辑依赖关系需要
        return jobSessionContext;
    }
    /**
     * 停止任务
     */
    @Override
    public void jobStop() {
        
    }

    public IBankOfferZPForService getBankOfferZPForService() {
        return bankOfferZPForService;
    }

    public void setBankOfferZPForService(IBankOfferZPForService bankOfferZPForService) {
        this.bankOfferZPForService = bankOfferZPForService;
    }

    public IBankTextDao getiBankTextDao() {
        return iBankTextDao;
    }

    public void setiBankTextDao(IBankTextDao iBankTextDao) {
        this.iBankTextDao = iBankTextDao;
    }

}
