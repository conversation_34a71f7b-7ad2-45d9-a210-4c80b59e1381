package com.nci.tunan.cap.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.nci.tunan.cap.impl.banktrans.service.IBankTextDetailService;
import com.nci.tunan.cap.impl.common.service.IPremArapService;
import com.nci.tunan.cap.impl.counterchagre.service.ICapArapEnterService;
import com.nci.tunan.cap.interfaces.model.bo.BankTextDetailBO;
import com.nci.tunan.cap.interfaces.model.bo.PremArapBO;
import com.nci.tunan.cap.interfaces.model.bo.UnifiedCallbackBranchesBO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.util.WorkDateUtil;

/**
 * @description 返盘转实收状态待处理和失败的数据批处理
 * <AUTHOR>
 * @date 2015-9-17 下午7:55:10
 * @.belongToModule 收付费-返盘
 */
public class RecallServiceBatchJob extends AbstractBatchJobForMod{
	/**
	 * 应收应付service
	 */
	private IPremArapService premArapService;
	/**
	 * 返盘成功转实收service
	 */
	private ICapArapEnterService capArapEnterService;
	/**
	 * 盘文件详情service
	 */
	private IBankTextDetailService bankTextDetailService;
	
	public IPremArapService getPremArapService() {
		return premArapService;
	}

	public void setPremArapService(IPremArapService premArapService) {
		this.premArapService = premArapService;
	}

	public ICapArapEnterService getCapArapEnterService() {
		return capArapEnterService;
	}

	public void setCapArapEnterService(ICapArapEnterService capArapEnterService) {
		this.capArapEnterService = capArapEnterService;
	}
	
	public IBankTextDetailService getBankTextDetailService() {
		return bankTextDetailService;
	}

	public void setBankTextDetailService(
			IBankTextDetailService bankTextDetailService) {
		this.bankTextDetailService = bankTextDetailService;
	}

	 /**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData jobData) {
		
	}
	/**
	 * 获取id名字
	 */
	@Override
	public String getIdName() {
		return null;
	}
	/**
	 * 是否关闭
	 */
	@Override
	public boolean isCanBeRun() {
		return true;
	}
	 /**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		
		 jobSessionContext.setStartNum(1);
	     jobSessionContext.setEndNum(1);
	     jobSessionContext.setModNum(1);//批处理逻辑依赖关系需要
	     return jobSessionContext;
	}
    /**
     * @description 返盘转实收状态待处理和失败的数据批处理
     * @param jobSessionContext 任务会话上下文
     * @param start 起始位置
     * @param counts 查询数量
     */
	//
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start,int counts) {
		//@invalid 根据返盘转实收失败和待处理状态数据查询应收应付表数据
		List<UnifiedCallbackBranchesBO> unifiedCallList = new ArrayList<UnifiedCallbackBranchesBO>();
		List<PremArapBO> listBO = premArapService.findPremArapByUnitNumber();
		List<BigDecimal> listSeqNo = new ArrayList<BigDecimal>();
		for(PremArapBO bo :listBO){
			listSeqNo.add(bo.getSeqNo());
		}
		BankTextDetailBO bankTextDetailBO = new BankTextDetailBO();
		bankTextDetailBO.setSeqnoList(listSeqNo);
		List<BankTextDetailBO> detailBOList = bankTextDetailService.findBySeqnoBankTextDetail(bankTextDetailBO);
		UnifiedCallbackBranchesBO unifiedSuccess;
		List<PremArapBO> detailsToCash;
		for(BankTextDetailBO detailBO : detailBOList){
			BigDecimal seqNo = detailBO.getSeqNo();
			String rtnCode = CapVerify.ternaryOperator(detailBO.getRtnCode()==null,"",detailBO.getRtnCode());
			Date bankDealDate = CapVerify.ternaryOperator(detailBO.getBankDealDate()==null,WorkDateUtil.getWorkDate(),detailBO.getBankDealDate());
			
			for(PremArapBO premArapBO : listBO){
				if(seqNo.equals(premArapBO.getSeqNo())){
					unifiedSuccess = new UnifiedCallbackBranchesBO();
					detailsToCash = new ArrayList<PremArapBO>();
					detailsToCash.add(premArapBO);
					unifiedSuccess.setPablist(detailsToCash);
					unifiedSuccess.setFinishTime(WorkDateUtil.getWorkDate());
					unifiedSuccess.setFeeStatus(CodeCst.FEE_STATUS__FINISH);
					unifiedSuccess.setFundsRtnCode(rtnCode);
					unifiedSuccess.setBankDealDate(bankDealDate);
					unifiedSuccess.setPayrefNo(premArapBO.getPayrefNo());
					unifiedCallList.add(unifiedSuccess);
				}
			}
		}
		AppUser currentUser = null;
		try {
			currentUser = AppUserContext.getCurrentUser();
		} catch (RuntimeException e) {
			currentUser = new AppUser();
			currentUser.setRoleId("1");
			currentUser.setUserName("1");
			currentUser.setUserId(CodeCst.NUMBER_TEN_THOUSAND);
			currentUser.setOrganCode("860000");//@invalid 默认机构为总公司财务部
			AppUserContext.setCurrentUser(currentUser);
		}
		capArapEnterService.toUnifidCall(unifiedCallList);
		List<JobData> datas = new ArrayList<JobData>();
		return datas;
	}
	/**
	 * 停止任务
	 */
	@Override
	public void jobStop() {
		
		
	}
	 /**
     * @description 批处理执行
     * @param arg0 任务会话上下文
     * @param arg1 参数列表
     * @param arg2 取模数值
     */
    @Override
    public List<JobData> execute(JobSessionContext arg0, List<JobData> arg1, int arg2) {
        return arg1;
    }
    /**
     * @description 批处理异常处理
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
        
    }
	

}
