package com.nci.tunan.cap.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.nci.tunan.cap.dao.IPayrefConfigDao;
import com.nci.tunan.cap.interfaces.model.po.PayrefConfigPO;
import com.nci.udmp.framework.exception.FrameworkRuntimeException;
import com.nci.udmp.framework.spring.SpringContextUtils;
import com.nci.udmp.util.cache.CacheUtil;
/**
 * 
 * @description 收付费配置变量
 * <AUTHOR>
 * @date 2016-1-18 下午2:21:04
 * @.belongToModule 收付费-公共配置
 */
public class PayrefConfigUtil {
    /**
     * 缓存key
     */
    public  static final String CAP_CONFIG_CACHE_KEY = "cap_config_cash_key";
    /**
     * 支付类型
     */
    public static final String TYPE__BANK_EXCLUDE = "bankexclude";
    /**
     * 支付类型
     */
    public static final String CODE__BANK_EXCLUDE = "2";
    /**
     * 支付类型
     */
    public static final String TYPE__PAYMODE_AS_CASH = "20";
    /**
     * 类型变量
     */
    public static final String TYPE__FTP = "2";
    
    /**
     * 类型变量
     */
    public static final String TYPE__REPORT = "REPORT";
    
    
    /**
     *类型变量 
     */
    public static final String TYPE__FTP_WECHAT = "XWDWX_FTP";
    /**
     *类型变量-医保 
     */
    public static final String TYPE__MEDICARE = "medicare";
    /**
     * 类型变量
     */
    public static final String TYPE__FTP_VMS = "4";
    /**
     * 类型变量
     */
    public static final String TYPE__FTP_DS = "7";//日结专用
    /**
     * 类型变量
     */
    public static final String TYPE__SFTP_NC = "8";//交易流水编号专用
    /**
     * 类型变量
     */
    public static final String TYPE__ARAP_EBT = "5";
    /**
     * 类型变量
     */
    public static final String TYPE__MAIL = "1";
    /**
     * 类型变量
     */
    public static final String TYPE__SMBFILE = "3";
    /**
     * 类型变量
     */
    public static final String TYPE__AMOUNT_OVER = "30";
    /**
     * 类型变量
     */
    public static final String TYPE__ARAP_CHECK = "ARAP_CHECK";
    /**
     * 类型变量
     */
    public static final String CODE__GL_CHECK = "GL_CHECK";
    public static final String CODE__TEXTPATH_ORGANUPLOAD = "20";
    /**
     * 类型变量
     */
    public static final String TYPE__DEBIT = "40";
    /**
     * 类型变量
     */
    public static final String TYPE__DEBIT_URL = "URL";
    /**
     * 类型变量
     */
    public static final String TYPE__NONREAL_URL = "NONREAL_URL";
    
    /**
     * 类型变量
     */
    public static final String ZF_BANKCODE="zf-bankCode";//分公司集中制返盘查询银行使用
    /**
     * 类型变量
     */
    public static final String YTB_BKSTATUS="ytb_bkstatus";//区分银保通首期续期缴费使用
    /**
     * 类型变量
     */
    
    public static final String ZFRANCH_PATH = "br-zp-path";//分公司集中制盘存放路径（目前江苏机构使用）
    /**
     * 类型变量
     */
    public static final String FPRANCH_PATH = "br-fp-path";//分公司集中返盘存放路径（目前江苏机构使用）
    
    /**
     *类型变量-微信支付
     */
    public static final String TYPE__WECHAT = "wechat"; 
    /**
     *日结归档ftp配置
     */
    public static final String TYPE__REPORTFILING = "reportFiling";
    
    public static IPayrefConfigDao payrefConfigDao = (IPayrefConfigDao) SpringContextUtils.getBean("CAP_payrefDao");
    /**
     * 清除缓存方法
     */
    public static void clear() {
        CacheUtil.clear(PayrefConfigUtil.CAP_CONFIG_CACHE_KEY);
    }
    /**
     * 读取配置文件
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Map<String, String>> checkReload() {
        Map<String, Map<String, String>> map = null;
        boolean mark = false;
        try {
            map = (Map<String, Map<String, String>>) CacheUtil.get(CacheRegionName.CAP_MidtableCache.toString(), PayrefConfigUtil.CAP_CONFIG_CACHE_KEY);
        } catch (FrameworkRuntimeException e) {
            mark = true;
        }
        if (!mark && map != null) {
            return map;
        }
        mark = false;
        synchronized (PayrefConfigUtil.class) {
            try {
                map = (Map<String, Map<String, String>>) CacheUtil.get(CacheRegionName.CAP_MidtableCache.toString(), PayrefConfigUtil.CAP_CONFIG_CACHE_KEY);
            } catch (FrameworkRuntimeException e) {
                mark = true;
            }
            if (!mark && map != null) {
                return map;
            }
            try {
                List<PayrefConfigPO> lst = payrefConfigDao.findPayrefByConfigTypeAndCode("", "");
                map = new HashMap<String, Map<String, String>>();
                for (PayrefConfigPO po : lst) {
                    if(map.containsKey(po.getConfigType())){
                        map.get(po.getConfigType()).put(po.getConfigCode(), po.getConfigTypeNum());
                    }else{
                        Map<String, String> innermap = new HashMap<String, String>();
                        innermap.put(po.getConfigCode(), po.getConfigTypeNum());
                        map.put(po.getConfigType(), innermap);
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            CacheUtil.put(CacheRegionName.CAP_MidtableCache.toString(), PayrefConfigUtil.CAP_CONFIG_CACHE_KEY, map);
        }
        return map;
    }
    /**
     * 重新加载配置文件
     * @return
     */
    public static Map<String, Map<String, String>> reload() {
        Map<String, Map<String, String>> map = null;
        synchronized (PayrefConfigUtil.class) {
            CacheUtil.clear(PayrefConfigUtil.CAP_CONFIG_CACHE_KEY);
            try {
                List<PayrefConfigPO> lst = payrefConfigDao.findPayrefByConfigTypeAndCode("", "");
                map = new HashMap<String, Map<String, String>>();
                for (PayrefConfigPO po : lst) {
                    if(map.containsKey(po.getConfigType())){
                        map.get(po.getConfigType()).put(po.getConfigCode(), po.getConfigTypeNum());
                    }else{
                        Map<String, String> innermap = new HashMap<String, String>();
                        innermap.put(po.getConfigCode(), po.getConfigTypeNum());
                        map.put(po.getConfigType(), innermap);
                    }
                }
                CacheUtil.put(CacheRegionName.CAP_MidtableCache.toString(), PayrefConfigUtil.CAP_CONFIG_CACHE_KEY, map);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return map;
    }
    
    /**
     * @description 根据类型和code查询value值
     * @version 0.0.1
     * @title getValueByTypeCode
     * @<NAME_EMAIL>
     * @param type 类型
     * @param code code 编码
     * @return value
    */
    public static String getValueByTypeCode(String type, String code){
        Map<String, Map<String, String>> map = checkReload();
        if(map.containsKey(type)){
            if(map.get(type).containsKey(code)){
                return map.get(type).get(code);
            }
        }
        return null;
    }
    
    /**
     * @description 根据类型获取所有codevaluemap
     * @version 0.0.1
     * @title getValueByTypeCode
     * @<NAME_EMAIL>
     * @param type 类型
     * @return map
    */
    public static Map<String, String> getCodeValueByType(String type){
        Map<String, Map<String, String>> map = checkReload();
        if(map.containsKey(type)){
            return map.get(type);
        }
        return null;
    }
    
    /**
     * @description 查询配置项
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param configType 类型
     * @param configCode 编码
     * @return 配置项列表
    */
    public static List<PayrefConfigPO> getConfigList(String configType, String configCode){
        return payrefConfigDao.findPayrefByConfigTypeAndCode(configType, configCode);
    }
    
    /**
     * @description 插入或更新配置
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param po 配置po
     * @return 配置项
    */
    public static PayrefConfigPO saveConfigList(PayrefConfigPO po){
        if(po.getConfigId()!=null){
            return payrefConfigDao.updatePayrefConfig(po);
        }else{
            return payrefConfigDao.insertPayrefConfig(po);
        }
    }
    
    /**
     * @description 删除配置
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param po 配置po
     * @return 是否成功
    */
    public static boolean deleteConfigList(PayrefConfigPO po){
        if(po.getConfigId()!=null){
            return payrefConfigDao.deletePayrefConfig(po);
        }
        return false;
    }
    
    /**
     * @description 执行sql
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param sql 待执行的sql
    */
    public static void commonRun(String sql){
        payrefConfigDao.commonRun(sql);
    }
}
