package com.nci.tunan.cap.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.nci.tunan.cap.shhealthcare.impl.service.IHealthcareBackboardBatchService;
import com.nci.tunan.cap.shhealthcare.impl.ucc.IPremuimInfoUpUCC;
import com.nci.tunan.cap.shhealthcare.interfaces.model.bo.PremArapMedicalBO;
import com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.DateUtilsEx;

/** 
 * @description 上海医保保费信息同步上传批处理
 * <AUTHOR> 
 * @date 2020年1月16日 下午3:07:12 
 * @.belongToModule  上海医保收付费 
*/
public class HealthCarePremInfoBatch extends AbstractBatchJobForMod {
	/** 
	* @Fields healthcareBackboardBatchService : 保费信息同步上传service
	*/ 
	private IHealthcareBackboardBatchService healthcareBackboardBatchService;
	/** 
	* @Fields premuimInfoUpUCC : 保费信息同步上传ucc 
	*/ 
	private IPremuimInfoUpUCC premuimInfoUpUCC;
	/**
	 * @description 启动批处理
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext, java.util.List, int)
	 * @param arg0 作业中共享的全局配置信息
	 * @param resultDatas 执行结果
	 * @param arg2 批处理逻辑依赖关系
	 * @return 启动结果
	*/
	@Override
	public List<JobData> execute(JobSessionContext arg0, List<JobData> resultDatas,
			int arg2) {
		logger.debug("上海医保保费信息同步上传批处理-execute方法开始");
		try{
			if (resultDatas.size() > 0) {
				ShHealthcareBatchBO flagBO = (ShHealthcareBatchBO) resultDatas.get(0).getData().get("healthcareBatchBO");
				if ("2".equals(flagBO.getArapflag())) {
					//查询出所有上海付费数据对应insert上海医保收付费表
					PremArapMedicalBO premarapMedicalBO=new PremArapMedicalBO();
					//如果单个投保单号跑批处理，T_PREM_ARAP_MEDICAL插入一条数据
					premarapMedicalBO.setDerivType(flagBO.getDerivType());
					premarapMedicalBO.setRetryRemark(flagBO.getRetryRemark());
					// 投保单号集合
					List<String> applyCodeList = new ArrayList<String>();
					for (JobData jobData : resultDatas) {
						ShHealthcareBatchBO shHealthcareBatchBO = (ShHealthcareBatchBO) jobData.getData().get("healthcareBatchBO");
						applyCodeList.add(shHealthcareBatchBO.getApplyCode());
					}
					if (resultDatas.size() == 1) {
						premarapMedicalBO.setApplyCode(flagBO.getApplyCode());
					} else {
						premarapMedicalBO.setApplyCodeList(applyCodeList);
					}
					boolean flag = false;
					if (flagBO.getRetryRemark() != null && "1".equals(flagBO.getRetryRemark())) {
						flag = true;
						logger.info("上海医保退款保费信息上传补跑批处理，不再执行落库操作！");
					} else {
						List<PremArapMedicalBO> premArapMedicalBOList = premuimInfoUpUCC
								.queryAllFKPremuimInfo(premarapMedicalBO);
						flag = premuimInfoUpUCC.saveAllFKPremuimInfo(premArapMedicalBOList);
					}
					if (!flag) {
						logger.debug("上海医保保费信息插入付款数据失败");
					}else{
						for (JobData jobData : resultDatas) {
							ShHealthcareBatchBO shHealthcareBatchBO = (ShHealthcareBatchBO) jobData.getData().get("healthcareBatchBO");
							premuimInfoUpUCC.savePremuimInfo(shHealthcareBatchBO);
						}
					}
				}else{
					for (JobData jobData : resultDatas) {
						ShHealthcareBatchBO shHealthcareBatchBO = (ShHealthcareBatchBO) jobData.getData().get("healthcareBatchBO");
						//先判断数据是否有问题，有问题跳过此次循环
						boolean flag = premuimInfoUpUCC.checkDataByParam(shHealthcareBatchBO);
						if(!flag){
							continue;
						}
						premuimInfoUpUCC.savePremuimInfo(shHealthcareBatchBO);
					}
				}
			} else {
				logger.info("上海医保保费上传批处理未查询到可执行的数据！");
			}
			logger.info("上海医保保费信息同步上传批处理-execute方法结束");
		} catch (Exception e) {
			logger.info("上海医保保费信息同步上传批处理接口异常！"+ "==异常原因是===" + e.getMessage(), e);
		}
		return resultDatas;
	}

	/**
	 * @description 写入操作
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 作业中共享的全局配置信息
	 * @param arg1 作业信息
	*/
	@Override
	public void write(JobSessionContext arg0, JobData arg1) {
		
		
	}

	/**
	 * @description 获取批处理名称
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
	 * @return 批处理名称
	*/
	@Override
	public String getIdName() {
		
		return null;
	}

	/**
	 * @description 是否可执行
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
	 * @return 返回是否可执行
	*/
	@Override
	public boolean isCanBeRun() {
		
		return true;
	}

	/**
	 * @description 启动批处理
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 作业中共享的全局配置信息
	 * @param arg1 作业信息
	*/
	@Override
	public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
		
		
	}

	/**
	 * @description 中止批处理
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop() 
	*/
	@Override
	public void jobStop() {
		
		
	}

	/**
	 * @description 根据批处理入参查询符合条件的保费信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#query(com.nci.udmp.component.batch.context.JobSessionContext, long, int)
	 * @param arg0 作业中共享的全局配置信息
	 * @param arg1 定义值
	 * @param arg2 定义除数
	 * @return 查询结果
	*/
	@Override
	public List<JobData> query(JobSessionContext arg0, long arg1, int arg2) {
		logger.debug("上海医保保费信息同步上传批处理-query方法开始");
		ShHealthcareBatchBO shhealthcareBatchBO=new ShHealthcareBatchBO();
		shhealthcareBatchBO.setStart(arg1);
		shhealthcareBatchBO.setModNum(arg2);
		String applyCode = ""; // 投保单号
		String derivType = ""; // 业务来源类型
		List<JobParam> paramlist = arg0.getParams();
		if(CollectionUtils.isNotEmpty(paramlist)){
			for (JobParam jobParam : paramlist) {
				if (jobParam.getParamName().equals("arapflag")&& jobParam.getParamType().equals("string")) {
					String arapflag = jobParam.getParamValue();
					shhealthcareBatchBO.setArapflag(arapflag);
				}
				if(jobParam.getParamName().trim().equalsIgnoreCase("applyCode") 
						&& jobParam.getParamType().trim().equalsIgnoreCase("string")){
					applyCode = jobParam.getParamValue().trim();
				}
				if(jobParam.getParamName().trim().equalsIgnoreCase("derivType") 
						&& jobParam.getParamType().trim().equalsIgnoreCase("string")){
					derivType = jobParam.getParamValue().trim();
				}
			}
		}
		// 如果输入投保单号为空，则数据只查询近两周内的数据
		if ("".equals(applyCode) || applyCode == null) {
			Date date = WorkDateUtil.getWorkDate(AppUserContext.getCurrentUser());
			Date date1 = DateUtilsEx.addDay(date, -CodeCst.NUMBER_FOURTEEN);
			String dayDate = DateUtilsEx.date2String(date1, "yyyy-MM-dd");
			shhealthcareBatchBO.setTodayDate(dayDate);
		}
		shhealthcareBatchBO.setApplyCode(applyCode);
		List<JobData> jobDataList = new ArrayList<JobData>();
		if ("sqsk".equals(shhealthcareBatchBO.getArapflag())) {
			shhealthcareBatchBO.setDerivType("001"); // 首期
			List<ShHealthcareBatchBO> healthcareBatchBOs = healthcareBackboardBatchService
					.querysksignBatchService(shhealthcareBatchBO);
			if (healthcareBatchBOs != null && healthcareBatchBOs.size() > 0) {
				for (ShHealthcareBatchBO healthcareBatchBO : healthcareBatchBOs) {
					Map<String, Object> data = new HashMap<String, Object>();
					healthcareBatchBO.setArapflag("1");
					healthcareBatchBO.setDerivType("001");
					data.put("healthcareBatchBO", healthcareBatchBO);
					JobData jobData = new JobData();
					jobData.setData(data);
					jobDataList.add(jobData);
				}
			}
		} else if ("xqsk".equals(shhealthcareBatchBO.getArapflag())) {
			shhealthcareBatchBO.setDerivType("003"); // 续期
			List<ShHealthcareBatchBO> healthcareBatchBOs = healthcareBackboardBatchService
					.querysksignBatchService(shhealthcareBatchBO);
			if (healthcareBatchBOs != null && healthcareBatchBOs.size() > 0) {
				for (ShHealthcareBatchBO healthcareBatchBO : healthcareBatchBOs) {
					Map<String, Object> data = new HashMap<String, Object>();
					healthcareBatchBO.setArapflag("1");
					healthcareBatchBO.setDerivType("003");
					data.put("healthcareBatchBO", healthcareBatchBO);
					JobData jobData = new JobData();
					jobData.setData(data);
					jobDataList.add(jobData);
				}
			}
		} else if ("tk".equals(shhealthcareBatchBO.getArapflag())) {
			if (derivType == null || "".equals(derivType)) {
				derivType = "004"; // 保全
			}
			shhealthcareBatchBO.setDerivType(derivType);
			List<ShHealthcareBatchBO> healthcareBatchBOs = healthcareBackboardBatchService
					.querytksignBatchService(shhealthcareBatchBO);
			if (healthcareBatchBOs != null && healthcareBatchBOs.size() > 0) {
				for (ShHealthcareBatchBO healthcareBatchBO : healthcareBatchBOs) {
					Map<String, Object> data = new HashMap<String, Object>();
					healthcareBatchBO.setArapflag("2");
					healthcareBatchBO.setDerivType(derivType);
					data.put("healthcareBatchBO", healthcareBatchBO);
					JobData jobData = new JobData();
					jobData.setData(data);
					jobDataList.add(jobData);
				}
			}
		} else if ("tkbt".equals(shhealthcareBatchBO.getArapflag())) { // 退款补推，T_PREM_ARAP_MEDICAL已存在1条退款的数据【执行过tk】
			if (derivType == null || "".equals(derivType)) {
				derivType = "004"; // 保全
			}
			shhealthcareBatchBO.setDerivType(derivType);
			shhealthcareBatchBO.setRetryRemark("1"); // 标识 1-补推
			List<ShHealthcareBatchBO> healthcareBatchBOs = healthcareBackboardBatchService
					.querytksignBatchService(shhealthcareBatchBO);
			if (healthcareBatchBOs != null && healthcareBatchBOs.size() > 0) {
				for (ShHealthcareBatchBO healthcareBatchBO : healthcareBatchBOs) {
					Map<String, Object> data = new HashMap<String, Object>();
					healthcareBatchBO.setArapflag("2");
					healthcareBatchBO.setDerivType(derivType);
					healthcareBatchBO.setRetryRemark("1"); // 标识 1-补推
					data.put("healthcareBatchBO", healthcareBatchBO);
					JobData jobData = new JobData();
					jobData.setData(data);
					jobDataList.add(jobData);
				}
			}
		}
		logger.debug("上海医保保费信息同步上传批处理-query方法结束");
		return jobDataList;
	}

	/**
	 * @description 查询总数
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
	 * @param arg0 作业中共享的全局配置信息
	 * @return 查询结果
	*/
	@Override
	public JobSessionContext queryCounts(JobSessionContext arg0) {
		arg0.setStartNum(0);
		arg0.setEndNum(2);
		return arg0;
	}

	public IHealthcareBackboardBatchService getHealthcareBackboardBatchService() {
		return healthcareBackboardBatchService;
	}

	public void setHealthcareBackboardBatchService(
			IHealthcareBackboardBatchService healthcareBackboardBatchService) {
		this.healthcareBackboardBatchService = healthcareBackboardBatchService;
	}

	public IPremuimInfoUpUCC getPremuimInfoUpUCC() {
		return premuimInfoUpUCC;
	}

	public void setPremuimInfoUpUCC(IPremuimInfoUpUCC premuimInfoUpUCC) {
		this.premuimInfoUpUCC = premuimInfoUpUCC;
	}
}