package com.nci.tunan.cap.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.net.URLConnection;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.naming.NoNameCoder;
import com.thoughtworks.xstream.io.xml.XppDriver;

/** 
 * @description 输出xml和解析xml的工具类
 * <AUTHOR> 
 * @date 2020年1月16日 下午3:13:23
 * @.belongToModule  上海医保收付费 
*/
public class HealthCareXmlUtil {
	/** 
	* @Fields NB_SH_PATH : 读取上海医保配置文件路径
	*/ 
	public static final String NB_SH_PATH = "META-INF/cap/properties/shHealthCare.properties";
	/** 
	* @Fields NB_SH_URL : 读取上海医保地址
	*/ 
	public static final String NB_SH_URL = "shUrl";
	/** 
	* @Fields NB_SH_USER : 读取上海医保用户名
	*/ 
	public static final String NB_SH_USER = "userName";
	/** 
	* @Fields NB_SH_PASSWORD : 读取上海医保密码
	*/ 
	public static final String NB_SH_PASSWORD = "passWord";
	/** 
	* @Fields NB_SH_ELEURL : 读取上海医保电子保单链接
	*/ 
	public static final String NB_SH_ELEURL = "eleUrl";
	/** 
	* @Fields NB_SH_USER_BANK : 读取上海医保用户名-银行卡信息上传
	*/ 
	public static final String NB_SH_USER_BANK = "userNameBank";
	/** 
	* @Fields NB_SH_PASSWORD_BANK : 读取上海医保密码-银行卡信息上传
	*/ 
	public static final String NB_SH_PASSWORD_BANK = "passWordBank";
	/**
	 * @description java 转换成xml
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param obj
	 * @return 字符串
	*/
	public static String toXml(Object obj){
		XStream xstream = new XStream(new XppDriver(new NoNameCoder()));
		xstream.setMode(XStream.NO_REFERENCES);
		xstream.processAnnotations(obj.getClass()); //通过注解方式的，一定要有这句话
		String top = "<?xml version=\"1.0\" encoding=\"GBK\"?> \n";  
		return top+xstream.toXML(obj);
	}
    /**
     * @description 用传统的URL类进行请求
     * @version
     * @title
     * <AUTHOR>
     * @param xmlUrl
     * @param xmlInfo
     * @return 返回报文
    */
    public static String xmlPost(String xmlUrl, String xmlInfo) {
        String httpResults = "";
        String line = "";
        try {
            URL url = new URL(xmlUrl);
            URLConnection con = url.openConnection();
            con.setDoOutput(true);
            con.setRequestProperty("Pragma", "no-cache");
            con.setRequestProperty("Cache-Control", "no-cache");
            con.setRequestProperty("Content-Type", "text/xml");
            OutputStreamWriter out = new OutputStreamWriter(con.getOutputStream(), "GBK");
            out.write(new String(xmlInfo.getBytes("GBK"), "GBK"));
            out.flush();
            out.close();
            BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "GBK"));

            for (line = br.readLine(); line != null; line = br.readLine()) {
                httpResults = httpResults + line.toString();
            }
            br.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return httpResults;
    } 
    /**
     * @description 将传入xml文本转换成Java对象
     * @version
     * @title
     * <AUTHOR>
     * @param xmlStr
     * @param clazz
     * @return 实体类
    */
    @SuppressWarnings("unchecked")
    public static <T> T toBean(String xmlStr,Class<T> clazz) {
    	XStream xstream=new XStream();
		xstream.processAnnotations(clazz);
        T obj=(T)xstream.fromXML(xmlStr);
		return obj;            
    }
   
}
