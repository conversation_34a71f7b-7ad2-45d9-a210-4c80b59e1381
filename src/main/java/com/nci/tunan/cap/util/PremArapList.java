package com.nci.tunan.cap.util;

import com.thoughtworks.xstream.annotations.XStreamAlias;


/**
 * 
 * @description VSM请求报文对象
 * @<NAME_EMAIL>
 * @date 2016年6月17日 上午10:22:23
 * @.belongToModule 收付费-推送VMS数据
 */
@XStreamAlias("DataArea")
public class PremArapList {
    /** 
     * VMS报文头类
     */ 
    @XStreamAlias("DATA")
    private  PremArap premArap;

    public PremArap getPremArap() {
        return premArap;
    }

    public void setPremArap(PremArap premArap) {
        this.premArap = premArap;
    } 
    
}
