package com.nci.tunan.cap.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.slf4j.Logger;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationInfoBO;
import com.nci.tunan.cap.interfaces.model.vo.ManipulationInfoVO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.ftp.FtpUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * <AUTHOR>
 * @.belongToModule 收付费-微信对账批处理
 * @date 2020年4月27日上午10:27:14
 * @description 微信对账读取批处理
 */
public class WeChatBatchJob  extends AbstractBatchJobForMod {
    /**
     * 日志工具
     */
    protected Logger logger = LoggerFactory.getLogger();
	/**
	 * 实时对账文件读取批处理service
	 */
    private IManipulationInfoService manipulationInfoService;

	public IManipulationInfoService getManipulationInfoService() {
		return manipulationInfoService;
	}
	 
	public void setManipulationInfoService(
			IManipulationInfoService manipulationInfoService) {
		this.manipulationInfoService = manipulationInfoService;
	}

	/**
     * @description 批处理执行
     * @param jobSessionContext 任务会话上下文
     * @param resultDatas 参数列表
     * @param modNum 取模数值
     */
	@Override
	public List<JobData> execute(JobSessionContext jobSessionContext,
			List<JobData> resultDatas, int modNum) {
		return resultDatas;
	}

	/**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData jobData) {
		logger.debug("------------------write------------------------");
		List<JobParam> paramList = jobSessionContext.getParams();
		Date enterAccDate = DateUtilsEx.getTodayDate();
		int minusDays = CodeCst.NUMBER_THREE;
		if (null != paramList) {
			String paramName;
			String paramValue;
			for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
				JobParam param = iter.next();
				logger.info("param.getParamName()=    " + param.getParamName());
				logger.info("param.getParamCode()=    " + param.getParamCode());
				logger.info("param.getParamType()=    " + param.getParamType());
				logger.info("param.getParamValue()=    "
						+ param.getParamValue());
				paramName = param.getParamName();
				paramValue = param.getParamValue();
				// 批处理启动参数指定日期，则生产文件从指定日期开始
				if ("enterAccDate".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
						enterAccDate = DateUtilsEx.formatToDate(paramValue, "");
						enterAccDate = DateUtilsEx.addDay(enterAccDate, 1);
					}
				}
				if ("minusDays".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
						minusDays = Integer.parseInt(paramValue);
					}
				}
			}
			for (int i = 0; i <= minusDays; i++) {
				String date	=DateUtilsEx.formatToString(enterAccDate,"YYYYMMdd");
				enterAccDate = DateUtilsEx.addDay(enterAccDate, -1);
				this.toReadFile(date);
			}
			
		}
		
	}
	/**
     * @description 批处理异常处理
     * @param arg0 任务会话上下文
     * @param arg1 任务数据参数
     */
	@Override
	public void jobErrorAudit(JobSessionContext jobSessionContext,
			JobData jobData) {
	}
	/**
     * 获取id名字
     */
	@Override
	public String getIdName() {
		return null;
	}
	/**
     * 判断任务运行
     */
	@Override
	public boolean isCanBeRun() {
		 return true;
	}
	/**
     * 查询任务数量
     * @param jobSessionContext  任务上下文
     */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		    jobSessionContext.setStartNum(1);
	        jobSessionContext.setEndNum(1);
	        jobSessionContext.setModNum(1);//批处理逻辑依赖关系需要
	    return jobSessionContext;
	}
	  /**
    1.获取文件(IP地址,端口号,用户名,密码，实时对账信息文件上传路径) ----------数据库t_payref_config配置
    * （单独配置） 2.根据调用接口的日期获取所要解析的文件 3.解析.txt文件（流读取） 4.根据文件内容格式 || 进行拆分 ,
    * 将拆分内容放入list<map>中 5.进行批量插入t_manipulation_info表中（如何预防重复插入）
    * @param jobSessionContext 任务会话上下文
    * @param start 起始位置
    * @param counts 查询数量
    */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start,
			int counts) {
		logger.debug("---------------query-------------------------");
		List<JobData> datas = new ArrayList<JobData>();
		JobData data = new JobData();
		datas.add(data);
		return datas;
	}
	/**
     * 停止任务
     */
	@Override
	public void jobStop() {
	}
	/**   
    * @description 1.连接客户端前置机，获取微信推送文件。2.解析文件，保存数据到对账表中。3.返回解析文件列表
    * @param Date 对账日期
    * @param minusDays 对账期间
    */
    private void toReadFile(String date) {
        logger.debug("------------------toReadFile------------------------");
        try {
            FTPClient ftpClient = this.getFTPClient();
            FTPClient ftpClientBackUP = this.getFTPClientBackUP();
            InputStream inputStream = null;
            BufferedReader reader = null;

            if (ftpClient != null) {
                try {
                    // 读取数据的ftp服务器的对账目录
                    String reconciliationRead = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT,
                            CodeCst.TXT_FILE_P) + date + "/xhx/";
                    // 备份文件袋ftp服务器的对账目录
                    String reconciliationbackups = PayrefConfigUtil.getValueByTypeCode(
                            PayrefConfigUtil.TYPE__FTP_WECHAT, CodeCst.FILE_P_BAK)
                            + date.substring(0, CodeCst.NUMBER_FOUR) + "/" + date + "/";
                    logger.debug("-------FTP服务器地址----------" + reconciliationRead + reconciliationbackups);
                    ftpClient.changeWorkingDirectory(reconciliationRead);
                    FTPFile[] file = ftpClient.listFiles();
                    List<ManipulationInfoBO> infoBoList = new ArrayList<ManipulationInfoBO>();
                    boolean dirFlag = ftpClientBackUP.changeWorkingDirectory(reconciliationbackups);
                    if (!dirFlag) {
                        boolean dirFlag1 = ftpClientBackUP.changeWorkingDirectory(reconciliationbackups
                                + date.substring(0, CodeCst.NUMBER_FOUR) + "/");
                        if (!dirFlag1) {
                            ftpClientBackUP.changeWorkingDirectory("cap/tms/newWeChat/newWeChatBackup/");
                            ftpClientBackUP.makeDirectory(date.substring(0, CodeCst.NUMBER_FOUR));
                            ftpClientBackUP.changeWorkingDirectory(date.substring(0, CodeCst.NUMBER_FOUR) + "/");
                            ftpClientBackUP.makeDirectory(date);
                            ftpClientBackUP.changeWorkingDirectory(date);

                        } else {
                            boolean dirFlag2 = ftpClientBackUP.changeWorkingDirectory(date);
                            if (!dirFlag2) {
                                ftpClientBackUP.makeDirectory(date);
                                ftpClientBackUP.changeWorkingDirectory(date);
                            }
                        }
                    }
                    for (int i = 0; i < file.length; i++) {
                        String[] str = file[i].getName().split("\\.");
                        if ("txt".equals(str[str.length - 1])) {
                            inputStream = ftpClient.retrieveFileStream(file[i].getName());
                            reader = new BufferedReader(new InputStreamReader(inputStream));
                            /**
                             * 微信未区分收付类型,推送同一文件下,此处根据文件名区分收费类型,文件包含WXrefundbill-
                             * 付费,WXpaybill-收费
                             */
                            boolean pol = str[0].contains("WXrefundbill");

                            if (pol) {
                                String lineTxt = null;
                                // 读取每一行数据
                                int line = 0;
                                int lineskip = 1; // 设置跳过的行数
                                while ((lineTxt = reader.readLine()) != null) {

                                    // 跳过
                                    line++;
                                    if (line == lineskip)
                                        continue;
                                    // 根据 , 进行拆分数据
                                    String replaceAll = lineTxt.replaceAll("\\`", "");
                                    String[] strArr = replaceAll.split("\\,");
                                    Map<Integer, String> maps = new HashMap<Integer, String>();
                                    for (int s = 0; s < strArr.length; s++) {
                                        maps.put(s, strArr[s].toString());
                                    }
                                    infoBoList.add(getManipulationInfoAPBO(maps));
                                }
                            } else {
                                String lineTxt = null;
                                // 读取每一行数据
                                int line = 0;
                                int lineskip = 1; // 设置跳过的行数
                                while ((lineTxt = reader.readLine()) != null) {
                                    // 跳过
                                    line++;
                                    if (line == lineskip)
                                        continue;
                                    // 根据 , 进行拆分数据
                                    String replaceAll = lineTxt.replaceAll("\\`", "");
                                    String[] strArr = replaceAll.split("\\,");
                                    Map<Integer, String> maps = new HashMap<Integer, String>();
                                    for (int s = 0; s < strArr.length; s++) {
                                        maps.put(s, strArr[s].toString());
                                    }
                                    infoBoList.add(getManipulationInfoARBO(maps));
                                }
                            }

                            ftpClient.getReply();
                            logger.debug("-----文件备份到核心服务器------" + reconciliationbackups);
                            // 备份文件到核心服务器
                            inputStream = ftpClient.retrieveFileStream(file[i].getName());
                            ftpClientBackUP.storeFile(file[i].getName(), inputStream);
                            inputStream.close();
                            ftpClient.completePendingCommand();
                        }
                    }
                    List<ManipulationInfoBO> saveInfoBOList = new ArrayList<ManipulationInfoBO>();
                    // 因ftp目录中对账文件存在重复，故先判断该笔一笔交易唯一批次号是否存在，不存在才添加
                    ManipulationInfoBO batchIDBO;

                    List<String> batchID = new ArrayList<String>();
                    for (ManipulationInfoBO bo : infoBoList) {
                        if (!batchID.contains(bo.getBatchId())) {
                            batchIDBO = new ManipulationInfoBO();
                            batchIDBO.setBatchId(bo.getBatchId());
                            batchIDBO.setArapFlag(bo.getArapFlag());
                            int num = manipulationInfoService.findManipulationInfoTotal(batchIDBO);
                            // 137685-需求取消-52746-新微店网销集中收费日结自动对账功能 删除收费部分
                            if (num == 0 && (CodeCst.ARAP_FLAG__AP).equals(bo.getArapFlag())) {
                                saveInfoBOList.add(bo);
                                batchID.add(bo.getBatchId());
                            } else {
                                batchID.add(bo.getBatchId());
                            }
                        }
                    }
                    manipulationInfoService.batchSaveManipulationWeChat(saveInfoBOList);
                } catch (IOException eIO) {
                    eIO.printStackTrace();
                    logger.info(eIO.getMessage());
                    throw eIO;
                } catch (ParseException ePa) {
                    ePa.printStackTrace();
                    logger.info(ePa.getMessage());
                    throw ePa;
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.info(e.getMessage());
                    throw e;
                }
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (reader != null) {
                reader.close();
            }
            ftpClient.logout();
            ftpClientBackUP.logout();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	/**
	 * @description 获取前置机FTP客户端
	 * @param @return 参数
	 * @throws IOException 
	*/
	private FTPClient getFTPClient() throws IOException {
		try {
		String serverIp = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT, CodeCst.IP);
		String ftpServerPort = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT,CodeCst.PORT);
        String ftpUserName = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT, CodeCst.USESR);
		String ftpPassword = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT, CodeCst.PASSWORD);
		logger.debug("-----ftp服务地址"+serverIp+ftpServerPort+ftpUserName+ftpPassword);
        FtpServerConfig ftpServerConfig = new FtpServerConfig();
        ftpServerConfig.setFtpServerIp(serverIp);
        ftpServerConfig.setFtpServerPort(Integer.parseInt(ftpServerPort));
        ftpServerConfig.setFtpUserName(ftpUserName);
        ftpServerConfig.setFtpPassword(ftpPassword);
        FTPClient ftpClient = FtpUtil.loginFtpServer(ftpServerConfig);
        logger.debug("----ftpClient------"+ftpClient);
        if(ftpClient!=null){
        	logger.debug("----ftp客户端------"+ftpClient);
        }else{
        	logger.debug("----ftp客户端无法连接------");
        }
        
        ftpClient.setControlEncoding("GBK"); // 设置格式
        ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
        ftpClient.enterLocalPassiveMode();// 设置被动模式
		return ftpClient;
		} catch (IOException e) {
			logger.debug("e.printStackTrace()");
			e.printStackTrace();
			throw e;
		}       
	}
	/**
	 * @description 核心FTP服务器
	 * @param  参数
	 * @return 
	 * @throws IOException 
	*/
	private FTPClient getFTPClientBackUP() throws IOException {
		try {
			String serverIp = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_IP);
	        String ftpServerPort = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_PORT);
	        String ftpUserName = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_USER_NAME);
	        String ftpPassword = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_PASSWORD);		
			FtpServerConfig ftpServerConfig = new FtpServerConfig();
			ftpServerConfig.setFtpServerIp(serverIp);
			ftpServerConfig.setFtpServerPort(Integer.parseInt(ftpServerPort));
			ftpServerConfig.setFtpUserName(ftpUserName);
			ftpServerConfig.setFtpPassword(ftpPassword);
			FTPClient ftpClient = FtpUtil.loginFtpServer(ftpServerConfig);
			logger.debug("----getFTPClientBackUP ftpClient------"+ftpClient);
			ftpClient.setControlEncoding("GBK"); // 设置格式
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
			ftpClient.enterLocalPassiveMode();// 设置被动模式
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
			return ftpClient;
		} catch (IOException e) {
			logger.debug("getFTPClientBackUP-e.printStackTrace()");
			e.printStackTrace();
			throw e;
		}
	}
	/**
	 * @description 由于微信推送收费、付费报文内容不同，对推送的收费数据封装到BO对象列表中
	 * @param maps 数据集合
	 * @param @return 参数
	*/
	private ManipulationInfoBO getManipulationInfoARBO(Map<Integer, String> maps) throws ParseException{
		ManipulationInfoVO infoVo = new ManipulationInfoVO();
		Date parse = new SimpleDateFormat("yyyyMMdd").parse(maps.get(CodeCst.NUMBER_ZERO).toString());
		infoVo.setEnterAccDate(parse);//交易时间
		infoVo.setBusunessChannel(maps.get(CodeCst.NUMBER_ONE).toString());//业务渠道 
		infoVo.setTradeSn(maps.get(CodeCst.NUMBER_TWO).toString());//微信订单号
		infoVo.setBatchId(maps.get(CodeCst.NUMBER_THREE).toString());//商户订单号
		infoVo.setTransType(maps.get(CodeCst.NUMBER_FOUR).toString());//交易类型
		if("SUCCESS".equals(maps.get(CodeCst.NUMBER_FIVE).toString())){
			infoVo.setReturnCode("1");//交易状态 1 微信扣款成功
			infoVo.setReturnMsg(maps.get(CodeCst.NUMBER_FIVE).toString());
		}else{
			infoVo.setReturnCode("0");//交易状态 1 微信扣款成功
			infoVo.setReturnMsg(maps.get(CodeCst.NUMBER_FIVE).toString());
		}
		infoVo.setBankCode(maps.get(CodeCst.NUMBER_SIX).toString());//付款银行
		infoVo.setCurrency(maps.get(CodeCst.NUMBER_SEVEN).toString());//货币种类
		infoVo.setPremium(new BigDecimal(maps.get(CodeCst.NUMBER_EIGHT).toString()));//总金额
		infoVo.setServicerCode(maps.get(CodeCst.NUMBER_NINE).toString());//核心日结-微信默认ydwx
		// 区分对账类型，配置表T_MANIPULATION_DATA_TYPE， 微信对账码值-XWDWXDZ
		infoVo.setManipulationDataType("XWDWXDZ");
		// 字段不能为空，微信对账不适用，初始化值wx
		infoVo.setUserCode("wx");
		//微信收费报文没有传，为区分收费、付费，添加收费标志
		infoVo.setArapFlag(CodeCst.AR_AP__AR);

		ManipulationInfoBO infoBO = BeanUtils.copyProperties(ManipulationInfoBO.class, infoVo);
		return infoBO;
	}
	/**
	 * @description 由于微信推送收费、付费报文内容不同，对推送的付费数据封装到BO对象列表中
	 * @param maps 数据集合
	 * @param @return 参数
	*/
	private ManipulationInfoBO getManipulationInfoAPBO(Map<Integer, String> maps) throws ParseException{
		ManipulationInfoVO infoVo = new ManipulationInfoVO();
		Date parse = new SimpleDateFormat("yyyyMMdd").parse(maps.get(CodeCst.NUMBER_ZERO).toString());
		infoVo.setEnterAccDate(parse);//交易时间
		infoVo.setBusunessChannel(maps.get(CodeCst.NUMBER_ONE).toString());//业务渠道 
		infoVo.setTradeSn(maps.get(CodeCst.NUMBER_TWO).toString());//微信订单号
		infoVo.setBatchId(maps.get(CodeCst.NUMBER_THREE).toString());//商户订单号
		infoVo.setTransType(maps.get(CodeCst.NUMBER_FOUR).toString());//交易类型
		if(maps.get(5).toString()=="SUCCESS"||maps.get(CodeCst.NUMBER_FIVE).toString().equals("SUCCESS")){
			infoVo.setReturnCode("1");//交易状态 1 微信扣款成功
			infoVo.setReturnMsg(maps.get(CodeCst.NUMBER_FIVE).toString());
		}else{
			infoVo.setReturnCode("0");//交易状态 1 微信扣款成功
			infoVo.setReturnMsg(maps.get(CodeCst.NUMBER_FIVE).toString());
		}
		infoVo.setCurrency(maps.get(CodeCst.NUMBER_SIX).toString());//货币种类
		infoVo.setPremium(new BigDecimal(maps.get(CodeCst.NUMBER_SEVEN).toString()));//总金额
		/**
		 * 增加微信退款单号,商户退款单号,退款金额,退款原因      8 9 10 11 
		 */
		infoVo.setRefundTradeSn(maps.get(CodeCst.NUMBER_EIGHT).toString());//微信退款单号
		infoVo.setRefundBusinessCode(maps.get(CodeCst.NUMBER_NINE).toString());//商户退款单号
		infoVo.setRefundAmount(new BigDecimal(maps.get(CodeCst.NUMBER_TEN).toString()));//退款金额
		infoVo.setRefundReason(maps.get(CodeCst.NUMBER_ELEVEN).toString());//退款原因
		infoVo.setServicerCode(maps.get(CodeCst.NUMBER_TWELVE).toString());//核心日结-微信默认ydwx
		// 区分对账类型，配置表T_MANIPULATION_DATA_TYPE， 微信对账码值-XWDWXDZ
		infoVo.setManipulationDataType("XWDWXDZ");
		// 字段不能为空，微信对账不适用，初始化值wx
		infoVo.setUserCode("wx");
		//微信收费报文没有传，为区分收费、付费，添加收费标志
		infoVo.setArapFlag(CodeCst.AR_AP__AP);
		
		ManipulationInfoBO infoBO = BeanUtils.copyProperties(ManipulationInfoBO.class, infoVo);
		return infoBO;
	}
}
