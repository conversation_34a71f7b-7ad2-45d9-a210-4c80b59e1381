/**
 * @Fields field:field:{todo}
 */
package com.nci.tunan.cap.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Colour;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;

import com.nci.tunan.cap.impl.pointofsalesinfo.service.IManipulationInfoService;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationDetailsList;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationFmsinterface;
import com.nci.tunan.cap.interfaces.model.bo.ManipulationInfoBO;
import com.nci.tunan.cap.interfaces.model.vo.ManipulationInfoVO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.component.ftp.config.FtpServerConfig;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.ftp.FtpUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * <AUTHOR>
 * @.belongToModule 收付费—新微店对账表格生成批处理
 * @date 2015年5月21日 下午2:01:04
 * @description 新微店对账表格生成批处理
 */
public class ManiWeChatJob extends AbstractBatchJobForMod {
	/**
	 * 实时对账文件读取批处理service
	 */
	private IManipulationInfoService manipulationInfoService;
	/**
	 * 新微单对账操作类
	 */
	private ManipulationInfoWeChat manipulationInfoWeChat;
	/**
	 * 日志工具
	 */
	protected Logger logger = LoggerFactory.getLogger();

	public IManipulationInfoService getManipulationInfoService() {
		return manipulationInfoService;
	}

	public void setManipulationInfoService(
			IManipulationInfoService manipulationInfoService) {
		this.manipulationInfoService = manipulationInfoService;
	}

	public ManipulationInfoWeChat getManipulationInfoWeChat() {
		return manipulationInfoWeChat;
	}

	public void setManipulationInfoWeChat(
			ManipulationInfoWeChat manipulationInfoWeChat) {
		this.manipulationInfoWeChat = manipulationInfoWeChat;
	}
	/**
     * @description 批处理任务的具体操作
     * @<NAME_EMAIL>
     * @param jobSessionContext 上下文参数
     * @param resultDatas 需要处理的结果集
     * @return 处理后的结果集
     */
	@Override
	public List<JobData> execute(JobSessionContext jobSessionContext,
			List<JobData> resultDatas, int modNum) {
		return resultDatas;
	}
	 /**
     * @description 写入操作
     * @<NAME_EMAIL>
     * @param jobSessionContext 上下文对象
     * @param jobData 数据对象
     */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData jobData) {

		logger.debug("------------------write------------------------");
		List<JobParam> paramList = jobSessionContext.getParams();
		Date enterAccDate = DateUtilsEx.getTodayDate();
		int minusDays = CodeCst.NUMBER_THREE;
		if (null != paramList) {
			String paramName;
			String paramValue;
			for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
				JobParam param = iter.next();
				logger.info("param.getParamName()=    " + param.getParamName());
				logger.info("param.getParamCode()=    " + param.getParamCode());
				logger.info("param.getParamType()=    " + param.getParamType());
				logger.info("param.getParamValue()=    "
						+ param.getParamValue());
				paramName = param.getParamName();
				paramValue = param.getParamValue();
				// 批处理启动参数指定日期，则生产文件从指定日期开始
				if ("enterAccDate".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
						enterAccDate = DateUtilsEx.formatToDate(paramValue, "");
						enterAccDate = DateUtilsEx.addDay(enterAccDate, 1);
					}
				}
				if ("minusDays".equals(paramName)) {
					if (CapVerify.objIsNotNull(paramValue, true)) {
						minusDays = Integer.parseInt(paramValue);
					}
				}
			}
		}
		ManipulationInfoVO infoVO = new ManipulationInfoVO();
		infoVO.setPayType("71");
		infoVO.setServicerCode("YDWX");
		infoVO.setReturnCode("1");

		// 因资金的文件存在：交易多天后才会给出交易结果的情况，故这里增加生产的表格天数
		for (int i = 1; i <= minusDays; i++) {
			enterAccDate = DateUtilsEx.addDay(enterAccDate, -1);
			infoVO.setStartDate(DateUtilsEx.formatToString(enterAccDate,
					"YYYY-MM-dd"));
			infoVO.setEndDate(DateUtilsEx.formatToString(enterAccDate,
					"YYYY-MM-dd"));
			try {// 收费对账文件
				infoVO.setArapFlag(CodeCst.AR_AP__AR);
				ManipulationInfoBO infoARBos = BeanUtils.copyProperties(
						ManipulationInfoBO.class, infoVO);
				// 1.查询对账文件导入明细表
				List<ManipulationInfoBO> arManiInfoBOList = manipulationInfoService
						.findAllManipulationWeChat(infoARBos);

				// 2.查询实收付表 网销日结数据
				List<ManipulationInfoBO> arCashDetailBOList = manipulationInfoService
						.findAllCashDetailWeChat(infoARBos);

				logger.debug("------------------新微店对账封装日结、对账数据到map集合  ------------------------");

				// 3.组装数据
				Map<String, ManipulationDetailsList> mmap = manipulationInfoWeChat
						.getCheckList(arCashDetailBOList, arManiInfoBOList,
								infoVO);

				// 4 生成文件
				logger.debug("------------------新微店对账开始生成对账文件  ------------------------");
				this.doDownloadManipulationInfoImpl(mmap, infoVO);

			} catch (Exception e) {
				e.printStackTrace();
			}

		}

	}
	/**
     * @description 批处理异常处理
     * @param arg0 任务会话上下文
     * @param arg1 任务数据参数
     */
	@Override
	public void jobErrorAudit(JobSessionContext jobSessionContext,
			JobData jobData) {

	}
	/**
     *@description 获取id名字
     */
	@Override
	public String getIdName() {
		return null;
	}
	/**
     *@description 判断任务运行
     */
	@Override
	public boolean isCanBeRun() {
		return true;
	}
	 /**
     * @description 查询任务数量
     * @param jobSessionContext  任务上下文
     */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		logger.debug("--------------queryCounts-------------------");
		jobSessionContext.setStartNum(1);
		jobSessionContext.setEndNum(1);
		jobSessionContext.setModNum(1);// 批处理逻辑依赖关系需要
		return jobSessionContext;
	}
    /** @description 获取文件(IP地址,端口号,用户名,密码，实时对账信息文件上传路径) 
    * @param jobSessionContext 任务会话上下文
    * @param start 起始位置
    * @param counts 查询数量
    */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start,
			int counts) {
		logger.debug("---------------query-------------------------");
		List<JobData> datas = new ArrayList<JobData>();
		JobData data = new JobData();
		datas.add(data);
		return datas;
	}
	/**
     *@description 停止任务
     */
	@Override
	public void jobStop() {

	}

	/**
	 * @description 生成对账差异清单
	 * @param mmap 封装新微店对账差异数据
	 * @param infoVO 对账文件明细差异
	 * @param @throws Exception 参数
	 */
	private void doDownloadManipulationInfoImpl(
			Map<String, ManipulationDetailsList> mmap, ManipulationInfoVO infoVO)
			throws IOException {
		logger.debug("------------------开始生成对账差异清单  ------------------------");

		String excelName = "Wxpaybill_" + infoVO.getArapFlag() + "_"
				+ infoVO.getStartDate() + ".xls";
		FTPClient ftpClient = this.getFTPClient();
		InputStream local = null;
		File file = new File(excelName);
		WritableWorkbook wwb = Workbook.createWorkbook(file);
		try {
			// 设置格式字体
			WritableFont wf = new jxl.write.WritableFont(WritableFont.ARIAL,
					CodeCst.NUMBER_FIFTEEN, WritableFont.BOLD, false,
					UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
			WritableCellFormat wcf = new WritableCellFormat(wf);
			wcf.setVerticalAlignment(VerticalAlignment.CENTRE);
			wcf.setAlignment(Alignment.CENTRE);
			// ************** 往工作表中添加数据 *****************//*
			int rowNum = 0; // 初始化行数
			int workAreaNum = 0; // 工作区
			WritableSheet ws = null;
			Map<String, Object> maps = new HashMap<String, Object>();

			workAreaNum = workAreaNum++;
			// 创建Excel工作表 指定名称和位置
			ws = wwb.createSheet("新微店对账差异明细", workAreaNum);
			workAreaNum = 1;
			maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO, maps, ws);
			workAreaNum = (Integer) maps.get("workAreaNum");
			rowNum = (Integer) maps.get("rowNum");
			ws = (WritableSheet) maps.get("ws");
			for (String key : mmap.keySet()) {
				// 对账日结
				String enterAccDate = key;
				// 对账明细
				ManipulationDetailsList list = mmap.get(enterAccDate);

				rowNum = rowNum + 1;
				ws.addCell(new Label(0, rowNum, "日期"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(1, rowNum, key));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);

				ws.addCell(new Label(2, rowNum, "收费方式:"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(CodeCst.NUMBER_THREE, rowNum, "网络销售"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);

				// 差异明细
				BigDecimal endOfDayNum = list.getEndOfDayNum();
				BigDecimal endTotalPremium = list.getEndTotalPremium();
				if (endOfDayNum == null) {
					endOfDayNum = new BigDecimal(0);
				}
				if (endTotalPremium == null) {
					endTotalPremium = new BigDecimal(0);
				}
				maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO,
						maps, ws);
				workAreaNum = (Integer) maps.get("workAreaNum");
				rowNum = (Integer) maps.get("rowNum");
				ws = (WritableSheet) maps.get("ws");
				rowNum = rowNum + 1;
				ws.addCell(new Label(0, rowNum, "新微店网销对账"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(1, rowNum, "笔数"));
				ws.setColumnView(1, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(2, rowNum, "总金额"));
				ws.setColumnView(2, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(CodeCst.NUMBER_THREE, rowNum, "明细"));
				ws.setColumnView(CodeCst.NUMBER_THREE,
						CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(CodeCst.NUMBER_FOUR, rowNum, "金额"));
				ws.setColumnView(CodeCst.NUMBER_FOUR,
						CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(CodeCst.NUMBER_FIVE, rowNum, "管理机构"));
				ws.setColumnView(CodeCst.NUMBER_FIVE,
						CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(CodeCst.NUMBER_SIX, rowNum, "备注"));
				ws.setColumnView(CodeCst.NUMBER_SIX, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new Label(CodeCst.NUMBER_SEVEN, rowNum, "数补处理"));
                ws.setColumnView(CodeCst.NUMBER_SEVEN, CodeCst.NUMBER_TWENTY_FIVE);
				maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO,
						maps, ws);
				workAreaNum = (Integer) maps.get("workAreaNum");
				rowNum = (Integer) maps.get("rowNum");
				ws = (WritableSheet) maps.get("ws");
				rowNum = rowNum + 1;

				ws.addCell(new Label(0, rowNum, "微信支付（网络销售）日结"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new jxl.write.Number(1, rowNum, CapVerify
						.ternaryOperator(null == endOfDayNum, BigDecimal.ZERO,
								endOfDayNum).doubleValue()));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new jxl.write.Number(2, rowNum, CapVerify
						.ternaryOperator(null == endTotalPremium,
								BigDecimal.ZERO, endTotalPremium).doubleValue()));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO,
						maps, ws);
				workAreaNum = (Integer) maps.get("workAreaNum");
				rowNum = (Integer) maps.get("rowNum");
				ws = (WritableSheet) maps.get("ws");
				rowNum = rowNum + 1;
				ws.addCell(new Label(0, rowNum, "对账文件"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new jxl.write.Number(1, rowNum, CapVerify
						.ternaryOperator(null == list.getReconciliationNum(),
								BigDecimal.ZERO, list.getReconciliationNum())
						.doubleValue()));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new jxl.write.Number(2, rowNum, CapVerify
						.ternaryOperator(null == list.getRecTotalPremium(),
								BigDecimal.ZERO, list.getRecTotalPremium())
						.doubleValue()));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO,
						maps, ws);
				workAreaNum = (Integer) maps.get("workAreaNum");
				rowNum = (Integer) maps.get("rowNum");
				ws = (WritableSheet) maps.get("ws");
				rowNum = rowNum + 1;
				ws.addCell(new Label(0, rowNum, "差额"));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new jxl.write.Number(1, rowNum, CapVerify
						.ternaryOperator(null == list.getDifferenceNum(),
								BigDecimal.ZERO, list.getDifferenceNum())
						.doubleValue()));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				ws.addCell(new jxl.write.Number(2, rowNum, CapVerify
						.ternaryOperator(null == list.getDifTotalPremium(),
								BigDecimal.ZERO, list.getDifTotalPremium())
						.doubleValue()));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
				List<ManipulationFmsinterface> manFmslist = list
						.getManFmslist();
				for (int i = 0; i < manFmslist.size(); i++) {
					// 集合
					ManipulationFmsinterface maniFms = manFmslist.get(i);
					maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO,
							maps, ws);
					workAreaNum = (Integer) maps.get("workAreaNum");
					rowNum = (Integer) maps.get("rowNum");
					ws = (WritableSheet) maps.get("ws");
					rowNum = rowNum + 1;
					// 明细
					ws.addCell(new Label(CodeCst.NUMBER_THREE, rowNum,
							CapVerify.ternaryOperator(
									null == maniFms.getCertifyNo(), "",
									maniFms.getCertifyNo())));
					ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
					// 金额
					ws.addCell(new jxl.write.Number(CodeCst.NUMBER_FOUR,
							rowNum, Double.valueOf(CapVerify.ternaryOperator(
									null == maniFms.getPremium(), "",
									maniFms.getPremium()).toString())));
					ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
					// 管理机构
					if (null != maniFms.getOrganName()
							&& !"".equals(maniFms.getOrganName())) {
						ws.addCell(new Label(CodeCst.NUMBER_FIVE, rowNum,
								maniFms.getOrganName()));
					} else {
						ws.addCell(new Label(CodeCst.NUMBER_FIVE, rowNum, "86"));
					}
					ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
					// 备注
					ws.addCell(new Label(CodeCst.NUMBER_SIX, rowNum, CapVerify
							.ternaryOperator(null == maniFms.getRemarks(), "",
									maniFms.getRemarks())));
					ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
					//问题单号
                    ws.addCell(new Label(CodeCst.NUMBER_SEVEN, rowNum, CapVerify.ternaryOperator(
                            null == maniFms.getItsmNo(), "", maniFms.getItsmNo())));
                    ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);

				}
				maps = toExcelSheet(rowNum, workAreaNum, wwb, wcf, infoVO,
						maps, ws);
				workAreaNum = (Integer) maps.get("workAreaNum");
				rowNum = (Integer) maps.get("rowNum");
				ws = (WritableSheet) maps.get("ws");
				// 空行
				rowNum = rowNum + 1;
				ws.addCell(new Label(0, rowNum, ""));
				ws.setColumnView(0, CodeCst.NUMBER_TWENTY_FIVE);
			}
			wwb.write();
			wwb.close();
			local = new FileInputStream(file);
			 String reconciliationbackups =
			PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP_WECHAT,
		    CodeCst.EXCEL_FILE_P) + infoVO.getStartDate();
			boolean flag = ftpClient
					.changeWorkingDirectory(reconciliationbackups);
			if (!flag) {
				ftpClient.makeDirectory(reconciliationbackups);
				ftpClient.changeWorkingDirectory(reconciliationbackups);
			}
			ftpClient.storeFile(new String(excelName.getBytes("utf-8"),
					"iso-8859-1"), local);
			local.close();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			throw new BizException("新网店网销对账差异明细Excel导出失败！");
		} catch (IOException e) {
			e.printStackTrace();
			throw new BizException("新网店网销对账差异明细Excel导出失败！");
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException("新网店网销对账差异明细Excel导出失败！");
		} finally {
			FtpUtil.logoutFtpServer(ftpClient);
		}

	}

	/**
	 * @description 创建Excel工作表 指定名称和位置
	 * @param rowNum 行号
	 * @param workAreaNum 工作区
	 * @param wwb Excel创建对象
	 * @param wcf Excel创建对象
	 * @param infoVO 对账入参
	 * @param maps 封装差异数据对象
	 * @param ws 表格对账
	 */
	private Map<String, Object> toExcelSheet(int rowNum, int workAreaNum,
			WritableWorkbook wwb, WritableCellFormat wcf,
			ManipulationInfoVO infoVO, Map<String, Object> maps,
			WritableSheet ws) throws Exception, WriteException {
		if (rowNum % CodeCst.excelMax == 0) {
			if (rowNum != 0) {
				workAreaNum = workAreaNum + 1;
				// 创建Excel工作表 指定名称和位置
				ws = wwb.createSheet("新微店对账差异明细(" + workAreaNum + ")",
						workAreaNum);
				rowNum = 0;
			}
			String arapFlag = "收费";
			if (CodeCst.AR_AP__AP.equals(infoVO.getArapFlag())) {
				arapFlag = "付费";
			}
			// 创建Excel工作表 指定名称和位置
			// 列,行
			// 1最左上角列号 2最左上角行号 3最右上角列号 最右下角行号
			ws.addCell(new Label(0, rowNum, "新微店网销对账清单", wcf));
			ws.mergeCells(0, rowNum, CodeCst.NUMBER_SEVEN, rowNum);
			ws.setColumnView(0, CodeCst.NUMBER_TWO_HUNDRED);
			rowNum = rowNum + 1;
			ws.addCell(new Label(0, rowNum, "对账起期:" + infoVO.getStartDate()));
			ws.mergeCells(0, rowNum, CodeCst.NUMBER_THREE, rowNum);
			ws.setColumnView(0, CodeCst.NUMBER_ONE_HUNDRED);
			ws.addCell(new Label(CodeCst.NUMBER_FOUR, rowNum, "对账止期:"
					+ infoVO.getEndDate()));
			ws.mergeCells(CodeCst.NUMBER_FOUR, rowNum, CodeCst.NUMBER_SEVEN,
					rowNum);
			ws.setColumnView(CodeCst.NUMBER_FOUR, CodeCst.NUMBER_ONE_HUNDRED);
		}
		maps.put("workAreaNum", workAreaNum);
		maps.put("rowNum", rowNum);
		maps.put("ws", ws);
		return maps;
	}

	/**
	 * @description 获取FTP客户端
	 * @param @return 参数
	 * @throws IOException
	 */
	private FTPClient getFTPClient() throws IOException {
		try {

			String serverIp = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_IP);
	        String ftpServerPort = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_SERVER_PORT);
	        String ftpUserName = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_USER_NAME);
	        String ftpPassword = PayrefConfigUtil.getValueByTypeCode(PayrefConfigUtil.TYPE__FTP, CodeCst.FTP_PASSWORD);		
			FtpServerConfig ftpServerConfig = new FtpServerConfig();
			ftpServerConfig.setFtpServerIp(serverIp);
			ftpServerConfig.setFtpServerPort(Integer.parseInt(ftpServerPort));
			ftpServerConfig.setFtpUserName(ftpUserName);
			ftpServerConfig.setFtpPassword(ftpPassword);
			FTPClient ftpClient = FtpUtil.loginFtpServer(ftpServerConfig);
			ftpClient.setControlEncoding("GBK"); // 设置格式
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
			ftpClient.enterLocalPassiveMode();// 设置被动模式
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);// 登陆后设置文件类型为二进制否则可能导致乱码文件无法打开
			return ftpClient;
		} catch (IOException e) {
			throw e;
		}

	}
}
