package com.nci.tunan.cap.util;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.Properties;
import java.util.ArrayList;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.git.common.logs.Logger;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.nci.tunan.cap.interfaces.model.po.ReportDownloadCommPO;

/**
 * @description 文件压缩工具类
 * <AUTHOR>
 * @date 2017-12-26 下午5:38:21
 * @.belongToModule 收付费-文件压缩工具类
 */
public class ZipUtils {

    /**
     * @description 压缩文件具体方法
     * @version
     * @title
     * <AUTHOR>
     * @param f
     *            文件
     * @param baseDir
     *            根目录
     * @param zos
     *            zip流
     */
    static void compress(File f, String baseDir, ZipOutputStream zos) {
        if (!f.exists()) {
            com.git.common.logs.Logger.error("待压缩的文件目录或文件" + f.getName() + "不存在");
            return;
        }
        File[] fs = f.listFiles();
        BufferedInputStream bis = null;
        byte[] bufs = new byte[CodeCst.NUMBER_ONE_THOUSAND_AND_TWENTY_FOUR * CodeCst.NUMBER_TEN];
        FileInputStream fis = null;

        try {
            for (int i = 0; i < fs.length; i++) {
                String fName = fs[i].getName();
                Logger.info("压缩：" + baseDir + fName);
                if (fs[i].isFile()) {
                    ZipEntry zipEntry = new ZipEntry(baseDir + fName);
                    zos.putNextEntry(zipEntry);
                    //@invalid 读取待压缩的文件并写进压缩包里
                    fis = new FileInputStream(fs[i]);
                    bis = new BufferedInputStream(fis, CodeCst.NUMBER_ONE_THOUSAND_AND_TWENTY_FOUR * CodeCst.NUMBER_TEN);
                    int read = 0;
                    while ((read = bis.read(bufs, 0, CodeCst.NUMBER_ONE_THOUSAND_AND_TWENTY_FOUR * CodeCst.NUMBER_TEN)) != -1) {
                        zos.write(bufs, 0, read);
                    }
                } else if (fs[i].isDirectory()) {
                    compress(fs[i], baseDir + fName + "/", zos);
                }
            } //@invalid end for
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            //@invalid 关闭流
            try {
                if (null != bis) {
                    bis.close();
                }
                if (null != fis) {
                    fis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * @description 压缩文件
     * @version
     * @title
     * <AUTHOR>
     * @param sourceFilePath
     *            需要压缩文件路径
     * @param baseDir
     *            压缩后文件根目录名称
     */
    public static void creatZipFile(String sourceFilePath, String baseDir) {
        File sourceDir = new File(sourceFilePath);
        File zipFile = new File(sourceFilePath + ".zip");
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(new FileOutputStream(zipFile));
            String dir = baseDir + "/";
            compress(sourceDir, dir, zos);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
    /**
     * @description 将月结清单Excel文件推送至SAP财务系统
     * @version
     * @title
     * <AUTHOR>
     * @param filePath
     *            需要推送的数据
     */
    public static void fileToSAP(ReportDownloadCommPO commPO, String glfPath, String yfwfPath, String yslxPath){
        String serverIp = PayrefConfigUtil.getValueByTypeCode(CodeCst.PAYREF_CONFIG_TYPE_YJQD, 
                CodeCst.SFTP_YJQD_SERVER_IP);// 获取服务器ip
        int sftpServerPort = Integer.parseInt(PayrefConfigUtil.getValueByTypeCode(CodeCst.PAYREF_CONFIG_TYPE_YJQD, 
                CodeCst.SFTP_YJQD_SERVER_PORT)); // 获取服务器端口
        String sftpUserName = PayrefConfigUtil.getValueByTypeCode(CodeCst.PAYREF_CONFIG_TYPE_YJQD, 
                CodeCst.SFTP_YJQD_USER_NAME); // 获取服务器用户名
        String sftpPassword = PayrefConfigUtil.getValueByTypeCode(CodeCst.PAYREF_CONFIG_TYPE_YJQD, 
                CodeCst.SFTP_YJQD_PASSWORD); // 获取服务器登录密码
        Logger.info("获取sftp文件服务器成功,IP地址为:" + serverIp);
        String remoteCatalog = PayrefConfigUtil.getValueByTypeCode(CodeCst.PAYREF_CONFIG_TYPE_YJQD, 
                CodeCst.SFTP_YJQD_PATH); // sftp服务器文件存放目录
        //拼写整个文件路径
        remoteCatalog = createDateFile(remoteCatalog);
        String glfRemoteCatalog = remoteCatalog + "/glf/";
        String yfwfRemoteCatalog = remoteCatalog + "/yfwf/";
        String yslxRemoteCatalog = remoteCatalog + "/yslx/";
        String statisticsMonth = commPO.getString("firstday").replace("-", "").substring(0, CodeCst.NUMBER_SIX); // 统计报表月份
        String workDay = commPO.getString("workday"); // 统计日期
        String fileName;
        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(sftpUserName,serverIp,sftpServerPort);
            session.setPassword(sftpPassword);
            Properties config = new Properties();
            config.setProperty("StrictHostKeyChecking","no");
            session.setConfig(config);
            session.connect();
            ChannelSftp sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();
            
            sftp.mkdir(remoteCatalog);//创建日期目录
            sftp.mkdir(glfRemoteCatalog);//创建管理费目录
            sftp.mkdir(yfwfRemoteCatalog);//创建应付未付目录
            sftp.mkdir(yslxRemoteCatalog);//创建应收利息目录
            
            List<File> glfFiles = getXlsxFiles(glfPath);
            List<File> yfwfFiles = getXlsxFiles(yfwfPath);
            List<File> yslxFiles = getXlsxFiles(yslxPath);
            for (File file : glfFiles) {
                if(file.getName().contains(workDay)){
                    sftp.put(file.getAbsolutePath(), glfRemoteCatalog + File.separator + file.getName());
                }
            }
            for (File file : yfwfFiles) {
                fileName = "86_yfwf" + statisticsMonth + "_" + workDay + ".xlsx";
                sftp.put(file.getAbsolutePath(), yfwfRemoteCatalog + File.separator + fileName);
            }
            for (File file : yslxFiles) {
                fileName = "86_yslx" + statisticsMonth + "_" + workDay + ".xlsx";
                sftp.put(file.getAbsolutePath(), yslxRemoteCatalog + File.separator + fileName);
            }
            sftp.disconnect();
            session.disconnect();
            Logger.info("文件存放SFTP服务器成功，地址为:" + remoteCatalog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static List<File> getXlsxFiles(String folderPath) {
        // 使用 File 类或其他文件操作类遍历文件夹
        File folder = new File(folderPath);
        List<File> files = new ArrayList<File>();

        // 递归遍历文件夹及其子文件夹
        for (File file : folder.listFiles()) {
            files.add(file);
        }
        return files;
    }
    
    public static String createDateFile(String directoryPath) {
        // 获取当前日期和时间，格式为 "yyyyMMdd_HHmmss"
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateFile = dateFormat.format(new Date());
        // 组合文件完整路径
        String filePath = directoryPath + "/" + dateFile ;
        return filePath;
    }
}
