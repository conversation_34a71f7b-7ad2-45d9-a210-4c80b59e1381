package com.nci.tunan.cap.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PushbackInputStream;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;

import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbException;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileInputStream;
import jcifs.smb.SmbFileOutputStream;

/**
 * @description 共享目录操作工具类
 * @<NAME_EMAIL>
 * @date 2016-5-27 下午5:18:30
 * @.belongToModule 收付费-工具类
 */
public class JcifsUtil {

    /**
     * SMBFILE_SEPARATOR :smb共享目录分隔符
     */
    public static final String SMBFILE_SEPARATOR = "/";

    /**
     * @description 从smbMachine读取文件列表
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param smbMachine
     *            共享机器的文件,如smb://xxx:xxx@**********/temp8/new/rec/return/
     *            return_process/,xxx:xxx是共享机器的用户名密码,必须已斜杠(/)结束
     * @return 文件名
     */
    /**
     * @description 从smbMachine读取文件列表
     * <AUTHOR>
     * @param serverIP
     *            共享目录IP
     * @param username
     *            共享目录账号
     * @param password
     *            共享目录密码
     * @param smbDir
     *            共享目录新建文件存放路径
     * @return 文件名
     */
    public static List<String> getFileNamesFromSmb(String serverIP, String username, String password, String smbDir) {

        SmbFile[] files = null;
        try {
            SmbFile file = connectSMB(serverIP, username, password, smbDir, null);
            file.connect();
            if (file.isDirectory()) {
                files = file.listFiles();
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (SmbException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<String> fileNames = new ArrayList<String>();
        if(null!=files&&files.length>0){
        	  for (SmbFile smbFile : files) {
                  //@invalid if(smbFile.getName().indexOf(".f")!=-1||smbFile.getName().indexOf(".txt")!=-1||smbFile.getName().indexOf(".e")!=-1){
                  fileNames.add(smbFile.getName());
                  //@invalid }
              }
        }
      
        return fileNames;
    }

    /**
     * @description 连接smb服务器
     * <AUTHOR>
     * @param serverIP
     *            共享目录IP
     * @param username
     *            共享目录账号
     * @param password
     *            共享目录密码
     * @param smbDir
     *            共享目录新建文件存放路径
     * @param filename
     *            文件名,为空则访问到文件存放路径目录
     * @return SmbFile
     */
    private static SmbFile connectSMB(String serverIP, String username, String password, String smbDir, String filename) {
        SmbFile rmifile = null;
        try {
            NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(serverIP, username, password);
            String smbPath = "smb://".concat(serverIP).concat(SMBFILE_SEPARATOR).concat(smbDir);
            if (!smbPath.endsWith(JcifsUtil.SMBFILE_SEPARATOR)) {
                smbPath = smbPath.concat(SMBFILE_SEPARATOR);
            }
            if (null != filename && !"".equals(filename)) {
                smbPath = smbPath.concat(filename);
            }
            rmifile = new SmbFile(smbPath, auth);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return rmifile;
    }

    /**
     * @description 从smbMachine读取文件并存储到localpath指定的路径
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param serverIP
     *            共享目录IP
     * @param username
     *            共享目录账号
     * @param password
     *            共享目录密码
     * @param smbDir
     *            共享目录新建文件存放路径
     * @param filename
     *            文件名,为空则访问到文件存放路径目录
     * @param localpath
     *            本地路径
     * @return File
     */
    public static File readFromSmb(String serverIP, String username, String password, String smbDir, String filename,
            String localpath) {
        File localfile = null;
        InputStream bis = null;
        OutputStream bos = null;
        boolean createFlag = false;
        List<File> files = new ArrayList<File>();
        try {
            SmbFile rmifile = connectSMB(serverIP, username, password, smbDir, filename);
            bis = new BufferedInputStream(new SmbFileInputStream(rmifile));
            if (!localpath.endsWith(JcifsUtil.SMBFILE_SEPARATOR)) {
                localfile = new File(localpath + File.separator + filename);
            } else if (localpath.endsWith(JcifsUtil.SMBFILE_SEPARATOR)) {
                localfile = new File(localpath + filename);
            }
            bos = new BufferedOutputStream(new FileOutputStream(localfile));
            int length = rmifile.getContentLength();
            byte[] buffer = new byte[length];
            bis.read(buffer);
            bos.write(buffer);
            try {
                bos.close();
                bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            files.add(localfile);
            createFlag = true;
        } catch (Exception e) {
            createFlag = false;
            e.printStackTrace();
            if (null != bos) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e.printStackTrace();
                }
            }
            if (null != bis) {
                try {
                    bis.close();
                } catch (IOException e1) {
                    e.printStackTrace();
                }
            }
        }
        if (createFlag) {
            return localfile;
        } else {
            return null;
        }

    }

    /**
     * @description 从smbMachine读取文件并存储到localpath指定的路径
     *              共享机器的文件,如smb://xxx:xxx@**********/temp8/new/rec/return/
     *              return_process/a.txt,xxx:xxx是共享机器的用户名密码
     * @param localpath
     *            本地文件路径
     * @param serverIP
     *            共享目录IP
     * @param username
     *            共享目录账号
     * @param password
     *            共享目录密码
     * @param filename
     *            共享目录新建文件名
     * @param smbDir
     *            共享目录新建文件存放路径
     * @return 结果
     */
    public static boolean writeToSmb(String localpath, String serverIP, String username, String password,
            String filename, String smbDir) {
        File localfile = null;
        InputStream bis = null;
        OutputStream bos = null;
        boolean createFlag = false;
        try {
            SmbFile rmifile = connectSMB(serverIP, username, password, smbDir, filename);
            //@invalid rmifile.connect();
            //@invalid boolean canWrite = rmifile.canWrite();
            bos = new BufferedOutputStream(new SmbFileOutputStream(rmifile));
            localfile = new File(localpath);
            FileInputStream fis = new FileInputStream(localfile);
            bis = new BufferedInputStream(fis);
            byte[] buffer = new byte[fis.available()];
            //@invalid while (bis.read(buffer) != -1) {
            bis.read(buffer);
            bos.write(buffer);
            //@invalid buffer = new byte[1024];
            // }
            try {
                bos.close();
                bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            createFlag = true;
        } catch (Exception e) {
            createFlag = false;
            e.printStackTrace();
            if (null != bos) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e.printStackTrace();
                }
            }
            if (null != bis) {
                try {
                    bis.close();
                } catch (IOException e1) {
                    e.printStackTrace();
                }
            }
        }
        return createFlag;
    }

    /**
     * @description 移动共享目录单个文件，目标文件夹不存在时不会自动创建文件夹，无论移动文件是否成功，源文件都会被删除
     * <AUTHOR>
     * @param serverIP
     *            共享目录IP
     * @param username
     *            共享目录账号
     * @param password
     *            共享目录密码
     * @param srcDir
     *            共享目录原文件目录
     * @param destDir
     *            共享目录备份文件目录
     * @param filename
     *            文件名
     * @return 结果
     */
    public static boolean removeFile(String serverIP, String username, String password, String srcDir, String destDir,
            String filename) {
        try {
            SmbFile srcSmbFileFile = connectSMB(serverIP, username, password, srcDir, filename);
            SmbFile destSmbFile = connectSMB(serverIP, username, password, destDir, filename);
            srcSmbFileFile.copyTo(destSmbFile);
            srcSmbFileFile.delete();
            return true;

        } catch (SmbException e) {
            return false;
        }
    }

    /**
     * @description text文件去除UTF-8 BOM标识
     * @param in
     *            输入流
     * @return InputStream
     * @throws IOException
     *             IO异常
     * <AUTHOR>
     */
    public static InputStream getTrimBOMInputStream(InputStream in) throws IOException {

        PushbackInputStream testin = new PushbackInputStream(in);
        /*@invalid int ch = testin.read(); if (ch != 0xEF) { 
          testin.unread(ch) ; 
          } else
          if ((ch = testin.read()) != 0xBB) { 
          testin.unread(ch) ;
          testin.unread(0xef); 
          } else if ((ch = testin.read()) != 0xBF) { throw
          new IOException("错误的UTF-8格式文件") ;
           }
         */
        int ch = testin.read();
        if (ch != CodeCst.HEXADECIMAL_0XEF) {
            testin.unread(ch);
            return testin;
        }
        ch = testin.read();
        if (ch != CodeCst.HEXADECIMAL_0XBB) {
            testin.unread(ch);
            testin.unread(CodeCst.HEXADECIMAL_0XEF);
            return testin;
        }
        ch = testin.read();
        if (ch != CodeCst.HEXADECIMAL_0XBF) {
            throw new IOException("错误的UTF-8格式文件");
        }
        return testin;
    }
}
