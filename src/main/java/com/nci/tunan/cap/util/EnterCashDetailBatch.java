package com.nci.tunan.cap.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;

import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import com.nci.tunan.cap.impl.counterchagre.service.ICapArapEnterService;
import com.nci.tunan.cap.interfaces.model.bo.BankTextBO;
import com.nci.tunan.cap.interfaces.model.po.GroupAndDetailCompPO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.core.transaction.TransactionUtil;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * @description 返盘后成功后转实收
 * @<NAME_EMAIL>
 * @date 2015-9-18 上午10:05:18
 * @.belongToModule 收付费-集中制返盘
 */
public class EnterCashDetailBatch extends AbstractBatchJobForMod {
	/**
	 * 转实收service
	 */
    private ICapArapEnterService capArapEnterService;
    /**
     * @description 批处理执行
     * @param jobSessionContext 任务会话上下文
     * @param resultDatas 参数列表
     * @param modNum 取模数值
     */
    @Override
    public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> resultDatas, int modNum) {
        
        return resultDatas;
    }
    /**
     * @description 批处理异常处理
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void jobErrorAudit(JobSessionContext jobSessionContext, JobData jobData) {
        
    }
    /**
     * @description 批处理写操作
     * @param jobSessionContext 任务会话上下文
     * @param jobData 任务数据参数
     */
    @Override
    public void write(JobSessionContext jobSessionContext, JobData jobData) {

    }
    /**
     * @description 批处理业务处理
     * @param jobSessionContext 任务会话上下文
     * @param start 起始位置
     * @param counts 查询数量
     */
    @Override
    public List<JobData> query(JobSessionContext jobSessionContext, long start, int counts) {
    	long startTime=System.currentTimeMillis();
    	jobSessionContext.setLong("startTime", startTime);
        logger.info("转实收======开始:<======EnterCashDetailBatch--query======>");
        logger.info("=====================jobSessionContext.getJobId():" + jobSessionContext.getJobId());
        logger.info("=====================jobSessionContext.getJobRunId():" + jobSessionContext.getJobRunId());
        /**
         * 增加入参处理逻辑
         */
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date backTextTime = DateUtilsEx.getTodayDate();
        Set<String> derivTypes = new HashSet<String>();
        List<JobParam> paramList = jobSessionContext.getParams();
        if (null != paramList) {
            for (Iterator<JobParam> iter = paramList.iterator(); iter.hasNext();) {
                JobParam param = iter.next();
                logger.info("param.getParamName()= " + param.getParamName());
                logger.info("param.getParamCode()= " + param.getParamCode());
                logger.info("param.getParamType()= " + param.getParamType());
                logger.info("param.getParamValue()= " + param.getParamValue());
                String paramName = param.getParamName();
                String paramValue = param.getParamValue();
                if ("backTextTime".equals(paramName) && null!=paramValue && CapVerify.objIsNotNull(paramValue,true)) {
                    try {
                        backTextTime=sdf.parse(paramValue);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                if ("derivType".equals(paramName) && null!=paramValue && CapVerify.objIsNotNull(paramValue,true)) {
                	derivTypes.add(paramValue);
                   
                }
            }
        }
        List<GroupAndDetailCompPO> bankTexts = capArapEnterService.getToCashBankText(start,counts,derivTypes,backTextTime);
        logger.info("转实收======>盘文件个数:"+bankTexts.size());
        if(bankTexts.size()>0) {
			for (GroupAndDetailCompPO text : bankTexts) {
				BankTextBO textBO = capArapEnterService.saveArapOptimize(text);
			}
			long endTime = System.currentTimeMillis();
	        long s = (endTime - jobSessionContext.getLong("startTime")) / CodeCst.NUMBER_THOUSAND;
	        logger.info("转实收======>批处理总耗时:" + s / CodeCst.NUMBER_THREE_THOUSAND_AND_SIX_HUNDRED + "时" + s
	                / CodeCst.NUMBER_SIXTY % CodeCst.NUMBER_SIXTY + "分" + s % CodeCst.NUMBER_SIXTY + "秒");
        List<JobData> jobDataList = new ArrayList<JobData>();
        JobData jobData = new JobData();
        jobData.set("enterCashList", bankTexts);
        jobDataList.add(jobData);
        return jobDataList;
        }
        return null;
    }
    /**
     * 判断任务是否运行
     */
    @Override
    public boolean isCanBeRun() {
        return true;
    }
    /**
     * 停止任务
     */
    @Override
    public void jobStop() {

    }
    /**
     * @description 转实收线程处理
     * @<NAME_EMAIL>
     * @date 2015-9-18 上午10:05:18
     * @.belongToModule 收付费-集中制返盘
     */
    public class EnterCashThread implements Callable<BankTextBO>{
    	/**
    	 * 实收付详情po
    	 */
    	private GroupAndDetailCompPO text;
    	public EnterCashThread(GroupAndDetailCompPO text){
    		this.text=text;
    	}
    	/**
    	 * 转实收付线程处理
    	 */
		@Override
		public BankTextBO call() throws Exception {
			final List<BankTextBO> boList = new ArrayList<BankTextBO>();
    		//@invalid 事务控制开始
            final TransactionTemplate transactionTemplate = TransactionUtil.getTransactionTemplate();
            //@invalid 事务控制
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {

                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                    def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                    //@invalid 如果status不重新赋值的话，就会报异常
                    //@invalid 因为transactionManager是改变了status的状态的！
                    status = transactionTemplate.getTransactionManager().getTransaction(def);
                    //@invalid 设为false
                    try {
                        BankTextBO textBO = capArapEnterService.saveArapOptimize(text);
                        boList.add(textBO);
                        if (!status.isCompleted()) {
                            transactionTemplate.getTransactionManager().commit(status);
                        }

                    } catch (RuntimeException e) {
                        logger.debug("<======ReturnBankTextBatch--返盘转实收处理异常======>",e);
                        //@invalid e.printStackTrace();
                        //@invalid status.setRollbackOnly();
                        logger.error("返盘转实收异常：",e);
                        if (!status.isCompleted()) {
                            transactionTemplate.getTransactionManager().rollback(status);
                        }
                    }
                }
            });
            //@invalid 事务控制结束
            return boList.size()>0?boList.get(0):null;
		}
    	
    }
    
    /**
     * @description 查询任务数量
     * @param jobSessionContext 任务上下文
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        logger.debug("==================EnterCashDetailBatch======queryCounts................");
        jobSessionContext.setStartNum(1);
        jobSessionContext.setEndNum(1);
        jobSessionContext.setModNum(1);//@invalid 批处理逻辑依赖关系需要
        return jobSessionContext;
    }

    public ICapArapEnterService getCapArapEnterService() {
        return capArapEnterService;
    }

    public void setCapArapEnterService(ICapArapEnterService capArapEnterService) {
        this.capArapEnterService = capArapEnterService;
    }
    /**
     * 获取id名字
     */
    @Override
    public String getIdName() {
        return null;
    }

}
