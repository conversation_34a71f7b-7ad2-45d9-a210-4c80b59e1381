package com.nci.tunan.cap.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;

import sun.nio.ch.FileChannelImpl;

/**
 * @description MD5文件加密工具类
 * <AUTHOR> <EMAIL>
 * @date 2015年10月22日 上午10:17:32
 * @.belongToModule 收付费-制盘\返盘
 */
public class CapMD5CipherUtil {
    /**
     * @description 对文件进行MD5加密，并返回加密后的字符串
     * @version 1.0
     * @title 对文件进行MD5加密，并返回加密后的字符串
     * <AUTHOR> <EMAIL>
     * @param file 被加密文件
     * @return 文件加密后的字符串
     */
    public static String getMd5ByFile(File file, byte[] safeCode) {
        String value = null;
        FileInputStream in = null;
        FileChannel inChannel = null;
        try {
            in = new FileInputStream(file);
            inChannel = in.getChannel();
            MappedByteBuffer byteBuffer = inChannel.map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            value = bytes2HexString(md5.digest(safeCode));
            //@invalid 加上这几行代码,手动unmap
            Method m = FileChannelImpl.class.getDeclaredMethod("unmap", MappedByteBuffer.class);
            m.setAccessible(true);
            m.invoke(FileChannelImpl.class, byteBuffer);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != inChannel) {
                try {
                    inChannel.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return value.toUpperCase();
    }

    /**
     * @description 对文件进行MD5加密，并将加密后的字符串写入到第二个参数文件中，写入文件后，会覆盖原文件的内容
     * @version 1.0
     * @title 对文件进行MD5加密，并将加密后的字符串写入到第二个参数文件中
     * <AUTHOR> <EMAIL>
     * @param file 待加密文件
     * @param writeFile 写入加密字符串的文件
     */
    public static void getMd5ByFile(File file, File writeFile, byte[] safeCode) {
        //@invalid 判断若待写入文件不存在，则创建一个新文件
        //@invalid if(!writeFile.exists()){
        //@invalid writeFile.createNewFile();
        //@invalid }
        //@invalid 创建一个输出流
        OutputStream out = null;
        String value = null;
        FileInputStream in = null;
        try {
            out = new FileOutputStream(writeFile);
            in = new FileInputStream(file);
            MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            byte[] md5Bytes = md5.digest(safeCode);
            BigInteger bi = new BigInteger(1, md5Bytes);
            value = bi.toString(CodeCst.NUMBER_SIXTEEN);
            byte[] b = value.toUpperCase().getBytes();
            out.write(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * @description 对文件进行MD5加密，并将加密后的字符串写入到第二个参数文件中，写入文件后，会覆盖原文件的内容
     * @version 1.0
     * @title 对文件进行MD5加密，并将加密后的字符串写入到第二个参数文件中
     * <AUTHOR> <EMAIL>
     * @param file 待加密文件
     * @param writeFile 写入加密字符串的文件
     */
    public static void getMd5ByFileOld(File file, File writeFile) {
        //@invalid 判断若待写入文件不存在，则创建一个新文件
        //@invalid if(!writeFile.exists()){
        //@invalid writeFile.createNewFile();
        //@invalid }
        //@invalid 创建一个输出流
        OutputStream out = null;
        String value = null;
        FileInputStream in = null;
        try {
            out = new FileOutputStream(writeFile);
            in = new FileInputStream(file);
            MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            BigInteger bi = new BigInteger(1, md5.digest());
            value = bi.toString(CodeCst.NUMBER_SIXTEEN);
            byte[] b = value.getBytes();
            out.write(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * @description 对文件进行MD5加密，并将加密后的字符串写入到第二个参数文件中，写入文件后，会覆盖原文件的内容
     * @version 1.0
     * @title 对文件进行MD5加密，并将加密后的字符串写入到第二个参数文件中
     * <AUTHOR> <EMAIL>
     * @param file 待加密文件
     * @param writeFile 写入加密字符串的文件
     * @param flag 是否以追加的形式添加到文件中
     */
    public static void getMd5ByFile(File file, File writeFile, boolean flag) {
        //@invalid 判断若待写入文件不存在，则创建一个新文件
        //@invalid if(!writeFile.exists()){
        //@invalid writeFile.createNewFile();
        //@invalid }
        //@invalid 创建一个输出流
        OutputStream out = null;
        String value = null;
        FileInputStream in = null;
        try {
            out = new FileOutputStream(writeFile, flag);
            in = new FileInputStream(file);
            MappedByteBuffer byteBuffer = in.getChannel().map(FileChannel.MapMode.READ_ONLY, 0, file.length());
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(byteBuffer);
            BigInteger bi = new BigInteger(1, md5.digest());
            value = bi.toString(CodeCst.NUMBER_SIXTEEN);
            byte[] b = value.getBytes();
            out.write(b);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * @description 将指定byte数组转换为16进制的形式(Java中的一个byte，其范围是-128~127的，
     *              而Integer.toHexString的参数本来是int
     *              ，如果不进行&0xff，那么当一个byte会转换成int时，对于负数
     *              ，会做位扩展，举例来说，一个byte的-1（即0xff
     *              ），会被转换成int的-1（即0xffffffff），那么转化出的结果就不是我们想要的了)
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param b 字节数组
     * @return String
     */
    public static String bytes2HexString(byte[] b) {
        String ret = "";
        for (int i = 0; i < b.length; i++) {
            String hex = Integer.toHexString(b[i] & CodeCst.HEXADECIMAL_0XFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            ret += hex.toUpperCase();
        }
        return ret;
    }
}
