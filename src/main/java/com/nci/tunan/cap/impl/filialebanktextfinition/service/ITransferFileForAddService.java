package com.nci.tunan.cap.impl.filialebanktextfinition.service;

import java.util.List;

import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskColconfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskConfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskLineconfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskValidcolcfgBO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IService;

/**
 * @.belongToModule 收付费—分公司转账格式定义
 * @description 分公司制返盘增加主表、行表、列表配置  Service层定义
 * <AUTHOR>
 * @date 2015-10-12 下午2:34:11
 */
public interface ITransferFileForAddService extends IService{
    
	/**
     * @description 分公司制返盘配置主表保存
     * @param bo 分公司制返盘配置主表BO对象
     */
    public OrganBankDiskConfigBO saveBankDiskConfig(OrganBankDiskConfigBO bo);
    
    /**
     * @description 分公司制返盘行配置表保存
     * @param bo 分公司制返盘行配置表BO对象
     */
    public OrganBankDiskLineconfigBO saveBankDiskLineConfig(OrganBankDiskLineconfigBO bo);
    
    /**
     * @description 分公司制返盘列配置表保存
     * @<NAME_EMAIL>
     * @param bo 分公司制返盘列配置表BO对象
     */
    public OrganBankDiskColconfigBO saveBankDiskColconfig(OrganBankDiskColconfigBO bo);
    
    /**
     * @description 查询分公司制返盘主表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘配置主表BO对象
     */
    public CurrentPage<OrganBankDiskConfigBO> queryInfo(CurrentPage<OrganBankDiskConfigBO> currentPage);
    
    /**
     * @description 查询分公司制返盘行表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘行配置表BO对象列表
     */
    public CurrentPage<OrganBankDiskLineconfigBO> queryInfoLine(CurrentPage<OrganBankDiskLineconfigBO> currentPage);
    
    /**
     * @description 修改分公司制返盘主表配置信息
     * @param bo 分公司制返盘配置主表BO对象
     */
    public OrganBankDiskConfigBO updateBankDiskConfig(OrganBankDiskConfigBO bo);
    
    /**
     * @description 查询索引
     */
    public Object queryDualId();
    
    /**
     * @description 查询分公司制返盘可用字段配置表全部信息
     * @param organBankDiskValidcolcfgBO 分公司制返盘可用字段配置表BO对象
     */
    public List<OrganBankDiskValidcolcfgBO> querycfgAll(OrganBankDiskValidcolcfgBO organBankDiskValidcolcfgBO);
    
    /**
     * @description 批量增加分公司制返盘列配置
     * @param bankDiskColconfigBOs 分公司制返盘列BO对象列表
     */
     public boolean batchSaveOrganBankDiskColconfig(List<OrganBankDiskColconfigBO> bankDiskColconfigBOs);
     /**
      * @description 查询制返盘配置行关联的所有列
      * @param organBankDiskColconfigBO 分公司制返盘列BO对象
      */
     public List<OrganBankDiskColconfigBO> queryColconfigsByLineId(OrganBankDiskColconfigBO organBankDiskColconfigBO);
     /**
      * @description 根据列ID查询列信息配置
      * @param organBankDiskColconfigBO 分公司制返盘列BO对象
      */
     public OrganBankDiskColconfigBO queryColconfigsByColId(OrganBankDiskColconfigBO organBankDiskColconfigBO);

     /**
      * @description 更新列配置
      * @param organBankDiskColconfigBO 分公司制返盘列BO对象
      */
     public OrganBankDiskColconfigBO updateColconfigInfo(OrganBankDiskColconfigBO organBankDiskColconfigBO);
     
     /**
      * @description 查询单条制返盘配行表配置
      * @param lineBO 分公司制返盘行表BO对象
      */
     public OrganBankDiskLineconfigBO queryInfoLineById(OrganBankDiskLineconfigBO lineBO);
     
     /**
      * @description 更新分公司制返盘行配置表
      * @<NAME_EMAIL>
      * @param bo 分公司制返盘行表BO对象
      */
     public OrganBankDiskLineconfigBO updateBankDiskLineConfig(OrganBankDiskLineconfigBO bo);
     
     /**
      * @description 分公司制返盘列配置删除
      * @param bo 分公司制返盘列BO对象
      */
     public boolean delBankDiskColconfig(List<OrganBankDiskColconfigBO> bo);
     /**
      * @description 查询分公司制返盘主表配置信息
      * @param configBO  分公司制返盘主表BO对象
      */
     public OrganBankDiskConfigBO queryDiskConfigById(OrganBankDiskConfigBO configBO);
     /**
      * @description 按条件查询盘信息
      * @param configBO 分公司制返盘主表BO对象
      */
     public OrganBankDiskConfigBO queryDiskConfigByCondition(OrganBankDiskConfigBO configBO);
     /**
      * @description 查询盘配置的行信息
      * @param configBO 分公司制返盘行表BO对象
      */
     public List<OrganBankDiskLineconfigBO> queryDiskLineConfigByConf(OrganBankDiskLineconfigBO configBO);
     /**
      * @description  按行ID删除列配置表
      * @param organBankDiskColconfigBO 分公司制返盘列表BO对象
      */
     public boolean deleteOrganBankDiskColconfigByLineId(OrganBankDiskColconfigBO organBankDiskColconfigBO);
     /**
      * @description 删除行配置
      * @param configBO 分公司制返盘行表BO对象
      */
     public boolean deleteOrganBankDiskLineconfig(OrganBankDiskLineconfigBO configBO);
     /**
      * @description 按盘ID批量删除行配置
      * @param configBO 当前页分公司制返盘行表BO对象
      */
     public boolean deleteOrganBankDiskLineconfigByConfId(List<OrganBankDiskLineconfigBO> configBO);
     /**
      * @description  按行ID批量删除列配置表
      * @param organBankDiskColconfigBO 当前页分公司制返盘列表BO对象
      */
     public boolean batchDelOrganBankDiskColconfigByLineId(List<OrganBankDiskColconfigBO> organBankDiskColconfigBO);
     /**
      * @description 删除分公司制返盘主表配置信息
      * @param bo 分公司制返盘主表BO对象
      */
     public boolean deleteOrganBankDiskConfig(OrganBankDiskConfigBO bo);
     
     /**
      * @description 批量修改数据
      * @param organBankDiskColconfigBOs 分公司制返盘列表BO对象列表
      * @version
      * @title
      * <AUTHOR>
      * @return boolean 批量修改是否成功
      */
 	 public boolean batchUpdateColconfigInfo(List<OrganBankDiskColconfigBO> organBankDiskColconfigBOs);
}
