package com.nci.tunan.cap.impl.queryboccustomerinfo.dao.impl;

import com.nci.tunan.cap.impl.queryboccustomerinfo.dao.IQueryBOCCustomerInfoDao;
import com.nci.tunan.cap.interfaces.model.po.PremArapPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;

public class QueryBOCCustomerInfoDaoImpl extends BaseDaoImpl implements IQueryBOCCustomerInfoDao{

    @Override
    public PremArapPO queryBankAccName(String bankAccNo) {
        PremArapPO po = new PremArapPO();
        po.setBankAccount(bankAccNo);
        return findObject("bank_info.findAccNameByAccNo",po);
    }

}
