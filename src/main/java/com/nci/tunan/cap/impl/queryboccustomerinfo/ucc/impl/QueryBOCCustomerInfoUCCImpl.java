package com.nci.tunan.cap.impl.queryboccustomerinfo.ucc.impl;

import org.slf4j.Logger;

import com.nci.tunan.cap.impl.queryboccustomerinfo.service.IQueryBOCCustomerInfoService;
import com.nci.tunan.cap.impl.queryboccustomerinfo.ucc.IQueryBOCCustomerInfoUCC;
import com.nci.tunan.cap.interfaces.model.po.PremArapPO;
import com.nci.tunan.cap.interfaces.vo.queryboccustomerinfo.QueryBOCCustomerInfoInputData;
import com.nci.tunan.cap.interfaces.vo.queryboccustomerinfo.QueryBOCCustomerInfoOutputData;
import com.nci.udmp.util.logging.LoggerFactory;

public class QueryBOCCustomerInfoUCCImpl implements IQueryBOCCustomerInfoUCC {

    private static Logger logger = LoggerFactory.getLogger();
    private IQueryBOCCustomerInfoService queryBOCCustomerInfoService;
    
    
    public IQueryBOCCustomerInfoService getQueryBOCCustomerInfoService() {
        return queryBOCCustomerInfoService;
    }


    public void setQueryBOCCustomerInfoService(IQueryBOCCustomerInfoService queryBOCCustomerInfoService) {
        this.queryBOCCustomerInfoService = queryBOCCustomerInfoService;
    }


    @Override
    public QueryBOCCustomerInfoOutputData queryAccNameByAccNo(QueryBOCCustomerInfoInputData input) {
        logger.debug("QueryBOCCustomerInfoUCCImpl.queryAccNameByAccNo Start--");
        QueryBOCCustomerInfoOutputData out = new QueryBOCCustomerInfoOutputData();
        if(input != null){
            out.setResultCode("");
            out.setResultMsg("");
            out.setBankAccNo(input.getBankAccNo());
            out.setTransSeq(input.getTransSeq());
            out.setCustomerName("");
        }
        //入参校验
        if(input.getBankAccNo() == null || "".equals(input.getBankAccNo())){
            out.setResultCode("0");
            out.setResultMsg("入参校验失败：银行卡号不能为空！");
            logger.debug("queryAccNameByAccNo--银行卡号为空！");
            return out;
        }
        if(input.getTransSeq() == null || "".equals(input.getTransSeq())){
            out.setResultCode("0");
            out.setResultMsg("入参校验失败：交易流水号不能为空！");
            logger.debug("queryAccNameByAccNo--交易流水号为空！");
            return out;
        }
        //根据中行卡号查询户名
        PremArapPO po = queryBOCCustomerInfoService.queryBankAccName(input.getBankAccNo());
        if(po.getBankUserName() != null && !"".equals(po.getBankUserName())){
            out.setResultCode("2");
            out.setResultMsg("成功查询到客户姓名！");
            out.setCustomerName(po.getBankUserName());
        }else{
            out.setResultCode("1");
            out.setResultMsg("未查询到客户姓名！");
            logger.debug("queryAccNameByAccNo--查询结果为空！");
        }
        logger.debug("QueryBOCCustomerInfoUCCImpl.queryAccNameByAccNo End--");
        return out;
    }

}
