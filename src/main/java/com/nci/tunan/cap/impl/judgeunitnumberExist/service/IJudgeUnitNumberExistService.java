package com.nci.tunan.cap.impl.judgeunitnumberExist.service;

import com.nci.tunan.cap.interfaces.vo.judgeunitnumberExist.InputData;
import com.nci.tunan.cap.interfaces.vo.judgeunitnumberExist.OutputData;
/**
 * 
 * @description 交退费编号是否存在判断ucc实现
 * @date 2015-9-17 下午4:29:05
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @.belongToModule 收付费-实收实付
 */
public interface IJudgeUnitNumberExistService {
	/**
	 * 
	 * @description 交退费编号是否存在判断
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cap.impl.judgeunitnumberExist.ucc.IJudgeUnitNumberExistUcc#judgeUnitnumberExist(com.nci.tunan.cap.interfaces.vo.judgeunitnumberExist.InputData)
	 * @param inputData 交退费编号输入参数
	 * @return
	 */
	public OutputData judgeUnitnumberExist(InputData inputData);

}
