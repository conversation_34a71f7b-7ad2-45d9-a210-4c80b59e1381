package com.nci.tunan.cap.impl.queryboccustomerinfo.service.impl;

import com.nci.tunan.cap.impl.queryboccustomerinfo.dao.IQueryBOCCustomerInfoDao;
import com.nci.tunan.cap.impl.queryboccustomerinfo.service.IQueryBOCCustomerInfoService;
import com.nci.tunan.cap.interfaces.model.po.PremArapPO;

public class QueryBOCCustomerInfoServiceImpl implements IQueryBOCCustomerInfoService{

    private IQueryBOCCustomerInfoDao queryBOCCustomerInfoDao;
    
    
    public IQueryBOCCustomerInfoDao getQueryBOCCustomerInfoDao() {
        return queryBOCCustomerInfoDao;
    }


    public void setQueryBOCCustomerInfoDao(IQueryBOCCustomerInfoDao queryBOCCustomerInfoDao) {
        this.queryBOCCustomerInfoDao = queryBOCCustomerInfoDao;
    }


    @Override
    public PremArapPO queryBankAccName(String bankAccNo) {
        return queryBOCCustomerInfoDao.queryBankAccName(bankAccNo);
    }

}
