package com.nci.tunan.cap.impl.filialebanktextfinition.service.impl;

import java.util.List;

import com.nci.tunan.cap.dao.IOrganBankDiskConfigDao;
import com.nci.tunan.cap.dao.IOrganBankDiskValidcolcfgDao;
import com.nci.tunan.cap.impl.filialebanktextfinition.service.ITransferFileForAddService;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskColconfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskConfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskLineconfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskValidcolcfgBO;
import com.nci.tunan.cap.interfaces.model.po.OrganBankDiskColconfigPO;
import com.nci.tunan.cap.interfaces.model.po.OrganBankDiskConfigPO;
import com.nci.tunan.cap.interfaces.model.po.OrganBankDiskLineconfigPO;
import com.nci.tunan.cap.interfaces.model.po.OrganBankDiskValidcolcfgPO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
/**
 * @.belongToModule 收付费—分公司转账格式定义
 * @description 分公司制返盘增加主表、行表、列表配置  Service层实现
 * <AUTHOR>
 * @date 2015-10-12 下午2:34:11
 */
public class TransferFileForAddServiceImpl implements ITransferFileForAddService{
	/**
	 * 分公司制返盘配置操作Dao层的定义
	 */
    private IOrganBankDiskConfigDao organBankDiskConfigDao;
    /**
     * 分公司制返盘可用字段配置操作Dao层的定义
     */
    private IOrganBankDiskValidcolcfgDao organBankDiskValidcolcfgDao;
    /**
     * @description 分公司制返盘配置主表保存
     * @param bo 分公司制返盘配置主表BO对象
     */
    @Override
    public OrganBankDiskConfigBO saveBankDiskConfig(OrganBankDiskConfigBO bo) {
        OrganBankDiskConfigPO organBankDiskConfigPO = new OrganBankDiskConfigPO();
        organBankDiskConfigPO = BeanUtils.copyProperties(OrganBankDiskConfigPO.class, bo);
        organBankDiskConfigPO = organBankDiskConfigDao.addOrganBankDiskConfig(organBankDiskConfigPO);
        OrganBankDiskConfigBO bo1 = new OrganBankDiskConfigBO();
        bo1 = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, organBankDiskConfigPO);
        return bo1;
    }
    /**
     * @description 分公司制返盘行配置表保存
     * @param bo 分公司制返盘行配置表BO对象
     */
    @Override
    public OrganBankDiskLineconfigBO saveBankDiskLineConfig(OrganBankDiskLineconfigBO bo) {
        OrganBankDiskLineconfigPO organBankDiskLineconfigPO = new OrganBankDiskLineconfigPO();
        organBankDiskLineconfigPO = BeanUtils.copyProperties(OrganBankDiskLineconfigPO.class, bo);
        organBankDiskLineconfigPO = organBankDiskConfigDao.addOrganBankDiskLineconfig(organBankDiskLineconfigPO);
        OrganBankDiskLineconfigBO organBankDiskLineconfigBO = new OrganBankDiskLineconfigBO();
        organBankDiskLineconfigBO = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class, organBankDiskLineconfigPO);
        return organBankDiskLineconfigBO;
    }
    /**
     * @description 查询索引
     */
    @Override
    public Object queryDualId() {
        
        return  organBankDiskConfigDao.queryDualId();
    }
    /**
     * @description 分公司制返盘列配置表保存
     * @<NAME_EMAIL>
     * @param bo 分公司制返盘列配置表BO对象
     */
    @Override
    public OrganBankDiskColconfigBO saveBankDiskColconfig(OrganBankDiskColconfigBO bo) {
        OrganBankDiskColconfigPO organBankDiskColconfigPO = new OrganBankDiskColconfigPO();
        organBankDiskColconfigPO = BeanUtils.copyProperties(OrganBankDiskColconfigPO.class, bo);
        organBankDiskColconfigPO = organBankDiskConfigDao.addOrganBankDiskColconfig(organBankDiskColconfigPO);
        OrganBankDiskColconfigBO organBankDiskColconfigBO = new OrganBankDiskColconfigBO();
        organBankDiskColconfigBO = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class, organBankDiskColconfigPO);
        return organBankDiskColconfigBO;
    }
    /**
     * @description 查询分公司制返盘主表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘配置主表BO对象
     */
    @Override
    public CurrentPage<OrganBankDiskConfigBO> queryInfo(CurrentPage<OrganBankDiskConfigBO> currentPage) {
        CurrentPage<OrganBankDiskConfigPO> po = new CurrentPage<OrganBankDiskConfigPO>();
        po = BeanUtils.copyCurrentPage(OrganBankDiskConfigPO.class, currentPage);
        return BeanUtils.copyCurrentPage(OrganBankDiskConfigBO.class, organBankDiskConfigDao.queryInfo(po));
    }
    /**
     * @description 修改分公司制返盘主表配置信息
     * @param bo 分公司制返盘配置主表BO对象
     */
    @Override
    public OrganBankDiskConfigBO updateBankDiskConfig(OrganBankDiskConfigBO bo) {
        OrganBankDiskConfigPO organBankDiskConfigPO = new OrganBankDiskConfigPO();
        organBankDiskConfigPO = BeanUtils.copyProperties(OrganBankDiskConfigPO.class, bo);
        organBankDiskConfigPO = organBankDiskConfigDao.updateOrganBankDiskConfig(organBankDiskConfigPO);
        OrganBankDiskConfigBO organBankDiskConfigBO = new OrganBankDiskConfigBO();
        organBankDiskConfigBO = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, organBankDiskConfigPO);
        return organBankDiskConfigBO;
    }
    /**
     * @description 查询分公司制返盘行表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘行配置表BO对象列表
     */
    @Override
    public CurrentPage<OrganBankDiskLineconfigBO> queryInfoLine(CurrentPage<OrganBankDiskLineconfigBO> currentPage) {
        CurrentPage<OrganBankDiskLineconfigPO> po = new CurrentPage<OrganBankDiskLineconfigPO>();
        po = BeanUtils.copyCurrentPage(OrganBankDiskLineconfigPO.class, currentPage);
        return BeanUtils.copyCurrentPage(OrganBankDiskLineconfigBO.class, organBankDiskConfigDao.queryInfoLine(po));
    }
    /**
     * @description 查询分公司制返盘可用字段配置表全部信息
     * @param organBankDiskValidcolcfgBO 分公司制返盘可用字段配置表BO对象
     */
    @Override
    public List<OrganBankDiskValidcolcfgBO> querycfgAll(OrganBankDiskValidcolcfgBO organBankDiskValidcolcfgBO) {
        OrganBankDiskValidcolcfgPO bankDiskValidcolcfgPO =BeanUtils.copyProperties(OrganBankDiskValidcolcfgPO.class, organBankDiskValidcolcfgBO);
        List<OrganBankDiskValidcolcfgPO> validcolcfg = organBankDiskValidcolcfgDao.findAllOrganBankDiskValidcolcfg(bankDiskValidcolcfgPO);
        return BeanUtils.copyList(OrganBankDiskValidcolcfgBO.class, validcolcfg);
    }
    /**
     * @description 批量增加分公司制返盘列配置
     * @param bankDiskColconfigBOs 分公司制返盘列BO对象列表
     */
    @Override
    public boolean batchSaveOrganBankDiskColconfig(List<OrganBankDiskColconfigBO> bankDiskColconfigBOs){
        List<OrganBankDiskColconfigPO> copyList = BeanUtils.copyList(OrganBankDiskColconfigPO.class, bankDiskColconfigBOs);
        return  organBankDiskConfigDao.batchSaveOrganBankDiskColconfig(copyList);
    }
    /**
     * @description 查询制返盘配置行关联的所有列
     * @param organBankDiskColconfigBO 分公司制返盘列BO对象
     */
    @Override
    public List<OrganBankDiskColconfigBO> queryColconfigsByLineId(OrganBankDiskColconfigBO organBankDiskColconfigBO) {
        OrganBankDiskColconfigPO copyProperties = BeanUtils.copyProperties(OrganBankDiskColconfigPO.class, organBankDiskColconfigBO);
        List<OrganBankDiskColconfigPO> colconfigs = organBankDiskConfigDao.queryColconfigByLineId(copyProperties);
        return BeanUtils.copyList(OrganBankDiskColconfigBO.class, colconfigs);
    }
    /**
     * @description 根据列ID查询列信息配置
     * @param organBankDiskColconfigBO 分公司制返盘列BO对象
     */
    @Override
    public OrganBankDiskColconfigBO queryColconfigsByColId(OrganBankDiskColconfigBO organBankDiskColconfigBO) {
        OrganBankDiskColconfigPO copyProperties = BeanUtils.copyProperties(OrganBankDiskColconfigPO.class, organBankDiskColconfigBO);
        OrganBankDiskColconfigPO colconfig = organBankDiskConfigDao.queryColconfigByColId(copyProperties);
        return BeanUtils.copyProperties(OrganBankDiskColconfigBO.class, colconfig);
    }
    /**
     * @description 更新列配置
     * @param organBankDiskColconfigBO 分公司制返盘列BO对象
     */
    @Override
    public OrganBankDiskColconfigBO updateColconfigInfo(OrganBankDiskColconfigBO organBankDiskColconfigBO) {
        OrganBankDiskColconfigPO copyProperties = BeanUtils.copyProperties(OrganBankDiskColconfigPO.class, organBankDiskColconfigBO);
        OrganBankDiskColconfigPO colconfig = organBankDiskConfigDao.updateColconfigInfo(copyProperties);
        return BeanUtils.copyProperties(OrganBankDiskColconfigBO.class, colconfig);
    }
    /**
     * @description 查询单条制返盘配行表配置
     * @param lineBO 分公司制返盘行表BO对象
     */
    @Override
    public OrganBankDiskLineconfigBO queryInfoLineById(OrganBankDiskLineconfigBO lineBO) {
        OrganBankDiskLineconfigPO po = BeanUtils.copyProperties(OrganBankDiskLineconfigPO.class, lineBO);
        return BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class, organBankDiskConfigDao.queryInfoLineById(po));
    }
    /**
     * @description 更新分公司制返盘行配置表
     * @<NAME_EMAIL>
     * @param bo 分公司制返盘行表BO对象
     */
    @Override
    public OrganBankDiskLineconfigBO updateBankDiskLineConfig(OrganBankDiskLineconfigBO bo) {
        OrganBankDiskLineconfigPO po = BeanUtils.copyProperties(OrganBankDiskLineconfigPO.class, bo);
        OrganBankDiskLineconfigPO resultPO = organBankDiskConfigDao.updateOrganBankDiskLineconfig(po);
        return BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class, resultPO);
    }
    /**
     * @description 分公司制返盘列配置删除
     * @param bo 分公司制返盘列BO对象
     */
    @Override
    public boolean delBankDiskColconfig(List<OrganBankDiskColconfigBO> bo) {
        List<OrganBankDiskColconfigPO> pos = BeanUtils.copyList(OrganBankDiskColconfigPO.class, bo);
        return organBankDiskConfigDao.deleteOrganBankDiskColconfig(pos);
    }
    /**
     * @description 查询分公司制返盘主表配置信息
     * @param configBO  分公司制返盘主表BO对象
     */
    @Override
    public OrganBankDiskConfigBO queryDiskConfigById(OrganBankDiskConfigBO configBO) {
        OrganBankDiskConfigPO po = BeanUtils.copyProperties(OrganBankDiskConfigPO.class, configBO);
        OrganBankDiskConfigPO config = organBankDiskConfigDao.queryDiskConfigById(po);
        return BeanUtils.copyProperties(OrganBankDiskConfigBO.class, config);
    }
    /**
     * @description 按条件查询盘信息
     * @param configBO 分公司制返盘主表BO对象
     */
    @Override
    public OrganBankDiskConfigBO queryDiskConfigByCondition(OrganBankDiskConfigBO configBO) {
        OrganBankDiskConfigPO po = BeanUtils.copyProperties(OrganBankDiskConfigPO.class, configBO);
        List<OrganBankDiskConfigPO> list = organBankDiskConfigDao.queryDiskConfigByCondition(po);
        if(list != null && list.size() > 0){
            return BeanUtils.copyProperties(OrganBankDiskConfigBO.class, list.get(0));
        }
        return null;
    }
    /**
     * @description 查询盘配置的行信息
     * @param configBO 分公司制返盘行表BO对象
     */
    @Override
    public List<OrganBankDiskLineconfigBO> queryDiskLineConfigByConf(OrganBankDiskLineconfigBO configBO) {
        OrganBankDiskLineconfigPO po = BeanUtils.copyProperties(OrganBankDiskLineconfigPO.class, configBO);
        List<OrganBankDiskLineconfigPO> resultPOs = organBankDiskConfigDao.queryDiskLineConfigByConf(po);
        return BeanUtils.copyList(OrganBankDiskLineconfigBO.class, resultPOs);
    }
    /**
     * @description  按行ID删除列配置表
     * @param organBankDiskColconfigBO 分公司制返盘列表BO对象
     */
    @Override
    public boolean deleteOrganBankDiskColconfigByLineId(OrganBankDiskColconfigBO organBankDiskColconfigBO) {
        OrganBankDiskColconfigPO pos = BeanUtils.copyProperties(OrganBankDiskColconfigPO.class, organBankDiskColconfigBO);
        return organBankDiskConfigDao.deleteOrganBankDiskColconfigByLineId(pos);
    }
    /**
     * @description 删除行配置
     * @param configBO 分公司制返盘行表BO对象
     */
    @Override
    public boolean deleteOrganBankDiskLineconfig(OrganBankDiskLineconfigBO configBO) {
        OrganBankDiskLineconfigPO po = BeanUtils.copyProperties(OrganBankDiskLineconfigPO.class, configBO);
        return organBankDiskConfigDao.deleteOrganBankDiskLineconfig(po);
    }
    /**
     * @description 按盘ID批量删除行配置
     * @param configBO 当前页分公司制返盘行表BO对象
     */
    @Override
    public boolean deleteOrganBankDiskLineconfigByConfId(List<OrganBankDiskLineconfigBO> configBO) {
        List<OrganBankDiskLineconfigPO> po = BeanUtils.copyList(OrganBankDiskLineconfigPO.class, configBO);
        return organBankDiskConfigDao.deleteOrganBankDiskLineconfigByConfId(po);
    }
    /**
     * @description  按行ID批量删除列配置表
     * @param organBankDiskColconfigBO 当前页分公司制返盘列表BO对象
     */
    @Override
    public boolean batchDelOrganBankDiskColconfigByLineId(List<OrganBankDiskColconfigBO> organBankDiskColconfigBO) {
        List<OrganBankDiskColconfigPO> pos = BeanUtils.copyList(OrganBankDiskColconfigPO.class, organBankDiskColconfigBO);
        return organBankDiskConfigDao.batchDelOrganBankDiskColconfigByLineId(pos);
    }
    /**
     * @description 删除分公司制返盘主表配置信息
     * @param bo 分公司制返盘主表BO对象
     */
    @Override
    public boolean deleteOrganBankDiskConfig(OrganBankDiskConfigBO bo) {
        OrganBankDiskConfigPO po = BeanUtils.copyProperties(OrganBankDiskConfigPO.class, bo);
        OrganBankDiskLineconfigPO lineconfigPO = new OrganBankDiskLineconfigPO();
        lineconfigPO.setDiskConfigId(po.getDiskConfigId());
        List<OrganBankDiskLineconfigPO> resLineConfs = organBankDiskConfigDao.queryDiskLineConfigByConf(lineconfigPO);
        OrganBankDiskColconfigPO tempColPO = null;
        for (OrganBankDiskLineconfigPO line : resLineConfs) {
            tempColPO = new OrganBankDiskColconfigPO();
            tempColPO.setDiskLineconfigId(line.getDiskLineconfigId());
            List<OrganBankDiskColconfigPO> colPOs = organBankDiskConfigDao.queryColconfig(tempColPO);
            organBankDiskConfigDao.batchDelOrganBankDiskColconfigByLineId(colPOs);
        }
        organBankDiskConfigDao.deleteOrganBankDiskLineconfigByConfId(resLineConfs);
        return organBankDiskConfigDao.deleteOrganBankDiskConfig(po);
    }
   
    public IOrganBankDiskConfigDao getOrganBankDiskConfigDao() {
        return organBankDiskConfigDao;
    }
    public void setOrganBankDiskConfigDao(IOrganBankDiskConfigDao organBankDiskConfigDao) {
        this.organBankDiskConfigDao = organBankDiskConfigDao;
    }
    public IOrganBankDiskValidcolcfgDao getOrganBankDiskValidcolcfgDao() {
        return organBankDiskValidcolcfgDao;
    }
    public void setOrganBankDiskValidcolcfgDao(IOrganBankDiskValidcolcfgDao organBankDiskValidcolcfgDao) {
        this.organBankDiskValidcolcfgDao = organBankDiskValidcolcfgDao;
    }
    
    /**
     * @description 批量修改数据
     * @param organBankDiskColconfigBOs 分公司制返盘列表BO对象列表
     * @version
     * @title
     * <AUTHOR>
     * @return boolean 批量修改是否成功
     */
     @Override
	 public boolean batchUpdateColconfigInfo(List<OrganBankDiskColconfigBO> organBankDiskColconfigBOs) {
    	List<OrganBankDiskColconfigPO> organBankDiskColconfigPOs = BeanUtils.copyList(OrganBankDiskColconfigPO.class, organBankDiskColconfigBOs);
		return organBankDiskConfigDao.batchUpdateColconfigInfo(organBankDiskColconfigPOs);
	 }
    
}
