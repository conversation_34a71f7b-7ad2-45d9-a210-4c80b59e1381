package com.nci.tunan.cap.impl.filialebanktextfinition.ucc.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.nci.tunan.cap.impl.filialebanktextfinition.service.ITransferFileForAddService;
import com.nci.tunan.cap.impl.filialebanktextfinition.ucc.ITransferFileForAddUCC;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskColconfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskConfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskLineconfigBO;
import com.nci.tunan.cap.interfaces.model.bo.OrganBankDiskValidcolcfgBO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskColconfigVO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskConfigVO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskLineconfigVO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskValidcolcfgVO;
import com.nci.tunan.cap.util.CapVerify;
import com.nci.tunan.cap.util.CodeCst;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IUCC;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.bean.BeanUtils;
/**
 * @.belongToModule 收付费—分公司转账格式定义
 * @description 分公司制返盘增加主表、行表、列表配置  ucc层实现
 * <AUTHOR>
 * @date 2015-10-12 下午2:34:11
 */
public class TransferFileForAddUCCImpl implements ITransferFileForAddUCC, IUCC {
	/**
	 * 分公司制返盘配置操作Service层的定义
	 */
    ITransferFileForAddService transferFileForAdd;
    /**
     * @description 分公司制返盘配置主表保存
     * @param vo 分公司制返盘配置主表VO对象
     */
    @Override
    public OrganBankDiskConfigVO saveBankDiskConfig(OrganBankDiskConfigVO vo) {
        OrganBankDiskConfigBO organBankDiskConfigBO = new OrganBankDiskConfigBO();
        organBankDiskConfigBO = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, vo);
        organBankDiskConfigBO = transferFileForAdd.saveBankDiskConfig(organBankDiskConfigBO);
        OrganBankDiskConfigVO vo1 = new OrganBankDiskConfigVO();
        vo1 = BeanUtils.copyProperties(OrganBankDiskConfigVO.class, organBankDiskConfigBO);
        return vo1;
    }
    /**
     * @description 分公司制返盘行配置表保存
     * @param vo 分公司制返盘行配置表VO对象
     */
    @Override
    public OrganBankDiskLineconfigVO saveBankDiskLineConfig(OrganBankDiskLineconfigVO vo) {
        OrganBankDiskLineconfigBO organBankDiskLineconfigBO = new OrganBankDiskLineconfigBO();
        organBankDiskLineconfigBO = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class, vo);
        organBankDiskLineconfigBO = transferFileForAdd.saveBankDiskLineConfig(organBankDiskLineconfigBO);
        OrganBankDiskLineconfigVO organBankDiskLineconfigVO = new OrganBankDiskLineconfigVO();
        organBankDiskLineconfigVO = BeanUtils
                .copyProperties(OrganBankDiskLineconfigVO.class, organBankDiskLineconfigBO);
        return organBankDiskLineconfigVO;
    }
    /**
     * @description 查询索引
     */
    @Override
    public Object queryDualId() {
        String id = (String) transferFileForAdd.queryDualId();
        return id;
    }
    /**
     * @description 分公司制返盘列配置表保存
     * @<NAME_EMAIL>
     * @param vo 分公司制返盘列配置表VO对象
     */
    @Override
    public OrganBankDiskColconfigVO saveBankDiskColConfig(OrganBankDiskColconfigVO vo) {
        OrganBankDiskColconfigBO organBankDiskColconfigBO = new OrganBankDiskColconfigBO();
        organBankDiskColconfigBO = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class, vo);
        organBankDiskColconfigBO = transferFileForAdd.saveBankDiskColconfig(organBankDiskColconfigBO);
        OrganBankDiskColconfigVO organBankDiskColconfigVO = new OrganBankDiskColconfigVO();
        organBankDiskColconfigVO = BeanUtils.copyProperties(OrganBankDiskColconfigVO.class, organBankDiskColconfigBO);
        return organBankDiskColconfigVO;
    }
    /**
     * @description 查询分公司制返盘主表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘配置主表VO对象
     */
    @Override
    public CurrentPage<OrganBankDiskConfigVO> queryInfo(CurrentPage<OrganBankDiskConfigVO> currentPage) {
        CurrentPage<OrganBankDiskConfigBO> organBankDiskConfigBO = new CurrentPage<OrganBankDiskConfigBO>();
        organBankDiskConfigBO = BeanUtils.copyCurrentPage(OrganBankDiskConfigBO.class, currentPage);
        return BeanUtils.copyCurrentPage(OrganBankDiskConfigVO.class,
                transferFileForAdd.queryInfo(organBankDiskConfigBO));
    }
    /**
     * @description 修改分公司制返盘主表配置信息
     * @param vo 分公司制返盘配置主表VO对象
     */
    @Override
    public OrganBankDiskConfigVO updateBankDiskConfig(OrganBankDiskConfigVO vo) {
        OrganBankDiskConfigBO organBankDiskConfigBO = new OrganBankDiskConfigBO();
        organBankDiskConfigBO = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, vo);
        organBankDiskConfigBO = transferFileForAdd.updateBankDiskConfig(organBankDiskConfigBO);
        OrganBankDiskConfigVO organBankDiskConfigVO = new OrganBankDiskConfigVO();
        organBankDiskConfigVO = BeanUtils.copyProperties(OrganBankDiskConfigVO.class, organBankDiskConfigBO);
        return organBankDiskConfigVO;
    }
    /**
     * @description 查询分公司制返盘行表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘行配置表VO对象
     */
    @Override
    public CurrentPage<OrganBankDiskLineconfigVO> queryInfoLine(CurrentPage<OrganBankDiskLineconfigVO> currentPage) {
        CurrentPage<OrganBankDiskLineconfigBO> organBankDiskLineconfigBO = new CurrentPage<OrganBankDiskLineconfigBO>();
        organBankDiskLineconfigBO = BeanUtils.copyCurrentPage(OrganBankDiskLineconfigBO.class, currentPage);
        return BeanUtils.copyCurrentPage(OrganBankDiskLineconfigVO.class,
                transferFileForAdd.queryInfoLine(organBankDiskLineconfigBO));
    }
    /**
     * @description 查询分公司制返盘可用字段配置表全部信息
     * @param organBankDiskValidcolcfgVO 分公司制返盘可用字段配置表VO对象
     */
    @Override
    public List<OrganBankDiskValidcolcfgVO> querycfgAll(OrganBankDiskValidcolcfgVO organBankDiskValidcolcfgVO) {
        OrganBankDiskValidcolcfgBO bankDiskValidcolcfgBO = BeanUtils.copyProperties(OrganBankDiskValidcolcfgBO.class,
                organBankDiskValidcolcfgVO);
        List<OrganBankDiskValidcolcfgBO> validcolcfg = transferFileForAdd.querycfgAll(bankDiskValidcolcfgBO);
        return BeanUtils.copyList(OrganBankDiskValidcolcfgVO.class, validcolcfg);
    }
    /**
     * @description 批量增加分公司制返盘列配置
     * @param bankDiskColconfigVOs 分公司制返盘列VO对象列表
     */
    @Override
    public boolean batchSaveOrganBankDiskColconfig(List<OrganBankDiskColconfigVO> bankDiskColconfigVOs) {
        List<OrganBankDiskColconfigBO> copyList = BeanUtils.copyList(OrganBankDiskColconfigBO.class,
                bankDiskColconfigVOs);
        return transferFileForAdd.batchSaveOrganBankDiskColconfig(copyList);
    }
    /**
     * @description 查询制返盘配置行关联的所有列
     * @param organBankDiskColconfigVO 分公司制返盘列VO对象
     */
    @Override
    public List<OrganBankDiskColconfigVO> queryDiskColCofigByLineId(OrganBankDiskColconfigVO organBankDiskColconfigVO) {
        OrganBankDiskColconfigBO copyProperties = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class,
                organBankDiskColconfigVO);
        List<OrganBankDiskColconfigBO> colconfigs = transferFileForAdd.queryColconfigsByLineId(copyProperties);
        return BeanUtils.copyList(OrganBankDiskColconfigVO.class, colconfigs);
    }
    /**
     * @description 根据列ID查询列信息配置
     * @param organBankDiskColconfigVO 分公司制返盘列VO对象
     */
    @Override
    public OrganBankDiskColconfigVO queryDiskColCofigByColId(OrganBankDiskColconfigVO organBankDiskColconfigVO) {
        OrganBankDiskColconfigBO copyProperties = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class,
                organBankDiskColconfigVO);
        OrganBankDiskColconfigBO colconfigs = transferFileForAdd.queryColconfigsByColId(copyProperties);
        return BeanUtils.copyProperties(OrganBankDiskColconfigVO.class, colconfigs);
    }
    /**
     * @description 更新列配置
     * @param organBankDiskColconfigVO 分公司制返盘列VO对象
     */
    @Override
    public OrganBankDiskColconfigVO updateColconfig(OrganBankDiskColconfigVO organBankDiskColconfigVO) {
        OrganBankDiskColconfigBO copyProperties = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class,
                organBankDiskColconfigVO);
        OrganBankDiskColconfigBO colconfigs = transferFileForAdd.updateColconfigInfo(copyProperties);
        return BeanUtils.copyProperties(OrganBankDiskColconfigVO.class, colconfigs);
    }
    /**
     * @description 查询单条制返盘配行表配置
     * @param lineVO 分公司制返盘行表VO对象
     */
    @Override
    public OrganBankDiskLineconfigVO queryInfoLineById(OrganBankDiskLineconfigVO lineVO) {
        OrganBankDiskLineconfigBO organBankDiskLineconfigBO = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class,
                lineVO);
        return BeanUtils.copyProperties(OrganBankDiskLineconfigVO.class,
                transferFileForAdd.queryInfoLineById(organBankDiskLineconfigBO));
    }
    /**
     * @description 查询分公司制返盘主表配置信息
     * @param configVO  分公司制返盘主表VO对象
     */
    @Override
    public OrganBankDiskConfigVO queryDiskConfigById(OrganBankDiskConfigVO configVO) {
        OrganBankDiskConfigBO bo = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, configVO);
        OrganBankDiskConfigBO config = transferFileForAdd.queryDiskConfigById(bo);
        return BeanUtils.copyProperties(OrganBankDiskConfigVO.class, config);
    }
    /**
     * @description 更新分公司制返盘行配置表
     * @<NAME_EMAIL>
     * @param vo 分公司制返盘行表VO对象
     */
    @Override
    public OrganBankDiskLineconfigVO updateBankDiskLineConfig(OrganBankDiskLineconfigVO vo) {
        OrganBankDiskLineconfigBO organBankDiskLineconfigBO = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class,
                vo);
        OrganBankDiskLineconfigBO resultBO = transferFileForAdd.updateBankDiskLineConfig(organBankDiskLineconfigBO);
        return BeanUtils.copyProperties(OrganBankDiskLineconfigVO.class, resultBO);
    }
    /**
     * @description 分公司制返盘列配置删除
     * @param vo 分公司制返盘列VO对象
     */
    @Override
    public boolean delBankDiskColConfig(List<OrganBankDiskColconfigVO> vo) {
        List<OrganBankDiskColconfigBO> bos = BeanUtils.copyList(OrganBankDiskColconfigBO.class, vo);
        return transferFileForAdd.delBankDiskColconfig(bos);
    }
    /**
     * @description 按条件查询盘信息
     * @param configVO 分公司制返盘主表VO对象
     */
    @Override
    public OrganBankDiskConfigVO queryDiskConfigByCondition(OrganBankDiskConfigVO configVO) {
        OrganBankDiskConfigBO configBO = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, configVO);
        OrganBankDiskConfigBO bo = transferFileForAdd.queryDiskConfigByCondition(configBO);
        if (bo != null) {
            return BeanUtils.copyProperties(OrganBankDiskConfigVO.class, bo);
        }
        return null;
    }
    /**
     * @description 查询盘配置的行信息
     * @param configVO 分公司制返盘行表VO对象
     */
    @Override
    public List<OrganBankDiskLineconfigVO> queryDiskLineConfigByConf(OrganBankDiskLineconfigVO configVO) {
        OrganBankDiskLineconfigBO organBankDiskLineconfigBO = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class,
                configVO);
        List<OrganBankDiskLineconfigBO> resultBO = transferFileForAdd
                .queryDiskLineConfigByConf(organBankDiskLineconfigBO);
        return BeanUtils.copyList(OrganBankDiskLineconfigVO.class, resultBO);
    }
    /**
     * @description 按盘ID批量删除行配置
     * @param configVO 当前页分公司制返盘行表VO对象
     */
    @Override
    public boolean deleteOrganBankDiskLineconfigByConfId(List<OrganBankDiskLineconfigVO> configVO) {
        List<OrganBankDiskLineconfigBO> bo = BeanUtils.copyList(OrganBankDiskLineconfigBO.class, configVO);
        return transferFileForAdd.deleteOrganBankDiskLineconfigByConfId(bo);
    }

    public ITransferFileForAddService getTransferFileForAdd() {
        return transferFileForAdd;
    }

    public void setTransferFileForAdd(ITransferFileForAddService transferFileForAdd) {
        this.transferFileForAdd = transferFileForAdd;
    }
    /**
     * @description  按行ID删除列配置表
     * @param organBankDiskColconfigVO 分公司制返盘列表VO对象
     */
    @Override
    public boolean deleteOrganBankDiskColconfigByLineId(OrganBankDiskColconfigVO organBankDiskColconfigVO) {
        OrganBankDiskColconfigBO bos = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class,
                organBankDiskColconfigVO);
        return transferFileForAdd.deleteOrganBankDiskColconfigByLineId(bos);
    }
    /**
     * @description 删除行配置
     * @param configVO 分公司制返盘行表VO对象
     */
    @Override
    public boolean deleteOrganBankDiskLineconfig(OrganBankDiskLineconfigVO configVO) {
        OrganBankDiskLineconfigBO bo = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class, configVO);
        return transferFileForAdd.deleteOrganBankDiskLineconfig(bo);
    }
    /**
     * @description  按行ID批量删除列配置表
     * @param organBankDiskColconfigVO 当前页分公司制返盘列表VO对象
     */
    @Override
    public boolean batchDelOrganBankDiskColconfigByLineId(List<OrganBankDiskColconfigVO> organBankDiskColconfigVO) {
        List<OrganBankDiskColconfigBO> bos = BeanUtils.copyList(OrganBankDiskColconfigBO.class,
                organBankDiskColconfigVO);
        return transferFileForAdd.batchDelOrganBankDiskColconfigByLineId(bos);
    }
    /**
     * @description 删除分公司制返盘主表配置信息
     * @param vo 分公司制返盘主表VO对象
     */
    @Override
    public boolean deleteOrganBankDiskConfig(OrganBankDiskConfigVO vo) {
        OrganBankDiskConfigBO bo = BeanUtils.copyProperties(OrganBankDiskConfigBO.class, vo);
        return transferFileForAdd.deleteOrganBankDiskConfig(bo);
    }

    /**
     * @description 盘信息修改前置校验，action业务逻辑需移至ucc
     * @param bankDiskConfigVO 分公司制返盘主表VO对象
     * @param exString url信息定义
     */
    @Override
    public String[] validEditDetial(OrganBankDiskConfigVO bankDiskConfigVO, String exString) {
        if (CapVerify.dataObjectNotNull(bankDiskConfigVO, true)) {
            // 1.查询现在的转账格式定义配置
            OrganBankDiskConfigVO conf = this.queryDiskConfigById(bankDiskConfigVO);
            String newBankDiskDelLine = CapVerify.ternaryOperator(bankDiskConfigVO.getBankDiskDelLine() == null, "",
                    bankDiskConfigVO.getBankDiskDelLine()).toString();
            String oldBankDiskDelLine = CapVerify.ternaryOperator(conf.getBankDiskDelLine() == null, "",
                    conf.getBankDiskDelLine()).toString();
            // 2.验证页面数据与查询出的数据是否一致，如果不一致为更新、否则不对数据进行操作
            if (!conf.getOrganCode().equals(bankDiskConfigVO.getOrganCode())
                    || !conf.getBankCode().equals(bankDiskConfigVO.getBankCode())
                    || !conf.getTransfersType().equals(bankDiskConfigVO.getTransfersType())
                    || !conf.getBankDiskDocType().equals(bankDiskConfigVO.getBankDiskDocType())
                    || !conf.getSumLineFlag().equals(bankDiskConfigVO.getSumLineFlag())
                    || !conf.getArapFlag().equals(bankDiskConfigVO.getArapFlag())
                    || !conf.getBankDiskType().equals(bankDiskConfigVO.getBankDiskType())
                    || !conf.getValidity().equals(bankDiskConfigVO.getValidity())
                    || !(CapVerify.ternaryOperator(conf.getBusinessType() == null, "", conf.getBusinessType()))
                            .equals(bankDiskConfigVO.getBusinessType())
                    || !newBankDiskDelLine.equals(oldBankDiskDelLine)) {
                OrganBankDiskConfigVO checkConf = this.queryDiskConfigByCondition(bankDiskConfigVO);
                if (checkConf == null || checkConf.getDiskConfigId() == null) {
                    //2.1 根据页面选择查询是否有配置汇总行
                	//2.2  如果查询没有汇总行，页面修改有汇总行，可在行修改页面进行添加操作；
                    //2.3 如果查询有汇总行，页面修改没有汇总行，下一步操作时进行汇总行删除操作
                    if (CodeCst.SUM_LINE_TYPE__NO.equals(bankDiskConfigVO.getSumLineFlag())
                            && !CodeCst.SUM_LINE_TYPE__NO.equals(conf.getSumLineFlag())) {
                        OrganBankDiskLineconfigVO lineConfVO = new OrganBankDiskLineconfigVO();
                        lineConfVO.setDiskConfigId(bankDiskConfigVO.getDiskConfigId());
                        lineConfVO.setLineType(conf.getSumLineFlag());
                        List<OrganBankDiskLineconfigVO> lineConfs = this.queryDiskLineConfigByConf(lineConfVO);
                        if (lineConfs != null && lineConfs.size() > 0) {
                            transferFileForAdd.deleteOrganBankDiskLineconfigByConfId(BeanUtils.copyList(
                                    OrganBankDiskLineconfigBO.class, lineConfs));
                        }
                    }
                    // 3. 更新配置信息主表,转账格式定义修改成功
                    transferFileForAdd.updateBankDiskConfig(BeanUtils.copyProperties(OrganBankDiskConfigBO.class,
                            bankDiskConfigVO));
                    return new String[] {
                            Constants.DWZ_STATUSCODE_200,
                            "",
                            "",
                            exString
                                    + "/cap/filialebanktextfinition/editDetial_CAP_filialeBankTextFinitionAction.action?bankDiskConfigVO.diskConfigId="
                                    + bankDiskConfigVO.getDiskConfigId(), Constants.DWZ_CALLBACKTYPE_FORWARD };
                } else {
                    return new String[] { Constants.DWZ_STATUSCODE_300, "信息被重复定义！", "", "",
                            Constants.DWZ_CALLBACKTYPE_CLOSE };
                }
            } else {
                return new String[] {
                        Constants.DWZ_STATUSCODE_200,
                        "",
                        "",
                        exString
                                + "/cap/filialebanktextfinition/editDetial_CAP_filialeBankTextFinitionAction.action?bankDiskConfigVO.diskConfigId="
                                + bankDiskConfigVO.getDiskConfigId(), Constants.DWZ_CALLBACKTYPE_FORWARD };
            }
        } else {
            return new String[] { Constants.DWZ_STATUSCODE_300, Constants.DWZ_MESSAGE_400, "", "", "" };
        }
    }

    /**
     * @description 删除行和列，action业务逻辑需移至ucc
     * @param colVO 分公司制返盘主列VO对象
     * @param lineVO 分公司制返盘主行VO对象
     */
    @Override
    public void deleteOrganBankDiskColAndLineConfigById(OrganBankDiskColconfigVO colVO, OrganBankDiskLineconfigVO lineVO) {
        OrganBankDiskColconfigBO bos = BeanUtils.copyProperties(OrganBankDiskColconfigBO.class, colVO);
        transferFileForAdd.deleteOrganBankDiskColconfigByLineId(bos);
        OrganBankDiskLineconfigBO bo = BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class, lineVO);
        transferFileForAdd.deleteOrganBankDiskLineconfig(bo);
    }

    /**
     * @description 行、列定义，action业务逻辑需移至ucc
     * @param bankDiskLineconfigVO 分公司制返盘行表VO对象
     * @param chksNames 列名称
     */
    @Override
    public OrganBankDiskLineconfigVO addInfoDetialRow(OrganBankDiskLineconfigVO bankDiskLineconfigVO, String chksNames) {
        List<OrganBankDiskColconfigVO> organBankDiskColconfigVOs = new ArrayList<OrganBankDiskColconfigVO>();
        if (CapVerify.dataObjectNotNull(bankDiskLineconfigVO, true)) {
            String[] ckNames = chksNames.split("\\|");
            //@invalid 1.查询此行选择的列是否有变动
            for (int i = CodeCst.NUMBER_ZERO; i < ckNames.length; i++) {
                String[] col = ckNames[i].split(",");
                //@invalid 1.1初始化列参数,并设置默认值
                OrganBankDiskColconfigVO organBankDiskColconfigVO = this.createColObject(col);
                organBankDiskColconfigVO.setColOrderIndex(new BigDecimal(i + CodeCst.NUMBER_ONE));
                organBankDiskColconfigVOs.add(organBankDiskColconfigVO);
            }
            //@invalid 2.保存行
            bankDiskLineconfigVO = BeanUtils.copyProperties(OrganBankDiskLineconfigVO.class, transferFileForAdd
                    .saveBankDiskLineConfig(BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class,
                            bankDiskLineconfigVO)));

            //@invalid 3.关联行
            for (int i = CodeCst.NUMBER_ZERO; i < organBankDiskColconfigVOs.size(); i++) {
                OrganBankDiskColconfigVO organBankDiskColconfigVO = organBankDiskColconfigVOs.get(i);
                organBankDiskColconfigVO.setDiskLineconfigId(bankDiskLineconfigVO.getDiskLineconfigId());
            }
            //@invalid 4.保存选中的列
            transferFileForAdd.batchSaveOrganBankDiskColconfig(BeanUtils.copyList(OrganBankDiskColconfigBO.class,
                    organBankDiskColconfigVOs));
        }
        return bankDiskLineconfigVO;
    }

    /**
     * @description 更新行配置，action业务逻辑需移至ucc
     * @param bankDiskLineconfigVO 分公司制返盘行表VO对象
     * @param bankDiskConfigVO 分公司制返盘主表VO对象
     * @param chksNames 列名称
     */ 
    @Override
    public OrganBankDiskLineconfigVO editConfigRow(OrganBankDiskLineconfigVO bankDiskLineconfigVO,
            OrganBankDiskConfigVO bankDiskConfigVO, String chksNames) {
        List<OrganBankDiskColconfigVO> organBankDiskColconfigVOs = new ArrayList<OrganBankDiskColconfigVO>();
        BigDecimal lineId = bankDiskLineconfigVO.getDiskLineconfigId();
        String[] ckNames = chksNames.split("\\|");
        // 1.如果行id不为空并且行的数量大于0
        if (lineId != null && lineId.intValue() > CodeCst.NUMBER_ZERO) { 
            OrganBankDiskColconfigVO queryColVO = new OrganBankDiskColconfigVO();
            queryColVO.setDiskLineconfigId(bankDiskLineconfigVO.getDiskLineconfigId());
            // 1.1查询需要修改的列信息
            List<OrganBankDiskColconfigVO> cols = this.queryDiskColCofigByLineId(queryColVO);
            List<OrganBankDiskColconfigVO> colsTwo = BeanUtils.copyList(OrganBankDiskColconfigVO.class, cols);
            List newCols = new ArrayList(Arrays.asList(ckNames));
            List newCols1 = new ArrayList(Arrays.asList(ckNames));
            for (int j = CodeCst.NUMBER_ZERO; j < newCols.size(); j++) {
                String str = (String) newCols.get(j);
                String[] addCol = str.split(",");
                for (int i = CodeCst.NUMBER_ZERO; i < colsTwo.size(); i++) {
                    OrganBankDiskColconfigVO col = colsTwo.get(i);
                    if (addCol[0].equals(col.getColName())) {
                        newCols.remove(j);
                        j--;
                    }
                }
            }
            // 1.2组装需要修改的列信息
            List newColsTwo = new ArrayList(Arrays.asList(ckNames));
            for (int i = CodeCst.NUMBER_ZERO; i < cols.size(); i++) {
                OrganBankDiskColconfigVO col = cols.get(i);
                for (int j = CodeCst.NUMBER_ZERO; j < newColsTwo.size(); j++) {
                    String str = (String) newColsTwo.get(j);
                    String[] addCol = str.split(",");
                    if (addCol[CodeCst.NUMBER_ZERO].equals(col.getColName())) {
                        cols.remove(i);
                        i--;
                    }
                }
            }
            bankDiskConfigVO = new OrganBankDiskConfigVO();
            bankDiskConfigVO.setDiskConfigId(bankDiskLineconfigVO.getDiskConfigId());
            bankDiskConfigVO.setBankDiskType(bankDiskLineconfigVO.getBankDiskType());
            //1.3删除不需要的列信息
            if (cols.size() > CodeCst.NUMBER_ZERO) {
                transferFileForAdd.delBankDiskColconfig(BeanUtils.copyList(OrganBankDiskColconfigBO.class, cols));
            }
            //1.4根据录入信息更新行信息
            bankDiskLineconfigVO = BeanUtils.copyProperties(OrganBankDiskLineconfigVO.class, transferFileForAdd
                    .updateBankDiskLineConfig(BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class,
                            bankDiskLineconfigVO)));
            //1.5查询此行是否有变动，如果有变动，初始化参数并设置默认值，变动的列信息关联本行，保存新添加的列
            if (newCols.size() > CodeCst.NUMBER_ZERO) {
                for (int i = CodeCst.NUMBER_ZERO; i < newCols.size(); i++) {
                    String str = (String) newCols.get(i);
                    String[] col = str.split(",");
                    organBankDiskColconfigVOs.add(this.createColObject(col));
                }
                for (int i = CodeCst.NUMBER_ZERO; i < organBankDiskColconfigVOs.size(); i++) {
                    OrganBankDiskColconfigVO organBankDiskColconfigVO = organBankDiskColconfigVOs.get(i);
                    organBankDiskColconfigVO.setDiskLineconfigId(bankDiskLineconfigVO.getDiskLineconfigId());
                }
                transferFileForAdd.batchSaveOrganBankDiskColconfig(BeanUtils.copyList(OrganBankDiskColconfigBO.class,
                        organBankDiskColconfigVOs));
            }
            
            List<OrganBankDiskColconfigVO> cols1 = this.queryDiskColCofigByLineId(queryColVO);
            for (int j = CodeCst.NUMBER_ZERO; j < newCols1.size(); j++) {
                String str = (String) newCols1.get(j);
                String[] addCol = str.split(",");
                for (int i = CodeCst.NUMBER_ZERO; i < cols1.size(); i++) {
                    OrganBankDiskColconfigVO col = cols1.get(i);
                    if (addCol[CodeCst.NUMBER_ZERO].equals(col.getColName())) {
                        col.setColOrderIndex(new BigDecimal(j + CodeCst.NUMBER_ONE));
                    }
                }
            }
            transferFileForAdd.batchUpdateColconfigInfo(BeanUtils.copyList(OrganBankDiskColconfigBO.class, cols1));
        } else {
        	//2.如果行id为空或者行的数量等于0
            // 2.1查询此行选择的列是否有变动，初始化列参数设置默认值
            for (int i = CodeCst.NUMBER_ZERO; i < ckNames.length; i++) {
                String[] col = ckNames[i].split(",");
                // 
                OrganBankDiskColconfigVO organBankDiskColconfigVO = this.createColObject(col);
                organBankDiskColconfigVO.setColOrderIndex(new BigDecimal(i + CodeCst.NUMBER_ONE));
                organBankDiskColconfigVOs.add(organBankDiskColconfigVO);
            }
            //2.2保存选中的行信息
            bankDiskLineconfigVO.setDiskConfigId(bankDiskConfigVO.getDiskConfigId());
            bankDiskLineconfigVO = BeanUtils.copyProperties(OrganBankDiskLineconfigVO.class, transferFileForAdd
                    .saveBankDiskLineConfig(BeanUtils.copyProperties(OrganBankDiskLineconfigBO.class,
                            bankDiskLineconfigVO)));
            //2.3列信息关联行信息
            for (int i = CodeCst.NUMBER_ZERO; i < organBankDiskColconfigVOs.size(); i++) {
                OrganBankDiskColconfigVO organBankDiskColconfigVO = organBankDiskColconfigVOs.get(i);
                organBankDiskColconfigVO.setDiskLineconfigId(bankDiskLineconfigVO.getDiskLineconfigId());
            }
            //2.4保存选中的列信息
            transferFileForAdd.batchSaveOrganBankDiskColconfig(BeanUtils.copyList(OrganBankDiskColconfigBO.class,
                    organBankDiskColconfigVOs));
        }
        return bankDiskLineconfigVO;
    }

    /**
     * @description 生成制返盘列字段
     * @param col 分公司制返盘列表字段
     */
    private OrganBankDiskColconfigVO createColObject(String[] col) {
        OrganBankDiskColconfigVO vo = new OrganBankDiskColconfigVO();
        vo.setColDisplayName(col[CodeCst.DISPLAY_NAME_INDEX]);
        vo.setColLong(CodeCst.LENGTH);
        vo.setColName(col[CodeCst.NAME_INDEX]);
        vo.setColOrderIndex(CodeCst.ORDER_INDEX);
        vo.setDefaultCode("");
        vo.setFillChar("");
        vo.setFormatStyle(CodeCst.FORMAT_STYLE_LINE);
        vo.setIgnoreSeparatorFlag(CodeCst.YES_NO__NUMNO);
        vo.setIsAssemble(CodeCst.YES_NO__NUMNO);
        vo.setIsBusinessPkCol(CodeCst.YES_NO__NUMNO);
        vo.setSplitColCharIndex(CodeCst.SPLIT_CHAR_INDEX);
        vo.setSplitColCharIndexLen(CodeCst.SPLIT_CHAR_INDEX_LENTH);
        vo.setStrjust(CodeCst.STRJUS_CODE_LEFT);
        return vo;
    }
}
