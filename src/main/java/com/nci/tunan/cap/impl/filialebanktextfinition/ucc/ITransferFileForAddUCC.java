package com.nci.tunan.cap.impl.filialebanktextfinition.ucc;

import java.util.List;

import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskColconfigVO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskConfigVO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskLineconfigVO;
import com.nci.tunan.cap.interfaces.model.vo.OrganBankDiskValidcolcfgVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IService;

/**
 * @.belongToModule 收付费—分公司转账格式定义
 * @description 分公司制返盘增加主表、行表、列表配置  ucc接口
 * <AUTHOR>
 * @date 2015-10-12 下午2:34:11
 */
public interface ITransferFileForAddUCC extends IService {

    /**
     * @description 分公司制返盘配置主表保存
     * @param vo 分公司制返盘配置主表VO对象
     */
    public OrganBankDiskConfigVO saveBankDiskConfig(OrganBankDiskConfigVO vo);

    /**
     * @description 分公司制返盘行配置表保存
     * @param vo 分公司制返盘行配置表VO对象
     */
    public OrganBankDiskLineconfigVO saveBankDiskLineConfig(OrganBankDiskLineconfigVO vo);

    /**
     * @description 分公司制返盘列配置表保存
     * @<NAME_EMAIL>
     * @param vo 分公司制返盘列配置表VO对象
     */
    public OrganBankDiskColconfigVO saveBankDiskColConfig(OrganBankDiskColconfigVO vo);
    
    /**
     * @description 查询分公司制返盘主表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘配置主表VO对象
     */
    public CurrentPage<OrganBankDiskConfigVO> queryInfo(CurrentPage<OrganBankDiskConfigVO> currentPage);
    
    /**
     * @description 查询分公司制返盘行表配置信息
     * @<NAME_EMAIL>
     * @param currentPage 当前页分公司制返盘行配置表VO对象
     */
    public CurrentPage<OrganBankDiskLineconfigVO> queryInfoLine(CurrentPage<OrganBankDiskLineconfigVO> currentPage);
    
    /**
     * @description 修改分公司制返盘主表配置信息
     * @param vo 分公司制返盘配置主表VO对象
     */
    public OrganBankDiskConfigVO updateBankDiskConfig(OrganBankDiskConfigVO vo);

    /**
     * @description 查询索引
     */
    public Object queryDualId();
    
    /**
     * @description 查询分公司制返盘可用字段配置表全部信息
     * @param organBankDiskValidcolcfgVO 分公司制返盘可用字段配置表VO对象
     */
    public List<OrganBankDiskValidcolcfgVO> querycfgAll(OrganBankDiskValidcolcfgVO organBankDiskValidcolcfgVO);
    /**
     * @description 批量增加分公司制返盘列配置
     * @param bankDiskColconfigVOs 分公司制返盘列VO对象列表
     */
     public boolean batchSaveOrganBankDiskColconfig(List<OrganBankDiskColconfigVO> bankDiskColconfigVOs);
     /**
      * @description 查询制返盘配置行关联的所有列
      * @param organBankDiskColconfigVO 分公司制返盘列VO对象
      */
     public List<OrganBankDiskColconfigVO> queryDiskColCofigByLineId(OrganBankDiskColconfigVO organBankDiskColconfigVO);
     /**
      * @description 根据列ID查询列信息配置
      * @param organBankDiskColconfigVO 分公司制返盘列VO对象
      */
     public OrganBankDiskColconfigVO queryDiskColCofigByColId(OrganBankDiskColconfigVO organBankDiskColconfigVO);

     /**
      * @description 更新列配置
      * @param organBankDiskColconfigVO 分公司制返盘列VO对象
      */
     public OrganBankDiskColconfigVO updateColconfig(OrganBankDiskColconfigVO organBankDiskColconfigVO);
     
     /**
      * @description 查询单条制返盘配行表配置
      * @param lineVO 分公司制返盘行表VO对象
      */
     public OrganBankDiskLineconfigVO queryInfoLineById(OrganBankDiskLineconfigVO lineVO);
     /**
      * @description 更新分公司制返盘行配置表
      * @<NAME_EMAIL>
      * @param vo 分公司制返盘行表VO对象
      */
     public OrganBankDiskLineconfigVO updateBankDiskLineConfig(OrganBankDiskLineconfigVO vo);
     /**
      * @description 分公司制返盘列配置删除
      * @param vo 分公司制返盘列VO对象
      */
     public boolean delBankDiskColConfig(List<OrganBankDiskColconfigVO> vo);
     /**
      * @description 查询分公司制返盘主表配置信息
      * @param configVO  分公司制返盘主表VO对象
      */
     public OrganBankDiskConfigVO queryDiskConfigById(OrganBankDiskConfigVO configVO);
     /**
      * @description 按条件查询盘信息
      * @param configVO 分公司制返盘主表VO对象
      */
     public OrganBankDiskConfigVO queryDiskConfigByCondition(OrganBankDiskConfigVO configVO);
     /**
      * @description 查询盘配置的行信息
      * @param configVO 分公司制返盘行表VO对象
      */
     public List<OrganBankDiskLineconfigVO> queryDiskLineConfigByConf(OrganBankDiskLineconfigVO configVO); 
     /**
      * @description  按行ID删除列配置表
      * @param organBankDiskColconfigVO 分公司制返盘列表VO对象
      */
     public boolean deleteOrganBankDiskColconfigByLineId(OrganBankDiskColconfigVO organBankDiskColconfigVO);
     /**
      * @description 删除行配置
      * @param configVO 分公司制返盘行表VO对象
      */
     public boolean deleteOrganBankDiskLineconfig(OrganBankDiskLineconfigVO configVO);
     /**
      * @description 按盘ID批量删除行配置
      * @param configVO 当前页分公司制返盘行表VO对象
      */
     public boolean deleteOrganBankDiskLineconfigByConfId(List<OrganBankDiskLineconfigVO> configVO);
     /**
      * @description  按行ID批量删除列配置表
      * @param organBankDiskColconfigVO 当前页分公司制返盘列表VO对象
      */
     public boolean batchDelOrganBankDiskColconfigByLineId(List<OrganBankDiskColconfigVO> organBankDiskColconfigVO);
     /**
      * @description 删除分公司制返盘主表配置信息
      * @param vo 分公司制返盘主表VO对象
      */
     public boolean deleteOrganBankDiskConfig(OrganBankDiskConfigVO vo);
     
     
     
     /**
      * @description 盘信息修改前置校验，action业务逻辑需移至ucc
      * @param bankDiskConfigVO 分公司制返盘主表VO对象
      * @param exString url信息定义
      */
     public String[] validEditDetial(OrganBankDiskConfigVO bankDiskConfigVO,String exString);
     
     /**
      * @description 删除行和列，action业务逻辑需移至ucc
      * @param colVO 分公司制返盘主列VO对象
      * @param lineVO 分公司制返盘主行VO对象
      */
     public void deleteOrganBankDiskColAndLineConfigById(OrganBankDiskColconfigVO colVO,OrganBankDiskLineconfigVO  lineVO);
     
     /**
      * @description 行、列定义，action业务逻辑需移至ucc
      * @param bankDiskLineconfigVO 分公司制返盘行表VO对象
      * @param chksNames 列名称
      */
     public OrganBankDiskLineconfigVO addInfoDetialRow(OrganBankDiskLineconfigVO bankDiskLineconfigVO,String chksNames);
     
     /**
      * @description 更新行配置，action业务逻辑需移至ucc
      * @param bankDiskLineconfigVO 分公司制返盘行表VO对象
      * @param bankDiskConfigVO 分公司制返盘主表VO对象
      * @param chksNames 列名称
      */       
     public OrganBankDiskLineconfigVO editConfigRow(OrganBankDiskLineconfigVO bankDiskLineconfigVO,OrganBankDiskConfigVO bankDiskConfigVO,String chksNames);
     
}
