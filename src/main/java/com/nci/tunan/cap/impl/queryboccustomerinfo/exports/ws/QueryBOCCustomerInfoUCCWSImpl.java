package com.nci.tunan.cap.impl.queryboccustomerinfo.exports.ws;


import java.util.Map;

import javax.jws.WebParam;
import javax.jws.WebParam.Mode;
import javax.xml.ws.Holder;

import org.slf4j.Logger;


import com.nci.tunan.cap.impl.queryboccustomerinfo.ucc.IQueryBOCCustomerInfoUCC;
import com.nci.tunan.cap.interfaces.customerinfoquery.exports.ws.IQueryBOCCustomerInfoUCCWS;
import com.nci.tunan.cap.interfaces.customerinfoquery.exports.SrvReqBody;
import com.nci.tunan.cap.interfaces.customerinfoquery.exports.SrvResBody;
import com.nci.tunan.cap.interfaces.customerinfoquery.exports.SrvResBizBody;
import com.nci.tunan.cap.interfaces.vo.queryboccustomerinfo.QueryBOCCustomerInfoInputData;
import com.nci.tunan.cap.interfaces.vo.queryboccustomerinfo.QueryBOCCustomerInfoOutputData;
import com.nci.tunan.cap.util.CodeCst;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

public class QueryBOCCustomerInfoUCCWSImpl implements IQueryBOCCustomerInfoUCCWS {
    
    private static Logger logger = LoggerFactory.getLogger();
    
    private IQueryBOCCustomerInfoUCC ucc = null;
    
    public IQueryBOCCustomerInfoUCC getUcc() {
        return ucc;
    }

    public void setUcc(IQueryBOCCustomerInfoUCC ucc) {
        this.ucc = ucc;
    }
    @Override
    public void queryBOCCustomerInfo(
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, partName = "parametersReqHeader") SysHeader parametersReqHeader,
            @WebParam(name = "request", targetNamespace = "http://www.newchinalife.com/service/bd", partName = "parametersReqBody") SrvReqBody parametersReqBody,
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, mode = Mode.OUT, partName = "parametersResHeader") Holder<SysHeader> parametersResHeader,
            @WebParam(name = "response", targetNamespace = "http://www.newchinalife.com/service/bd", mode = Mode.OUT, partName = "parametersResBody") Holder<SrvResBody> parametersResBody) {
        
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap.get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String systemName = StringUtilsEx.getStr("com.nci.tunan.cap.impl.queryboccustomerinfo.exports.ws", CodeCst.NUMBER_FOUR, ".");
        String dealNo = systemName + "_" + "IQueryBOCCustomerInfoUCC" + "_" + "queryAccNameByAccNo";
        if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(parametersReqHeader, null, parametersReqBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime);
        }
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD(null);
        QueryBOCCustomerInfoInputData input = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id："   + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        String dealStatus = "2";
        
        try {
            QueryBOCCustomerInfoOutputData outputData = ucc.queryAccNameByAccNo(input);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(outputData);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(parametersReqBody.getBizHeader());
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
            dealStatus = "3";
            logger.error("调用接口过程中产生异常!", e2);
        } finally {
            if (dealSwitch) {
                logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysHeader, null, srvResBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime, dealStatus);
            }
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        }
    }
    
}
 

